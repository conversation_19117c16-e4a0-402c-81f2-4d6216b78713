name: gdjg_pure_flutter
description: "A new GDJG Flutter group_project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 7.0.0+700

environment:
  sdk: ^3.6.2

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  get: ^4.7.2

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  flutter_screenutil: ^5.9.3
  flutter_smart_dialog: ^4.9.8+8
  # 数据解析
  json_annotation: ^4.9.0
  pull_to_refresh: ^2.0.0
  mask_text_input_formatter: ^2.9.0
  dio: ^5.3.3
  video_player: 2.10.0
  collection: 1.19.0
  shared_preferences: ^2.5.3
  mmkv: ^2.2.2
  webview_flutter: ^4.13.0
  url_launcher: ^6.3.1
  grouped_list: ^5.1.2
  # 日期时间处理
  intl: ^0.18.1

  # 网络请求
  net_plugin:
    git:
      url: ******************************:flutter/plugin/net_plugin.git
      ref: gdjg # 可以是分支名、标签或提交哈希
  #同步锁
  synchronized: ^3.0.0
  # 农历转换
  lunar: ^1.7.5
  # 权限处理
  permission_handler: ^11.3.2
  event_bus: ^2.0.1
  #相册和相机
  wechat_assets_picker: ^9.5.0
  # 图片选择
  image_picker: ^1.1.2
  # 微信SDK
  fluwx: ^5.6.0
  # 二维码扫描
  mobile_scanner: ^6.0.10
  # 生成二维码图片
  qr_flutter: ^4.1.0
  # 文件路径处理
  path_provider: ^2.1.4
  #语音播放
  audioplayers: ^6.3.0

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0
  build_runner: 2.4.15
  json_serializable: ^6.8.0
  runner_entity:
    git:
      url: ******************************:liguang/runner_entity.git
      ref: master

  flutter_iconfont_generator: ^1.0.1
# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true


  assets:
    - assets/images/tabbar/
    - assets/images/common/
    - assets/images/login/
    - lib/data/group_data/group_statistics/test_json.json
    - assets/images/group/
    - assets/images/worker/
    - assets/sounds/
  fonts:
    - family: CondMedium
      fonts:
        - asset: assets/fonts/cond_medium.otf

iconfont:
  symbol_url: "//at.alicdn.com/t/c/font_2761690_ggh2adc46l5.js"  # Get from iconfont.cn
  save_dir: "./lib/iconfont"                    # Output directory
  trim_icon_prefix: "icon"                      # Remove icon name prefix
  default_icon_size: 18                         # Default icon size
  null_safety: true
  # To add assets to your application, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
