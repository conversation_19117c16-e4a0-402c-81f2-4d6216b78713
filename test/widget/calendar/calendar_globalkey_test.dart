import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:gdjg_pure_flutter/widget/calendar/calendar_view.dart';

void main() {
  group('Calendar GlobalKey Tests', () {
    testWidgets('Multiple CalendarView widgets should not have GlobalKey conflicts', (WidgetTester tester) async {
      // 创建两个不同的控制器
      final controller1 = CalendarController();
      final controller2 = CalendarController();

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Column(
              children: [
                // 第一个日历组件
                Expanded(
                  child: CalendarView(
                    key: const ValueKey('calendar1'),
                    controller: controller1,
                    config: const CalendarConfig(
                      primaryColor: Colors.blue,
                      enableSwipeToChangeMonth: true,
                    ),
                  ),
                ),
                // 第二个日历组件
                Expanded(
                  child: CalendarView(
                    key: const ValueKey('calendar2'),
                    controller: controller2,
                    config: const CalendarConfig(
                      primaryColor: Colors.green,
                      enableRangeSelection: true,
                      enableSwipeToChangeMonth: true,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      );

      // 验证两个日历组件都能正常渲染
      expect(find.byKey(const ValueKey('calendar1')), findsOneWidget);
      expect(find.byKey(const ValueKey('calendar2')), findsOneWidget);
      
      // 验证没有GlobalKey冲突异常
      expect(tester.takeException(), isNull);

      // 清理资源
      controller1.dispose();
      controller2.dispose();
    });

    testWidgets('CalendarExample page should render without GlobalKey conflicts', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: Column(
              children: [
                // 模拟CalendarExample页面的结构
                Expanded(
                  child: CalendarView(
                    key: ValueKey('basic_calendar'),
                    config: CalendarConfig(
                      primaryColor: Colors.blue,
                      enableSwipeToChangeMonth: true,
                      showEventDots: true,
                    ),
                  ),
                ),
                Expanded(
                  child: CalendarView(
                    key: ValueKey('range_calendar'),
                    config: CalendarConfig(
                      primaryColor: Colors.green,
                      enableRangeSelection: true,
                      enableSwipeToChangeMonth: true,
                      showEventDots: false,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      );

      // 验证页面能正常渲染
      expect(find.byKey(const ValueKey('basic_calendar')), findsOneWidget);
      expect(find.byKey(const ValueKey('range_calendar')), findsOneWidget);
      
      // 验证没有GlobalKey冲突异常
      expect(tester.takeException(), isNull);
    });

    testWidgets('Multiple CalendarView with different configurations should work', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Column(
              children: [
                // 多个不同配置的日历组件
                Expanded(
                  child: CalendarView(
                    key: const ValueKey('theme1'),
                    config: const CalendarConfig(
                      primaryColor: Colors.blue,
                      enableSwipeToChangeMonth: false,
                    ),
                  ),
                ),
                Expanded(
                  child: CalendarView(
                    key: const ValueKey('theme2'),
                    config: const CalendarConfig(
                      primaryColor: Colors.red,
                      enableSwipeToChangeMonth: false,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      );

      // 验证日历组件都能正常渲染
      expect(find.byKey(const ValueKey('theme1')), findsOneWidget);
      expect(find.byKey(const ValueKey('theme2')), findsOneWidget);

      // 验证没有GlobalKey冲突异常
      expect(tester.takeException(), isNull);
    });

    testWidgets('CalendarView with and without controllers should coexist', (WidgetTester tester) async {
      final controller = CalendarController();

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Column(
              children: [
                // 带控制器的日历
                Expanded(
                  child: CalendarView(
                    key: const ValueKey('with_controller'),
                    controller: controller,
                  ),
                ),
                // 不带控制器的日历
                Expanded(
                  child: CalendarView(
                    key: const ValueKey('without_controller'),
                  ),
                ),
              ],
            ),
          ),
        ),
      );

      // 验证两个日历组件都能正常渲染
      expect(find.byKey(const ValueKey('with_controller')), findsOneWidget);
      expect(find.byKey(const ValueKey('without_controller')), findsOneWidget);
      
      // 验证没有GlobalKey冲突异常
      expect(tester.takeException(), isNull);

      // 清理资源
      controller.dispose();
    });
  });
}
