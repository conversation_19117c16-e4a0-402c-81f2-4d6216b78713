import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:gdjg_pure_flutter/widget/calendar/calendar_view.dart';

void main() {
  group('CalendarView Tests', () {
    testWidgets('CalendarView renders correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: CalendarView(),
          ),
        ),
      );

      // 验证日历组件是否渲染
      expect(find.byType(CalendarView), findsOneWidget);
      
      // 验证星期标题是否存在
      expect(find.text('日'), findsOneWidget);
      expect(find.text('一'), findsOneWidget);
      expect(find.text('六'), findsOneWidget);
    });

    testWidgets('Day selection works correctly', (WidgetTester tester) async {
      DateTime? selectedDate;
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: CalendarView(
              onDaySelected: (date) {
                selectedDate = date;
              },
            ),
          ),
        ),
      );

      // 点击日期15
      await tester.tap(find.text('15'));
      await tester.pump();

      // 验证回调是否被调用
      expect(selectedDate, isNotNull);
      expect(selectedDate!.day, equals(15));
    });

    testWidgets('Month navigation works correctly', (WidgetTester tester) async {
      DateTime? changedMonth;
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: CalendarView(
              onMonthChanged: (month) {
                changedMonth = month;
              },
            ),
          ),
        ),
      );

      // 点击下一个月按钮
      await tester.tap(find.byIcon(Icons.chevron_right));
      await tester.pump();

      // 验证月份切换回调
      expect(changedMonth, isNotNull);
    });
  });

  group('CalendarController Tests', () {
    test('CalendarController initializes correctly', () {
      final controller = CalendarController();
      
      expect(controller.currentMonth.day, equals(1));
      expect(controller.selectedDate, isNull);
      expect(controller.events, isEmpty);
    });

    test('Event management works correctly', () {
      final controller = CalendarController();
      final event = CalendarEvent(
        id: '1',
        title: 'Test Event',
        date: DateTime.now(),
      );

      // 添加事件
      controller.addEvent(event);
      expect(controller.events.length, equals(1));

      // 获取事件
      final events = controller.getEventsForDate(event.date);
      expect(events.length, equals(1));
      expect(events.first.title, equals('Test Event'));

      // 移除事件
      controller.removeEvent(event);
      expect(controller.getEventsForDate(event.date), isEmpty);
    });

    test('Date selection works correctly', () {
      final controller = CalendarController();
      final testDate = DateTime(2024, 6, 15);

      controller.selectDate(testDate);
      expect(controller.selectedDate, equals(testDate));
    });

    test('Month navigation works correctly', () {
      final controller = CalendarController(initialDate: DateTime(2024, 6, 1));
      
      // 下一个月
      controller.nextMonth();
      expect(controller.currentMonth.month, equals(7));
      expect(controller.currentMonth.year, equals(2024));

      // 上一个月
      controller.previousMonth();
      expect(controller.currentMonth.month, equals(6));
      expect(controller.currentMonth.year, equals(2024));
    });

    test('Range selection works correctly', () {
      final controller = CalendarController();
      final startDate = DateTime(2024, 6, 10);
      final endDate = DateTime(2024, 6, 20);

      controller.setDateRange(startDate, endDate);
      expect(controller.rangeStart, equals(startDate));
      expect(controller.rangeEnd, equals(endDate));
    });
  });

  group('CalendarUtils Tests', () {
    test('isSameDay works correctly', () {
      final date1 = DateTime(2024, 6, 15, 10, 30);
      final date2 = DateTime(2024, 6, 15, 20, 45);
      final date3 = DateTime(2024, 6, 16, 10, 30);

      expect(CalendarUtils.isSameDay(date1, date2), isTrue);
      expect(CalendarUtils.isSameDay(date1, date3), isFalse);
      expect(CalendarUtils.isSameDay(null, date1), isFalse);
      expect(CalendarUtils.isSameDay(date1, null), isFalse);
    });

    test('isToday works correctly', () {
      final today = DateTime.now();
      final yesterday = today.subtract(Duration(days: 1));

      expect(CalendarUtils.isToday(today), isTrue);
      expect(CalendarUtils.isToday(yesterday), isFalse);
    });

    test('isInMonth works correctly', () {
      final date = DateTime(2024, 6, 15);
      final month = DateTime(2024, 6, 1);
      final differentMonth = DateTime(2024, 7, 1);

      expect(CalendarUtils.isInMonth(date, month), isTrue);
      expect(CalendarUtils.isInMonth(date, differentMonth), isFalse);
    });

    test('isWeekend works correctly', () {
      final saturday = DateTime(2024, 6, 15); // 假设这是周六
      final sunday = DateTime(2024, 6, 16);   // 假设这是周日
      final monday = DateTime(2024, 6, 17);   // 假设这是周一

      expect(CalendarUtils.isWeekend(saturday), isTrue);
      expect(CalendarUtils.isWeekend(sunday), isTrue);
      expect(CalendarUtils.isWeekend(monday), isFalse);
    });

    test('isInRange works correctly', () {
      final date = DateTime(2024, 6, 15);
      final start = DateTime(2024, 6, 10);
      final end = DateTime(2024, 6, 20);
      final outsideDate = DateTime(2024, 6, 25);

      expect(CalendarUtils.isInRange(date, start, end), isTrue);
      expect(CalendarUtils.isInRange(outsideDate, start, end), isFalse);
      expect(CalendarUtils.isInRange(date, null, end), isFalse);
      expect(CalendarUtils.isInRange(date, start, null), isFalse);
    });

    test('getMonthDates returns correct number of dates', () {
      final month = DateTime(2024, 6, 1);
      final dates = CalendarUtils.getMonthDates(month);

      // 应该返回42个日期（6周 × 7天）
      expect(dates.length, equals(42));
      
      // 第一个日期应该是周日
      expect(dates.first.weekday % 7, equals(0));
    });

    test('formatMonthYear works correctly', () {
      final date = DateTime(2024, 6, 15);
      final monthLabels = ['一月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '十一月', '十二月'];
      
      final formatted = CalendarUtils.formatMonthYear(date, monthLabels);
      expect(formatted, equals('2024年六月'));
    });
  });

  group('CalendarEvent Tests', () {
    test('CalendarEvent equality works correctly', () {
      final event1 = CalendarEvent(
        id: '1',
        title: 'Event 1',
        date: DateTime.now(),
      );
      
      final event2 = CalendarEvent(
        id: '1',
        title: 'Event 1 Modified',
        date: DateTime.now().add(Duration(days: 1)),
      );
      
      final event3 = CalendarEvent(
        id: '2',
        title: 'Event 2',
        date: DateTime.now(),
      );

      // 相同ID的事件应该相等
      expect(event1, equals(event2));
      expect(event1.hashCode, equals(event2.hashCode));
      
      // 不同ID的事件应该不相等
      expect(event1, isNot(equals(event3)));
    });
  });

  group('CalendarConfig Tests', () {
    test('CalendarConfig copyWith works correctly', () {
      const originalConfig = CalendarConfig(
        primaryColor: Colors.blue,
        dayHeight: 48.0,
      );

      final newConfig = originalConfig.copyWith(
        primaryColor: Colors.red,
      );

      expect(newConfig.primaryColor, equals(Colors.red));
      expect(newConfig.dayHeight, equals(48.0)); // 未修改的属性保持不变
    });

    test('CalendarConfig has correct default values', () {
      const config = CalendarConfig();

      expect(config.primaryColor, equals(Colors.blue));
      expect(config.backgroundColor, equals(Colors.white));
      expect(config.dayHeight, equals(48.0));
      expect(config.enableSwipeToChangeMonth, isTrue);
      expect(config.showEventDots, isTrue);
      expect(config.maxEventDots, equals(3));
    });
  });
}
