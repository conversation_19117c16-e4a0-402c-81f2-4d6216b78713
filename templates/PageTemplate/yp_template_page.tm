import 'package:flutter/material.dart';
import 'package:gdjg_pure_flutter/utils/route_util/base_page/base_ful_page.dart';
import 'vm/{NAME}[-s]_viewmodel.dart';

/// @date {year}/{month}/{day}
/// @param props 页面路由参数
/// @returns
/// @description {NAME}[-C]页面入口
class {NAME}[-C]Page extends BaseFulPage {
  {NAME}[-C]Page({super.key}) : super(appBar: YPAppBar(title: "标题"));

  @override
  State<{NAME}[-C]Page> createState() => _{NAME}[-C]PageState();
}

class _{NAME}[-C]PageState extends BaseFulPageState<{NAME}[-C]Page> {
  final {NAME}[-C]ViewModel viewModel = {NAME}[-C]ViewModel();

  @override
  Widget yBuild(BuildContext context) {
    return Center(
        child: Text(viewModel.uiState.value.data.toString()),
      );
  }
}
