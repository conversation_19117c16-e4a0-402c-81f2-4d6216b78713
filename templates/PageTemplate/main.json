{"name": "PageTemplate", "description": "generate dart UI files", "param": ["NAME"], "selectParam": [], "addFile": [{"name": "{NAME}[-s]_page.dart", "path": "./{NAME}[-s]", "fileTemplatePath": "yp_template_page.tm"}, {"name": "{NAME}[-s]_viewmodel.dart", "path": "./{NAME}[-s]/vm", "fileTemplatePath": "yp_template_viewmodel.tm"}, {"name": "{NAME}[-s]_ui_rep.dart", "path": "./{NAME}[-s]/ui_rep", "fileTemplatePath": "yp_template_ui_rep.tm", "description": "UI请求网络实现类；业务实体类"}, {"name": "{NAME}[-s]_ui_state.dart", "path": "./{NAME}[-s]/vm/protocol", "fileTemplatePath": "yp_template_ui_state.tm", "description": "UI实体"}, {"name": "{NAME}[-s]_props.dart", "path": "./{NAME}[-s]/entity", "fileTemplatePath": "yp_template_props.tm", "description": "页面入参"}], "insertInFile": [], "globalBasePath": ""}