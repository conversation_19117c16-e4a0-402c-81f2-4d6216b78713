import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../base/page_error_view.dart';
import '../base/page_loading_view.dart';
import 'entity/{NAME}[-s]_props.dart';
import 'vm/{NAME}[-s]_vmcell.dart';

/// @date {year}/{month}/{day}
/// @param props 页面路由参数
/// @returns
/// @description {NAME}[-C]页面入口
class {NAME}[-C]View extends StatelessWidget {
  {NAME}[-C]View({super.key, this.props});

  final {NAME}[-C]Props? props;
  final {NAME}[-C]VMCell viewmodel = {NAME}[-C]VMCell();

  /// 有的组件不需要加载转态和错误处理，去掉即可
  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (viewmodel.isLoading.value) {
        return const PageLoadingView();
      }
      if (viewmodel.uiState.value.isShowError == true) {
        return PageErrorView(onReload: () => viewmodel.fetchData());
      }
      return contentView();
    });
  }

  /// 实际展示的视图，需要处理：
  /// 1. 画出UI
  /// 2. 绑定UIState
  /// 3. 处理事件监听
  /// 4. 向VM传递来自View层的事件
  Widget contentView() {
    // 进行事件处理
    // handle{NAME}[-C]VMEvent(props.vm)
    return Center(
      child: Text(viewmodel.uiState.value.data.toString()),
    );
  }
}
