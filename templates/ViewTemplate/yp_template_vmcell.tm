import "package:get/get.dart";
import "../ui_rep/{NAME}[-s]_ui_rep.dart";
import "protocol/{NAME}[-s]_ui_state.dart";

/// @date {year}/{month}/{day}
/// @description {NAME}[-C]页VMCell
/// 1. 处理数据的加载状态，如loading和错误视图等
/// 2. 观测UIRep中的业务实体转为UIState
/// 3. 接受来自view的意图并分发事件进行处理
/// 4. 对View层暴露事件（操作视图和处理一些与系统权限交互等场景）
class {NAME}[-C]VMCell {
  var isLoading = false.obs;
  var isShowError = false.obs;
  var uiState = {NAME}[-C]UIState().obs;
  var uiRep = {NAME}[-C]UIRep();

  {NAME}[-C]VMCell() {
    fetchData();
    ever(uiRep.entity, (value) {
      convertEntityToUIState(value);
    });
  }

  /// 初始化数据同步，如果不需要loading，则在这里去掉
  void fetchData() async {
    isLoading.value = true;
    try {
      // 同步获取数据
      // var result = uiRep.getStatus();
      var result = await uiRep.fetchData();
      // 这里的result仅做弹窗之类的处理，不在此处转为UI实体
    } catch (e) {
      uiState.value.data = "failed";
      uiState.value.isShowError = true;
    } finally {
      isLoading.value = false;
    }
  }

  /// 将实体数据转换为UIState
  /// @param entity
  /// @returns
  void convertEntityToUIState({NAME}[-C]UIRepEntity entity) {
    uiState.value.data = entity.data;
  }
}
