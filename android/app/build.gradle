plugins {
    id "com.android.application"
    id "kotlin-android"
    // The Flutter Gradle Plugin must be applied after the Android and Kotlin Gradle plugins.
    id "dev.flutter.flutter-gradle-plugin"
}

android {
    namespace = "com.yupao.gongdijigong"
    compileSdk = flutter.compileSdkVersion
    ndkVersion = flutter.ndkVersion

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_1_8
        targetCompatibility = JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_1_8
    }

    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId = "com.yupao.gongdijigong2"
        // You can update the following values to match your application needs.
        // For more information, see: https://flutter.dev/to/review-gradle-config.
        minSdk = 23
        targetSdk = flutter.targetSdkVersion
        versionCode = flutter.versionCode
        versionName = flutter.versionName
    }

    buildTypes {
        release {
            // TODO: Add your own signing config for the release build.
            // Signing with the debug keys for now, so `flutter run --release` works.
            signingConfig = signingConfigs.debug
        }
    }

    dependencies {
        implementation 'com.yupao.ht_net:ht_net:1.0.27'
        implementation 'com.yupao.utils:utils_system:6.2.0.3'
        implementation "androidx.appcompat:appcompat:1.6.1"
        implementation 'com.alibaba:fastjson:1.2.31'

        implementation 'com.meituan.android.walle:library:1.1.6'
        implementation 'com.yupao.statistics:feature_statistics:1.1.0'

        modules {
            module("com.tencent:mmkv-static") {
                replacedBy("com.tencent:mmkv", "Using mmkv for flutter")
            }
            module("com.tencent:mmkv-shared") {
                replacedBy("com.tencent:mmkv", "Using mmkv for flutter")
            }
        }

    }
}

flutter {
    source = "../.."
}
