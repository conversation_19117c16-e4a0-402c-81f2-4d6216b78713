allprojects {
    repositories {
        maven { url 'https://maven.aliyun.com/repository/google' }    // 阿里云 Google 镜像
        maven { url 'https://maven.aliyun.com/repository/public' }
        maven { url "https://mirrors.cloud.tencent.com/flutter" }

        maven {
            allowInsecureProtocol = true
            url "http://repository.vrtbbs.com/repository/maven-public/"
        }

        maven {
            url 'https://artifact.bytedance.com/repository/Volcengine/'
        }

        google()
        mavenCentral()
    }
}

rootProject.buildDir = "../build"
subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"
}
subprojects {
    project.evaluationDependsOn(":app")
}

tasks.register("clean", Delete) {
    delete rootProject.buildDir
}
