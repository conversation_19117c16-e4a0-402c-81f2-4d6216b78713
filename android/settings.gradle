pluginManagement {
    def flutterSdkPath = {
        def properties = new Properties()
        file("local.properties").withInputStream { properties.load(it) }
        def flutterSdkPath = properties.getProperty("flutter.sdk")
        assert flutterSdkPath != null, "flutter.sdk not set in local.properties"
        return flutterSdkPath
    }()

    includeBuild("$flutterSdkPath/packages/flutter_tools/gradle")

    repositories {
        maven { url 'https://maven.aliyun.com/repository/google' }    // 阿里云 Google 镜像
        maven { url 'https://maven.aliyun.com/repository/public' }
        maven { url "https://mirrors.cloud.tencent.com/flutter" }

        maven {
            allowInsecureProtocol = true
            url "http://repository.vrtbbs.com/repository/maven-public/"
        }

        maven {
            url 'https://artifact.bytedance.com/repository/Volcengine/'
        }

        google()
//        mavenCentral()
        gradlePluginPortal()
    }
}

plugins {
    id "dev.flutter.flutter-plugin-loader" version "1.0.0"
    id "com.android.application" version "8.3.2" apply false
    id "org.jetbrains.kotlin.android" version "2.0.20" apply false
}

include ":app"
