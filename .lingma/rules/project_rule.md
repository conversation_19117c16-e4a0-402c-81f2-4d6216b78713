## 总原则

你是AI编程助手，遵循核心工作流（研究->构思->计划->执行->评审）用中文协助用户，面向专业程序员，交互应简洁专业，避免不必要解释。

## 1.  MCP使用规则

### 1.interactive-feedback

1.  在任何过程、任务或对话中，无论是提问、回应还是完成阶段任务，都必须调用 MCP mcp-feedback-enhanced。
2.  收到用户反馈时，如果反馈内容不为空，必须再次调用 MCP mcp-feedback-enhanced，并根据反馈调整行为。
3.  只有当用户明确表示“结束”或“不再需要交互”时，才能停止调用 MCP mcp-feedback-enhanced，然后过程才算完成。
4.  除非收到结束命令，否则所有步骤都必须反复调用 MCP mcp-feedback-enhanced。
5.  在完成任务之前，使用 MCP mcp-feedback-enhanced 向用户请求反馈。
6.  \[沟通守则]
    响应以模式标签 \[模式：X] 开始，初始为 \[模式：研究]。
    核心工作流严格按研究->构思->计划->执行->评审 顺序流转，用户可指令跳转。
7.  \[核心工作流详解]
    \[模式：研究]：理解需求。
    \[模式：构思]：提供至少两种可行方案及评估（例如：方案1：描述）。完成后用interactive-feedback请求用户选择。
    \[模式：计划]：将选定方案细化为详尽、有序、可执行的步骤清单（含原子操作：文件、函数/类、逻辑概要；预期结果；）。不写完整代码。完成后用interactive-feedback请求用户批准。
    \[模式：执行]：必须用户批准方可执行。严格按计划编码执行。计划简要（含上下文和计划）存入./issues/任务名.md。关键步骤后及完成时用interactive-feedback反馈。
    \[模式：评审]：对照计划评估执行结果，报告问题与建议。完成后用interactive-feedback请求用户确认。
    \[快速模式]
    \[模式：快速]：跳过核心工作流，快速响应。完成后用interactive-feedback请求用户确认。
    8.\[主动反馈与MCP服务]
    通用反馈：研究/构思遇疑问时，使用 interactive\_feedback 征询意见。任务完成（对话结束）前也需征询。
    MCP服务：
    interactive\_feedback: 用户反馈。

## 2.Flutter/Dart

### 1.命名方式

*   类名应使用大驼峰命名法（PascalCase），如 `SendCodeNetModel`。
*   变量和方法名应使用小驼峰命名法（camelCase），如 `token`、`toJson()`。
*   文件名也应使用小驼峰命名法，如 `send_code_net_model.dart`。
*   文件名字和类名必须保持一致，如文件名字为：`send_code_net_model.dart`，类名为：`SendCodeNetModel`

### 2.参数模型

*   这是一个参数模型类，通常用于方法的入参，如一个方法的入参很多，会使用参数模型把参数封装起来。
*   文件结尾必须是`param_model`。如`code_login_param_model.dart`。
*   模型中的所有字段类型不能为可空。
*   必须需要一个toMap方法。
*   原则上不允许出现任何的其他的方法。

以下是参数模型的示例

```
import 'login_share_param_model.dart';

class CodeLoginParamModel {
  String code;
  ShareReq shareReq;
  String tel;
  String verifyToken;

  CodeLoginParamModel({
    required this.code,
    required this.shareReq,
    required this.tel,
    required this.verifyToken,
  });

  Map<String, Object> toMap() {
    return {
      'code': code,
      'shareReq': shareReq.toMap(),
      'tel': tel,
      'verifyToken': verifyToken,
    };
  }
}

```

### 3.网络模型

*   这是一个网络数据模型类，通常用于接收接口数据返回解析。
*   文件的结尾必须是net\_model结尾。如`login_code_login_net_model.dart`。
*   网络实体类需要有注解@JsonSerializable()
*   每一个网络实体类都有一个补充类，专门负责 JSON 编码/解码。如`send_code_net_model.dart`会有一个`send_code_net_model.g.dart`。
*   原则上不允许出现除编码/解法方法以外的任何的其他的方法。

以下是`send_code_net_model.dart`示例：

    import 'dart:convert';

    import 'package:json_annotation/json_annotation.dart';

    part 'send_code_net_model.g.dart';

    @JsonSerializable()
    class SendCodeNetModel {
      /// 剩余可发送次数
      int? remainingSendTimes;
      /// 校验token（校验验证码时使用）
      String? verifyToken;
      /// 发送间隔（单位秒）
      int? sendInterval;

      SendCodeNetModel();

      factory SendCodeNetModel.fromJson(Map<String, dynamic> json) => _$LoginIgnoreSendModelFromJson(json);

      Map<String, dynamic> toJson() => _$LoginIgnoreSendModelToJson(this);

      @override
      String toString() {
        return jsonEncode(this);
      }
    }

以下是`send_code_net_model.g.dart`示例

```
// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'send_code_net_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SendCodeNetModel _$LoginIgnoreSendModelFromJson(
        Map<String, dynamic> json) =>
    SendCodeNetModel()
      ..remainingSendTimes = (json['remainingSendTimes'] as num?)?.toInt()
      ..verifyToken = json['verifyToken'] as String?
      ..sendInterval = (json['sendInterval'] as num?)?.toInt();

Map<String, dynamic> _$LoginIgnoreSendModelToJson(
    SendCodeNetModel instance) =>
    <String, dynamic>{
      'remainingSendTimes': instance.remainingSendTimes,
      'verifyToken': instance.verifyToken,
      'sendInterval': instance.sendInterval,
    };

```

### 4.远程数据来源

*   这是一个远程数据来源类，通常用于发起网络请求。相同业务域下的网络请求，可以放在同一个远程数据来源类中。
*   文件结尾必须是`rds`。如：`auth_rds`

#### 1.声明接口请求方法

*   该方法必须为异步请求。返回类型固定为`Future<RespResult<T>>` ,其中泛型`T`为任意Object。
*   方法发起请求的类必须是 `NetCore`类。

    以下是请求方法的示例。

          Future<RespResult<SendCodeNetModel>> sendCode(String phone) async {
            return await NetCore.requestYPJAVA(
                BaseBizRequestEntity(
                    url: '/reach/v1/verifyCode/loginIgnore/send',
                    method: HTTP_METHOD.POST,
                    content: {
                      "biz": "login",
                      "tel": phone,
                    }),
                (json) => SendCodeNetModel.fromJson(json));
          }

以下是远程来源类的示例

```
import 'package:gdjg_pure_flutter/data/account/ds/model/net/send_code_net_model.dart';
import 'package:net_plugin/pigeon/api_generated/net_channel.g.dart';
import 'package:net_plugin/pigeon/net_result/resp_result.dart';
import 'package:net_plugin/pigeon/net_service/net_core.dart';

import 'model/net/login_code_login_net_model.dart';
import 'model/net/waa_login_net_model.dart';
import 'model/param/code_login_param_model.dart';
import 'model/param/one_key_login_params_model.dart';
import 'model/param/waa_login_param_model.dart';
import 'model/param/we_chat_login_params_model.dart';


class AuthRds {
  ///  发送验证码
  Future<RespResult<SendCodeNetModel>> sendCode(String phone) async {
    return await NetCore.requestYPJAVA(
        BaseBizRequestEntity(
            url: '/reach/v1/verifyCode/loginIgnore/send',
            method: HTTP_METHOD.POST,
            content: {
              "biz": "login",
              "tel": phone,
            }),
        (json) => SendCodeNetModel.fromJson(json));
  }

  ///  验证码登陆
  Future<RespResult<LoginResultNetModel>> codeLogin(CodeLoginParamModel param) async {
    return await NetCore.requestYPJAVA(
        BaseBizRequestEntity(
            url: '/account/v1/login/codeLogin',
            method: HTTP_METHOD.POST,
            content: param.toMap()),
        (json) => LoginResultNetModel.fromJson(json));
  }

  /// 一键登录
  Future<RespResult<LoginResultNetModel>> oneKeyLogin(OneKeyLoginParamsModel param) async {
    return await NetCore.requestYPJAVA(
        BaseBizRequestEntity(
            url: '/account/v1/login/oneClickLogin',
            method: HTTP_METHOD.POST,
            content: param.toMap()),
        (json) => LoginResultNetModel.fromJson(json));
  }

  /// 微信登录
  Future<RespResult<LoginResultNetModel>> weChatLogin(WeChatLoginParamsModel param) async {
    return await NetCore.requestYPJAVA(
        BaseBizRequestEntity(
            url: '/account/v1/login/wechatAuthLogin',
            method: HTTP_METHOD.POST,
            content: param.toMap()),
        (json) => LoginResultNetModel.fromJson(json));
  }
  /// 工地记工登录
  Future<RespResult<WaaLoginNetModel>> waaLogin(WaaLoginParamModel param) async {
    return await NetCore.requestYPJAVA(
        BaseBizRequestEntity(
            url: '/api/member/get_yuapo_info',
            method: HTTP_METHOD.POST,
            content: param.toMap()),
        (json) => WaaLoginNetModel.fromJson(json));
  }
}

```

