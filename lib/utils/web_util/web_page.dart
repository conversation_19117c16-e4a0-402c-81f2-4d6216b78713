import 'package:flutter/material.dart';
import 'package:gdjg_pure_flutter/utils/route_util/base_page/base_ful_page.dart';
import 'package:gdjg_pure_flutter/utils/web_util/web_page_param.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:webview_flutter_android/webview_flutter_android.dart';
import 'package:webview_flutter_wkwebview/webview_flutter_wkwebview.dart';

class WebPage extends BaseFulPage {
  const WebPage({Key? key}) : super(key: key);

  @override
  State createState() => _WebPageState();
}

class _WebPageState<WebPage> extends BaseFulPageState {
  late final WebViewController _webViewController;
  double progress = 0.0;


  @override
  void onPageRoute(Object? routeParams, bool fromLaunchTask) {
    // TODO: implement onPageRoute
    super.onPageRoute(routeParams, fromLaunchTask);
    if (routeParams != null) {
      WebPageParam webPageParam = routeParams as WebPageParam;
      String title = routeParams.title??"";
      dynamicTitle = title;
      // #docregion platform_features
      late final PlatformWebViewControllerCreationParams params;
      if (WebViewPlatform.instance is WebKitWebViewPlatform) {
        params = WebKitWebViewControllerCreationParams(
          allowsInlineMediaPlayback: true,
          mediaTypesRequiringUserAction: const <PlaybackMediaTypes>{},
        );
      } else {
        params = const PlatformWebViewControllerCreationParams();
      }

      final WebViewController controller =
      WebViewController.fromPlatformCreationParams(params);
      // #enddocregion platform_features
      controller
        ..setJavaScriptMode(JavaScriptMode.unrestricted)
        ..setBackgroundColor(Colors.white)
        ..setNavigationDelegate(
          NavigationDelegate(
              onProgress: (int progress) {
                debugPrint('WebView is loading (progress : $progress%)');
                setState(() {
                  this.progress = progress / 100;
                });
              },
              onNavigationRequest: (NavigationRequest request) async {
                if (request.url.startsWith('weixin://') ||
                    request.url.startsWith('yupao://')) {
                  // 使用 url_launcher 打开 URL
                  if (await canLaunchUrl(Uri.parse(request.url))) {
                    await launchUrl(Uri.parse(request.url),
                        mode: LaunchMode.externalApplication);
                  } else {
                    debugPrint('无法打开 URL: $request.url');
                  }
                  return NavigationDecision.prevent;
                }
                return NavigationDecision.navigate;
              },
              onPageStarted: (String url) {},
              onPageFinished: (url) async {
                if (title.isEmpty) {
                  _webViewController
                      .runJavaScriptReturningResult("document.title")
                      .then((value) {
                    setState(() {
                      title = value.toString(); // 更新标题
                    });
                  });
                }
              }),
        )
        ..loadRequest(Uri.parse(webPageParam.url));
      // #docregion platform_features
      if (controller.platform is AndroidWebViewController) {
        AndroidWebViewController.enableDebugging(true);
        (controller.platform as AndroidWebViewController)
            .setMediaPlaybackRequiresUserGesture(false);
      }
      // #enddocregion platform_features
      _webViewController = controller;
    }
  }

  Widget _buildWebViewWidget(WebViewController webVC) {
    if (WebViewPlatform.instance is AndroidWebViewPlatform) {
      return WebViewWidget.fromPlatformCreationParams(
        params: AndroidWebViewWidgetCreationParams(controller: webVC.platform, displayWithHybridComposition: true),
      );
    }
    // 其他系统保持默认值即可
    return WebViewWidget(controller: webVC);
  }

  @override
  Widget yBuild(BuildContext context) {
    return Container(
      width: MediaQuery.of(context).size.width,
      height: double.infinity,
      color: Colors.white,
      child: Stack(
        children: [
          _buildWebViewWidget(_webViewController),
          if (progress < 1.0)
            Positioned(
              top: 0,
              left: 0,
              right: 0,
              child: LinearProgressIndicator(
                minHeight: 2,
                value: progress,
                backgroundColor: Colors.grey[200],
                valueColor:
                const AlwaysStoppedAnimation<Color>(Colors.blue),
              ),
            ),
        ],
      ),
    );
  }
}
