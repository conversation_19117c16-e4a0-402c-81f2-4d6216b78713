
import 'dart:async';

import 'package:event_bus/event_bus.dart';

class EventBusUtil {
  EventBusUtil._();

  static final EventBus _eventBus = EventBus();

  //此处的T需要明确具体的类型，外部调用需要指定
  static StreamSubscription<T> collect<T>(void Function(T event) notify) {
    return _eventBus.on<T>().listen((event) {
      notify(event);
    });
  }

  static void emit<T>(T event) {
    _eventBus.fire(event);
  }

  //如果在widget里监听，那么需要暂存sub，并在页面销毁的时候需要调用此方法取消监听
  static void cancelCollect(StreamSubscription sub) {
    sub.cancel();
  }
}