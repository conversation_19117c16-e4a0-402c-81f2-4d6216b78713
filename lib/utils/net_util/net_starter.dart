
import 'package:gdjg_pure_flutter/utils/net_util/net_init_supplier_impl.dart';
import 'package:gdjg_pure_flutter/utils/net_util/net_interceptor_impl.dart';
import 'package:gdjg_pure_flutter/utils/net_util/net_supplier_impl.dart';
import 'package:net_plugin/pigeon/net_service/net_core.dart';
import 'package:net_plugin/pigeon/product/product.dart';

class NetStarter {
  static void init() {
    NetCore.setProductType(PRODUCT_TYPE.GDJG_APP);
    NetCore.registerHeaderSupplier(HeaderSupplierImpl.getInstance());
    NetCore.registerNetRespInterceptor(NetInterceptorImpl());
    NetCore.registerNetInitSupplier(NetInitSupplierImpl());
    NetCore.setHasAgreement(true);
  }

  // 同意隐私协议后调用
  static void matchPrivacyAgreement() {
    NetCore.setHasAgreement(true);
  }
}