import 'package:net_plugin/pigeon/net_service/header_supplier.dart';

import '../store_util/shared_prefs.dart';

class HeaderSupplierImpl implements HeaderSupplier {
  // 创建单例
  static final HeaderSupplierImpl _instance = HeaderSupplierImpl._();

  final SharedPreferencesHelper prefsHelper = SharedPreferencesHelper();
  HeaderSupplierImpl._();

  static getInstance() {
    return _instance;
  }


  //
  String _token = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpYXQiOjE3NTA0MTAwNTYsImV4cCI6MTc2MDc3ODA1NiwiZGF0YSI6eyJzaW5nbGUiOiJEVVhKWVNKTDM5N0JMRjY2IiwidWlkIjoyMDAzNzQzMiwiYnVzaW5lc3MiOiIyIiwic3lzdGVtX3R5cGUiOiJhbmRyb2lkIiwibWluaV90b2tlbiI6IkRVWEpZU0pMMzk3QkxGNjYiLCJpZCI6MjAwMzc0MzIsInV1aWQiOjIwMDM3NDMyfSwidG9rZW4iOnsicmVnUnQiOiJhbmRyb2lkIiwidGVuYW50S2V5IjoiSkdKWiIsInRlbmFudElkIjoyMDAzNzQzMiwicGFja2FnZU5hbWUiOiJjb20ueXVwYW8uZ29uZ2Rpamlnb25nIiwidXNlcklkIjoyMDAzNzQzMiwidG9rZW4iOiJEVVhKWVNKTDM5N0JMRjY2In19._oHP-bN0cArGbKlwFmJ-BDJGt-ZIsY90G4iTCHM29H0";
  String _jgjzToken = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJ0ZXN0LmxvZ2luIiwiaWF0IjoxNzUwNDEwMDU2LCJ1aWQiOjIwMDM3NDMyfQ.0iYTAVxzhutc6kFpJsMm6C6x_n3X7uvH7aj3vw3LJjc";
  String _uid = "20037432";


  set token(String token) {
    _token = token;
  }

  set uid(String uid) {
    _uid = uid;
  }


  set jgjzToken(String jgjzToken) {
    _jgjzToken = jgjzToken;
  }

  @override
  String applyAuth() {
    // 海投字段不用管
    return '';
  }


  @override
  String applyRole() {
    // 海投字段不用管
    return '';
  }

  @override
  String applyHTToken() {
    // 海投字段不用管
    return '';
  }

  @override
  String applyHTUserId() {
    // 海投字段不用管
    return '';
  }


  @override
  String applyToken() {
    return _token;
  }

  @override
  String applyUid() {
    return _uid;
  }

  @override
  String applyUUID() {
    return _uid;
  }

  @override
  String applyJGJZToken() {
    return _jgjzToken;
  }
}
