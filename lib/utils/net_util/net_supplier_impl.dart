import 'package:net_plugin/pigeon/net_service/header_supplier.dart';

import '../../data/account/repo/auth_repo.dart';
import '../store_util/shared_prefs.dart';

class HeaderSupplierImpl implements HeaderSupplier {
  // 创建单例
  static final HeaderSupplierImpl _instance = HeaderSupplierImpl._();

  final SharedPreferencesHelper prefsHelper = SharedPreferencesHelper();
  HeaderSupplierImpl._();
  final authRepo = AuthRepo();

  static getInstance() {
    return _instance;
  }


  @override
  String applyAuth() {
    // 海投字段不用管
    return '';
  }


  @override
  String applyRole() {
    // 海投字段不用管
    return '';
  }

  @override
  String applyHTToken() {
    // 海投字段不用管
    return '';
  }

  @override
  String applyHTUserId() {
    // 海投字段不用管
    return '';
  }


  @override
  String applyToken() {
    return 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.dvh4h4ddr8poXHMkMZJlZGoKueE4ji1_IUszbowl5Hc';
  }

  @override
  String applyUid() {
    return '25396857';
  }

  @override
  String applyUUID() {
    return '25396857';
  }

  @override
  String applyJGJZToken() {
    return 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJ0ZXN0LmxvZ2luIiwiaWF0IjoxNzUwODM3NzY0LCJ1aWQiOjI1Mzk2ODU3fQ.njpx276ZkKKI97DpFqI5RRugMXfhBgWv78H_pBX7LQA';
  }
}
