import 'package:flutter/foundation.dart';
import 'package:gdjg_pure_flutter/utils/store_util/shared_prefs.dart';
import 'package:net_plugin/pigeon/net_env/net_url_const.dart';

class NetEnv {
  // 静态私有实例
  static final NetEnv _instance = NetEnv._internal();

  bool _isTestEnv = false;
  String key = "isTestEnv";

  // 私有构造函数
  NetEnv._internal();

  // 工厂构造函数，返回单例实例
  factory NetEnv() {
    return _instance;
  }

  //是否是正式包
  bool isReleaseMode() {
    return kReleaseMode;
  }

  //用于判断API调用环境，而非正式包/测试包
  bool isTestEnv() {
    if (kReleaseMode) {
      return false;
    }
    SharedPreferencesHelper prefsHelper = SharedPreferencesHelper();
    _isTestEnv = prefsHelper.getBool(key) ?? true;
    return _isTestEnv;
  }

  void setTestEnv(bool isTestEnv) {
    SharedPreferencesHelper prefsHelper = SharedPreferencesHelper();
    prefsHelper.setBool(key, isTestEnv);
    _isTestEnv = isTestEnv;
  }


  // 测试环境
  final String phpTestBaseUrl = 'http://app-test.cdmgkj.cn';
  // 生产环境
  final String phpProdBaseUrl = 'https://app.cdmgkj.cn';

  getUploadDomain() {
    // if (isTestEnv()) {
    //   return phpTestBaseUrl;
    // } else {
    //   return phpProdBaseUrl;
    // }
    return phpProdBaseUrl;
  }

  getH5Domain() {
    // todo H5地址
    return '';
  }

  String getYPJavaDomain() {
    return NetUrlConst.getBaseUrl(API_DOMAIN.YP_JAVA, isTestEnv: isTestEnv());
  }
}
