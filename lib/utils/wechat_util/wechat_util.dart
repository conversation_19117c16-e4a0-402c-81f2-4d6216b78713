
import 'dart:io';
import 'dart:typed_data';

import 'package:fluwx/fluwx.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/toast_util.dart';
import 'package:gdjg_pure_flutter/utils/wechat_util/wechat_config.dart';

/// 微信工具类
class WeChatUtil {
  WeChatUtil._();
  static final Fluwx _fluwx = Fluwx();

  /// 微信AppID
  static String get _weChatAppId => WeChatConfig.weChatAppId;

  /// 是否已初始化
  static bool _isInitialized = false;

  /// 初始化微信SDK
  static Future<bool> initialize() async {
    if (_isInitialized) {
      return true;
    }

    try {
      // 检查AppID是否配置
      if (_weChatAppId.isEmpty) {
        return false;
      }

      // 注册微信API
      await _fluwx.registerApi(
        appId: _weChatAppId,
        doOnAndroid: true,
        doOnIOS: true,
        universalLink: WeChatConfig.weChatUniversalLink,
      );

      _isInitialized = true;
      return true;
    } catch (e) {
      return false;
    }
  }

  /// 检查微信是否已安装
  static Future<bool> checkWeChatInstalled() async {
    try {
      return await _fluwx.isWeChatInstalled;
    } catch (e) {
      return false;
    }
  }

  /// 跳转微信小程序
  /// [appId] 小程序AppID
  /// [originalId] 小程序原始ID（可选，优先使用）
  /// [path] 小程序页面路径（可选）
  /// [type] 小程序类型，默认正式版
  static Future<bool> launchMiniProgram({
    required String appId,
    String? originalId,
    String? path,
    WXMiniProgramType type = WXMiniProgramType.release,
  }) async {
    try {
      // 确保微信SDK已初始化
      if (!_isInitialized) {
        final initResult = await initialize();
        if (!initResult) {
          ToastUtil.showToast('微信SDK初始化失败');
          return false;
        }
      }

      // 检查微信是否已安装
      final isInstalled = await checkWeChatInstalled();
      if (!isInstalled) {
        ToastUtil.showToast('请先安装微信');
        return false;
      }

      // 检查必要参数
      if (appId.isEmpty && (originalId?.isEmpty ?? true)) {
        ToastUtil.showToast('小程序ID不能为空');
        return false;
      }

      // 使用原始ID或AppID作为username
      final username = originalId?.isNotEmpty == true ? originalId! : appId;

      // 创建小程序对象并执行跳转
      final miniProgram = MiniProgram(
        username: username,
        path: path,
        miniProgramType: type,
      );

      final result = await _fluwx.open(target: miniProgram);

      if (!result) {
        ToastUtil.showToast('跳转小程序失败');
      }

      return result;
    } catch (e) {
      ToastUtil.showToast('跳转小程序失败');
      return false;
    }
  }

  /// 分享文本到微信
  /// [text] 分享的文本内容
  /// [scene] 分享场景（会话/朋友圈/收藏）
  static Future<bool> shareText({
    required String text,
    WeChatScene scene = WeChatScene.session,
  }) async {
    try {
      // 确保微信SDK已初始化
      if (!_isInitialized) {
        final initResult = await initialize();
        if (!initResult) {
          ToastUtil.showToast('微信SDK初始化失败');
          return false;
        }
      }

      // 检查微信是否已安装
      final isInstalled = await checkWeChatInstalled();
      if (!isInstalled) {
        ToastUtil.showToast('请先安装微信');
        return false;
      }

      // 分享文本
      final model = WeChatShareTextModel(text, scene: scene);
      final result = await _fluwx.share(model);

      if (result) {
        ToastUtil.showToast('分享成功');
      } else {
        ToastUtil.showToast('分享失败');
      }

      return result;
    } catch (e) {
      ToastUtil.showToast('分享失败');
      return false;
    }
  }

  /// 分享网页到微信
  /// [webpageUrl] 网页链接
  /// [title] 分享标题
  /// [description] 分享描述
  /// [thumbnail] 缩略图数据
  /// [scene] 分享场景
  static Future<bool> shareWebpage({
    required String webpageUrl,
    String? title,
    String? description,
    String? thumbnail,
    WeChatScene scene = WeChatScene.session,
  }) async {
    try {
      // 确保微信SDK已初始化
      if (!_isInitialized) {
        final initResult = await initialize();
        if (!initResult) {
          ToastUtil.showToast('微信SDK初始化失败');
          return false;
        }
      }

      // 检查微信是否已安装
      final isInstalled = await checkWeChatInstalled();
      if (!isInstalled) {
        ToastUtil.showToast('请先安装微信');
        return false;
      }

      // 分享网页
      final model = WeChatShareWebPageModel(
        webpageUrl,
        title: title,
        description: description,
        scene: scene,
      );

      final result = await _fluwx.share(model);

      if (result) {
        ToastUtil.showToast('分享成功');
      } else {
        ToastUtil.showToast('分享失败');
      }

      return result;
    } catch (e) {
      ToastUtil.showToast('分享失败');
      return false;
    }
  }

  /// 分享小程序到微信
  /// [webpageUrl] 兼容低版本的网页链接
  /// [userName] 小程序原始ID
  /// [path] 小程序页面路径
  /// [title] 分享标题
  /// [description] 分享描述
  /// [thumbnail] 缩略图
  /// [scene] 分享场景
  static Future<bool> shareMiniProgram({
    required String webpageUrl,
    required String userName,
    String? path,
    String? title,
    String? description,
    String? thumbnail,
    WeChatScene scene = WeChatScene.session,
  }) async {
    try {
      // 确保微信SDK已初始化
      if (!_isInitialized) {
        final initResult = await initialize();
        if (!initResult) {
          ToastUtil.showToast('微信SDK初始化失败');
          return false;
        }
      }

      // 检查微信是否已安装
      final isInstalled = await checkWeChatInstalled();
      if (!isInstalled) {
        ToastUtil.showToast('请先安装微信');
        return false;
      }

      // 分享小程序
      final model = WeChatShareMiniProgramModel(
        webPageUrl: webpageUrl,
        userName: userName,
        path: path ?? '',
        title: title,
        description: description,
        scene: scene,
      );

      final result = await _fluwx.share(model);

      if (result) {
        ToastUtil.showToast('分享成功');
      } else {
        ToastUtil.showToast('分享失败');
      }

      return result;
    } catch (e) {
      ToastUtil.showToast('分享失败');
      return false;
    }
  }

  /// 打开微信应用
  static Future<bool> openWeChatApp() async {
    try {
      // 确保微信SDK已初始化
      if (!_isInitialized) {
        final initResult = await initialize();
        if (!initResult) {
          ToastUtil.showToast('微信SDK初始化失败');
          return false;
        }
      }

      final result = await _fluwx.open(target: WeChatApp());

      if (!result) {
        ToastUtil.showToast('打开微信失败');
      }

      return result;
    } catch (e) {
      ToastUtil.showToast('打开微信失败');
      return false;
    }
  }
  /// 分享图片到微信
  /// [imageData] 图片字节数据
  /// [scene] 分享场景（会话/朋友圈/收藏）
  /// [title] 分享标题
  /// [description] 分享描述
  static Future<bool> shareImage({
    required Uint8List imageData,
    WeChatScene scene = WeChatScene.session,
    String? title,
    String? description,
  }) async {
    try {
      // 确保微信SDK已初始化
      if (!_isInitialized) {
        final initResult = await initialize();
        if (!initResult) {
          ToastUtil.showToast('微信SDK初始化失败');
          return false;
        }
      }

      // 检查微信是否已安装
      final isInstalled = await checkWeChatInstalled();
      if (!isInstalled) {
        ToastUtil.showToast('请先安装微信');
        return false;
      }

      // 创建图片分享对象
      final imageToShare = WeChatImageToShare(uint8List: imageData);

      // 分享图片
      final model = WeChatShareImageModel(
        imageToShare,
        scene: scene,
        title: title,
        description: description,
      );

      final result = await _fluwx.share(model);

      if (result) {
        ToastUtil.showToast('分享成功');
      } else {
        ToastUtil.showToast('分享失败');
      }

      return result;
    } catch (e) {
      ToastUtil.showToast('分享失败: $e');
      return false;
    }
  }

  ///分享文件到微信
  /// [filePath] 文件路径，例如 "xxx.elsx"
  /// [scene] 分享场景（会话/朋友圈/收藏）
  static Future<bool> shareFile({
    required String filePath,
    WeChatScene scene = WeChatScene.session,
  }) async {
    try {
      // 确保微信SDK已初始化
      if (!_isInitialized) {
        final initResult = await initialize();
        if (!initResult) {
          ToastUtil.showToast('微信SDK初始化失败');
          return false;
        }
      }

      // 检查微信是否已安装
      final isInstalled = await checkWeChatInstalled();
      if (!isInstalled) {
        ToastUtil.showToast('请先安装微信');
        return false;
      }

      // 分享文件
      final model = WeChatShareFileModel(
        WeChatFile.file(File(filePath)),
        scene: scene,
      );

      final result = await _fluwx.share(model);

      if (result) {
        ToastUtil.showToast('分享成功');
      } else {
        ToastUtil.showToast('分享失败11');
      }

      return result;
    } catch (e) {
      ToastUtil.showToast('分享失败: $e');
      return false;
    }
  }

}
