
import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:gdjg_pure_flutter/utils/system_util/string_util.dart';
import 'package:gdjg_pure_flutter/utils/system_util/yprint.dart';
import 'package:mmkv/mmkv.dart';

class KVUtil {
  static final Map<String, YPKV> _instanceMap = {};

  static const String _defaultFileName = "yp_default_kv_file";

  KVUtil._();

  static Future<String> initKV() async{
    MMKVLogLevel logLevel = kDebugMode ? MMKVLogLevel.Info : MMKVLogLevel.None;
    return MMKV.initialize(logLevel: logLevel);
  }

  static YPKV getKV([String fileName = _defaultFileName]) {
    if (_instanceMap.containsKey(fileName)) {
      return _instanceMap[fileName]!;
    } else {
      YPKV ypkv;
      ypkv = YPKV(mmkv: MMKV(fileName));
      _instanceMap[fileName] = ypkv;
      return ypkv;
    }
  }
}

class YPKV {
  MMKV mmkv;
  YPKV({
    required this.mmkv,
  });

  void setString(String key, String? value) {
    checkCapacity(value);
    mmkv.encodeString(key, value);
  }

  String? getString(String key, {String? defaultValue}) {
    String? value = mmkv.decodeString(key);
    if (value == null) {
      return defaultValue;
    } else {
      return value;
    }
  }

  void setInt(String key, int value) {
    mmkv.encodeInt(key, value);
  }

  int getInt(String key, {int defaultValue = 0}) {
    return mmkv.decodeInt(key);
  }

  void setBool(String key, bool value) {
    mmkv.encodeBool(key, value);
  }

  bool getBool(String key, {bool defaultValue = false}) {
    return mmkv.decodeBool(key, defaultValue: defaultValue);
  }

  void setDouble(String key, double value) {
    mmkv.encodeDouble(key, value);
  }

  double getDouble(String key, {double defaultValue = 0}) {
    return mmkv.decodeDouble(key, defaultValue: defaultValue);
  }

  T? getObject<T>(String key, T Function(Map<String, dynamic> json) fromJsonT) {
    String? jsonStr = mmkv.decodeString(key);
    if (jsonStr.isNullOrEmpty()) {
      return null;
    } else {
      try {
        var jsonMap = jsonDecode(jsonStr!);
        return fromJsonT(jsonMap);
      } catch (e) {
        yprint("getObject transfer error $e");
        return null;
      }
    }
  }

  void setObject(String key, Map<dynamic, dynamic> Function() toJson) {
    try {
      String jsonStr = jsonEncode(toJson());
      checkCapacity(jsonStr);
      mmkv.encodeString(key, jsonStr);
    } catch (e) {
      yprint("setObject transfer error $e");
    }
  }

  void checkCapacity(String? value) {
    if (!value.isNullOrEmpty()) {
      if (value!.length > 1024 * 1024 * 4) {
        yprint("ypkv exceed max size please be careful!!!", true);
      }
    }
  }
}