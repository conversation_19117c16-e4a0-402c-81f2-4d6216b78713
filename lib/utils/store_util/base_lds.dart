import 'dart:async';
import 'dart:convert';

import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:synchronized/synchronized.dart';

import 'kv_util.dart';

abstract class BaseLds<T> {

  static final _obsMap = <String, Rxn<dynamic>>{};

  final _lock = Lock(); // 同步锁
  late YPKV _store;

  BaseLds() {
    _store = KVUtil.getKV(_getKey());
  }

  String getBizName();

  String? getStoreIndex() => null;

  T fromJson(Map<String, dynamic> json) =>
      throw UnimplementedError("存入实体时需要实现 fromJson 方法");

  Map<String, dynamic> toJson(T value) =>
      throw UnimplementedError("取出实体时需要实现 toJson 方法");

  String _getKey() {
    final key = StringBuffer("${getBizName()}_${runtimeType.toString()}");
    if (getStoreIndex()?.isNotEmpty ?? false) {
      key.write("_${getStoreIndex()}");
    }
    return key.toString();
  }

  void save(T value) {
    final key = _getKey();

    _lock.synchronized(() async {
      try {
        if (value is String) {
          _store.setString(key, value);
        } else if (value is int) {
          _store.setInt(key, value);
        } else if (value is double) {
          _store.setDouble(key, value);
        } else if (value is bool) {
          _store.setBool(key, value);
        } else if (value is Map || value is Object) {
          final data = jsonEncode(toJson(value));
          _store.setString(key, data);
        } else {
          throw UnsupportedError(
              'Value of type $T is not supported for storage.');
        }

        _innerSet(value);
      } catch (e, stackTrace) {
        print("Error setting value: $e\nStack Trace: $stackTrace");
        rethrow;
      }
    });
  }

  T? get({T? defaultValue}) {

    final key = _getKey();
    T? result;

    _lock.synchronized(() {
      try {
        if (T == String) {
          result = _store.getString(key, defaultValue: defaultValue as String?)
              as T?;
        } else if (T == int) {
          final defaultIntValue = defaultValue ?? 0 as T;
          result =
              _store.getInt(key, defaultValue: defaultIntValue as int) as T;
        } else if (T == double) {
          final defaultDoubleValue = defaultValue ?? 0.0 as T;
          result = _store.getDouble(key,
              defaultValue: defaultDoubleValue as double) as T;
        } else if (T == bool) {
          final defaultBoolValue = defaultValue ?? false as T;
          result =
              _store.getBool(key, defaultValue: defaultBoolValue as bool) as T;
        } else if (T == Map || T == Object) {
          final jsonString = _store.getString(key);
          if (jsonString?.isNotEmpty ?? false) {
            final jsonData = jsonDecode(jsonString!);
            result = fromJson(jsonData);
          } else {
            result = defaultValue;
          }
        } else {
          throw UnsupportedError('Type $T is not supported for retrieval.');
        }
      } catch (e, stackTrace) {
        debugPrint("Error getting value: $e\nStack Trace: $stackTrace");
        result = defaultValue;
      }
    });

    return result;
  }

  Rxn<T> _getObs(){
    if (_obsMap.containsKey(_getKey())) {
      return _obsMap[_getKey()]! as Rxn<T>;
    }else{
      final obs = Rxn<T>();
      _obsMap[_getKey()] = obs;
      return obs;
    }
  }

  void _innerSet(T value) {
    _getObs().value = value;
  }

  StreamSubscription<T?> listen(void Function(T?) onData,
      {bool isListenDefaultIntValue = false}) {
    final stream = _getObs().listen(onData);
    if (isListenDefaultIntValue) {
      final data = get();
      if (data == null) {
        onData(data);
       }
    }
    return stream;
  }
}
