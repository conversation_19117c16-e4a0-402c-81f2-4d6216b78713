import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:gdjg_pure_flutter/utils/store_util/kv_util.dart';
import 'package:gdjg_pure_flutter/utils/wechat_util/wechat_util.dart';

import '../net_util/net_starter.dart';
import '../store_util/shared_prefs.dart';
import '../store_util/sp_keys.dart';

class AppInitUtil {
  static delayInit() {
    //三方sdk或者涉及到隐私的需要在隐私政策同意后方可调用
    // UmengUtil.init();
    // UmengUtil.initUMVerify();
    // VolcanoUtil.init();
    NetStarter.matchPrivacyAgreement();
    // // 初始化微信SDK
    // WeChatUtil.initialize();
  }

  static initIgnorePrivacy() async {
    WidgetsFlutterBinding.ensureInitialized();
    await KVUtil.initKV();
    NetStarter.init();
  }

  static appInit() async {
    _logMode();
    await initIgnorePrivacy();
    bool agreePrivacy =
        SharedPreferencesHelper().getBool(StoreKeys.key_agreement_accepted, defaultValue: false) ??
            false;
    if (agreePrivacy) {
      delayInit();
    }
  }

  static void _logMode() {
    if (kReleaseMode) {
      debugPrint('当前为 Release 模式');
    } else if (kProfileMode) {
      debugPrint('当前为 Profile 模式');
    } else {
      debugPrint('当前为 Debug 模式');
    }
  }
}
