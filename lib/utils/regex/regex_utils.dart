import 'package:gdjg_pure_flutter/utils/system_util/string_util.dart';

class RegexUtils {
  /// 匹配1开头的11位手机号
  static bool isMobile(String? value) {
    if (value.isNullOrEmpty()) {
      return false;
    }
    final reg = RegExp(r'^1\d{10}$');
    return reg.hasMatch(value!!);
  }

  /// 判断是否为4位验证码（纯数字）
  static bool isVerificationCode(String? value) {
    if (value.isNullOrEmpty()) {
      return false;
    }
    final reg = RegExp(r'^\d{4}$');
    return reg.hasMatch(value!!);
  }
}
