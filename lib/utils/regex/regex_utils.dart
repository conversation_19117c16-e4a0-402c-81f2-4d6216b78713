import 'package:gdjg_pure_flutter/utils/system_util/string_util.dart';

class RegexUtils {
  /// 匹配1开头的11位手机号
  static bool isMobile(String? value) {
    if (value.isNullOrEmpty()) {
      return false;
    }
    final reg = RegExp(r'^1\d{10}$');
    return reg.hasMatch(value!);
  }

  /// 判断是否为4位验证码（纯数字）
  static bool isVerificationCode(String? value) {
    if (value.isNullOrEmpty()) {
      return false;
    }
    final reg = RegExp(r'^\d{4}$');
    return reg.hasMatch(value!);
  }

  /// 判断是否为有效的URL
  static bool isUrl(String? value) {
    if (value.isNullOrEmpty()) {
      return false;
    }
    final reg = RegExp(r'^https?://[^\s/$.?#].[^\s]*$', caseSensitive: false);
    return reg.hasMatch(value!);
  }

  /// 判断字符是否为中文字符
  static bool _isChineseChar(String char) {
    if (char.isEmpty) return false;
    final codeUnit = char.codeUnitAt(0);

    // 中文字符的Unicode范围
    return (codeUnit >= 0x4E00 && codeUnit <= 0x9FFF) ||  // CJK统一汉字
           (codeUnit >= 0x3400 && codeUnit <= 0x4DBF) ||  // CJK扩展A
           (codeUnit >= 0xF900 && codeUnit <= 0xFAFF) ||  // CJK兼容汉字
           (codeUnit >= 0x2E80 && codeUnit <= 0x2EFF) ||  // CJK部首补充
           (codeUnit >= 0x2F00 && codeUnit <= 0x2FDF) ||  // 康熙部首
           (codeUnit >= 0x31C0 && codeUnit <= 0x31EF);    // CJK笔画
  }

  /// 判断字符串是否为纯中文字符
  static bool isChinese(String? value) {
    if (value.isNullOrEmpty()) {
      return false;
    }

    for (int i = 0; i < value!.length; i++) {
      if (!_isChineseChar(value[i])) {
        return false;
      }
    }
    return true;
  }

  /// 验证姓名格式（2-5个纯中文字符）
  static bool isValidName(String? value) {
    if (value.isNullOrEmpty()) {
      return false;
    }

    final trimmedValue = value!.trim();
    return trimmedValue.length >= 2 &&
           trimmedValue.length <= 5 &&
           isChinese(trimmedValue);
  }
}
