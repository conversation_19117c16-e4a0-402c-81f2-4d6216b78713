import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';

/// [backDismiss] true 可以硬件返回关闭弹框, false 不可以返回关闭弹框,默认false
/// [clickMaskDismiss] true 点击萌层可关闭弹框, false 不可关闭, 默认false
void showCommonDialog(
  CommonDialogConfig? config, {
  bool clickMaskDismiss = false,
  bool backDismiss = false,
}) {
  YPRoute.openDialog(
    tag: 'commonDialog',
    clickMaskDismiss: false,
    onBack: () => !backDismiss,
    builder: (context) => _CommonDialog(config),
  );
}

class CommonDialogConfig {
  final VoidCallback? onNegative;
  final VoidCallback? onPositive;
  final String? title;
  final String? content;
  final TextStyle? titleStyle;
  final TextStyle? contentStyle;
  final Widget? titleWidget;
  final Widget? contentWidget;
  final String? negative;
  final String? positive;
  final bool? hiddenNegative;
  final bool? hiddenPositive;
  final TextStyle? positiveStyle;
  final TextStyle? negativeStyle;


  CommonDialogConfig(
      {this.onNegative,
      this.onPositive,
      this.title,
      this.content,
      this.titleStyle,
      this.contentStyle,
      this.titleWidget,
      this.contentWidget,
      this.negative,
      this.positive,
      this.hiddenNegative,
      this.hiddenPositive,
      this.negativeStyle,
      this.positiveStyle});

  /// 外部要微调可以使用defaultTitleStyle.copyWith 改个新的传进来
  static TextStyle defaultTitleStyle = TextStyle(
      color: Color(0xFF323233), fontSize: 18.sp, fontWeight: FontWeight.bold);

  static TextStyle defaultContentStyle =
      TextStyle(color: Color(0xFF8A8A99), fontSize: 16.sp, height: 1.4);
}

class _CommonDialog extends StatelessWidget {
  const _CommonDialog(this.config, {super.key});

  final CommonDialogConfig? config;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(top: 24.h),
      width: MediaQuery.of(context).size.width * 0.85,
      decoration: BoxDecoration(
          color: Colors.white, borderRadius: BorderRadius.circular(8.r)),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [_buildTitle(), _buildContent(), _buildOperator()],
      ),
    );
  }

  _buildTitle() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.only(left: 20.w, right: 20.w, bottom: 8.h),
      child: config?.titleWidget ??
          Text(
            textAlign: TextAlign.center,
            config?.title ?? '温馨提示',
            style: config?.titleStyle ?? CommonDialogConfig.defaultTitleStyle,
          ),
    );
  }

  _buildContent() {
    return Container(
      constraints: BoxConstraints(maxHeight: 400.h),
      child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 12.w),
          child: SingleChildScrollView(
            child: config?.contentWidget ??
                Text(
                  config?.content ?? '',
                  style: config?.contentStyle ??
                      CommonDialogConfig.defaultContentStyle,
                ),
          )),
    );
  }

  _buildOperator() {
    dismiss() {
      YPRoute.closeDialog();
    }

    return Container(
      margin: EdgeInsets.only(top: 24.h),
      decoration: BoxDecoration(
          border:
              Border(top: BorderSide(color: Color(0xFFE6E6E6), width: 0.5.w))),
      child: SizedBox(
        height: 48.h,
        child: Row(
          children: [
            if (config?.hiddenNegative != true)
              Expanded(
                  child: GestureDetector(
                onTap: () {
                  dismiss();
                  config?.onNegative?.call();
                },
                child: Text(
                  config?.negative ?? '取消',
                  textAlign: TextAlign.center,
                  style:config?.negativeStyle?? TextStyle(
                    color: Color(0xFF323233),
                    fontSize: 18.sp,
                  ),
                ),
              )),
            if (!(config?.hiddenPositive == true ||
                config?.hiddenNegative == true))
              Container(
                width: 0.5.w, // 分割线高度
                color: Color(0xFFE6E6E6), // 带背景颜色
              ),
            if (config?.hiddenPositive != true)
              Expanded(
                  child: GestureDetector(
                onTap: () {
                  dismiss();
                  config?.onPositive?.call();
                },
                child: Text(
                  config?.positive ?? '确定',
                  textAlign: TextAlign.center,
                  style: config?.positiveStyle??TextStyle(
                      color: ColorsUtil.primaryColor,
                      fontSize: 18.sp,
                      fontWeight: FontWeight.bold),
                ),
              )),
          ],
        ),
      ),
    );
  }
}
