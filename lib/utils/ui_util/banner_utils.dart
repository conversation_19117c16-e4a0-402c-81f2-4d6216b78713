import 'package:flutter/material.dart';
import 'package:gdjg_pure_flutter/data/utils_data/carousel/entity/adposition_detail_biz_model.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/toast_util.dart';
import 'package:gdjg_pure_flutter/utils/wechat_util/wechat_util.dart';

/// Banner工具类
class BannerUtils {
  BannerUtils._();

  /// 处理banner点击事件
  /// [context] 上下文
  /// [bannerItem] banner数据
  /// [pageCode] 页面标识，用于埋点统计
  static Future<void> onItemClick(
    BuildContext context,
    AdpositionDetailABizModel bannerItem, {
    String? pageCode,
  }) async {
    if (!context.mounted) return;

    // 埋点统计
    _reportBannerClick(pageCode, bannerItem.code);

    // 如果有弹窗提示，先显示弹窗
    if (bannerItem.alert.isNotEmpty) {
      final confirmed = await _showAlertDialog(context, bannerItem.alert);
      if (!confirmed || !context.mounted) return;
    }

    // 执行跳转逻辑
    if (context.mounted) {
      await _handleJump(context, bannerItem);
    }
  }

  ///TODO: 显示确认弹窗
  static Future<bool> _showAlertDialog(BuildContext context, String message) async {
    bool confirmed = false;
    return confirmed;
  }

  /// 处理跳转逻辑
  static Future<void> _handleJump(
      BuildContext context, AdpositionDetailABizModel bannerItem) async {
    switch (bannerItem.type.toInt()) {
      case 1: // 根据code字段自行处理
        // TODO: 根据实现自定义处理逻辑
        break;
      case 2: // 链接
        await _handleWebLink(context, bannerItem);
        break;
      case 3: // 分享
        await _handleShare(context, bannerItem);
        break;
      case 4: // 其他
        await _handleOther(context, bannerItem);
        break;
      case 5: // 小程序
        await _handleMiniProgram(context, bannerItem);
        break;
      case 6: // 开屏广告
        await _handleLaunchScreen(context, bannerItem);
        break;
      default:
        break;
    }
  }

  /// 处理网页链接
  static Future<void> _handleWebLink(
      BuildContext context, AdpositionDetailABizModel bannerItem) async {
    final url = bannerItem.contentHref;

    if (url.isEmpty) {
      return;
    }

    // 跳转到WebView页面
    YPRoute.openWebPage(
      url: url,
      title: bannerItem.contentTitle.isNotEmpty ? bannerItem.contentTitle : null,
    );
  }

  /// 处理分享
  static Future<void> _handleShare(
      BuildContext context, AdpositionDetailABizModel bannerItem) async {
    // TODO: 实现分享功能
    ToastUtil.showToast('分享功能开发中');
  }

  /// 处理其他类型
  static Future<void> _handleOther(
      BuildContext context, AdpositionDetailABizModel bannerItem) async {
    ToastUtil.showToast('其他类型处理');
  }

  /// 处理小程序
  static Future<void> _handleMiniProgram(
      BuildContext context, AdpositionDetailABizModel bannerItem) async {
    try {
      if (bannerItem.miniAppid.isEmpty && bannerItem.originalId.isEmpty) {
        ToastUtil.showToast('小程序ID不能为空');
        return;
      }

      // //跳转
      // final result = await WeChatUtil.launchMiniProgram(
      //   appId: bannerItem.miniAppid,
      //   originalId: bannerItem.originalId,
      //   path: bannerItem.miniPath,
      // );
      //
      // if (!result) {
      //   debugPrint('小程序跳转失败: appId=${bannerItem.miniAppid}, originalId=${bannerItem.originalId}, path=${bannerItem.miniPath}');
      // }
    } catch (e) {
      debugPrint('跳转小程序异常: $e');
      ToastUtil.showToast('跳转小程序失败');
    }
  }

  /// 处理开屏广告
  static Future<void> _handleLaunchScreen(
      BuildContext context, AdpositionDetailABizModel bannerItem) async {
    // TODO: 实现开屏广告逻辑
    ToastUtil.showToast('开屏广告处理');
  }

  /// 埋点统计
  static void _reportBannerClick(String? pageCode, String bannerCode) {
    // TODO: 实现埋点统计
    // 可以集成友盟、神策等统计SDK
  }
}
