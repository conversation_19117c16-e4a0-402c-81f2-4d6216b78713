import 'dart:math';
import 'dart:ui';

class ColorsUtil {
  /// 获取随机颜色
  static Color get randomColor {
    return Color.fromARGB(255, Random().nextInt(255), Random().nextInt(255),
        Random().nextInt(255));
  }

  /// 主色调
  static Color get primaryColor => rgba(82, 144, 253, 1);

  static Color get ypPrimaryColor => Color(0xFF0092FF);

  /// 主背景色
  static Color get ypBgColor => Color(0xFFF5F6FA);

  /// 主灰色
  static Color get ypGreyColor => Color(0xFFF0F0F0);

  /// 主色调15%
  static Color get primaryColor15 => rgba(82, 144, 253, 0.15);

  /// 黑色85%
  static Color get black85 => rgba(0, 0, 0, 0.85);

  /// 黑色65%
  static Color get black65 => rgba(0, 0, 0, 0.65);

  /// 黑色45%
  static Color get black45 => rgba(0, 0, 0, 0.45);

  /// 黑色50%
  static Color get black50 => rgba(0, 0, 0, 0.5);

  /// 黑色38%
  static Color get black38 => rgba(0, 0, 0, 0.38);

  /// 黑色25%
  static Color get black25 => rgba(0, 0, 0, 0.25);

  /// 黑色5%
  static Color get black5 => rgba(0, 0, 0, 0.05);

  /// 黑色41
  static Color get black41 => rgba(41, 49, 64, 1);

  /// 白色45%
  static Color get white45 => rgba(255, 255, 255, 0.45);

  /// 白色65%
  static Color get white65 => rgba(255, 255, 255, 0.65);

  /// 白色30%
  static Color get white30 => rgba(255, 255, 255, 0.3);

  /// 灰色100%
  static Color get divideLineColor => rgba(238, 238, 238, 1);

  /// 灰色
  static Color get greyColor => rgba(96, 96, 102, 1);

  /// 输入框背景色100%
  static Color get inputBgColor => rgba(240, 240, 240, 1);

  /// 输入框hit字体色
  static Color get hintFontColor => rgba(138, 138, 153, 1);

  static Color get blueLight => rgb(229, 244, 255);

  static Color get yellowLight => Color(0xFFffeede);

  /// 根据rgb和透明度获取颜色
  static Color rgba(int r, int g, int b, double a) {
    return Color.fromRGBO(r, g, b, a);
  }

  /// 根据rgb获取颜色
  static Color rgb(int r, int g, int b) {
    return Color.fromRGBO(r, g, b, 1);
  }
}
