import 'dart:async';
import 'package:flutter/widgets.dart';

class DebounceUtil {
  final Duration delay;
  Timer? _timer;
  BuildContext? _currentContext;

  DebounceUtil({this.delay = const Duration(milliseconds: 500)});

  void run(BuildContext context, VoidCallback action) {
    _currentContext = context;
    _timer?.cancel();
    _timer = Timer(delay, () {
      if (_currentContext != null &&
          _currentContext?.mounted == true) {
        action();
      }
    });
  }

  void dispose() {
    _timer?.cancel();
    _currentContext = null;
  }
}