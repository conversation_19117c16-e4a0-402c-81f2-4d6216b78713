import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';

/// 月份选择器工具类
class MonthSelectUtil {
  /// 显示月份选择器
  /// [context] 上下文
  /// [initialYear] 初始年份，默认当前年份
  /// [initialMonth] 初始月份，默认当前月份
  /// [onSelected] 选择回调 (year, month)
  static void show({
    required BuildContext context,
    int? initialYear,
    int? initialMonth,
    required Function(int year, int month) onSelected,
  }) {
    final now = DateTime.now();

    YPRoute.openDialog(      builder: (context) => MonthSelectWidget(
      initialYear: initialYear ?? now.year,
      initialMonth: initialMonth ?? now.month,
      hasInitialValue: initialYear != null && initialMonth != null,
      onSelected: onSelected,
    ),
      alignment: Alignment.bottomCenter,
      maskColor: Colors.black.withValues(alpha: 0.5));
  }
}

/// 月份选择器组件
class MonthSelectWidget extends StatefulWidget {
  final int initialYear;
  final int initialMonth;
  final bool hasInitialValue;
  final Function(int year, int month) onSelected;

  const MonthSelectWidget({
    super.key,
    required this.initialYear,
    required this.initialMonth,
    required this.hasInitialValue,
    required this.onSelected,
  });

  @override
  State<MonthSelectWidget> createState() => _MonthSelectWidgetState();
}

class _MonthSelectWidgetState extends State<MonthSelectWidget> {
  late int currentYear;
  late int selectedMonth;
  bool hasUserSelected = false;
  final DateTime now = DateTime.now();

  @override
  void initState() {
    super.initState();
    currentYear = widget.initialYear;
    selectedMonth = widget.initialMonth;

    // 如果传递了明确的初始值，则预选中
    hasUserSelected = widget.hasInitialValue;
  }

  /// 获取当前年份可显示的月份数量
  int get maxMonth {
    return currentYear == now.year ? now.month : 12;
  }

  /// 切换到上一年
  void _previousYear() {
    // 限制不能小于当前年份减5年
    final minYear = now.year - 5;
    if (currentYear <= minYear) {
      return;
    }

    setState(() {
      currentYear--;
      // 如果切换到当前年份，且选中月份超过当前月份，则重置为当前月份
      if (currentYear == now.year && selectedMonth > now.month) {
        selectedMonth = now.month;
      }
    });
  }

  /// 切换到下一年
  void _nextYear() {
    // 限制不能超过当前年份
    if (currentYear >= now.year) {
      return;
    }

    setState(() {
      currentYear++;
      // 如果切换到当前年份，且选中月份超过当前月份，则重置为当前月份
      if (currentYear == now.year && selectedMonth > now.month) {
        selectedMonth = now.month;
      }
    });
  }

  /// 选择月份
  void _selectMonth(int month) {
    setState(() {
      selectedMonth = month;
      hasUserSelected = true;
    });
    widget.onSelected(currentYear, month);
    SmartDialog.dismiss();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(8.r)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 头部
          _buildHeader(),

          // 分割线
          Divider(height: 0.5.h, color: const Color(0xFFE6E6E6)),

          // 年份选择
          _buildYearSelector(),

          // 月份网格
          _buildMonthGrid(),


          // 底部安全距离
          SizedBox(height: MediaQuery.of(context).padding.bottom),
        ],
      ),
    );
  }

  /// 构建头部
  Widget _buildHeader() {
    return Container(
      height: 44.h,
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      child: Row(
        children: [
          GestureDetector(
            onTap: () => SmartDialog.dismiss(),
            child: Image.asset(
              'assets/images/common/icon_close.webp',
              width: 20.w,
              height: 20.h,
            ),
          ),
          Expanded(
            child: Center(
              child: Text(
                '请选择月份',
                style: TextStyle(
                  fontSize: 16.sp,
                  height: 1.25,
                  fontWeight: FontWeight.w600,
                  color: const Color(0xFF323233),
                ),
              ),
            ),
          ),
          SizedBox(width: 24.w), // 占位，保持标题居中
        ],
      ),
    );
  }

  /// 构建年份选择器
  Widget _buildYearSelector() {
    final minYear = now.year - 5;

    return Container(
      height: 60.h,
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      child: Row(
        children: [
          // 左箭头和文字 - 当到达最小年份时不显示
          if (currentYear > minYear)
            GestureDetector(
              onTap: _previousYear,
              child: Row(
                children: [
                  Image.asset(
                    'assets/images/common/icon_month_left.webp',
                    width: 18.w,
                    height: 18.h,
                  ),
                  Text(
                    '上一年',
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: ColorsUtil.primaryColor,
                    ),
                  ),
                ],
              ),
            )
          else
            Container(width: 56.w), // 占位容器，保持布局对称

          const Expanded(
            child: Center(
              child: Text(
                '',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.w600,
                  color: Color(0xFF323233),
                ),
              ),
            ),
          ),
          Text(
            '$currentYear年',
            style: TextStyle(
              fontSize: 14.sp,
              height: 1.3,
              fontWeight: FontWeight.w600,
              color: const Color(0xFF323233),
            ),
          ),
          const Expanded(
            child: Center(
              child: Text(
                '',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.w600,
                  color: Color(0xFF323233),
                ),
              ),
            ),
          ),

          // 右箭头和文字 - 当到达当前年份时不显示
          if (currentYear < now.year)
            GestureDetector(
              onTap: _nextYear,
              child: Row(
                children: [
                  Text(
                    '下一年',
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: ColorsUtil.primaryColor,
                    ),
                  ),
                  Image.asset(
                    'assets/images/common/icon_month_right.webp',
                    width: 18.w,
                    height: 18.h,
                  ),
                ],
              ),
            )
          else
            Container(width: 56.w), // 占位容器，保持布局对称
        ],
      ),
    );
  }

  /// 构建月份网格
  Widget _buildMonthGrid() {
    return Container(
      padding: EdgeInsets.only(top:0,bottom: 16.h,left: 18.w,right: 17.w),
      child: GridView.builder(
        shrinkWrap: true,
        padding: EdgeInsets.zero, // 强制去除内边距
        physics: const NeverScrollableScrollPhysics(),
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 4,
          childAspectRatio: 2.1,
          crossAxisSpacing: 12.w,
          mainAxisSpacing: 12.h,
        ),
        itemCount: maxMonth,
        itemBuilder: (context, index) {
          final month = index + 1;
          final isSelected = hasUserSelected &&
                           month == selectedMonth &&
                           currentYear == widget.initialYear;

          return GestureDetector(
            onTap: () => _selectMonth(month),
            child: Container(
              decoration: BoxDecoration(
                color: isSelected
                    ?  ColorsUtil.primaryColor
                    : const Color(0xFFF0F0F0),
                borderRadius: BorderRadius.circular(4.r),
              ),
              child: Center(
                child: Text(
                  '$month月',
                  style: TextStyle(
                    height: 1.25,
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w400,
                    color: isSelected ? Colors.white : const Color(0xFF323233),
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
