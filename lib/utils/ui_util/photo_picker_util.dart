import 'dart:io';
import 'dart:typed_data';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/common_dialog.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/toast_util.dart';
import 'package:net_plugin/pigeon/file_upload/input_param/upload_input_param.dart';
import 'package:net_plugin/pigeon/file_upload/input_param/upload_req_param.dart';
import 'package:net_plugin/pigeon/file_upload/output_result/upload_result.dart';
import 'package:net_plugin/pigeon/net_service/net_core.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:wechat_assets_picker/wechat_assets_picker.dart';

class PhotoPickerUtil {
  /// 检查并请求权限
  static Future<bool> _checkPermission(
      BuildContext context, String sourceType) async {
    try {
      // Android 平台权限检查
      if (Platform.isAndroid) {
        bool isAndroid13 = false;

        // 尝试获取 Android 版本信息
        try {
          final deviceInfo = DeviceInfoPlugin();
          final androidInfo = await deviceInfo.androidInfo;
          isAndroid13 = androidInfo.version.sdkInt >= 33;
        } catch (e) {
          debugPrint('获取Android版本信息失败: $e');
          // 失败时默认使用旧版权限
          isAndroid13 = false;
        }

        print('isAndroid13: $isAndroid13');

        // 根据sourceType检查相应权限
        if (sourceType == 'gallery') {
          // Android 13 及以上版本
          if (isAndroid13) {
            try {
              final status = await Permission.photos.request();
              if (!status.isGranted) {
                _showPermissionDeniedDialog(context, '相册');
                return false;
              }
            } catch (e) {
              debugPrint('请求photos权限失败: $e');
              return false;
            }
          }
          // Android 13 以下版本
          else {
            try {
              final status = await Permission.storage.status;
              if (!status.isGranted) {
                _showPermissionDeniedDialog(context, '存储');
                return false;
              }
            } catch (e) {
              debugPrint('请求storage权限失败: $e');
              return false;
            }
          }
        }

        // 如果是相机模式或需要相机权限
        if (sourceType == 'camera') {
          try {
            final cameraStatus = await Permission.camera.request();
            final storageStatus = await Permission.storage.request();

            if (!cameraStatus.isGranted) {
              _showPermissionDeniedDialog(context, '相机');
              return false;
            }
            if (!storageStatus.isGranted) {
              _showPermissionDeniedDialog(context, '存储');
              return false;
            }
          } catch (e) {
            debugPrint('请求相机权限失败: $e');
            return false;
          }
        }

        return true;
      }
      // iOS 平台权限检查
      else if (Platform.isIOS) {
        try {
          if (sourceType == 'gallery') {
            // 相册权限
            final photoStatus = await Permission.photos.request();
            if (!photoStatus.isGranted) {
              _showPermissionDeniedDialog(context, '相册');
              return false;
            }
          }

          if (sourceType == 'camera') {
            // 相机权限
            final cameraStatus = await Permission.camera.request();
            if (!cameraStatus.isGranted) {
              _showPermissionDeniedDialog(context, '相机');
              return false;
            }
          }

          return true;
        } catch (e) {
          debugPrint('iOS权限请求失败: $e');
          return false;
        }
      }

      return false;
    } catch (e) {
      debugPrint('权限检查过程出错: $e');
      ToastUtil.showToast('权限检查失败，请稍后重试');
      return false;
    }
  }

  static handlePhotoSelection(
    BuildContext context,
    Function(String) onImageSelected, {
    int maxAssets = 9, // 默认最多选择9张图片
  }) async {
    try {
      // 1. 检查权限 - 传入sourceType参数
      bool hasPermission = await _checkPermission(context, 'gallery');
      if (!hasPermission) return;
      SmartDialog.dismiss();

      // 2. 根据sourceType选择图片来源
      File? file;
      // 2.2 从相册选择照片
      final List<AssetEntity>? result = await AssetPicker.pickAssets(
        context,
        pickerConfig: AssetPickerConfig(
          maxAssets: maxAssets, // 图片数量限制
          requestType: RequestType.common, // 只请求图片类型
          themeColor: const Color(0xFF3986eb), // 主题颜色
          textDelegate: const AssetPickerTextDelegate(),
        ),
      );

      // 如果用户取消选择，直接返回
      if (result == null || result.isEmpty) {
        debugPrint('未选择图片');
        return;
      }

      for (final AssetEntity asset in result) {
        final File? file = await asset.file;
        if (file != null) {
          onImageSelected(file.path);
          // 上传图片
          await _uploadFile(file.path);
        }
      }
    } catch (e) {
      debugPrint('handleImageSelection中出错: $e');
    }
  }

  /// 上传图片到服务器
  ///
  /// [filePath] - 要上传的图片本地路径
  static _uploadFile(String filePath) async {
    // 调用网络服务上传图片
    UploadResult result = await NetCore.requestUpload(UploadInputParam());

    // 检查上传结果
    if (result.isSuccess == true) {
      ToastUtil.showToast('图片上传成功');
      debugPrint('上传成功');
      // 将上传成功的图片URL添加到列表中
      // _uploadedImageUrls.add(result.relativeUrl!);
      // 通知父组件图片URL已更新
      // widget.onImagesUploaded(_uploadedImageUrls);
    } else {
      debugPrint('上传失败');
      // 上传失败时从选择列表中移除该图片
      // _selectedImages.removeWhere((path) => path == filePath);
      // 显示上传失败提示
      ToastUtil.showToast('图片上传失败，请重试');
    }
  }

  static void _showPermissionDeniedDialog(
      BuildContext context, String permissionName) {
    showCommonDialog(CommonDialogConfig(
      title: '存储空间权限使用说明',
      content: '用于文件存储、拍摄的照片保存至手机相册、选择本地照片进编辑。若您拒绝，将影响上述功能的使用，但不影响基础功能的使用',
      positive: '去开启',
      onPositive: () {
        SmartDialog.dismiss(tag: 'commonDialog');
        openAppSettings();
      },
    ));
  }

  /// 保存图片到相册
  /// [imageBytes] 图片字节数据
  /// [fileName] 文件名（可选，默认使用时间戳）
  /// 返回保存是否成功
  static Future<bool> saveImageToGallery(
    Uint8List imageBytes, {
    String? fileName,
  }) async {
    try {
      // 生成文件名
      final name =
          fileName ?? '图片_${DateTime.now().millisecondsSinceEpoch}.png';
      // 获取临时目录
      final tempDir = await getTemporaryDirectory();
      final file = File('${tempDir.path}/$name');
      // 写入文件
      await file.writeAsBytes(imageBytes);
      // 使用wechat_assets_picker保存到相册
      final AssetEntity? result = await PhotoManager.editor.saveImageWithPath(
        file.path,
        title: name,
      );
      // 清理临时文件
      try {
        await file.delete();
      } catch (e) {
        debugPrint('清理临时文件失败: $e');
      }
      return result != null;
    } catch (e) {
      debugPrint('保存图片到相册失败: $e');
      return false;
    }
  }

  /// 检查存储权限（用于保存图片）
  /// 返回是否有权限
  static Future<bool> checkStoragePermission() async {
    try {
      if (Platform.isAndroid) {
        // 尝试使用photos权限（Android 13+）
        var status = await Permission.photos.request();
        if (status.isGranted) {
          return true;
        }

        // 如果photos权限不可用，尝试storage权限
        status = await Permission.storage.request();
        return status.isGranted;
      } else if (Platform.isIOS) {
        final status = await Permission.photos.request();
        return status.isGranted;
      }
      return false;
    } catch (e) {
      debugPrint('权限请求失败: $e');
      return false;
    }
  }
}
