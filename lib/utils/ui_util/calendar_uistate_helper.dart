
import 'package:gdjg_pure_flutter/data/common_data/biz/calendar_biz_model.dart';
import 'package:gdjg_pure_flutter/model/RecordType.dart';
import 'package:gdjg_pure_flutter/utils/system_util/string_util.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';
import 'package:gdjg_pure_flutter/widget/calendar/dayevent/DayEvent.dart';

///
/// 班组和个人大日历UIState转换帮助类
class CalendarUIStateHelper {
  static convertEntityToCalendarUIState(List<CalendarEntity> calendar) {
    Map<DateTime, List<DayEvent>> events = {};
    for (var element in calendar) {
      if (element.businessType.isNotEmpty) {
        var day = element.day;
        var date = DateTime.parse(day);
        List<DayEvent> otherList = [];
        List<DayEvent> list = [];

        /// 4借支
        if (element.businessType.contains(RecordType.debt.value)) {
          otherList.add(DayEvent(type: DayEventType.Borrowing));
        }

        ///9 工资 （结算和未结是这个类型）
        if (element.businessType.contains(RecordType.wageLast.value)) {
          otherList.add(DayEvent(type: DayEventType.Settlement));
        }

        ///备注
        if (element.isNote > 0) {
          otherList.add(DayEvent(type: DayEventType.Remark));
        }

        ///点工
        var sportWork = element.spotWork;
        if (sportWork != null) {
          if (sportWork.workTime.isNotEmpty && sportWork.workTime != '0') {
            list.add(DayEvent(
                type: DayEventType.DailyPaid,
                uiState: DayEventUIState(
                  value: "${sportWork.workTime}工",
                )));
          }
          if (sportWork.workTimeHour.isNotEmpty &&
              sportWork.workTimeHour != '0') {
            list.add(DayEvent(
                type: DayEventType.DailyPaid,
                uiState: DayEventUIState(
                  value: "${sportWork.workTimeHour}小时",
                )));
          }
          if (sportWork.overTimeWork.isNotEmpty &&
              sportWork.overTimeWork != '0') {
            list.add(DayEvent(
                type: DayEventType.DailyPaid,
                uiState: DayEventUIState(
                  value: "${sportWork.overTimeWork}工",
                  color: ColorsUtil.yellowMedium,
                )));
          }
          if (sportWork.overTime.isNotEmpty && sportWork.overTime != '0') {
            list.add(DayEvent(
                type: DayEventType.DailyPaid,
                uiState: DayEventUIState(
                  value: "${sportWork.overTime}小时",
                  color: ColorsUtil.yellowMedium,
                )));
          }
        }
        if (list.length >= 4) {
          events.putIfAbsent(date, () => list..addAll(otherList));
          continue;
        }

        ///包工
        var contractor = element.contractor;
        if (contractor != null) {
          if (contractor.contractorWorkTime.isNotEmpty &&
              contractor.contractorWorkTime != '0') {
            list.add(DayEvent(
                type: DayEventType.LumpSum,
                uiState: DayEventUIState(
                  value: "包${contractor.contractorWorkTime}工",
                )));
          }
          if (list.length >= 4) {
            events.putIfAbsent(date, () => list..addAll(otherList));
            continue;
          }
          if (contractor.contractorWorkTimeHour.isNotEmpty &&
              contractor.contractorWorkTimeHour != '0') {
            list.add(DayEvent(
                type: DayEventType.LumpSum,
                uiState: DayEventUIState(
                  value: "包${contractor.contractorWorkTimeHour}小时",
                )));
          }
          if (list.length >= 4) {
            events.putIfAbsent(date, () => list..addAll(otherList));
            continue;
          }
          if (contractor.contractorOverTime.isNotEmpty &&
              contractor.contractorOverTime != '0') {
            list.add(DayEvent(
                type: DayEventType.LumpSum,
                uiState: DayEventUIState(
                  value: "包${contractor.contractorOverTime}小时",
                  color: ColorsUtil.yellowMedium,
                )));
          }
        }
        if (list.length >= 4) {
          events.putIfAbsent(date, () => list..addAll(otherList));
          continue;
        }

        ///工量
        var unit = element.unit;
        if (unit != null && unit.num > 0) {
          list.add(DayEvent(
              type: DayEventType.Workload,
              uiState: DayEventUIState(
                value: "量${unit.num.trimTrailingZeros()}笔",
              )));
        }
        if (list.length >= 4) {
          events.putIfAbsent(date, () => list..addAll(otherList));
          continue;
        }

        ///短工
        var workMoney = element.workMoney;
        if (workMoney != null && workMoney.num > 0) {
          list.add(DayEvent(
              type: DayEventType.Temporary,
              uiState: DayEventUIState(
                value: "短${workMoney.num}",
              )));
        }
        if (list.length >= 4) {
          events.putIfAbsent(date, () => list..addAll(otherList));
          continue;
        }

        ///其他费用
        var otherExpensesNum = element.otherExpensesNum;
        if (otherExpensesNum > 0) {
          list.add(DayEvent(
              type: DayEventType.Expense,
              uiState: DayEventUIState(
                value: "费$otherExpensesNum笔",
              )));
        }
        if (list.length >= 4) {
          events.putIfAbsent(date, () => list..addAll(otherList));
          continue;
        }
        if (list.isEmpty) {
          if (sportWork != null || contractor != null) {
            list.add(DayEvent(type: DayEventType.Rest));
          }
        }
        events.putIfAbsent(date, () => list..addAll(otherList));
      }
    }

    return events;
  }
}
