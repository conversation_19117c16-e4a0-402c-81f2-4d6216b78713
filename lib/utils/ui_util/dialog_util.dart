import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/photo_picker_util.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/toast_util.dart';

import '../../generated/assets.dart';
import 'colors_util.dart';

class DialogUtil {
  /// 显示底部弹窗
  static void showUploadImageBottomSheet(BuildContext context, {required Function(String) onTap}) {
    YPRoute.openDialog(
        alignment: Alignment.bottomCenter,
        builder: (_) {
          return BottomSheetContainer(context: context,onTap: onTap,);
        });
  }
}

class BottomSheetContainer extends StatelessWidget {
  final BuildContext context;
  final Function(String) onTap;
  BottomSheetContainer({super.key,required this.context,required this.onTap});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      constraints: const BoxConstraints(
        maxHeight: 180,
      ),
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
      ),
      // height: MediaQuery.of(context).viewPadding.bottom + 24,
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
            width: double.infinity,
            child: Stack(
              children: [
                Positioned(
                  left: 0,
                  child: GestureDetector(
                    onTap: () {
                      SmartDialog.dismiss();
                    },
                    child: Image(
                      image: AssetImage(Assets.commonIconClose),
                      width: 20,
                      height: 20,
                    ),
                  ),
                ),
                Center(
                    child: Text(
                  '请选择上传方式',
                  style: TextStyle(
                      fontSize: 17.sp,
                      color: ColorsUtil.black85,
                      fontWeight: FontWeight.w500),
                ))
              ],
            ),
          ),
          Container(
            height: 1,
            color: ColorsUtil.divideLineColor,
          ),
          SizedBox(
            height: 16,
          ),
          Row(mainAxisAlignment: MainAxisAlignment.spaceEvenly, children: [
            GestureDetector(
              onTap: (){
                ToastUtil.showToast('跳转"水印相机"');
                SmartDialog.dismiss();
              },
              child: Column(
                children: [
                  Image(
                    image: AssetImage(Assets.commonWaaIconSelectImgTyleShoot),
                    width: 64,
                    height: 64,
                  ),
                  SizedBox(
                    height: 16,
                  ),
                  Text(
                    '拍摄',
                    style: TextStyle(
                        fontSize: 15.sp,
                        color: ColorsUtil.black41,),
                  ),
                ],
              ),
            ),
            GestureDetector(
              onTap: (){
                PhotoPickerUtil.handlePhotoSelection(this.context, (file) {
                  onTap(file);
                });
              },
              child: Column(
                children: [
                  Image(
                    image: AssetImage(Assets.commonWaaIconSelectImgTyleAlbum),
                    width: 64,
                    height: 64,
                  ),
                  SizedBox(
                    height: 16,
                  ),
                  Text(
                    '从照片选择',
                    style: TextStyle(
                        fontSize: 15.sp,
                        color: ColorsUtil.black41,),
                  ),
                ],
              ),
            ),
            GestureDetector(
              onTap: (){
                ToastUtil.showToast('跳转"拍摄视频"');
                SmartDialog.dismiss();
              },
              child: Column(
                children: [
                  Image(
                    image: AssetImage(Assets.commonWaaIconSelectImgTyleVideo),
                    width: 64,
                    height: 64,
                  ),
                  SizedBox(
                    height: 16,
                  ),
                  Text(
                    '拍摄视频',
                    style: TextStyle(
                        fontSize: 15.sp,
                        color: ColorsUtil.black41,),
                  ),
                ],
              ),
            ),
          ])
        ],
      ),
    );
  }
}

class ButtonDialog extends StatelessWidget {
  final String title;
  final String content;
  final String? leftButtonText; // 可选的左按钮文本
  final String rightButtonText; // 右按钮文本
  final Function? leftButtonTap; // 可选的左按钮点击事件
  final Function rightButtonTap; // 右按钮点击事件
  final bool showCloseButton; // 是否显示关闭按钮

  const ButtonDialog({
    super.key,
    required this.title,
    required this.content,
    this.leftButtonText, // 设为可选参数
    required this.rightButtonText,
    this.leftButtonTap, // 设为可选参数
    required this.rightButtonTap,
    this.showCloseButton = true, // 默认显示关闭按钮
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 315.w,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Stack(
        children: [
          Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                width: double.infinity,
                padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 24.h),
                decoration: const BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Color(0xFFD8E9FF),
                      Color(0x00D8E9FF),
                    ],
                  ),
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(12),
                    topRight: Radius.circular(12),
                  ),
                ),
                child: Text(
                  title,
                  style: TextStyle(
                    fontSize: 20.sp,
                    fontWeight: FontWeight.w500,
                    color: ColorsUtil.black85,
                  ),
                ),
              ),
              Padding(
                padding: EdgeInsets.only(left: 24.w, right: 24.w, bottom: 24.h),
                child: Text(
                  content,
                  style: TextStyle(
                    fontSize: 16.sp,
                    color: ColorsUtil.black65,
                  ),
                ),
              ),
              Padding(
                padding: EdgeInsets.fromLTRB(24.w, 0, 24.w, 24.h),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    if (leftButtonText != null) ...[
                      Flexible(
                        child: GestureDetector(
                          onTap: () {
                            SmartDialog.dismiss().then((value) {
                              leftButtonTap?.call();
                            });
                          },
                          child: Container(
                            margin: EdgeInsets.only(right: 15.w),
                            padding: EdgeInsets.symmetric(
                                horizontal: 36.w, vertical: 8.h),
                            decoration: BoxDecoration(
                              color: Colors.white,
                              border: Border.all(
                                  color: const Color(0xFF3B85EB), width: 1.w),
                              borderRadius: BorderRadius.circular(46.r),
                            ),
                            child: Text(
                              leftButtonText!,
                              style: TextStyle(
                                fontSize: 15.sp,
                                fontWeight: FontWeight.w500,
                                color: const Color(0xFF3B85EB),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                    Flexible(
                      child: GestureDetector(
                        onTap: () {
                          SmartDialog.dismiss().then((value) {
                            rightButtonTap();
                          });
                        },
                        child: Container(
                          padding: EdgeInsets.symmetric(
                              horizontal: 36.w, vertical: 8.h),
                          decoration: BoxDecoration(
                            color: ColorsUtil.primaryColor,
                            borderRadius: BorderRadius.circular(46.r),
                          ),
                          child: Text(
                            rightButtonText,
                            style: TextStyle(
                              fontSize: 16.sp,
                              fontWeight: FontWeight.w500,
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          if (showCloseButton)
            Positioned(
              top: 8,
              right: 8,
              child: GestureDetector(
                onTap: () => SmartDialog.dismiss(),
                child: Image.asset(
                  "assets/images/common/dialog_close.png",
                  width: 24.w,
                  height: 24.h,
                ),
              ),
            ),
        ],
      ),
    );
  }
}
