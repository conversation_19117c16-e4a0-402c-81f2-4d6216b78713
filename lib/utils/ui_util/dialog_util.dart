import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/toast_util.dart';

import '../../generated/assets.dart';
import 'colors_util.dart';

class DialogUtil {
  /// 显示底部弹窗
  static void showUploadImageBottomSheet() {
    YPRoute.openDialog(
        alignment: Alignment.bottomCenter,
        builder: (_) {
          return BottomSheetContainer();
        });
  }
}

class BottomSheetContainer extends StatelessWidget {
  BottomSheetContainer({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      constraints: const BoxConstraints(
        maxHeight: 245,
      ),
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
      ),
      // height: MediaQuery.of(context).viewPadding.bottom + 24,
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
            width: double.infinity,
            child: Stack(
              children: [
                Positioned(
                  left: 0,
                  child: GestureDetector(
                    onTap: () {
                      SmartDialog.dismiss();
                    },
                    child: Image(
                      image: AssetImage(Assets.commonIconClose),
                      width: 20,
                      height: 20,
                    ),
                  ),
                ),
                Center(
                    child: Text(
                  '请选择上传方式',
                  style: TextStyle(
                      fontSize: 17.sp,
                      color: ColorsUtil.black85,
                      fontWeight: FontWeight.w500),
                ))
              ],
            ),
          ),
          Container(
            height: 1,
            color: ColorsUtil.divideLineColor,
          ),
          SizedBox(
            height: 30,
          ),
          Row(mainAxisAlignment: MainAxisAlignment.spaceEvenly, children: [
            GestureDetector(
              onTap: (){
                ToastUtil.showToast('跳转"水印相机"');
                SmartDialog.dismiss();
              },
              child: Column(
                children: [
                  Image(
                    image: AssetImage(Assets.commonImageTakePhoto),
                    width: 72,
                    height: 72,
                  ),
                  SizedBox(
                    height: 16,
                  ),
                  Text(
                    '拍摄',
                    style: TextStyle(
                        fontSize: 15.sp,
                        color: ColorsUtil.black41,),
                  ),
                ],
              ),
            ),
            GestureDetector(
              onTap: (){
                ToastUtil.showToast('跳转"从照片选择"');
                SmartDialog.dismiss();
              },
              child: Column(
                children: [
                  Image(
                    image: AssetImage(Assets.commonImageTakePhoto),
                    width: 72,
                    height: 72,
                  ),
                  SizedBox(
                    height: 16,
                  ),
                  Text(
                    '从照片选择',
                    style: TextStyle(
                        fontSize: 15.sp,
                        color: ColorsUtil.black41,),
                  ),
                ],
              ),
            ),
            GestureDetector(
              onTap: (){
                ToastUtil.showToast('跳转"拍摄视频"');
                SmartDialog.dismiss();
              },
              child: Column(
                children: [
                  Image(
                    image: AssetImage(Assets.commonImageTakePhoto),
                    width: 72,
                    height: 72,
                  ),
                  SizedBox(
                    height: 16,
                  ),
                  Text(
                    '拍摄视频',
                    style: TextStyle(
                        fontSize: 15.sp,
                        color: ColorsUtil.black41,),
                  ),
                ],
              ),
            ),
          ])
        ],
      ),
    );
  }
}
