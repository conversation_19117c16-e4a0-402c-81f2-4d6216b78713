import 'package:flutter/material.dart';
import 'package:gdjg_pure_flutter/generated/assets.dart';
import 'package:gdjg_pure_flutter/iconfont/iconfont.dart';
import 'package:gdjg_pure_flutter/utils/system_util/yprint.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';
import 'package:get/get.dart';

class AppBarUtil {
  // 通用样式 AppBar
  static AppBar buildCommonAppBar({
    required String title,
    VoidCallback? onBackTap,
    Color backgroundColor = Colors.white,
    Color iconColor = Colors.black,
  }) {
    return AppBar(
      backgroundColor: backgroundColor,
      leading: GestureDetector(
          onTap: onBackTap ?? () => Get.back(),
          child: Container(
            padding: const EdgeInsets.only(left: 8.0),
            child: Image(
              image: AssetImage(Assets.commonIconArrowBack),
            ),
          )),
      leadingWidth: 38,
      titleSpacing: 08,
      title: Text(title,
          style: const TextStyle(
              color: Colors.black, fontSize: 22, fontWeight: FontWeight.w600)),
      centerTitle: false,
    );
  }

  // 含资源位的 AppBar（右侧带一个可定制的 Widget）
  static AppBar buildWithResourceWidget({
    required String title,
    VoidCallback? onBackTap,
    Color backgroundColor = Colors.white,
    String? resourceText,
    String? resourceIcon,
    Color? resourceBgColor,
    Color? resourceTextColor,
    IconFont? resourceIconFont,
    VoidCallback? onResourceTap,
    Widget? customWidget,
  }) {
    yprint('自定义的右侧视图是否为空“？   ${customWidget == null}');
    return AppBar(
      backgroundColor: backgroundColor,
      leading: GestureDetector(
          onTap: onBackTap ?? () => Get.back(),
          child: Container(
            padding: const EdgeInsets.only(left: 8.0),
            child: Image(
              image: AssetImage(Assets.commonIconArrowBack),
            ),
          )),
      leadingWidth: 38,
      titleSpacing: 08,
      title: Text(title,
          style: const TextStyle(
              color: Colors.black, fontSize: 22, fontWeight: FontWeight.w600)),
      centerTitle: false,
      actions: [
        GestureDetector(
          onTap: onResourceTap,
          child: customWidget ??
              Container(
                margin: const EdgeInsets.only(right: 16.0),
                padding:
                    const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
                decoration: BoxDecoration(
                  color: resourceBgColor ?? ColorsUtil.primaryColor15,
                  borderRadius: BorderRadius.circular(14.0),
                ),
                child: Row(
                  children: [
                    if (resourceIconFont != null) resourceIconFont,
                    if (resourceIcon != null)
                      Image.asset(resourceIcon, width: 18, height: 18),
                    if (resourceText != null)
                      Text(
                        resourceText,
                        style: TextStyle(
                            color: resourceTextColor ?? ColorsUtil.primaryColor,
                            fontSize: 14,
                            fontWeight: FontWeight.w500),
                      ),
                  ],
                ),
              ),
        ),
      ],
    );
  }
}
