import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

/// 封装了Pull_to_refresh的刷新和加载更多功能
class RefreshLoadMoreVvm<T> {
  final RefreshController refreshController;
  final RxList<T> _innerList = <T>[].obs;
  final Future<List<T>> Function(int page, int pageSize) dataFetcher;
  final int pageSize;

  int _currentPage = 1;
  final _hasMore = true.obs;

  RefreshLoadMoreVvm({
    required this.refreshController,
    required this.dataFetcher,
    this.pageSize = 10,
  });

  List<T> get list => _innerList;

  bool get hasMore => _hasMore.value;

  /// 刷新数据
  Future<void> refresh() async {
    try {
      _currentPage = 1;
      final newData = await dataFetcher(_currentPage, pageSize);
      _innerList.assignAll(newData);
      _hasMore.value = newData.length >= pageSize;
      refreshController.refreshCompleted();
      if (!hasMore) {
        refreshController.loadNoData();
      }
    } catch (e) {
      refreshController.refreshFailed();
      rethrow;
    }
  }

  /// 加载更多数据
  Future<void> loadMore() async {
    if (!hasMore) {
      refreshController.loadNoData();
      return;
    }

    try {
      _currentPage++;
      final newData = await dataFetcher(_currentPage, pageSize);
      _innerList.addAll(newData);
      _hasMore.value = newData.length >= pageSize;
      refreshController.loadComplete();
      if (!hasMore) {
        refreshController.loadNoData();
      }
    } catch (e) {
      _currentPage--; // 回滚页码
      refreshController.loadFailed();
      rethrow;
    }
  }

  /// 重置状态
  void reset() {
    _currentPage = 1;
    _hasMore.value = true;
    _innerList.clear();
    refreshController.resetNoData();
  }

  /// 绑定到SmartRefresher的便捷方法
  void bindToRefresher() {
    refreshController.requestRefresh();
  }

  void dispose() {
    refreshController.dispose();
  }
}
