/// 日期时间工具类
/// 提供通用的日期格式化功能
class DateUtil {
  /// 格式化相对时间显示
  /// 今天显示"今天"，昨天显示"昨天"，前天显示"前天"，其他显示自定义格式
  /// 
  /// [dateStr] 日期字符串，支持 ISO 8601 格式
  /// [showYear] 是否显示年份，默认 false
  /// [showTime] 是否显示时间（时分），默认 false
  /// [showSeconds] 是否显示秒，需要 showTime=true 才生效，默认 false
  /// 
  /// 返回格式化后的时间字符串
  /// 
  /// 示例：
  /// ```dart
  /// DateUtil.formatRelativeDate('2024-01-15') // 返回：01月15日
  /// DateUtil.formatRelativeDate('2024-01-15', showYear: true) // 返回：2024年01月15日
  /// DateUtil.formatRelativeDate('2024-01-15T14:30:00', showTime: true) // 返回：01月15日 14:30
  /// ```
  static String formatRelativeDate(
    String? dateStr, {
    bool showYear = false,
    bool showTime = false,
    bool showSeconds = false,
  }) {
    if (dateStr == null || dateStr.isEmpty) return '';

    try {
      final inputDate = DateTime.parse(dateStr);
      final now = DateTime.now();
      final today = DateTime(now.year, now.month, now.day);
      final targetDate = DateTime(inputDate.year, inputDate.month, inputDate.day);

      final difference = today.difference(targetDate).inDays;

      // 处理相对时间显示（今天、昨天、前天）
      switch (difference) {
        case 0:
          return '今天';
        case 1:
          return '昨天';
        case 2:
          return '前天';
        default:
          return _formatCustomDate(inputDate, showYear, showTime, showSeconds);
      }
    } catch (e) {
      return dateStr; // 解析失败时返回原字符串
    }
  }

  /// 格式化自定义日期显示
  /// 根据参数控制显示年份、时间、秒
  static String _formatCustomDate(
    DateTime date,
    bool showYear,
    bool showTime,
    bool showSeconds,
  ) {
    final StringBuffer buffer = StringBuffer();

    // 添加年份
    if (showYear) {
      buffer.write('${date.year}年');
    }

    // 添加月日
    buffer.write('${date.month.toString().padLeft(2, '0')}月');
    buffer.write('${date.day.toString().padLeft(2, '0')}日');

    // 添加时间
    if (showTime) {
      buffer.write(' ${date.hour.toString().padLeft(2, '0')}:');
      buffer.write(date.minute.toString().padLeft(2, '0'));

      // 添加秒
      if (showSeconds) {
        buffer.write(':${date.second.toString().padLeft(2, '0')}');
      }
    }

    return buffer.toString();
  }

  /// 判断两个日期是否为同一天
  /// [date1] 第一个日期
  /// [date2] 第二个日期
  /// 返回 true 如果是同一天，否则返回 false
  static bool isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
        date1.month == date2.month &&
        date1.day == date2.day;
  }

  /// 获取两个日期之间的天数差
  /// [startDate] 开始日期
  /// [endDate] 结束日期
  /// 返回天数差（正数表示 endDate 在 startDate 之后）
  static int daysBetween(DateTime startDate, DateTime endDate) {
    final start = DateTime(startDate.year, startDate.month, startDate.day);
    final end = DateTime(endDate.year, endDate.month, endDate.day);
    return end.difference(start).inDays;
  }
}
