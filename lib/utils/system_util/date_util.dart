/// 日期时间工具类
/// 提供通用的日期格式化功能
class DateUtil {
  /// 格式化相对时间显示
  /// 今天显示"今天"，昨天显示"昨天"，前天显示"前天"，其他显示自定义格式
  ///
  /// [dateStr] 日期字符串，支持 ISO 8601 格式
  /// [showYear] 是否显示年份，默认 false
  /// [showTime] 是否显示时间（时分），默认 false
  /// [showSeconds] 是否显示秒，需要 showTime=true 才生效，默认 false
  ///
  /// 返回格式化后的时间字符串
  ///
  /// 示例：
  /// ```dart
  /// DateUtil.formatRelativeDate('2024-01-15') // 返回：01月15日
  /// DateUtil.formatRelativeDate('2024-01-15', showYear: true) // 返回：2024年01月15日
  /// DateUtil.formatRelativeDate('2024-01-15T14:30:00', showTime: true) // 返回：01月15日 14:30
  /// ```
  static String formatRelativeDate(
    String? dateStr, {
    bool showYear = false,
    bool showTime = false,
    bool showSeconds = false,
  }) {
    if (dateStr == null || dateStr.isEmpty) return '';

    try {
      final inputDate = DateTime.parse(dateStr);
      final now = DateTime.now();
      final today = DateTime(now.year, now.month, now.day);
      final targetDate =
          DateTime(inputDate.year, inputDate.month, inputDate.day);

      final difference = today.difference(targetDate).inDays;

      // 处理相对时间显示（今天、昨天、前天）
      switch (difference) {
        case 0:
          return '今天';
        case 1:
          return '昨天';
        case 2:
          return '前天';
        default:
          return _formatCustomDate(inputDate, showYear, showTime, showSeconds);
      }
    } catch (e) {
      return dateStr; // 解析失败时返回原字符串
    }
  }

  /// 格式化自定义日期显示
  /// 根据参数控制显示年份、时间、秒
  static String _formatCustomDate(
    DateTime date,
    bool showYear,
    bool showTime,
    bool showSeconds,
  ) {
    final StringBuffer buffer = StringBuffer();

    // 添加年份
    if (showYear) {
      buffer.write('${date.year}年');
    }

    // 添加月日
    buffer.write('${date.month.toString().padLeft(2, '0')}月');
    buffer.write('${date.day.toString().padLeft(2, '0')}日');

    // 添加时间
    if (showTime) {
      buffer.write(' ${date.hour.toString().padLeft(2, '0')}:');
      buffer.write(date.minute.toString().padLeft(2, '0'));

      // 添加秒
      if (showSeconds) {
        buffer.write(':${date.second.toString().padLeft(2, '0')}');
      }
    }

    return buffer.toString();
  }

  /// 判断两个日期是否为同一天
  /// [date1] 第一个日期
  /// [date2] 第二个日期
  /// 返回 true 如果是同一天，否则返回 false
  static bool isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
        date1.month == date2.month &&
        date1.day == date2.day;
  }

  /// 获取两个日期之间的天数差
  /// [startDate] 开始日期
  /// [endDate] 结束日期
  /// 返回天数差（正数表示 endDate 在 startDate 之后）
  static int daysBetween(DateTime startDate, DateTime endDate) {
    final start = DateTime(startDate.year, startDate.month, startDate.day);
    final end = DateTime(endDate.year, endDate.month, endDate.day);
    return end.difference(start).inDays;
  }

  static String formatStartDate(DateTime? date) {
    if (date == null) return "2020-01-01";
    return "${date.year}-${date.month.toString().padLeft(2, '0')}-01";
  }

  static String formatEndDate(DateTime? date) {
    DateTime now = DateTime.now();
    if (date == null) {
      return "${now.year}-${now.month.toString().padLeft(2, '0')}-${now.day.toString().padLeft(2, '0')}";
    }
    DateTime endOfMonth = DateTime(date.year, date.month + 1, 0); // 获取该月最后一天
    DateTime endDateValue = endOfMonth.isAfter(now) ? now : endOfMonth;
    return "${endDateValue.year}-${endDateValue.month.toString().padLeft(2, '0')}-${endDateValue.day.toString().padLeft(2, '0')}";
  }

  static String formatDate(DateTime? date) {
    if (date == null) {
      DateTime now = DateTime.now();
      return "${now.year}-${now.month.toString().padLeft(2, '0')}-${now.day.toString().padLeft(2, '0')}";
    }
    return "${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}";
  }

  /// 格式化中文日期显示
  /// 默认显示年月日格式，月份和日期补零
  ///
  /// [date] 要格式化的日期
  /// [showYear] 是否显示年份，默认 true
  ///
  /// 返回格式化后的中文日期字符串
  ///
  /// 示例：
  /// ```dart
  /// DateUtil.formatChineseDate(DateTime(2020, 11, 1)) // 返回：2020年11月01日
  /// DateUtil.formatChineseDate(DateTime(2020, 1, 5)) // 返回：2020年01月05日
  /// DateUtil.formatChineseDate(DateTime(2020, 11, 1), showYear: false) // 返回：11月01日
  /// ```
  static String formatChineseDate(DateTime? date, {bool showYear = true}) {
    if (date == null) return '';

    final StringBuffer buffer = StringBuffer();

    // 添加年份
    if (showYear) {
      buffer.write('${date.year}年');
    }

    // 添加月日
    buffer.write('${date.month.toString().padLeft(2, '0')}月');
    buffer.write('${date.day.toString().padLeft(2, '0')}日');

    return buffer.toString();
  }

  /// 时间戳转24小时制
  /// 示例：
  /// int timestamp = 1751878448;
  /// String timeStr = timestampToTime(timestamp);
  /// print(timeStr); // 输出：16:54
  ///
  static String timestampToTime(int timestamp) {
    // 1. 将时间戳转为 DateTime 对象
    final dateTime = DateTime.fromMillisecondsSinceEpoch(timestamp * 1000);

    // 2. 格式化时间（如 16:54）
    final hour = dateTime.hour.toString().padLeft(2, '0'); // 补零，如 9 → 09
    final minute = dateTime.minute.toString().padLeft(2, '0');

    return '$hour:$minute';
  }
}
