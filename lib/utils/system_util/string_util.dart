extension StringExtensions on String? {
  bool isNullOrEmpty() {
    return this == null || this == '';
  }
}

// 扩展函数：字符串安全转换
extension DoubleExtensions on double? {
  String formatDoubleToMoney() {
    if (this == null) return "0.00";
    try {
      return this?.toStringAsFixed(2) ?? "0.00";
    } catch (e) {
      return "0.00";
    }
  }
}

// 扩展函数：字符串安全转换
extension StringMoneyExtensions on String? {
  String formatStringToMoney() {
    if (this == null || this == "" || this == "-0") return "0.00";
    try {
      return double.parse(this!).toStringAsFixed(2);
    } catch (e) {
      return "0.00";
    }
  }
}

// 扩展函数：去除 double 值小数点后多余的零
extension DoubleTrimZeros on double {
  String trimTrailingZeros() {
    if (this == 0) return "0";

    String stringValue = toString();
    // 处理科学计数法表示的数字
    if (stringValue.contains('e')) {
      stringValue = toStringAsFixed(10);
    }

    // 去除多余的零
    return stringValue
        .replaceAll(RegExp(r'\.0+$'), '') // 去除末尾的 .000...
        .replaceAll(RegExp(r'(\.\d*?)0+$'), r'$1') // 去除小数点后多余的零
        .replaceAll(RegExp(r'\.$'), ''); // 如果最后只剩小数点，也去除
  }
}

// 扩展函数：去除字符串中数字的小数点后多余的零
extension StringTrimZeros on String? {
  String trimTrailingZeros() {
    if (this == null || this!.isEmpty) return "";

    // 尝试解析为 double
    final doubleValue = double.tryParse(this!);
    if (doubleValue != null) {
      return doubleValue.trimTrailingZeros();
    }

    // 处理纯数字字符串
    if (RegExp(r'^-?\d+\.?\d*$').hasMatch(this!)) {
      return this!
          .replaceAll(RegExp(r'\.0+$'), '')
          .replaceAll(RegExp(r'(\.\d*?)0+$'), r'$1')
          .replaceAll(RegExp(r'\.$'), '');
    }

    // 非数字字符串原样返回
    return this!;
  }
}

extension AhaExtension on String? {
  double get toDouble {
    try {
      return double.tryParse(this ?? "0") ?? 0;
    } catch (e) {
      return 0;
    }
  }
}

extension StringLastTwoChars on String? {
  String? get lastTwoChars {
    if (this == null || this!.length <= 2) {
      return this;
    }
    return this!.substring(this!.length - 2);
  }
}


