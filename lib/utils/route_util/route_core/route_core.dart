import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_smart_dialog/src/kit/typedef.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_core/yp_page_route.dart';
import 'package:gdjg_pure_flutter/utils/system_util/string_util.dart';

import '../interceptors/route_interceptor.dart';

class RouteCore {
  RouteCore._();

  //当前栈顶系统路由
  static PageRoute? _topRoute;

  //避免使用Context，全局持有Context
  static GlobalKey<NavigatorState> routeGlobalKey = GlobalKey<NavigatorState>();

  //路由导航器
  static final NavigatorState? _routeState = routeGlobalKey.currentState;

  //路由路径--页面 映射表
  static final Map<String, YPPageRoute> _routeMap = {};

  //兼容路由查找Set
  static final Set<String> _unFoundCompatRouteNameSet = {};

  //路由栈，通过List 模拟Stack
  static final List<String> _routeStack = [];

  //路由拦截器
  static final List<RouteInterceptor> _routeInterceptorList = [];

  //single task模式缓存，用于缓存当前页面的参数，用于在返回时传递参数
  static Object? _singleTaskModeCache;

  //配合控制single task返回值，有效的page show动作
  static bool _validPageShow = false;

  //目标pop 路由
  static String _targetPopRouteName = '';

  //pop缓存
  static Object? _popResult;

  //弹窗相关
  //弹窗栈
  static final List<String> _dialogStack = [];

  static void setTopRoute(PageRoute? route) {
    _topRoute = route;
  }

  static PageRoute? getTopRoute() {
    return _topRoute;
  }

  static void registerRoute(YPPageRoute ypPageRoute) {
    _routeMap[ypPageRoute.routeName] = ypPageRoute;
  }

  static void registerRoutes(List<YPPageRoute> ypPageRoutes) {
    final map = {for (var item in ypPageRoutes) (item).routeName: item};
    _routeMap.addAll(map);
  }

  static void registerInterceptor(RouteInterceptor routeInterceptor) {
    _routeInterceptorList.add(routeInterceptor);
  }

  static void addRoute2Stack(String? routeName) {
    if (!routeName.isNullOrEmpty()) {
      _routeStack.add(routeName!);
    }
  }

  static Object? getLaunchModeCache() {
    return _singleTaskModeCache;
  }

  static void clearLaunchModeCache() {
    _singleTaskModeCache = null;
  }

  static bool validPageShow() {
    if (_validPageShow) {
      Future.microtask(() {
        _validPageShow = false;
      });
    }
    return _validPageShow;
  }

  static Object? getPopResult() {
    return _popResult;
  }

  static void removeRouteFromStack(String? routeName) {
    if (!routeName.isNullOrEmpty()) {
      _routeStack.remove(routeName);
      if (_routeStack.lastOrNull == _targetPopRouteName) {
        _validPageShow = true;
      }
    }
  }

  static YPPageRoute? _findTargetRoute(String routeName) {
    YPPageRoute? ypPageRoute;
    if (_routeMap.containsKey(routeName)) {
      ypPageRoute = _routeMap[routeName]!;
    } else {
      //找不到路由，再到兼容表里查询
      List<MapEntry<String, YPPageRoute>> entries = _routeMap.entries.toList();
      //遍历兼容链表
      for (var entry in entries) {
        for (var name in entry.value.compatRouteNames) {
          if (name == routeName) {
            ypPageRoute = entry.value;
            //添加到map里
            _routeMap[routeName] = ypPageRoute;
            return ypPageRoute;
          }
        }
      }
    }
    return ypPageRoute;
  }

  static bool _tackleSingleTaskPage(YPPageRoute ypPageRoute, Object? params) {
    if (ypPageRoute.isSingleTask) {
      //查找目标页面是否在栈里
      if (_routeStack.contains(ypPageRoute.routeName)) {
        if (_routeStack.firstOrNull == ypPageRoute.routeName) {
          //在栈顶，不处理
          return false;
        } else {
          //非栈顶，清空栈直至目标页面
          if (params != null) {
            //有参数，则缓存参数
            _singleTaskModeCache = params;
          }
          _targetPopRouteName = ypPageRoute.routeName;
          _routeState?.popUntil(
              (route) => route.settings.name == ypPageRoute.routeName);
          return false;
        }
      }
    }
    //不是single task，可以直接push
    return true;
  }

  static Future<T?> _realOpenPageForNormal<T extends Object?>(
      YPPageRoute ypPageRoute, Object? params) async {
    return _routeState?.push(MaterialPageRoute(
        builder: ypPageRoute.widgetBuilder,
        settings:
            RouteSettings(name: ypPageRoute.routeName, arguments: params)));
  }

  static Future<T?>? _tackleOpenThenClose<T extends Object?>(
      YPPageRoute ypPageRoute, Object? params, int? closeNumBeforeOpen) {
    if (closeNumBeforeOpen != null &&
        closeNumBeforeOpen > 0 &&
        _routeStack.length >= closeNumBeforeOpen) {
      int targetIndex = _routeStack.length - closeNumBeforeOpen - 1;
      String closeTargetRouteName = '';
      if (targetIndex >= 0) {
        closeTargetRouteName = _routeStack[targetIndex];
      }
      return _routeState?.pushAndRemoveUntil(
          MaterialPageRoute(
              builder: ypPageRoute.widgetBuilder,
              settings: RouteSettings(
                  name: ypPageRoute.routeName, arguments: params)), (route) {
        bool isTargetRoute = closeTargetRouteName == route.settings.name;
        if (!isTargetRoute) {
          _routeStack.remove(route.settings.name);
        }
        return isTargetRoute;
      });
    }
    return null;
  }

  static Future<bool> tackleInterceptor({String? routeName, Object? params}) async{
    for (var interceptor in _routeInterceptorList) {
      if (interceptor.matchRoute().contains(routeName)) {
        return interceptor.intercept(routeName: routeName, params: params);
      }
    }
    return false;
  }

  static bool onlyOnePageExist() {
    return _routeStack.length == 1;
  }

  static Future<T?>? openPage<T extends Object?>(
      {String? routeName,
      Object? params,
      int? closeNumBeforeOpen,
      bool? clearStackAndPush}) async {
    if (routeName == null ||
        routeName.isEmpty ||
        _unFoundCompatRouteNameSet.contains(routeName)) {
      return null;
    }

    bool handled = await tackleInterceptor(routeName: routeName, params: params);
    if (!handled) {
      YPPageRoute? ypPageRoute = _findTargetRoute(routeName);
      if (ypPageRoute != null) {
        if (clearStackAndPush == true) {
          return _tackleOpenThenClose(ypPageRoute, params, _routeStack.length);
        } else {
          if (closeNumBeforeOpen != null && closeNumBeforeOpen > 0) {
            return _tackleOpenThenClose(ypPageRoute, params, closeNumBeforeOpen);
          } else {
            if (_tackleSingleTaskPage(ypPageRoute, params)) {
              return _realOpenPageForNormal(ypPageRoute, params);
            }
          }
        }
      }
    }
    _popResult = null;
    return null;
  }

  static void closePage(Object? result) {
    _validPageShow = true;
    _popResult = result;
    if (_routeStack.length == 1) {
      SystemNavigator.pop();
    } else {
      _routeState?.pop(result);
    }
  }

  static Future<dynamic> openDialog(
      {required WidgetBuilder builder,
      Color? maskColor,
      VoidCallback? onDismiss,
      bool? clickMaskDismiss = true,
      VoidCallback? onMaskClick,
      SmartOnBack? onBack,
      SmartDialogController? controller,
      Alignment? alignment,
      bool? usePenetrate,
      bool? useAnimation,
      SmartAnimationType? animationType,
      List<SmartNonAnimationType>? nonAnimationTypes,
      Duration? animationTime,
      Widget? maskWidget,
      bool? debounce,
      Duration? displayTime,
      String? tag,
      @Deprecated("please use backType") bool? backDismiss,
      bool? keepSingle,
      bool? permanent,
      bool? useSystem,
      bool? bindPage,
      BuildContext? bindWidget,
      Rect? ignoreArea,
      SmartBackType? backType}) {
    _dialogStack.add("id");
    return SmartDialog.show(
        builder: builder,
        clickMaskDismiss: clickMaskDismiss,
        maskColor: maskColor,
        onMask: onMaskClick,
        onDismiss: () {
          if (_dialogStack.isNotEmpty) {
            _dialogStack.removeLast();
          }
          if (onDismiss != null) {
            onDismiss();
          }
        },
        onBack: onBack,
        alignment: alignment);
  }

  static void closeDialog() {
    SmartDialog.dismiss();
  }

  static bool isDialogShowing() {
    return _dialogStack.isNotEmpty;
  }
}
