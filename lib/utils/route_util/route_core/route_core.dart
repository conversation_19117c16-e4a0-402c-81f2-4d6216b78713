import 'package:flutter/material.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_core/yp_page_route.dart';
import 'package:gdjg_pure_flutter/utils/system_util/string_util.dart';

class RouteCore {
  //避免使用Context，全局持有Context
  static GlobalKey<NavigatorState> routeGlobalKey = GlobalKey<NavigatorState>();

  //路由导航器
  static final NavigatorState? _routeState = routeGlobalKey.currentState;

  //路由路径--页面 映射表
  static final Map<String, YPPageRoute> _routeMap = {};

  //兼容路由查找Set
  static final Set<String> _compatRouteNameSet = {};

  //路由栈，通过List 模拟Stack
  static final List<String> _routeStack = [];

  //single task模式缓存，用于缓存当前页面的参数，用于在返回时传递参数
  static Object? _singleTaskModeCache;
  //配合控制single task返回值
  static bool _couldGetCache = false;
  //目标pop 路由
  static String _targetPopRouteName = '';

  //pop缓存
  static Object? _popResult;

  static void registerRoute(YPPageRoute ypPageRoute) {
    _routeMap[ypPageRoute.routeName] = ypPageRoute;
  }

  static void registerRoutes(List<YPPageRoute> ypPageRoutes) {
    final map = {for (var item in ypPageRoutes) (item).routeName: item};
    _routeMap.addAll(map);
  }

  static void addRoute2Stack(String? routeName) {
    if (!routeName.isNullOrEmpty()) {
      _routeStack.add(routeName!);
    }
  }

  static Object? getLaunchModeCache() {
    if (!_couldGetCache) {
      return null;
    }
    _couldGetCache = false;
    return _singleTaskModeCache;
  }

  static void clearLaunchModeCache() {
    _singleTaskModeCache = null;
  }

  static Object? getPopResult() {
    return _popResult;
  }

  static void removeRouteFromStack(String? routeName) {
    if (!routeName.isNullOrEmpty()) {
      _routeStack.remove(routeName);
      if (_routeStack.last == _targetPopRouteName) {
        _couldGetCache = true;
      }
    }
  }

  static Future<dynamic> openPage(
      {String? routeName, Object? arguments, Object? params}) async {
    YPPageRoute? ypPageRoute;
    if (routeName == null ||
        routeName.isEmpty ||
        _compatRouteNameSet.contains(routeName)) {
      return null;
    }

    if (_routeMap.containsKey(routeName)) {
      ypPageRoute = _routeMap[routeName]!;
    } else {
      //找不到路由，再到兼容表里查询
      List<MapEntry<String, YPPageRoute>> entries = _routeMap.entries.toList();
      //遍历兼容链表
      for (var entry in entries) {
        for (var name in entry.value.compatRouteNames) {
          if (name == routeName) {
            ypPageRoute = entry.value;
            //添加到map里
            _routeMap[routeName] = ypPageRoute;
          }
        }
      }
    }

    if (ypPageRoute != null) {
      if (ypPageRoute.isSingleTask) {
        //查找目标页面是否在栈里
        if (_routeStack.contains(ypPageRoute.routeName)) {
          if (_routeStack.last == ypPageRoute.routeName) {
            //在栈顶，不处理
            return;
          } else {
            //非栈顶，清空栈直至目标页面
            if (params != null) {
              //有参数，则缓存参数
              _singleTaskModeCache = params;
            }
            _targetPopRouteName = ypPageRoute.routeName;
            _routeState?.popUntil((route)=>route.settings.name == ypPageRoute?.routeName);
            return;
          }
        }
      }

      return _routeState?.push(MaterialPageRoute(
          builder: ypPageRoute.widgetBuilder,
          settings:
              RouteSettings(name: ypPageRoute.routeName, arguments: params)));
    } else {
      _compatRouteNameSet.add(routeName);
    }

    _popResult = null;
  }

  static void closePage(Object? result) {
    _popResult = result;
    _routeState?.pop(result);
  }
}
