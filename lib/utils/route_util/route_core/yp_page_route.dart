
import 'package:flutter/material.dart';

class YPPageRoute {
  YPPageRoute({required this.routeName, required this.widgetBuilder, this.isSingleTask = false, this.compatRouteNames = const []});
  //页面路由唯一名字，全局需要唯一，命名方式:/模块名/页面名，名字全小写，若名字太长，则使用"_"连接，比如/user_center/my_setting
  final String routeName;
  //页面绑定的对象
  final WidgetBuilder widgetBuilder;
  //如果栈里存在页面A->B-C-D，D跳转到A，若是isSingleTask=true，则会清除B-C-D页面，最终栈为 A，否则最终栈为A->B-C-D-A
  //默认false
  bool isSingleTask;
  //预留，兼容路由
  List<String> compatRouteNames;
}