
abstract mixin class PageExtra {
  //该方法有两种场景会触发
  //1. A页面跳转到B页面，B页面接收A页面的传值，则B页面需要重写该方法，此时fromLaunchTask=false
  //2. 栈里存在ABCDE页面，E是栈顶，当从E页面跳转到A（A是singletask模式）页面时，E传值给A
  //A 需要重写该方法，此时fromLaunchTask=true
  onPageRoute(Object? routeParams, bool fromLaunchTask);

  //A页面跳转到B页面，B页面退出后，要给A页面返回值，则B页面需要重写该方法
  Object? onSend2Last();

  //A跳转到B，A有两种方法获取从B页面返回值
  //1. A通过YPRoute.openPage()调用跳转到B，A通过.then接收返回值
  //2. A重写onReceiveFromNext，2比1先执行
  //B页面返回后，A页面接收到B页面返回值，则A页面需要重写该方法
  void onReceiveFromNext(Object? backData);
}