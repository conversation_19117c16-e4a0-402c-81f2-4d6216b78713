import 'package:flutter/material.dart';
import 'package:gdjg_pure_flutter/utils/route_util/base_page/page_extra.dart';
import 'package:gdjg_pure_flutter/utils/route_util/base_page/page_lifecycle.dart';
import 'package:gdjg_pure_flutter/utils/route_util/observer/route_observer.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/utils/system_util/yprint.dart';

//A页面打开B页面整体生命周期如下
//A onPageHide->B onPageCreate->B onPageShow->B onPageRoute-> B build

//B返回A
//A onReceiveFromLast->A onPageShow->B onPageHide->B onPageDestroy



typedef PopCallback = bool Function();

//页面级别的Widget(可以路由）需要继承该基类，其他子View不作要求
abstract class BaseFulPage extends StatefulWidget {
  final String? title;
  final bool canPop;//true 直接返回，false，需要拦截
  final PopCallback? popCallback;//返回true，关闭当前页面
  const BaseFulPage({super.key, this.title, this.canPop = true, this.popCallback});
}

//继承自BaseFulPageState，无需重写initState、didChangeDependencies、dispose 方法，可用PageLifecycle替代
abstract class BaseFulPageState<T extends BaseFulPage> extends State<T>
    with WidgetsBindingObserver, RouteAware, PageLifecycle, PageExtra {

  String _TAG = '';

  bool _applyRoute = false;

  @override
  void initState() {
    super.initState();
    _TAG = "yp_lifecycle ${runtimeType.toString()}";
    yprint("$_TAG initState");
    WidgetsBinding.instance.addObserver(this);
    onPageCreate();
  }

  @override
  void dispose() {
    super.dispose();
    WidgetsBinding.instance.removeObserver(this);
    pageRouteObserver.unsubscribe(this);
    yprint("$_TAG dispose");
    onPageDestroy();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    pageRouteObserver.subscribe(this, ModalRoute.of(context) as PageRoute);
    if (!_applyRoute) {
      _applyRoute = true;
      onPageRoute(ModalRoute.of(context)?.settings.arguments, false);
    }
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    switch (state) {
      case AppLifecycleState.resumed:
        yprint('$_TAG resumed');
        onPageShow();
        break;
      case AppLifecycleState.inactive:
        yprint('$_TAG inactive');
        break;
      case AppLifecycleState.paused:
        yprint('$_TAG paused');
        onPageHide();
        break;
      case AppLifecycleState.detached:
        yprint('$_TAG detached');
        break;
      case AppLifecycleState.hidden:
        yprint('$_TAG hidden');
        break;
    }
  }

  @override
  Widget build(BuildContext context) => PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) {
        if (!didPop) {
          if (!widget.canPop) {
            if (widget.popCallback != null) {
              bool needClosePage = widget.popCallback!();
              if (needClosePage) {
                YPRoute.closePage(onSend2Last());
              }
            } else {
              //统一处理，弹窗
              yprint("good");
              YPRoute.closePage(onSend2Last());
            }
          } else {
            YPRoute.closePage(onSend2Last());
          }
        }
      },
      child: yBuild(context));

  Widget yBuild(BuildContext context);

  @override
  void didPop() {
    //pop 返回页面时回调，比如B返回A，B会执行这个回调，B会销毁
    super.didPop();
    yprint("$_TAG didPop");
    onPageHide();
  }

  @override
  void didPush() {
    //push 进入页面时回调，比如A跳转到B，B回调执行这个方法
    super.didPush();
    yprint("$_TAG didPush");
    onPageShow();
  }

  @override
  void didPopNext() {
    //pop 返回页面时回调，比如B返回A，A回调执行这个方法，A执行后，B的生命周期开始动
    super.didPopNext();
    yprint("$_TAG didPopNext");
    Object? routeCache = YPRoute.getLaunchModeCache();
    if (routeCache != null) {
      onPageRoute(routeCache, true);
      YPRoute.clearLaunchModeCache();
    }
    onReceiveFromNext(YPRoute.getPopResult());
    onPageShow();
  }

  @override
  void didPushNext() {
    //push 进入下一个页面回调，比如A跳转到B，A回调执行这个方法，先走A的生命周期
    super.didPushNext();
    yprint("$_TAG didPushNext");
    onPageHide();
  }

  @override
  void onPageCreate() {
    yprint("$_TAG onPageCreate");
  }

  @override
  void onPageShow() {
    yprint("$_TAG onPageShow");
  }

  @override
  void onPageHide() {
    yprint("$_TAG onPageHide");
  }

  @override
  void onPageDestroy() {
    yprint("$_TAG onPageDestroy");
  }

  @override
  void onPageRoute(Object? routeParams, bool fromLaunchTask) {
    yprint("$_TAG onPageRoute");
  }

  @override
  Object? onSend2Last() {
    return null;
  }

  @override
  void onReceiveFromNext(Object? backData) {
    yprint("$_TAG onReceiveFromLast");
  }
}
