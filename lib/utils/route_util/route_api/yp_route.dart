import 'package:gdjg_pure_flutter/utils/route_util/route_core/route_core.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_core/yp_page_route.dart';

class YPRoute {
  static registerRoute(YPPageRoute ypPageRoute) {
    RouteCore.registerRoute(ypPageRoute);
  }

  static registerRoutes(List<YPPageRoute> ypPageRoutes) {
    RouteCore.registerRoutes(ypPageRoutes);
  }

  static void addRoute2Stack(String? routeName) {
    RouteCore.addRoute2Stack(routeName);
  }

  static void removeRouteFromStack(String? routeName) {
    RouteCore.removeRouteFromStack(routeName);
  }

  static Object? getLaunchModeCache() {
    return RouteCore.getLaunchModeCache();
  }

  static void clearLaunchModeCache() {
    return RouteCore.clearLaunchModeCache();
  }

  static bool validPageShow() {
    return RouteCore.validPageShow();
  }

  static Object? getPopResult() {
    return RouteCore.getPopResult();
  }

  static Future<Object?>? openPage(String? routeName, {Object? params, int? closeNumBeforeOpen, bool? clearStackAndPush}) async{
    return RouteCore.openPage(routeName: routeName, params: params, closeNumBeforeOpen: closeNumBeforeOpen, clearStackAndPush: clearStackAndPush);
  }

  static void closePage(Object? result) {
    RouteCore.closePage(result);
  }
}
