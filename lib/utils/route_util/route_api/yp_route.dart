import 'package:flutter/cupertino.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:flutter_smart_dialog/src/kit/typedef.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_core/route_core.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_core/yp_page_route.dart';
import 'package:gdjg_pure_flutter/utils/web_util/web_page_param.dart';

import '../interceptors/route_interceptor.dart';

class YPRoute {

  YPRoute._();

  static void setTopRoute(PageRoute? route) {
    RouteCore.setTopRoute(route);
  }

  static PageRoute? getTopRoute() {
    return RouteCore.getTopRoute();
  }

  static registerRoute(YPPageRoute ypPageRoute) {
    RouteCore.registerRoute(ypPageRoute);
  }

  static registerRoutes(List<YPPageRoute> ypPageRoutes) {
    RouteCore.registerRoutes(ypPageRoutes);
  }

  static void registerInterceptor(RouteInterceptor routeInterceptor) {
    RouteCore.registerInterceptor(routeInterceptor);
  }

  static void addRoute2Stack(String? routeName) {
    RouteCore.addRoute2Stack(routeName);
  }

  static void removeRouteFromStack(String? routeName) {
    RouteCore.removeRouteFromStack(routeName);
  }

  static Object? getLaunchModeCache() {
    return RouteCore.getLaunchModeCache();
  }

  static void clearLaunchModeCache() {
    return RouteCore.clearLaunchModeCache();
  }

  static bool validPageShow() {
    return RouteCore.validPageShow();
  }

  static Object? getPopResult() {
    return RouteCore.getPopResult();
  }

  static bool onlyOnePageExist() {
    return RouteCore.onlyOnePageExist();
  }

  static Future<Object?>? openPage(String? routeName,
      {Object? params,
      int? closeNumBeforeOpen,
      bool? clearStackAndPush}) async {
    return RouteCore.openPage(
        routeName: routeName,
        params: params,
        closeNumBeforeOpen: closeNumBeforeOpen,
        clearStackAndPush: clearStackAndPush);
  }

  static void closePage([Object? result]) {
    RouteCore.closePage(result);
  }

  static Future<dynamic> openDialog(
      {required WidgetBuilder builder,
      Color? maskColor,
      VoidCallback? onDismiss,
      VoidCallback? onMaskClick,
      SmartOnBack? onBack,
      SmartDialogController? controller,
      Alignment? alignment,
      bool? clickMaskDismiss,
      bool? usePenetrate,
      bool? useAnimation,
      SmartAnimationType? animationType,
      List<SmartNonAnimationType>? nonAnimationTypes,
      Duration? animationTime,
      Widget? maskWidget,
      bool? debounce,
      Duration? displayTime,
      String? tag,
      @Deprecated("please use backType") bool? backDismiss,
      bool? keepSingle,
      bool? permanent,
      bool? useSystem,
      bool? bindPage,
      BuildContext? bindWidget,
      Rect? ignoreArea,
      SmartBackType? backType}) {
    return RouteCore.openDialog(
        builder: builder,
        maskColor: maskColor,
        clickMaskDismiss: clickMaskDismiss,
        onMaskClick: onMaskClick,
        onDismiss: onDismiss,
        onBack: onBack,
        alignment: alignment,
        usePenetrate: usePenetrate,
        useAnimation: useAnimation,
        animationType: animationType,
        nonAnimationTypes: nonAnimationTypes,
        animationTime: animationTime,
        maskWidget: maskWidget,
        debounce: debounce,
        displayTime: displayTime,
        tag: tag,
        backDismiss: backDismiss,
        keepSingle: keepSingle,
        permanent: permanent,
        useSystem: useSystem,
        bindPage: bindPage,
        );
  }

  static void closeDialog() {
    RouteCore.closeDialog();
  }

  static bool isDialogShowing() {
    return RouteCore.isDialogShowing();
  }


  static void openWebPage({required String url, String? title}) {
    RouteCore.openPage(routeName: "/web/web_page", params: WebPageParam(url: url, title: title));
  }
}
