

import 'package:flutter/cupertino.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/utils/system_util/yprint.dart';

//页面路由，用于每个页面内监听
final RouteObserver<PageRoute> pageRouteObserver = RouteObserver<PageRoute>();

//全局路由，统一监听所有路由跳转
final NavigatorObserver globalRouteObserver = GlobalRouteObserver();

class GlobalRouteObserver extends NavigatorObserver {
  final String TAG = 'yp_lifecycle_global';
  @override
  void didPush(Route<dynamic> route, Route<dynamic>? previousRoute) {
    super.didPush(route, previousRoute);
    YPRoute.addRoute2Stack(route.settings.name);
    yprint('$TAG didPush current route: ${route.settings.name} last route: ${previousRoute?.settings.name}');
    if (route is PageRoute) {
      YPRoute.setTopRoute(route);
    }
  }
  @override
  void didPop(Route<dynamic> route, Route<dynamic>? previousRoute) {
    super.didPop(route, previousRoute);
    YPRoute.removeRouteFromStack(route.settings.name);
    yprint('$TAG didPop current route: ${route.settings.name} last route: ${previousRoute?.settings.name}');
  }
}