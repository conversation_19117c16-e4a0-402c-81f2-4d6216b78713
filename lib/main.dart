import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:gdjg_pure_flutter/feature/group/us/group_us.dart';
import 'package:gdjg_pure_flutter/feature/worker/us/worker_us.dart';
import 'package:gdjg_pure_flutter/init_module/init_start.dart';
import 'package:gdjg_pure_flutter/utils/route_util/observer/route_observer.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_core/route_core.dart';
import 'package:get/get.dart';

import 'feature/tabbar/us/identity_us.dart';
import 'init_module/init_route.dart';

void main() {
  WidgetsFlutterBinding.ensureInitialized();

  // 初始化控制器
  Get.put(IdentityUS());
  Get.put(WorkerUS());
  Get.put(GroupUS());
  // 强制竖屏
  SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]).then((_) async {
    await initStart();
    runApp(const MyApp());
  });

  // runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return ScreenUtilInit(
      designSize: const Size(375, 812),
      minTextAdapt: true,
      splitScreenMode: true,
      builder: (context, child) {
        return MaterialApp(
          title: '工地记工',
          theme: ThemeData(
            appBarTheme:
                const AppBarTheme(surfaceTintColor: Colors.transparent),
            primaryColor: const Color(0xFF5290FD),
            scaffoldBackgroundColor: const Color(0xFFF5F6FA),
            visualDensity: VisualDensity.adaptivePlatformDensity,
            splashColor: Colors.transparent,
            highlightColor: Colors.transparent,
            textSelectionTheme:
                TextSelectionThemeData(cursorColor: const Color(0xFF5290FD)),
            textTheme: TextTheme(
              titleLarge: TextStyle(
                fontSize: 16.sp,
                fontWeight: FontWeight.w600,
                color: const Color(0xFF222222),
              ),
              bodyMedium: TextStyle(
                fontSize: 14.sp,
                color: const Color(0xFF666666),
              ),
              labelMedium: TextStyle(
                fontSize: 12.sp,
                color: const Color(0xFF999999),
              ),
            ),
            cardTheme: CardThemeData(
              elevation: 1,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8.r),
              ),
            ),
          ),
          debugShowCheckedModeBanner: false,
          builder: FlutterSmartDialog.init(),
          initialRoute: RouteNameCollection.main,
          routes: getInitRouteMap(),
          navigatorObservers: [
            globalRouteObserver,
            pageRouteObserver
          ], //按顺序执行回调
          navigatorKey: RouteCore.routeGlobalKey,
        );
      },
    );
  }
}
