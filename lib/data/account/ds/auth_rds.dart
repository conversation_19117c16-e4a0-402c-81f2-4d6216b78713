import 'package:gdjg_pure_flutter/data/account/ds/model/net/send_code_net_model.dart';
import 'package:net_plugin/pigeon/api_generated/net_channel.g.dart';
import 'package:net_plugin/pigeon/net_result/resp_result.dart';
import 'package:net_plugin/pigeon/net_service/net_core.dart';

import 'model/net/login_code_login_net_model.dart';
import 'model/net/waa_login_net_model.dart';
import 'model/param/code_login_param_model.dart';
import 'model/param/one_key_login_params_model.dart';
import 'model/param/waa_login_param_model.dart';
import 'model/param/we_chat_login_params_model.dart';

class AuthRds {
  ///  发送验证码
  Future<RespResult<SendCodeNetModel>> sendCode(String phone) async {
    return await NetCore.requestYPJAVA(
        BaseBizRequestEntity(
            url: '/reach/v1/verifyCode/loginIgnore/send',
            method: HTTP_METHOD.POST,
            content: {
              "biz": "login",
              "tel": phone,
            },
            requestExtra: RequestExtra(showLoading: true, printResp: true)),
        (json) => SendCodeNetModel.fromJson(json));
  }

  ///  验证码登陆
  Future<RespResult<LoginResultNetModel>> codeLogin(
      CodeLoginParamModel param) async {
    return await NetCore.requestYPJAVA(
        BaseBizRequestEntity(
            url: '/account/v1/login/codeLogin',
            method: HTTP_METHOD.POST,
            content: param.toMap()),
        (json) => LoginResultNetModel.fromJson(json));
  }

  /// 一键登录
  Future<RespResult<LoginResultNetModel>> oneKeyLogin(
      OneKeyLoginParamsModel param) async {
    return await NetCore.requestYPJAVA(
        BaseBizRequestEntity(
            url: '/account/v1/login/oneClickLogin',
            method: HTTP_METHOD.POST,
            content: param.toMap()),
        (json) => LoginResultNetModel.fromJson(json));
  }

  /// 微信登录
  Future<RespResult<LoginResultNetModel>> weChatLogin(
      WeChatLoginParamsModel param) async {
    return await NetCore.requestYPJAVA(
        BaseBizRequestEntity(
            url: '/account/v1/login/wechatAuthLogin',
            method: HTTP_METHOD.POST,
            content: param.toMap()),
        (json) => LoginResultNetModel.fromJson(json));
  }

  /// 工地记工登录
  Future<RespResult<WaaLoginNetModel>> waaLogin(
      WaaLoginParamModel param) async {
    return await NetCore.requestJGPHP(
        BaseBizRequestEntity(
            url: '/api/member/get_yuapo_info',
            method: HTTP_METHOD.POST,
            content: param.toMap()),
        (json) => WaaLoginNetModel.fromJson(json));
  }
}
