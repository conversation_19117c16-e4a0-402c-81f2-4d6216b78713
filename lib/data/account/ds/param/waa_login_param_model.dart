
class MemberGetYuapoInfoAParamModel {

  ///
  String? token;

  ///
  String? new_member;

  ///
  String? origin;

  ///
  String? refid;

  MemberGetYuapoInfoAParamModel();

  Map<String, Object> toMap() {
    var map = <String, Object>{};
    if (token != null) map["token"] = token!;
    if (new_member != null) map["new_member"] = new_member!;
    if (origin != null) map["origin"] = origin!;
    if (refid != null) map["refid"] = refid!;
    return map;
  }

}