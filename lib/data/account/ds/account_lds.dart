import '../../../utils/store_util/base_lds.dart';
import 'model/net/waa_login_net_model.dart';
import 'model/net/waa_login_net_model_transform.dart';

class AccountLds extends BaseLds<WaaLoginNetModel> {

  @override
  String getBizName() {
    return "account";
  }

  @override
  Map<String, dynamic> toJson(WaaLoginNetModel value) {
    return waaLoginNetModelToJson(value);
  }

  @override
  fromJson(Map<String, dynamic> json) {
    return waaLoginNetModelFromJson(json);
  }
}
