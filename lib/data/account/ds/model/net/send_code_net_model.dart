import 'dart:convert';

import 'package:json_annotation/json_annotation.dart';

part 'send_code_net_model.g.dart';

@JsonSerializable()
class SendCodeNetModel {
  /// 剩余可发送次数
  int? remainingSendTimes;
  /// 校验token（校验验证码时使用）
  String? verifyToken;
  /// 发送间隔（单位秒）
  int? sendInterval;

  SendCodeNetModel();

  factory SendCodeNetModel.fromJson(Map<String, dynamic> json) => _$LoginIgnoreSendModelFromJson(json);

  Map<String, dynamic> toJson() => _$LoginIgnoreSendModelToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

