import 'dart:convert';

import 'package:gdjg_pure_flutter/data/account/ds/model/net/waa_login_net_model_transform.dart';

class WaaLoginNetModel {
  /// 用户ID
  int? uid;

  /// UUID
  int? uuid;

  /// 用户名
  String? username;

  /// 昵称
  String? nickname;

  /// 手机号
  String? tel;

  /// 登录token
  String? token;

  /// 单点登录token
  String? singleToken;

  /// jgjz token
  String? jgjzToken;

  WaaLoginNetModel();

  factory WaaLoginNetModel.fromJson(Map<String, dynamic> json) =>
      waaLoginNetModelFromJson(json);

  Map<String, dynamic> toJson() => waaLoginNetModelToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
