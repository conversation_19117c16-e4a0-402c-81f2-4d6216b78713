import 'dart:convert';

import 'package:json_annotation/json_annotation.dart';

part 'login_code_login_net_model.g.dart';

@JsonSerializable()
class LoginResultNetModel {
  String? token;
  String? ticket;

  LoginResultNetModel();

  factory LoginResultNetModel.fromJson(Map<String, dynamic> json) => _$LoginResultNetModelFromJson(json);

  Map<String, dynamic> toJson() => _$LoginResultNetModelToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

