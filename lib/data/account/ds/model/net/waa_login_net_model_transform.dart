

import 'package:gdjg_pure_flutter/data/account/ds/model/net/waa_login_net_model.dart';

WaaLoginNetModel waaLoginNetModelFromJson(Map<String, dynamic> json) =>
    WaaLoginNetModel()
      ..uid = (json['uid'] as num?)?.toInt()
      ..uuid = (json['uuid'] as num?)?.toInt()
      ..username = json['username'] as String?
      ..nickname = json['nickname'] as String?
      ..tel = json['tel'] as String?
      ..token = json['token'] as String?
      ..singleToken = json['singletoken'] as String?
      ..jgjzToken = json['jgjztoken'] as String?;

Map<String, dynamic> waaLoginNetModelToJson(WaaLoginNetModel instance) =>
    <String, dynamic>{
      'uid': instance.uid,
      'uuid': instance.uuid,
      'username': instance.username,
      'nickname': instance.nickname,
      'tel': instance.tel,
      'token': instance.token,
      'singletoken': instance.singleToken,
      'jgjztoken': instance.jgjzToken,
    };