import 'login_share_param_model.dart';

class CodeLoginParamModel {
  String? code;
  ShareReq? shareReq;
  String? tel;
  String? verifyToken;

  CodeLoginParamModel({
    this.code,
    this.shareReq,
    this.tel,
    this.verifyToken,
  });

  Map<String, Object> toMap() {
    final Map<String, Object> map = {};

    if (code != null) {
      map['code'] = code!;
    }
    if (shareReq != null) {
      map['shareReq'] = shareReq!.toMap();
    }
    if (tel != null) {
      map['tel'] = tel!;
    }
    if (verifyToken != null) {
      map['verifyToken'] = verifyToken!;
    }

    return map;
  }
}
