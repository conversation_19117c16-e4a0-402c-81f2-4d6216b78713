class ShareReq {
  String? refTenantId;
  String? shareSource;
  String? trackSeed;

  ShareReq({
    this.refTenantId,
    this.shareSource,
    this.trackSeed,
  });

  Map<String, Object> toMap() {
    final Map<String, Object> map = {};

    if (refTenantId != null) {
      map['refTenantId'] = refTenantId!;
    }
    if (shareSource != null) {
      map['shareSource'] = shareSource!;
    }
    if (trackSeed != null) {
      map['trackSeed'] = trackSeed!;
    }

    return map;
  }
}
