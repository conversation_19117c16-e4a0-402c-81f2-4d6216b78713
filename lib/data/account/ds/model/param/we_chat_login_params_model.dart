import 'login_share_param_model.dart';

class WeChatLoginParamsModel {
  final ShareReq shareReq;
  final String authCode;
  final String appId;
  final String c; // 设备信息上下文

  WeChatLoginParamsModel({
    required this.shareReq,
    required this.authCode,
    required this.appId,
    required this.c,
  });

  Map<String, Object> toMap() {
    return {
      'shareReq': shareReq.toMap(),
      'authCode': authCode,
      'appId': appId,
      'c': c,
    };
  }
}