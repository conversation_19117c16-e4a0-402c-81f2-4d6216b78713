import 'package:json_annotation/json_annotation.dart';

class WaaLoginParamModel {
  String? token;
  String? newMember;
  String? origin;
  String? refid;

  WaaLoginParamModel({
     this.token,
     this.newMember,
     this.origin,
     this.refid,
  });

  Map<String, Object> toMap() {
    return {
      if (token != null) 'token': token!,
      if (newMember != null) 'newMember': newMember!,
      if (origin != null) 'origin': origin!,
      if (refid != null) 'refid': refid!,
    };
  }
}