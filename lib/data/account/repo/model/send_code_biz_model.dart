import 'dart:convert';

import 'package:gdjg_pure_flutter/data/account/ds/model/net/send_code_net_model.dart';

class SendCodeBizModel {
  /// 剩余可发送次数
  int remainingSendTimes;

  /// 校验token（校验验证码时使用）
  String verifyToken;

  /// 发送间隔（单位秒）
  int sendInterval;

  SendCodeBizModel({
    this.remainingSendTimes = 0,
    this.verifyToken = "",
    this.sendInterval = 0,
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}
