import 'dart:convert';

class WaaLoginBizModel {
  /// 用户ID
  final int uid;

  /// 用户唯一标识
  final int uuid;

  /// 用户名
  final String username;

  /// 昵称
  final String nickname;

  /// 手机号
  final String tel;

  /// 登录token
  final String token;

  /// 单一token
  final String singletoken;

  /// 其他token
  final String jgjztoken;

  WaaLoginBizModel({
    required this.uid,
    required this.uuid,
    required this.username,
    required this.nickname,
    required this.tel,
    required this.token,
    required this.singletoken,
    required this.jgjztoken,
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}
