import 'dart:convert';

class WaaLoginBizModel {
  /// 用户ID
  int uid;

  /// 用户唯一标识
  int uuid;

  /// 用户名
  String username;

  /// 昵称
  String nickname;

  /// 手机号
  String tel;

  /// 登录token
  String token;

  /// 单一token
  String singletoken;

  /// 其他token
  String jgjztoken;

  WaaLoginBizModel({
    required this.uid,
    required this.uuid,
    required this.username,
    required this.nickname,
    required this.tel,
    required this.token,
    required this.singletoken,
    required this.jgjztoken,
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}
