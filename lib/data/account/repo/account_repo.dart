import 'package:gdjg_pure_flutter/data/account/ds/auth_rds.dart';
import 'package:net_plugin/pigeon/net_result/resp_fail.dart';
import 'package:net_plugin/pigeon/net_result/resp_result.dart';

import '../../../utils/regex/regex_utils.dart';
import '../ds/accout_rds.dart';
import '../ds/model/param/code_login_param_model.dart';
import 'auth_repo.dart';
import 'model/login_code_login_biz_model.dart';

class AccountRepo {
  final _accountRds = AccountRds();
  final _authRds = AuthRds();
  final _authRepo = AuthRepo();




}