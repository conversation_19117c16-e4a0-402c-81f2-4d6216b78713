import 'package:gdjg_pure_flutter/data/account/repo/auth_repo_transform.dart';
import 'package:net_plugin/pigeon/net_result/resp_fail.dart';
import 'package:net_plugin/pigeon/net_result/resp_result.dart';

import '../../../utils/regex/regex_utils.dart';
import '../ds/account_lds.dart';
import '../ds/auth_rds.dart';
import '../ds/model/net/login_code_login_net_model.dart';
import '../ds/model/param/code_login_param_model.dart';
import '../ds/model/param/login_share_param_model.dart';
import '../ds/model/param/one_key_login_params_model.dart';
import '../ds/model/param/waa_login_param_model.dart';
import '../ds/model/param/we_chat_login_params_model.dart';
import 'model/login_code_login_biz_model.dart';
import 'model/send_code_biz_model.dart';
import 'model/waa_login_biz_model.dart';

class AuthRepo {
  final _authRds = AuthRds();
  final _accountLds = AccountLds();

  Future<RespResult<WaaLoginBizModel>> loginCodeLoginInner(
      CodeLoginParamModel param) async {
    if (!RegexUtils.isMobile(param.tel)) {
      return RespFail.buildProcessFail("请输入正确手机号");
    }
    if (!RegexUtils.isVerificationCode(param.code)) {
      return RespFail.buildProcessFail("请输入正确验证码");
    }
    final resp = await _authRds.codeLogin(param);

    final data = resp.getSucData();
    if (data == null) {
      return RespFail.buildBizFail<WaaLoginBizModel>(
          resp.fail?.code, resp.fail?.errorMsg, null, resp.fail?.askId);
    }

    return await _waaLogin(data, param.shareReq);
  }

  Future<RespResult<WaaLoginBizModel>> oneKeyLogin(
      OneKeyLoginParamsModel param) async {
    final resp = await _authRds.oneKeyLogin(param);
    final data = resp.getSucData();
    if (data == null) {
      return RespFail.buildBizFail<WaaLoginBizModel>(
          resp.fail?.code, resp.fail?.errorMsg, null, resp.fail?.askId);
    }
    return await _waaLogin(data, param.shareReq);
  }

  Future<RespResult<WaaLoginBizModel>> weChatLogin(
      WeChatLoginParamsModel param) async {
    final resp = await _authRds.weChatLogin(param);
    final data = resp.getSucData();
    if (data == null) {
      return RespFail.buildBizFail<WaaLoginBizModel>(
          resp.fail?.code, resp.fail?.errorMsg, null, resp.fail?.askId);
    }
    return await _waaLogin(data, param.shareReq);
  }

  Future<RespResult<SendCodeBizModel>> sendCode(String phone) async {
    if (!RegexUtils.isMobile(phone)) {
      return RespFail.buildProcessFail("请输入正确手机号");
    }
    final resp = await _authRds.sendCode(phone);
    return resp.map(sendCodeTransform);
  }

  Future<RespResult<WaaLoginBizModel>> _waaLogin(
      LoginResultNetModel? param, ShareReq? shareReq) async {
    final re = await _authRds.waaLogin(WaaLoginParamModel(
        token: param?.token,
        newMember: "",
        origin: "",
        refid: shareReq?.refTenantId));

    if (re.isOK()) {
      final data = re.getSucData();
      if (data != null) {
        _accountLds.save(data);
      }
    }

    return re.map(waaLoginTransform);
  }

  WaaLoginBizModel getAccount() {
    return waaLoginTransform(_accountLds.get());
  }
}
