import '../ds/model/net/login_code_login_net_model.dart';
import '../ds/model/net/send_code_net_model.dart';
import '../ds/model/net/waa_login_net_model.dart';
import 'auth_repo.dart';
import 'model/login_code_login_biz_model.dart';
import 'model/send_code_biz_model.dart';
import 'model/waa_login_biz_model.dart';

extension AuthRepoTransform on AuthRepo {
  LoginBizModel loginCodeLoginTransform(LoginResultNetModel? model) {
    return LoginBizModel(
      token: model?.token ?? "",
      ticket: model?.ticket ?? "",
    );
  }

  SendCodeBizModel sendCodeTransform(SendCodeNetModel? model) {
    return SendCodeBizModel(
      remainingSendTimes: model?.remainingSendTimes ?? 0,
      verifyToken: model?.verifyToken ?? "",
      sendInterval: model?.sendInterval ?? 60,
    );
  }

  WaaLoginBizModel waaLoginTransform(WaaLoginNetModel? model) {
    return WaaLoginBizModel(
      uid: model?.uid ?? 0,
      uuid: model?.uuid ?? 0,
      username: model?.username ?? "",
      nickname: model?.nickname ?? "",
      tel: model?.tel ?? "",
      token: model?.token ?? "",
      singletoken: model?.singleToken ?? "",
      jgjztoken: model?.jgjzToken ?? "",
    );
  }
}
