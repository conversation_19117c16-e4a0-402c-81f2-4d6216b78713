import 'package:gdjg_pure_flutter/data/contact_data/ds/model/net/worker_create_new_net_model.dart';
import 'package:gdjg_pure_flutter/data/contact_data/ds/model/net/worker_recover_net_model.dart';
import 'package:gdjg_pure_flutter/data/contact_data/ds/param/worker_create_new_param_model.dart';
import 'package:gdjg_pure_flutter/data/contact_data/repo/model/worker_create_new_biz_model.dart';
import 'package:gdjg_pure_flutter/data/contact_data/repo/model/worker_recover_biz_model.dart';
import 'package:net_plugin/pigeon/net_result/resp_result.dart';
import '../ds/contact_rds.dart';

class ContactRepo {
  final _contactRds = ContactRds();

  ///手动添加工友
  Future<RespResult<WorkerCreateNewBizModel?>> contactCreateWorker(
      WorkerCreateNewParamModel param) async {
    final result = await _contactRds.contactCreateWorker(param);
    return result.map(_transformCreateWorker);
  }

  ///恢复删除工友
  Future<RespResult<WorkerRecoverBizModel?>> recoverWorker(
      WorkerCreateNewParamModel param) async {
    final result = await _contactRds.recoverWorker(param);
    return result.map(_transformRecoverWorker);
  }

  WorkerCreateNewBizModel? _transformCreateWorker(
      WorkerCreateNewNetModel? netModel) {
    return netModel?.transform();
  }

  WorkerRecoverBizModel? _transformRecoverWorker(
      WorkerRecoverNetModel? netModel) {
    return netModel?.transform();
  }
}
