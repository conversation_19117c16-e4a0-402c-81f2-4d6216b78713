import 'dart:convert';

class WorkerRecoverBizModel {
  /// 
  double code;
  /// 
  String message;
  /// 
  final WorkerRecoverABizModel? data;

  WorkerRecoverBizModel({
    this.code = 0.0,
    this.message = "",
    this.data,
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class WorkerRecoverABizModel {
  /// 
  double workerId;
  /// 
  String namePy;

  WorkerRecoverABizModel({
    this.workerId = 0.0,
    this.namePy = "",
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}

