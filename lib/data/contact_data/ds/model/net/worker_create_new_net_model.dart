import 'dart:convert';
import 'package:gdjg_pure_flutter/data/contact_data/repo/model/worker_create_new_biz_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'worker_create_new_net_model.g.dart';

@JsonSerializable()
class WorkerCreateNewNetModel {
  ///
  int? worker_id;

  ///
  String? name_py;

  WorkerCreateNewNetModel();

  factory WorkerCreateNewNetModel.fromJson(Map<String, dynamic> json) =>
      _$WorkerCreateNewNetModelFromJson(json);

  Map<String, dynamic> toJson() => _$WorkerCreateNewNetModelToJson(this);

  WorkerCreateNewBizModel transform() {
    return WorkerCreateNewBizModel(
      workerId: worker_id ?? 0,
      namePy: name_py ?? "",
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}
