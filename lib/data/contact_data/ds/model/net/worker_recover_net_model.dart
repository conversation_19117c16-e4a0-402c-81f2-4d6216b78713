import 'dart:convert';
import 'package:gdjg_pure_flutter/data/contact_data/repo/model/worker_recover_biz_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'worker_recover_net_model.g.dart';

@JsonSerializable()
class WorkerRecoverNetModel {
  /// 
  double? code;
  /// 
  String? message;

  WorkerRecoverNetModel();

  factory WorkerRecoverNetModel.fromJson(Map<String, dynamic> json) => _$WorkerRecoverNetModelFromJson(json);

  Map<String, dynamic> toJson() => _$WorkerRecoverNetModelToJson(this);

  WorkerRecoverBizModel transform() {
    return WorkerRecoverBizModel(
      code: code ?? 0.0,
      message: message ?? "",
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class WorkerRecoverANetModel {
  /// 
  double? worker_id;
  /// 
  String? name_py;

  WorkerRecoverANetModel();

  factory WorkerRecoverANetModel.fromJson(Map<String, dynamic> json) => _$WorkerRecoverANetModelFromJson(json);

  Map<String, dynamic> toJson() => _$WorkerRecoverANetModelToJson(this);

  WorkerRecoverABizModel transform() {
    return WorkerRecoverABizModel(
      workerId: worker_id ?? 0.0,
      namePy: name_py ?? "",
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

