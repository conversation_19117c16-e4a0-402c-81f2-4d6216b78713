// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'worker_recover_net_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

WorkerRecoverNetModel _$WorkerRecoverNetModelFromJson(
        Map<String, dynamic> json) =>
    WorkerRecoverNetModel()
      ..code = (json['code'] as num?)?.toDouble()
      ..message = json['message'] as String?;

Map<String, dynamic> _$WorkerRecoverNetModelToJson(
        WorkerRecoverNetModel instance) =>
    <String, dynamic>{
      'code': instance.code,
      'message': instance.message,
    };

WorkerRecoverANetModel _$WorkerRecoverANetModelFromJson(
        Map<String, dynamic> json) =>
    WorkerRecoverANetModel()
      ..worker_id = (json['worker_id'] as num?)?.toDouble()
      ..name_py = json['name_py'] as String?;

Map<String, dynamic> _$WorkerRecoverANetModelToJson(
        WorkerRecoverANetModel instance) =>
    <String, dynamic>{
      'worker_id': instance.worker_id,
      'name_py': instance.name_py,
    };
