import 'package:gdjg_pure_flutter/data/contact_data/ds/model/net/worker_create_new_net_model.dart';
import 'package:gdjg_pure_flutter/data/contact_data/ds/model/net/worker_recover_net_model.dart';
import 'package:gdjg_pure_flutter/data/contact_data/ds/param/worker_create_new_param_model.dart';
import 'package:net_plugin/pigeon/api_generated/net_channel.g.dart';
import 'package:net_plugin/pigeon/net_result/resp_result.dart';
import 'package:net_plugin/pigeon/net_service/net_core.dart';

class ContactRds {
  ///手动添加工友
  Future<RespResult<WorkerCreateNewNetModel>> contactCreateWorker(
      WorkerCreateNewParamModel params) async {
    return await NetCore.requestJGPHP(
        BaseBizRequestEntity(
            url: '/api/v3/worker/create_new',
            method: HTTP_METHOD.POST,
            content: params.toMap(),
            requestExtra: RequestExtra(showLoading: true, printResp: true)),
        (json) => WorkerCreateNewNetModel.fromJson(json));
  }
  ///恢复删除工友
  Future<RespResult<WorkerRecoverNetModel>> recoverWorker(
      WorkerCreateNewParamModel params) async {
    return await NetCore.requestJGPHP(
        BaseBizRequestEntity(
            url: '/api/v3/worker/recover',
            method: HTTP_METHOD.POST,
            content: params.toMap(),
            requestExtra: RequestExtra(showLoading: true, printResp: true)),
        (json) => WorkerRecoverNetModel.fromJson(json));
  }
}
