import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';

class WorkerCreateNewParamModel {
  /// 工人名字
  final String name;

  /// 工人电话
  final String tel;

  /// 班组id
  final String dept_id;

  WorkerCreateNewParamModel({
    this.name = "",
    this.tel = "",
    this.dept_id = "",
  });

  Map<String, Object> toMap() {
    return {
      "name": name,
      "name_color": '#FFFFFF', //ColorsUtil.randomColor
      "tel": tel,
      "dept_id": dept_id,
    };
  }
}
