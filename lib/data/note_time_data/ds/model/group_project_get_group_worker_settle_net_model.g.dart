// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'group_project_get_group_worker_settle_net_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

GroupProjectGetGroupWorkerSettleNetModel
    _$GroupProjectGetGroupWorkerSettleNetModelFromJson(
            Map<String, dynamic> json) =>
        GroupProjectGetGroupWorkerSettleNetModel()
          ..not_settle_money = (json['not_settle_money'] as num?)?.toDouble()
          ..start_time = json['start_time'] as String?
          ..end_time = json['end_time'] as String?
          ..is_settle = (json['is_settle'] as num?)?.toDouble()
          ..worker_name = json['worker_name'] as String?
          ..fee_standard_id = json['fee_standard_id'] as String?
          ..fee_standard_id_contractor =
              json['fee_standard_id_contractor'] as String?;

Map<String, dynamic> _$GroupProjectGetGroupWorkerSettleNetModelToJson(
        GroupProjectGetGroupWorkerSettleNetModel instance) =>
    <String, dynamic>{
      'not_settle_money': instance.not_settle_money,
      'start_time': instance.start_time,
      'end_time': instance.end_time,
      'is_settle': instance.is_settle,
      'worker_name': instance.worker_name,
      'fee_standard_id': instance.fee_standard_id,
      'fee_standard_id_contractor': instance.fee_standard_id_contractor,
    };
