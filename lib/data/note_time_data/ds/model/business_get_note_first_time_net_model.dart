import 'dart:convert';
import 'package:gdjg_pure_flutter/data/note_time_data/repo/model/business_get_note_first_time_biz_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'business_get_note_first_time_net_model.g.dart';

@JsonSerializable()
class BusinessGetNoteFirstTimeNetModel {
  /// 
  String? date;

  BusinessGetNoteFirstTimeNetModel();

  factory BusinessGetNoteFirstTimeNetModel.fromJson(
      Map<String, dynamic> json) =>
      _$BusinessGetNoteFirstTimeNetModelFromJson(json);

  Map<String, dynamic> toJson() =>
      _$BusinessGetNoteFirstTimeNetModelToJson(this);

  transform() {
    return BusinessGetNoteFirstTimeBizModel(
      date: date ?? "2020-01-01",
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

