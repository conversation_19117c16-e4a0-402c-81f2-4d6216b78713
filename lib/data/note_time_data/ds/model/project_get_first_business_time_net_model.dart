import 'dart:convert';
import 'package:gdjg_pure_flutter/data/note_time_data/repo/model/project_get_first_business_time_biz_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'project_get_first_business_time_net_model.g.dart';

@JsonSerializable()
class ProjectGetFirstBusinessTimeNetModel {
  /// 
  String? start_time;
  /// 
  String? end_time;

  ProjectGetFirstBusinessTimeNetModel();

  factory ProjectGetFirstBusinessTimeNetModel.fromJson(Map<String, dynamic> json) => _$ProjectGetFirstBusinessTimeNetModelFromJson(json);

  Map<String, dynamic> toJson() => _$ProjectGetFirstBusinessTimeNetModelToJson(this);

   transform() {
    return ProjectGetFirstBusinessTimeBizModel(
      startTime: start_time ?? "",
      endTime: end_time ?? "",
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

