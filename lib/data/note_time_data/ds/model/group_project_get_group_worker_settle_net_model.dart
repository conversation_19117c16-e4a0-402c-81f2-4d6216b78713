import 'dart:convert';
import 'package:gdjg_pure_flutter/data/note_time_data/repo/model/group_project_get_group_worker_settle_biz_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'group_project_get_group_worker_settle_net_model.g.dart';

@JsonSerializable()
class GroupProjectGetGroupWorkerSettleNetModel {
  /// 未结金额
  double? not_settle_money;
  /// 开始时间
  String? start_time;
  /// 结束时间
  String? end_time;
  /// 是否已结清
  double? is_settle;
  /// 
  String? worker_name;
  /// 点工工资规则id
  String? fee_standard_id;
  /// 包工工资规则id
  String? fee_standard_id_contractor;

  GroupProjectGetGroupWorkerSettleNetModel();

  factory GroupProjectGetGroupWorkerSettleNetModel.fromJson(Map<String, dynamic> json) => _$GroupProjectGetGroupWorkerSettleNetModelFromJson(json);

  Map<String, dynamic> toJson() => _$GroupProjectGetGroupWorkerSettleNetModelToJson(this);

   transform() {
    return GroupProjectGetGroupWorkerSettleBizModel(
      notSettleMoney: not_settle_money ?? 0.0,
      startTime: start_time ?? "",
      endTime: end_time ?? "",
      isSettle: is_settle ?? 0.0,
      workerName: worker_name ?? "",
      feeStandardId: fee_standard_id ?? "",
      feeStandardIdContractor: fee_standard_id_contractor ?? "",
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

