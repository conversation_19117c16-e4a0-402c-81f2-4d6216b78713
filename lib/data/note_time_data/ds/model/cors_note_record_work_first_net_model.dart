import 'dart:convert';
import 'package:gdjg_pure_flutter/data/note_time_data/repo/model/cors_note_record_work_first_biz_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'cors_note_record_work_first_net_model.g.dart';

@JsonSerializable()
class CorsNoteRecordWorkFirstNetModel {
  /// 
  String? date;

  CorsNoteRecordWorkFirstNetModel();

  factory CorsNoteRecordWorkFirstNetModel.fromJson(Map<String, dynamic> json) => _$CorsNoteRecordWorkFirstNetModelFromJson(json);

  Map<String, dynamic> toJson() => _$CorsNoteRecordWorkFirstNetModelToJson(this);

   transform() {
    return CorsNoteRecordWorkFirstBizModel(
      date: date ?? "",
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

