import 'package:gdjg_pure_flutter/data/note_time_data/ds/model/business_get_note_first_time_net_model.dart';
import 'package:gdjg_pure_flutter/data/note_time_data/ds/model/cors_note_record_work_first_net_model.dart';
import 'package:gdjg_pure_flutter/data/note_time_data/ds/model/group_project_get_group_worker_settle_net_model.dart';
import 'package:gdjg_pure_flutter/data/note_time_data/ds/model/project_get_first_business_time_net_model.dart';
import 'package:net_plugin/pigeon/api_generated/net_channel.g.dart';
import 'package:net_plugin/pigeon/net_result/resp_result.dart';
import 'package:net_plugin/pigeon/net_service/net_core.dart';

import 'param/business_get_note_first_time_param_model.dart';
import 'param/group_project_get_group_worker_settle_param_model.dart';
import 'param/project_get_first_business_time_param_model.dart';

class NoteTimeRds {

  /// 获取项目第一次记工时间到最后一笔记工时间
  Future<RespResult<ProjectGetFirstBusinessTimeNetModel>> getFirstBusinessTime(ProjectGetFirstBusinessTimeParamModel params) async {
    return await NetCore.requestJGPHP(
        BaseBizRequestEntity(
            url: '/api/v3/group-project/get-group-worker-count',
            method: HTTP_METHOD.GET,
            content: params.toMap()),
        (json) => ProjectGetFirstBusinessTimeNetModel.fromJson(json));
  }

  /// 获取第一次记工时间,跨账本、单账本未结/统计
  Future<RespResult<CorsNoteRecordWorkFirstNetModel>> getRecordWorkFirst() async {
    return await NetCore.requestJGPHP(
        BaseBizRequestEntity(
            url: '/api/cors_note/record-work-first',
            method: HTTP_METHOD.GET,
            content: {}),
            (json) => CorsNoteRecordWorkFirstNetModel.fromJson(json));
  }

  /// 获取第一笔流水时间,流水统计相关
  Future<RespResult<BusinessGetNoteFirstTimeNetModel>> getNoteFirstTime(BusinessGetNoteFirstTimeParamModel params) async {
    return await NetCore.requestJGPHP(
        BaseBizRequestEntity(
            url: '/api/business/get-note-first-time',
            method: HTTP_METHOD.GET,
            content: params.toMap()),
            (json) => BusinessGetNoteFirstTimeNetModel.fromJson(json));
  }

  /// 获取我参与的获取开始结束时间
  Future<RespResult<GroupProjectGetGroupWorkerSettleNetModel>> getGroupWorkerSettle(GroupProjectGetGroupWorkerSettleParamModel params) async {
    return await NetCore.requestJGPHP(
        BaseBizRequestEntity(
            url: '/api/v3/group-project/get-group-worker-settle',
            method: HTTP_METHOD.GET,
            content: params.toMap()),
            (json) => GroupProjectGetGroupWorkerSettleNetModel.fromJson(json));
  }
}
