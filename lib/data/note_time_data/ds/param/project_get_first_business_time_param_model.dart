
///获取第一次记工时间 参数
class ProjectGetFirstBusinessTimeParamModel {

  /// 记工本id
  final String work_note;

  /// 0 在建 1 已结束
  final String status;

  /// 1 班组流水，2个人流水，默认为2
  final String identity;

   ProjectGetFirstBusinessTimeParamModel({
     this.work_note = "",
     this.status = "",
     this.identity = "",
  });

  toMap() {
    return {
      "work_note": work_note,
      "status": status,
      "identity": identity,
    };
  }
}

