
///获取第一次记工时间 参数
class ProjectGetFirstBusinessTimeParamModel {

  /// 记工本id 个人用这个
  final String work_notes;

  /// 记工本id 班组用这个
  final String work_note;

  /// 0 在建 1 已结束
  final String status;

  /// 1 班组流水，2个人流水，默认为2
  final String identity;

   ProjectGetFirstBusinessTimeParamModel({
     this.work_notes = "",
     this.work_note = "",
     this.status = "0",
     this.identity = "",
  });

  toMap() {
    return {
      "work_notes": work_notes,
      "work_note": work_note,
      "status": status,
      "identity": identity,
    };
  }
}

