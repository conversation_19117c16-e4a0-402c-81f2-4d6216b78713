import 'dart:convert';
import 'package:gdjg_pure_flutter/data/note_time_data/repo/model/business_get_note_first_time_biz_model.dart';
import 'package:json_annotation/json_annotation.dart';


class BusinessGetNoteFirstTimeParamModel {
  /// 记工本id
  final String work_note;

  BusinessGetNoteFirstTimeParamModel({
    this.work_note = "",
  });

  toMap() {
    return {
      "work_note": work_note,
    };
  }
}

