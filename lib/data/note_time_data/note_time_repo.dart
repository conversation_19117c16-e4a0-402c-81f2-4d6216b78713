import 'package:gdjg_pure_flutter/data/note_time_data/ds/note_time_rds.dart';
import 'package:gdjg_pure_flutter/data/note_time_data/ds/model/business_get_note_first_time_net_model.dart';
import 'package:gdjg_pure_flutter/data/note_time_data/ds/model/cors_note_record_work_first_net_model.dart';
import 'package:gdjg_pure_flutter/data/note_time_data/ds/model/group_project_get_group_worker_settle_net_model.dart';
import 'package:gdjg_pure_flutter/data/note_time_data/ds/model/project_get_first_business_time_net_model.dart';
import 'package:gdjg_pure_flutter/data/note_time_data/ds/param/group_project_get_group_worker_settle_param_model.dart';
import 'package:gdjg_pure_flutter/data/note_time_data/ds/param/project_get_first_business_time_param_model.dart';
import 'package:gdjg_pure_flutter/data/note_time_data/repo/model/business_get_note_first_time_biz_model.dart';
import 'package:gdjg_pure_flutter/data/note_time_data/repo/model/cors_note_record_work_first_biz_model.dart';
import 'package:gdjg_pure_flutter/data/note_time_data/repo/model/group_project_get_group_worker_settle_biz_model.dart';
import 'package:gdjg_pure_flutter/data/note_time_data/repo/model/project_get_first_business_time_biz_model.dart';
import 'package:net_plugin/pigeon/net_result/resp_result.dart';

class NoteTimeRepo {
  final _noteTimeRds = NoteTimeRds();

  /// 获取项目第一次记工时间到最后一笔记工时间
  Future<RespResult<ProjectGetFirstBusinessTimeBizModel>> getFirstBusinessTime(
      ProjectGetFirstBusinessTimeParamModel params) async {
    final result = await _noteTimeRds.getFirstBusinessTime(params);
    return result.map(_transformFirstBusinessTime);
  }

  /// 获取项目 第一次记工时间到最后一笔记工时间
  Future<RespResult<ProjectGetFirstBusinessTimeBizModel>> getWorkerFirstBusinessTime(
      ProjectGetFirstBusinessTimeParamModel params) async {
    final result = await _noteTimeRds.getWorkerFirstBusinessTime(params);
    return result.map(_transformFirstBusinessTime);
  }

  /// 获取第一次记工时间,跨账本、单账本未结/统计
  Future<RespResult<CorsNoteRecordWorkFirstBizModel>>
      getRecordWorkFirst() async {
    final result = await _noteTimeRds.getRecordWorkFirst();
    return result.map(_transformRecordWorkFirst);
  }

  /// 获取第一笔流水时间,流水统计相关
  Future<RespResult<BusinessGetNoteFirstTimeBizModel>> getNoteFirstTime(String workNoteId) async {
    final result = await _noteTimeRds.getNoteFirstTime(workNoteId);
    return result.map(_transformNoteFirstTime);
  }

  /// 获取工友所在项目的时间，结算,点包工资等信息
  Future<RespResult<GroupProjectGetGroupWorkerSettleBizModel>>
      getGroupWorkerSettle(
          GroupProjectGetGroupWorkerSettleParamModel params) async {
    final result = await _noteTimeRds.getGroupWorkerSettle(params);
    return result.map(_transformGroupWorkerSettle);
  }

  ProjectGetFirstBusinessTimeBizModel _transformFirstBusinessTime(
      ProjectGetFirstBusinessTimeNetModel? netModel) {
    return netModel?.transform();
  }

  CorsNoteRecordWorkFirstBizModel _transformRecordWorkFirst(
      CorsNoteRecordWorkFirstNetModel? netModel) {
    return netModel?.transform();
  }

  BusinessGetNoteFirstTimeBizModel _transformNoteFirstTime(
      BusinessGetNoteFirstTimeNetModel? netModel) {
    return netModel?.transform();
  }

  GroupProjectGetGroupWorkerSettleBizModel _transformGroupWorkerSettle(
      GroupProjectGetGroupWorkerSettleNetModel? netModel) {
    return netModel?.transform();
  }
}
