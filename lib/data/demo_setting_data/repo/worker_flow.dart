import 'dart:io';

import 'package:dio/dio.dart';
import 'package:dio/io.dart';
import 'package:flutter/foundation.dart';
import 'package:gdjg_pure_flutter/data/demo_setting_data/entity/worker_flow/business_get_index_business_list_biz_model.dart';
import 'package:gdjg_pure_flutter/data/demo_setting_data/entity/worker_flow/business_get_index_business_list_net_model.dart';

class WorkerFlowRepo {
  ///
  Future<BusinessGetIndexBusinessListBizModel?> fetchHeaderData() async {
    try {
      var token =
          "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpYXQiOjE3NTA0MDc4ODcsImV4cCI6MTc2MDc3NTg4NywiZGF0YSI6eyJzaW5nbGUiOiIyN0FDRzFQVklYSUhROEVBIiwidWlkIjoyNTEzNTYyNywiYnVzaW5lc3MiOiIyIiwic3lzdGVtX3R5cGUiOiJhbmRyb2lkIiwibWluaV90b2tlbiI6IjI3QUNHMVBWSVhJSFE4RUEiLCJpZCI6MjUxMzU2MjcsInV1aWQiOjI1MTM1NjI3fSwidG9rZW4iOnsicmVnUnQiOiJhbmRyb2lkIiwidGVuYW50S2V5IjoiSkdKWiIsInRlbmFudElkIjoyNTEzNTYyNywicGFja2FnZU5hbWUiOiJjb20ueXVwYW8uZ29uZ2Rpamlnb25nIiwidXNlcklkIjoyNTEzNTYyNywidG9rZW4iOiIyN0FDRzFQVklYSUhROEVBIn19.LdJz4KOiXyGEL_-chdEVenEbc_m-tkITI7NEfdm-BXM";
      var jgjztoken =
          "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJ0ZXN0LmxvZ2luIiwiaWF0IjoxNzUwNDA3ODg3LCJ1aWQiOjI1MTM1NjI3fQ.MavprWEOyrpZJqFJaMa5aA40YezjDxjnuDYMEdOREgY";
      Dio dio = Dio(BaseOptions(
        baseUrl: "http://app-test.cdmgkj.cn",
        connectTimeout: const Duration(seconds: 10),
        receiveTimeout: const Duration(seconds: 10),
        headers: {
          'Content-Type': 'application/json',
          'jgjztoken': jgjztoken,
          'singletoken': token,
          'uuid': '25135627',
          'uid': '25135627',
          'deviceuuid': '195b682cf0a4f826',
          'imei': '195b682cf0a4f826',
          'env': 'TEST',
          'channel': 'authority',
          'user-agent': 'YP JGJZ Redmi 22101317C 13 6.6.0 195b682cf0a4f826 1749713690379',
          'business': '2',
          'package_name': 'gdjg',
          'version': '6.6.0',
          'device': 'Redmi,22101317C',
          'versioncode': '660',
          'system_type': 'android',
          'system': 'android',
          'systemversion': '13',
          'apiversion': '3.0',
          'token': token,
          'source': 'agd',
          'oaid': '2068c49e6c60528a',
        },
      ));
      if (kDebugMode) {
        dio.httpClientAdapter = IOHttpClientAdapter()
          ..createHttpClient = () {
            var httpClient = HttpClient();
            // httpClient.findProxy = (uri) => "PROXY ************:8889";
            httpClient.badCertificateCallback =
                (X509Certificate cert, String host, int port) => true;
            return httpClient;
          };
      }
      var mYear = DateTime.now().year;
      var mMonth = DateTime.now().month;
      var mDay = DateTime.now().day;
      Response response = await dio.get(
        "/api/v3/business/get-index-business-count",
        data: {
          "identity": 2,
          "start_time": "$mYear-$mMonth-01",
          "end_time": "$mYear-$mMonth-$mDay", //当日
          "status": 0,
        },
      );
      if (response.statusCode == 200) {
        print('成功: ${response.data}');
        var respData = BusinessGetIndexBusinessListNetModel.fromJson(response.data);
        var respEntity = respData.transform();
        return respEntity;
      } else {
        print('失败: code:${response.statusCode} msg:${response.statusMessage}');
      }
    } catch (e) {
      print('请求异常: $e');
      throw Exception("error");
    }
    return null;
  }

  Future<BusinessGetIndexBusinessListBizModel?> fetchListData() async {
    try {
      var token =
          "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpYXQiOjE3NTA0MDc4ODcsImV4cCI6MTc2MDc3NTg4NywiZGF0YSI6eyJzaW5nbGUiOiIyN0FDRzFQVklYSUhROEVBIiwidWlkIjoyNTEzNTYyNywiYnVzaW5lc3MiOiIyIiwic3lzdGVtX3R5cGUiOiJhbmRyb2lkIiwibWluaV90b2tlbiI6IjI3QUNHMVBWSVhJSFE4RUEiLCJpZCI6MjUxMzU2MjcsInV1aWQiOjI1MTM1NjI3fSwidG9rZW4iOnsicmVnUnQiOiJhbmRyb2lkIiwidGVuYW50S2V5IjoiSkdKWiIsInRlbmFudElkIjoyNTEzNTYyNywicGFja2FnZU5hbWUiOiJjb20ueXVwYW8uZ29uZ2Rpamlnb25nIiwidXNlcklkIjoyNTEzNTYyNywidG9rZW4iOiIyN0FDRzFQVklYSUhROEVBIn19.LdJz4KOiXyGEL_-chdEVenEbc_m-tkITI7NEfdm-BXM";
      var jgjztoken =
          "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJ0ZXN0LmxvZ2luIiwiaWF0IjoxNzUwNDA3ODg3LCJ1aWQiOjI1MTM1NjI3fQ.MavprWEOyrpZJqFJaMa5aA40YezjDxjnuDYMEdOREgY";
      Dio dio = Dio(BaseOptions(
        baseUrl: "http://app-test.cdmgkj.cn",
        connectTimeout: const Duration(seconds: 10),
        receiveTimeout: const Duration(seconds: 10),
        headers: {
          'Content-Type': 'application/json',
          'jgjztoken': jgjztoken,
          'singletoken': token,
          'uuid': '25135627',
          'uid': '25135627',
          'deviceuuid': '195b682cf0a4f826',
          'imei': '195b682cf0a4f826',
          'env': 'TEST',
          'channel': 'authority',
          'user-agent': 'YP JGJZ Redmi 22101317C 13 6.6.0 195b682cf0a4f826 1749713690379',
          'business': '2',
          'package_name': 'gdjg',
          'version': '6.6.0',
          'device': 'Redmi,22101317C',
          'versioncode': '660',
          'system_type': 'android',
          'system': 'android',
          'systemversion': '13',
          'apiversion': '3.0',
          'token': token,
          'source': 'agd',
          'oaid': '2068c49e6c60528a',
        },
      ));
      if (kDebugMode) {
        dio.httpClientAdapter = IOHttpClientAdapter()
          ..createHttpClient = () {
            var httpClient = HttpClient();
            // httpClient.findProxy = (uri) => "PROXY ************:8889";
            httpClient.badCertificateCallback =
                (X509Certificate cert, String host, int port) => true;
            return httpClient;
          };
      }
      var mYear = DateTime.now().year;
      var mMonth = DateTime.now().month;
      var mDay = DateTime.now().day;
      Response response = await dio.get(
        "/api/v3/business/get-index-business-list",
        data: {
          "identity": 2,
          "start_time": "$mYear-$mMonth-01",
          "end_time": "$mYear-$mMonth-$mDay", //当日
          "limit": 20,
          "page": 1,
        },
      );
      if (response.statusCode == 200) {
        print('成功: ${response.data}');
        var respData = BusinessGetIndexBusinessListNetModel.fromJson(response.data["data"]);
        var respEntity = respData.transform();
        return respEntity;
      } else {
        print('失败: code:${response.statusCode} msg:${response.statusMessage}');
      }
    } catch (e) {
      print('请求异常: $e');
      throw Exception("error");
    }
    return null;
  }
}
