import 'package:dio/dio.dart';

/// 工友管理仓库
class WorkerManagerRepo {
  final Dio _dio = Dio(BaseOptions(
    baseUrl: 'http://app-test.cdmgkj.cn',
    connectTimeout: const Duration(seconds: 10),
    receiveTimeout: const Duration(seconds: 10),
    headers: {
      'Content-Type': 'application/json',
      'jgjztoken':
      'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJ0ZXN0LmxvZ2luIiwiaWF0IjoxNzUwNjU4Mjc0LCJ1aWQiOjIwMDkyMjIzfQ.hOVGm9cvU-Gd1XpcicTl1_mCGo70nKq6fAw7W72AFuY',
      'singletoken':
      'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpYXQiOjE3NTA2NTgyNzMsImV4cCI6MTc2MTAyNjI3MywiZGF0YSI6eyJzaW5nbGUiOiJYN1NaVVRXUFVHTlY3SjRRIiwidWlkIjoyMDA5MjIyMywiYnVzaW5lc3MiOiIyIiwic3lzdGVtX3R5cGUiOiJhbmRyb2lkIiwibWluaV90b2tlbiI6Ilg3U1pVVFdQVUdOVjdKNFEiLCJpZCI6MjAwOTIyMjMsInV1aWQiOjIwMDkyMjIzfSwidG9rZW4iOnsicmVnUnQiOiJhbmRyb2lkIiwidGVuYW50S2V5IjoiSkdKWiIsInRlbmFudElkIjoyMDA5MjIyMywicGFja2FnZU5hbWUiOiJjb20ueXVwYW8uZ29uZ2Rpamlnb25nIiwidXNlcklkIjoyMDA5MjIyMywidG9rZW4iOiJYN1NaVVRXUFVHTlY3SjRRIn19.iGrZlZz1Qvy6ndTlUriX8ZRAnWeTKhmh1nrFKnizJPg',
      'uuid': '20092223',
      'uid': '20092223',
      'deviceuuid': '9ff47ff8494c4ec2',
      'imei': '9ff47ff8494c4ec2',
      'env': 'TEST',
      'channel': 'authority',
      'user-agent':
      'YP JGJZ Redmi 22101317C 13 6.7.0 9ff47ff8494c4ec2 1750661270718',
      'business': '2',
      'package_name': 'gdjg',
      'version': '6.7.0',
      'device': 'Redmi,22101317C',
      'versioncode': '670',
      'system_type': 'android',
      'system': 'android',
      'systemversion': '13',
      'apiversion': '3.0',
      'token':
      'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpYXQiOjE3NTA2NTgyNzMsImV4cCI6MTc2MTAyNjI3MywiZGF0YSI6eyJzaW5nbGUiOiJYN1NaVVRXUFVHTlY3SjRRIiwidWlkIjoyMDA5MjIyMywiYnVzaW5lc3MiOiIyIiwic3lzdGVtX3R5cGUiOiJhbmRyb2lkIiwibWluaV90b2tlbiI6Ilg3U1pVVFdQVUdOVjdKNFEiLCJpZCI6MjAwOTIyMjMsInV1aWQiOjIwMDkyMjIzfSwidG9rZW4iOnsicmVnUnQiOiJhbmRyb2lkIiwidGVuYW50S2V5IjoiSkdKWiIsInRlbmFudElkIjoyMDA5MjIyMywicGFja2FnZU5hbWUiOiJjb20ueXVwYW8uZ29uZ2Rpamlnb25nIiwidXNlcklkIjoyMDA5MjIyMywidG9rZW4iOiJYN1NaVVRXUFVHTlY3SjRRIn19.iGrZlZz1Qvy6ndTlUriX8ZRAnWeTKhmh1nrFKnizJPg',
      'source': 'agd',
      'oaid': '7af86edfb3481d7e',
    },
  ));

  /// 更新项目在场工友权限
  Future<bool> updateWorkerPermission(int type) async {
    try {
      final result = await _dio.get('/v3/worker/update/show', data: {
        "worker_id": 3174275,
        "is_show": type,
      });
      print("更新项目在场工友权限-$result");
      if (result.statusCode == 200) {
        return true;
      } else {
        print(
            'update-worker-permission请求失败: code:${result.statusCode} msg:${result.statusMessage}');
      }
    } catch (e) {
      throw Exception('update-worker-permission请求出错$e');
    }
    return false;
  }
}
