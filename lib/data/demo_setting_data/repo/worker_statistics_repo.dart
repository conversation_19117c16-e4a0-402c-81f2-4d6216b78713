import 'package:dio/dio.dart';
import 'package:gdjg_pure_flutter/data/demo_setting_data/entity/worker_info/group_worker/group_project_get_group_worker_settle_net_model.dart';
import 'package:gdjg_pure_flutter/data/demo_setting_data/entity/worker_info/worker/workers_get_worker_info_net_model.dart';
import 'package:get/get.dart';

import '../../../feature/group/worker_selector/entity/WorkerListNetModel.dart';

/// 工友统计仓库
class WorkerStatisticsRepo {
  final Dio _dio = Dio(BaseOptions(
    baseUrl: 'http://app-test.cdmgkj.cn',
    connectTimeout: const Duration(seconds: 10),
    receiveTimeout: const Duration(seconds: 10),
    headers: {
      'Content-Type': 'application/json',
      'jgjztoken':
          'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJ0ZXN0LmxvZ2luIiwiaWF0IjoxNzUwNjU4Mjc0LCJ1aWQiOjIwMDkyMjIzfQ.hOVGm9cvU-Gd1XpcicTl1_mCGo70nKq6fAw7W72AFuY',
      'singletoken':
          'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpYXQiOjE3NTA2NTgyNzMsImV4cCI6MTc2MTAyNjI3MywiZGF0YSI6eyJzaW5nbGUiOiJYN1NaVVRXUFVHTlY3SjRRIiwidWlkIjoyMDA5MjIyMywiYnVzaW5lc3MiOiIyIiwic3lzdGVtX3R5cGUiOiJhbmRyb2lkIiwibWluaV90b2tlbiI6Ilg3U1pVVFdQVUdOVjdKNFEiLCJpZCI6MjAwOTIyMjMsInV1aWQiOjIwMDkyMjIzfSwidG9rZW4iOnsicmVnUnQiOiJhbmRyb2lkIiwidGVuYW50S2V5IjoiSkdKWiIsInRlbmFudElkIjoyMDA5MjIyMywicGFja2FnZU5hbWUiOiJjb20ueXVwYW8uZ29uZ2Rpamlnb25nIiwidXNlcklkIjoyMDA5MjIyMywidG9rZW4iOiJYN1NaVVRXUFVHTlY3SjRRIn19.iGrZlZz1Qvy6ndTlUriX8ZRAnWeTKhmh1nrFKnizJPg',
      'uuid': '20092223',
      'uid': '20092223',
      'deviceuuid': '9ff47ff8494c4ec2',
      'imei': '9ff47ff8494c4ec2',
      'env': 'TEST',
      'channel': 'authority',
      'user-agent':
          'YP JGJZ Redmi 22101317C 13 6.7.0 9ff47ff8494c4ec2 1750661270718',
      'business': '2',
      'package_name': 'gdjg',
      'version': '6.7.0',
      'device': 'Redmi,22101317C',
      'versioncode': '670',
      'system_type': 'android',
      'system': 'android',
      'systemversion': '13',
      'apiversion': '3.0',
      'token':
          'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpYXQiOjE3NTA2NTgyNzMsImV4cCI6MTc2MTAyNjI3MywiZGF0YSI6eyJzaW5nbGUiOiJYN1NaVVRXUFVHTlY3SjRRIiwidWlkIjoyMDA5MjIyMywiYnVzaW5lc3MiOiIyIiwic3lzdGVtX3R5cGUiOiJhbmRyb2lkIiwibWluaV90b2tlbiI6Ilg3U1pVVFdQVUdOVjdKNFEiLCJpZCI6MjAwOTIyMjMsInV1aWQiOjIwMDkyMjIzfSwidG9rZW4iOnsicmVnUnQiOiJhbmRyb2lkIiwidGVuYW50S2V5IjoiSkdKWiIsInRlbmFudElkIjoyMDA5MjIyMywicGFja2FnZU5hbWUiOiJjb20ueXVwYW8uZ29uZ2Rpamlnb25nIiwidXNlcklkIjoyMDA5MjIyMywidG9rZW4iOiJYN1NaVVRXUFVHTlY3SjRRIn19.iGrZlZz1Qvy6ndTlUriX8ZRAnWeTKhmh1nrFKnizJPg',
      'source': 'agd',
      'oaid': '7af86edfb3481d7e',
    },
  ));

  /// 获取项目下的工友
  Future<ApiV3WorkerDeptModel?> fetchWorkersInProject() async {
    try {
      final response = await _dio.get('/api/v3/worker/list/dept', data: {
        "dept_id": 41679,
        "is_deleted": 1,
      });
      if (response.statusCode == 200) {
        var respData = ApiV3WorkerDeptNetModel.fromJson(response.data);
        return respData.data;
      } else {
        print(
            'worker/list/dept请求失败: code:${response.statusCode} msg:${response.statusMessage}');
      }
    } catch (e) {
      // 模拟异常情况
      throw Exception("请求出错$e");
    }
    return null;
  }

  /// 获取工友信息
  Future<WorkersGetWorkerInfoAModel?> fetchWorkerInfo() async {
    try {
      final response = await _dio.get('/api/workers/get-worker-info', data: {
        "worker_id": 3174275,
        "dept_id": 41679,
      });
      if (response.statusCode == 200) {
        var respData = WorkersGetWorkerInfoModel.fromJson(response.data);
        return respData.data;
      } else {
        print(
            '请求失败: code:${response.statusCode} msg:${response.statusMessage}');
      }
    } catch (e, stackTrace) {
      print('${e},--------- 请求异常: $stackTrace');
      throw Exception("error");
    }

    return null;
  }

  /// 获取项目工友结算信息
  Future<GroupProjectGetGroupWorkerSettleAModel?>
      fetchGroupWorkerSettle() async {
    try {
      final response = await _dio
          .get('/api/v3/group-project/get-group-worker-settle', data: {
        "work_note": 781307,
        "worker_id": 3174275,
      });
      if (response.statusCode == 200) {
        var respData =
            GroupProjectGetGroupWorkerSettleModel.fromJson(response.data);
        return respData.data;
      } else {
        print(
            '请求失败: code:${response.statusCode} msg:${response.statusMessage}');
      }
    } catch (e) {
      throw Exception('group/project/get-group-worker-settle请求出错$e');
    }
    return null;
  }
}
