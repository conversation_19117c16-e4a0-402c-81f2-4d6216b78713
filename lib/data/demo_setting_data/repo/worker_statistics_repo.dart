import 'package:dio/dio.dart';
import 'package:gdjg_pure_flutter/data/demo_setting_data/entity/worker_info/group_worker/group_project_get_group_worker_settle_net_model.dart';
import 'package:gdjg_pure_flutter/data/demo_setting_data/entity/worker_info/worker/workers_get_worker_info_net_model.dart';

import '../../../feature/group/worker_selector/entity/WorkerListNetModel.dart';

/// 工友统计仓库
class WorkerStatisticsRepo {
  final Dio _dio = Dio(BaseOptions(
    baseUrl: 'http://app-test.cdmgkj.cn',
    connectTimeout: const Duration(seconds: 10),
    receiveTimeout: const Duration(seconds: 10),
    headers: {
      'Content-Type': 'application/json',
      'jgjztoken':
          'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJ0ZXN0LmxvZ2luIiwiaWF0IjoxNzUwNDAzMzY4LCJ1aWQiOjI1NDEzMzg2fQ.bUu2jcAOOBiW5thO4mAQXM_9fewa-HSWbAdFheiKkjc',
      'singletoken':
          'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpYXQiOjE3NTA0MDMzNjgsImV4cCI6MTc2MDc3MTM2OCwiZGF0YSI6eyJzaW5nbGUiOiI4WTM2TktXTUpSR0xIR1U2IiwidWlkIjoyNTQxMzM4NiwiYnVzaW5lc3MiOiIyIiwic3lzdGVtX3R5cGUiOiJhbmRyb2lkIiwibWluaV90b2tlbiI6IjhZMzZOS1dNSlJHTEhHVTYiLCJpZCI6MjU0MTMzODYsInV1aWQiOjI1NDEzMzg2fSwidG9rZW4iOnsicmVnUnQiOiJhbmRyb2lkIiwidGVuYW50S2V5IjoiSkdKWiIsInRlbmFudElkIjoyNTQxMzM4NiwicGFja2FnZU5hbWUiOiJjb20ueXVwYW8uZ29uZ2Rpamlnb25nIiwidXNlcklkIjoyNTQxMzM4NiwidG9rZW4iOiI4WTM2TktXTUpSR0xIR1U2In19.QTpaeGIRbx2Zetg2vyER3QOyLWhPem1dwJPIMUzdeqo',
      'uuid': '25413386',
      'uid': '25413386',
      'deviceuuid': '23e8c035b7e6dbe6',
      'imei': '23e8c035b7e6dbe6',
      'env': 'TEST',
      'channel': 'authority',
      'user-agent':
          'YP JGJZ Redmi M2103K19C 11 6.7.0 23e8c035b7e6dbe6 1750398455075',
      'business': '2',
      'package_name': 'gdjg',
      'version': '6.7.0',
      'device': 'Redmi,M2103K19C',
      'versioncode': '670',
      'system_type': 'android',
      'system': 'android',
      'systemversion': '11',
      'apiversion': '3.0',
      'token':
          'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpYXQiOjE3NTA0MDMzNjgsImV4cCI6MTc2MDc3MTM2OCwiZGF0YSI6eyJzaW5nbGUiOiI4WTM2TktXTUpSR0xIR1U2IiwidWlkIjoyNTQxMzM4NiwiYnVzaW5lc3MiOiIyIiwic3lzdGVtX3R5cGUiOiJhbmRyb2lkIiwibWluaV90b2tlbiI6IjhZMzZOS1dNSlJHTEhHVTYiLCJpZCI6MjU0MTMzODYsInV1aWQiOjI1NDEzMzg2fSwidG9rZW4iOnsicmVnUnQiOiJhbmRyb2lkIiwidGVuYW50S2V5IjoiSkdKWiIsInRlbmFudElkIjoyNTQxMzM4NiwicGFja2FnZU5hbWUiOiJjb20ueXVwYW8uZ29uZ2Rpamlnb25nIiwidXNlcklkIjoyNTQxMzM4NiwidG9rZW4iOiI4WTM2TktXTUpSR0xIR1U2In19.QTpaeGIRbx2Zetg2vyER3QOyLWhPem1dwJPIMUzdeqo',
      'source': 'agd',
      'oaid': '6c0f5ec366a21187',
    },
  ));

  /// 获取项目下的工友
  Future<ApiV3WorkerDeptModel?> fetchWorkersInProject() async {
    try {
      final response = await _dio.get('/api/v3/worker/list/dept', data: {
        "dept_id": 41605,
        "is_deleted": -1,
      });
      if (response.statusCode == 200) {
        var respData = ApiV3WorkerDeptNetModel.fromJson(response.data);
        return respData.data;
      } else {
        print(
            'worker/list/dept请求失败: code:${response.statusCode} msg:${response.statusMessage}');
      }
    } catch (e) {
      // 模拟异常情况
      throw Exception("请求出错$e");
    }
    return null;
  }

  /// 获取工友信息
  Future<WorkersGetWorkerInfoAModel?> fetchWorkerInfo() async {
    try {
      final response = await _dio.get('/api/workers/get-worker-info', data: {
        "worker_id": 3174249,
        "dept_id": 41605,
      });
      if (response.statusCode == 200) {
        var respData = WorkersGetWorkerInfoModel.fromJson(response.data);
        return respData.data;
      } else {
        print(
            '请求失败: code:${response.statusCode} msg:${response.statusMessage}');
      }
    } catch (e) {
      throw Exception('get-worker-info请求出错$e');
    }

    return null;
  }

  /// 获取项目工友结算信息
  Future<GroupProjectGetGroupWorkerSettleAModel?>
      fetchGroupWorkerSettle() async {
    try {
      final response = await _dio
          .get('/api/v3/group-project/get-group-worker-settle', data: {
        "work_note": 781263,
        "worker_id": 3174249,
      });
      if (response.statusCode == 200) {
        var respData =
            GroupProjectGetGroupWorkerSettleModel.fromJson(response.data);
        return respData.data;
      } else {
        print(
            '请求失败: code:${response.statusCode} msg:${response.statusMessage}');
      }
    } catch (e) {
      throw Exception('group/project/get-group-worker-settle请求出错$e');
    }
    return null;
  }
}
