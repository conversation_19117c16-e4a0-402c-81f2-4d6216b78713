// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'workers_get_worker_info_net_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

WorkersGetWorkerInfoModel _$WorkersGetWorkerInfoModelFromJson(
        Map<String, dynamic> json) =>
    WorkersGetWorkerInfoModel()
      ..code = (json['code'] as num?)?.toDouble()
      ..msg = json['msg'] as String?
      ..data = json['data'] == null
          ? null
          : WorkersGetWorkerInfoAModel.fromJson(
              json['data'] as Map<String, dynamic>);

Map<String, dynamic> _$WorkersGetWorkerInfoModelToJson(
        WorkersGetWorkerInfoModel instance) =>
    <String, dynamic>{
      'code': instance.code,
      'msg': instance.msg,
      'data': instance.data,
    };

WorkersGetWorkerInfoAModel _$WorkersGetWorkerInfoAModelFromJson(
        Map<String, dynamic> json) =>
    WorkersGetWorkerInfoAModel()
      ..id = (json['id'] as num?)?.toDouble()
      ..name = json['name'] as String?
      ..tel = json['tel'] as String?
      ..name_py = json['name_py'] as String?
      ..avatar = json['avatar'] as String?
      ..name_color = json['name_color'] as String?
      ..member_id = (json['member_id'] as num?)?.toDouble()
      ..is_deleted = (json['is_deleted'] as num?)?.toDouble()
      ..corp_id = (json['corp_id'] as num?)?.toDouble()
      ..is_bind = (json['is_bind'] as num?)?.toDouble()
      ..username = json['username'] as String?
      ..is_show = (json['is_show'] as num?)?.toDouble()
      ..is_self_created = (json['is_self_created'] as num?)?.toDouble()
      ..is_agent = (json['is_agent'] as num?)?.toDouble()
      ..fee_standard_info = json['fee_standard_info'] == null
          ? null
          : FeeStandardInfoModel.fromJson(
              json['fee_standard_info'] as Map<String, dynamic>)
      ..contractor_fee_standard_info =
          json['contractor_fee_standard_info'] == null
              ? null
              : ContractorFeeStandardInfoModel.fromJson(
                  json['contractor_fee_standard_info'] as Map<String, dynamic>)
      ..grant = json['grant'] == null
          ? null
          : GrantModel.fromJson(json['grant'] as Map<String, dynamic>);

Map<String, dynamic> _$WorkersGetWorkerInfoAModelToJson(
        WorkersGetWorkerInfoAModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'tel': instance.tel,
      'name_py': instance.name_py,
      'avatar': instance.avatar,
      'name_color': instance.name_color,
      'member_id': instance.member_id,
      'is_deleted': instance.is_deleted,
      'corp_id': instance.corp_id,
      'is_bind': instance.is_bind,
      'username': instance.username,
      'is_show': instance.is_show,
      'is_self_created': instance.is_self_created,
      'is_agent': instance.is_agent,
      'fee_standard_info': instance.fee_standard_info,
      'contractor_fee_standard_info': instance.contractor_fee_standard_info,
      'grant': instance.grant,
    };

FeeStandardInfoModel _$FeeStandardInfoModelFromJson(
        Map<String, dynamic> json) =>
    FeeStandardInfoModel()
      ..businessType = (json['businessType'] as num?)?.toDouble()
      ..feeStandardId = (json['feeStandardId'] as num?)?.toDouble()
      ..overtimeHoursPrice = (json['overtimeHoursPrice'] as num?)?.toDouble()
      ..overtimeHoursStandard =
          (json['overtimeHoursStandard'] as num?)?.toDouble()
      ..overtimeType = (json['overtimeType'] as num?)?.toDouble()
      ..workingHoursPrice = (json['workingHoursPrice'] as num?)?.toDouble()
      ..workingHoursStandard =
          (json['workingHoursStandard'] as num?)?.toDouble();

Map<String, dynamic> _$FeeStandardInfoModelToJson(
        FeeStandardInfoModel instance) =>
    <String, dynamic>{
      'businessType': instance.businessType,
      'feeStandardId': instance.feeStandardId,
      'overtimeHoursPrice': instance.overtimeHoursPrice,
      'overtimeHoursStandard': instance.overtimeHoursStandard,
      'overtimeType': instance.overtimeType,
      'workingHoursPrice': instance.workingHoursPrice,
      'workingHoursStandard': instance.workingHoursStandard,
    };

ContractorFeeStandardInfoModel _$ContractorFeeStandardInfoModelFromJson(
        Map<String, dynamic> json) =>
    ContractorFeeStandardInfoModel()
      ..feeStandardId = (json['feeStandardId'] as num?)?.toDouble()
      ..businessType = (json['businessType'] as num?)?.toDouble()
      ..workingHoursPrice = json['workingHoursPrice'] as String?
      ..workingHoursStandard = json['workingHoursStandard'] as String?
      ..overtimeType = (json['overtimeType'] as num?)?.toDouble()
      ..overtimeHoursPrice = json['overtimeHoursPrice'] as String?
      ..overtimeHoursStandard = json['overtimeHoursStandard'] as String?;

Map<String, dynamic> _$ContractorFeeStandardInfoModelToJson(
        ContractorFeeStandardInfoModel instance) =>
    <String, dynamic>{
      'feeStandardId': instance.feeStandardId,
      'businessType': instance.businessType,
      'workingHoursPrice': instance.workingHoursPrice,
      'workingHoursStandard': instance.workingHoursStandard,
      'overtimeType': instance.overtimeType,
      'overtimeHoursPrice': instance.overtimeHoursPrice,
      'overtimeHoursStandard': instance.overtimeHoursStandard,
    };

GrantModel _$GrantModelFromJson(Map<String, dynamic> json) => GrantModel()
  ..bank_name = json['bank_name'] as String?
  ..bank_no = json['bank_no'] as String?
  ..card_name = json['card_name'] as String?
  ..card_no = json['card_no'] as String?
  ..expired_time = json['expired_time'] as String?
  ..grant_id = (json['grant_id'] as num?)?.toDouble();

Map<String, dynamic> _$GrantModelToJson(GrantModel instance) =>
    <String, dynamic>{
      'bank_name': instance.bank_name,
      'bank_no': instance.bank_no,
      'card_name': instance.card_name,
      'card_no': instance.card_no,
      'expired_time': instance.expired_time,
      'grant_id': instance.grant_id,
    };
