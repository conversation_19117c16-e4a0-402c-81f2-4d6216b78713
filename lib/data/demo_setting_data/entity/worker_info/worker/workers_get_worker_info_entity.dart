import 'dart:convert';

import 'package:gdjg_pure_flutter/data/demo_setting_data/entity/worker_info/worker/workers_get_worker_info_net_model.dart';

class WorkersGetWorkerInfoEntity {
  ///
  double code;

  ///
  String msg;

  ///
  final WorkersGetWorkerInfoAEntity? data;

  WorkersGetWorkerInfoEntity({
    this.code = 0.0,
    this.msg = "",
    this.data = null,
  });

  WorkersGetWorkerInfoEntity transform(WorkersGetWorkerInfoModel? model) {
    return WorkersGetWorkerInfoEntity(
      code: model?.code ?? 0.0,
      msg: model?.msg ?? "",
      data: WorkersGetWorkerInfoAEntity().transform(model?.data),
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

/// 工友信息
class WorkersGetWorkerInfoAEntity {
  ///
  double id;

  /// 工人姓名
  String name;

  /// 头像
  String avatar;

  /// 电话
  String tel;

  ///
  String namePy;

  ///
  String nameColor;

  ///
  double memberId;

  ///
  double isDeleted;

  ///
  double corpId;

  /// 是否绑定
  double isBind;

  /// 真实姓名
  String username;

  /// 是否允许在线查看记工数据1-允许 2-不允许
  double isShow;

  /// 是否是班组长 0-不是 1-是
  double isSelfCreated;

  /// 是否是带班 0-不是 1-是
  double isAgent;

  /// 点工工价
  final FeeStandardInfoEntity? feeStandardInfo;

  /// 包工工价
  final ContractorFeeStandardInfoEntity? contractorFeeStandardInfo;

  /// 隐私信息
  final GrantEntity? grant;

  WorkersGetWorkerInfoAEntity({
    this.id = 0.0,
    this.name = "",
    this.avatar = "",
    this.tel = "",
    this.namePy = "",
    this.nameColor = "",
    this.memberId = 0.0,
    this.isDeleted = 0.0,
    this.corpId = 0.0,
    this.isBind = 0.0,
    this.username = "",
    this.isShow = 0.0,
    this.isSelfCreated = 0.0,
    this.isAgent = 0.0,
    this.feeStandardInfo = null,
    this.contractorFeeStandardInfo = null,
    this.grant = null,
  });

  WorkersGetWorkerInfoAEntity transform(WorkersGetWorkerInfoAModel? model) {
    return WorkersGetWorkerInfoAEntity(
      id: model?.id ?? 0.0,
      name: model?.name ?? "",
      avatar: model?.avatar ?? "",
      tel: model?.tel ?? "",
      namePy: model?.name_py ?? "",
      nameColor: model?.name_color ?? "",
      memberId: model?.member_id ?? 0.0,
      isDeleted: model?.is_deleted ?? 0.0,
      corpId: model?.corp_id ?? 0.0,
      isBind: model?.is_bind ?? 0.0,
      username: model?.username ?? "",
      isShow: model?.is_show ?? 0.0,
      isSelfCreated: model?.is_self_created ?? 0.0,
      isAgent: model?.is_agent ?? 0.0,
      feeStandardInfo:
          FeeStandardInfoEntity().transform(model?.fee_standard_info),
      contractorFeeStandardInfo: ContractorFeeStandardInfoEntity()
          .transform(model?.contractor_fee_standard_info),
      grant: GrantEntity().transform(model?.grant),
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }

  String getPortraitName() {
    final nameLength = name.length ?? 0;
    if (nameLength > 2) {
      // 获取最后两个字符
      return name.substring(nameLength - 2);
    }
    return name;
  }
}

class FeeStandardInfoEntity {
  ///
  double businessType;

  ///
  double feeStandardId;

  ///
  String overtimeHoursPrice;

  ///
  String overtimeHoursStandard;

  ///
  double overtimeType;

  ///
  String workingHoursPrice;

  ///
  String workingHoursStandard;

  FeeStandardInfoEntity({
    this.businessType = 0.0,
    this.feeStandardId = 0.0,
    this.overtimeHoursPrice = "",
    this.overtimeHoursStandard = "",
    this.overtimeType = 0.0,
    this.workingHoursPrice = "",
    this.workingHoursStandard = "",
  });

  FeeStandardInfoEntity transform(FeeStandardInfoModel? model) {
    return FeeStandardInfoEntity(
      businessType: model?.businessType ?? 0.0,
      feeStandardId: model?.feeStandardId ?? 0.0,
      overtimeHoursPrice: model?.overtimeHoursPrice?.toString() ?? "",
      overtimeHoursStandard: model?.overtimeHoursStandard?.toString() ?? "",
      overtimeType: model?.overtimeType ?? 0.0,
      workingHoursPrice: model?.workingHoursPrice?.toString() ?? "",
      workingHoursStandard: model?.workingHoursStandard?.toString() ?? "",
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class ContractorFeeStandardInfoEntity {
  ///
  double feeStandardId;

  ///
  double businessType;

  ///
  String workingHoursPrice;

  ///
  String workingHoursStandard;

  ///
  double overtimeType;

  ///
  String overtimeHoursPrice;

  ///
  String overtimeHoursStandard;

  ContractorFeeStandardInfoEntity({
    this.feeStandardId = 0.0,
    this.businessType = 0.0,
    this.workingHoursPrice = "",
    this.workingHoursStandard = "",
    this.overtimeType = 0.0,
    this.overtimeHoursPrice = "",
    this.overtimeHoursStandard = "",
  });

  ContractorFeeStandardInfoEntity transform(
      ContractorFeeStandardInfoModel? model) {
    return ContractorFeeStandardInfoEntity(
      feeStandardId: model?.feeStandardId ?? 0.0,
      businessType: model?.businessType ?? 0.0,
      workingHoursPrice: model?.workingHoursPrice ?? "",
      workingHoursStandard: model?.workingHoursStandard ?? "",
      overtimeType: model?.overtimeType ?? 0.0,
      overtimeHoursPrice: model?.overtimeHoursPrice ?? "",
      overtimeHoursStandard: model?.overtimeHoursStandard ?? "",
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class GrantEntity {
  /// 银行卡名字
  String bankName;

  /// 银行卡卡号
  String bankNo;

  /// 身份证名字
  String cardName;

  /// 身份证号
  String cardNo;

  /// 授权时间
  String expiredTime;

  /// ID
  double grantId;

  GrantEntity({
    this.bankName = "",
    this.bankNo = "",
    this.cardName = "",
    this.cardNo = "",
    this.expiredTime = "",
    this.grantId = 0.0,
  });

  GrantEntity transform(GrantModel? model) {
    return GrantEntity(
      bankName: model?.bank_name ?? "",
      bankNo: model?.bank_no ?? "",
      cardName: model?.card_name ?? "",
      cardNo: model?.card_no ?? "",
      expiredTime: model?.expired_time ?? "",
      grantId: model?.grant_id ?? 0.0,
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}
