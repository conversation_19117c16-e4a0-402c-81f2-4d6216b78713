import 'dart:convert';

import 'package:json_annotation/json_annotation.dart';

part 'workers_get_worker_info_net_model.g.dart';

@JsonSerializable()
class WorkersGetWorkerInfoModel {
  /// 
  double? code;
  /// 
  String? msg;
  /// 
  WorkersGetWorkerInfoAModel? data;

  WorkersGetWorkerInfoModel();

  factory WorkersGetWorkerInfoModel.fromJson(Map<String, dynamic> json) => _$WorkersGetWorkerInfoModelFromJson(json);

  Map<String, dynamic> toJson() => _$WorkersGetWorkerInfoModelToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class WorkersGetWorkerInfoAModel {
  /// 
  double? id;
  /// 工人姓名
  String? name;
  /// 电话
  String? tel;
  /// 
  String? name_py;
  /// 头像
  String? avatar;
  ///
  String? name_color;
  /// 
  double? member_id;
  /// 
  double? is_deleted;
  /// 
  double? corp_id;
  /// 是否绑定
  double? is_bind;
  /// 真实姓名
  String? username;
  /// 是否允许在线查看记工数据1-允许 2-不允许
  double? is_show;
  /// 是否是班组长 0-不是 1-是
  double? is_self_created;
  /// 是否是带班 0-不是 1-是
  double? is_agent;
  /// 
  FeeStandardInfoModel? fee_standard_info;
  /// 
  ContractorFeeStandardInfoModel? contractor_fee_standard_info;
  /// 隐私信息
  GrantModel? grant;

  WorkersGetWorkerInfoAModel();

  factory WorkersGetWorkerInfoAModel.fromJson(Map<String, dynamic> json) => _$WorkersGetWorkerInfoAModelFromJson(json);

  Map<String, dynamic> toJson() => _$WorkersGetWorkerInfoAModelToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class FeeStandardInfoModel {
  /// 
  double? businessType;
  /// 
  double? feeStandardId;
  /// 
  double? overtimeHoursPrice;
  /// 
  double? overtimeHoursStandard;
  /// 
  double? overtimeType;
  /// 
  double? workingHoursPrice;
  /// 
  double? workingHoursStandard;

  FeeStandardInfoModel();

  factory FeeStandardInfoModel.fromJson(Map<String, dynamic> json) => _$FeeStandardInfoModelFromJson(json);

  Map<String, dynamic> toJson() => _$FeeStandardInfoModelToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class ContractorFeeStandardInfoModel {
  /// 
  double? feeStandardId;
  /// 
  double? businessType;
  /// 
  String? workingHoursPrice;
  /// 
  String? workingHoursStandard;
  /// 
  double? overtimeType;
  /// 
  String? overtimeHoursPrice;
  /// 
  String? overtimeHoursStandard;

  ContractorFeeStandardInfoModel();

  factory ContractorFeeStandardInfoModel.fromJson(Map<String, dynamic> json) => _$ContractorFeeStandardInfoModelFromJson(json);

  Map<String, dynamic> toJson() => _$ContractorFeeStandardInfoModelToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class GrantModel {
  /// 银行卡名字
  String? bank_name;
  /// 银行卡卡号
  String? bank_no;
  /// 身份证名字
  String? card_name;
  /// 身份证号
  String? card_no;
  /// 授权时间
  String? expired_time;
  /// ID
  double? grant_id;

  GrantModel();

  factory GrantModel.fromJson(Map<String, dynamic> json) => _$GrantModelFromJson(json);

  Map<String, dynamic> toJson() => _$GrantModelToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

