import 'dart:convert';

class BusinessGetIndexBusinessListBizModel {
  ///
  List<BusinessGetIndexBusinessListABizModel> list;

  BusinessGetIndexBusinessListBizModel({
    this.list = const [],
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class BusinessGetIndexBusinessListABizModel {
  ///
  String date;

  ///
  List<BusinessGetIndexBusinessListBBizModel> list;

  BusinessGetIndexBusinessListABizModel({
    this.date = "",
    this.list = const [],
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class BusinessGetIndexBusinessListBBizModel {
  ///
  double id;

  ///
  double businessType;

  ///
  String businessTime;

  ///
  double workNote;

  ///
  double workerId;

  ///
  double unitWorkType;

  ///
  double workTime;

  ///
  double workTimeHour;

  ///
  double overtime;

  ///
  double overtimeWork;

  ///
  String morningWorkTime;

  ///
  String morningWorkTimeHour;

  ///
  String afternoonWorkTime;

  ///
  String afternoonWorkTimeHour;

  ///
  double userChooseSpotwork;

  ///
  String unitNum;

  ///
  String unitPrice;

  /// 4.0以前旧版本工量单位 4.0后废弃
  String unit;

  ///
  String note;

  ///
  double feeMoney;

  ///
  double createdTime;

  ///
  String workNoteName;

  ///
  String workerName;

  ///
  String money;

  ///
  double feeStandardId;

  ///
  double hasImg;

  /// 工量 分项名称
  String unitWorkTypeName;

  /// 工量 分项单位
  String unitWorkTypeUnit;

  /// 仅[5.0.0]及以后 图片信息
  List<ImgInfoBizModel> imgInfo;

  /// 其他费用
  final OtherExpensesBizModel? otherExpenses;

  BusinessGetIndexBusinessListBBizModel({
    this.id = 0.0,
    this.businessType = 0.0,
    this.businessTime = "",
    this.workNote = 0.0,
    this.workerId = 0.0,
    this.unitWorkType = 0.0,
    this.workTime = 0.0,
    this.workTimeHour = 0.0,
    this.overtime = 0.0,
    this.overtimeWork = 0.0,
    this.morningWorkTime = "",
    this.morningWorkTimeHour = "",
    this.afternoonWorkTime = "",
    this.afternoonWorkTimeHour = "",
    this.userChooseSpotwork = 0.0,
    this.unitNum = "",
    this.unitPrice = "",
    this.unit = "",
    this.note = "",
    this.feeMoney = 0.0,
    this.createdTime = 0.0,
    this.workNoteName = "",
    this.workerName = "",
    this.money = "",
    this.feeStandardId = 0.0,
    this.hasImg = 0.0,
    this.unitWorkTypeName = "",
    this.unitWorkTypeUnit = "",
    this.imgInfo = const [],
    this.otherExpenses = null,
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class ImgInfoBizModel {
  /// 图片完整路径
  String url;
  String type;
  String baseUrl;
  String cover;

  ImgInfoBizModel({
    this.url = "",
    this.type = "",
    this.baseUrl = "",
    this.cover = "",
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class OtherExpensesBizModel {
  /// 主键id
  String id;

  /// 记工表iD
  String bkBusinessId;

  /// 其他费用ID
  String bkOtherExpensesId;

  /// 记工用户ID
  String memberId;

  /// 鱼泡ID
  String yupaoId;

  /// 费用名称
  String name;

  OtherExpensesBizModel({
    this.id = "",
    this.bkBusinessId = "",
    this.bkOtherExpensesId = "",
    this.memberId = "",
    this.yupaoId = "",
    this.name = "",
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}
