// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'business_get_index_business_list_net_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

BusinessGetIndexBusinessListNetModel _$BusinessGetIndexBusinessListNetModelFromJson(
        Map<String, dynamic> json) =>
    BusinessGetIndexBusinessListNetModel()
      ..list = (json['list'] as List<dynamic>?)
          ?.map((e) => BusinessGetIndexBusinessListANetModel.fromJson(
              e as Map<String, dynamic>))
          .toList();

Map<String, dynamic> _$BusinessGetIndexBusinessListNetModelToJson(
        BusinessGetIndexBusinessListNetModel instance) =>
    <String, dynamic>{
      'list': instance.list,
    };

BusinessGetIndexBusinessListANetModel _$BusinessGetIndexBusinessListANetModelFromJson(
        Map<String, dynamic> json) =>
    BusinessGetIndexBusinessListANetModel()
      ..date = json['date'] as String?
      ..list = (json['list'] as List<dynamic>?)
          ?.map((e) => BusinessGetIndexBusinessListBNetModel.fromJson(
              e as Map<String, dynamic>))
          .toList();

Map<String, dynamic> _$BusinessGetIndexBusinessListANetModelToJson(
        BusinessGetIndexBusinessListANetModel instance) =>
    <String, dynamic>{
      'date': instance.date,
      'list': instance.list,
    };

BusinessGetIndexBusinessListBNetModel _$BusinessGetIndexBusinessListBNetModelFromJson(
        Map<String, dynamic> json) =>
    BusinessGetIndexBusinessListBNetModel()
      ..id = (json['id'] as num?)?.toDouble()
      ..business_type = (json['business_type'] as num?)?.toDouble()
      ..business_time = json['business_time'] as String?
      ..work_note = (json['work_note'] as num?)?.toDouble()
      ..worker_id = (json['worker_id'] as num?)?.toDouble()
      ..unit_work_type = (json['unit_work_type'] as num?)?.toDouble()
      ..work_time = (json['work_time'] as num?)?.toDouble()
      ..work_time_hour = (json['work_time_hour'] as num?)?.toDouble()
      ..overtime = (json['overtime'] as num?)?.toDouble()
      ..overtime_work = (json['overtime_work'] as num?)?.toDouble()
      ..morning_work_time = json['morning_work_time'] as String?
      ..morning_work_time_hour = json['morning_work_time_hour'] as String?
      ..afternoon_work_time = json['afternoon_work_time'] as String?
      ..afternoon_work_time_hour = json['afternoon_work_time_hour'] as String?
      ..user_choose_spotwork =
          (json['user_choose_spotwork'] as num?)?.toDouble()
      ..unit_num = json['unit_num'] as String?
      ..unit_price = json['unit_price'] as String?
      ..unit = json['unit'] as String?
      ..note = json['note'] as String?
      ..fee_money = (json['fee_money'] as num?)?.toDouble()
      ..created_time = (json['created_time'] as num?)?.toDouble()
      ..work_note_name = json['work_note_name'] as String?
      ..worker_name = json['worker_name'] as String?
      ..money = json['money'] as String?
      ..fee_standard_id = (json['fee_standard_id'] as num?)?.toDouble()
      ..has_img = (json['has_img'] as num?)?.toDouble()
      ..unit_work_type_name = json['unit_work_type_name'] as String?
      ..unit_work_type_unit = json['unit_work_type_unit'] as String?
      ..img_info = (json['img_info'] as List<dynamic>?)
          ?.map((e) => ImgInfoNetModel.fromJson(e as Map<String, dynamic>))
          .toList()
      ..other_expenses = json['other_expenses'] == null
          ? null
          : OtherExpensesNetModel.fromJson(
              json['other_expenses'] as Map<String, dynamic>);

Map<String, dynamic> _$BusinessGetIndexBusinessListBNetModelToJson(
        BusinessGetIndexBusinessListBNetModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'business_type': instance.business_type,
      'business_time': instance.business_time,
      'work_note': instance.work_note,
      'worker_id': instance.worker_id,
      'unit_work_type': instance.unit_work_type,
      'work_time': instance.work_time,
      'work_time_hour': instance.work_time_hour,
      'overtime': instance.overtime,
      'overtime_work': instance.overtime_work,
      'morning_work_time': instance.morning_work_time,
      'morning_work_time_hour': instance.morning_work_time_hour,
      'afternoon_work_time': instance.afternoon_work_time,
      'afternoon_work_time_hour': instance.afternoon_work_time_hour,
      'user_choose_spotwork': instance.user_choose_spotwork,
      'unit_num': instance.unit_num,
      'unit_price': instance.unit_price,
      'unit': instance.unit,
      'note': instance.note,
      'fee_money': instance.fee_money,
      'created_time': instance.created_time,
      'work_note_name': instance.work_note_name,
      'worker_name': instance.worker_name,
      'money': instance.money,
      'fee_standard_id': instance.fee_standard_id,
      'has_img': instance.has_img,
      'unit_work_type_name': instance.unit_work_type_name,
      'unit_work_type_unit': instance.unit_work_type_unit,
      'img_info': instance.img_info,
      'other_expenses': instance.other_expenses,
    };

ImgInfoNetModel _$ImgInfoNetModelFromJson(Map<String, dynamic> json) => ImgInfoNetModel()
  ..url = json['url'] as String?
  ..type = json['type'] as String?
  ..base_url = json['base_url'] as String?
  ..cover = json['cover'] as String?;

Map<String, dynamic> _$ImgInfoNetModelToJson(ImgInfoNetModel instance) =>
    <String, dynamic>{
      'url': instance.url,
      'type': instance.type,
      'base_url': instance.base_url,
      'cover': instance.cover,
    };

OtherExpensesNetModel _$OtherExpensesNetModelFromJson(Map<String, dynamic> json) =>
    OtherExpensesNetModel()
      ..id = json['id'] as String?
      ..bk_business_id = json['bk_business_id'] as String?
      ..bk_other_expenses_id = json['bk_other_expenses_id'] as String?
      ..member_id = json['member_id'] as String?
      ..yupao_id = json['yupao_id'] as String?
      ..name = json['name'] as String?;

Map<String, dynamic> _$OtherExpensesNetModelToJson(OtherExpensesNetModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'bk_business_id': instance.bk_business_id,
      'bk_other_expenses_id': instance.bk_other_expenses_id,
      'member_id': instance.member_id,
      'yupao_id': instance.yupao_id,
      'name': instance.name,
    };
