class BusinessGetIndexBusinessCountParamModel {

  /// 1 班组流水，2个人流水，默认为2 
  final String identity;

  /// 有默认值，默认为当前月 
  final String start_time;

  /// 有默认值，默认为当前月 
  final String end_time;

  /// 记工类型 多选, 隔开 
  final String business_type;

  /// 记工本id, 默认全部记工本 
  final String work_notes;

  /// 点工统计展示  1 上班工天，加班小时 2 上班加班 工天 3 上班加班小时
  final String show_type;

  /// 项目 1 结清 0未结
  final String status;

  /// 班组项目用
  final String worker_ids;

   BusinessGetIndexBusinessCountParamModel({
     this.identity = "",
     this.start_time = "",
     this.end_time = "",
     this.business_type = "",
     this.work_notes = "",
     this.show_type = "",
     this.status = "",
     this.worker_ids = "",
  });
}

