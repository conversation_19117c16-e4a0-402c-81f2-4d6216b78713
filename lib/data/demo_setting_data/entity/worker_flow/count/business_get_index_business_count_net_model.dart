import 'dart:convert';
import 'package:json_annotation/json_annotation.dart';
import 'business_get_index_business_count_biz_model.dart';

part 'business_get_index_business_count_net_model.g.dart';

@JsonSerializable()
class BusinessGetIndexBusinessCountNetModel {
  /// 点工
  SpotWorkNetModel? spot_work;

  /// 工量
  UnitNetModel? unit;

  /// 记工钱/短工
  WorkMoneyNetModel? work_money;

  /// 借支
  BorrowNetModel? borrow;

  /// 包工
  ContractorNetModel? contractor;

  /// 工资
  WageNetModel? wage;

  ///
  HourNetModel? hour;

  List<OtherExpensesCountNetModel>? other_expenses;

  /// [4.9]统计汇总
  SummaryNetModel? summary;

  ///
  double? unsettled;

  ///
  double? income;

  int? num;

  BusinessGetIndexBusinessCountNetModel();

  factory BusinessGetIndexBusinessCountNetModel.fromJson(Map<String, dynamic> json) =>
      _$BusinessGetIndexBusinessCountNetModelFromJson(json);

  Map<String, dynamic> toJson() => _$BusinessGetIndexBusinessCountNetModelToJson(this);

  BusinessCountBizModel transform() {
    return BusinessCountBizModel(
      spotWork: spot_work?.transform(),
      unit: unit?.transform(),
      workMoney: work_money?.transform(),
      borrow: borrow?.transform(),
      contractor: contractor?.transform(),
      wage: wage?.transform(),
      hour: hour?.transform(),
      otherExpenses: other_expenses?.map((e) => e.transform()).toList() ?? [],
      summary: summary?.transform(),
      unsettled: unsettled ?? 0.0,
      income: income ?? 0.0,
      num: num ?? 0,
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class SpotWorkNetModel {
  /// 上班工时
  String? work_time;

  /// 上班小时
  String? work_time_hour;

  /// 加班 小时
  String? overtime;

  /// 加班工
  String? overtime_work;

  /// 点工工钱
  double? spot_work_fee_money;

  /// 数量
  double? num;

  SpotWorkNetModel();

  factory SpotWorkNetModel.fromJson(Map<String, dynamic> json) => _$SpotWorkNetModelFromJson(json);

  Map<String, dynamic> toJson() => _$SpotWorkNetModelToJson(this);

  SpotWorkBizModel transform() {
    return SpotWorkBizModel(
      workTime: work_time ?? "",
      workTimeHour: work_time_hour ?? "",
      overtime: overtime ?? "",
      overtimeWork: overtime_work ?? "",
      spotWorkFeeMoney: spot_work_fee_money ?? 0.0,
      num: num ?? 0.0,
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class UnitNetModel {
  /// 工量详情 按分项id汇总
  List<CountUnitNetModel>? count_unit;

  /// 记工笔数
  double? num;

  /// 工量工钱
  String? unit_money;

  UnitNetModel();

  factory UnitNetModel.fromJson(Map<String, dynamic> json) => _$UnitNetModelFromJson(json);

  Map<String, dynamic> toJson() => _$UnitNetModelToJson(this);

  UnitBizModel transform() {
    return UnitBizModel(
      countUnit: count_unit?.map((e) => e.transform()).toList() ?? [],
      num: num ?? 0.0,
      unitMoney: unit_money ?? "",
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class CountUnitNetModel {
  /// 分项id
  double? unit_work_type;

  /// 分项名
  String? unit_work_type_name;

  /// 分项单位
  String? unit_work_type_unit;

  /// 4.0以前旧版单位 4.0后废弃
  String? unit;

  /// 工程量
  String? count;

  /// 工钱
  String? unit_money;

  /// 记工笔数
  int? num;

  ///
  String? last_unit_price;

  CountUnitNetModel();

  factory CountUnitNetModel.fromJson(Map<String, dynamic> json) =>
      _$CountUnitNetModelFromJson(json);

  Map<String, dynamic> toJson() => _$CountUnitNetModelToJson(this);

  CountUnitBizModel transform() {
    return CountUnitBizModel(
      unitWorkType: unit_work_type ?? 0.0,
      unitWorkTypeName: unit_work_type_name ?? "",
      unitWorkTypeUnit: unit_work_type_unit ?? "",
      unit: unit ?? "",
      count: count ?? "",
      unitMoney: unit_money ?? "",
      num: num ?? 0,
      lastUnitPrice: last_unit_price ?? "",
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class WorkMoneyNetModel {
  ///
  String? work_money;

  ///
  int? num;

  WorkMoneyNetModel();

  factory WorkMoneyNetModel.fromJson(Map<String, dynamic> json) =>
      _$WorkMoneyNetModelFromJson(json);

  Map<String, dynamic> toJson() => _$WorkMoneyNetModelToJson(this);

  WorkMoneyBizModel transform() {
    return WorkMoneyBizModel(
      workMoney: work_money ?? "",
      num: num ?? 0,
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class BorrowNetModel {
  ///
  double? borrow_count;

  ///
  int? num;

  BorrowNetModel();

  factory BorrowNetModel.fromJson(Map<String, dynamic> json) => _$BorrowNetModelFromJson(json);

  Map<String, dynamic> toJson() => _$BorrowNetModelToJson(this);

  BorrowBizModel transform() {
    return BorrowBizModel(
      borrowCount: borrow_count ?? 0.0,
      num: num ?? 0,
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class ContractorNetModel {
  ///
  String? contractor_work_time;

  ///
  String? contractor_work_time_hour;

  ///
  int? num;

  /// 包工钱
  double? contractor_money;

  ///
  String? contractor_overtime;

  ContractorNetModel();

  factory ContractorNetModel.fromJson(Map<String, dynamic> json) =>
      _$ContractorNetModelFromJson(json);

  Map<String, dynamic> toJson() => _$ContractorNetModelToJson(this);

  ContractorBizModel transform() {
    return ContractorBizModel(
      contractorWorkTime: contractor_work_time ?? "",
      contractorWorkTimeHour: contractor_work_time_hour ?? "",
      num: num ?? 0,
      contractorMoney: contractor_money ?? 0.0,
      contractorOvertime: contractor_overtime ?? "",
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class WageNetModel {
  ///
  double? wage_count;

  ///
  int? num;

  WageNetModel();

  factory WageNetModel.fromJson(Map<String, dynamic> json) => _$WageNetModelFromJson(json);

  Map<String, dynamic> toJson() => _$WageNetModelToJson(this);

  WageBizModel transform() {
    return WageBizModel(
      wageCount: wage_count ?? 0.0,
      num: num ?? 0,
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class HourNetModel {
  /// 小时工
  double? hour_time;

  ///
  double? hour_overtime;

  ///
  double? num;

  HourNetModel();

  factory HourNetModel.fromJson(Map<String, dynamic> json) => _$HourNetModelFromJson(json);

  Map<String, dynamic> toJson() => _$HourNetModelToJson(this);

  HourBizModel transform() {
    return HourBizModel(
      hourTime: hour_time ?? 0.0,
      hourOvertime: hour_overtime ?? 0.0,
      num: num ?? 0.0,
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class OtherExpensesCountNetModel {
  ///
  String? money;
  String? real_money;
  int? count;
  int? bk_other_expenses_id;
  String? name;
  int? num;

  OtherExpensesCountNetModel();

  factory OtherExpensesCountNetModel.fromJson(Map<String, dynamic> json) =>
      _$OtherExpensesCountNetModelFromJson(json);

  Map<String, dynamic> toJson() => _$OtherExpensesCountNetModelToJson(this);

  OtherExpensesCountBizModel transform() {
    return OtherExpensesCountBizModel(
      money: money ?? "",
      realMoney: real_money ?? "",
      count: count ?? 0,
      bkOtherExpensesId: bk_other_expenses_id ?? 0,
      name: name ?? "",
      num: num ?? 0,
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class SummaryNetModel {
  /// 上班工时
  String? work_time;

  /// 上班小时
  String? work_hour;

  /// 加班工时
  double? overtime_work;

  /// 加班小时
  String? overtime;

  /// 借支金额
  String? borrow;

  /// 结算金额
  String? wage;

  SummaryNetModel();

  factory SummaryNetModel.fromJson(Map<String, dynamic> json) => _$SummaryNetModelFromJson(json);

  Map<String, dynamic> toJson() => _$SummaryNetModelToJson(this);

  SummaryBizModel transform() {
    return SummaryBizModel(
      workTime: work_time ?? "",
      workHour: work_hour ?? "",
      overtimeWork: overtime_work ?? 0.0,
      overtime: overtime ?? "",
      borrow: borrow ?? "",
      wage: wage ?? "",
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}
