// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'business_get_index_business_count_net_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

BusinessGetIndexBusinessCountNetModel _$BusinessGetIndexBusinessCountNetModelFromJson(
        Map<String, dynamic> json) =>
    BusinessGetIndexBusinessCountNetModel()
      ..spot_work = json['spot_work'] == null
          ? null
          : SpotWorkNetModel.fromJson(json['spot_work'] as Map<String, dynamic>)
      ..unit =
          json['unit'] == null ? null : UnitNetModel.fromJson(json['unit'] as Map<String, dynamic>)
      ..work_money = json['work_money'] == null
          ? null
          : WorkMoneyNetModel.fromJson(json['work_money'] as Map<String, dynamic>)
      ..borrow = json['borrow'] == null
          ? null
          : BorrowNetModel.fromJson(json['borrow'] as Map<String, dynamic>)
      ..contractor = json['contractor'] == null
          ? null
          : ContractorNetModel.fromJson(json['contractor'] as Map<String, dynamic>)
      ..wage =
          json['wage'] == null ? null : WageNetModel.fromJson(json['wage'] as Map<String, dynamic>)
      ..hour =
          json['hour'] == null ? null : HourNetModel.fromJson(json['hour'] as Map<String, dynamic>)
      ..other_expenses = (json['other_expenses'] as List<dynamic>?)
          ?.map((e) => OtherExpensesCountNetModel.fromJson(e as Map<String, dynamic>))
          .toList()
      ..summary = json['summary'] == null
          ? null
          : SummaryNetModel.fromJson(json['summary'] as Map<String, dynamic>)
      ..unsettled = (json['unsettled'] as num?)?.toDouble()
      ..income = (json['income'] as num?)?.toDouble();

Map<String, dynamic> _$BusinessGetIndexBusinessCountNetModelToJson(
        BusinessGetIndexBusinessCountNetModel instance) =>
    <String, dynamic>{
      'spot_work': instance.spot_work,
      'unit': instance.unit,
      'work_money': instance.work_money,
      'borrow': instance.borrow,
      'contractor': instance.contractor,
      'wage': instance.wage,
      'hour': instance.hour,
      'summary': instance.summary,
      'unsettled': instance.unsettled,
      'income': instance.income,
    };

SpotWorkNetModel _$SpotWorkNetModelFromJson(Map<String, dynamic> json) => SpotWorkNetModel()
  ..work_time = json['work_time'] as String?
  ..work_time_hour = json['work_time_hour'] as String?
  ..overtime = json['overtime'] as String?
  ..overtime_work = json['overtime_work'] as String?
  ..spot_work_fee_money = (json['spot_work_fee_money'] as num?)?.toDouble()
  ..num = (json['num'] as num?)?.toDouble();

Map<String, dynamic> _$SpotWorkNetModelToJson(SpotWorkNetModel instance) => <String, dynamic>{
      'work_time': instance.work_time,
      'work_time_hour': instance.work_time_hour,
      'overtime': instance.overtime,
      'overtime_work': instance.overtime_work,
      'spot_work_fee_money': instance.spot_work_fee_money,
      'num': instance.num,
    };

UnitNetModel _$UnitNetModelFromJson(Map<String, dynamic> json) => UnitNetModel()
  ..count_unit = (json['count_unit'] as List<dynamic>?)
      ?.map((e) => CountUnitNetModel.fromJson(e as Map<String, dynamic>))
      .toList()
  ..num = (json['num'] as num?)?.toDouble()
  ..unit_money = json['unit_money'] as String;

Map<String, dynamic> _$UnitNetModelToJson(UnitNetModel instance) => <String, dynamic>{
      'count_unit': instance.count_unit,
      'num': instance.num,
      'unit_money': instance.unit_money,
    };

CountUnitNetModel _$CountUnitNetModelFromJson(Map<String, dynamic> json) => CountUnitNetModel()
  ..unit_work_type = (json['unit_work_type'] as num?)?.toDouble()
  ..unit_work_type_name = json['unit_work_type_name'] as String?
  ..unit_work_type_unit = json['unit_work_type_unit'] as String?
  ..unit = json['unit'] as String?
  ..count = json['count'] as String?
  ..unit_money = json['unit_money'] as String?
  ..num = (json['num'] as num?)?.toInt()
  ..last_unit_price = json['last_unit_price'] as String?;

Map<String, dynamic> _$CountUnitNetModelToJson(CountUnitNetModel instance) => <String, dynamic>{
      'unit_work_type': instance.unit_work_type,
      'unit_work_type_name': instance.unit_work_type_name,
      'unit_work_type_unit': instance.unit_work_type_unit,
      'unit': instance.unit,
      'count': instance.count,
      'unit_money': instance.unit_money,
      'num': instance.num,
      'last_unit_price': instance.last_unit_price,
    };

WorkMoneyNetModel _$WorkMoneyNetModelFromJson(Map<String, dynamic> json) => WorkMoneyNetModel()
  ..work_money = json['work_money'] as String?
  ..num = (json['num'] as num?)?.toInt();

Map<String, dynamic> _$WorkMoneyNetModelToJson(WorkMoneyNetModel instance) => <String, dynamic>{
      'work_money': instance.work_money,
      'num': instance.num,
    };

BorrowNetModel _$BorrowNetModelFromJson(Map<String, dynamic> json) => BorrowNetModel()
  ..borrow_count = (json['borrow_count'] as num?)?.toDouble()
  ..num = (json['num'] as num?)?.toInt();

Map<String, dynamic> _$BorrowNetModelToJson(BorrowNetModel instance) => <String, dynamic>{
      'borrow_count': instance.borrow_count,
      'num': instance.num,
    };

ContractorNetModel _$ContractorNetModelFromJson(Map<String, dynamic> json) => ContractorNetModel()
  ..contractor_work_time = json['contractor_work_time'] as String?
  ..contractor_work_time_hour = json['contractor_work_time_hour'] as String?
  ..num = (json['num'] as num?)?.toInt()
  ..contractor_money = (json['contractor_money'] as num?)?.toDouble()
  ..contractor_overtime = json['contractor_overtime'] as String?;

Map<String, dynamic> _$ContractorNetModelToJson(ContractorNetModel instance) => <String, dynamic>{
      'contractor_work_time': instance.contractor_work_time,
      'contractor_work_time_hour': instance.contractor_work_time_hour,
      'num': instance.num,
      'contractor_money': instance.contractor_money,
      'contractor_overtime': instance.contractor_overtime,
    };

WageNetModel _$WageNetModelFromJson(Map<String, dynamic> json) => WageNetModel()
  ..wage_count = (json['wage_count'] as num?)?.toDouble()
  ..num = (json['num'] as num?)?.toInt();

Map<String, dynamic> _$WageNetModelToJson(WageNetModel instance) => <String, dynamic>{
      'wage_count': instance.wage_count,
      'num': instance.num,
    };

HourNetModel _$HourNetModelFromJson(Map<String, dynamic> json) => HourNetModel()
  ..hour_time = (json['hour_time'] as num?)?.toDouble()
  ..hour_overtime = (json['hour_overtime'] as num?)?.toDouble()
  ..num = (json['num'] as num?)?.toDouble();

Map<String, dynamic> _$HourNetModelToJson(HourNetModel instance) => <String, dynamic>{
      'hour_time': instance.hour_time,
      'hour_overtime': instance.hour_overtime,
      'num': instance.num,
    };

OtherExpensesCountNetModel _$OtherExpensesCountNetModelFromJson(Map<String, dynamic> json) =>
    OtherExpensesCountNetModel()
      ..money = json['money'] as String?
      ..real_money = json['real_money'] as String?
      ..count = json['count'] as int?
      ..bk_other_expenses_id = json['bk_other_expenses_id'] as int?
      ..name = json['name'] as String?
      ..num = json['num'] as int?;

Map<String, dynamic> _$OtherExpensesCountNetModelToJson(OtherExpensesCountNetModel instance) =>
    <String, dynamic>{
      'money': instance.money,
      'real_money': instance.real_money,
      'count': instance.count,
      'bk_other_expenses_id': instance.bk_other_expenses_id,
      'name': instance.name,
      'num': instance.num,
    };

SummaryNetModel _$SummaryNetModelFromJson(Map<String, dynamic> json) => SummaryNetModel()
  ..work_time = json['work_time'] as String?
  ..work_hour = json['work_hour'] as String?
  ..overtime_work = (json['overtime_work'] as num?)?.toDouble()
  ..overtime = json['overtime'] as String?
  ..borrow = json['borrow'] as String?
  ..wage = json['wage'] as String?;

Map<String, dynamic> _$SummaryNetModelToJson(SummaryNetModel instance) => <String, dynamic>{
      'work_time': instance.work_time,
      'work_hour': instance.work_hour,
      'overtime_work': instance.overtime_work,
      'overtime': instance.overtime,
      'borrow': instance.borrow,
      'wage': instance.wage,
    };
