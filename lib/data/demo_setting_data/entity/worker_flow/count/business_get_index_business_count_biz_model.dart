import 'dart:convert';

class BusinessCountBizModel {
  /// 点工
  final SpotWorkBizModel? spotWork;

  /// 工量
  final UnitBizModel? unit;

  /// 记工钱/短工
  final WorkMoneyBizModel? workMoney;

  /// 借支
  final BorrowBizModel? borrow;

  /// 包工
  final ContractorBizModel? contractor;

  /// 工资
  final WageBizModel? wage;

  ///
  final HourBizModel? hour;

  ///
  final List<OtherExpensesCountBizModel> otherExpenses;

  /// [4.9]统计汇总
  final SummaryBizModel? summary;

  ///
  double unsettled;

  ///
  double income;

  int num;

  BusinessCountBizModel({
    this.spotWork,
    this.unit,
    this.workMoney,
    this.borrow,
    this.contractor,
    this.wage,
    this.hour,
    this.otherExpenses = const [],
    this.summary,
    this.unsettled = 0.0,
    this.income = 0.0,
    this.num = 0,
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class SpotWorkBizModel {
  /// 上班工时
  String workTime;

  /// 上班小时
  String workTimeHour;

  /// 加班 小时
  String overtime;

  /// 加班工
  String overtimeWork;

  /// 点工工钱
  double spotWorkFeeMoney;

  /// 数量
  double num;

  SpotWorkBizModel({
    this.workTime = "",
    this.workTimeHour = "",
    this.overtime = "",
    this.overtimeWork = "",
    this.spotWorkFeeMoney = 0.0,
    this.num = 0.0,
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class UnitBizModel {
  /// 工量详情 按分项id汇总
  List<CountUnitBizModel> countUnit;

  /// 记工笔数
  double num;

  /// 工量工钱
  String unitMoney;

  UnitBizModel({
    this.countUnit = const [],
    this.num = 0.0,
    this.unitMoney = "",
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class CountUnitBizModel {
  /// 分项id
  double unitWorkType;

  /// 分项名
  String unitWorkTypeName;

  /// 分项单位
  String unitWorkTypeUnit;

  /// 4.0以前旧版单位 4.0后废弃
  String unit;

  /// 工程量
  String count;

  /// 工钱
  String unitMoney;

  /// 记工笔数
  int num;

  ///
  String lastUnitPrice;

  CountUnitBizModel({
    this.unitWorkType = 0.0,
    this.unitWorkTypeName = "",
    this.unitWorkTypeUnit = "",
    this.unit = "",
    this.count = "",
    this.unitMoney = "",
    this.num = 0,
    this.lastUnitPrice = "",
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class WorkMoneyBizModel {
  ///
  String workMoney;

  ///
  int num;

  WorkMoneyBizModel({
    this.workMoney = "",
    this.num = 0,
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class BorrowBizModel {
  ///
  double borrowCount;

  ///
  int num;

  BorrowBizModel({
    this.borrowCount = 0.0,
    this.num = 0,
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class ContractorBizModel {
  ///
  String contractorWorkTime;

  ///
  String contractorWorkTimeHour;

  ///
  int num;

  /// 包工钱
  double contractorMoney;

  ///
  String contractorOvertime;

  ContractorBizModel({
    this.contractorWorkTime = "",
    this.contractorWorkTimeHour = "",
    this.num = 0,
    this.contractorMoney = 0.0,
    this.contractorOvertime = "",
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class WageBizModel {
  ///
  double wageCount;

  ///
  int num;

  WageBizModel({
    this.wageCount = 0.0,
    this.num = 0,
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class HourBizModel {
  /// 小时工
  double hourTime;

  ///
  double hourOvertime;

  ///
  double num;

  HourBizModel({
    this.hourTime = 0.0,
    this.hourOvertime = 0.0,
    this.num = 0.0,
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}

///其他费用
class OtherExpensesCountBizModel {
  String money;
  String realMoney;
  int count;
  int bkOtherExpensesId;
  String name;
  int num;

  OtherExpensesCountBizModel({
    this.money = "",
    this.realMoney = "",
    this.count = 0,
    this.bkOtherExpensesId = 0,
    this.name = "",
    this.num = 0,
  });
}

class SummaryBizModel {
  /// 上班工时
  String workTime;

  /// 上班小时
  String workHour;

  /// 加班工时
  double overtimeWork;

  /// 加班小时
  String overtime;

  /// 借支金额
  String borrow;

  /// 结算金额
  String wage;

  SummaryBizModel({
    this.workTime = "",
    this.workHour = "",
    this.overtimeWork = 0.0,
    this.overtime = "",
    this.borrow = "",
    this.wage = "",
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}
