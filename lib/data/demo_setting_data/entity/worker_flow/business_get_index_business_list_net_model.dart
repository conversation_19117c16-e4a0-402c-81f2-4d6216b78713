import 'dart:convert';
import 'package:json_annotation/json_annotation.dart';
import 'business_get_index_business_list_biz_model.dart';

part 'business_get_index_business_list_net_model.g.dart';

@JsonSerializable()
class BusinessGetIndexBusinessListNetModel {
  ///
  List<BusinessGetIndexBusinessListANetModel>? list;

  BusinessGetIndexBusinessListNetModel();

  factory BusinessGetIndexBusinessListNetModel.fromJson(Map<String, dynamic> json) =>
      _$BusinessGetIndexBusinessListNetModelFromJson(json);

  Map<String, dynamic> toJson() => _$BusinessGetIndexBusinessListNetModelToJson(this);

  BusinessGetIndexBusinessListBizModel transform() {
    return BusinessGetIndexBusinessListBizModel(
      list: list?.map((e) => e.transform()).toList() ?? [],
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class BusinessGetIndexBusinessListANetModel {
  ///
  String? date;

  ///
  List<BusinessGetIndexBusinessListBNetModel>? list;

  BusinessGetIndexBusinessListANetModel();

  factory BusinessGetIndexBusinessListANetModel.fromJson(Map<String, dynamic> json) =>
      _$BusinessGetIndexBusinessListANetModelFromJson(json);

  Map<String, dynamic> toJson() => _$BusinessGetIndexBusinessListANetModelToJson(this);

  BusinessGetIndexBusinessListABizModel transform() {
    return BusinessGetIndexBusinessListABizModel(
      date: this.date ?? "",
      list: this.list?.map((e) => e.transform()).toList() ?? [],
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class BusinessGetIndexBusinessListBNetModel {
  ///
  double? id;

  ///
  double? business_type;

  ///
  String? business_time;

  ///
  double? work_note;

  ///
  double? worker_id;

  ///
  double? unit_work_type;

  ///
  double? work_time;

  ///
  double? work_time_hour;

  ///
  double? overtime;

  ///
  double? overtime_work;

  ///
  String? morning_work_time;

  ///
  String? morning_work_time_hour;

  ///
  String? afternoon_work_time;

  ///
  String? afternoon_work_time_hour;

  ///
  double? user_choose_spotwork;

  ///
  String? unit_num;

  ///
  String? unit_price;

  /// 4.0以前旧版本工量单位 4.0后废弃
  String? unit;

  ///
  String? note;

  ///
  double? fee_money;

  ///
  double? created_time;

  ///
  String? work_note_name;

  ///
  String? worker_name;

  ///
  String? money;

  ///
  double? fee_standard_id;

  ///
  double? has_img;

  /// 工量 分项名称
  String? unit_work_type_name;

  /// 工量 分项单位
  String? unit_work_type_unit;

  /// 仅[5.0.0]及以后 图片信息
  List<ImgInfoNetModel>? img_info;

  /// 其他费用
  OtherExpensesNetModel? other_expenses;

  BusinessGetIndexBusinessListBNetModel();

  factory BusinessGetIndexBusinessListBNetModel.fromJson(Map<String, dynamic> json) =>
      _$BusinessGetIndexBusinessListBNetModelFromJson(json);

  Map<String, dynamic> toJson() => _$BusinessGetIndexBusinessListBNetModelToJson(this);

  BusinessGetIndexBusinessListBBizModel transform() {
    return BusinessGetIndexBusinessListBBizModel(
      id: this.id ?? 0.0,
      businessType: this.business_type ?? 0.0,
      businessTime: this.business_time ?? "",
      workNote: this.work_note ?? 0.0,
      workerId: this.worker_id ?? 0.0,
      unitWorkType: this.unit_work_type ?? 0.0,
      workTime: this.work_time ?? 0.0,
      workTimeHour: this.work_time_hour ?? 0.0,
      overtime: this.overtime ?? 0.0,
      overtimeWork: this.overtime_work ?? 0.0,
      morningWorkTime: this.morning_work_time ?? "",
      morningWorkTimeHour: this.morning_work_time_hour ?? "",
      afternoonWorkTime: this.afternoon_work_time ?? "",
      afternoonWorkTimeHour: this.afternoon_work_time_hour ?? "",
      userChooseSpotwork: this.user_choose_spotwork ?? 0.0,
      unitNum: this.unit_num ?? "",
      unitPrice: this.unit_price ?? "",
      unit: this.unit ?? "",
      note: this.note ?? "",
      feeMoney: this.fee_money ?? 0.0,
      createdTime: this.created_time ?? 0.0,
      workNoteName: this.work_note_name ?? "",
      workerName: this.worker_name ?? "",
      money: this.money ?? "",
      feeStandardId: this.fee_standard_id ?? 0.0,
      hasImg: this.has_img ?? 0.0,
      unitWorkTypeName: this.unit_work_type_name ?? "",
      unitWorkTypeUnit: this.unit_work_type_unit ?? "",
      imgInfo: this.img_info?.map((e) => e.transform()).toList() ?? [],
      otherExpenses: this.other_expenses?.transform(),
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class ImgInfoNetModel {
  /// 图片完整路径
  String? url;
  String? type;
  String? base_url;
  String? cover;

  ImgInfoNetModel();

  factory ImgInfoNetModel.fromJson(Map<String, dynamic> json) => _$ImgInfoNetModelFromJson(json);

  Map<String, dynamic> toJson() => _$ImgInfoNetModelToJson(this);

  ImgInfoBizModel transform() {
    return ImgInfoBizModel(
      url: this.url ?? "",
      type: this.type ?? "",
      baseUrl: this.base_url ?? "",
      cover: this.cover ?? "",
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class OtherExpensesNetModel {
  /// 主键id
  String? id;

  /// 记工表iD
  String? bk_business_id;

  /// 其他费用ID
  String? bk_other_expenses_id;

  /// 记工用户ID
  String? member_id;

  /// 鱼泡ID
  String? yupao_id;

  /// 费用名称
  String? name;

  OtherExpensesNetModel();

  factory OtherExpensesNetModel.fromJson(Map<String, dynamic> json) =>
      _$OtherExpensesNetModelFromJson(json);

  Map<String, dynamic> toJson() => _$OtherExpensesNetModelToJson(this);

  OtherExpensesBizModel transform() {
    return OtherExpensesBizModel(
      id: this.id ?? "",
      bkBusinessId: this.bk_business_id ?? "",
      bkOtherExpensesId: this.bk_other_expenses_id ?? "",
      memberId: this.member_id ?? "",
      yupaoId: this.yupao_id ?? "",
      name: this.name ?? "",
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}
