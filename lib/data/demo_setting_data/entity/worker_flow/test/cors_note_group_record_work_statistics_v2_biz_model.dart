import 'dart:convert';

class CorsNoteGroupRecordWorkStatisticsV2BizModel {
  /// 自己的worker_id
  String selfWorkerId;
  /// 
  List<CorsNoteGroupRecordWorkStatisticsV2ABizModel> list;
  /// 是否有班组 0否1是
  String hasTeam;

  CorsNoteGroupRecordWorkStatisticsV2BizModel({
    this.selfWorkerId = "",
    this.list = const [],
    this.hasTeam = "",
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class CorsNoteGroupRecordWorkStatisticsV2ABizModel {
  /// 短工笔数
  double moneyNum;
  /// 工资收入/支出
  double wage;
  /// 
  double wageNum;
  /// 借支
  double borrowMoney;
  /// 
  double borrowMoneyNum;
  /// 是否有记工
  double hasBusiness;
  /// 工量 (v1接口count与num 含义反了 该接口修复)
  List<UnitCountBizModel> unitCount;
  ///  不为0 表示未算的笔数
  double spotWorkNoFeeNum;
  /// 不为0 表示未结清的钱
  double spotWorkNoFeeMoney;
  /// 工量笔数
  double unitCountNum;
  /// 
  double workerId;
  /// 
  String name;
  /// 点工
  final SpotWorkBizModel? spotWork;
  /// 点工笔数
  double spotWorkNum;
  /// 包工
  final ContractorWorkBizModel? contractorWork;
  /// 包工笔数
  double contractorWorkNum;
  /// 小时工
  final HourBizModel? hour;
  /// 小时工笔数
  double hourNum;
  /// 短工
  final MoneyBizModel? money;

  CorsNoteGroupRecordWorkStatisticsV2ABizModel({
    this.moneyNum = 0.0,
    this.wage = 0.0,
    this.wageNum = 0.0,
    this.borrowMoney = 0.0,
    this.borrowMoneyNum = 0.0,
    this.hasBusiness = 0.0,
    this.unitCount = const [],
    this.spotWorkNoFeeNum = 0.0,
    this.spotWorkNoFeeMoney = 0.0,
    this.unitCountNum = 0.0,
    this.workerId = 0.0,
    this.name = "",
    this.spotWork,
    this.spotWorkNum = 0.0,
    this.contractorWork,
    this.contractorWorkNum = 0.0,
    this.hour,
    this.hourNum = 0.0,
    this.money,
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class UnitCountBizModel {
  /// 4.0以前旧版单位 4.0后废弃
  String unit;
  /// 记工笔数
  double num;
  /// 分项单位
  String unitWorkTypeUnit;
  /// 分项名称
  String unitWorkTypeName;
  /// 分项 id
  double unitWorkType;
  /// 工钱
  String unitMoney;
  /// 工程量
  String count;

  UnitCountBizModel({
    this.unit = "",
    this.num = 0.0,
    this.unitWorkTypeUnit = "",
    this.unitWorkTypeName = "",
    this.unitWorkType = 0.0,
    this.unitMoney = "",
    this.count = "",
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class SpotWorkBizModel {
  /// 金额
  double money;
  /// 工时长 上班多少工
  double workTime;
  /// 工时长 上班多小时
  double workTimeHour;
  /// 工时长 加班多少小时
  double overtime;
  /// 工时长 加班多少工
  double overtimeWork;
  /// 
  double allMoney;
  /// 工资
  double feeMoney;

  SpotWorkBizModel({
    this.money = 0.0,
    this.workTime = 0.0,
    this.workTimeHour = 0.0,
    this.overtime = 0.0,
    this.overtimeWork = 0.0,
    this.allMoney = 0.0,
    this.feeMoney = 0.0,
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class ContractorWorkBizModel {
  /// 
  double money;
  /// 工时长 上班多少工
  double workTime;
  /// 工时长 上班多小时
  double workTimeHour;
  /// 工时长 加班多少小时
  double overtime;
  /// 工时长 加班多少工
  double overtimeWork;
  /// 
  double allMoney;
  /// 工资
  String feeMoney;

  ContractorWorkBizModel({
    this.money = 0.0,
    this.workTime = 0.0,
    this.workTimeHour = 0.0,
    this.overtime = 0.0,
    this.overtimeWork = 0.0,
    this.allMoney = 0.0,
    this.feeMoney = "",
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class HourBizModel {
  /// 
  double money;
  /// 工时长 上班(小时数)
  double workTimeHour;
  /// 工时长 加班(小时数)
  double overtime;
  /// 
  double allMoney;

  HourBizModel({
    this.money = 0.0,
    this.workTimeHour = 0.0,
    this.overtime = 0.0,
    this.allMoney = 0.0,
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class MoneyBizModel {
  /// 金额
  double money;

  MoneyBizModel({
    this.money = 0.0,
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}

