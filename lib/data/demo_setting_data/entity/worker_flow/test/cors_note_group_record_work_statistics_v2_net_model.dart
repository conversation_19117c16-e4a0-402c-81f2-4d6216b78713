import 'dart:convert';
import 'package:json_annotation/json_annotation.dart';
import 'cors_note_group_record_work_statistics_v2_biz_model.dart';

part 'cors_note_group_record_work_statistics_v2_net_model.g.dart';

@JsonSerializable()
class CorsNoteGroupRecordWorkStatisticsV2NetModel {
  /// 自己的worker_id
  String? self_worker_id;
  /// 
  List<CorsNoteGroupRecordWorkStatisticsV2ANetModel>? list;
  /// 是否有班组 0否1是
  String? hasTeam;

  CorsNoteGroupRecordWorkStatisticsV2NetModel();

  factory CorsNoteGroupRecordWorkStatisticsV2NetModel.fromJson(Map<String, dynamic> json) => _$CorsNoteGroupRecordWorkStatisticsV2NetModelFromJson(json);

  Map<String, dynamic> toJson() => _$CorsNoteGroupRecordWorkStatisticsV2NetModelToJson(this);

  CorsNoteGroupRecordWorkStatisticsV2BizModel transform() {
    return CorsNoteGroupRecordWorkStatisticsV2BizModel(
      selfWorkerId: self_worker_id ?? "",
      list: list?.map((e) => e.transform()).toList() ?? [],
      hasTeam: hasTeam ?? "",
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class CorsNoteGroupRecordWorkStatisticsV2ANetModel {
  /// 短工笔数
  double? money_num;
  /// 工资收入/支出
  double? wage;
  /// 
  double? wage_num;
  /// 借支
  double? borrow_money;
  /// 
  double? borrow_money_num;
  /// 是否有记工
  double? has_business;
  /// 工量 (v1接口count与num 含义反了 该接口修复)
  List<UnitCountNetModel>? unit_count;
  ///  不为0 表示未算的笔数
  double? spot_work_no_fee_num;
  /// 不为0 表示未结清的钱
  double? spot_work_no_fee_money;
  /// 工量笔数
  double? unit_count_num;
  /// 
  double? worker_id;
  /// 
  String? name;
  /// 点工
  SpotWorkNetModel? spot_work;
  /// 点工笔数
  double? spot_work_num;
  /// 包工
  ContractorWorkNetModel? contractor_work;
  /// 包工笔数
  double? contractor_work_num;
  /// 小时工
  HourNetModel? hour;
  /// 小时工笔数
  double? hour_num;
  /// 短工
  MoneyNetModel? money;

  CorsNoteGroupRecordWorkStatisticsV2ANetModel();

  factory CorsNoteGroupRecordWorkStatisticsV2ANetModel.fromJson(Map<String, dynamic> json) => _$CorsNoteGroupRecordWorkStatisticsV2ANetModelFromJson(json);

  Map<String, dynamic> toJson() => _$CorsNoteGroupRecordWorkStatisticsV2ANetModelToJson(this);

  CorsNoteGroupRecordWorkStatisticsV2ABizModel transform() {
    return CorsNoteGroupRecordWorkStatisticsV2ABizModel(
      moneyNum: money_num ?? 0.0,
      wage: wage ?? 0.0,
      wageNum: wage_num ?? 0.0,
      borrowMoney: borrow_money ?? 0.0,
      borrowMoneyNum: borrow_money_num ?? 0.0,
      hasBusiness: has_business ?? 0.0,
      unitCount: unit_count?.map((e) => e.transform()).toList() ?? [],
      spotWorkNoFeeNum: spot_work_no_fee_num ?? 0.0,
      spotWorkNoFeeMoney: spot_work_no_fee_money ?? 0.0,
      unitCountNum: unit_count_num ?? 0.0,
      workerId: worker_id ?? 0.0,
      name: name ?? "",
      spotWork: spot_work?.transform(),
      spotWorkNum: spot_work_num ?? 0.0,
      contractorWork: contractor_work?.transform(),
      contractorWorkNum: contractor_work_num ?? 0.0,
      hour: hour?.transform(),
      hourNum: hour_num ?? 0.0,
      money: money?.transform(),
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class UnitCountNetModel {
  /// 4.0以前旧版单位 4.0后废弃
  String? unit;
  /// 记工笔数
  double? num;
  /// 分项单位
  String? unit_work_type_unit;
  /// 分项名称
  String? unit_work_type_name;
  /// 分项 id
  double? unit_work_type;
  /// 工钱
  String? unit_money;
  /// 工程量
  String? count;

  UnitCountNetModel();

  factory UnitCountNetModel.fromJson(Map<String, dynamic> json) => _$UnitCountNetModelFromJson(json);

  Map<String, dynamic> toJson() => _$UnitCountNetModelToJson(this);

  UnitCountBizModel transform() {
    return UnitCountBizModel(
      unit: unit ?? "",
      num: num ?? 0.0,
      unitWorkTypeUnit: unit_work_type_unit ?? "",
      unitWorkTypeName: unit_work_type_name ?? "",
      unitWorkType: unit_work_type ?? 0.0,
      unitMoney: unit_money ?? "",
      count: count ?? "",
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class SpotWorkNetModel {
  /// 金额
  double? money;
  /// 工时长 上班多少工
  double? work_time;
  /// 工时长 上班多小时
  double? work_time_hour;
  /// 工时长 加班多少小时
  double? overtime;
  /// 工时长 加班多少工
  double? overtime_work;
  /// 
  double? all_money;
  /// 工资
  double? fee_money;

  SpotWorkNetModel();

  factory SpotWorkNetModel.fromJson(Map<String, dynamic> json) => _$SpotWorkNetModelFromJson(json);

  Map<String, dynamic> toJson() => _$SpotWorkNetModelToJson(this);

  SpotWorkBizModel transform() {
    return SpotWorkBizModel(
      money: money ?? 0.0,
      workTime: work_time ?? 0.0,
      workTimeHour: work_time_hour ?? 0.0,
      overtime: overtime ?? 0.0,
      overtimeWork: overtime_work ?? 0.0,
      allMoney: all_money ?? 0.0,
      feeMoney: fee_money ?? 0.0,
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class ContractorWorkNetModel {
  /// 
  double? money;
  /// 工时长 上班多少工
  double? work_time;
  /// 工时长 上班多小时
  double? work_time_hour;
  /// 工时长 加班多少小时
  double? overtime;
  /// 工时长 加班多少工
  double? overtime_work;
  /// 
  double? all_money;
  /// 工资
  String? fee_money;

  ContractorWorkNetModel();

  factory ContractorWorkNetModel.fromJson(Map<String, dynamic> json) => _$ContractorWorkNetModelFromJson(json);

  Map<String, dynamic> toJson() => _$ContractorWorkNetModelToJson(this);

  ContractorWorkBizModel transform() {
    return ContractorWorkBizModel(
      money: money ?? 0.0,
      workTime: work_time ?? 0.0,
      workTimeHour: work_time_hour ?? 0.0,
      overtime: overtime ?? 0.0,
      overtimeWork: overtime_work ?? 0.0,
      allMoney: all_money ?? 0.0,
      feeMoney: fee_money ?? "",
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class HourNetModel {
  /// 
  double? money;
  /// 工时长 上班(小时数)
  double? work_time_hour;
  /// 工时长 加班(小时数)
  double? overtime;
  /// 
  double? all_money;

  HourNetModel();

  factory HourNetModel.fromJson(Map<String, dynamic> json) => _$HourNetModelFromJson(json);

  Map<String, dynamic> toJson() => _$HourNetModelToJson(this);

  HourBizModel transform() {
    return HourBizModel(
      money: money ?? 0.0,
      workTimeHour: work_time_hour ?? 0.0,
      overtime: overtime ?? 0.0,
      allMoney: all_money ?? 0.0,
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class MoneyNetModel {
  /// 金额
  double? money;

  MoneyNetModel();

  factory MoneyNetModel.fromJson(Map<String, dynamic> json) => _$MoneyNetModelFromJson(json);

  Map<String, dynamic> toJson() => _$MoneyNetModelToJson(this);

  MoneyBizModel transform() {
    return MoneyBizModel(
      money: money ?? 0.0,
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

