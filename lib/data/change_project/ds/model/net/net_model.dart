import 'dart:convert';
import 'package:gdjg_pure_flutter/data/change_project/repo/model/biz_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'net_model.g.dart';

@JsonSerializable()
class ProjectGetAllProjectNameNetModel {
  /// 
  List<ProjectGetAllProjectNameANetModel>? list;

  ProjectGetAllProjectNameNetModel();

  factory ProjectGetAllProjectNameNetModel.fromJson(Map<String, dynamic> json) => _$ProjectGetAllProjectNameNetModelFromJson(json);

  Map<String, dynamic> toJson() => _$ProjectGetAllProjectNameNetModelToJson(this);

  ProjectGetAllProjectNameBizModel transform() {
    return ProjectGetAllProjectNameBizModel(
      list: list?.map((e) => e.transform()).toList() ?? [],
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class ProjectGetAllProjectNameANetModel {
  /// 
  double? id;
  /// 
  String? name;
  /// 
  double? dept_id;
  /// 
  double? corp_id;
  /// 是否是上次记工记账 0-否 1-是
  double? is_last_bookkeeping;

  ProjectGetAllProjectNameANetModel();

  factory ProjectGetAllProjectNameANetModel.fromJson(Map<String, dynamic> json) => _$ProjectGetAllProjectNameANetModelFromJson(json);

  Map<String, dynamic> toJson() => _$ProjectGetAllProjectNameANetModelToJson(this);

  ProjectGetAllProjectNameABizModel transform() {
    return ProjectGetAllProjectNameABizModel(
      id: id ?? 0.0,
      name: name ?? "",
      deptId: dept_id ?? 0.0,
      corpId: corp_id ?? 0.0,
      isLastBookkeeping: is_last_bookkeeping ?? 0.0,
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

