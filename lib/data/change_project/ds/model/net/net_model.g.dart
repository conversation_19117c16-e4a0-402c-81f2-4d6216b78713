// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'net_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ProjectGetAllProjectNameNetModel _$ProjectGetAllProjectNameNetModelFromJson(
        Map<String, dynamic> json) =>
    ProjectGetAllProjectNameNetModel()
      ..list = (json['list'] as List<dynamic>?)
          ?.map((e) => ProjectGetAllProjectNameANetModel.fromJson(
              e as Map<String, dynamic>))
          .toList();

Map<String, dynamic> _$ProjectGetAllProjectNameNetModelToJson(
        ProjectGetAllProjectNameNetModel instance) =>
    <String, dynamic>{
      'list': instance.list,
    };

ProjectGetAllProjectNameANetModel _$ProjectGetAllProjectNameANetModelFromJson(
        Map<String, dynamic> json) =>
    ProjectGetAllProjectNameANetModel()
      ..id = (json['id'] as num?)?.toDouble()
      ..name = json['name'] as String?
      ..dept_id = (json['dept_id'] as num?)?.toDouble()
      ..corp_id = (json['corp_id'] as num?)?.toDouble()
      ..is_last_bookkeeping = (json['is_last_bookkeeping'] as num?)?.toDouble();

Map<String, dynamic> _$ProjectGetAllProjectNameANetModelToJson(
        ProjectGetAllProjectNameANetModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'dept_id': instance.dept_id,
      'corp_id': instance.corp_id,
      'is_last_bookkeeping': instance.is_last_bookkeeping,
    };
