class ProjectGetAllProjectNameParamModel {

  /// 1班组 2个人 ，默认2
  final String identity;

  /// 0 在建 1 已结束
  final String status;

  /// 1记工 2记账
  final String data_type;

   ProjectGetAllProjectNameParamModel({
     this.identity = "2",
     this.status = "0",
     this.data_type = "1",
  });

   Map<String, Object> toMap() {
     return {
       'identity': identity,
       'status': status,
       'data_type': data_type,
     };
   }
}

