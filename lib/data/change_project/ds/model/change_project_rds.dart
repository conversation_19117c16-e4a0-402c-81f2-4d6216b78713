
import 'package:gdjg_pure_flutter/data/change_project/ds/model/net/net_model.dart';
import 'package:gdjg_pure_flutter/data/change_project/ds/model/param/param_model.dart';
import 'package:net_plugin/pigeon/api_generated/net_channel.g.dart';
import 'package:net_plugin/pigeon/net_result/resp_result.dart';
import 'package:net_plugin/pigeon/net_service/net_core.dart';

class ChangeProjectRds {

  Future<RespResult<ProjectGetAllProjectNameNetModel>> getAllProjectList(ProjectGetAllProjectNameParamModel param) async {
    return await NetCore.requestJGPHP(
        BaseBizRequestEntity(
            url: '/api/v3/project/get-all-project-name',
            method: HTTP_METHOD.GET,
            content: param.toMap()),
        (json) => ProjectGetAllProjectNameNetModel.fromJson(json));
  }

}