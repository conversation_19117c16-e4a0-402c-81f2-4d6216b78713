import 'dart:convert';

class ProjectGetAllProjectNameBizModel {
  /// 
  List<ProjectGetAllProjectNameABizModel> list;

  ProjectGetAllProjectNameBizModel({
    this.list = const [],
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class ProjectGetAllProjectNameABizModel {
  /// 
  double id;
  /// 
  String name;
  /// 
  double deptId;
  /// 
  double corpId;
  /// 是否是上次记工记账 0-否 1-是
  double isLastBookkeeping;

  ProjectGetAllProjectNameABizModel({
    this.id = 0.0,
    this.name = "",
    this.deptId = 0.0,
    this.corpId = 0.0,
    this.isLastBookkeeping = 0.0,
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}

