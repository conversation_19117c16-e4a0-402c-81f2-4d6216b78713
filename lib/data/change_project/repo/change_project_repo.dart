import 'package:gdjg_pure_flutter/data/change_project/ds/model/change_project_rds.dart';
import 'package:gdjg_pure_flutter/data/change_project/ds/model/net/net_model.dart';
import 'package:gdjg_pure_flutter/data/change_project/ds/model/param/param_model.dart';
import 'package:gdjg_pure_flutter/data/change_project/repo/model/biz_model.dart';
import 'package:net_plugin/pigeon/net_result/resp_result.dart';

class ChangeProjectRepo {
  final _rds = ChangeProjectRds();

  Future<RespResult<ProjectGetAllProjectNameBizModel?>> getAllProjectList(ProjectGetAllProjectNameParamModel param) async {
    var result = await _rds.getAllProjectList(param);

    return result.map(_transformProjectList);
  }

  ProjectGetAllProjectNameBizModel? _transformProjectList(ProjectGetAllProjectNameNetModel? netModel) {
    return netModel?.transform();
  }
}
