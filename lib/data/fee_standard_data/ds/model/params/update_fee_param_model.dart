class FeeStandardUpdateNewParamModel {

  /// 记工本ID
  final String? workNoteId;

  /// 工友ID
  final String? workerId;

  /// 记工类型
  final String? businessType;

  /// 上班工单价
  final String? workingHoursPrice;

  /// 上班工时比
  final String? workingHoursStandard;

  /// 加班小时单价
  final String? overtimeHoursPrice;

  /// 加班工时比
  final String? overtimeHoursStandard;

  /// 1 按工长算 | 2按小时算
  final double? overtimeType;


  /// 1 按工长算 | 2按小时算
  final int type;

  FeeStandardUpdateNewParamModel({
    this.workNoteId,
    this.workerId,
    this.businessType,
    this.workingHoursPrice,
    this.workingHoursStandard,
    this.overtimeHoursPrice,
    this.overtimeHoursStandard,
    this.overtimeType,
    this.type = 1,
  });

  Map<String, Object> toMap() {
    return {
      "workNoteId": workNoteId ?? "",
      "workerId": workerId ?? "",
      "businessType": businessType ?? "",
      "workingHoursPrice": workingHoursPrice ?? "",
      "workingHoursStandard": workingHoursStandard ?? "",
      "overtimeHoursPrice": overtimeHoursPrice ?? "",
      "overtimeHoursStandard": overtimeHoursStandard ?? "",
      "overtimeType": overtimeType ?? "",
      "type": type,
    };
  }
}

