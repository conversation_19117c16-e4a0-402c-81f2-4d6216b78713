class FeeStandardParamModel {
  String? businessType;
  String? workNoteId;
  String? type;
  String? workerId;

  FeeStandardParamModel({
    this.businessType,
    this.workNoteId,
    this.type,
    this.workerId,
  });

  Map<String, Object> toMap() {
    return {
      "businessType": businessType ?? "",
      "workNoteId": workNoteId ?? "",
      "type": type ?? "",
      "workerId": workerId ?? "",
    };
  }
}
