class FeeStandardRecalculateTeamParamModel {

  /// 账本id 全部传0 多个用逗号隔开
  final String workNoteId;

  /// 开始时间 2020-2-2
  final String startTime;

  /// 结束时间 2020-2-2
  final String endTime;

  /// 记工类型
  final String businessType;

  /// 上班标准 1 点工 2 包工
  final String workingHoursStandard;

  /// 工资标准 每个工多少钱
  final String workingHoursPrice;

  /// 加班类型 1 按工天算 2 按小时算
  final String overtimeType;

  /// 加班标准 (按工天算)
  final String overtimeHoursStandard;

  /// 加班工时价格 (按小时算)
  final String overtimeHoursPrice;

  /// 
  final String workerId;

   FeeStandardRecalculateTeamParamModel({
    this.workNoteId = "",
    this.startTime = "",
    this.endTime = "",
    this.businessType = "",
    this.workingHoursStandard = "",
    this.workingHoursPrice = "",
    this.overtimeType = "",
    this.overtimeHoursStandard = "",
    this.overtimeHoursPrice = "",
    this.workerId = "",
  });

  Map<String, String> toMap() {
    return {
      "workNoteId": workNoteId,
      "startTime": startTime,
      "endTime": endTime,
      "businessType": businessType,
      "workingHoursStandard": workingHoursStandard,
      "workingHoursPrice": workingHoursPrice,
      "overtimeType": overtimeType,
      "overtimeHoursStandard": overtimeHoursStandard,
      "overtimeHoursPrice": overtimeHoursPrice,
      "workerId": workerId,
    };
  }
}

