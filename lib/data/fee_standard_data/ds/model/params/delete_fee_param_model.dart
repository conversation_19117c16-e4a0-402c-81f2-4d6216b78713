class DeleteFeeStandardParamModel {
  /// 记工本ID
  final String? work_note;

  /// 工友ID
  final String? worker_ids;

  /// 记工类型
  final String? businessType;

  /// 上班工单价
  final String? type;

  DeleteFeeStandardParamModel(
      {this.work_note, this.worker_ids, this.businessType, this.type});

  Map<String, Object> toMap() {
    return {
      "work_note": work_note ?? "",
      "worker_ids": worker_ids ?? "",
      "businessType": businessType ?? "",
      "type": type ?? "",
    };
  }
}
