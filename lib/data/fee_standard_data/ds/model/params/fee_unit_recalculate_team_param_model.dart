class FeeUnitStandardRecalculateTeamParamModel {
  /// 账本id 全部传0 多个用逗号隔开
  final String work_note;

  /// 开始时间 2020-2-2
  final String start_time;

  /// 结束时间 2020-2-2
  final String end_time;

  final String unit_price;

  final String unit_work_type;

  ///
  final String worker_id;

  FeeUnitStandardRecalculateTeamParamModel({
    this.work_note = "",
    this.start_time = "",
    this.end_time = "",
    this.unit_price = "",
    this.unit_work_type = "",
    this.worker_id = "",
  });

  Map<String, String> toMap() {
    return {
      'work_note': work_note,
      'worker_id': worker_id,
      'start_time': start_time,
      'end_time': end_time,
      'unit_price': unit_price,
      'unit_work_type': unit_work_type,
    };
  }
}
