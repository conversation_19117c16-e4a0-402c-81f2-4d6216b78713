// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'fee_standard_net_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

FeeStandardNetModel _$FeeStandardGetNewNetModelFromJson(
        Map<String, dynamic> json) =>
    FeeStandardNetModel()
      ..feeStandardId = json['feeStandardId']?.toString()
      ..workingHoursPrice = json['workingHoursPrice']?.toString()
      ..workingHoursStandard = json['workingHoursStandard']?.toString()
      ..businessType = json['businessType']?.toString()
      ..overtimeType = (json['overtimeType'] as num?)?.toDouble()
      ..overtimeHoursStandard = json['overtimeHoursStandard']?.toString()
      ..overtimeHoursPrice = json['overtimeHoursPrice']?.toString()
      ..createdBy = (json['createdBy'] as num?)?.toDouble()
      ..createdTime = json['createdTime'] as String?
      ..isDefault = json['default'] as bool?
      ..fee_switch = (json['fee_switch'] as num?)?.toDouble();

Map<String, dynamic> _$FeeStandardGetNewNetModelToJson(FeeStandardNetModel instance) =>
    <String, dynamic>{
      'feeStandardId': instance.feeStandardId,
      'workingHoursPrice': instance.workingHoursPrice,
      'workingHoursStandard': instance.workingHoursStandard,
      'businessType': instance.businessType,
      'overtimeType': instance.overtimeType,
      'overtimeHoursStandard': instance.overtimeHoursStandard,
      'overtimeHoursPrice': instance.overtimeHoursPrice,
      'createdBy': instance.createdBy,
      'createdTime': instance.createdTime,
      'default': instance.isDefault,
      'fee_switch': instance.fee_switch,
    };
