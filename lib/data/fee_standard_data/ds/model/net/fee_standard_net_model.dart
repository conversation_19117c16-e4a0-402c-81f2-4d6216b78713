import 'dart:convert';
import 'package:json_annotation/json_annotation.dart';

part 'fee_standard_net_model.g.dart';

@JsonSerializable()
class FeeStandardNetModel {
  /// 工资规则ID
  String? feeStandardId;
  /// 一个工单价
  String? workingHoursPrice;
  /// 工时转换比率
  String? workingHoursStandard;
  /// 记工类型
  String? businessType;
  /// 加班计算工资类型 1 小时转化工计算| 2 按小时单价计算
  double? overtimeType;
  /// 加班工时比
  String? overtimeHoursStandard;
  /// 加班小时单价
  String? overtimeHoursPrice;
  /// 
  double? createdBy;
  /// 工资规则创建时间
  String? createdTime;
  /// 是否为默认工资规则
  @JsonKey(name: "default")
  bool? isDefault;
  /// 工资开关状态
  double? fee_switch;

  FeeStandardNetModel();

  factory FeeStandardNetModel.fromJson(Map<String, dynamic> json) => _$FeeStandardGetNewNetModelFromJson(json);

  Map<String, dynamic> toJson() => _$FeeStandardGetNewNetModelToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

