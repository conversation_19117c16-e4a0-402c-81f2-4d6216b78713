import 'dart:convert';
import 'package:gdjg_pure_flutter/data/fee_standard_data/repo/model/fee_standard_update_new_biz_model.dart';


class FeeStandardUpdateNewNetModel {

  /// 工资规则id
  String? feeStandardId;

  FeeStandardUpdateNewNetModel();

  Map<String, dynamic> toJson(FeeStandardUpdateNewNetModel instance) => <String, dynamic>{
      "feeStandardId": instance.feeStandardId,
    };

  factory FeeStandardUpdateNewNetModel.fromJson(Map<String, dynamic> json) {
    var netModel = FeeStandardUpdateNewNetModel();
    netModel.feeStandardId = json["feeStandardId"]?.toString();
    return netModel;
  }

  FeeStandardUpdateNewBizModel transform() {
    return FeeStandardUpdateNewBizModel(
      feeStandardId: feeStandardId ?? "",
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

