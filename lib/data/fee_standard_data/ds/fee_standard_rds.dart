import 'package:gdjg_pure_flutter/data/fee_standard_data/ds/model/net/fee_standard_net_model.dart';
import 'package:gdjg_pure_flutter/data/fee_standard_data/ds/model/net/fee_standard_update_new_net_model.dart';
import 'package:gdjg_pure_flutter/data/fee_standard_data/ds/model/params/delete_fee_param_model.dart';
import 'package:gdjg_pure_flutter/data/fee_standard_data/ds/model/params/fee_standard_param_model.dart';
import 'package:gdjg_pure_flutter/data/fee_standard_data/ds/model/params/fee_standard_recalculate_team_param_model.dart';
import 'package:gdjg_pure_flutter/data/fee_standard_data/ds/model/params/fee_unit_recalculate_team_param_model.dart';
import 'package:gdjg_pure_flutter/data/fee_standard_data/ds/model/params/update_fee_param_model.dart';
import 'package:net_plugin/pigeon/api_generated/net_channel.g.dart';
import 'package:net_plugin/pigeon/net_result/resp_result.dart';
import 'package:net_plugin/pigeon/net_service/net_core.dart';

class FeeStandardRds {
  /// 班组获取最后一笔工资规则
  Future<RespResult<FeeStandardNetModel>> getNewFeeStandard(
      FeeStandardParamModel params) async {
    return await NetCore.requestJGPHP(
        BaseBizRequestEntity(
            url: '/api/fee-standard/get_new',
            method: HTTP_METHOD.GET,
            content: params.toMap()),
        (json) => FeeStandardNetModel.fromJson(json));
  }

  /// 工人获取最后一笔工资规则
  Future<RespResult<FeeStandardNetModel>> getWorkerFeeStandard(
      FeeStandardParamModel params) async {
    return await NetCore.requestJGPHP(
        BaseBizRequestEntity(
            url: '/api/fee-standard/get_worker',
            method: HTTP_METHOD.GET,
            content: params.toMap()),
        (json) => FeeStandardNetModel.fromJson(json));
  }

  /// 班组修改工资规则
  Future<RespResult<dynamic>> recalculateTeam(
      FeeStandardRecalculateTeamParamModel params) async {
    return await NetCore.requestJGPHP(
        BaseBizRequestEntity(
            url: '/api/fee-standard/recalculate_team',
            method: HTTP_METHOD.POST,
            content: params.toMap(),
            requestExtra: RequestExtra(showLoading: true)),
        (json) => json);
  }

  /// 个人修改工资规则
  Future<RespResult<dynamic>> recalculateWorker(
      FeeStandardRecalculateTeamParamModel params) async {
    return await NetCore.requestJGPHP(
        BaseBizRequestEntity(
            url: '/api/fee-standard/recalculate_worker',
            method: HTTP_METHOD.POST,
            content: params.toMap(),
            requestExtra: RequestExtra(showLoading: true)),
        (json) => json);
  }

  /// 班组修改工量工资规则
  Future<RespResult<dynamic>> recalculateUnitTeam(
      FeeUnitStandardRecalculateTeamParamModel params) async {
    return await NetCore.requestJGPHP(
        BaseBizRequestEntity(
            url: '/api/UnitWorkType/recalculate-team',
            method: HTTP_METHOD.POST,
            content: params.toMap(),
            requestExtra: RequestExtra(showLoading: true)),
        (json) => json);
  }

  /// 个人修改工量工资规则
  Future<RespResult<dynamic>> recalculateUnitWorker(
      FeeUnitStandardRecalculateTeamParamModel params) async {
    return await NetCore.requestJGPHP(
        BaseBizRequestEntity(
            url: '/api/UnitWorkType/recalculate-worker',
            method: HTTP_METHOD.POST,
            content: params.toMap(),
            requestExtra: RequestExtra(showLoading: true)),
        (json) => json);
  }

  /// 个人修改工量工资规则
  Future<RespResult<FeeStandardUpdateNewNetModel>> updateFeeStandard(
      FeeStandardUpdateNewParamModel params) async {
    return await NetCore.requestJGPHP(
        BaseBizRequestEntity(
            url: '/api/fee-standard/update_new',
            method: HTTP_METHOD.POST,
            content: params.toMap(),
            requestExtra: RequestExtra(showLoading: true)),
        (json) => FeeStandardUpdateNewNetModel.fromJson(json));
  }

  /// 删除工资规则
  Future<RespResult<dynamic>> deleteFeeStandard(
      DeleteFeeStandardParamModel params) async {
    return await NetCore.requestJGPHP(
        BaseBizRequestEntity(
            url: '/api/fee-standard/delete_default_new',
            method: HTTP_METHOD.POST,
            content: params.toMap(),
            requestExtra: RequestExtra(showLoading: true)),
        (json) => json);
  }
}
