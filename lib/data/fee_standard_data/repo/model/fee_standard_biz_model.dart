import 'dart:convert';

import 'package:gdjg_pure_flutter/utils/system_util/string_util.dart';

class FeeStandardBizModel {
  /// 工资规则ID
  String feeStandardId;

  /// 一个工单价
  String workingHoursPrice;

  /// 工时转换比率
  String workingHoursStandard;

  /// 记工类型
  String businessType;

  /// 加班计算工资类型 1 小时转化工计算| 2 按小时单价计算
  double overtimeType;

  /// 加班工时比
  String overtimeHoursStandard;

  /// 加班小时单价
  String overtimeHoursPrice;

  ///
  double createdBy;

  /// 工资规则创建时间
  String createdTime;

  /// 是否为默认工资规则
  bool isDefault;

  /// 工资开关状态
  double feeSwitch;

  FeeStandardBizModel({
    this.feeStandardId = '',
    this.workingHoursPrice = "0",
    this.workingHoursStandard = "0",
    this.businessType = "",
    this.overtimeType = 0.0,
    this.overtimeHoursStandard = "0",
    this.overtimeHoursPrice = "0",
    this.createdBy = 0.0,
    this.createdTime = "",
    this.isDefault = false,
    this.feeSwitch = 0.0,
  });

  @override
  String toString() {
    return jsonEncode(this);
  }

  /// 是否有工资规则
  bool hasFeeStandard() {
    return feeStandardId.isNotEmpty && feeStandardId != "0";
  }

  /// 上班工资规则字符串
  String _workStr() {
    if (feeStandardId.isEmpty) {
      return "";
    }
    var workingHours = double.tryParse(workingHoursStandard) ?? 0;
    var price = workingHoursPrice.formatStringToMoney();
    if (workingHours == 1) {
      return '上班${workingHours.trimTrailingZeros()}小时${price.trimTrailingZeros()}元';
    } else {
      return '1个工${workingHours.trimTrailingZeros()}小时${price.trimTrailingZeros()}元';
    }
  }

  /// 加班工资规则字符串
  String _overStr() {
    if (feeStandardId.isEmpty) {
      return "";
    }
    if (overtimeType == 1) {
      // 按工天算
      return '加班${overtimeHoursStandard.trimTrailingZeros()}小时1个工';
    } else {
      return '加班1小时${overtimeHoursPrice.trimTrailingZeros()}元';
    }
  }

  ///工资规则转换
  String getWageText() {
    var workStr = _workStr();
    var overStr = _overStr();
    if (workStr.isNotEmpty && overStr.isNotEmpty) {
      return "$workStr\n$overStr";
    } else if (workStr.isNotEmpty) {
      return workStr;
    } else if (overStr.isNotEmpty) {
      return overStr;
    } else {
      return "";
    }
  }
}
