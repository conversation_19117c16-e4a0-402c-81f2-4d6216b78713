import 'package:gdjg_pure_flutter/data/fee_standard_data/ds/fee_standard_rds.dart';
import 'package:gdjg_pure_flutter/data/fee_standard_data/ds/model/net/fee_standard_update_new_net_model.dart';
import 'package:gdjg_pure_flutter/data/fee_standard_data/ds/model/params/delete_fee_param_model.dart';
import 'package:gdjg_pure_flutter/data/fee_standard_data/ds/model/params/fee_standard_param_model.dart';
import 'package:gdjg_pure_flutter/data/fee_standard_data/ds/model/params/fee_standard_recalculate_team_param_model.dart';
import 'package:gdjg_pure_flutter/data/fee_standard_data/ds/model/params/fee_unit_recalculate_team_param_model.dart';
import 'package:gdjg_pure_flutter/data/fee_standard_data/ds/model/params/update_fee_param_model.dart';
import 'package:gdjg_pure_flutter/data/fee_standard_data/repo/fee_standard_transform.dart';
import 'package:gdjg_pure_flutter/data/fee_standard_data/repo/model/fee_standard_biz_model.dart';
import 'package:net_plugin/pigeon/net_result/resp_result.dart';

///工资规则相关
class FeeStandardRepo {
  final _feeStandardRds = FeeStandardRds();

  /// 获取班组 最后一笔工资规则
  Future<RespResult<FeeStandardBizModel>> getNewFeeStandard(
      FeeStandardParamModel param) async {
    final result = await _feeStandardRds.getNewFeeStandard(param);
    return result.map(FeeStandardTransform.feeStandardTransform);
  }

  /// 获取工人 最后一笔工资规则
  Future<RespResult<FeeStandardBizModel>> getWorkerFeeStandard(
      FeeStandardParamModel param) async {
    final result = await _feeStandardRds.getWorkerFeeStandard(param);
    return result.map(FeeStandardTransform.feeStandardTransform);
  }

  /// 班组修改工资规则
  Future<RespResult<dynamic>> recalculateTeam(FeeStandardRecalculateTeamParamModel param) async {
    return await _feeStandardRds.recalculateTeam(param);
  }

  /// 个人修改工资规则
  Future<RespResult<dynamic>> recalculateWorker(FeeStandardRecalculateTeamParamModel param) async {
    return await _feeStandardRds.recalculateWorker(param);
  }

  /// 班组修改工量工资规则
  Future<RespResult<dynamic>> recalculateUnitTeam(FeeUnitStandardRecalculateTeamParamModel param) async {
    return await _feeStandardRds.recalculateUnitTeam(param);
  }

  /// 个人修改工量工资规则
  Future<RespResult<dynamic>> recalculateUnitWorker(FeeUnitStandardRecalculateTeamParamModel param) async {
    return await _feeStandardRds.recalculateUnitWorker(param);
  }

  /// 更新工资规则
  Future<RespResult<FeeStandardUpdateNewNetModel>> updateFeeStandard(FeeStandardUpdateNewParamModel param) async {
    return await _feeStandardRds.updateFeeStandard(param);
  }

  /// 删除工资规则
  Future<RespResult<dynamic>> deleteFeeStandard(DeleteFeeStandardParamModel param) async {
    return await _feeStandardRds.deleteFeeStandard(param);
  }
}
