import 'package:gdjg_pure_flutter/data/fee_standard_data/ds/model/net/fee_standard_net_model.dart';
import 'package:gdjg_pure_flutter/data/fee_standard_data/repo/model/fee_standard_biz_model.dart';

class FeeStandardTransform {
  static FeeStandardBizModel feeStandardTransform(
      FeeStandardNetModel? netModel) {
    return FeeStandardBizModel(
      feeStandardId: netModel?.feeStandardId ?? "",
      workingHoursPrice: netModel?.workingHoursPrice ?? "0",
      workingHoursStandard: netModel?.workingHoursStandard ?? "0",
      businessType: netModel?.businessType ?? "",
      overtimeType: netModel?.overtimeType ?? 0.0,
      overtimeHoursStandard: netModel?.overtimeHoursStandard ?? "0",
      overtimeHoursPrice: netModel?.overtimeHoursPrice ?? "0",
      createdBy: netModel?.createdBy ?? 0.0,
      createdTime: netModel?.createdTime ?? "",
      isDefault: netModel?.isDefault ?? false,
      feeSwitch: netModel?.fee_switch ?? 0.0,
    );
  }
}
