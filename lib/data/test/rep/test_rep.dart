

import 'package:gdjg_pure_flutter/data/test/ds/test_rds.dart';
import 'package:gdjg_pure_flutter/data/test/entity/project_get_project_list_entity.dart';
import 'package:gdjg_pure_flutter/data/test/entity/project_get_project_list_net_model.dart';
import 'package:gdjg_pure_flutter/data/test/entity/project_get_project_list_req_entity.dart';
import 'package:net_plugin/pigeon/net_result/resp_result.dart';

class TestRep {
  TestRds rds = TestRds();


  Future<RespResult<ProjectGetProjectListAEntity>> queryProjectList(
      ProjectGetProjectListReqEntity req,
      ) async {

    return await rds.queryProjectList(req);
  }

}