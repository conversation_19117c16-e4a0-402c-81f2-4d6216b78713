import 'dart:convert';

import 'project_get_project_list_net_model.dart';

class ProjectGetProjectListEntity {
  /// 
  double code;
  /// 
  final ProjectGetProjectListAEntity? data;
  /// 
  String msg;

  ProjectGetProjectListEntity({
    this.code = 0.0,
    this.data = null,
    this.msg = "",
  });

  static ProjectGetProjectListEntity transform(ProjectGetProjectListModel? model){
    return ProjectGetProjectListEntity(
      code: model?.code ?? 0.0,
      data: ProjectGetProjectListAEntity.transform(model?.data),
      msg: model?.msg ?? "",
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class ProjectGetProjectListAEntity {
  /// 在建项目
  final DoingEntity? doing;
  /// 已结清项目
  final EndEntity? end;

  ProjectGetProjectListAEntity({
    this.doing = null,
    this.end = null,
  });

  static ProjectGetProjectListAEntity transform(ProjectGetProjectListAModel? model){
    return ProjectGetProjectListAEntity(
      doing: DoingEntity().transform(model?.doing),
      end: EndEntity().transform(model?.end),
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class DoingEntity {
  /// 总未结
  double all;
  /// 
  List<ProjectGetProjectListBEntity> list;

  DoingEntity({
    this.all = 0.0,
    this.list = const [],
  });

  DoingEntity transform(DoingModel? model){
    return DoingEntity(
      all: model?.all ?? 0.0,
      list: model?.list?.map((e) => ProjectGetProjectListBEntity().transform(e)).toList() ?? [],
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class ProjectGetProjectListBEntity {
  /// work_note
  double id;
  /// 项目名称
  String name;
  /// 状态 0-在建 1-结清
  double status;
  /// 工资规则 id
  double feeStandardId;
  /// 是否有点工 0-否 1-是
  double hasSpotWork;
  /// 未结
  double unsettled;
  /// 收入
  double income;
  /// 是否有短工 0-否 1-是
  double hasMoney;
  /// 第一笔流水日期
  String startTime;
  /// 最后一笔流水日期
  String endTime;
  /// 最后操作日期
  String lastOperationDate;
  /// 最后流水日期
  String lastBusinessDate;
  /// 企业 id
  double corpId;
  /// 班组 id
  double deptId;
  /// [4.6]是否有过记工/记账 0-没有 1-有
  double hasBusiness;

  ProjectGetProjectListBEntity({
    this.id = 0.0,
    this.name = "",
    this.status = 0.0,
    this.feeStandardId = 0.0,
    this.hasSpotWork = 0.0,
    this.unsettled = 0.0,
    this.income = 0.0,
    this.hasMoney = 0.0,
    this.startTime = "",
    this.endTime = "",
    this.lastOperationDate = "",
    this.lastBusinessDate = "",
    this.corpId = 0.0,
    this.deptId = 0.0,
    this.hasBusiness = 0.0,
  });

  ProjectGetProjectListBEntity transform(ProjectGetProjectListBModel? model){
    return ProjectGetProjectListBEntity(
      id: model?.id ?? 0.0,
      name: model?.name ?? "",
      status: model?.status ?? 0.0,
      feeStandardId: model?.fee_standard_id ?? 0.0,
      hasSpotWork: model?.has_spot_work ?? 0.0,
      unsettled: model?.unsettled ?? 0.0,
      income: model?.income ?? 0.0,
      hasMoney: model?.has_money ?? 0.0,
      startTime: model?.start_time ?? "",
      endTime: model?.end_time ?? "",
      lastOperationDate: model?.last_operation_date ?? "",
      lastBusinessDate: model?.last_business_date ?? "",
      corpId: model?.corp_id ?? 0.0,
      deptId: model?.dept_id ?? 0.0,
      hasBusiness: model?.has_business ?? 0.0,
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class EndEntity {
  /// 总收入
  double all;
  /// 
  List<ProjectGetProjectListCEntity> list;

  EndEntity({
    this.all = 0.0,
    this.list = const [],
  });

  EndEntity transform(EndModel? model){
    return EndEntity(
      all: model?.all ?? 0.0,
      list: model?.list?.map((e) => ProjectGetProjectListCEntity().transform(e)).toList() ?? [],
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class ProjectGetProjectListCEntity {
  /// 
  double id;
  /// 
  String name;
  /// 
  double status;
  /// 
  double feeStandardId;
  /// 
  double hasSpotWork;
  /// 
  double unsettled;
  /// 
  double income;
  /// 
  double hasMoney;
  /// 第一笔流水日期
  String startTime;
  /// 最后一笔流水日期
  String endTime;
  /// 最后操作日期
  String lastOperationDate;
  /// 最后流水日期
  String lastBusinessDate;
  /// 企业 id
  double corpId;
  /// 班组 id
  double deptId;
  /// [4.6]是否有过记工/记账 0-没有 1-有
  double hasBusiness;

  ProjectGetProjectListCEntity({
    this.id = 0.0,
    this.name = "",
    this.status = 0.0,
    this.feeStandardId = 0.0,
    this.hasSpotWork = 0.0,
    this.unsettled = 0.0,
    this.income = 0.0,
    this.hasMoney = 0.0,
    this.startTime = "",
    this.endTime = "",
    this.lastOperationDate = "",
    this.lastBusinessDate = "",
    this.corpId = 0.0,
    this.deptId = 0.0,
    this.hasBusiness = 0.0,
  });

  ProjectGetProjectListCEntity transform(ProjectGetProjectListCModel? model){
    return ProjectGetProjectListCEntity(
      id: model?.id ?? 0.0,
      name: model?.name ?? "",
      status: model?.status ?? 0.0,
      feeStandardId: model?.fee_standard_id ?? 0.0,
      hasSpotWork: model?.has_spot_work ?? 0.0,
      unsettled: model?.unsettled ?? 0.0,
      income: model?.income ?? 0.0,
      hasMoney: model?.has_money ?? 0.0,
      startTime: model?.start_time ?? "",
      endTime: model?.end_time ?? "",
      lastOperationDate: model?.last_operation_date ?? "",
      lastBusinessDate: model?.last_business_date ?? "",
      corpId: model?.corp_id ?? 0.0,
      deptId: model?.dept_id ?? 0.0,
      hasBusiness: model?.has_business ?? 0.0,
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

