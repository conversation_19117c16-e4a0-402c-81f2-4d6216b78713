// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'project_get_project_list_net_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ProjectGetProjectListModel _$ProjectGetProjectListModelFromJson(
        Map<String, dynamic> json) =>
    ProjectGetProjectListModel()
      ..code = (json['code'] as num?)?.toDouble()
      ..data = json['data'] == null
          ? null
          : ProjectGetProjectListAModel.fromJson(
              json['data'] as Map<String, dynamic>)
      ..msg = json['msg'] as String?;

Map<String, dynamic> _$ProjectGetProjectListModelToJson(
        ProjectGetProjectListModel instance) =>
    <String, dynamic>{
      'code': instance.code,
      'data': instance.data,
      'msg': instance.msg,
    };

ProjectGetProjectListAModel _$ProjectGetProjectListAModelFromJson(
        Map<String, dynamic> json) =>
    ProjectGetProjectListAModel()
      ..doing = json['doing'] == null
          ? null
          : DoingModel.fromJson(json['doing'] as Map<String, dynamic>)
      ..end = json['end'] == null
          ? null
          : EndModel.fromJson(json['end'] as Map<String, dynamic>);

Map<String, dynamic> _$ProjectGetProjectListAModelToJson(
        ProjectGetProjectListAModel instance) =>
    <String, dynamic>{
      'doing': instance.doing,
      'end': instance.end,
    };

DoingModel _$DoingModelFromJson(Map<String, dynamic> json) => DoingModel()
  ..all = (json['all'] as num?)?.toDouble()
  ..list = (json['list'] as List<dynamic>?)
      ?.map((e) =>
          ProjectGetProjectListBModel.fromJson(e as Map<String, dynamic>))
      .toList();

Map<String, dynamic> _$DoingModelToJson(DoingModel instance) =>
    <String, dynamic>{
      'all': instance.all,
      'list': instance.list,
    };

ProjectGetProjectListBModel _$ProjectGetProjectListBModelFromJson(
        Map<String, dynamic> json) =>
    ProjectGetProjectListBModel()
      ..id = (json['id'] as num?)?.toDouble()
      ..name = json['name'] as String?
      ..status = (json['status'] as num?)?.toDouble()
      ..fee_standard_id = (json['fee_standard_id'] as num?)?.toDouble()
      ..has_spot_work = (json['has_spot_work'] as num?)?.toDouble()
      ..unsettled = (json['unsettled'] as num?)?.toDouble()
      ..income = (json['income'] as num?)?.toDouble()
      ..has_money = (json['has_money'] as num?)?.toDouble()
      ..start_time = json['start_time'] as String?
      ..end_time = json['end_time'] as String?
      ..last_operation_date = json['last_operation_date'] as String?
      ..last_business_date = json['last_business_date'] as String?
      ..corp_id = (json['corp_id'] as num?)?.toDouble()
      ..dept_id = (json['dept_id'] as num?)?.toDouble()
      ..has_business = (json['has_business'] as num?)?.toDouble();

Map<String, dynamic> _$ProjectGetProjectListBModelToJson(
        ProjectGetProjectListBModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'status': instance.status,
      'fee_standard_id': instance.fee_standard_id,
      'has_spot_work': instance.has_spot_work,
      'unsettled': instance.unsettled,
      'income': instance.income,
      'has_money': instance.has_money,
      'start_time': instance.start_time,
      'end_time': instance.end_time,
      'last_operation_date': instance.last_operation_date,
      'last_business_date': instance.last_business_date,
      'corp_id': instance.corp_id,
      'dept_id': instance.dept_id,
      'has_business': instance.has_business,
    };

EndModel _$EndModelFromJson(Map<String, dynamic> json) => EndModel()
  ..all = (json['all'] as num?)?.toDouble()
  ..list = (json['list'] as List<dynamic>?)
      ?.map((e) =>
          ProjectGetProjectListCModel.fromJson(e as Map<String, dynamic>))
      .toList();

Map<String, dynamic> _$EndModelToJson(EndModel instance) => <String, dynamic>{
      'all': instance.all,
      'list': instance.list,
    };

ProjectGetProjectListCModel _$ProjectGetProjectListCModelFromJson(
        Map<String, dynamic> json) =>
    ProjectGetProjectListCModel()
      ..id = (json['id'] as num?)?.toDouble()
      ..name = json['name'] as String?
      ..status = (json['status'] as num?)?.toDouble()
      ..fee_standard_id = (json['fee_standard_id'] as num?)?.toDouble()
      ..has_spot_work = (json['has_spot_work'] as num?)?.toDouble()
      ..unsettled = (json['unsettled'] as num?)?.toDouble()
      ..income = (json['income'] as num?)?.toDouble()
      ..has_money = (json['has_money'] as num?)?.toDouble()
      ..start_time = json['start_time'] as String?
      ..end_time = json['end_time'] as String?
      ..last_operation_date = json['last_operation_date'] as String?
      ..last_business_date = json['last_business_date'] as String?
      ..corp_id = (json['corp_id'] as num?)?.toDouble()
      ..dept_id = (json['dept_id'] as num?)?.toDouble()
      ..has_business = (json['has_business'] as num?)?.toDouble();

Map<String, dynamic> _$ProjectGetProjectListCModelToJson(
        ProjectGetProjectListCModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'status': instance.status,
      'fee_standard_id': instance.fee_standard_id,
      'has_spot_work': instance.has_spot_work,
      'unsettled': instance.unsettled,
      'income': instance.income,
      'has_money': instance.has_money,
      'start_time': instance.start_time,
      'end_time': instance.end_time,
      'last_operation_date': instance.last_operation_date,
      'last_business_date': instance.last_business_date,
      'corp_id': instance.corp_id,
      'dept_id': instance.dept_id,
      'has_business': instance.has_business,
    };
