import 'dart:convert';

import 'package:json_annotation/json_annotation.dart';

part 'project_get_project_list_net_model.g.dart';

@JsonSerializable()
class ProjectGetProjectListModel {
  /// 
  double? code;
  /// 
  ProjectGetProjectListAModel? data;
  /// 
  String? msg;

  ProjectGetProjectListModel();

  factory ProjectGetProjectListModel.fromJson(Map<String, dynamic> json) => _$ProjectGetProjectListModelFromJson(json);

  Map<String, dynamic> toJson() => _$ProjectGetProjectListModelToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class ProjectGetProjectListAModel {
  /// 在建项目
  DoingModel? doing;
  /// 已结清项目
  EndModel? end;

  ProjectGetProjectListAModel();

  factory ProjectGetProjectListAModel.fromJson(Map<String, dynamic> json) => _$ProjectGetProjectListAModelFromJson(json);

  Map<String, dynamic> toJson() => _$ProjectGetProjectListAModelToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class DoingModel {
  /// 总未结
  double? all;
  /// 
  List<ProjectGetProjectListBModel>? list;

  DoingModel();

  factory DoingModel.fromJson(Map<String, dynamic> json) => _$DoingModelFromJson(json);

  Map<String, dynamic> toJson() => _$DoingModelToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class ProjectGetProjectListBModel {
  /// work_note
  double? id;
  /// 项目名称
  String? name;
  /// 状态 0-在建 1-结清
  double? status;
  /// 工资规则 id
  double? fee_standard_id;
  /// 是否有点工 0-否 1-是
  double? has_spot_work;
  /// 未结
  double? unsettled;
  /// 收入
  double? income;
  /// 是否有短工 0-否 1-是
  double? has_money;
  /// 第一笔流水日期
  String? start_time;
  /// 最后一笔流水日期
  String? end_time;
  /// 最后操作日期
  String? last_operation_date;
  /// 最后流水日期
  String? last_business_date;
  /// 企业 id
  double? corp_id;
  /// 班组 id
  double? dept_id;
  /// [4.6]是否有过记工/记账 0-没有 1-有
  double? has_business;

  ProjectGetProjectListBModel();

  factory ProjectGetProjectListBModel.fromJson(Map<String, dynamic> json) => _$ProjectGetProjectListBModelFromJson(json);

  Map<String, dynamic> toJson() => _$ProjectGetProjectListBModelToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class EndModel {
  /// 总收入
  double? all;
  /// 
  List<ProjectGetProjectListCModel>? list;

  EndModel();

  factory EndModel.fromJson(Map<String, dynamic> json) => _$EndModelFromJson(json);

  Map<String, dynamic> toJson() => _$EndModelToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class ProjectGetProjectListCModel {
  /// 
  double? id;
  /// 
  String? name;
  /// 
  double? status;
  /// 
  double? fee_standard_id;
  /// 
  double? has_spot_work;
  /// 
  double? unsettled;
  /// 
  double? income;
  /// 
  double? has_money;
  /// 第一笔流水日期
  String? start_time;
  /// 最后一笔流水日期
  String? end_time;
  /// 最后操作日期
  String? last_operation_date;
  /// 最后流水日期
  String? last_business_date;
  /// 企业 id
  double? corp_id;
  /// 班组 id
  double? dept_id;
  /// [4.6]是否有过记工/记账 0-没有 1-有
  double? has_business;

  ProjectGetProjectListCModel();

  factory ProjectGetProjectListCModel.fromJson(Map<String, dynamic> json) => _$ProjectGetProjectListCModelFromJson(json);

  Map<String, dynamic> toJson() => _$ProjectGetProjectListCModelToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

