
import 'package:gdjg_pure_flutter/data/test/entity/project_get_project_list_entity.dart';
import 'package:gdjg_pure_flutter/data/test/entity/project_get_project_list_net_model.dart';
import 'package:gdjg_pure_flutter/data/test/entity/project_get_project_list_req_entity.dart';
import 'package:net_plugin/pigeon/api_generated/net_channel.g.dart';
import 'package:net_plugin/pigeon/net_result/resp_fail.dart';
import 'package:net_plugin/pigeon/net_result/resp_result.dart';
import 'package:net_plugin/pigeon/net_service/net_core.dart';

class TestRds {
  Future<RespResult<ProjectGetProjectListAEntity>> queryProjectList(
    ProjectGetProjectListReqEntity req,
  ) async {
    final queryBody = {
      'is_ignore': req.is_ignore,
      'identity': req.identity,
    };

    var reqParam = BaseBizRequestEntity(
        url: 'api/v3/project/get-project-list',
        method: HTTP_METHOD.GET,
        content: queryBody,
        requestExtra: RequestExtra(domain: NET_DOMAIN.JG_PHP, printResp: false, showLoading: true));
    final RespResult<ProjectGetProjectListAEntity> result = await NetCore.requestJGPHP(reqParam, (json) {
      return ProjectGetProjectListAEntity.transform(ProjectGetProjectListAModel.fromJson(json));
    }).catchError((error) {
      print('错误----${error.toString()}');
      return Future.value(RespResult<ProjectGetProjectListAEntity>(fail: RespFail(errorMsg: error.toString())));
    });
    return result;
  }
}