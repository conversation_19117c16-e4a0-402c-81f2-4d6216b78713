
import 'dart:convert';

import 'count_biz_model.dart';

class CalendarEntity {
  /// 是否有备注
  int isNote;

  /// 日期
  String day;

  /// 工量
  final UnitBizModel? unit;

  /// 短工
  final WorkMoneyBizModel? workMoney;

  /// 小时工
  HourBizModel? hour;

  /// 包工
  final ContractorBizModel? contractor;

  /// 所有记工类型
  List<int> businessType;

  /// 点工
  final SpotWorkBizModel? spotWork;

  int otherExpensesNum;

  CalendarEntity({
    this.isNote = 0,
    this.day = "",
    this.unit,
    this.workMoney,
    this.hour,
    this.contractor,
    this.businessType = const [],
    this.spotWork,
    this.otherExpensesNum = 0,
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}