
import 'dart:convert';

import 'package:gdjg_pure_flutter/utils/system_util/string_util.dart';

class CountBizModel {

  ///
  double id;
  ///
  String name;

  ///
  final SpotWorkBizModel? spotWork;

  ///
  final UnitBizModel? unit;

  ///
  final WorkMoneyBizModel? workMoney;

  ///
  final BorrowBizModel? borrow;

  ///
  final ContractorBizModel? contractor;

  ///
  final WageBizModel? wage;

  ///
  final HourBizModel? hour;

  ///
  double? unsettled;

  ///
  double? income;

  ///
  double num;

  List<OtherExpenseBizModel> otherExpenses;

  CountBizModel({
    this.id = 0.0,
    this.name = "",
    this.spotWork,
    this.unit,
    this.workMoney,
    this.borrow,
    this.contractor,
    this.wage,
    this.hour,
    this.unsettled = 0.0,
    this.income = 0.0,
    this.num = 0.0,
    this.otherExpenses = const [],
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class SpotWorkBizModel {
  /// 上班（工）
  String workTime;

  /// 上班（小时）
  String workTimeHour;

  /// 加班（小时）
  String overTime;

  /// 加班（工）
  String overTimeWork;

  ///
  double spotWorkFeeMoney;

  ///
  double num;

  SpotWorkBizModel({
    this.workTime = "",
    this.workTimeHour = "",
    this.overTime = "",
    this.overTimeWork = "",
    this.spotWorkFeeMoney = 0.0,
    this.num = 0.0,
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class UnitBizModel {
  ///
  List<CountUnitEntity> countUnit;

  ///
  double num;

  UnitBizModel({
    this.countUnit = const [],
    this.num = 0.0,
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class CountUnitEntity {
  String unit;
  String count;
  double num;
  String unitWorkType;
  String unitWorkTypeName;
  String unitWorkTypeUnit;
  String unitMoney;
  String lastUnitPrice;

  CountUnitEntity({
    this.unit = "",
    this.count = "",
    this.num = 0.0,
    this.unitWorkType = "",
    this.unitWorkTypeName = "",
    this.unitWorkTypeUnit = "",
    this.unitMoney = "",
    this.lastUnitPrice = "",
  });

  String getTitle() {
    return "工量 ${getTypeNameStr()}";
  }

  String getCountStr() {
    return "总计:$count ${getTypeUnitStr()}";
  }

  String getTypeUnitStr() {
    return unitWorkTypeUnit.isEmpty ? "" : unitWorkTypeUnit;
  }

  String getTypeNameStr() {
    return unitWorkTypeName.isEmpty ? "" : unitWorkTypeName;
  }

  ///最后的工量工价
  String getLastUnitPrice() {
    return lastUnitPrice.isEmpty ? "0.00" : lastUnitPrice.formatStringToMoney();
  }
}

class WorkMoneyBizModel {
  ///
  String workMoney;

  ///
  double num;

  WorkMoneyBizModel({
    this.workMoney = "",
    this.num = 0.0,
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class BorrowBizModel {
  ///
  String borrowCount;

  ///
  double num;

  BorrowBizModel({
    this.borrowCount = "0",
    this.num = 0.0,
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class ContractorBizModel {
  /// 包工（工）
  String contractorWorkTime;

  /// 包工（小时）
  String contractorWorkTimeHour;

  ///加班小时
  String contractorOverTime;

  ///
  double contractorMoney;

  ///
  double num;

  ContractorBizModel({
    this.contractorWorkTime = "0",
    this.contractorWorkTimeHour = "0",
    this.num = 0.0,
    this.contractorMoney = 0.0,
    this.contractorOverTime = "0",
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class WageBizModel {
  ///
  String wageCount;

  ///
  double num;

  WageBizModel({
    this.wageCount = '0.0',
    this.num = 0.0,
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class HourBizModel {
  ///
  double hourTime;

  ///
  double hourOvertime;

  ///
  double num;

  HourBizModel({
    this.hourTime = 0.0,
    this.hourOvertime = 0.0,
    this.num = 0.0,
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class OtherExpenseBizModel {

  /// 总额
  String money;

  /// 总数量
  String num;

  /// 费用名称
  String name;

  OtherExpenseBizModel({
    this.money = "",
    this.num = "",
    this.name = "",
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}