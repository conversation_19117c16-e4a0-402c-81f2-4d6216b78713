part of 'calendar_net_model.dart';

CalendarNetModel _$CalendarModelFromJson(Map<String, dynamic> json) =>
    CalendarNetModel()
      ..is_note = (json['is_note'] as num?)?.toInt()
      ..day = json['day'] as String?
      ..unit = json['unit'] == null
          ? null
          : UnitNetModel.fromJson(json['unit'] as Map<String, dynamic>)
      ..work_money = json['work_money'] == null
          ? null
          : WorkMoneyNetModel.fromJson(
              json['work_money'] as Map<String, dynamic>)
      ..hour = json['hour'] == null
          ? null
          : HourNetModel.fromJson(json['hour'] as Map<String, dynamic>)
      ..contractor = json['contractor'] == null
          ? null
          : ContractorNetModel.fromJson(
              json['contractor'] as Map<String, dynamic>)
      ..business_type = (json["business_type"] as List<dynamic>?)
          ?.map((e) => e as int)
          .toList()
      ..spot_work = json['spot_work'] == null
          ? null
          : SpotWorkNetModel.fromJson(json['spot_work'] as Map<String, dynamic>)
      ..other_expenses_num = (json['other_expenses_num'] as num?)?.toInt();

Map<String, dynamic> _$CalendarModelToJson(CalendarNetModel instance) =>
    <String, dynamic>{
      'is_note': instance.is_note,
      'day': instance.day,
      'unit': instance.unit,
      'work_money': instance.work_money,
      'hour': instance.hour,
      'contractor': instance.contractor,
      'business_type': instance.business_type,
      'spot_work': instance.spot_work,
      'other_expenses_num': instance.other_expenses_num,
    };
