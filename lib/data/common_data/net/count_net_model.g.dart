// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'count_net_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CountNetModel _$CountModelFromJson(Map<String, dynamic> json) => CountNetModel()
  ..id = (json['id'] as num?)?.toDouble()
  ..name = json['name'] as String?
  ..spot_work = json['spot_work'] == null
      ? null
      : SpotWorkNetModel.fromJson(json['spot_work'] as Map<String, dynamic>)
  ..unit = json['unit'] == null
      ? null
      : UnitNetModel.fromJson(json['unit'] as Map<String, dynamic>)
  ..work_money = json['work_money'] == null
      ? null
      : WorkMoneyNetModel.fromJson(json['work_money'] as Map<String, dynamic>)
  ..borrow = json['borrow'] == null
      ? null
      : BorrowNetModel.fromJson(json['borrow'] as Map<String, dynamic>)
  ..contractor = json['contractor'] == null
      ? null
      : ContractorNetModel.fromJson(json['contractor'] as Map<String, dynamic>)
  ..wage = json['wage'] == null
      ? null
      : WageNetModel.fromJson(json['wage'] as Map<String, dynamic>)
  ..hour = json['hour'] == null
      ? null
      : HourNetModel.fromJson(json['hour'] as Map<String, dynamic>)
  ..unsettled = (json['unsettled'] as num?)?.toDouble()
  ..income = (json['income'] as num?)?.toDouble()
  ..num = (json['num'] as num?)?.toDouble()
  ..other_expenses = (json["other_expenses"] as List<dynamic>?)
      ?.map((e) => OtherExpenseNetModel.fromJson(e as Map<String, dynamic>))
      .toList();

Map<String, dynamic> _$CountModelToJson(CountNetModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'spot_work': instance.spot_work,
      'unit': instance.unit,
      'work_money': instance.work_money,
      'borrow': instance.borrow,
      'contractor': instance.contractor,
      'wage': instance.wage,
      'hour': instance.hour,
      'unsettled': instance.unsettled,
      'income': instance.income,
      'num': instance.num,
      "other_expenses": instance.other_expenses,
    };

SpotWorkNetModel _$SpotWorkModelFromJson(Map<String, dynamic> json) =>
    SpotWorkNetModel()
      ..work_time = json['work_time']?.toString()
      ..work_time_hour = json['work_time_hour']?.toString()
      ..overtime = json['overtime']?.toString()
      ..overtime_work = json['overtime_work']?.toString()
      ..spot_work_fee_money = (json['spot_work_fee_money'] as num?)?.toDouble()
      ..num = (json['num'] as num?)?.toDouble();

Map<String, dynamic> _$SpotWorkModelToJson(SpotWorkNetModel instance) =>
    <String, dynamic>{
      'work_time': instance.work_time,
      'work_time_hour': instance.work_time_hour,
      'overtime': instance.overtime,
      'overtime_work': instance.overtime_work,
      'spot_work_fee_money': instance.spot_work_fee_money,
      'num': instance.num,
    };

// UnitModel的fromJson和toJson实现
UnitNetModel _$UnitModelFromJson(Map<String, dynamic> json) {
  return UnitNetModel()
    ..count_unit = (json['count_unit'] as List<dynamic>?)
        ?.map((e) => CountUnitModel.fromJson(e as Map<String, dynamic>))
        .toList()
    ..num = (json['num'] as num?)?.toDouble();
}

Map<String, dynamic> _$UnitModelToJson(UnitNetModel instance) {
  return <String, dynamic>{
    'count_unit': instance.count_unit,
    'num': instance.num,
  };
}

// CountUnitModel的fromJson和toJson实现
CountUnitModel _$CountUnitModelFromJson(Map<String, dynamic> json) {
  return CountUnitModel(
    unit: json['unit']?.toString(),
    count: json['count']?.toString(),
    num: json['num']?.toDouble(),
    unitWorkType: json['unit_work_type']?.toString(),
    unitWorkTypeName: json['unit_work_type_name']?.toString(),
    unitWorkTypeUnit: json['unit_work_type_unit']?.toString(),
    unitMoney: json['unit_money']?.toString(),
    lastUnitPrice: json['last_unit_price']?.toString(),
  );
}

Map<String, dynamic> _$CountUnitModelToJson(CountUnitModel instance) {
  return <String, dynamic>{
    'unit': instance.unit,
    'count': instance.count,
    'num': instance.num,
    'unitWorkType': instance.unitWorkType,
    'unitWorkTypeName': instance.unitWorkTypeName,
    'unitWorkTypeUnit': instance.unitWorkTypeUnit,
    'unitMoney': instance.unitMoney,
    'lastUnitPrice': instance.lastUnitPrice,
  };
}

WorkMoneyNetModel _$WorkMoneyModelFromJson(Map<String, dynamic> json) =>
    WorkMoneyNetModel()
      ..work_money = (json['work_money'])?.toString()
      ..num = json['num']?.toDouble();

Map<String, dynamic> _$WorkMoneyModelToJson(WorkMoneyNetModel instance) =>
    <String, dynamic>{
      'work_money': instance.work_money,
      'num': instance.num,
    };

BorrowNetModel _$BorrowModelFromJson(Map<String, dynamic> json) =>
    BorrowNetModel()
      ..borrow_count = json['borrow_count']?.toString()
      ..num = json['num']?.toDouble();

Map<String, dynamic> _$BorrowModelToJson(BorrowNetModel instance) =>
    <String, dynamic>{
      'borrow_count': instance.borrow_count,
      'num': instance.num,
    };

ContractorNetModel _$ContractorModelFromJson(Map<String, dynamic> json) =>
    ContractorNetModel()
      ..contractor_work_time = json['contractor_work_time']?.toString()
      ..contractor_work_time_hour =
          json['contractor_work_time_hour']?.toString()
      ..num = (json['num'] as num?)?.toDouble()
      ..contractor_money = json['contractor_money']?.toDouble()
      ..contractor_overtime = json['contractor_overtime']?.toString();

Map<String, dynamic> _$ContractorModelToJson(ContractorNetModel instance) =>
    <String, dynamic>{
      'contractor_work_time': instance.contractor_work_time,
      'contractor_work_time_hour': instance.contractor_work_time_hour,
      'num': instance.num,
      'contractor_money': instance.contractor_money,
      'contractor_overtime': instance.contractor_overtime,
    };

WageNetModel _$WageModelFromJson(Map<String, dynamic> json) => WageNetModel()
  ..wage_count = json['wage_count']?.toString()
  ..num = (json['num'] as num?)?.toDouble();

Map<String, dynamic> _$WageModelToJson(WageNetModel instance) =>
    <String, dynamic>{
      'wage_count': instance.wage_count,
      'num': instance.num,
    };

HourNetModel _$HourModelFromJson(Map<String, dynamic> json) => HourNetModel()
  ..hour_time = (json['hour_time'] as num?)?.toDouble()
  ..hour_overtime = (json['hour_overtime'] as num?)?.toDouble()
  ..num = (json['num'] as num?)?.toDouble();

Map<String, dynamic> _$HourModelToJson(HourNetModel instance) =>
    <String, dynamic>{
      'hour_time': instance.hour_time,
      'hour_overtime': instance.hour_overtime,
      'num': instance.num,
    };
