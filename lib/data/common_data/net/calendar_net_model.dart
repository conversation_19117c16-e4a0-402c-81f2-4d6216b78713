
import 'dart:convert';

import 'package:gdjg_pure_flutter/data/common_data/biz/calendar_biz_model.dart';
import 'package:gdjg_pure_flutter/data/common_data/net/count_net_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'calendar_net_model.g.dart';

@JsonSerializable()
class CalendarNetModel {
  /// 是否有备注
  int? is_note;

  /// 日期
  String? day;

  /// 工量
  UnitNetModel? unit;

  /// 短工
  WorkMoneyNetModel? work_money;

  /// 小时工
  HourNetModel? hour;

  /// 包工
  ContractorNetModel? contractor;

  /// 所有记工类型
  List<int>? business_type;

  /// 点工
  SpotWorkNetModel? spot_work;

  int? other_expenses_num;

  CalendarNetModel();

  factory CalendarNetModel.fromJson(Map<String, dynamic> json) =>
      _$CalendarModelFromJson(json);

  Map<String, dynamic> toJson() => _$CalendarModelToJson(this);

  CalendarEntity transform() {
    return CalendarEntity(
        isNote: is_note ?? 0,
        day: day ?? "",
        unit: unit?.transform(),
        workMoney: work_money?.transform(),
        hour: hour?.transform(),
        contractor: contractor?.transform(),
        businessType: business_type ?? [],
        spotWork: spot_work?.transform(),
        otherExpensesNum: other_expenses_num ?? 0);
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}