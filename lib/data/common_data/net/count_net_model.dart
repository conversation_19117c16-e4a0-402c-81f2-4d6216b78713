
import 'dart:convert';

import 'package:gdjg_pure_flutter/data/common_data/biz/count_biz_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'count_net_model.g.dart';

@JsonSerializable()
class CountNetModel {
  ///
  double? id;

  ///
  String? name;

  ///
  SpotWorkNetModel? spot_work;

  ///
  UnitNetModel? unit;

  ///
  WorkMoneyNetModel? work_money;

  ///
  BorrowNetModel? borrow;

  ///
  ContractorNetModel? contractor;

  ///
  WageNetModel? wage;

  ///
  HourNetModel? hour;

  ///
  double? unsettled;

  ///
  double? income;

  ///
  double? num;

  List<OtherExpenseNetModel>? other_expenses;

  CountNetModel();

  factory CountNetModel.fromJson(Map<String, dynamic> json) =>
      _$CountModelFromJson(json);

  Map<String, dynamic> toJson() => _$CountModelToJson(this);

  CountBizModel transform() {
    return CountBizModel(
      id: id ?? 0.0,
      name: name ?? "",
      spotWork: spot_work?.transform(),
      unit: unit?.transform(),
      workMoney: work_money?.transform(),
      borrow: borrow?.transform(),
      contractor: contractor?.transform(),
      wage: wage?.transform(),
      hour: hour?.transform(),
      unsettled: unsettled ?? 0.0,
      income: income ?? 0.0,
      num: num ?? 0.0,
      otherExpenses: other_expenses?.map((e) => e.transform()).toList() ?? [],
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class SpotWorkNetModel {
  /// 上班（工）
  String? work_time;

  /// 上班（小时）
  String? work_time_hour;

  /// 加班（工）
  String? overtime;

  /// 加班（小时）
  String? overtime_work;

  ///
  double? spot_work_fee_money;

  ///
  double? num;

  SpotWorkNetModel();

  factory SpotWorkNetModel.fromJson(Map<String, dynamic> json) =>
      _$SpotWorkModelFromJson(json);

  Map<String, dynamic> toJson() => _$SpotWorkModelToJson(this);

  SpotWorkBizModel transform() {
    return SpotWorkBizModel(
      workTime: work_time ?? "",
      workTimeHour: work_time_hour ?? "",
      overTime: overtime ?? "",
      overTimeWork: overtime_work ?? "",
      spotWorkFeeMoney: spot_work_fee_money ?? 0.0,
      num: num ?? 0.0,
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class UnitNetModel {
  ///
  List<CountUnitModel>? count_unit;

  ///
  double? num;

  UnitNetModel();

  factory UnitNetModel.fromJson(Map<String, dynamic> json) =>
      _$UnitModelFromJson(json);

  Map<String, dynamic> toJson() => _$UnitModelToJson(this);

  UnitBizModel transform() {
    return UnitBizModel(
      countUnit: count_unit?.map((e) => e.transform()).toList() ?? [],
      num: num ?? 0.0,
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class CountUnitModel {
  String? unit;
  String? count;
  double? num;
  String? unitWorkType;
  String? unitWorkTypeName;
  String? unitWorkTypeUnit;
  String? unitMoney;
  String? lastUnitPrice;

  CountUnitModel({
    this.unit,
    this.count,
    this.num,
    this.unitWorkType,
    this.unitWorkTypeName,
    this.unitWorkTypeUnit,
    this.unitMoney,
    this.lastUnitPrice,
  });

  factory CountUnitModel.fromJson(Map<String, dynamic> json) =>
      _$CountUnitModelFromJson(json);

  Map<String, dynamic> toJson() => _$CountUnitModelToJson(this);

  CountUnitEntity transform() {
    return CountUnitEntity(
      unit: unit ?? "",
      count: count ?? "",
      num: num?.toDouble() ?? 0.00,
      unitWorkType: unitWorkType ?? "",
      unitWorkTypeName: unitWorkTypeName ?? "",
      unitWorkTypeUnit: unitWorkTypeUnit ?? "",
      unitMoney: unitMoney ?? "",
      lastUnitPrice: lastUnitPrice ?? "",
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class WorkMoneyNetModel {
  ///
  String? work_money;

  ///
  double? num;

  WorkMoneyNetModel();

  factory WorkMoneyNetModel.fromJson(Map<String, dynamic> json) =>
      _$WorkMoneyModelFromJson(json);

  Map<String, dynamic> toJson() => _$WorkMoneyModelToJson(this);

  WorkMoneyBizModel transform() {
    return WorkMoneyBizModel(
      workMoney: work_money ?? "",
      num: num ?? 0.0,
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class BorrowNetModel {
  ///
  String? borrow_count;

  ///
  double? num;

  BorrowNetModel();

  factory BorrowNetModel.fromJson(Map<String, dynamic> json) =>
      _$BorrowModelFromJson(json);

  Map<String, dynamic> toJson() => _$BorrowModelToJson(this);

  BorrowBizModel transform() {
    return BorrowBizModel(
      borrowCount: borrow_count ?? "0",
      num: num ?? 0.0,
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class ContractorNetModel {
  ///
  String? contractor_work_time;

  ///
  String? contractor_work_time_hour;

  ///
  String? contractor_overtime;

  double? contractor_money;

  ///
  double? num;

  ContractorNetModel();

  factory ContractorNetModel.fromJson(Map<String, dynamic> json) =>
      _$ContractorModelFromJson(json);

  Map<String, dynamic> toJson() => _$ContractorModelToJson(this);

  ContractorBizModel transform() {
    return ContractorBizModel(
      contractorWorkTime: contractor_work_time ?? "0.0",
      contractorWorkTimeHour: contractor_work_time_hour ?? "0.0",
      num: num ?? 0.0,
      contractorMoney: contractor_money ?? 0.0,
      contractorOverTime: contractor_overtime ?? "0.0",
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class WageNetModel {
  ///
  String? wage_count;

  ///
  double? num;

  WageNetModel();

  factory WageNetModel.fromJson(Map<String, dynamic> json) =>
      _$WageModelFromJson(json);

  Map<String, dynamic> toJson() => _$WageModelToJson(this);

  WageBizModel transform() {
    return WageBizModel(
      wageCount: wage_count ?? '0.0',
      num: num ?? 0.0,
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class HourNetModel {
  ///
  double? hour_time;

  ///
  double? hour_overtime;

  ///
  double? num;

  HourNetModel();

  factory HourNetModel.fromJson(Map<String, dynamic> json) =>
      _$HourModelFromJson(json);

  Map<String, dynamic> toJson() => _$HourModelToJson(this);

  HourBizModel transform() {
    return HourBizModel(
      hourTime: hour_time ?? 0.0,
      hourOvertime: hour_overtime ?? 0.0,
      num: num ?? 0.0,
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class OtherExpenseNetModel {
  /// 总额
  String? money;

  /// 数量
  String? num;

  /// 费用名称
  String? name;

  OtherExpenseNetModel();

  Map<String, dynamic> toJson(OtherExpenseNetModel instance) =>
      <String, dynamic>{
        "money": instance.money,
        "num": instance.num,
        "name": instance.name,
      };

  factory OtherExpenseNetModel.fromJson(Map<String, dynamic> json) {
    var netModel = OtherExpenseNetModel();
    netModel.money = json["money"]?.toString();
    netModel.num = json["num"]?.toString();
    netModel.name = json["name"]?.toString();
    return netModel;
  }

  OtherExpenseBizModel transform() {
    return OtherExpenseBizModel(
      money: money ?? "",
      num: num ?? "",
      name: name ?? "",
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}