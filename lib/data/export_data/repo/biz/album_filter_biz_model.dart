import 'dart:convert';

class AlbumFilterBizModel {

  /// 项目工友信息
  List<WorkerNotesListBizModel> workerNotesList;

  AlbumFilterBizModel({
    this.workerNotesList = const [],
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class WorkerNotesListBizModel {

  /// 记账本ID
  double workNote;

  /// 项目名称
  String name;

  /// 项目类型 1班组 2个人
  double identity;

  /// 是否结清 1是 0否
  double isIgnored;

  /// 是否是创建者 1是 0否
  double isSelfCreated;

  /// 是否是带班  1是 0否
  double isAgent;

  /// 工人id
  double workerId;

  /// 工友列表
  List<WorkerListBizModel> workerList;

  WorkerNotesListBizModel({
    this.workNote = 0.0,
    this.name = "",
    this.identity = 0.0,
    this.isIgnored = 0.0,
    this.isSelfCreated = 0.0,
    this.isAgent = 0.0,
    this.workerId = 0.0,
    this.workerList = const [],
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class WorkerListBizModel {

  /// 工友ID
  double workerId;

  /// 工友名称
  String workerName;

  /// 工人头像
  String workerAvatar;

  WorkerListBizModel({
    this.workerId = 0.0,
    this.workerName = "",
    this.workerAvatar = "",
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}

