import 'package:gdjg_pure_flutter/data/export_data/ds/export_rds.dart';
import 'package:gdjg_pure_flutter/data/export_data/ds/model/param/attendance_download_excel_param_model.dart';
import 'package:gdjg_pure_flutter/data/export_data/repo/biz/album_filter_biz_model.dart';
import 'package:gdjg_pure_flutter/data/export_data/repo/biz/attendance_download_excel_biz_model.dart';
import 'package:net_plugin/pigeon/net_result/resp_result.dart';

class ExportRepo {
  final ExportRds _exportRds = ExportRds();

  Future<RespResult<AlbumFilterBizModel?>> fetchWorkerAndWorker() async {
    var result = await _exportRds.fetchWorkerAndWorker();
    return result.map((i) => i?.transform());
  }

  Future<RespResult<AttendanceDownloadExcelBizModel?>> fetchReportLink(AttendanceDownloadExcelParamModel params) async {
    var result = await _exportRds.fetchReportLink(params);
    return result.map((i) => i?.transform());
  }
}