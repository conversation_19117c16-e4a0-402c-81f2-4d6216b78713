import 'package:gdjg_pure_flutter/data/export_data/ds/model/net/album_filter_net_model.dart';
import 'package:gdjg_pure_flutter/data/export_data/ds/model/net/attendance_download_excel_net_model.dart';
import 'package:gdjg_pure_flutter/data/export_data/ds/model/param/attendance_download_excel_param_model.dart';
import 'package:net_plugin/pigeon/api_generated/net_channel.g.dart';
import 'package:net_plugin/pigeon/net_result/resp_result.dart';
import 'package:net_plugin/pigeon/net_service/net_core.dart';

class ExportRds {
  ///获取全部项目和工友信息
  Future<RespResult<AlbumFilterNetModel>> fetchWorkerAndWorker() async {
    return await NetCore.requestJGPHP(
        BaseBizRequestEntity(
            url: '/api/v3/album/filter',
            method: HTTP_METHOD.GET,
            requestExtra: RequestExtra(showLoading: true, printResp: true)),
        (json) => AlbumFilterNetModel.fromJson(json));
  }

  ///获取报表链接
  Future<RespResult<AttendanceDownloadExcelNetModel>> fetchReportLink(
      AttendanceDownloadExcelParamModel params) async {
    return await NetCore.requestJGPHP(
        BaseBizRequestEntity(
            url: '/api/attendance/download_excel',
            method: HTTP_METHOD.GET,
            content: params.toMap(),
            requestExtra: RequestExtra(showLoading: true, printResp: true)),
        (json) => AttendanceDownloadExcelNetModel.fromJson(json));
  }
}
