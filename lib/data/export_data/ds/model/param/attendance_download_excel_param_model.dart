class AttendanceDownloadExcelParamModel {
  /// 部门id
  final String? work_note;

  /// 开始的年月日
  final String? start_date;

  /// 结束的年月日
  final String? end_date;

  /// 是班组长或代班下载的，还是工人下载的（1班组长或者代班       2工人）
  final String? worker_id;
  final String? return_url;
  final String? type;

  AttendanceDownloadExcelParamModel({
    this.work_note,
    this.start_date,
    this.end_date,
    this.worker_id,
    this.return_url,
    this.type,
  });

  Map<String, Object> toMap() {
    var map = <String, Object>{};
    if (work_note != null) map["work_note"] = work_note!;
    if (start_date != null) map["start_date"] = start_date!;
    if (end_date != null) map["end_date"] = end_date!;
    if (worker_id != null) map["worker_id"] = worker_id!;
    if (return_url != null) map["return_url"] = return_url!;
    if (type != null) map["type"] = type!;
    return map;
  }
}
