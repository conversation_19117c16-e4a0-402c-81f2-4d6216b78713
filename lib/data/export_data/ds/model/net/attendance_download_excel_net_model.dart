import 'dart:convert';
import '../../../repo/biz/attendance_download_excel_biz_model.dart';

class AttendanceDownloadExcelNetModel {
  String? url;
  String? file_name;

  AttendanceDownloadExcelNetModel();

  Map<String, dynamic> toJson(AttendanceDownloadExcelNetModel instance) {
    var map = <String, Object>{};
    if (url != null) map["url"] = url!;
    if (file_name != null) map["file_name"] = file_name!;
    return map;
  }

  factory AttendanceDownloadExcelNetModel.fromJson(Map<String, dynamic> json) {
    var netModel = AttendanceDownloadExcelNetModel();
    netModel.url = json["url"]?.toString();
    netModel.file_name = json["file_name"]?.toString();
    return netModel;
  }

  AttendanceDownloadExcelBizModel transform() {
    return AttendanceDownloadExcelBizModel(
      url: url ?? "",
      fileName: file_name ?? "",
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

