import 'dart:convert';

import 'package:gdjg_pure_flutter/data/export_data/repo/biz/album_filter_biz_model.dart';

class AlbumFilterNetModel {

  /// 项目工友信息
  List<WorkerNotesListNetModel>? worker_notes_list;

  AlbumFilterNetModel();

  Map<String, dynamic> toJson(AlbumFilterNetModel instance) {
    var map = <String, Object>{};
    if (worker_notes_list != null) map["worker_notes_list"] = worker_notes_list!;
    return map;
  }

  factory AlbumFilterNetModel.fromJson(Map<String, dynamic> json) {
    var netModel = AlbumFilterNetModel();
    netModel.worker_notes_list = (json["worker_notes_list"] as List<dynamic>?)
      ?.map((e) => WorkerNotesListNetModel.fromJson(e as Map<String, dynamic>))
      .toList();
    return netModel;
  }

  AlbumFilterBizModel transform() {
    return AlbumFilterBizModel(
      workerNotesList: worker_notes_list?.map((e) => e.transform()).toList() ?? [],
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class WorkerNotesListNetModel {

  /// 记账本ID
  double? work_note;

  /// 项目名称
  String? name;

  /// 项目类型 1班组 2个人
  double? identity;

  /// 是否结清 1是 0否
  double? is_ignored;

  /// 是否是创建者 1是 0否
  double? is_self_created;

  /// 是否是带班  1是 0否
  double? is_agent;

  /// 工人id
  double? worker_id;

  /// 工友列表
  List<WorkerListNetModel>? worker_list;

  WorkerNotesListNetModel();

  Map<String, dynamic> toJson(WorkerNotesListNetModel instance) {
    var map = <String, Object>{};
    if (work_note != null) map["work_note"] = work_note!;
    if (name != null) map["name"] = name!;
    if (identity != null) map["identity"] = identity!;
    if (is_ignored != null) map["is_ignored"] = is_ignored!;
    if (is_self_created != null) map["is_self_created"] = is_self_created!;
    if (is_agent != null) map["is_agent"] = is_agent!;
    if (worker_id != null) map["worker_id"] = worker_id!;
    if (worker_list != null) map["worker_list"] = worker_list!;
    return map;
  }

  factory WorkerNotesListNetModel.fromJson(Map<String, dynamic> json) {
    var netModel = WorkerNotesListNetModel();
    netModel.work_note = double.tryParse(json["work_note"].toString());
    netModel.name = json["name"]?.toString();
    netModel.identity = double.tryParse(json["identity"].toString());
    netModel.is_ignored = double.tryParse(json["is_ignored"].toString());
    netModel.is_self_created = double.tryParse(json["is_self_created"].toString());
    netModel.is_agent = double.tryParse(json["is_agent"].toString());
    netModel.worker_id = double.tryParse(json["worker_id"].toString());
    netModel.worker_list = (json["worker_list"] as List<dynamic>?)
      ?.map((e) => WorkerListNetModel.fromJson(e as Map<String, dynamic>))
      .toList();
    return netModel;
  }

  WorkerNotesListBizModel transform() {
    return WorkerNotesListBizModel(
      workNote: work_note ?? 0.0,
      name: name ?? "",
      identity: identity ?? 0.0,
      isIgnored: is_ignored ?? 0.0,
      isSelfCreated: is_self_created ?? 0.0,
      isAgent: is_agent ?? 0.0,
      workerId: worker_id ?? 0.0,
      workerList: worker_list?.map((e) => e.transform()).toList() ?? [],
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class WorkerListNetModel {

  /// 工友ID
  double? worker_id;

  /// 工友名称
  String? worker_name;

  /// 工人头像
  String? worker_avatar;

  WorkerListNetModel();

  Map<String, dynamic> toJson(WorkerListNetModel instance) {
    var map = <String, Object>{};
    if (worker_id != null) map["worker_id"] = worker_id!;
    if (worker_name != null) map["worker_name"] = worker_name!;
    if (worker_avatar != null) map["worker_avatar"] = worker_avatar!;
    return map;
  }

  factory WorkerListNetModel.fromJson(Map<String, dynamic> json) {
    var netModel = WorkerListNetModel();
    netModel.worker_id = double.tryParse(json["worker_id"].toString());
    netModel.worker_name = json["worker_name"]?.toString();
    netModel.worker_avatar = json["worker_avatar"]?.toString();
    return netModel;
  }

  WorkerListBizModel transform() {
    return WorkerListBizModel(
      workerId: worker_id ?? 0.0,
      workerName: worker_name ?? "",
      workerAvatar: worker_avatar ?? "",
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

