import '../../../repo/model/member_get_related_tel_new_biz_model.dart';

class MemberGetRelatedTelNewNetModel {
  List<MemberGetRelatedTelNewANetModel>? list;

  MemberGetRelatedTelNewNetModel();

  Map<String, dynamic> toJson(MemberGetRelatedTelNewNetModel instance) {
    var map = <String, Object>{};
    if (list != null) map["list"] = list!;
    return map;
  }

  factory MemberGetRelatedTelNewNetModel.fromJson(Map<String, dynamic> json) {
    var netModel = MemberGetRelatedTelNewNetModel();
    netModel.list = (json["list"] as List<dynamic>?)
        ?.map((e) =>
            MemberGetRelatedTelNewANetModel.fromJson(e as Map<String, dynamic>))
        .toList();
    return netModel;
  }

  MemberGetRelatedTelNewBizModel transform() {
    return MemberGetRelatedTelNewBizModel(
      list: list?.map((e) => e.transform()).toList() ?? [],
    );
  }
}

class MemberGetRelatedTelNewANetModel {
  /// 展示手机号
  String? tel;

  /// 手机hash
  String? tel_hash;

  MemberGetRelatedTelNewANetModel();

  Map<String, dynamic> toJson(MemberGetRelatedTelNewANetModel instance) {
    var map = <String, Object>{};
    if (tel != null) map["tel"] = tel!;
    if (tel_hash != null) map["tel_hash"] = tel_hash!;
    return map;
  }

  factory MemberGetRelatedTelNewANetModel.fromJson(Map<String, dynamic> json) {
    var netModel = MemberGetRelatedTelNewANetModel();
    netModel.tel = json["tel"]?.toString();
    netModel.tel_hash = json["tel_hash"]?.toString();
    return netModel;
  }

  MemberGetRelatedTelANewBizModel transform() {
    return MemberGetRelatedTelANewBizModel(
      tel: tel ?? "",
      telHash: tel_hash ?? "",
    );
  }
}
