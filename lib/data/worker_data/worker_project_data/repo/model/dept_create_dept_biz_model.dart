import 'dart:convert';

class DeptCreateDeptBizModel {

  /// 企业 id
  double corpId;

  /// 创建时间时间戳（秒级精度）
  double createTime;

  /// 班组 id
  double deptId;

  /// 班组类型，1-多人，2-个人
  double identity;

  /// 是否暂停更新，0-否，1-是
  double isDeleted;

  /// 是否已结束，0-否，1-是
  double isIgnored;

  /// 是否是我创建，0-否，1-是
  double isSelfCreated;

  /// 创建者用户 id
  double leaderUid;

  /// 班组名称
  String name;

  /// 
  double noteIdentity;

  /// 排序字段，暂无用
  double order;

  /// 用户在班组内的 user_id
  double selfUser;

  /// 账本工资开关是否打开，1-打开，2-关闭
  double workNoteFeeSwitch;

  /// 账本 id
  double workNoteId;

  /// 当前用户工人 id
  double workerId;

  /// 班组内工友数量
  double workerNum;

  DeptCreateDeptBizModel({
    this.corpId = 0.0,
    this.createTime = 0.0,
    this.deptId = 0.0,
    this.identity = 0.0,
    this.isDeleted = 0.0,
    this.isIgnored = 0.0,
    this.isSelfCreated = 0.0,
    this.leaderUid = 0.0,
    this.name = "",
    this.noteIdentity = 0.0,
    this.order = 0.0,
    this.selfUser = 0.0,
    this.workNoteFeeSwitch = 0.0,
    this.workNoteId = 0.0,
    this.workerId = 0.0,
    this.workerNum = 0.0,
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}
