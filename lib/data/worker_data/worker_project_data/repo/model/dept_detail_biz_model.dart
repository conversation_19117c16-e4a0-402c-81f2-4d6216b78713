import 'dart:convert';

class DeptDetailBizModel {

  /// 班组 id
  double deptId;

  /// 班组创建时间（秒级精度）
  double createTime;

  /// 企业 id
  double corpId;

  /// 班组名称
  String name;

  /// 班组长 user_id
  double leaderUid;

  /// 班组类型 1-多人 2-个人
  double identity;

  /// 是否已停止更新数据 0-否 1-是
  double isDeleted;

  /// 是否是我代班 0-否 1-是
  double isAgent;

  /// 是否我创建的 0-否 1-是
  double isSelfCreated;

  /// [4.6]新增 是否隐藏工资 0-否 1-是
  double hideMoney;

  /// 当前用户在班组内 user_id
  double selfUser;

  /// 当前用户在班组的工人 id
  double workerId;

  /// 班组内工友数量
  double workerNum;

  /// 账本的工资开关 0-关闭 1-打开
  double workNoteFeeSwitch;

  /// 账本 id
  double workNoteId;

  /// 账本的工资开关 0-关闭 1-打开
  double feeSwitch;

  /// 0未结束，1已结束
  double isIgnored;

  /// 今日是否记工，1是，0否 （目前仅用于个人本的判断逻辑）
  double businessToday;

  /// 最后一次记账时间
  String lastBusinessDate;

  DeptDetailBizModel({
    this.deptId = 0.0,
    this.createTime = 0.0,
    this.corpId = 0.0,
    this.name = "",
    this.leaderUid = 0.0,
    this.identity = 0.0,
    this.isDeleted = 0.0,
    this.isAgent = 0.0,
    this.isSelfCreated = 0.0,
    this.hideMoney = 0.0,
    this.selfUser = 0.0,
    this.workerId = 0.0,
    this.workerNum = 0.0,
    this.workNoteFeeSwitch = 0.0,
    this.workNoteId = 0.0,
    this.feeSwitch = 0.0,
    this.isIgnored = 0.0,
    this.businessToday = 0.0,
    this.lastBusinessDate = "",
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}
