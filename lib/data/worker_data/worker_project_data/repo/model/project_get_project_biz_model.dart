import 'dart:convert';

class ProjectGetProjectBizModel {

  /// 项目id
  double id;

  ///
  String name;

  /// 0 在建，1结清
  double status;

  /// 部门id
  double deptId;

  ///
  String startTime;

  ///
  String endTime;

  /// 点工工价
  FeeStandardBizModel? feeStandard;

  /// 企业id
  double corpId;

  /// 工人id
  double workerId;

  /// 工资规则状态：0-关闭 1-开启
  double feeSwitch;

  /// 包工工价
  ContractorFeeStandardBizModel? contractorFeeStandard;

  /// [4.6]新增 最后流水日期
  String lastBusinessDate;

  ProjectGetProjectBizModel({
    this.id = 0.0,
    this.name = "",
    this.status = 0.0,
    this.deptId = 0.0,
    this.startTime = "",
    this.endTime = "",
    this.feeStandard,
    this.corpId = 0.0,
    this.workerId = 0.0,
    this.feeSwitch = 0.0,
    this.contractorFeeStandard,
    this.lastBusinessDate = "",
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class FeeStandardBizModel {

  ///
  String workingHoursStandard;

  ///
  String workingHoursPrice;

  ///
  String overtimeHoursStandard;

  ///
  String overtimeHoursPrice;

  ///
  double overtimeType;

  ///
  double businessType;

  ///
  double feeSwitch;

  ///
  double feeStandardId;

  FeeStandardBizModel({
    this.workingHoursStandard = "",
    this.workingHoursPrice = "",
    this.overtimeHoursStandard = "",
    this.overtimeHoursPrice = "",
    this.overtimeType = 0.0,
    this.businessType = 0.0,
    this.feeSwitch = 0.0,
    this.feeStandardId = 0.0,
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class ContractorFeeStandardBizModel {

  ///
  String workingHoursStandard;

  ///
  String workingHoursPrice;

  ///
  String overtimeHoursStandard;

  ///
  String overtimeHoursPrice;

  ///
  double overtimeType;

  ///
  double businessType;

  ///
  double feeSwitch;

  ///
  double feeStandardId;

  ContractorFeeStandardBizModel({
    this.workingHoursStandard = "",
    this.workingHoursPrice = "",
    this.overtimeHoursStandard = "",
    this.overtimeHoursPrice = "",
    this.overtimeType = 0.0,
    this.businessType = 0.0,
    this.feeSwitch = 0.0,
    this.feeStandardId = 0.0,
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}

