import 'dart:convert';

import '../../ds/model/net/dept_create_dept_net_model.dart';

class DeptCreateDeptEntity {
  /// 
  double code;
  /// 
  String msg;
  /// 
  final DeptCreateDeptAEntity? data;

  DeptCreateDeptEntity({
    this.code = 0.0,
    this.msg = "",
    this.data = null,
  });

  DeptCreateDeptEntity transform(DeptCreateDeptModel? model){
    return DeptCreateDeptEntity(
      code: model?.code ?? 0.0,
      msg: model?.msg ?? "",
      data: DeptCreateDeptAEntity().transform(model?.data),
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class DeptCreateDeptAEntity {
  /// 企业 id
  double corpId;
  /// 创建时间时间戳（秒级精度）
  double createTime;
  /// 班组 id
  double deptId;
  /// 班组类型，1-多人，2-个人
  double identity;
  /// 是否暂停更新，0-否，1-是
  double isDeleted;
  /// 是否已结束，0-否，1-是
  double isIgnored;
  /// 是否是我创建，0-否，1-是
  double isSelfCreated;
  /// 创建者用户 id
  double leaderUid;
  /// 班组名称
  String name;
  /// 
  double noteIdentity;
  /// 排序字段，暂无用
  double order;
  /// 用户在班组内的 user_id
  double selfUser;
  /// 账本工资开关是否打开，1-打开，2-关闭
  double workNoteFeeSwitch;
  /// 账本 id
  double workNoteId;
  /// 当前用户工人 id
  String workerId;
  /// 班组内工友数量
  double workerNum;

  DeptCreateDeptAEntity({
    this.corpId = 0.0,
    this.createTime = 0.0,
    this.deptId = 0.0,
    this.identity = 0.0,
    this.isDeleted = 0.0,
    this.isIgnored = 0.0,
    this.isSelfCreated = 0.0,
    this.leaderUid = 0.0,
    this.name = "",
    this.noteIdentity = 0.0,
    this.order = 0.0,
    this.selfUser = 0.0,
    this.workNoteFeeSwitch = 0.0,
    this.workNoteId = 0.0,
    this.workerId = "",
    this.workerNum = 0.0,
  });

  DeptCreateDeptAEntity transform(DeptCreateDeptAModel? model){
    return DeptCreateDeptAEntity(
      corpId: model?.corp_id ?? 0.0,
      createTime: model?.create_time ?? 0.0,
      deptId: model?.dept_id ?? 0.0,
      identity: model?.identity ?? 0.0,
      isDeleted: model?.is_deleted ?? 0.0,
      isIgnored: model?.is_ignored ?? 0.0,
      isSelfCreated: model?.is_self_created ?? 0.0,
      leaderUid: model?.leader_uid ?? 0.0,
      name: model?.name ?? "",
      noteIdentity: model?.note_identity ?? 0.0,
      order: model?.order ?? 0.0,
      selfUser: model?.self_user ?? 0.0,
      workNoteFeeSwitch: model?.work_note_fee_switch ?? 0.0,
      workNoteId: model?.work_note_id ?? 0.0,
      workerId: model?.worker_id ?? "",
      workerNum: model?.worker_num ?? 0.0,
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

