import 'dart:convert';

class ProjectGetProjectListBizModel {
  /// 在建项目
  final DoingBizModel? doing;
  /// 已结清项目
  final EndBizModel? end;

  ProjectGetProjectListBizModel({
    this.doing = null,
    this.end = null,
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class DoingBizModel {
  /// 总未结
  double all;
  /// 
  List<ProjectGetProjectListABizModel> list;

  DoingBizModel({
    this.all = 0.0,
    this.list = const [],
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class ProjectGetProjectListABizModel {
  /// work_note
  double id;
  /// 项目名称
  String name;
  /// 状态 0-在建 1-结清
  double status;
  /// 工资规则 id
  double feeStandardId;
  /// 是否有点工 0-否 1-是
  double hasSpotWork;
  /// 未结
  double unsettled;
  /// 收入
  double income;
  /// 是否有短工 0-否 1-是
  double hasMoney;
  /// 第一笔流水日期
  String startTime;
  /// 最后一笔流水日期
  String endTime;
  /// 最后操作日期
  String lastOperationDate;
  /// 最后流水日期
  String lastBusinessDate;
  /// 企业 id
  double corpId;
  /// 班组 id
  double deptId;
  /// [4.6]是否有过记工/记账 0-没有 1-有
  double hasBusiness;

  ProjectGetProjectListABizModel({
    this.id = 0.0,
    this.name = "",
    this.status = 0.0,
    this.feeStandardId = 0.0,
    this.hasSpotWork = 0.0,
    this.unsettled = 0.0,
    this.income = 0.0,
    this.hasMoney = 0.0,
    this.startTime = "",
    this.endTime = "",
    this.lastOperationDate = "",
    this.lastBusinessDate = "",
    this.corpId = 0.0,
    this.deptId = 0.0,
    this.hasBusiness = 0.0,
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class EndBizModel {
  /// 总收入
  double all;
  /// 
  List<ProjectGetProjectListBBizModel> list;

  EndBizModel({
    this.all = 0.0,
    this.list = const [],
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class ProjectGetProjectListBBizModel {
  /// 
  double id;
  /// 
  String name;
  /// 
  double status;
  /// 
  double feeStandardId;
  /// 
  double hasSpotWork;
  /// 
  double unsettled;
  /// 
  double income;
  /// 
  double hasMoney;
  /// 第一笔流水日期
  String startTime;
  /// 最后一笔流水日期
  String endTime;
  /// 最后操作日期
  String lastOperationDate;
  /// 最后流水日期
  String lastBusinessDate;
  /// 企业 id
  double corpId;
  /// 班组 id
  double deptId;
  /// [4.6]是否有过记工/记账 0-没有 1-有
  double hasBusiness;

  ProjectGetProjectListBBizModel({
    this.id = 0.0,
    this.name = "",
    this.status = 0.0,
    this.feeStandardId = 0.0,
    this.hasSpotWork = 0.0,
    this.unsettled = 0.0,
    this.income = 0.0,
    this.hasMoney = 0.0,
    this.startTime = "",
    this.endTime = "",
    this.lastOperationDate = "",
    this.lastBusinessDate = "",
    this.corpId = 0.0,
    this.deptId = 0.0,
    this.hasBusiness = 0.0,
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}

