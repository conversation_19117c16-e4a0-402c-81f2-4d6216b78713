import 'dart:convert';

class DeptListGroupBizModel {
  ///
  List<DeptListGroupABizModel> list;
  /// 结束账本数量
  double ignoredNum;

  DeptListGroupBizModel({
    this.list = const [],
    this.ignoredNum = 0.0,
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class DeptListGroupABizModel {
  /// 记工本id
  double workNoteId;
  /// 1自己创建，0非自己创建
  double isSelfCreated;
  /// 1已结束，0未结束
  double isIgnored;
  /// 1是工人，0不是工人
  double isWorker;
  /// 企业id
  double corpId;
  /// 账本名称
  String name;
  /// 创建时间
  double createTime;
  /// 
  double selfUser;
  /// 工人数量
  double workerNum;
  /// 创建时间
  String createTimeStr;
  /// 工人id
  double workerId;
  /// 1今日记工，0今日未记工
  double businessToday;
  /// 上次记工时间
  String lastBusinessDate;
  /// 上次操作时间
  String lastOperationDate;
  /// 
  double sortInt;
  /// 1上次记工，0非上次记工
  double lastBookkeeping;
  /// 1显示打卡按钮，0不显示打卡按钮
  double showDkButton;
  /// 部门id
  double deptId;
  /// 1班组，2个人
  double identity;
  /// 1是代班，0不是代班
  double isAgent;
  /// 账本的工资开关 1 打开， 2关闭
  double feeSwitch;
  /// [4.6]仅在我参与项目中出现 项目是否隐藏工资 0-不隐藏 1-隐藏
  double hideMoney;
  /// [4.6]是否有过记工/记账 0-没有 1-有
  double hasBusiness;

  DeptListGroupABizModel({
    this.workNoteId = 0.0,
    this.isSelfCreated = 0.0,
    this.isIgnored = 0.0,
    this.isWorker = 0.0,
    this.corpId = 0.0,
    this.name = "",
    this.createTime = 0.0,
    this.selfUser = 0.0,
    this.workerNum = 0.0,
    this.createTimeStr = "",
    this.workerId = 0.0,
    this.businessToday = 0.0,
    this.lastBusinessDate = "",
    this.lastOperationDate = "",
    this.sortInt = 0.0,
    this.lastBookkeeping = 0.0,
    this.showDkButton = 0.0,
    this.deptId = 0.0,
    this.identity = 0.0,
    this.isAgent = 0.0,
    this.feeSwitch = 0.0,
    this.hideMoney = 0.0,
    this.hasBusiness = 0.0,
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}

