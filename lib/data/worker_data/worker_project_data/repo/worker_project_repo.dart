import 'package:gdjg_pure_flutter/data/worker_data/worker_project_data/ds/model/param/dept_create_dept_req_entity.dart';
import 'package:gdjg_pure_flutter/data/worker_data/worker_project_data/ds/model/param/project_get_project_list_param_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/worker_project_data/ds/rds/worker_project_rds.dart';
import 'package:gdjg_pure_flutter/data/worker_data/worker_project_data/repo/model/dept_create_dept_entity.dart';
import 'package:gdjg_pure_flutter/data/worker_data/worker_project_data/repo/model/dept_list_group_biz_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/worker_project_data/repo/model/project_get_project_list_biz_model.dart';
import 'package:net_plugin/pigeon/net_result/resp_result.dart';

class WorkerProjectRep {
  WorkerProjectRds rds = WorkerProjectRds();

  //获取项目列表
  Future<RespResult<ProjectGetProjectListBizModel>> queryProjectList(
      ProjectGetProjectListParamModel req,
      ) async {
    final result = await rds.queryProjectList(req);
    return result.map((netModel) => netModel?.transform() ?? ProjectGetProjectListBizModel());
  }

  //新建项目
  Future<RespResult<DeptCreateDeptEntity>> createProject(
      DeptCreateDeptReqEntity req,
      ) async {
    final result = await rds.createProject(req);
    return result.map((netModel) => DeptCreateDeptEntity().transform(netModel));
  }

  //获取我参与的项目列表
  Future<RespResult<DeptListGroupBizModel>> queryParticipatedProjectList() async {
    final result = await rds.queryParticipatedProjectList();
    return result.map((netModel) => netModel?.transform() ?? DeptListGroupBizModel());
  }
}


