import 'package:gdjg_pure_flutter/data/worker_data/worker_project_data/ds/model/net/dept_create_dept_net_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/worker_project_data/ds/model/net/dept_list_group_net_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/worker_project_data/ds/model/net/project_get_project_list_net_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/worker_project_data/ds/model/param/dept_create_dept_req_entity.dart';
import 'package:gdjg_pure_flutter/data/worker_data/worker_project_data/ds/model/param/project_get_project_list_param_model.dart';
import 'package:net_plugin/pigeon/api_generated/net_channel.g.dart';
import 'package:net_plugin/pigeon/net_result/resp_result.dart';
import 'package:net_plugin/pigeon/net_service/net_core.dart';

class WorkerProjectRds {
  //获取项目列表
  Future<RespResult<ProjectGetProjectListNetModel>> queryProjectList(
    ProjectGetProjectListParamModel req,
  ) async {
    return await NetCore.requestJGPHP(
        BaseBizRequestEntity(
            url: 'api/v3/project/get-project-list',
            method: HTTP_METHOD.GET,
            content: req.toMap().cast<String, Object>(),
            requestExtra: RequestExtra(domain: NET_DOMAIN.JG_PHP, printResp: false, showLoading: false)),
        (json) => ProjectGetProjectListNetModel.fromJson(json));
  }

  //新建项目
  Future<RespResult<DeptCreateDeptModel>> createProject(
    DeptCreateDeptReqEntity req,
  ) async {
    return await NetCore.requestJGPHP(
        BaseBizRequestEntity(
            url: 'api/dept/create-dept',
            method: HTTP_METHOD.POST,
            content: req.toMap().cast<String, Object>(),
            requestExtra: RequestExtra(
                domain: NET_DOMAIN.JG_PHP, printResp: false, showLoading: false)),
        (json) => DeptCreateDeptModel.fromJson(json));
  }

  //获取我参与的项目列表
  Future<RespResult<DeptListGroupNetModel>> queryParticipatedProjectList() async {
    return await NetCore.requestJGPHP(
        BaseBizRequestEntity(
            url: 'api/v3/dept/list/group',
            method: HTTP_METHOD.GET,
            content: {
              'is_ignore': '0',
              'type': 'join',
            },
            requestExtra: RequestExtra(domain: NET_DOMAIN.JG_PHP, printResp: false, showLoading: false)),
        (json) => DeptListGroupNetModel.fromJson(json));
  }
}
