import 'package:gdjg_pure_flutter/data/worker_data/worker_project_data/ds/model/net/dept_create_dept_net_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/worker_project_data/ds/model/net/dept_detail_net_model.dart';

import 'package:gdjg_pure_flutter/data/worker_data/worker_project_data/ds/model/net/personal_with_join_net_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/worker_project_data/ds/model/net/project_get_project_list_net_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/worker_project_data/ds/model/net/project_get_project_net_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/worker_project_data/ds/param/dept_create_dept_req_entity.dart';
import 'package:gdjg_pure_flutter/data/worker_data/worker_project_data/ds/param/dept_detail_param_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/worker_project_data/ds/param/dept_put_away_dept_param_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/worker_project_data/ds/param/dept_update_name_param_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/worker_project_data/ds/param/project_get_project_list_param_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_un_liauidated/ds/model/param/group_project_get_group_worker_settle_param_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_un_liauidated/ds/model/net/group_project_get_group_worker_settle_net_model.dart';
import 'package:net_plugin/pigeon/api_generated/net_channel.g.dart';
import 'package:net_plugin/pigeon/net_result/resp_result.dart';
import 'package:net_plugin/pigeon/net_service/net_core.dart';

class WorkerProjectRds {
  //获取项目列表
  Future<RespResult<ProjectGetProjectListNetModel>> queryProjectList(
    ProjectGetProjectListParamModel req,
  ) async {
    return await NetCore.requestJGPHP(
        BaseBizRequestEntity(
            url: 'api/v3/project/get-project-list',
            method: HTTP_METHOD.GET,
            content: req.toMap().cast<String, Object>(),
            requestExtra: RequestExtra(domain: NET_DOMAIN.JG_PHP, printResp: false, showLoading: false)),
        (json) => ProjectGetProjectListNetModel.fromJson(json));
  }

  //新建项目
  Future<RespResult<DeptCreateDeptNetModel>> createProject(
    DeptCreateDeptParamModel req,
  ) async {
    return await NetCore.requestJGPHP(
        BaseBizRequestEntity(
            url: 'api/dept/create-dept',
            method: HTTP_METHOD.POST,
            content: req.toMap().cast<String, Object>(),
            requestExtra: RequestExtra(
                domain: NET_DOMAIN.JG_PHP, printResp: false, showLoading: false)),
        (json) => DeptCreateDeptNetModel.fromJson(json));
  }

  //获取项目详情
  Future<RespResult<ProjectGetProjectNetModel>> getProject(String projectId) async {
    return await NetCore.requestJGPHP(
        BaseBizRequestEntity(
            url: 'api/v3/project/get-project',
            method: HTTP_METHOD.GET,
            content: {'work_note': projectId},
            requestExtra: RequestExtra(domain: NET_DOMAIN.JG_PHP, printResp: true, showLoading: false)),
        (json) => ProjectGetProjectNetModel.fromJson(json));
  }

  //设为已结清
  Future<RespResult<bool>> putAwayDept(DeptPutAwayDeptParamModel req) async {
    final result = await NetCore.requestJGPHP(
        BaseBizRequestEntity(
            url: 'api/dept/put-away-dept',
            method: HTTP_METHOD.POST,
            content: req.toMap().cast<String, Object>(),
            requestExtra: RequestExtra(domain: NET_DOMAIN.JG_PHP, printResp: false, showLoading: true)),
        (json) => true); // 接口返回空数据，只关注成功状态
    return result;
  }

  //获取已结清项目列表
  Future<RespResult<NetModelPersonalWithJoinNetModel>> queryCompletedProjectList() async {
    return await NetCore.requestJGPHP(
        BaseBizRequestEntity(
            url: 'api/v3/dept/list/personal_with_join',
            method: HTTP_METHOD.GET,
            content: {'is_ignore': '1'},
            requestExtra: RequestExtra(domain: NET_DOMAIN.JG_PHP, printResp: false, showLoading: false)),
        (json) => NetModelPersonalWithJoinNetModel.fromJson(json));
  }

  //删除项目
  Future<RespResult<bool>> deleteProject(String deptId) async {
    final result = await NetCore.requestJGPHP(
        BaseBizRequestEntity(
            url: 'api/v3/dept/delete',
            method: HTTP_METHOD.POST,
            content: {'dept_id': deptId},
            requestExtra: RequestExtra(domain: NET_DOMAIN.JG_PHP, printResp: false, showLoading: true)),
        (json) => true); // 接口返回空数据，只关注成功状态
    return result;
  }

  //修改项目名称
  Future<RespResult<bool>> updateDeptName(DeptUpdateDeptNameParamModel req) async {
    final result = await NetCore.requestJGPHP(
        BaseBizRequestEntity(
            url: 'api/dept/update-dept-name',
            method: HTTP_METHOD.POST,
            content: req.toMap().cast<String, Object>(),
            requestExtra: RequestExtra(domain: NET_DOMAIN.JG_PHP, printResp: false, showLoading: true)),
        (json) => true); // 接口返回空数据，只关注成功状态
    return result;
  }

  //获取工人在项目中的结算信息
  Future<RespResult<GroupProjectGetGroupWorkerSettleNetModel>> getGroupWorkerSettle(
      GroupProjectGetGroupWorkerSettleParamModel req) async {
    return await NetCore.requestJGPHP(
        BaseBizRequestEntity(
            url: 'api/v3/group-project/get-group-worker-settle',
            method: HTTP_METHOD.GET,
            content: req.toMap().cast<String, Object>(),
            requestExtra: RequestExtra(domain: NET_DOMAIN.JG_PHP, printResp: false, showLoading: true)),
        (json) => GroupProjectGetGroupWorkerSettleNetModel.fromJson(json));
  }

  // 获取部门详情
  Future<RespResult<DeptDetailNetModel>> getDeptDetail(
    DeptDetailParamModel req,
  ) async {
    return await NetCore.requestJGPHP(
        BaseBizRequestEntity(
            url: 'api/dept/detail',
            method: HTTP_METHOD.GET,
            content: req.toMap(),
            requestExtra: RequestExtra(domain: NET_DOMAIN.JG_PHP, printResp: false, showLoading: false)),
        (json) => DeptDetailNetModel.fromJson(json));
  }
}
