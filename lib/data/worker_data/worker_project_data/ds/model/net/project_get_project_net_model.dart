import 'dart:convert';
import 'package:gdjg_pure_flutter/data/worker_data/worker_project_data/repo/model/project_get_project_biz_model.dart';

class ProjectGetProjectNetModel {

  /// 项目id
  String? id;

  /// 
  String? name;

  /// 0 在建，1结清
  String? status;

  /// 部门id
  String? dept_id;

  /// 
  String? start_time;

  /// 
  String? end_time;

  /// 点工工价
  FeeStandardNetModel? fee_standard;

  /// 企业id
  String? corp_id;

  /// 工人id
  String? worker_id;

  /// 工资规则状态：0-关闭 1-开启
  String? fee_switch;

  /// 包工工价
  ContractorFeeStandardNetModel? contractor_fee_standard;

  /// [4.6]新增 最后流水日期
  String? last_business_date;

  ProjectGetProjectNetModel();

  Map<String, dynamic> toJson(ProjectGetProjectNetModel instance) => <String, dynamic>{
      "id": instance.id,
      "name": instance.name,
      "status": instance.status,
      "dept_id": instance.dept_id,
      "start_time": instance.start_time,
      "end_time": instance.end_time,
      "fee_standard": instance.fee_standard,
      "corp_id": instance.corp_id,
      "worker_id": instance.worker_id,
      "fee_switch": instance.fee_switch,
      "contractor_fee_standard": instance.contractor_fee_standard,
      "last_business_date": instance.last_business_date,
    };

  factory ProjectGetProjectNetModel.fromJson(Map<String, dynamic> json) {
    var netModel = ProjectGetProjectNetModel();
    netModel.id = json["id"]?.toString();
    netModel.name = json["name"]?.toString();
    netModel.status = json["status"]?.toString();
    netModel.dept_id = json["dept_id"]?.toString();
    netModel.start_time = json["start_time"]?.toString();
    netModel.end_time = json["end_time"]?.toString();
    netModel.fee_standard = json["fee_standard"] == null
      ? null
      : FeeStandardNetModel.fromJson(json["fee_standard"] as Map<String, dynamic>);
    netModel.corp_id = json["corp_id"]?.toString();
    netModel.worker_id = json["worker_id"]?.toString();
    netModel.fee_switch = json["fee_switch"]?.toString();
    netModel.contractor_fee_standard = json["contractor_fee_standard"] == null
      ? null
      : ContractorFeeStandardNetModel.fromJson(json["contractor_fee_standard"] as Map<String, dynamic>);
    netModel.last_business_date = json["last_business_date"]?.toString();
    return netModel;
  }

  ProjectGetProjectBizModel transform() {
    return ProjectGetProjectBizModel(
      id: double.parse(id ?? "0.0"),
      name: name ?? "",
      status: double.parse(status ?? "0.0"),
      deptId: double.parse(dept_id ?? "0.0"),
      startTime: start_time ?? "",
      endTime: end_time ?? "",
      feeStandard: fee_standard?.transform(),
      corpId: double.parse(corp_id ?? "0.0"),
      workerId: double.parse(worker_id ?? "0.0"),
      feeSwitch: double.parse(fee_switch ?? "0.0"),
      contractorFeeStandard: contractor_fee_standard?.transform(),
      lastBusinessDate: last_business_date ?? "",
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class FeeStandardNetModel {

  /// 
  String? workingHoursStandard;

  /// 
  String? workingHoursPrice;

  /// 
  String? overtimeHoursStandard;

  /// 
  String? overtimeHoursPrice;

  /// 
  String? overtimeType;

  /// 
  String? businessType;

  /// 
  String? fee_switch;

  /// 
  String? feeStandardId;

  FeeStandardNetModel();

  Map<String, dynamic> toJson(FeeStandardNetModel instance) => <String, dynamic>{
      "workingHoursStandard": instance.workingHoursStandard,
      "workingHoursPrice": instance.workingHoursPrice,
      "overtimeHoursStandard": instance.overtimeHoursStandard,
      "overtimeHoursPrice": instance.overtimeHoursPrice,
      "overtimeType": instance.overtimeType,
      "businessType": instance.businessType,
      "fee_switch": instance.fee_switch,
      "feeStandardId": instance.feeStandardId,
    };

  factory FeeStandardNetModel.fromJson(Map<String, dynamic> json) {
    var netModel = FeeStandardNetModel();
    netModel.workingHoursStandard = json["workingHoursStandard"]?.toString();
    netModel.workingHoursPrice = json["workingHoursPrice"]?.toString();
    netModel.overtimeHoursStandard = json["overtimeHoursStandard"]?.toString();
    netModel.overtimeHoursPrice = json["overtimeHoursPrice"]?.toString();
    netModel.overtimeType = json["overtimeType"]?.toString();
    netModel.businessType = json["businessType"]?.toString();
    netModel.fee_switch = json["fee_switch"]?.toString();
    netModel.feeStandardId = json["feeStandardId"]?.toString();
    return netModel;
  }

  FeeStandardBizModel transform() {
    return FeeStandardBizModel(
      workingHoursStandard: workingHoursStandard ?? "",
      workingHoursPrice: workingHoursPrice ?? "",
      overtimeHoursStandard: overtimeHoursStandard ?? "",
      overtimeHoursPrice: overtimeHoursPrice ?? "",
      overtimeType: double.parse(overtimeType ?? "0.0"),
      businessType: double.parse(businessType ?? "0.0"),
      feeSwitch: double.parse(fee_switch ?? "0.0"),
      feeStandardId: double.parse(feeStandardId ?? "0.0"),
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class ContractorFeeStandardNetModel {

  /// 
  String? workingHoursStandard;

  /// 
  String? workingHoursPrice;

  /// 
  String? overtimeHoursStandard;

  /// 
  String? overtimeHoursPrice;

  /// 
  String? overtimeType;

  /// 
  String? businessType;

  /// 
  String? fee_switch;

  /// 
  String? feeStandardId;

  ContractorFeeStandardNetModel();

  Map<String, dynamic> toJson(ContractorFeeStandardNetModel instance) => <String, dynamic>{
      "workingHoursStandard": instance.workingHoursStandard,
      "workingHoursPrice": instance.workingHoursPrice,
      "overtimeHoursStandard": instance.overtimeHoursStandard,
      "overtimeHoursPrice": instance.overtimeHoursPrice,
      "overtimeType": instance.overtimeType,
      "businessType": instance.businessType,
      "fee_switch": instance.fee_switch,
      "feeStandardId": instance.feeStandardId,
    };

  factory ContractorFeeStandardNetModel.fromJson(Map<String, dynamic> json) {
    var netModel = ContractorFeeStandardNetModel();
    netModel.workingHoursStandard = json["workingHoursStandard"]?.toString();
    netModel.workingHoursPrice = json["workingHoursPrice"]?.toString();
    netModel.overtimeHoursStandard = json["overtimeHoursStandard"]?.toString();
    netModel.overtimeHoursPrice = json["overtimeHoursPrice"]?.toString();
    netModel.overtimeType = json["overtimeType"]?.toString();
    netModel.businessType = json["businessType"]?.toString();
    netModel.fee_switch = json["fee_switch"]?.toString();
    netModel.feeStandardId = json["feeStandardId"]?.toString();
    return netModel;
  }

  ContractorFeeStandardBizModel transform() {
    return ContractorFeeStandardBizModel(
      workingHoursStandard: workingHoursStandard ?? "",
      workingHoursPrice: workingHoursPrice ?? "",
      overtimeHoursStandard: overtimeHoursStandard ?? "",
      overtimeHoursPrice: overtimeHoursPrice ?? "",
      overtimeType: double.parse(overtimeType ?? "0.0"),
      businessType: double.parse(businessType ?? "0.0"),
      feeSwitch: double.parse(fee_switch ?? "0.0"),
      feeStandardId: double.parse(feeStandardId ?? "0.0"),
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

