// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'project_get_project_list_net_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ProjectGetProjectListNetModel _$ProjectGetProjectListNetModelFromJson(
        Map<String, dynamic> json) =>
    ProjectGetProjectListNetModel()
      ..doing = json['doing'] == null
          ? null
          : DoingNetModel.fromJson(json['doing'] as Map<String, dynamic>)
      ..end = json['end'] == null
          ? null
          : EndNetModel.fromJson(json['end'] as Map<String, dynamic>);

Map<String, dynamic> _$ProjectGetProjectListNetModelToJson(
        ProjectGetProjectListNetModel instance) =>
    <String, dynamic>{
      'doing': instance.doing,
      'end': instance.end,
    };

DoingNetModel _$DoingNetModelFromJson(Map<String, dynamic> json) =>
    DoingNetModel()
      ..all = (json['all'] as num?)?.toDouble()
      ..list = (json['list'] as List<dynamic>?)
          ?.map((e) => ProjectGetProjectListANetModel.fromJson(
              e as Map<String, dynamic>))
          .toList();

Map<String, dynamic> _$DoingNetModelToJson(DoingNetModel instance) =>
    <String, dynamic>{
      'all': instance.all,
      'list': instance.list,
    };

ProjectGetProjectListANetModel _$ProjectGetProjectListANetModelFromJson(
        Map<String, dynamic> json) =>
    ProjectGetProjectListANetModel()
      ..id = (json['id'] as num?)?.toDouble()
      ..name = json['name'] as String?
      ..status = (json['status'] as num?)?.toDouble()
      ..fee_standard_id = (json['fee_standard_id'] as num?)?.toDouble()
      ..has_spot_work = (json['has_spot_work'] as num?)?.toDouble()
      ..unsettled = (json['unsettled'] as num?)?.toDouble()
      ..income = (json['income'] as num?)?.toDouble()
      ..has_money = (json['has_money'] as num?)?.toDouble()
      ..start_time = json['start_time'] as String?
      ..end_time = json['end_time'] as String?
      ..last_operation_date = json['last_operation_date'] as String?
      ..last_business_date = json['last_business_date'] as String?
      ..corp_id = (json['corp_id'] as num?)?.toDouble()
      ..dept_id = (json['dept_id'] as num?)?.toDouble()
      ..has_business = (json['has_business'] as num?)?.toDouble();

Map<String, dynamic> _$ProjectGetProjectListANetModelToJson(
        ProjectGetProjectListANetModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'status': instance.status,
      'fee_standard_id': instance.fee_standard_id,
      'has_spot_work': instance.has_spot_work,
      'unsettled': instance.unsettled,
      'income': instance.income,
      'has_money': instance.has_money,
      'start_time': instance.start_time,
      'end_time': instance.end_time,
      'last_operation_date': instance.last_operation_date,
      'last_business_date': instance.last_business_date,
      'corp_id': instance.corp_id,
      'dept_id': instance.dept_id,
      'has_business': instance.has_business,
    };

EndNetModel _$EndNetModelFromJson(Map<String, dynamic> json) => EndNetModel()
  ..all = (json['all'] as num?)?.toDouble()
  ..list = (json['list'] as List<dynamic>?)
      ?.map((e) =>
          ProjectGetProjectListBNetModel.fromJson(e as Map<String, dynamic>))
      .toList();

Map<String, dynamic> _$EndNetModelToJson(EndNetModel instance) =>
    <String, dynamic>{
      'all': instance.all,
      'list': instance.list,
    };

ProjectGetProjectListBNetModel _$ProjectGetProjectListBNetModelFromJson(
        Map<String, dynamic> json) =>
    ProjectGetProjectListBNetModel()
      ..id = (json['id'] as num?)?.toDouble()
      ..name = json['name'] as String?
      ..status = (json['status'] as num?)?.toDouble()
      ..fee_standard_id = (json['fee_standard_id'] as num?)?.toDouble()
      ..has_spot_work = (json['has_spot_work'] as num?)?.toDouble()
      ..unsettled = (json['unsettled'] as num?)?.toDouble()
      ..income = (json['income'] as num?)?.toDouble()
      ..has_money = (json['has_money'] as num?)?.toDouble()
      ..start_time = json['start_time'] as String?
      ..end_time = json['end_time'] as String?
      ..last_operation_date = json['last_operation_date'] as String?
      ..last_business_date = json['last_business_date'] as String?
      ..corp_id = (json['corp_id'] as num?)?.toDouble()
      ..dept_id = (json['dept_id'] as num?)?.toDouble()
      ..has_business = (json['has_business'] as num?)?.toDouble();

Map<String, dynamic> _$ProjectGetProjectListBNetModelToJson(
        ProjectGetProjectListBNetModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'status': instance.status,
      'fee_standard_id': instance.fee_standard_id,
      'has_spot_work': instance.has_spot_work,
      'unsettled': instance.unsettled,
      'income': instance.income,
      'has_money': instance.has_money,
      'start_time': instance.start_time,
      'end_time': instance.end_time,
      'last_operation_date': instance.last_operation_date,
      'last_business_date': instance.last_business_date,
      'corp_id': instance.corp_id,
      'dept_id': instance.dept_id,
      'has_business': instance.has_business,
    };
