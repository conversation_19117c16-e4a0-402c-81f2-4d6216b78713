import 'dart:convert';
import 'package:gdjg_pure_flutter/data/worker_data/worker_project_data/repo/model/project_get_project_list_biz_model.dart';

class ProjectGetProjectListNetModel {

  /// 在建项目
  DoingNetModel? doing;

  /// 已结清项目
  EndNetModel? end;

  ProjectGetProjectListNetModel();

  Map<String, dynamic> toJson(ProjectGetProjectListNetModel instance) => <String, dynamic>{
      "doing": instance.doing,
      "end": instance.end,
    };

  factory ProjectGetProjectListNetModel.fromJson(Map<String, dynamic> json) {
    var netModel = ProjectGetProjectListNetModel();
    netModel.doing = json["doing"] == null
      ? null
      : DoingNetModel.fromJson(json["doing"] as Map<String, dynamic>);
    netModel.end = json["end"] == null
      ? null
      : EndNetModel.fromJson(json["end"] as Map<String, dynamic>);
    return netModel;
  }

  ProjectGetProjectListBizModel transform() {
    return ProjectGetProjectListBizModel(
      doing: doing?.transform(),
      end: end?.transform(),
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class DoingNetModel {

  /// 总未结
  String? all;

  /// 
  List<ProjectGetProjectListANetModel>? list;

  DoingNetModel();

  Map<String, dynamic> toJson(DoingNetModel instance) => <String, dynamic>{
      "all": instance.all,
      "list": instance.list,
    };

  factory DoingNetModel.fromJson(Map<String, dynamic> json) {
    var netModel = DoingNetModel();
    netModel.all = json["all"]?.toString();
    netModel.list = (json["list"] as List<dynamic>?)
      ?.map((e) => ProjectGetProjectListANetModel.fromJson(e as Map<String, dynamic>))
      .toList();
    return netModel;
  }

  DoingBizModel transform() {
    return DoingBizModel(
      all: double.parse(all ?? "0.0"),
      list: list?.map((e) => e.transform()).toList() ?? [],
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class ProjectGetProjectListANetModel {

  /// work_note
  String? id;

  /// 项目名称
  String? name;

  /// 状态 0-在建 1-结清
  String? status;

  /// 工资规则 id
  String? fee_standard_id;

  /// 是否有点工 0-否 1-是
  String? has_spot_work;

  /// 未结
  String? unsettled;

  /// 收入
  String? income;

  /// 是否有短工 0-否 1-是
  String? has_money;

  /// 第一笔流水日期
  String? start_time;

  /// 最后一笔流水日期
  String? end_time;

  /// 最后操作日期
  String? last_operation_date;

  /// 最后流水日期
  String? last_business_date;

  /// 企业 id
  String? corp_id;

  /// 班组 id
  String? dept_id;

  /// [4.6]是否有过记工/记账 0-没有 1-有
  String? has_business;

  ProjectGetProjectListANetModel();

  Map<String, dynamic> toJson(ProjectGetProjectListANetModel instance) => <String, dynamic>{
      "id": instance.id,
      "name": instance.name,
      "status": instance.status,
      "fee_standard_id": instance.fee_standard_id,
      "has_spot_work": instance.has_spot_work,
      "unsettled": instance.unsettled,
      "income": instance.income,
      "has_money": instance.has_money,
      "start_time": instance.start_time,
      "end_time": instance.end_time,
      "last_operation_date": instance.last_operation_date,
      "last_business_date": instance.last_business_date,
      "corp_id": instance.corp_id,
      "dept_id": instance.dept_id,
      "has_business": instance.has_business,
    };

  factory ProjectGetProjectListANetModel.fromJson(Map<String, dynamic> json) {
    var netModel = ProjectGetProjectListANetModel();
    netModel.id = json["id"]?.toString();
    netModel.name = json["name"]?.toString();
    netModel.status = json["status"]?.toString();
    netModel.fee_standard_id = json["fee_standard_id"]?.toString();
    netModel.has_spot_work = json["has_spot_work"]?.toString();
    netModel.unsettled = json["unsettled"]?.toString();
    netModel.income = json["income"]?.toString();
    netModel.has_money = json["has_money"]?.toString();
    netModel.start_time = json["start_time"]?.toString();
    netModel.end_time = json["end_time"]?.toString();
    netModel.last_operation_date = json["last_operation_date"]?.toString();
    netModel.last_business_date = json["last_business_date"]?.toString();
    netModel.corp_id = json["corp_id"]?.toString();
    netModel.dept_id = json["dept_id"]?.toString();
    netModel.has_business = json["has_business"]?.toString();
    return netModel;
  }

  ProjectGetProjectListABizModel transform() {
    return ProjectGetProjectListABizModel(
      id: double.parse(id ?? "0.0"),
      name: name ?? "",
      status: double.parse(status ?? "0.0"),
      feeStandardId: double.parse(fee_standard_id ?? "0.0"),
      hasSpotWork: double.parse(has_spot_work ?? "0.0"),
      unsettled: double.parse(unsettled ?? "0.0"),
      income: double.parse(income ?? "0.0"),
      hasMoney: double.parse(has_money ?? "0.0"),
      startTime: start_time ?? "",
      endTime: end_time ?? "",
      lastOperationDate: last_operation_date ?? "",
      lastBusinessDate: last_business_date ?? "",
      corpId: double.parse(corp_id ?? "0.0"),
      deptId: double.parse(dept_id ?? "0.0"),
      hasBusiness: double.parse(has_business ?? "0.0"),
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class EndNetModel {

  /// 总收入
  String? all;

  /// 
  List<ProjectGetProjectListBNetModel>? list;

  EndNetModel();

  Map<String, dynamic> toJson(EndNetModel instance) => <String, dynamic>{
      "all": instance.all,
      "list": instance.list,
    };

  factory EndNetModel.fromJson(Map<String, dynamic> json) {
    var netModel = EndNetModel();
    netModel.all = json["all"]?.toString();
    netModel.list = (json["list"] as List<dynamic>?)
      ?.map((e) => ProjectGetProjectListBNetModel.fromJson(e as Map<String, dynamic>))
      .toList();
    return netModel;
  }

  EndBizModel transform() {
    return EndBizModel(
      all: double.parse(all ?? "0.0"),
      list: list?.map((e) => e.transform()).toList() ?? [],
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class ProjectGetProjectListBNetModel {

  /// 
  String? id;

  /// 
  String? name;

  /// 
  String? status;

  /// 
  String? fee_standard_id;

  /// 
  String? has_spot_work;

  /// 
  String? unsettled;

  /// 
  String? income;

  /// 
  String? has_money;

  /// 第一笔流水日期
  String? start_time;

  /// 最后一笔流水日期
  String? end_time;

  /// 最后操作日期
  String? last_operation_date;

  /// 最后流水日期
  String? last_business_date;

  /// 企业 id
  String? corp_id;

  /// 班组 id
  String? dept_id;

  /// [4.6]是否有过记工/记账 0-没有 1-有
  String? has_business;

  ProjectGetProjectListBNetModel();

  Map<String, dynamic> toJson(ProjectGetProjectListBNetModel instance) => <String, dynamic>{
      "id": instance.id,
      "name": instance.name,
      "status": instance.status,
      "fee_standard_id": instance.fee_standard_id,
      "has_spot_work": instance.has_spot_work,
      "unsettled": instance.unsettled,
      "income": instance.income,
      "has_money": instance.has_money,
      "start_time": instance.start_time,
      "end_time": instance.end_time,
      "last_operation_date": instance.last_operation_date,
      "last_business_date": instance.last_business_date,
      "corp_id": instance.corp_id,
      "dept_id": instance.dept_id,
      "has_business": instance.has_business,
    };

  factory ProjectGetProjectListBNetModel.fromJson(Map<String, dynamic> json) {
    var netModel = ProjectGetProjectListBNetModel();
    netModel.id = json["id"]?.toString();
    netModel.name = json["name"]?.toString();
    netModel.status = json["status"]?.toString();
    netModel.fee_standard_id = json["fee_standard_id"]?.toString();
    netModel.has_spot_work = json["has_spot_work"]?.toString();
    netModel.unsettled = json["unsettled"]?.toString();
    netModel.income = json["income"]?.toString();
    netModel.has_money = json["has_money"]?.toString();
    netModel.start_time = json["start_time"]?.toString();
    netModel.end_time = json["end_time"]?.toString();
    netModel.last_operation_date = json["last_operation_date"]?.toString();
    netModel.last_business_date = json["last_business_date"]?.toString();
    netModel.corp_id = json["corp_id"]?.toString();
    netModel.dept_id = json["dept_id"]?.toString();
    netModel.has_business = json["has_business"]?.toString();
    return netModel;
  }

  ProjectGetProjectListBBizModel transform() {
    return ProjectGetProjectListBBizModel(
      id: double.parse(id ?? "0.0"),
      name: name ?? "",
      status: double.parse(status ?? "0.0"),
      feeStandardId: double.parse(fee_standard_id ?? "0.0"),
      hasSpotWork: double.parse(has_spot_work ?? "0.0"),
      unsettled: double.parse(unsettled ?? "0.0"),
      income: double.parse(income ?? "0.0"),
      hasMoney: double.parse(has_money ?? "0.0"),
      startTime: start_time ?? "",
      endTime: end_time ?? "",
      lastOperationDate: last_operation_date ?? "",
      lastBusinessDate: last_business_date ?? "",
      corpId: double.parse(corp_id ?? "0.0"),
      deptId: double.parse(dept_id ?? "0.0"),
      hasBusiness: double.parse(has_business ?? "0.0"),
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

