import 'dart:convert';
import 'package:gdjg_pure_flutter/data/worker_data/worker_project_data/repo/model/project_get_project_list_biz_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'project_get_project_list_net_model.g.dart';

@JsonSerializable()
class ProjectGetProjectListNetModel {
  /// 在建项目
  DoingNetModel? doing;
  /// 已结清项目
  EndNetModel? end;

  ProjectGetProjectListNetModel();

  factory ProjectGetProjectListNetModel.fromJson(Map<String, dynamic> json) => _$ProjectGetProjectListNetModelFromJson(json);

  Map<String, dynamic> toJson() => _$ProjectGetProjectListNetModelToJson(this);

  ProjectGetProjectListBizModel transform() {
    return ProjectGetProjectListBizModel(
      doing: this.doing?.transform(),
      end: this.end?.transform(),
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class DoingNetModel {
  /// 总未结
  double? all;
  ///
  List<ProjectGetProjectListANetModel>? list;

  DoingNetModel();

  factory DoingNetModel.fromJson(Map<String, dynamic> json) => _$DoingNetModelFromJson(json);

  Map<String, dynamic> toJson() => _$DoingNetModelToJson(this);

  DoingBizModel transform() {
    return DoingBizModel(
      all: this.all ?? 0.0,
      list: this.list?.map((e) => e.transform()).toList() ?? [],
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class ProjectGetProjectListANetModel {
  /// work_note
  double? id;
  /// 项目名称
  String? name;
  /// 状态 0-在建 1-结清
  double? status;
  /// 工资规则 id
  double? fee_standard_id;
  /// 是否有点工 0-否 1-是
  double? has_spot_work;
  /// 未结
  double? unsettled;
  /// 收入
  double? income;
  /// 是否有短工 0-否 1-是
  double? has_money;
  /// 第一笔流水日期
  String? start_time;
  /// 最后一笔流水日期
  String? end_time;
  /// 最后操作日期
  String? last_operation_date;
  /// 最后流水日期
  String? last_business_date;
  /// 企业 id
  double? corp_id;
  /// 班组 id
  double? dept_id;
  /// [4.6]是否有过记工/记账 0-没有 1-有
  double? has_business;

  ProjectGetProjectListANetModel();

  factory ProjectGetProjectListANetModel.fromJson(Map<String, dynamic> json) => _$ProjectGetProjectListANetModelFromJson(json);

  Map<String, dynamic> toJson() => _$ProjectGetProjectListANetModelToJson(this);

  ProjectGetProjectListABizModel transform() {
    return ProjectGetProjectListABizModel(
      id: this.id ?? 0.0,
      name: this.name ?? "",
      status: this.status ?? 0.0,
      feeStandardId: this.fee_standard_id ?? 0.0,
      hasSpotWork: this.has_spot_work ?? 0.0,
      unsettled: this.unsettled ?? 0.0,
      income: this.income ?? 0.0,
      hasMoney: this.has_money ?? 0.0,
      startTime: this.start_time ?? "",
      endTime: this.end_time ?? "",
      lastOperationDate: this.last_operation_date ?? "",
      lastBusinessDate: this.last_business_date ?? "",
      corpId: this.corp_id ?? 0.0,
      deptId: this.dept_id ?? 0.0,
      hasBusiness: this.has_business ?? 0.0,
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class EndNetModel {
  /// 总收入
  double? all;
  ///
  List<ProjectGetProjectListBNetModel>? list;

  EndNetModel();

  factory EndNetModel.fromJson(Map<String, dynamic> json) => _$EndNetModelFromJson(json);

  Map<String, dynamic> toJson() => _$EndNetModelToJson(this);

  EndBizModel transform() {
    return EndBizModel(
      all: this.all ?? 0.0,
      list: this.list?.map((e) => e.transform()).toList() ?? [],
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class ProjectGetProjectListBNetModel {
  ///
  double? id;
  ///
  String? name;
  ///
  double? status;
  ///
  double? fee_standard_id;
  ///
  double? has_spot_work;
  ///
  double? unsettled;
  ///
  double? income;
  ///
  double? has_money;
  /// 第一笔流水日期
  String? start_time;
  /// 最后一笔流水日期
  String? end_time;
  /// 最后操作日期
  String? last_operation_date;
  /// 最后流水日期
  String? last_business_date;
  /// 企业 id
  double? corp_id;
  /// 班组 id
  double? dept_id;
  /// [4.6]是否有过记工/记账 0-没有 1-有
  double? has_business;

  ProjectGetProjectListBNetModel();

  factory ProjectGetProjectListBNetModel.fromJson(Map<String, dynamic> json) => _$ProjectGetProjectListBNetModelFromJson(json);

  Map<String, dynamic> toJson() => _$ProjectGetProjectListBNetModelToJson(this);

  ProjectGetProjectListBBizModel transform() {
    return ProjectGetProjectListBBizModel(
      id: this.id ?? 0.0,
      name: this.name ?? "",
      status: this.status ?? 0.0,
      feeStandardId: this.fee_standard_id ?? 0.0,
      hasSpotWork: this.has_spot_work ?? 0.0,
      unsettled: this.unsettled ?? 0.0,
      income: this.income ?? 0.0,
      hasMoney: this.has_money ?? 0.0,
      startTime: this.start_time ?? "",
      endTime: this.end_time ?? "",
      lastOperationDate: this.last_operation_date ?? "",
      lastBusinessDate: this.last_business_date ?? "",
      corpId: this.corp_id ?? 0.0,
      deptId: this.dept_id ?? 0.0,
      hasBusiness: this.has_business ?? 0.0,
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}
