// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'dept_create_dept_net_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

DeptCreateDeptModel _$DeptCreateDeptModelFromJson(Map<String, dynamic> json) =>
    DeptCreateDeptModel()
      ..code = (json['code'] as num?)?.toDouble()
      ..msg = json['msg'] as String?
      ..data = json['data'] == null
          ? null
          : DeptCreateDeptAModel.fromJson(json['data'] as Map<String, dynamic>);

Map<String, dynamic> _$DeptCreateDeptModelToJson(
        DeptCreateDeptModel instance) =>
    <String, dynamic>{
      'code': instance.code,
      'msg': instance.msg,
      'data': instance.data,
    };

DeptCreateDeptAModel _$DeptCreateDeptAModelFromJson(
        Map<String, dynamic> json) =>
    DeptCreateDeptAModel()
      ..corp_id = (json['corp_id'] as num?)?.toDouble()
      ..create_time = (json['create_time'] as num?)?.toDouble()
      ..dept_id = (json['dept_id'] as num?)?.toDouble()
      ..identity = (json['identity'] as num?)?.toDouble()
      ..is_deleted = (json['is_deleted'] as num?)?.toDouble()
      ..is_ignored = (json['is_ignored'] as num?)?.toDouble()
      ..is_self_created = (json['is_self_created'] as num?)?.toDouble()
      ..leader_uid = (json['leader_uid'] as num?)?.toDouble()
      ..name = json['name'] as String?
      ..note_identity = (json['note_identity'] as num?)?.toDouble()
      ..order = (json['order'] as num?)?.toDouble()
      ..self_user = (json['self_user'] as num?)?.toDouble()
      ..work_note_fee_switch =
          (json['work_note_fee_switch'] as num?)?.toDouble()
      ..work_note_id = (json['work_note_id'] as num?)?.toDouble()
      ..worker_id = json['worker_id'] as String?
      ..worker_num = (json['worker_num'] as num?)?.toDouble();

Map<String, dynamic> _$DeptCreateDeptAModelToJson(
        DeptCreateDeptAModel instance) =>
    <String, dynamic>{
      'corp_id': instance.corp_id,
      'create_time': instance.create_time,
      'dept_id': instance.dept_id,
      'identity': instance.identity,
      'is_deleted': instance.is_deleted,
      'is_ignored': instance.is_ignored,
      'is_self_created': instance.is_self_created,
      'leader_uid': instance.leader_uid,
      'name': instance.name,
      'note_identity': instance.note_identity,
      'order': instance.order,
      'self_user': instance.self_user,
      'work_note_fee_switch': instance.work_note_fee_switch,
      'work_note_id': instance.work_note_id,
      'worker_id': instance.worker_id,
      'worker_num': instance.worker_num,
    };
