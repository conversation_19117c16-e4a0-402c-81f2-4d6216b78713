// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'dept_list_group_net_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

DeptListGroupNetModel _$DeptListGroupNetModelFromJson(
        Map<String, dynamic> json) =>
    DeptListGroupNetModel()
      ..list = (json['list'] as List<dynamic>?)
          ?.map((e) => DeptListGroupANetModel.fromJson(e as Map<String, dynamic>))
          .toList()
      ..ignored_num = (json['ignored_num'] as num?)?.toDouble();

Map<String, dynamic> _$DeptListGroupNetModelToJson(
        DeptListGroupNetModel instance) =>
    <String, dynamic>{
      'list': instance.list,
      'ignored_num': instance.ignored_num,
    };

DeptListGroupANetModel _$DeptListGroupANetModelFromJson(
        Map<String, dynamic> json) =>
    DeptListGroupANetModel()
      ..work_note_id = (json['work_note_id'] as num?)?.toDouble()
      ..is_self_created = (json['is_self_created'] as num?)?.toDouble()
      ..is_ignored = (json['is_ignored'] as num?)?.toDouble()
      ..is_worker = (json['is_worker'] as num?)?.toDouble()
      ..corp_id = (json['corp_id'] as num?)?.toDouble()
      ..name = json['name'] as String?
      ..create_time = (json['create_time'] as num?)?.toDouble()
      ..self_user = (json['self_user'] as num?)?.toDouble()
      ..worker_num = (json['worker_num'] as num?)?.toDouble()
      ..create_time_str = json['create_time_str'] as String?
      ..worker_id = (json['worker_id'] as num?)?.toDouble()
      ..business_today = (json['business_today'] as num?)?.toDouble()
      ..last_business_date = json['last_business_date'] as String?
      ..last_operation_date = json['last_operation_date'] as String?
      ..sort_int = (json['sort_int'] as num?)?.toDouble()
      ..last_bookkeeping = (json['last_bookkeeping'] as num?)?.toDouble()
      ..show_dk_button = (json['show_dk_button'] as num?)?.toDouble()
      ..dept_id = (json['dept_id'] as num?)?.toDouble()

      ..is_agent = (json['is_agent'] as num?)?.toDouble()
      ..fee_switch = (json['fee_switch'] as num?)?.toDouble()
      ..hide_money = (json['hide_money'] as num?)?.toDouble()
      ..has_business = (json['has_business'] as num?)?.toDouble();

Map<String, dynamic> _$DeptListGroupANetModelToJson(
        DeptListGroupANetModel instance) =>
    <String, dynamic>{
      'work_note_id': instance.work_note_id,
      'is_self_created': instance.is_self_created,
      'is_ignored': instance.is_ignored,
      'is_worker': instance.is_worker,
      'corp_id': instance.corp_id,
      'name': instance.name,
      'create_time': instance.create_time,
      'self_user': instance.self_user,
      'worker_num': instance.worker_num,
      'create_time_str': instance.create_time_str,
      'worker_id': instance.worker_id,
      'business_today': instance.business_today,
      'last_business_date': instance.last_business_date,
      'last_operation_date': instance.last_operation_date,
      'sort_int': instance.sort_int,
      'last_bookkeeping': instance.last_bookkeeping,
      'show_dk_button': instance.show_dk_button,
      'dept_id': instance.dept_id,

      'is_agent': instance.is_agent,
      'fee_switch': instance.fee_switch,
      'hide_money': instance.hide_money,
      'has_business': instance.has_business,
    };
