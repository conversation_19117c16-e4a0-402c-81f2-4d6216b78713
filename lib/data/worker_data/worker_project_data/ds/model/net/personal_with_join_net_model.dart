import 'dart:convert';
import 'package:gdjg_pure_flutter/data/worker_data/worker_project_data/repo/model/personal_with_join_biz_model.dart';

class NetModelPersonalWithJoinNetModel {
  List<NetModelPersonalWithJoinANetModel>? list;

  NetModelPersonalWithJoinNetModel();

  Map<String, dynamic> toJson(NetModelPersonalWithJoinNetModel instance) => <String, dynamic>{
      "list": instance.list,
    };

  factory NetModelPersonalWithJoinNetModel.fromJson(Map<String, dynamic> json) {
    var netModel = NetModelPersonalWithJoinNetModel();
    netModel.list = (json["list"] as List<dynamic>?)
      ?.map((e) => NetModelPersonalWithJoinANetModel.fromJson(e as Map<String, dynamic>))
      .toList();
    return netModel;
  }

  NetModelPersonalWithJoinBizModel transform() {
    return NetModelPersonalWithJoinBizModel(
      list: list?.map((e) => e.transform()).toList() ?? [],
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class NetModelPersonalWithJoinANetModel {

  /// 记工本id
  String? work_note_id;

  /// 1班组，2个人
  String? identity;

  /// 账本的工资开关 1 打开， 2关闭
  String? fee_switch;

  /// 0我参与 1个人本
  String? is_self_created;

  /// 0未结清 1已结清
  String? is_ignored;

  /// 0不是工人 1是工人
  String? is_worker;

  /// 0不是代班 1是代班
  String? is_agent;

  /// 部门id
  String? dept_id;

  /// 企业id
  String? corp_id;

  /// 项目名称
  String? name;

  /// 创建时间
  String? create_time;
  String? self_user;

  /// 工人数量
  String? worker_num;

  /// 创建时间
  String? create_time_str;

  /// 工人id
  String? worker_id;

  /// 0今日未记工 1今日记工
  String? business_today;

  /// 首笔记工时间
  String? first_business_date;

  /// 上次记工时间
  String? last_business_date;

  /// 上次操作时间
  String? last_operation_date;

  /// 是否有过记工/记账 0-没有 1-有
  String? has_business;
  String? sort_int;

  /// 1上次记工，0非上次记工
  String? last_bookkeeping;

  /// 0不显示打卡按钮 1显示打卡按钮
  String? show_dk_button;

  /// (我参与的项目)项目是否隐藏工资 0不隐藏 1隐藏
  String? hide_money;

  NetModelPersonalWithJoinANetModel();

  Map<String, dynamic> toJson(NetModelPersonalWithJoinANetModel instance) => <String, dynamic>{
      "work_note_id": instance.work_note_id,
      "identity": instance.identity,
      "fee_switch": instance.fee_switch,
      "is_self_created": instance.is_self_created,
      "is_ignored": instance.is_ignored,
      "is_worker": instance.is_worker,
      "is_agent": instance.is_agent,
      "dept_id": instance.dept_id,
      "corp_id": instance.corp_id,
      "name": instance.name,
      "create_time": instance.create_time,
      "self_user": instance.self_user,
      "worker_num": instance.worker_num,
      "create_time_str": instance.create_time_str,
      "worker_id": instance.worker_id,
      "business_today": instance.business_today,
      "first_business_date": instance.first_business_date,
      "last_business_date": instance.last_business_date,
      "last_operation_date": instance.last_operation_date,
      "has_business": instance.has_business,
      "sort_int": instance.sort_int,
      "last_bookkeeping": instance.last_bookkeeping,
      "show_dk_button": instance.show_dk_button,
      "hide_money": instance.hide_money,
    };

  factory NetModelPersonalWithJoinANetModel.fromJson(Map<String, dynamic> json) {
    var netModel = NetModelPersonalWithJoinANetModel();
    netModel.work_note_id = json["work_note_id"]?.toString();
    netModel.identity = json["identity"]?.toString();
    netModel.fee_switch = json["fee_switch"]?.toString();
    netModel.is_self_created = json["is_self_created"]?.toString();
    netModel.is_ignored = json["is_ignored"]?.toString();
    netModel.is_worker = json["is_worker"]?.toString();
    netModel.is_agent = json["is_agent"]?.toString();
    netModel.dept_id = json["dept_id"]?.toString();
    netModel.corp_id = json["corp_id"]?.toString();
    netModel.name = json["name"]?.toString();
    netModel.create_time = json["create_time"]?.toString();
    netModel.self_user = json["self_user"]?.toString();
    netModel.worker_num = json["worker_num"]?.toString();
    netModel.create_time_str = json["create_time_str"]?.toString();
    netModel.worker_id = json["worker_id"]?.toString();
    netModel.business_today = json["business_today"]?.toString();
    netModel.first_business_date = json["first_business_date"]?.toString();
    netModel.last_business_date = json["last_business_date"]?.toString();
    netModel.last_operation_date = json["last_operation_date"]?.toString();
    netModel.has_business = json["has_business"]?.toString();
    netModel.sort_int = json["sort_int"]?.toString();
    netModel.last_bookkeeping = json["last_bookkeeping"]?.toString();
    netModel.show_dk_button = json["show_dk_button"]?.toString();
    netModel.hide_money = json["hide_money"]?.toString();
    return netModel;
  }

  NetModelPersonalWithJoinABizModel transform() {
    return NetModelPersonalWithJoinABizModel(
      workNoteId: double.parse(work_note_id ?? "0.0"),
      identity: double.parse(identity ?? "0.0"),
      feeSwitch: double.parse(fee_switch ?? "0.0"),
      isSelfCreated: double.parse(is_self_created ?? "0.0"),
      isIgnored: double.parse(is_ignored ?? "0.0"),
      isWorker: double.parse(is_worker ?? "0.0"),
      isAgent: double.parse(is_agent ?? "0.0"),
      deptId: double.parse(dept_id ?? "0.0"),
      corpId: double.parse(corp_id ?? "0.0"),
      name: name ?? "",
      createTime: double.parse(create_time ?? "0.0"),
      selfUser: double.parse(self_user ?? "0.0"),
      workerNum: double.parse(worker_num ?? "0.0"),
      createTimeStr: create_time_str ?? "",
      workerId: double.parse(worker_id ?? "0.0"),
      businessToday: double.parse(business_today ?? "0.0"),
      firstBusinessDate: first_business_date ?? "",
      lastBusinessDate: last_business_date ?? "",
      lastOperationDate: last_operation_date ?? "",
      hasBusiness: double.parse(has_business ?? "0.0"),
      sortInt: double.parse(sort_int ?? "0.0"),
      lastBookkeeping: double.parse(last_bookkeeping ?? "0.0"),
      showDkButton: double.parse(show_dk_button ?? "0.0"),
      hideMoney: double.parse(hide_money ?? "0.0"),
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}


