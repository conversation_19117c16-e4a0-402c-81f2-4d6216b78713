import 'dart:convert';
import 'package:gdjg_pure_flutter/data/worker_data/worker_project_data/repo/model/dept_create_dept_biz_model.dart';

class DeptCreateDeptNetModel {

  /// 企业 id
  String? corp_id;

  /// 创建时间时间戳（秒级精度）
  String? create_time;

  /// 班组 id
  String? dept_id;

  /// 班组类型，1-多人，2-个人
  String? identity;

  /// 是否暂停更新，0-否，1-是
  String? is_deleted;

  /// 是否已结束，0-否，1-是
  String? is_ignored;

  /// 是否是我创建，0-否，1-是
  String? is_self_created;

  /// 创建者用户 id
  String? leader_uid;

  /// 班组名称
  String? name;

  /// 
  String? note_identity;

  /// 排序字段，暂无用
  String? order;

  /// 用户在班组内的 user_id
  String? self_user;

  /// 账本工资开关是否打开，1-打开，2-关闭
  String? work_note_fee_switch;

  /// 账本 id
  String? work_note_id;

  /// 当前用户工人 id
  String? worker_id;

  /// 班组内工友数量
  String? worker_num;

  DeptCreateDeptNetModel();

  Map<String, dynamic> toJson(DeptCreateDeptNetModel instance) => <String, dynamic>{
      "corp_id": instance.corp_id,
      "create_time": instance.create_time,
      "dept_id": instance.dept_id,
      "identity": instance.identity,
      "is_deleted": instance.is_deleted,
      "is_ignored": instance.is_ignored,
      "is_self_created": instance.is_self_created,
      "leader_uid": instance.leader_uid,
      "name": instance.name,
      "note_identity": instance.note_identity,
      "order": instance.order,
      "self_user": instance.self_user,
      "work_note_fee_switch": instance.work_note_fee_switch,
      "work_note_id": instance.work_note_id,
      "worker_id": instance.worker_id,
      "worker_num": instance.worker_num,
    };

  factory DeptCreateDeptNetModel.fromJson(Map<String, dynamic> json) {
    var netModel = DeptCreateDeptNetModel();
    netModel.corp_id = json["corp_id"]?.toString();
    netModel.create_time = json["create_time"]?.toString();
    netModel.dept_id = json["dept_id"]?.toString();
    netModel.identity = json["identity"]?.toString();
    netModel.is_deleted = json["is_deleted"]?.toString();
    netModel.is_ignored = json["is_ignored"]?.toString();
    netModel.is_self_created = json["is_self_created"]?.toString();
    netModel.leader_uid = json["leader_uid"]?.toString();
    netModel.name = json["name"]?.toString();
    netModel.note_identity = json["note_identity"]?.toString();
    netModel.order = json["order"]?.toString();
    netModel.self_user = json["self_user"]?.toString();
    netModel.work_note_fee_switch = json["work_note_fee_switch"]?.toString();
    netModel.work_note_id = json["work_note_id"]?.toString();
    netModel.worker_id = json["worker_id"]?.toString();
    netModel.worker_num = json["worker_num"]?.toString();
    return netModel;
  }

  DeptCreateDeptBizModel transform() {
    return DeptCreateDeptBizModel(
      corpId: double.parse(corp_id ?? "0.0"),
      createTime: double.parse(create_time ?? "0.0"),
      deptId: double.parse(dept_id ?? "0.0"),
      identity: double.parse(identity ?? "0.0"),
      isDeleted: double.parse(is_deleted ?? "0.0"),
      isIgnored: double.parse(is_ignored ?? "0.0"),
      isSelfCreated: double.parse(is_self_created ?? "0.0"),
      leaderUid: double.parse(leader_uid ?? "0.0"),
      name: name ?? "",
      noteIdentity: double.parse(note_identity ?? "0.0"),
      order: double.parse(order ?? "0.0"),
      selfUser: double.parse(self_user ?? "0.0"),
      workNoteFeeSwitch: double.parse(work_note_fee_switch ?? "0.0"),
      workNoteId: double.parse(work_note_id ?? "0.0"),
      workerId: double.parse(worker_id ?? "0.0"),
      workerNum: double.parse(worker_num ?? "0.0"),
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}
