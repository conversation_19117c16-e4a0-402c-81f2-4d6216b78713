import 'dart:convert';

import 'package:json_annotation/json_annotation.dart';

part 'dept_create_dept_net_model.g.dart';

@JsonSerializable()
class DeptCreateDeptModel {
  ///
  double? code;
  ///
  String? msg;
  ///
  DeptCreateDeptAModel? data;

  DeptCreateDeptModel();

  factory DeptCreateDeptModel.fromJson(Map<String, dynamic> json) => _$DeptCreateDeptModelFromJson(json);

  Map<String, dynamic> toJson() => _$DeptCreateDeptModelToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class DeptCreateDeptAModel {
  /// 企业 id
  double? corp_id;
  /// 创建时间时间戳（秒级精度）
  double? create_time;
  /// 班组 id
  double? dept_id;
  /// 班组类型，1-多人，2-个人
  double? identity;
  /// 是否暂停更新，0-否，1-是
  double? is_deleted;
  /// 是否已结束，0-否，1-是
  double? is_ignored;
  /// 是否是我创建，0-否，1-是
  double? is_self_created;
  /// 创建者用户 id
  double? leader_uid;
  /// 班组名称
  String? name;
  ///
  double? note_identity;
  /// 排序字段，暂无用
  double? order;
  /// 用户在班组内的 user_id
  double? self_user;
  /// 账本工资开关是否打开，1-打开，2-关闭
  double? work_note_fee_switch;
  /// 账本 id
  double? work_note_id;
  /// 当前用户工人 id
  String? worker_id;
  /// 班组内工友数量
  double? worker_num;

  DeptCreateDeptAModel();

  factory DeptCreateDeptAModel.fromJson(Map<String, dynamic> json) => _$DeptCreateDeptAModelFromJson(json);

  Map<String, dynamic> toJson() => _$DeptCreateDeptAModelToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
