import 'dart:convert';
import 'package:gdjg_pure_flutter/data/worker_data/worker_project_data/repo/model/dept_list_group_biz_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'dept_list_group_net_model.g.dart';

@JsonSerializable()
class DeptListGroupNetModel {
  /// 
  List<DeptListGroupANetModel>? list;
  /// 结束账本数量
  double? ignored_num;

  DeptListGroupNetModel();

  factory DeptListGroupNetModel.fromJson(Map<String, dynamic> json) => _$DeptListGroupNetModelFromJson(json);

  Map<String, dynamic> toJson() => _$DeptListGroupNetModelToJson(this);

  DeptListGroupBizModel transform() {
    return DeptListGroupBizModel(
      list: this.list?.map((e) => e.transform()).toList() ?? [],
      ignoredNum: this.ignored_num ?? 0.0,
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class DeptListGroupANetModel {
  /// 记工本id
  double? work_note_id;
  /// 1自己创建，0非自己创建
  double? is_self_created;
  /// 1已结束，0未结束
  double? is_ignored;
  /// 1是工人，0不是工人
  double? is_worker;
  /// 企业id
  double? corp_id;
  /// 账本名称
  String? name;
  /// 创建时间
  double? create_time;
  /// 
  double? self_user;
  /// 工人数量
  double? worker_num;
  /// 创建时间
  String? create_time_str;
  /// 工人id
  double? worker_id;
  /// 1今日记工，0今日未记工
  double? business_today;
  /// 上次记工时间
  String? last_business_date;
  /// 上次操作时间
  String? last_operation_date;
  /// 
  double? sort_int;
  /// 1上次记工，0非上次记工
  double? last_bookkeeping;
  /// 1显示打卡按钮，0不显示打卡按钮
  double? show_dk_button;
  /// 部门id
  double? dept_id;
  /// 1班组，2个人
  double? identity;
  /// 1是代班，0不是代班
  double? is_agent;
  /// 账本的工资开关 1 打开， 2关闭
  double? fee_switch;
  /// [4.6]仅在我参与项目中出现 项目是否隐藏工资 0-不隐藏 1-隐藏
  double? hide_money;
  /// [4.6]是否有过记工/记账 0-没有 1-有
  double? has_business;

  DeptListGroupANetModel();

  factory DeptListGroupANetModel.fromJson(Map<String, dynamic> json) => _$DeptListGroupANetModelFromJson(json);

  Map<String, dynamic> toJson() => _$DeptListGroupANetModelToJson(this);

  DeptListGroupABizModel transform() {
    return DeptListGroupABizModel(
      workNoteId: this.work_note_id ?? 0.0,
      isSelfCreated: this.is_self_created ?? 0.0,
      isIgnored: this.is_ignored ?? 0.0,
      isWorker: this.is_worker ?? 0.0,
      corpId: this.corp_id ?? 0.0,
      name: this.name ?? "",
      createTime: this.create_time ?? 0.0,
      selfUser: this.self_user ?? 0.0,
      workerNum: this.worker_num ?? 0.0,
      createTimeStr: this.create_time_str ?? "",
      workerId: this.worker_id ?? 0.0,
      businessToday: this.business_today ?? 0.0,
      lastBusinessDate: this.last_business_date ?? "",
      lastOperationDate: this.last_operation_date ?? "",
      sortInt: this.sort_int ?? 0.0,
      lastBookkeeping: this.last_bookkeeping ?? 0.0,
      showDkButton: this.show_dk_button ?? 0.0,
      deptId: this.dept_id ?? 0.0,
      identity: this.identity ?? 0.0,
      isAgent: this.is_agent ?? 0.0,
      feeSwitch: this.fee_switch ?? 0.0,
      hideMoney: this.hide_money ?? 0.0,
      hasBusiness: this.has_business ?? 0.0,
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}
