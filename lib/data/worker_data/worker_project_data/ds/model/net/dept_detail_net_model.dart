import 'dart:convert';
import 'package:gdjg_pure_flutter/data/worker_data/worker_project_data/repo/model/dept_detail_biz_model.dart';

class DeptDetailNetModel {

  /// 班组 id
  double? dept_id;

  /// 班组创建时间（秒级精度）
  double? create_time;

  /// 企业 id
  double? corp_id;

  /// 班组名称
  String? name;

  /// 班组长 user_id
  double? leader_uid;

  /// 班组类型 1-多人 2-个人
  double? identity;

  /// 是否已停止更新数据 0-否 1-是
  double? is_deleted;

  /// 是否是我代班 0-否 1-是
  double? is_agent;

  /// 是否我创建的 0-否 1-是
  double? is_self_created;

  /// [4.6]新增 是否隐藏工资 0-否 1-是
  double? hide_money;

  /// 当前用户在班组内 user_id
  double? self_user;

  /// 当前用户在班组的工人 id
  double? worker_id;

  /// 班组内工友数量
  double? worker_num;

  /// 账本的工资开关 0-关闭 1-打开
  double? work_note_fee_switch;

  /// 账本 id
  double? work_note_id;

  /// 账本的工资开关 0-关闭 1-打开
  double? fee_switch;

  /// 0未结束，1已结束
  double? is_ignored;

  /// 今日是否记工，1是，0否 （目前仅用于个人本的判断逻辑）
  double? business_today;

  /// 最后一次记账时间
  String? last_business_date;

  DeptDetailNetModel();

  Map<String, dynamic> toJson(DeptDetailNetModel instance) {
    var map = <String, Object>{};
    if (dept_id != null) map["dept_id"] = dept_id!;
    if (create_time != null) map["create_time"] = create_time!;
    if (corp_id != null) map["corp_id"] = corp_id!;
    if (name != null) map["name"] = name!;
    if (leader_uid != null) map["leader_uid"] = leader_uid!;
    if (identity != null) map["identity"] = identity!;
    if (is_deleted != null) map["is_deleted"] = is_deleted!;
    if (is_agent != null) map["is_agent"] = is_agent!;
    if (is_self_created != null) map["is_self_created"] = is_self_created!;
    if (hide_money != null) map["hide_money"] = hide_money!;
    if (self_user != null) map["self_user"] = self_user!;
    if (worker_id != null) map["worker_id"] = worker_id!;
    if (worker_num != null) map["worker_num"] = worker_num!;
    if (work_note_fee_switch != null) map["work_note_fee_switch"] = work_note_fee_switch!;
    if (work_note_id != null) map["work_note_id"] = work_note_id!;
    if (fee_switch != null) map["fee_switch"] = fee_switch!;
    if (is_ignored != null) map["is_ignored"] = is_ignored!;
    if (business_today != null) map["business_today"] = business_today!;
    if (last_business_date != null) map["last_business_date"] = last_business_date!;
    return map;
  }

  factory DeptDetailNetModel.fromJson(Map<String, dynamic> json) {
    var netModel = DeptDetailNetModel();
    netModel.dept_id = double.tryParse(json["dept_id"].toString());
    netModel.create_time = double.tryParse(json["create_time"].toString());
    netModel.corp_id = double.tryParse(json["corp_id"].toString());
    netModel.name = json["name"]?.toString();
    netModel.leader_uid = double.tryParse(json["leader_uid"].toString());
    netModel.identity = double.tryParse(json["identity"].toString());
    netModel.is_deleted = double.tryParse(json["is_deleted"].toString());
    netModel.is_agent = double.tryParse(json["is_agent"].toString());
    netModel.is_self_created = double.tryParse(json["is_self_created"].toString());
    netModel.hide_money = double.tryParse(json["hide_money"].toString());
    netModel.self_user = double.tryParse(json["self_user"].toString());
    netModel.worker_id = double.tryParse(json["worker_id"].toString());
    netModel.worker_num = double.tryParse(json["worker_num"].toString());
    netModel.work_note_fee_switch = double.tryParse(json["work_note_fee_switch"].toString());
    netModel.work_note_id = double.tryParse(json["work_note_id"].toString());
    netModel.fee_switch = double.tryParse(json["fee_switch"].toString());
    netModel.is_ignored = double.tryParse(json["is_ignored"].toString());
    netModel.business_today = double.tryParse(json["business_today"].toString());
    netModel.last_business_date = json["last_business_date"]?.toString();
    return netModel;
  }

  DeptDetailBizModel transform() {
    return DeptDetailBizModel(
      deptId: dept_id ?? 0.0,
      createTime: create_time ?? 0.0,
      corpId: corp_id ?? 0.0,
      name: name ?? "",
      leaderUid: leader_uid ?? 0.0,
      identity: identity ?? 0.0,
      isDeleted: is_deleted ?? 0.0,
      isAgent: is_agent ?? 0.0,
      isSelfCreated: is_self_created ?? 0.0,
      hideMoney: hide_money ?? 0.0,
      selfUser: self_user ?? 0.0,
      workerId: worker_id ?? 0.0,
      workerNum: worker_num ?? 0.0,
      workNoteFeeSwitch: work_note_fee_switch ?? 0.0,
      workNoteId: work_note_id ?? 0.0,
      feeSwitch: fee_switch ?? 0.0,
      isIgnored: is_ignored ?? 0.0,
      businessToday: business_today ?? 0.0,
      lastBusinessDate: last_business_date ?? "",
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}
