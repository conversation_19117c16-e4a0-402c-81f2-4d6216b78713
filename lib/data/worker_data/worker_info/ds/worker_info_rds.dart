import 'package:gdjg_pure_flutter/data/worker_data/worker_info/ds/model/net/attendance_worker_net_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/worker_info/ds/model/net/member_has_business_net_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/worker_info/ds/model/net/net_model_dept_net_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/worker_info/ds/model/net/workers_get_worker_info_net_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/worker_info/ds/param/attendance_worker_param_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/worker_info/ds/param/net_model_dept_param_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/worker_info/ds/param/workers_get_worker_info_param_model.dart';
import 'package:net_plugin/pigeon/api_generated/net_channel.g.dart';
import 'package:net_plugin/pigeon/net_result/resp_result.dart';
import 'package:net_plugin/pigeon/net_service/net_core.dart';

/// 工友信息仓库
class WorkerInfoRds {
  /// 获取班组工友信息
  Future<RespResult<WorkersGetWorkerInfoNetModel>> getWorkerInfo(
      WorkersGetWorkerInfoParamModel params) async {
    return await NetCore.requestJGPHP(
        BaseBizRequestEntity(
            url: '/api/workers/get-worker-info',
            method: HTTP_METHOD.GET,
            content: params.toMap()),
        (json) => WorkersGetWorkerInfoNetModel.fromJson(json));
  }

  /// 获取项目在场工友列表
  Future<RespResult<NetModelDeptNetModel>> getGroupWorkers(
      NetModelDeptParamModel params) async {
    return await NetCore.requestJGPHP(
        BaseBizRequestEntity(
          url: '/api/v3/worker/list/dept',
          method: HTTP_METHOD.GET,
          content: params.toMap(),
        ),
        (json) => NetModelDeptNetModel.fromJson(json));
  }

  /// 获取出勤工友列表
  Future<RespResult<WorkersGetWorkersAtWorkNetModel>> getWorkersAtWork(
      WorkersGetWorkersAtWorkParamModel params) async {
    return await NetCore.requestJGPHP(
        BaseBizRequestEntity(
          url: '/api/workers/get-workers-at-work',
          method: HTTP_METHOD.GET,
          content: params.toMap(),
        ),
        (json) => WorkersGetWorkersAtWorkNetModel.fromJson(json));
  }

  /// 获取用户是否记过工-/api/member/has_business
  Future<RespResult<MemberHasBusinessNetModel>> getHasBusiness() async {
    return await NetCore.requestJGPHP(
        BaseBizRequestEntity(
          url: '/api/member/has_business',
          method: HTTP_METHOD.GET,
        ),
        (json) => MemberHasBusinessNetModel.fromJson(json));
  }
}
