// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'workers_get_worker_info_net_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

WorkersGetWorkerInfoNetModel _$WorkersGetWorkerInfoNetModelFromJson(
        Map<String, dynamic> json) =>
    WorkersGetWorkerInfoNetModel()
      ..id = (json['id'] as num?)?.toDouble()
      ..name = json['name'] as String?
      ..tel = json['tel'] as String?
      ..name_py = json['name_py'] as String?
      ..name_color = json['name_color'] as String?
      ..member_id = (json['member_id'] as num?)?.toDouble()
      ..is_deleted = (json['is_deleted'] as num?)?.toDouble()
      ..corp_id = (json['corp_id'] as num?)?.toDouble()
      ..is_bind = (json['is_bind'] as num?)?.toDouble()
      ..username = json['username'] as String?
      ..is_show = (json['is_show'] as num?)?.toDouble()
      ..is_self_created = (json['is_self_created'] as num?)?.toDouble()
      ..is_agent = (json['is_agent'] as num?)?.toDouble()
      ..fee_standard_info = json['fee_standard_info'] == null
          ? null
          : FeeStandardNetModel.fromJson(
              json['fee_standard_info'] as Map<String, dynamic>)
      ..contractor_fee_standard_info =
          json['contractor_fee_standard_info'] == null
              ? null
              : FeeStandardNetModel.fromJson(
                  json['contractor_fee_standard_info'] as Map<String, dynamic>)
      ..grant = json['grant'] == null
          ? null
          : GrantNetModel.fromJson(json['grant'] as Map<String, dynamic>);

Map<String, dynamic> _$WorkersGetWorkerInfoNetModelToJson(
        WorkersGetWorkerInfoNetModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'tel': instance.tel,
      'name_py': instance.name_py,
      'name_color': instance.name_color,
      'member_id': instance.member_id,
      'is_deleted': instance.is_deleted,
      'corp_id': instance.corp_id,
      'is_bind': instance.is_bind,
      'username': instance.username,
      'is_show': instance.is_show,
      'is_self_created': instance.is_self_created,
      'is_agent': instance.is_agent,
      'fee_standard_info': instance.fee_standard_info,
      'contractor_fee_standard_info': instance.contractor_fee_standard_info,
      'grant': instance.grant,
    };

GrantNetModel _$GrantNetModelFromJson(Map<String, dynamic> json) =>
    GrantNetModel()
      ..bank_name = json['bank_name'] as String?
      ..bank_no = json['bank_no'] as String?
      ..card_name = json['card_name'] as String?
      ..card_no = json['card_no'] as String?
      ..expired_time = json['expired_time'] as String?
      ..grant_id = (json['grant_id'] as num?)?.toDouble();

Map<String, dynamic> _$GrantNetModelToJson(GrantNetModel instance) =>
    <String, dynamic>{
      'bank_name': instance.bank_name,
      'bank_no': instance.bank_no,
      'card_name': instance.card_name,
      'card_no': instance.card_no,
      'expired_time': instance.expired_time,
      'grant_id': instance.grant_id,
    };

OccNetModel _$OccNetModelFromJson(Map<String, dynamic> json) => OccNetModel()
  ..industry_id = (json['industry_id'] as num?)?.toDouble()
  ..industry_name = json['industry_name'] as String?
  ..occupation_id = (json['occupation_id'] as num?)?.toDouble()
  ..occupation_name = json['occupation_name'] as String?;

Map<String, dynamic> _$OccNetModelToJson(OccNetModel instance) =>
    <String, dynamic>{
      'industry_id': instance.industry_id,
      'industry_name': instance.industry_name,
      'occupation_id': instance.occupation_id,
      'occupation_name': instance.occupation_name,
    };
