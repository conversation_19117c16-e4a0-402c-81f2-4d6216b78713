import 'dart:convert';

import 'package:gdjg_pure_flutter/data/worker_data/worker_info/repo/model/net_model_dept_biz_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'net_model_dept_net_model.g.dart';

@JsonSerializable()
class NetModelDeptNetModel {
  List<NoteWorkerNetModel>? note_worker;
  String? share_token;
  double? del_num;

  NetModelDeptNetModel();

  factory NetModelDeptNetModel.fromJson(Map<String, dynamic> json) =>
      _$NetModelDeptNetModelFromJson(json);

  Map<String, dynamic> toJson() => _$NetModelDeptNetModelToJson(this);

  NetModelDeptBizModel transform() {
    return NetModelDeptBizModel(
      noteWorker: note_worker?.map((e) => e.transform()).toList() ?? [],
      shareToken: share_token ?? "",
      delNum: del_num ?? 0.0,
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class NoteWorkerNetModel {
  double? worker_id;
  String? name;
  String? tel;
  String? name_py;
  String? name_color;
  String? avatar;
  double? quit_time;
  double? is_bind;
  double? member_id;
  double? user_id;
  double? is_rest;
  double? is_self;
  double? is_self_created;
  double? is_agent;
  double? contract_employee_status;
  double? is_grant;
  dynamic? occ;

  NoteWorkerNetModel();

  factory NoteWorkerNetModel.fromJson(Map<String, dynamic> json) =>
      _$NoteWorkerNetModelFromJson(json);

  Map<String, dynamic> toJson() => _$NoteWorkerNetModelToJson(this);

  NoteWorkerBizModel transform() {
    return NoteWorkerBizModel(
      workerId: worker_id?.toInt() ?? -1,
      name: name ?? "",
      tel: tel ?? "",
      namePy: name_py ?? "",
      nameColor: name_color ?? "",
      avatar: avatar ?? "",
      quitTime: quit_time ?? 0.0,
      isBind: is_bind ?? 0.0,
      memberId: member_id ?? 0.0,
      userId: user_id ?? 0.0,
      isRest: is_rest ?? 0.0,
      isSelf: is_self ?? 0.0,
      isSelfCreated: is_self_created ?? 0.0,
      isAgent: is_agent ?? 0.0,
      contractEmployeeStatus: contract_employee_status ?? 0.0,
      isGrant: is_grant ?? 0.0,
      occ: occ ?? "",
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}
