// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'net_model_dept_net_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

NetModelDeptNetModel _$NetModelDeptNetModelFromJson(
        Map<String, dynamic> json) =>
    NetModelDeptNetModel()
      ..note_worker = (json['note_worker'] as List<dynamic>?)
          ?.map((e) => NoteWorkerNetModel.fromJson(e as Map<String, dynamic>))
          .toList()
      ..share_token = json['share_token'] as String?
      ..del_num = (json['del_num'] as num?)?.toDouble();

Map<String, dynamic> _$NetModelDeptNetModelToJson(
        NetModelDeptNetModel instance) =>
    <String, dynamic>{
      'note_worker': instance.note_worker,
      'share_token': instance.share_token,
      'del_num': instance.del_num,
    };

NoteWorkerNetModel _$NoteWorkerNetModelFromJson(Map<String, dynamic> json) =>
    NoteWorkerNetModel()
      ..worker_id = (json['worker_id'] as num?)?.toDouble()
      ..name = json['name'] as String?
      ..tel = json['tel'] as String?
      ..name_py = json['name_py'] as String?
      ..name_color = json['name_color'] as String?
      ..avatar = json['avatar'] as String?
      ..quit_time = (json['quit_time'] as num?)?.toDouble()
      ..is_bind = (json['is_bind'] as num?)?.toDouble()
      ..member_id = (json['member_id'] as num?)?.toDouble()
      ..user_id = (json['user_id'] as num?)?.toDouble()
      ..is_rest = (json['is_rest'] as num?)?.toDouble()
      ..is_self = (json['is_self'] as num?)?.toDouble()
      ..is_self_created = (json['is_self_created'] as num?)?.toDouble()
      ..is_agent = (json['is_agent'] as num?)?.toDouble()
      ..contract_employee_status =
          (json['contract_employee_status'] as num?)?.toDouble()
      ..is_grant = (json['is_grant'] as num?)?.toDouble()
      ..occ = json['occ'];

Map<String, dynamic> _$NoteWorkerNetModelToJson(NoteWorkerNetModel instance) =>
    <String, dynamic>{
      'worker_id': instance.worker_id,
      'name': instance.name,
      'tel': instance.tel,
      'name_py': instance.name_py,
      'name_color': instance.name_color,
      'avatar': instance.avatar,
      'quit_time': instance.quit_time,
      'is_bind': instance.is_bind,
      'member_id': instance.member_id,
      'user_id': instance.user_id,
      'is_rest': instance.is_rest,
      'is_self': instance.is_self,
      'is_self_created': instance.is_self_created,
      'is_agent': instance.is_agent,
      'contract_employee_status': instance.contract_employee_status,
      'is_grant': instance.is_grant,
      'occ': instance.occ,
    };
