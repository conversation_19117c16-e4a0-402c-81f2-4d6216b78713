import 'package:gdjg_pure_flutter/data/worker_data/worker_info/repo/model/attendance_worker_biz_model.dart';

class WorkersGetWorkersAtWorkNetModel {
  List<WorkerNetModel>? workers;

  /// 休假工友数
  double? rest_worker_num;
  List<double>? business_worker_id;

  /// 选择日期里已记工的工人 根据记工类型分组
  List<BusinessWorkerIdByTypeNetModel>? business_worker_id_by_type;

  /// 选择日期里已记工的工人每个记工类型下最后一笔记工的信息
  List<LastBusinessByTypeNetModel>? last_business_by_type;

  /// 无点工工资人数
  double? no_point_work_fee_num;

  /// 无包工工资人数
  double? no_contractor_fee_num;

  WorkersGetWorkersAtWorkNetModel();

  Map<String, dynamic> toJson() {
    var map = <String, Object>{};
    if (workers != null) map["workers"] = workers!;
    if (rest_worker_num != null) map["rest_worker_num"] = rest_worker_num!;
    if (business_worker_id != null)
      map["business_worker_id"] = business_worker_id!;
    if (business_worker_id_by_type != null)
      map["business_worker_id_by_type"] = business_worker_id_by_type!;
    if (last_business_by_type != null)
      map["last_business_by_type"] = last_business_by_type!;
    if (no_point_work_fee_num != null)
      map["no_point_work_fee_num"] = no_point_work_fee_num!;
    if (no_contractor_fee_num != null)
      map["no_contractor_fee_num"] = no_contractor_fee_num!;
    return map;
  }

  factory WorkersGetWorkersAtWorkNetModel.fromJson(Map<String, dynamic> json) {
    var netModel = WorkersGetWorkersAtWorkNetModel();
    netModel.workers = (json["workers"] as List<dynamic>?)
        ?.map((e) => WorkerNetModel.fromJson(e as Map<String, dynamic>))
        .toList();
    netModel.rest_worker_num =
        double.tryParse(json["rest_worker_num"].toString());
    netModel.business_worker_id = (json["business_worker_id"] as List<dynamic>?)
        ?.map((e) => e as double)
        .toList();
    netModel.business_worker_id_by_type = (json["business_worker_id_by_type"]
    as List<dynamic>?)
        ?.map((e) =>
        BusinessWorkerIdByTypeNetModel.fromJson(e as Map<String, dynamic>))
        .toList();
    netModel.last_business_by_type =
        (json["last_business_by_type"] as List<dynamic>?)
            ?.map((e) =>
            LastBusinessByTypeNetModel.fromJson(e as Map<String, dynamic>))
            .toList();
    netModel.no_point_work_fee_num =
        double.tryParse(json["no_point_work_fee_num"].toString());
    netModel.no_contractor_fee_num =
        double.tryParse(json["no_contractor_fee_num"].toString());
    return netModel;
  }

  WorkersGetWorkersAtWorkBizModel transform() {
    return WorkersGetWorkersAtWorkBizModel(
      workers: workers?.map((e) => e.transform()).toList() ?? [],
      restWorkerNum: rest_worker_num ?? 0.0,
      businessWorkerId: business_worker_id ?? [],
      businessWorkerIdByType:
      business_worker_id_by_type?.map((e) => e.transform()).toList() ?? [],
      lastBusinessByType:
      last_business_by_type?.map((e) => e.transform()).toList() ?? [],
      noPointWorkFeeNum: no_point_work_fee_num ?? 0.0,
      noContractorFeeNum: no_contractor_fee_num ?? 0.0,
    );
  }
}

class WorkerNetModel {
  double? id;
  String? name;
  double? is_self;
  String? avatar;
  double? member_id;

  WorkerNetModel();

  Map<String, dynamic> toJson() {
    var map = <String, Object>{};
    if (id != null) map["id"] = id!;
    if (name != null) map["name"] = name!;
    if (is_self != null) map["is_self"] = is_self!;
    if (avatar != null) map["avatar"] = avatar!;
    if (member_id != null) map["member_id"] = member_id!;
    return map;
  }

  factory WorkerNetModel.fromJson(Map<String, dynamic> json) {
    var netModel = WorkerNetModel();
    netModel.id = double.tryParse(json["id"].toString());
    netModel.name = json["name"]?.toString();
    netModel.is_self = double.tryParse(json["is_self"].toString());
    netModel.avatar = json["avatar"]?.toString();
    netModel.member_id = double.tryParse(json["member_id"].toString());
    return netModel;
  }

  WorkerBizModel transform() {
    return WorkerBizModel(
      id: id ?? 0.0,
      name: name ?? "",
      isSelf: is_self ?? 0.0,
      memberId: member_id ?? 0.0,
      avatar: avatar ?? "",
    );
  }
}

class BusinessWorkerIdByTypeNetModel {
  double? business_type;
  List<double>? worker_ids;

  BusinessWorkerIdByTypeNetModel();

  Map<String, dynamic> toJson() {
    var map = <String, Object>{};
    if (business_type != null) map["business_type"] = business_type!;
    if (worker_ids != null) map["worker_ids"] = worker_ids!;
    return map;
  }

  factory BusinessWorkerIdByTypeNetModel.fromJson(Map<String, dynamic> json) {
    var netModel = BusinessWorkerIdByTypeNetModel();
    netModel.business_type = double.tryParse(json["business_type"].toString());
    netModel.worker_ids = (json["worker_ids"] as List<dynamic>?)
        ?.map((e) => e as double)
        .toList();
    return netModel;
  }

  BusinessWorkerIdByTypeBizModel transform() {
    return BusinessWorkerIdByTypeBizModel(
      businessType: business_type ?? 0.0,
      workerIds: worker_ids ?? [],
    );
  }
}

class LastBusinessByTypeNetModel {
  double? business_type;
  double? worker_id;
  double? last_time;

  LastBusinessByTypeNetModel();

  Map<String, dynamic> toJson() {
    var map = <String, Object>{};
    if (business_type != null) map["business_type"] = business_type!;
    if (worker_id != null) map["worker_id"] = worker_id!;
    if (last_time != null) map["last_time"] = last_time!;
    return map;
  }

  factory LastBusinessByTypeNetModel.fromJson(Map<String, dynamic> json) {
    var netModel = LastBusinessByTypeNetModel();
    netModel.business_type = double.tryParse(json["business_type"].toString());
    netModel.worker_id = double.tryParse(json["worker_id"].toString());
    netModel.last_time = double.tryParse(json["last_time"].toString());
    return netModel;
  }

  LastBusinessByTypeBizModel transform() {
    return LastBusinessByTypeBizModel(
      businessType: business_type ?? 0.0,
      workerId: worker_id ?? 0.0,
      lastTime: last_time ?? 0.0,
    );
  }
}
