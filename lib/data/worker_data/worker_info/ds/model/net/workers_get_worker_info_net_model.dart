import 'dart:convert';

import 'package:gdjg_pure_flutter/data/fee_standard_data/ds/model/net/fee_standard_net_model.dart';
import 'package:gdjg_pure_flutter/data/fee_standard_data/repo/fee_standard_transform.dart';
import 'package:gdjg_pure_flutter/data/worker_data/worker_info/repo/model/workers_get_worker_info_biz_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'workers_get_worker_info_net_model.g.dart';

@JsonSerializable()
class WorkersGetWorkerInfoNetModel {
  ///
  double? id;

  /// 工人姓名
  String? name;

  /// 电话
  String? tel;

  ///
  String? name_py;

  ///
  String? name_color;

  /// 头像
  String? avatar;

  ///
  double? member_id;

  ///
  double? is_deleted;

  ///
  double? corp_id;

  /// 是否绑定
  double? is_bind;

  /// 真实姓名
  String? username;

  /// 是否允许在线查看记工数据1-允许 2-不允许
  double? is_show;

  /// 是否是班组长 0-不是 1-是
  double? is_self_created;

  /// 是否是带班 0-不是 1-是
  double? is_agent;

  /// 是否是带班 0-不是 1-是

  ///
  FeeStandardNetModel? fee_standard_info;

  ///
  FeeStandardNetModel? contractor_fee_standard_info;

  /// 隐私信息
  GrantNetModel? grant;

  /// 职位信息
  OccNetModel? occ;

  WorkersGetWorkerInfoNetModel();

  factory WorkersGetWorkerInfoNetModel.fromJson(Map<String, dynamic> json) =>
      _$WorkersGetWorkerInfoNetModelFromJson(json);

  Map<String, dynamic> toJson() => _$WorkersGetWorkerInfoNetModelToJson(this);

  WorkersGetWorkerInfoBizModel transform() {
    return WorkersGetWorkerInfoBizModel(
      id: id?.toInt() ?? -1,
      name: name ?? "",
      tel: tel ?? "",
      namePy: name_py ?? "",
      nameColor: name_color ?? "",
      memberId: member_id ?? 0.0,
      isDeleted: is_deleted ?? 0.0,
      corpId: corp_id ?? 0.0,
      isBind: is_bind ?? 0.0,
      username: username ?? "",
      isShow: is_show ?? 0.0,
      isSelfCreated: is_self_created ?? 0.0,
      isAgent: is_agent ?? 0.0,
      feeStandardInfo:
          FeeStandardTransform.feeStandardTransform(fee_standard_info),
      contractorFeeStandardInfo: FeeStandardTransform.feeStandardTransform(
          contractor_fee_standard_info),
      grant: grant?.transform(),
      occ: occ?.transform(),
      avatar: avatar ?? "",
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class GrantNetModel {
  /// 银行卡名字
  String? bank_name;

  /// 银行卡卡号
  String? bank_no;

  /// 身份证名字
  String? card_name;

  /// 身份证号
  String? card_no;

  /// 授权时间
  String? expired_time;

  /// ID
  double? grant_id;

  GrantNetModel();

  factory GrantNetModel.fromJson(Map<String, dynamic> json) =>
      _$GrantNetModelFromJson(json);

  Map<String, dynamic> toJson() => _$GrantNetModelToJson(this);

  GrantBizModel transform() {
    return GrantBizModel(
      bankName: bank_name ?? "",
      bankNo: bank_no ?? "",
      cardName: card_name ?? "",
      cardNo: card_no ?? "",
      expiredTime: expired_time ?? "",
      grantId: grant_id ?? 0.0,
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class OccNetModel {
  /// 行业id
  double? industry_id;

  /// 行业名称
  String? industry_name;

  /// 职位id
  double? occupation_id;

  /// 职位名称
  String? occupation_name;

  OccNetModel();

  factory OccNetModel.fromJson(Map<String, dynamic> json) =>
      _$OccNetModelFromJson(json);

  Map<String, dynamic> toJson() => _$OccNetModelToJson(this);

  OccBizModel transform() {
    return OccBizModel(
      industryId: industry_id?.toInt() ?? 0,
      industryName: industry_name ?? "",
      occupationId: occupation_id?.toInt() ?? 0,
      occupationName: occupation_name ?? "",
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}
