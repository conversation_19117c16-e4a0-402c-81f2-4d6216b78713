import 'package:gdjg_pure_flutter/data/worker_data/worker_info/repo/model/member_has_business_biz_model.dart';

class MemberHasBusinessNetModel {
  /// 0-无记工 1-有记工
  double? has_business;

  MemberHasBusinessNetModel();

  Map<String, dynamic> toJson() {
    var map = <String, Object>{};
    if (has_business != null) map["has_business"] = has_business!;
    return map;
  }

  factory MemberHasBusinessNetModel.fromJson(Map<String, dynamic> json) {
    var netModel = MemberHasBusinessNetModel();
    netModel.has_business = double.tryParse(json["has_business"].toString());
    return netModel;
  }

  MemberHasBusinessBizModel transform() {
    return MemberHasBusinessBizModel(
      hasBusiness: has_business ?? 0.0,
    );
  }
}
