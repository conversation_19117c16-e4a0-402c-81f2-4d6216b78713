class WorkersGetWorkersAtWorkParamModel {
  /// 项目ID
  final String? workNote;

  /// 选中的记工时间 多个时间用,隔开
  final String? businessTime;

  WorkersGetWorkersAtWorkParamModel({
    this.workNote,
    this.businessTime,
  });

  Map<String, Object> toMap() {
    var map = <String, Object>{};
    if (workNote != null) map["work_note"] = workNote!;
    if (businessTime != null) map["business_time"] = businessTime!;
    map["fee"] = 1;
    map["struct_type"] = 1;
    return map;
  }
}
