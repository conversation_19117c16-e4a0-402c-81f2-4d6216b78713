class NetModelDeptParamModel {

  /// 记工本的所属部门ID
  final String dept_id;

  /// 0 在场工人，1 退场工人
  final String is_deleted;

  /// 类型 1-点工 6-包工
  final String businessType;

  /// 是否休息 0-否 1-是
  final String is_rest;

   NetModelDeptParamModel({
    this.dept_id = "",
    this.is_deleted = "",
    this.businessType = "",
    this.is_rest = "",
  });

  Map<String, String> toMap() {
    return {
      "dept_id": dept_id,
      "is_deleted": is_deleted,
      "businessType": businessType,
      "is_rest": is_rest,
    };
  }
}

