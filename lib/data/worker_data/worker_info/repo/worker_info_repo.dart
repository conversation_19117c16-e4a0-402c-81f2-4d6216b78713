import 'package:gdjg_pure_flutter/data/note_time_data/ds/note_time_rds.dart';
import 'package:gdjg_pure_flutter/data/note_time_data/ds/param/group_project_get_group_worker_settle_param_model.dart';
import 'package:gdjg_pure_flutter/data/note_time_data/repo/model/group_project_get_group_worker_settle_biz_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/worker_info/ds/param/attendance_worker_param_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/worker_info/ds/param/net_model_dept_param_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/worker_info/ds/param/workers_get_worker_info_param_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/worker_info/ds/worker_info_rds.dart';
import 'package:gdjg_pure_flutter/data/worker_data/worker_info/repo/model/attendance_worker_biz_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/worker_info/repo/model/member_has_business_biz_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/worker_info/repo/model/net_model_dept_biz_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/worker_info/repo/model/workers_get_worker_info_biz_model.dart';
import 'package:net_plugin/pigeon/net_result/resp_fail.dart';
import 'package:net_plugin/pigeon/net_result/resp_result.dart';

/// 工友信息仓库
class WorkerInfoRepo {
  final _workerInfoRds = WorkerInfoRds();
  final _noteTimeRds = NoteTimeRds();

  /// 获取工友信息
  Future<RespResult<WorkersGetWorkerInfoBizModel>> fetchWorkerInfo(
      WorkersGetWorkerInfoParamModel param) async {
    final result = await _workerInfoRds.getWorkerInfo(param);
    final data = result.getSucData();
    if (data == null) {
      return RespFail.buildBizFail(
          result.fail?.code, result.fail?.errorMsg, null, result.fail?.askId);
    }
    return result.map((model) => model!.transform());
  }

  /// 获取工友未结信息
  Future<RespResult<GroupProjectGetGroupWorkerSettleBizModel>>
      fetchWorkersSettle(
          GroupProjectGetGroupWorkerSettleParamModel params) async {
    final result = await _noteTimeRds.getGroupWorkerSettle(params);
    final data = result.getSucData();
    if (data == null) {
      return RespFail.buildBizFail(
          result.fail?.code, result.fail?.errorMsg, null, result.fail?.askId);
    }
    return result.map((model) => model!.transform());
  }

  /// 获取项目在场工友列表
  Future<RespResult<List<NoteWorkerBizModel>>> fetchGroupWorkers(
      NetModelDeptParamModel params) async {
    final result = await _workerInfoRds.getGroupWorkers(params);
    final data = result.getSucData();
    if (data == null) {
      return RespFail.buildBizFail(
          result.fail?.code, result.fail?.errorMsg, null, result.fail?.askId);
    }
    return result.map((model) => model!.transform().noteWorker);
  }

  /// 获取出勤工友列表
  Future<RespResult<WorkersGetWorkersAtWorkBizModel>> fetchWorkersAtWork(
      WorkersGetWorkersAtWorkParamModel params) async {
    final result = await _workerInfoRds.getWorkersAtWork(params);
    final data = result.getSucData();
    if (data == null) {
      return RespFail.buildBizFail(
          result.fail?.code, result.fail?.errorMsg, null, result.fail?.askId);
    }
    return result.map((model) => model!.transform());
  }

  /// 获取用户是否记过工
  Future<RespResult<bool>> fetchHasBusiness() async {
    final result = await _workerInfoRds.getHasBusiness();
    final data = result.getSucData();
    if (data == null) {
      return RespFail.buildBizFail(
          result.fail?.code, result.fail?.errorMsg, null, result.fail?.askId);
    }
    return result.map((model) => model!.transform().hasBusiness == 1);
  }
}
