import 'dart:convert';

import 'package:gdjg_pure_flutter/data/fee_standard_data/repo/model/fee_standard_biz_model.dart';

class WorkersGetWorkerInfoBizModel {
  ///
  int id;

  /// 工人姓名
  String name;

  /// 电话
  String tel;

  /// 头像
  String avatar;

  ///
  String namePy;

  ///
  String nameColor;

  ///
  double memberId;

  ///
  double isDeleted;

  ///
  double corpId;

  /// 是否绑定
  double isBind;

  /// 真实姓名
  String username;

  /// 是否允许在线查看记工数据1-允许 2-不允许
  double isShow;

  /// 是否是班组长 0-不是 1-是
  double isSelfCreated;

  /// 是否是带班 0-不是 1-是
  double isAgent;

  ///
  final FeeStandardBizModel? feeStandardInfo;

  ///
  final FeeStandardBizModel? contractorFeeStandardInfo;

  /// 隐私信息
  final GrantBizModel? grant;

  /// 职位信息
  final OccBizModel? occ;

  WorkersGetWorkerInfoBizModel({
    this.id = -1,
    this.name = "",
    this.tel = "",
    this.namePy = "",
    this.nameColor = "",
    this.memberId = 0.0,
    this.isDeleted = 0.0,
    this.corpId = 0.0,
    this.isBind = 0.0,
    this.username = "",
    this.isShow = 0.0,
    this.isSelfCreated = 0.0,
    this.isAgent = 0.0,
    this.feeStandardInfo,
    this.contractorFeeStandardInfo,
    this.grant,
    this.avatar = "",
    this.occ,
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class GrantBizModel {
  /// 银行卡名字
  String bankName;

  /// 银行卡卡号
  String bankNo;

  /// 身份证名字
  String cardName;

  /// 身份证号
  String cardNo;

  /// 授权时间
  String expiredTime;

  /// ID
  double grantId;

  GrantBizModel({
    this.bankName = "",
    this.bankNo = "",
    this.cardName = "",
    this.cardNo = "",
    this.expiredTime = "",
    this.grantId = 0.0,
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class OccBizModel {
  /// 行业id
  int industryId;

  /// 行业名称
  String industryName;

  /// 职位id
  int occupationId;

  /// 职位名称
  String occupationName;

  OccBizModel({
    this.industryId = 0,
    this.industryName = "",
    this.occupationId = 0,
    this.occupationName = "",
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}
