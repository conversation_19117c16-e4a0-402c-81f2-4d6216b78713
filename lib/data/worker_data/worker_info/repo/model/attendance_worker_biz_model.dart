class WorkersGetWorkersAtWorkBizModel {
  List<WorkerBizModel> workers;

  /// 休假工友数
  double restWorkerNum;
  List<double> businessWorkerId;

  /// 选择日期里已记工的工人 根据记工类型分组
  List<BusinessWorkerIdByTypeBizModel> businessWorkerIdByType;

  /// 选择日期里已记工的工人每个记工类型下最后一笔记工的信息
  List<LastBusinessByTypeBizModel> lastBusinessByType;

  /// 无点工工资人数
  double noPointWorkFeeNum;

  /// 无包工工资人数
  double noContractorFeeNum;

  WorkersGetWorkersAtWorkBizModel({
    this.workers = const [],
    this.restWorkerNum = 0.0,
    this.businessWorkerId = const [],
    this.businessWorkerIdByType = const [],
    this.lastBusinessByType = const [],
    this.noPointWorkFeeNum = 0.0,
    this.noContractorFeeNum = 0.0,
  });
}

class WorkerBizModel {
  double id;
  String name;
  double isSelf;
  double memberId;
  String avatar;

  WorkerBizModel({
    this.id = 0.0,
    this.name = "",
    this.isSelf = 0.0,
    this.memberId = 0.0,
    this.avatar = "",
  });
}

class BusinessWorkerIdByTypeBizModel {
  double businessType;
  List<double> workerIds;

  BusinessWorkerIdByTypeBizModel({
    this.businessType = 0.0,
    this.workerIds = const [],
  });
}

class LastBusinessByTypeBizModel {
  double businessType;
  double workerId;
  double lastTime;

  LastBusinessByTypeBizModel({
    this.businessType = 0.0,
    this.workerId = 0.0,
    this.lastTime = 0.0,
  });
}
