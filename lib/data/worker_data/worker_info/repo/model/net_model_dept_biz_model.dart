import 'dart:convert';

class NetModelDeptBizModel {
  List<NoteWorkerBizModel> noteWorker;
  String shareToken;
  double delNum;

  NetModelDeptBizModel({
    this.noteWorker = const [],
    this.shareToken = "",
    this.delNum = 0.0,
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class NoteWorkerBizModel {
  int workerId;
  String name;
  String tel;
  String namePy;
  String nameColor;
  String avatar;
  double quitTime;
  double isBind;
  double memberId;
  double userId;
  double isRest;
  double isSelf;
  double isSelfCreated;
  double isAgent;
  double contractEmployeeStatus;
  double isGrant;
  dynamic occ;

  NoteWorkerBizModel({
    this.workerId = -1,
    this.name = "",
    this.tel = "",
    this.namePy = "",
    this.nameColor = "",
    this.avatar = "",
    this.quitTime = 0.0,
    this.isBind = 0.0,
    this.memberId = 0.0,
    this.userId = 0.0,
    this.isRest = 0.0,
    this.isSelf = 0.0,
    this.isSelfCreated = 0.0,
    this.isAgent = 0.0,
    this.contractEmployeeStatus = 0.0,
    this.isGrant = 0.0,
    this.occ = "",
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}

