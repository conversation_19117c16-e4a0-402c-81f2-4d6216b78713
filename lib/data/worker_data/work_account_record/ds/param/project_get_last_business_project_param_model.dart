class ProjectGetLastBusinessProjectParamModel {

  /// 身份 1-班组 2-个人
  final String? identity;

  /// 0-在建账本 1-结束账本，
  final String? status;

  /// 1-记工 2-记账
  final String? data_type;

   ProjectGetLastBusinessProjectParamModel({
    this.identity,
    this.status,
    this.data_type,
  });

  Map<String, Object> toMap() {
    var map = <String, Object>{};
      if (identity != null) map["identity"] = identity!;
      if (status != null) map["status"] = status!;
      if (data_type != null) map["data_type"] = data_type!;
    return map;
  }
}

