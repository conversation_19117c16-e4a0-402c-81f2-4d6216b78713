import 'package:gdjg_pure_flutter/data/worker_data/work_account_record/ds/model/project_get_last_business_project_net_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/work_account_record/ds/param/project_get_last_business_project_param_model.dart';
import 'package:net_plugin/pigeon/api_generated/net_channel.g.dart';
import 'package:net_plugin/pigeon/net_result/resp_result.dart';
import 'package:net_plugin/pigeon/net_service/net_core.dart';

class WorkerAccountRecordRds {
  ///获取上次记工的记工本
  Future<RespResult<ProjectGetLastBusinessProjectNetModel>>
      getProjectLastBusinessProject(
          ProjectGetLastBusinessProjectParamModel params) async {
    return await NetCore.requestJGPHP(
        BaseBizRequestEntity(
            url: '/api/v3/project/get-last-business-project',
            method: HTTP_METHOD.GET,
            content: params.toMap(),
            requestExtra: RequestExtra(showLoading: true)),
        (json) => ProjectGetLastBusinessProjectNetModel.fromJson(json));
  }
  ///获取工资来源
  Future<RespResult<ProjectGetLastBusinessProjectNetModel>>
  getBusinessGetProjectCount(
      ProjectGetLastBusinessProjectParamModel params) async {
    return await NetCore.requestJGPHP(
        BaseBizRequestEntity(
            url: '/api/bookkeeping-source/get-wage-source',
            method: HTTP_METHOD.GET,
            content: params.toMap(),
            requestExtra: RequestExtra(showLoading: true)),
            (json) => ProjectGetLastBusinessProjectNetModel.fromJson(json));
  }
}
