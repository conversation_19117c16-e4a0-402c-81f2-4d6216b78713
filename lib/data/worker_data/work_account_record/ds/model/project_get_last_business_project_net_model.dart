import 'dart:convert';
import '../../repo/model/project_get_last_business_project_biz_model.dart';

class ProjectGetLastBusinessProjectNetModel {

  /// 
  double? id;

  /// 
  String? name;

  /// 
  double? dept_id;

  /// 
  double? corp_id;

  ProjectGetLastBusinessProjectNetModel();

  Map<String, dynamic> toJson(ProjectGetLastBusinessProjectNetModel instance) {
    var map = <String, Object>{};
    if (id != null) map["id"] = id!;
    if (name != null) map["name"] = name!;
    if (dept_id != null) map["dept_id"] = dept_id!;
    if (corp_id != null) map["corp_id"] = corp_id!;
    return map;
  }

  factory ProjectGetLastBusinessProjectNetModel.fromJson(Map<String, dynamic> json) {
    var netModel = ProjectGetLastBusinessProjectNetModel();
    netModel.id = double.tryParse(json["id"].toString());
    netModel.name = json["name"]?.toString();
    netModel.dept_id = double.tryParse(json["dept_id"].toString());
    netModel.corp_id = double.tryParse(json["corp_id"].toString());
    return netModel;
  }

  ProjectGetLastBusinessProjectBizModel transform() {
    return ProjectGetLastBusinessProjectBizModel(
      id: id ?? 0.0,
      name: name ?? "",
      deptId: dept_id ?? 0.0,
      corpId: corp_id ?? 0.0,
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

