import 'package:gdjg_pure_flutter/data/worker_data/work_account_record/ds/model/project_get_last_business_project_net_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/work_account_record/ds/param/project_get_last_business_project_param_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/work_account_record/ds/worker_account_record_rds.dart';
import 'package:gdjg_pure_flutter/data/worker_data/work_account_record/repo/model/project_get_last_business_project_biz_model.dart';
import 'package:net_plugin/pigeon/net_result/resp_result.dart';

class WorkerAccountRecordRepo {
  final _workerAccountRecordRds = WorkerAccountRecordRds();

  Future<RespResult<ProjectGetLastBusinessProjectBizModel>>
      getProjectLastBusinessProject(
          ProjectGetLastBusinessProjectParamModel param) async {
    final result =
        await _workerAccountRecordRds.getProjectLastBusinessProject(param);
    return result.map(_transform);
  }

  ProjectGetLastBusinessProjectBizModel _transform(
      ProjectGetLastBusinessProjectNetModel? netModel) {
    if (netModel == null) {
      return ProjectGetLastBusinessProjectBizModel();
    }
    return netModel.transform();
  }
}
