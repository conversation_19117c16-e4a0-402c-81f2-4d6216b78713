import 'package:gdjg_pure_flutter/data/worker_data/worker_statistics/ds/model/business_get_project_count_net_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/worker_statistics/ds/param/business_get_project_count_param_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/worker_statistics/ds/worker_statistics_rds.dart';
import 'package:gdjg_pure_flutter/data/worker_data/worker_statistics/repo/model/business_get_project_count_biz_model.dart';
import 'package:net_plugin/pigeon/net_result/resp_result.dart';

class WorkerStatisticsRepo {
  final _workerStatisticsRds = WorkerStatisticsRds();

  ///工人获取统计
  Future<RespResult<BusinessGetProjectCountBizModel>>
      getBusinessGetProjectCount(
          BusinessGetProjectCountParamModel param) async {
    final result = await _workerStatisticsRds.getBusinessGetProjectCount(param);
    return result.map(_transform);
  }

  BusinessGetProjectCountBizModel _transform(
      BusinessGetProjectCountNetModel? netModel) {
    if (netModel == null) {
      return BusinessGetProjectCountBizModel();
    }
    return netModel.transform();
  }
}
