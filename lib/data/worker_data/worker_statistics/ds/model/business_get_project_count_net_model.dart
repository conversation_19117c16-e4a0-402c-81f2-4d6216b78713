import 'dart:convert';

import 'package:gdjg_pure_flutter/data/common_data/net/count_net_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/worker_statistics/repo/model/business_get_project_count_biz_model.dart';

class BusinessGetProjectCountNetModel {
  /// 全部
  CountNetModel? all;

  /// 分项目
  List<CountNetModel>? project;

  BusinessGetProjectCountNetModel();

  Map<String, dynamic> toJson(BusinessGetProjectCountNetModel instance) =>
      <String, dynamic>{
        "all": instance.all,
        "project": instance.project,
      };

  factory BusinessGetProjectCountNetModel.fromJson(Map<String, dynamic> json) {
    var netModel = BusinessGetProjectCountNetModel();
    netModel.all = json["all"] == null
        ? null
        : CountNetModel.fromJson(json["all"] as Map<String, dynamic>);
    netModel.project = (json["project"] as List<dynamic>?)
        ?.map((e) => CountNetModel.fromJson(e as Map<String, dynamic>))
        .toList();
    return netModel;
  }

  BusinessGetProjectCountBizModel transform() {
    return BusinessGetProjectCountBizModel(
      all: all?.transform(),
      project: project?.map((e) => e.transform()).toList() ?? [],
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}
