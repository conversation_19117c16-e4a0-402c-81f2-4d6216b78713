import 'package:gdjg_pure_flutter/data/worker_data/worker_statistics/ds/model/business_get_project_count_net_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/worker_statistics/ds/param/business_get_project_count_param_model.dart';
import 'package:net_plugin/pigeon/api_generated/net_channel.g.dart';
import 'package:net_plugin/pigeon/net_result/resp_result.dart';
import 'package:net_plugin/pigeon/net_service/net_core.dart';

class WorkerStatisticsRds {
  ///工人获取统计
  Future<RespResult<BusinessGetProjectCountNetModel>>
      getBusinessGetProjectCount(
          BusinessGetProjectCountParamModel params) async {
    return await NetCore.requestJGPHP(
        BaseBizRequestEntity(
            url: '/api/v3/business/get-project-count',
            method: HTTP_METHOD.GET,
            content: params.toMap(),
            requestExtra: RequestExtra(showLoading: true)),
        (json) => BusinessGetProjectCountNetModel.fromJson(json));
  }
}
