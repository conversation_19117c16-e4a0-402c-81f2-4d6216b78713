class BusinessGetProjectCountParamModel {

  /// 
  final String? identity;

  /// 
  final String? start_time;

  /// 
  final String? end_time;

  /// 
  final String? business_type;

  /// 
  final String? work_notes;

  /// 0 未结清项目。  1 已结清项目，
  final String? status;

  /// 工量分项id 仅business_type为2时生效
  final String? unit_work_type;

   BusinessGetProjectCountParamModel({
    this.identity,
    this.start_time,
    this.end_time,
    this.business_type,
    this.work_notes,
    this.status,
    this.unit_work_type,
  });

  Map<String, Object> toMap() {
    return {
      'identity': identity ?? "",
      'start_time': start_time?? "",
      'end_time': end_time?? "",
      'business_type': business_type?? "",
      'work_notes': work_notes?? "",
      'status': status?? "",
      'unit_work_type': unit_work_type?? "",
    };
  }
}

