import 'dart:ffi';

import 'package:gdjg_pure_flutter/data/worker_data/worker_manager/ds/param/update_show_param_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/worker_manager/ds/param/worker_quit_param_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/worker_manager/ds/param/worker_share_info_param_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/worker_manager/ds/param/worker_update_param_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/worker_manager/ds/worker_manager_rds.dart';
import 'package:gdjg_pure_flutter/data/worker_data/worker_manager/repo/model/worker_share_info_biz_model.dart';
import 'package:net_plugin/pigeon/net_result/resp_fail.dart';
import 'package:net_plugin/pigeon/net_result/resp_result.dart';

/// 工友管理仓库
class WorkerManagerRepo {
  final _workerManagerRds = WorkerManagerRds();

  /// 工友退场
  Future<RespResult<bool>> workerQuit(WorkerQuitParamModel param) async {
    return await _workerManagerRds.workerQuit(param);
  }

  /// 删除工友
  Future<RespResult<bool>> deleteWorker(String workerId) async {
    if (workerId.isEmpty) {
      return RespFail.buildProcessFail("工友ID不能为空");
    }
    return await _workerManagerRds.deleteWorker(workerId);
  }

  /// 更新工友查看记工数据权限
  Future<RespResult<bool>> updateWorkerPermission(
      UpdateShowParamModel params) async {
    return await _workerManagerRds.updateWorkerPermission(params);
  }

  /// 工人填写手机号页面分享-获取分享信息
  Future<RespResult<ShareGetTelShareBizModel?>> getWorkerFillTelShareInfo(
      ShareGetTelShareParamModel params) async {
    final result = await _workerManagerRds.getShareInfo(params);
    return result.map((result) => result?.transform());
  }

  /// 修改工友姓名电话接口
  Future<RespResult<bool>> updateWorkerNamePhone(
      WorkerUpdateParamModel params) async {
    return await _workerManagerRds.updateWorkerNamePhone(params);
  }
}
