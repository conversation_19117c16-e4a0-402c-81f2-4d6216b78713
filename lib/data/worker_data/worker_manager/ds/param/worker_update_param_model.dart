class WorkerUpdateParamModel {
  /// 工人id
  final String workerId;

  /// 电话
  final String? tel;

  /// 姓名
  final String name;

  /// name：只修改姓名(可不用传电话)
  final String? type;

  ///
  final String? workNote;

  ///
  final String? deptId;

  WorkerUpdateParamModel({
    required this.workerId,
    required this.name,
    this.tel,
    this.type,
    this.workNote,
    this.deptId,
  });

  Map<String, Object> toMap() {
    var map = <String, Object>{};
    map["worker_id"] = workerId;
    map["name"] = name;
    if (tel != null) map["tel"] = tel!;
    if (type != null) map["type"] = type!;
    if (workNote != null) map["work_note"] = workNote!;
    if (deptId != null) map["dept_id"] = deptId!;
    return map;
  }
}
