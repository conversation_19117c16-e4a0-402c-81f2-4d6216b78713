/// 是否允许在线查看记工数据请求参数
/// @param worker_id 工人id
/// @param is_show 是否允许在线查看记工数据 0-不允许 1-允许查看记工及工资 2-仅允许查看记工
class UpdateShowParamModel {
  /// 必传-工人id
  final String workerId;

  /// 必传-是否允许在线查看记工数据 0-不允许 1-允许查看记工及工资 2-仅允许查看记工
  final WSPermissionType isShow;

  /// dept_id 工友录没有可以不传
  final String? deptId;

  UpdateShowParamModel({
    required this.workerId,
    required this.isShow,
    this.deptId,
  });

  Map<String, Object> toMap() {
    var map = <String, Object>{};
    map["worker_id"] = workerId;
    map["is_show"] = isShow.value;
    if (deptId != null) map["dept_id"] = deptId!;
    return map;
  }
}

/// 是否允许在线查看记工数据 0-不允许 1-允许查看记工及工资 2-仅允许查看记工
enum WSPermissionType {
  /// 不允许
  no(0),
  allow(1),
  onlyAllow(2);

  const WSPermissionType(this.value);

  final int value;
}
