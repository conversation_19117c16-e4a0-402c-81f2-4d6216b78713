import 'package:gdjg_pure_flutter/data/worker_data/worker_manager/ds/model/worker_share_info_net_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/worker_manager/ds/param/update_show_param_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/worker_manager/ds/param/worker_quit_param_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/worker_manager/ds/param/worker_share_info_param_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/worker_manager/ds/param/worker_update_param_model.dart';
import 'package:net_plugin/pigeon/api_generated/net_channel.g.dart';
import 'package:net_plugin/pigeon/net_result/resp_result.dart';
import 'package:net_plugin/pigeon/net_service/net_core.dart';

class WorkerManagerRds {
  /// 工友退场
  /// @param params 请求参数
  Future<RespResult<bool>> workerQuit(WorkerQuitParamModel params) async {
    return await NetCore.requestJGPHP(
        BaseBizRequestEntity(
          url: '/api/v3/worker/quit',
          method: HTTP_METHOD.POST,
          content: params.toMap(),
          requestExtra: RequestExtra(showLoading: true, printResp: true),
        ),
        (json) => true);
  }

  /// 删除工友
  /// @param workerId 工友id
  Future<RespResult<bool>> deleteWorker(String workerId) async {
    return await NetCore.requestJGPHP(
        BaseBizRequestEntity(
          url: '/api/v3/worker/delete',
          method: HTTP_METHOD.POST,
          content: {'worker_id': workerId},
          requestExtra: RequestExtra(showLoading: true, printResp: true),
        ),
        (json) => true);
  }

  /// 更新工友查看记工数据权限
  Future<RespResult<bool>> updateWorkerPermission(
      UpdateShowParamModel params) async {
    return await NetCore.requestJGPHP(
        BaseBizRequestEntity(
          url: '/api/v3/worker/update/show',
          method: HTTP_METHOD.POST,
          content: params.toMap(),
          requestExtra: RequestExtra(showLoading: true, printResp: true),
        ),
        (json) => true);
  }

  /// 工人填写手机号页面分享-获取分享信息
  Future<RespResult<ShareGetTelShareNetModel>> getShareInfo(
      ShareGetTelShareParamModel params) async {
    return await NetCore.requestJGPHP(
        BaseBizRequestEntity(
            url: '/api/v3/worker/share/get_tel_share',
            method: HTTP_METHOD.GET,
            content: params.toMap(),
            requestExtra: RequestExtra(showLoading: true, printResp: true)),
        (json) => ShareGetTelShareNetModel.fromJson(json));
  }

  /// 修改工友姓名电话接口
  Future<RespResult<bool>> updateWorkerNamePhone(
      WorkerUpdateParamModel params) async {
    return await NetCore.requestJGPHP(
        BaseBizRequestEntity(
            url: '/api/v3/worker/update',
            method: HTTP_METHOD.POST,
            content: params.toMap(),
            requestExtra: RequestExtra(showLoading: true, printResp: true)),
        (json) => true);
  }
}
