import 'package:gdjg_pure_flutter/data/worker_data/worker_manager/repo/model/worker_share_info_biz_model.dart';

class ShareGetTelShareNetModel {
  String? web_url;

  /// 小程序ID
  String? original_id;

  /// 路径
  String? mini_path;

  /// 带参数的完整路径
  String? mini_path_full;

  /// 分享code
  String? code;

  /// 分享标题
  String? title;

  /// 分享图片
  String? img_url;

  ShareGetTelShareNetModel();

  Map<String, dynamic> toJson(ShareGetTelShareNetModel instance) {
    var map = <String, Object>{};
    if (web_url != null) map["web_url"] = web_url!;
    if (original_id != null) map["original_id"] = original_id!;
    if (mini_path != null) map["mini_path"] = mini_path!;
    if (mini_path_full != null) map["mini_path_full"] = mini_path_full!;
    if (code != null) map["code"] = code!;
    if (title != null) map["title"] = title!;
    if (img_url != null) map["img_url"] = img_url!;
    return map;
  }

  factory ShareGetTelShareNetModel.fromJson(Map<String, dynamic> json) {
    var netModel = ShareGetTelShareNetModel();
    netModel.web_url = json["web_url"]?.toString();
    netModel.original_id = json["original_id"]?.toString();
    netModel.mini_path = json["mini_path"]?.toString();
    netModel.mini_path_full = json["mini_path_full"]?.toString();
    netModel.code = json["code"]?.toString();
    netModel.title = json["title"]?.toString();
    netModel.img_url = json["img_url"]?.toString();
    return netModel;
  }

  ShareGetTelShareBizModel transform() {
    return ShareGetTelShareBizModel(
      webUrl: web_url ?? "",
      originalId: original_id ?? "",
      miniPath: mini_path ?? "",
      miniPathFull: mini_path_full ?? "",
      code: code ?? "",
      title: title ?? "",
      imgUrl: img_url ?? "",
    );
  }
}