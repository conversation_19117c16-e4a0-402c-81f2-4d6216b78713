import 'package:gdjg_pure_flutter/data/worker_data/corp_select/ds/corp_select_rds.dart';
import 'package:gdjg_pure_flutter/data/worker_data/corp_select/ds/param/corp_select_param_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/corp_select/repo/model/corp_select_biz_model.dart';
import 'package:net_plugin/pigeon/net_result/resp_fail.dart';
import 'package:net_plugin/pigeon/net_result/resp_result.dart';

/// 选择企业仓库
class CorpSelectRepo {
  final _corpSelectRds = CorpSelectRds();

  /// 切换企业
  Future<RespResult<CorpSelectBizModel>> fetchCorpSelect(
      CorpSelectParamModel param) async {
    final result = await _corpSelectRds.getCorpSelect(param);
    final data = result.getSucData();
    if (data == null) {
      return RespFail.buildBizFail(
          result.fail?.code, result.fail?.errorMsg, null, result.fail?.askId);
    }
    return result.map((model) => model!.transform());
  }
}
