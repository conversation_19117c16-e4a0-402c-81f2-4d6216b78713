import 'package:gdjg_pure_flutter/data/worker_data/corp_select/ds/model/net/corp_select_net_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/corp_select/ds/param/corp_select_param_model.dart';
import 'package:net_plugin/pigeon/api_generated/net_channel.g.dart';
import 'package:net_plugin/pigeon/net_result/resp_result.dart';
import 'package:net_plugin/pigeon/net_service/net_core.dart';

/// 企业切换数据源
class CorpSelectRds {
  /// 获取企业切换信息
  Future<RespResult<CorpSelectNetModel>> getCorpSelect(
      CorpSelectParamModel params) async {
    return await NetCore.requestJGPHP(
        BaseBizRequestEntity(
            url: '/api/corp/select',
            method: HTTP_METHOD.POST,
            content: params.toMap(),
            requestExtra: RequestExtra(domain: NET_DOMAIN.JG_PHP, printResp: false, showLoading: false)),
        (json) => CorpSelectNetModel.fromJson(json));
  }
}
