import 'dart:convert';
import '../../../repo/model/corp_select_biz_model.dart';

class CorpSelectNetModel {

  /// 用户id
  double? user_id;

  /// 企业id
  double? corp_id;

  CorpSelectNetModel();

  Map<String, dynamic> toJson() {
    var map = <String, Object>{};
    if (user_id != null) map["user_id"] = user_id!;
    if (corp_id != null) map["corp_id"] = corp_id!;
    return map;
  }

  factory CorpSelectNetModel.fromJson(Map<String, dynamic> json) {
    var netModel = CorpSelectNetModel();
    netModel.user_id = double.tryParse(json["user_id"].toString());
    netModel.corp_id = double.tryParse(json["corp_id"].toString());
    return netModel;
  }

  CorpSelectBizModel transform() {
    return CorpSelectBizModel(
      userId: user_id ?? 0.0,
      corpId: corp_id ?? 0.0,
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}
