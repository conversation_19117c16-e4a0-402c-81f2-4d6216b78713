class CorpSelectParamModel {

  /// 企业id
  String? corp_id;

  /// 用户id
  double? member_id;
  String? member_worker;
  double? token_user_id;
  double? token_corp_id;

  CorpSelectParamModel({
    this.corp_id,
    this.member_id,
    this.member_worker,
    this.token_user_id,
    this.token_corp_id,
  });

  Map<String, Object> toMap() {
    var map = <String, Object>{};
    if (corp_id != null) map["corp_id"] = corp_id!;
    if (member_id != null) map["member_id"] = member_id!;
    if (member_worker != null) map["member_worker"] = member_worker!;
    if (token_user_id != null) map["token_user_id"] = token_user_id!;
    if (token_corp_id != null) map["token_corp_id"] = token_corp_id!;
    return map;
  }
}
