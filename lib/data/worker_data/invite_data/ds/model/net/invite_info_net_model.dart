import 'dart:convert';
import '../../../repo/model/invite_info_biz_model.dart';

class InviteInfoNetModel {

  /// 类型
  String? type;

  /// 成员ID
  double? member_id;

  /// 公司ID
  double? corp_id;

  /// 部门ID
  double? dept_id;

  /// 班组长UID
  double? leader_uid;

  /// 工友ID
  double? worker_id;

  /// 时间
  double? time;

  /// 部门名称
  String? dept_name;

  InviteInfoNetModel();

  Map<String, dynamic> toJson() {
    var map = <String, Object>{};
    if (type != null) map["type"] = type!;
    if (member_id != null) map["member_id"] = member_id!;
    if (corp_id != null) map["corp_id"] = corp_id!;
    if (dept_id != null) map["dept_id"] = dept_id!;
    if (leader_uid != null) map["leader_uid"] = leader_uid!;
    if (worker_id != null) map["worker_id"] = worker_id!;
    if (time != null) map["time"] = time!;
    if (dept_name != null) map["dept_name"] = dept_name!;
    return map;
  }

  factory InviteInfoNetModel.fromJson(Map<String, dynamic> json) {
    var netModel = InviteInfoNetModel();
    netModel.type = json["type"]?.toString();
    netModel.member_id = double.tryParse(json["member_id"]?.toString() ?? "0");
    netModel.corp_id = double.tryParse(json["corp_id"]?.toString() ?? "0");
    netModel.dept_id = double.tryParse(json["dept_id"]?.toString() ?? "0");
    netModel.leader_uid = double.tryParse(json["leader_uid"]?.toString() ?? "0");
    netModel.worker_id = double.tryParse(json["worker_id"]?.toString() ?? "0");
    netModel.time = double.tryParse(json["time"]?.toString() ?? "0");
    netModel.dept_name = json["dept_name"]?.toString();
    return netModel;
  }

  InviteInfoBizModel transform() {
    return InviteInfoBizModel(
      type: type ?? "",
      memberId: member_id ?? 0.0,
      corpId: corp_id ?? 0.0,
      deptId: dept_id ?? 0.0,
      leaderUid: leader_uid ?? 0.0,
      workerId: worker_id ?? 0.0,
      time: time ?? 0.0,
      deptName: dept_name ?? "",
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}
