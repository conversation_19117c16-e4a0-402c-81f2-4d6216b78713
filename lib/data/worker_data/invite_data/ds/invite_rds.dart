import 'package:gdjg_pure_flutter/data/worker_data/invite_data/ds/model/net/invite_info_net_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/invite_data/ds/model/param/invite_accept_param_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/invite_data/ds/model/param/invite_info_param_model.dart';
import 'package:net_plugin/pigeon/api_generated/net_channel.g.dart';
import 'package:net_plugin/pigeon/net_result/resp_result.dart';
import 'package:net_plugin/pigeon/net_service/net_core.dart';

class InviteRds {
  /// 获取邀请信息
  Future<RespResult<InviteInfoNetModel>> getInviteInfo(
      InviteInfoParamModel params) async {
    return await NetCore.requestJGPHP(
        BaseBizRequestEntity(
            url: '/api/v3/invite/info',
            method: HTTP_METHOD.GET,
            content: params.toMap(),
            requestExtra: RequestExtra(showLoading: true, printResp: true)),
        (json) => InviteInfoNetModel.fromJson(json));
  }

  /// 接受邀请
  Future<RespResult<bool>> acceptInvite(
      InviteAcceptParamModel params) async {
    return await NetCore.requestJGPHP(
        BaseBizRequestEntity(
            url: '/api/v3/invite/accept',
            method: HTTP_METHOD.POST,
            content: params.toMap(),
            requestExtra: RequestExtra(showLoading: true, printResp: true)),
        (json) => true);
  }
}
