import 'dart:convert';

class InviteInfoBizModel {

  /// 类型
  String type;

  /// 成员ID
  double memberId;

  /// 公司ID
  double corpId;

  /// 部门ID
  double deptId;

  /// 班组长UID
  double leaderUid;

  /// 工友ID
  double workerId;

  /// 时间
  double time;

  /// 部门名称
  String deptName;

  InviteInfoBizModel({
    this.type = "",
    this.memberId = 0.0,
    this.corpId = 0.0,
    this.deptId = 0.0,
    this.leaderUid = 0.0,
    this.workerId = 0.0,
    this.time = 0.0,
    this.deptName = "",
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}
