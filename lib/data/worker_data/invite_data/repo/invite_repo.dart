import 'package:gdjg_pure_flutter/data/worker_data/invite_data/ds/invite_rds.dart';
import 'package:gdjg_pure_flutter/data/worker_data/invite_data/ds/model/param/invite_accept_param_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/invite_data/ds/model/param/invite_info_param_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/invite_data/repo/model/invite_info_biz_model.dart';
import 'package:net_plugin/pigeon/net_result/resp_result.dart';

class InviteRepo {
  final _inviteRds = InviteRds();

  /// 获取邀请信息
  Future<RespResult<InviteInfoBizModel>> getInviteInfo(
      InviteInfoParamModel param) async {
    final result = await _inviteRds.getInviteInfo(param);
    return result.map((netModel) => netModel?.transform() ?? InviteInfoBizModel());
  }

  /// 接受邀请
  Future<RespResult<bool>> acceptInvite(
      InviteAcceptParamModel param) async {
    final result = await _inviteRds.acceptInvite(param);
    return result;
  }

}
