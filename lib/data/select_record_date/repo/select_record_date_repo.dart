
import 'package:gdjg_pure_flutter/data/select_record_date/ds/model/net/net_model.dart';
import 'package:gdjg_pure_flutter/data/select_record_date/ds/model/param/param_model.dart';
import 'package:gdjg_pure_flutter/data/select_record_date/ds/model/select_record_date_rds.dart';
import 'package:gdjg_pure_flutter/data/select_record_date/repo/model/biz_model.dart';
import 'package:net_plugin/pigeon/net_result/resp_result.dart';

class SelectRecordDateRepo {
  final _rds = SelectRecordDateRds();

  Future<RespResult<BusinessGetCalendarBizModel?>> getCalendarBusinessInfo(BusinessGetCalendarParamModel param) async {
    var result = await _rds.getCalendarBusinessInfo(param);
    return result.map(_transformProjectList);
  }

  BusinessGetCalendarBizModel? _transformProjectList(BusinessGetCalendarNetModel? netModel) {
    return netModel?.transform();
  }

}