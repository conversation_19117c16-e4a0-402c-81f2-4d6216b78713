import 'dart:convert';

class BusinessGetCalendarBizModel {

  /// 日历列表
  List<BusinessGetCalendarABizModel> list;

  BusinessGetCalendarBizModel({
    this.list = const [],
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class BusinessGetCalendarABizModel {

  /// 日期
  String date;

  /// 列表
  List<BusinessGetCalendarBBizModel> list;

  BusinessGetCalendarABizModel({
    this.date = "",
    this.list = const [],
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class BusinessGetCalendarBBizModel {

  /// 日期
  String day;

  /// 业务类型 1记工天，2记工量，3记工天，4借支, 5支出, 6包工， 7小时工
  String businessTypes;

  BusinessGetCalendarBBizModel({
    this.day = "",
    this.businessTypes = "",
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}

