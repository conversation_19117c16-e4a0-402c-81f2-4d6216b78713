class BusinessGetCalendarParamModel {

  /// 开始时间
  final String? start_date;

  /// 结束时间
  final String? end_date;

  /// 项目id
  final String? work_note;

  /// 工本id
  final String? note_id;


   BusinessGetCalendarParamModel({
    this.start_date,
    this.end_date,
    this.work_note,
    this.note_id,
  });

  Map<String, Object> toMap() {
    var map = <String, Object>{};
      if (start_date != null) map["start_date"] = start_date!;
      if (end_date != null) map["end_date"] = end_date!;
      if (work_note != null) map["work_note"] = work_note!;
      if (note_id != null) map["note_id"] = note_id!;
    return map;
  }
}

