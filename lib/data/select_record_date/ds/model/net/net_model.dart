import 'dart:convert';

import 'package:gdjg_pure_flutter/data/select_record_date/repo/model/biz_model.dart';

class BusinessGetCalendarNetModel {

  /// 日历列表
  List<BusinessGetCalendarANetModel>? list;

  BusinessGetCalendarNetModel();

  Map<String, dynamic> toJson(BusinessGetCalendarNetModel instance) {
    var map = <String, Object>{};
    if (list != null) map["list"] = list!;
    return map;
  }

  factory BusinessGetCalendarNetModel.fromJson(Map<String, dynamic> json) {
    var netModel = BusinessGetCalendarNetModel();
    netModel.list = (json["list"] as List<dynamic>?)
      ?.map((e) => BusinessGetCalendarANetModel.fromJson(e as Map<String, dynamic>))
      .toList();
    return netModel;
  }

  BusinessGetCalendarBizModel transform() {
    return BusinessGetCalendarBizModel(
      list: list?.map((e) => e.transform()).toList() ?? [],
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class BusinessGetCalendarANetModel {

  /// 日期
  String? date;

  /// 列表
  List<BusinessGetCalendarBNetModel>? list;

  BusinessGetCalendarANetModel();

  Map<String, dynamic> toJson(BusinessGetCalendarANetModel instance) {
    var map = <String, Object>{};
    if (date != null) map["date"] = date!;
    if (list != null) map["list"] = list!;
    return map;
  }

  factory BusinessGetCalendarANetModel.fromJson(Map<String, dynamic> json) {
    var netModel = BusinessGetCalendarANetModel();
    netModel.date = json["date"]?.toString();
    netModel.list = (json["list"] as List<dynamic>?)
      ?.map((e) => BusinessGetCalendarBNetModel.fromJson(e as Map<String, dynamic>))
      .toList();
    return netModel;
  }

  BusinessGetCalendarABizModel transform() {
    return BusinessGetCalendarABizModel(
      date: date ?? "",
      list: list?.map((e) => e.transform()).toList() ?? [],
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class BusinessGetCalendarBNetModel {

  /// 日期
  String? day;

  /// 业务类型 1记工天，2记工量，3记工天，4借支, 5支出, 6包工， 7小时工
  String? business_types;

  BusinessGetCalendarBNetModel();

  Map<String, dynamic> toJson(BusinessGetCalendarBNetModel instance) {
    var map = <String, Object>{};
    if (day != null) map["day"] = day!;
    if (business_types != null) map["business_types"] = business_types!;
    return map;
  }

  factory BusinessGetCalendarBNetModel.fromJson(Map<String, dynamic> json) {
    var netModel = BusinessGetCalendarBNetModel();
    netModel.day = json["day"]?.toString();
    netModel.business_types = json["business_types"]?.toString();
    return netModel;
  }

  BusinessGetCalendarBBizModel transform() {
    return BusinessGetCalendarBBizModel(
      day: day ?? "",
      businessTypes: business_types ?? "",
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

