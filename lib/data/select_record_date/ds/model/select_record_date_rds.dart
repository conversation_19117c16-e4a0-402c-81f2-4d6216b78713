
import 'package:gdjg_pure_flutter/data/select_record_date/ds/model/net/net_model.dart';
import 'package:net_plugin/pigeon/api_generated/net_channel.g.dart';
import 'package:net_plugin/pigeon/net_result/resp_result.dart';
import 'package:net_plugin/pigeon/net_service/net_core.dart';

import 'param/param_model.dart';

class SelectRecordDateRds {

  Future<RespResult<BusinessGetCalendarNetModel>> getCalendarBusinessInfo(BusinessGetCalendarParamModel param) async {
    return await NetCore.requestJGPHP(
        BaseBizRequestEntity(
            url: '/api/business/get-calendar',
            method: HTTP_METHOD.GET,
            content: param.toMap()),
            (json) => BusinessGetCalendarNetModel.fromJson(json));
  }
}