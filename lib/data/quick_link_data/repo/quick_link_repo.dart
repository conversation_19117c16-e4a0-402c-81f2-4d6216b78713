import 'package:gdjg_pure_flutter/data/quick_link_data/ds/quick_link_rds.dart';
import 'package:gdjg_pure_flutter/data/quick_link_data/repo/model/quick_link_entity.dart';
import 'package:gdjg_pure_flutter/data/quick_link_data/ds/model/net/quick_link_net_model.dart';
import 'package:gdjg_pure_flutter/model/RecordNoteType.dart';
import 'package:net_plugin/pigeon/net_result/resp_result.dart';

class QuickLinkRepo {

  final _quickLinkRds = QuickLinkRds();

  Future<RespResult<List<QuickLinkEntity>>> getQuickLinkData(
      RecordNoteType noteType) async {
    var param = RecordNoteType.personal == noteType
        ? "CALENDAR_WORKER"
        : "PROJECT_CALENDAR_TEAM";
    final result = await _quickLinkRds.getQuickLinkData(param);
    return result.map(_transform);
  }

  List<QuickLinkEntity> _transform(QuickLinkAModel? netModel) {
    var list = netModel?.list?.map((toElement) =>
        QuickLinkEntity(
          code: toElement.code ?? "",
          icon: toElement.icon ?? "",
          title: toElement.title ?? "",
          dest: toElement.dest ?? "",
          url: toElement.url ?? "",
          webTitle: toElement.web_title ?? "",
          alert: toElement.alert ?? "",
          miniAppid: toElement.mini_appid ?? "",
          originalId: toElement.original_id ?? "",
          miniPath: toElement.mini_path ?? "",
        )
    ).toList();
    return list ?? [];
  }
}
