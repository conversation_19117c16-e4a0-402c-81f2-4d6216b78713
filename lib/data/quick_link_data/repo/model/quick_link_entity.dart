import 'dart:convert';

class QuickLinkEntity {
  /// 埋点标识
  String code;

  /// 图标
  String icon;

  /// 标题
  String title;

  /// 目的地 url H5链接 browser_url 外部浏览器打开的H5链接 mini_app 小程序 insurance 务工保险 VIP 购买VIP页面 vip_options 会员多选项弹窗 show_job_list 展示招工列表 show_machine_list 展示机械列表 show_bidding_list 展示招标列表
  String dest;

  /// 仅dest为url或browser_url时 客户端需要将 {token} {identity} {source} {uid} {env} {version} 替换成对应值
  String url;

  /// 网页标题 仅dest为url时
  String webTitle;

  /// 跳转前弹窗提示文案 为空则不弹窗
  String alert;

  /// 仅dest为mini_app时
  String miniAppid;

  /// 仅dest为mini_app时
  String originalId;

  /// 仅dest为mini_app时
  String miniPath;

  /// 仅工人大日历页 优先展示 值连续，值变更时客户端需添加分隔线
  double highlight;

  QuickLinkEntity({
    this.code = "",
    this.icon = "",
    this.title = "",
    this.dest = "",
    this.url = "",
    this.webTitle = "",
    this.alert = "",
    this.miniAppid = "",
    this.originalId = "",
    this.miniPath = "",
    this.highlight = 0.0,
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}
