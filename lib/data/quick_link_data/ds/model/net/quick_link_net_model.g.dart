// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'quick_link_net_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************
QuickLinkAModel _$QuickLinkAModelFromJson(
        Map<String, dynamic> json) =>
    QuickLinkAModel()
      ..list = (json['list'] as List<dynamic>?)
          ?.map(
              (e) => QuickLinkBModel.fromJson(e as Map<String, dynamic>))
          .toList();

Map<String, dynamic> _$QuickLinkAModelToJson(
        QuickLinkAModel instance) =>
    <String, dynamic>{
      'list': instance.list,
    };

QuickLinkBModel _$QuickLinkBModelFromJson(
        Map<String, dynamic> json) =>
    QuickLinkBModel()
      ..code = json['code'] as String?
      ..icon = json['icon'] as String?
      ..title = json['title'] as String?
      ..dest = json['dest'] as String?
      ..url = json['url'] as String?
      ..web_title = json['web_title'] as String?
      ..alert = json['alert'] as String?
      ..mini_appid = json['mini_appid'] as String?
      ..original_id = json['original_id'] as String?
      ..mini_path = json['mini_path'] as String?
      ..highlight = (json['highlight'] as num?)?.toDouble();

Map<String, dynamic> _$QuickLinkBModelToJson(
        QuickLinkBModel instance) =>
    <String, dynamic>{
      'code': instance.code,
      'icon': instance.icon,
      'title': instance.title,
      'dest': instance.dest,
      'url': instance.url,
      'web_title': instance.web_title,
      'alert': instance.alert,
      'mini_appid': instance.mini_appid,
      'original_id': instance.original_id,
      'mini_path': instance.mini_path,
      'highlight': instance.highlight,
    };
