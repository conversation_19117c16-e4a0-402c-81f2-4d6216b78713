import 'package:gdjg_pure_flutter/data/quick_link_data/ds/model/net/quick_link_net_model.dart';
import 'package:net_plugin/pigeon/api_generated/net_channel.g.dart';
import 'package:net_plugin/pigeon/net_result/resp_result.dart';
import 'package:net_plugin/pigeon/net_service/net_core.dart';

class QuickLinkRds {
  Future<RespResult<QuickLinkAModel>> getQuickLinkData(String params) async {
    var map = {"page": params};
    return await NetCore.requestJGPHP(
        BaseBizRequestEntity(
            url: '/api/v3/setting/quick_link?page=$params',
            method: HTTP_METHOD.GET,
        ),
        (json) => QuickLinkAModel.fromJson(json));
  }
}
