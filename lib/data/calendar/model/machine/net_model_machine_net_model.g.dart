// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'net_model_machine_net_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

NetModelMachineNetModel _$NetModelMachineNetModelFromJson(
        Map<String, dynamic> json) =>
    NetModelMachineNetModel()
      ..list = (json['list'] as List<dynamic>?)
          ?.map((e) =>
              NetModelMachineANetModel.fromJson(e as Map<String, dynamic>))
          .toList()
      ..filter = json['filter'] == null
          ? null
          : FilterNetModel.fromJson(json['filter'] as Map<String, dynamic>)
      ..dest = json['dest'] == null
          ? null
          : DestNetModel.fromJson(json['dest'] as Map<String, dynamic>);

Map<String, dynamic> _$NetModelMachineNetModelToJson(
        NetModelMachineNetModel instance) =>
    <String, dynamic>{
      'list': instance.list,
      'filter': instance.filter,
      'dest': instance.dest,
    };

NetModelMachineANetModel _$NetModelMachineANetModelFromJson(
        Map<String, dynamic> json) =>
    NetModelMachineANetModel()
      ..uu = json['uu'] as String?
      ..title = json['title'] as String?
      ..time = json['time'] as String?
      ..status = json['status'] as String?
      ..area = json['area'] as String?
      ..tel = json['tel'] as String?
      ..user = json['user'] as String?
      ..tag = (json['tag'] as List<dynamic>?)?.map((e) => e as String).toList()
      ..mode = json['mode'] as String?;

Map<String, dynamic> _$NetModelMachineANetModelToJson(
        NetModelMachineANetModel instance) =>
    <String, dynamic>{
      'uu': instance.uu,
      'title': instance.title,
      'time': instance.time,
      'status': instance.status,
      'area': instance.area,
      'tel': instance.tel,
      'user': instance.user,
      'tag': instance.tag,
      'mode': instance.mode,
    };

FilterNetModel _$FilterNetModelFromJson(Map<String, dynamic> json) =>
    FilterNetModel()
      ..mode = json['mode'] as String?
      ..area_id = json['area_id'] as String?
      ..type = json['type'] as String?
      ..page = json['page'] as String?
      ..area_name = json['area_name'] as String?;

Map<String, dynamic> _$FilterNetModelToJson(FilterNetModel instance) =>
    <String, dynamic>{
      'mode': instance.mode,
      'area_id': instance.area_id,
      'type': instance.type,
      'page': instance.page,
      'area_name': instance.area_name,
    };

DestNetModel _$DestNetModelFromJson(Map<String, dynamic> json) => DestNetModel()
  ..type = json['type'] as String?
  ..original_id = json['original_id'] as String?
  ..mini_appid = json['mini_appid'] as String?
  ..mini_path_list = json['mini_path_list'] as String?
  ..mini_path_detail = json['mini_path_detail'] as String?
  ..alert = json['alert'] as String?;

Map<String, dynamic> _$DestNetModelToJson(DestNetModel instance) =>
    <String, dynamic>{
      'type': instance.type,
      'original_id': instance.original_id,
      'mini_appid': instance.mini_appid,
      'mini_path_list': instance.mini_path_list,
      'mini_path_detail': instance.mini_path_detail,
      'alert': instance.alert,
    };
