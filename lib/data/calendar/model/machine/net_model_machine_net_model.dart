import 'dart:convert';

import 'package:json_annotation/json_annotation.dart';

import 'net_model_machine_biz_model.dart';

part 'net_model_machine_net_model.g.dart';

@JsonSerializable()
class NetModelMachineNetModel {
  List<NetModelMachineANetModel>? list;

  /// 实际筛选条件
  FilterNetModel? filter;

  /// 目的地
  DestNetModel? dest;

  NetModelMachineNetModel();

  factory NetModelMachineNetModel.fromJson(Map<String, dynamic> json) =>
      _$NetModelMachineNetModelFromJson(json);

  Map<String, dynamic> toJson() => _$NetModelMachineNetModelToJson(this);

  NetModelMachineBizModel transform() {
    return NetModelMachineBizModel(
      list: list?.map((e) => e.transform()).toList() ?? [],
      filter: filter?.transform(),
      dest: dest?.transform(),
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class NetModelMachineANetModel {
  /// 信息唯一标识
  String? uu;

  /// 标题
  String? title;

  /// 发布日期（Y-M-D H:i）
  String? time;

  /// 完成状态 【求租】1:求租中，2：已租到 【出租】1:出租中，2：已出租 【出售】1:待交易，2：已完成 【求购】1:待交易，2：已完成
  String? status;

  /// 施工地址
  String? area;

  /// 电话
  String? tel;

  /// 发布者名称
  String? user;

  /// 标签
  List<String>? tag;

  /// 信息类型  1求租  2出租  3出售 4求购
  String? mode;

  NetModelMachineANetModel();

  factory NetModelMachineANetModel.fromJson(Map<String, dynamic> json) =>
      _$NetModelMachineANetModelFromJson(json);

  Map<String, dynamic> toJson() => _$NetModelMachineANetModelToJson(this);

  NetModelMachineABizModel transform() {
    return NetModelMachineABizModel(
      uu: uu ?? "",
      title: title ?? "",
      time: time ?? "",
      status: status ?? "",
      area: area ?? "",
      tel: tel ?? "",
      user: user ?? "",
      tag: tag ?? const [],
      mode: mode ?? "",
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class FilterNetModel {
  String? mode;
  String? area_id;
  String? type;
  String? page;

  /// 默认地区(area_id传0)时返回地区名
  String? area_name;

  FilterNetModel();

  factory FilterNetModel.fromJson(Map<String, dynamic> json) =>
      _$FilterNetModelFromJson(json);

  Map<String, dynamic> toJson() => _$FilterNetModelToJson(this);

  FilterBizModel transform() {
    return FilterBizModel(
      mode: mode ?? "",
      areaId: area_id ?? "",
      type: type ?? "",
      page: page ?? "",
      areaName: area_name ?? "",
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class DestNetModel {
  /// 跳转类型 mini_app 小程序
  String? type;
  String? original_id;
  String? mini_appid;

  /// 小程序列表路径 目前恒为空
  String? mini_path_list;

  /// 小程序详情路径 需要拼接uuid及mode
  String? mini_path_detail;

  /// 跳转前弹窗提示文案 为空则不弹窗
  String? alert;

  DestNetModel();

  factory DestNetModel.fromJson(Map<String, dynamic> json) =>
      _$DestNetModelFromJson(json);

  Map<String, dynamic> toJson() => _$DestNetModelToJson(this);

  DestBizModel transform() {
    return DestBizModel(
      type: type ?? "",
      originalId: original_id ?? "",
      miniAppid: mini_appid ?? "",
      miniPathList: mini_path_list ?? "",
      miniPathDetail: mini_path_detail ?? "",
      alert: alert ?? "",
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}
