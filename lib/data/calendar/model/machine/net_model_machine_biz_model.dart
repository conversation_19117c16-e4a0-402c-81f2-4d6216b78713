import 'dart:convert';

class NetModelMachineBizModel {
  List<NetModelMachineABizModel> list;

  /// 实际筛选条件
  final FilterBizModel? filter;

  /// 目的地
  final DestBizModel? dest;

  NetModelMachineBizModel({
    this.list = const [],
    this.filter,
    this.dest,
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class NetModelMachineABizModel {
  /// 信息唯一标识
  String uu;

  /// 标题
  String title;

  /// 发布日期（Y-M-D H:i）
  String time;

  /// 完成状态 【求租】1:求租中，2：已租到 【出租】1:出租中，2：已出租 【出售】1:待交易，2：已完成 【求购】1:待交易，2：已完成
  String status;

  /// 施工地址
  String area;

  /// 电话
  String tel;

  /// 发布者名称
  String user;

  /// 标签
  List<String> tag;

  /// 信息类型  1求租  2出租  3出售 4求购
  String mode;

  NetModelMachineABizModel({
    this.uu = "",
    this.title = "",
    this.time = "",
    this.status = "",
    this.area = "",
    this.tel = "",
    this.user = "",
    this.tag = const [],
    this.mode = "",
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class FilterBizModel {
  String mode;
  String areaId;
  String type;
  String page;

  /// 默认地区(area_id传0)时返回地区名
  String areaName;

  FilterBizModel({
    this.mode = "",
    this.areaId = "",
    this.type = "",
    this.page = "",
    this.areaName = "",
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class DestBizModel {
  /// 跳转类型 mini_app 小程序
  String type;
  String originalId;
  String miniAppid;

  /// 小程序列表路径 目前恒为空
  String miniPathList;

  /// 小程序详情路径 需要拼接uuid及mode
  String miniPathDetail;

  /// 跳转前弹窗提示文案 为空则不弹窗
  String alert;

  DestBizModel({
    this.type = "",
    this.originalId = "",
    this.miniAppid = "",
    this.miniPathList = "",
    this.miniPathDetail = "",
    this.alert = "",
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}
