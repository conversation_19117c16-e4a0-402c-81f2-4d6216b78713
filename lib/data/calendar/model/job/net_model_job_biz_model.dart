import 'dart:convert';

class NetModelJobBizModel {
  List<NetModelJobABizModel> list;

  /// 实际筛选条件
  final FilterBizModel? filter;

  /// 目的地
  final DestBizModel? dest;

  NetModelJobBizModel({
    this.list = const [],
    this.filter,
    this.dest,
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class NetModelJobABizModel {
  /// 实名状态-认证等级：0-未认证 1-个人认证 2-企业认证-公司 3-企业认证-个体工商户
  double checkDegree;
  double id;

  /// 地址
  String showAddress;

  /// 标签
  List<ShowLabelV2BizModel> showLabelV2;

  /// 发布时间
  String timeStr;

  /// 标题
  String title;

  NetModelJobABizModel({
    this.checkDegree = 0.0,
    this.id = 0.0,
    this.showAddress = "",
    this.showLabelV2 = const [],
    this.timeStr = "",
    this.title = "",
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class ShowLabelV2BizModel {
  int type;
  String name;

  ShowLabelV2BizModel({
    this.type = -1,
    this.name = "",
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class FilterBizModel {
  /// 地区id
  String areaId;

  /// 分页用time 第一页不传 其他页透传该参数
  String time;

  /// 工种id
  String occupationId;

  /// 页码
  String page;

  /// 默认地区(area_id传0)时返回地区名
  String areaName;

  FilterBizModel({
    this.areaId = "",
    this.time = "",
    this.occupationId = "",
    this.page = "",
    this.areaName = "",
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class DestBizModel {
  /// 跳转类型 mini_app 小程序
  String type;

  /// 小程序id
  String originalId;

  /// 小程序appid
  String miniAppid;

  /// 小程序列表路径
  String miniPathList;

  /// 小程序详情路径 需要拼接id
  String miniPathDetail;

  /// 跳转前弹窗提示文案 为空则不弹窗
  String alert;

  DestBizModel({
    this.type = "",
    this.originalId = "",
    this.miniAppid = "",
    this.miniPathList = "",
    this.miniPathDetail = "",
    this.alert = "",
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}
