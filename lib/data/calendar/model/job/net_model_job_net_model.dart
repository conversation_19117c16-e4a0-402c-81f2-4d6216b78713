import 'dart:convert';

import 'package:json_annotation/json_annotation.dart';

import 'net_model_job_biz_model.dart';

part 'net_model_job_net_model.g.dart';

@JsonSerializable()
class NetModelJobNetModel {
  List<NetModelJobANetModel>? list;

  /// 实际筛选条件
  FilterNetModel? filter;

  /// 目的地
  DestNetModel? dest;

  NetModelJobNetModel();

  factory NetModelJobNetModel.fromJson(Map<String, dynamic> json) =>
      _$NetModelJobNetModelFromJson(json);

  Map<String, dynamic> toJson() => _$NetModelJobNetModelToJson(this);

  NetModelJobBizModel transform() {
    return NetModelJobBizModel(
      list: list?.map((e) => e.transform()).toList() ?? [],
      filter: filter?.transform(),
      dest: dest?.transform(),
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class NetModelJobANetModel {
  /// 实名状态-认证等级：0-未认证 1-个人认证 2-企业认证-公司 3-企业认证-个体工商户
  double? check_degree;
  double? id;

  /// 地址
  String? show_address;

  /// 标签
  List<ShowLabelV2NetModel>? show_label_v2;

  /// 发布时间
  String? time_str;

  /// 标题
  String? title;

  NetModelJobANetModel();

  factory NetModelJobANetModel.fromJson(Map<String, dynamic> json) =>
      _$NetModelJobANetModelFromJson(json);

  Map<String, dynamic> toJson() => _$NetModelJobANetModelToJson(this);

  NetModelJobABizModel transform() {
    return NetModelJobABizModel(
      checkDegree: check_degree ?? 0.0,
      id: id ?? 0.0,
      showAddress: show_address ?? "",
      showLabelV2: show_label_v2?.map((e) => e.transform()).toList() ?? [],
      timeStr: time_str ?? "",
      title: title ?? "",
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class ShowLabelV2NetModel {
  int? type;
  String? name;

  ShowLabelV2NetModel();

  factory ShowLabelV2NetModel.fromJson(Map<String, dynamic> json) =>
      _$ShowLabelV2NetModelFromJson(json);

  Map<String, dynamic> toJson() => _$ShowLabelV2NetModelToJson(this);

  ShowLabelV2BizModel transform() {
    return ShowLabelV2BizModel(
      type: type ?? -1,
      name: name ?? "",
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class FilterNetModel {
  /// 地区id
  String? area_id;

  /// 分页用time 第一页不传 其他页透传该参数
  String? time;

  /// 工种id
  String? occupation_id;

  /// 页码
  String? page;

  /// 默认地区(area_id传0)时返回地区名
  String? area_name;

  FilterNetModel();

  factory FilterNetModel.fromJson(Map<String, dynamic> json) =>
      _$FilterNetModelFromJson(json);

  Map<String, dynamic> toJson() => _$FilterNetModelToJson(this);

  FilterBizModel transform() {
    return FilterBizModel(
      areaId: area_id ?? "",
      time: time ?? "",
      occupationId: occupation_id ?? "",
      page: page ?? "",
      areaName: area_name ?? "",
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class DestNetModel {
  /// 跳转类型 mini_app 小程序
  String? type;

  /// 小程序id
  String? original_id;

  /// 小程序appid
  String? mini_appid;

  /// 小程序列表路径
  String? mini_path_list;

  /// 小程序详情路径 需要拼接id
  String? mini_path_detail;

  /// 跳转前弹窗提示文案 为空则不弹窗
  String? alert;

  DestNetModel();

  factory DestNetModel.fromJson(Map<String, dynamic> json) =>
      _$DestNetModelFromJson(json);

  Map<String, dynamic> toJson() => _$DestNetModelToJson(this);

  DestBizModel transform() {
    return DestBizModel(
      type: type ?? "",
      originalId: original_id ?? "",
      miniAppid: mini_appid ?? "",
      miniPathList: mini_path_list ?? "",
      miniPathDetail: mini_path_detail ?? "",
      alert: alert ?? "",
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}
