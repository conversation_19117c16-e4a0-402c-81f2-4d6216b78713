// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'net_model_job_net_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

NetModelJobNetModel _$NetModelJobNetModelFromJson(Map<String, dynamic> json) =>
    NetModelJobNetModel()
      ..list = (json['list'] as List<dynamic>?)
          ?.map((e) => NetModelJobANetModel.fromJson(e as Map<String, dynamic>))
          .toList()
      ..filter = json['filter'] == null
          ? null
          : FilterNetModel.fromJson(json['filter'] as Map<String, dynamic>)
      ..dest = json['dest'] == null
          ? null
          : DestNetModel.fromJson(json['dest'] as Map<String, dynamic>);

Map<String, dynamic> _$NetModelJobNetModelToJson(
        NetModelJobNetModel instance) =>
    <String, dynamic>{
      'list': instance.list,
      'filter': instance.filter,
      'dest': instance.dest,
    };

NetModelJobANetModel _$NetModelJobANetModelFromJson(
        Map<String, dynamic> json) =>
    NetModelJobANetModel()
      ..check_degree = (json['check_degree'] as num?)?.toDouble()
      ..id = (json['id'] as num?)?.toDouble()
      ..show_address = json['show_address'] as String?
      ..show_label_v2 = (json['show_label_v2'] as List<dynamic>?)
          ?.map((e) => ShowLabelV2NetModel.fromJson(e as Map<String, dynamic>))
          .toList()
      ..time_str = json['time_str'] as String?
      ..title = json['title'] as String?;

Map<String, dynamic> _$NetModelJobANetModelToJson(
        NetModelJobANetModel instance) =>
    <String, dynamic>{
      'check_degree': instance.check_degree,
      'id': instance.id,
      'show_address': instance.show_address,
      'show_label_v2': instance.show_label_v2,
      'time_str': instance.time_str,
      'title': instance.title,
    };

ShowLabelV2NetModel _$ShowLabelV2NetModelFromJson(Map<String, dynamic> json) =>
    ShowLabelV2NetModel()
      ..type = json['type'] as int?
      ..name = json['name'] as String?;

Map<String, dynamic> _$ShowLabelV2NetModelToJson(
        ShowLabelV2NetModel instance) =>
    <String, dynamic>{
      'type': instance.type,
      'name': instance.name,
    };

FilterNetModel _$FilterNetModelFromJson(Map<String, dynamic> json) =>
    FilterNetModel()
      ..area_id = json['area_id'] as String?
      ..time = json['time'] as String?
      ..occupation_id = json['occupation_id'] as String?
      ..page = json['page'] as String?
      ..area_name = json['area_name'] as String?;

Map<String, dynamic> _$FilterNetModelToJson(FilterNetModel instance) =>
    <String, dynamic>{
      'area_id': instance.area_id,
      'time': instance.time,
      'occupation_id': instance.occupation_id,
      'page': instance.page,
      'area_name': instance.area_name,
    };

DestNetModel _$DestNetModelFromJson(Map<String, dynamic> json) => DestNetModel()
  ..type = json['type'] as String?
  ..original_id = json['original_id'] as String?
  ..mini_appid = json['mini_appid'] as String?
  ..mini_path_list = json['mini_path_list'] as String?
  ..mini_path_detail = json['mini_path_detail'] as String?
  ..alert = json['alert'] as String?;

Map<String, dynamic> _$DestNetModelToJson(DestNetModel instance) =>
    <String, dynamic>{
      'type': instance.type,
      'original_id': instance.original_id,
      'mini_appid': instance.mini_appid,
      'mini_path_list': instance.mini_path_list,
      'mini_path_detail': instance.mini_path_detail,
      'alert': instance.alert,
    };
