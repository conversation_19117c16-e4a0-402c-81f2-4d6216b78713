class NetModelJobParamModel {
  /// 地区id 不填默认ip所在地地区
  final String area_id;

  /// 工种id 不填默认全部工种
  final String occupation_id;

  /// 第一页不传time 其他页透传第一页返回结果中的filter.time
  final String time;

  /// 页码
  final String page;

  NetModelJobParamModel({
    this.area_id = "",
    this.occupation_id = "",
    this.time = "",
    this.page = "",
  });

  Map<String, String> toMap() {
    return {
      "area_id": area_id,
      "occupation_id": occupation_id,
      "time": time,
      "page": page,
    };
  }
}
