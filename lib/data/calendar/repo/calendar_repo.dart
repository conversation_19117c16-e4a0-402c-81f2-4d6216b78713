import 'package:gdjg_pure_flutter/data/calendar/ds/calendar_rds.dart';
import 'package:gdjg_pure_flutter/data/calendar/ds/model/net/business_get_project_calendar_count_net_model.dart';
import 'package:gdjg_pure_flutter/data/calendar/ds/model/param/business_get_project_calendar_count_param_model.dart';
import 'package:gdjg_pure_flutter/data/calendar/model/job/net_model_job_biz_model.dart';
import 'package:gdjg_pure_flutter/data/calendar/model/job/net_model_job_param_model.dart';
import 'package:gdjg_pure_flutter/data/calendar/model/machine/net_model_machine_biz_model.dart';
import 'package:gdjg_pure_flutter/data/calendar/model/machine/net_model_machine_param_model.dart';
import 'package:gdjg_pure_flutter/data/calendar/repo/model/business_get_project_calendar_count_biz_model.dart';
import 'package:net_plugin/pigeon/net_result/resp_result.dart';

class CalendarRepo {
  final rds = CalendarRds();

  ///获取项目日历数据
  Future<RespResult<BusinessGetProjectCalendarCountBizModel>> getProjectCalendarCount(
      BusinessGetProjectCalendarCountParamModel param) async {
    final result = await rds.getProjectCalendarCount(param);
    return result.map(_transform);
  }

  BusinessGetProjectCalendarCountBizModel _transform(BusinessGetProjectCalendarCountNetModel? netModel) {
    if (netModel == null) {
      return BusinessGetProjectCalendarCountBizModel();
    }
    return netModel.transform();
  }


  /// 查询招工列表数据
  Future<RespResult<NetModelJobBizModel?>> queryRecruitList(
      NetModelJobParamModel params) async {
    final result = await rds.queryRecruitList(params);
    return result.map((i) => i?.transform());
  }

  /// 查询鱼泡机械列表数据
  Future<RespResult<NetModelMachineBizModel?>> queryMechanicalList(
      NetModelMachineParamModel params) async {
    final result = await rds.queryMechanicalList(params);
    return result.map((i) => i?.transform());
  }
}
