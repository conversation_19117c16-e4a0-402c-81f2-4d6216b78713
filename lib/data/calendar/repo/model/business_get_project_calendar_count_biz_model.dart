import 'dart:convert';

import 'package:gdjg_pure_flutter/data/common_data/biz/calendar_biz_model.dart';
import 'package:gdjg_pure_flutter/data/common_data/biz/count_biz_model.dart';

class BusinessGetProjectCalendarCountBizModel {
  /// 统计数据
  final CountBizModel? count;

  /// 日历数据
  List<CalendarEntity> calendar;

  //
  // /// 项目列表
  // List<ProjectEntity> project;
  //
  // /// [4.9]统计汇总
  // final SummaryEntity? summary;

  BusinessGetProjectCalendarCountBizModel({
    this.count,
    this.calendar = const [],
    // this.project = const [],
    // this.summary,
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}
