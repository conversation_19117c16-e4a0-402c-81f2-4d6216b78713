import 'package:gdjg_pure_flutter/data/calendar/ds/model/net/business_get_project_calendar_count_net_model.dart';
import 'package:gdjg_pure_flutter/data/calendar/ds/model/param/business_get_project_calendar_count_param_model.dart';
import 'package:gdjg_pure_flutter/data/calendar/model/job/net_model_job_net_model.dart';
import 'package:gdjg_pure_flutter/data/calendar/model/job/net_model_job_param_model.dart';
import 'package:gdjg_pure_flutter/data/calendar/model/machine/net_model_machine_net_model.dart';
import 'package:gdjg_pure_flutter/data/calendar/model/machine/net_model_machine_param_model.dart';
import 'package:net_plugin/pigeon/api_generated/net_channel.g.dart';
import 'package:net_plugin/pigeon/net_result/resp_result.dart';
import 'package:net_plugin/pigeon/net_service/net_core.dart';

class CalendarRds {
  /// 获取招工列表数据
  Future<RespResult<BusinessGetProjectCalendarCountNetModel>> getProjectCalendarCount(
      BusinessGetProjectCalendarCountParamModel params) async {
    return await NetCore.requestJGPHP(
        BaseBizRequestEntity(
            url: '/api/v3/business/get-project-calendar-count',
            method: HTTP_METHOD.GET,
            content: params.toMap(),
            requestExtra: RequestExtra(showLoading: true, printResp: true)),
        (v) => BusinessGetProjectCalendarCountNetModel.fromJson(v));
  }

  /// 获取招工列表数据
  Future<RespResult<NetModelJobNetModel>> queryRecruitList(
      NetModelJobParamModel params) async {
    return await NetCore.requestJGPHP(
        BaseBizRequestEntity(
            url: '/api/v3/yupao/list/job',
            method: HTTP_METHOD.GET,
            content: params.toMap(),
            requestExtra: RequestExtra(showLoading: true, printResp: true)),
        (v) => NetModelJobNetModel.fromJson(v));
  }

  /// 获取鱼泡机械列表数据
  Future<RespResult<NetModelMachineNetModel>> queryMechanicalList(
      NetModelMachineParamModel params) async {
    return await NetCore.requestJGPHP(
        BaseBizRequestEntity(
            url: '/api/v3/yupao/list/machine',
            method: HTTP_METHOD.GET,
            content: params.toMap(),
            requestExtra: RequestExtra(showLoading: true, printResp: true)),
        (v) => NetModelMachineNetModel.fromJson(v));
  }
}
