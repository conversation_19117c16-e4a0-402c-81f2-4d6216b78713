import 'dart:convert';
import 'package:gdjg_pure_flutter/data/common_data/net/calendar_net_model.dart';
import 'package:gdjg_pure_flutter/data/common_data/net/count_net_model.dart';
import 'package:json_annotation/json_annotation.dart';

import '../../../repo/model/business_get_project_calendar_count_biz_model.dart';

@JsonSerializable()
class BusinessGetProjectCalendarCountNetModel {

  /// 统计数据
  CountNetModel? count;

  /// 日历数据
  List<CalendarNetModel>? calendar;

  BusinessGetProjectCalendarCountNetModel();

  Map<String, dynamic> toJson(BusinessGetProjectCalendarCountNetModel instance) => <String, dynamic>{
      "count": instance.count,
      "calendar": instance.calendar,
    };

  factory BusinessGetProjectCalendarCountNetModel.fromJson(Map<String, dynamic> json) {
    var netModel = BusinessGetProjectCalendarCountNetModel();
    netModel.count = json["count"] == null
      ? null
      : CountNetModel.fromJson(json["count"] as Map<String, dynamic>);
    netModel.calendar = (json["calendar"] as List<dynamic>?)
      ?.map((e) => CalendarNetModel.fromJson(e as Map<String, dynamic>))
      .toList();
    return netModel;
  }

  BusinessGetProjectCalendarCountBizModel transform() {
    return BusinessGetProjectCalendarCountBizModel(
      count: count?.transform(),
      calendar: calendar?.map((e) => e.transform()).toList() ?? [],
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}


