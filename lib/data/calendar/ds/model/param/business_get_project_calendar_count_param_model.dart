class BusinessGetProjectCalendarCountParamModel {

  /// 
  final String? start_time;

  /// 
  final String? end_time;

  /// 账本
  final String? work_notes;

  /// 身份
  final String? identity;

  /// status 项目 1 已结 0 在建
  final String? status;

  /// 暂时不用，工时转化方式
  final String? show_type;

  BusinessGetProjectCalendarCountParamModel({
    this.start_time,
    this.end_time,
    this.work_notes,
    this.identity,
    this.status,
    this.show_type,
  });

  Map<String, Object> toMap() {
    var map = <String, Object>{};
    if (start_time != null) map["start_time"] = start_time!;
    if (end_time != null) map["end_time"] = end_time!;
    if (work_notes != null) map["work_notes"] = work_notes!;
    if (identity != null) map["identity"] = identity!;
    if (status != null) map["status"] = status!;
    if (show_type != null) map["show_type"] = show_type!;
    return map;
  }
}

