class BusinessGetBkIndexBizModel {
  AllBizModel? all;
  List<BusinessGetBkIndexABizModel> list;

  BusinessGetBkIndexBizModel({
    this.all,
    this.list = const [],
  });
}

class AllBizModel {
  String income;
  String expend;

  AllBizModel({
    this.income = "",
    this.expend = "",
  });
}

class BusinessGetBkIndexABizModel {
  double month;
  String income;
  String expend;
  double hasBookkeeping;
  List<UrlBizModel> url;

  BusinessGetBkIndexABizModel({
    this.month = 0.0,
    this.income = "",
    this.expend = "",
    this.hasBookkeeping = 0.0,
    this.url = const [],
  });
}

class UrlBizModel {
  String url;
  String type;
  String baseUrl;

  UrlBizModel({
    this.url = "",
    this.type = "",
    this.baseUrl = "",
  });
}

