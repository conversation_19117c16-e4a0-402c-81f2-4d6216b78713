class DeptGetDeptBizModel {

  /// 
  List<DeptGetDeptABizModel> list;

  /// 当前类型（我创建、我加入）已结束班组的数量
  double ignoredNum;

  DeptGetDeptBizModel({
    this.list = const [],
    this.ignoredNum = 0.0,
  });
}

class DeptGetDeptABizModel {

  /// 班组 id
  double deptId;

  /// 企业 id
  double corpId;

  /// 班组长 id
  double leaderUid;

  /// 班组名称
  String name;

  /// 排序字段
  double order;

  /// 创建时间时间戳（秒级精度）
  double createTime;

  /// 是否暂停更新，0-否，1-是
  double isDeleted;

  /// 记工本 id
  double workNoteId;

  /// 最后一笔记工记账的时间
  String lastBusinessDate;

  /// 最后一次操作的时间
  String lastOperationDate;

  /// 用户在班组内的 user_id
  double selfUser;

  /// 班组类型，1-多人，2-个人
  double identity;

  /// 班组内工友数量
  double workerNum;

  /// 班组创建时间字符串
  String createTimeStr;

  /// 0-我加入的班组，1-我创建的班组
  double isSelfCreated;

  /// 账本工资开关是否打开，1-打开，2-关闭
  double workNoteFeeSwitch;

  /// 当前用户是否是代班，0-否，1-是
  double isAgent;

  /// 工人 id
  double workerId;

  /// 是否显示打卡按钮，0-否，1-是
  double showDkButton;

  DeptGetDeptABizModel({
    this.deptId = 0.0,
    this.corpId = 0.0,
    this.leaderUid = 0.0,
    this.name = "",
    this.order = 0.0,
    this.createTime = 0.0,
    this.isDeleted = 0.0,
    this.workNoteId = 0.0,
    this.lastBusinessDate = "",
    this.lastOperationDate = "",
    this.selfUser = 0.0,
    this.identity = 0.0,
    this.workerNum = 0.0,
    this.createTimeStr = "",
    this.isSelfCreated = 0.0,
    this.workNoteFeeSwitch = 0.0,
    this.isAgent = 0.0,
    this.workerId = 0.0,
    this.showDkButton = 0.0,
  });
}

