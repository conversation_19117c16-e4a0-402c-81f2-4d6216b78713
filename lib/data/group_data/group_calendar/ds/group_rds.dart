import 'package:gdjg_pure_flutter/data/group_data/group_calendar/ds/model/net/group_calendar_net_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_calendar/ds/model/param/group_calendar_param_model.dart';
import 'package:net_plugin/pigeon/api_generated/net_channel.g.dart';
import 'package:net_plugin/pigeon/net_result/resp_result.dart';
import 'package:net_plugin/pigeon/net_service/net_core.dart';

class GroupRds {

  Future<RespResult<GroupBusinessGetGroupCalendarCountAModel>> getGroupCalendar(
      GroupCalendarParamModel params) async {
    return await NetCore.requestJGPHP(
        BaseBizRequestEntity(
            url: '/api/v3/group-business/get-group-calendar-count',
            method: HTTP_METHOD.GET,
            content: params.toMap()),
        (json) => GroupBusinessGetGroupCalendarCountAModel.fromJson(json));
  }
}
