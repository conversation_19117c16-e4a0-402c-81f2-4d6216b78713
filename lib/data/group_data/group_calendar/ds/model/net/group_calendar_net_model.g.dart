// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'group_calendar_net_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

GroupCalendarNetModel
    _$GroupCalendarNetModelFromJson(
            Map<String, dynamic> json) =>
        GroupCalendarNetModel()
          ..count = json['count'] == null
              ? null
              : CountNetModel.fromJson(json['count'] as Map<String, dynamic>)
          ..calendar = (json['calendar'] as List<dynamic>?)
              ?.map((e) => CalendarNetModel.fromJson(e as Map<String, dynamic>))
              .toList();
// ..project = (json['project'] as List<dynamic>?)
//     ?.map((e) => ProjectModel.fromJson(e as Map<String, dynamic>))
//     .toList()
// ..summary = json['summary'] == null
//     ? null
//     : SummaryModel.fromJson(json['summary'] as Map<String, dynamic>);

Map<String, dynamic> _$GroupCalendarNetModelToJson(
        GroupCalendarNetModel instance) =>
    <String, dynamic>{
      'count': instance.count,
      'calendar': instance.calendar,
      // 'project': instance.project,
      // 'summary': instance.summary,
    };

ProjectModel _$ProjectModelFromJson(Map<String, dynamic> json) => ProjectModel()
  ..id = (json['id'] as num?)?.toDouble()
  ..dept_id = (json['dept_id'] as num?)?.toDouble()
  ..name = json['name']?.toString();

Map<String, dynamic> _$ProjectModelToJson(ProjectModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'dept_id': instance.dept_id,
      'name': instance.name,
    };

SummaryModel _$SummaryModelFromJson(Map<String, dynamic> json) => SummaryModel()
  ..work_time = (json['work_time'] as num?)?.toDouble()
  ..work_hour = (json['work_hour'] as num?)?.toDouble()
  ..overtime_work = (json['overtime_work'] as num?)?.toDouble()
  ..overtime = (json['overtime'] as num?)?.toDouble()
  ..borrow = json['borrow'] as String?
  ..wage = json['wage'] as String?;

Map<String, dynamic> _$SummaryModelToJson(SummaryModel instance) =>
    <String, dynamic>{
      'work_time': instance.work_time,
      'work_hour': instance.work_hour,
      'overtime_work': instance.overtime_work,
      'overtime': instance.overtime,
      'borrow': instance.borrow,
      'wage': instance.wage,
    };
