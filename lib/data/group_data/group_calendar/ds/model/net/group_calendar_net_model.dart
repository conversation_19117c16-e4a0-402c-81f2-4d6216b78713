import 'dart:convert';

import 'package:gdjg_pure_flutter/data/common_data/net/calendar_net_model.dart';
import 'package:gdjg_pure_flutter/data/common_data/net/count_net_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_calendar/repo/model/group_calendar_biz_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'group_calendar_net_model.g.dart';

@JsonSerializable()
class GroupCalendarNetModel {
  /// 统计数据
  CountNetModel? count;

  /// 日历数据
  List<CalendarNetModel>? calendar;

  /// 项目列表
  List<ProjectModel>? project;

  /// [4.9]统计汇总
  SummaryModel? summary;

  GroupCalendarNetModel();

  factory GroupCalendarNetModel.fromJson(
          Map<String, dynamic> json) =>
      _$GroupCalendarNetModelFromJson(json);

  Map<String, dynamic> toJson() =>
      _$GroupCalendarNetModelToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }

  GroupCalendarBizModel transform() {
    return GroupCalendarBizModel(
      count: count?.transform(),
      calendar: calendar?.map((e) => e.transform()).toList() ?? [],
      project: project?.map((e) => e.transform()).toList() ??
              [],
      summary: summary?.transform(),
    );
  }
}

@JsonSerializable()
class ProjectModel {
  ///
  double? id;

  ///
  double? dept_id;

  ///
  String? name;

  ProjectModel();

  factory ProjectModel.fromJson(Map<String, dynamic> json) =>
      _$ProjectModelFromJson(json);

  Map<String, dynamic> toJson() => _$ProjectModelToJson(this);

  ProjectEntity transform() {
    return ProjectEntity(
      id: id ?? 0.0,
      deptId: dept_id ?? 0.0,
      name: name ?? "",
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class SummaryModel {
  /// 上班工时
  double? work_time;

  /// 上班小时
  double? work_hour;

  /// 加班工时
  double? overtime_work;

  /// 加班小时
  double? overtime;

  /// 借支金额
  String? borrow;

  /// 结算金额
  String? wage;

  SummaryModel();

  factory SummaryModel.fromJson(Map<String, dynamic> json) =>
      _$SummaryModelFromJson(json);

  Map<String, dynamic> toJson() => _$SummaryModelToJson(this);

  SummaryEntity transform() {
    return SummaryEntity(
      workTime: work_time ?? 0.0,
      workHour: work_hour ?? 0.0,
      overtimeWork: overtime_work ?? 0.0,
      overtime: overtime ?? 0.0,
      borrow: borrow ?? "",
      wage: wage ?? "",
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}
