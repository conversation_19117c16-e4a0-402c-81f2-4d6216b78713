import 'dart:convert';
import 'dart:ffi';

import 'package:gdjg_pure_flutter/data/group_data/group_calendar/repo/model/group_calendar_biz_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'group_calendar_net_model.g.dart';

@JsonSerializable()
class GroupBusinessGetGroupCalendarCountAModel {
  /// 统计数据
  CountNetModel? count;

  /// 日历数据
  List<CalendarModel>? calendar;

  /// 项目列表
  List<ProjectModel>? project;

  /// [4.9]统计汇总
  SummaryModel? summary;

  GroupBusinessGetGroupCalendarCountAModel();

  factory GroupBusinessGetGroupCalendarCountAModel.fromJson(
          Map<String, dynamic> json) =>
      _$GroupBusinessGetGroupCalendarCountAModelFromJson(json);

  Map<String, dynamic> toJson() =>
      _$GroupBusinessGetGroupCalendarCountAModelToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }

  transform() {
    return GroupCalendarBizModel(
      count: count?.transform(),
      // calendar:
      //     data?.calendar?.map((e) => CalendarEntity().transform(e)).toList() ??
      //         [],
      // project:
      //     data?.project?.map((e) => ProjectEntity().transform(e)).toList() ??
      //         [],
      // summary: SummaryEntity().transform(data?.summary),
    );
  }
}

@JsonSerializable()
class CountNetModel {
  ///
  double? id;

  ///
  String? name;

  ///
  SpotWorkNetModel? spot_work;

  ///
  UnitNetModel? unit;

  ///
  WorkMoneyNetModel? work_money;

  ///
  BorrowNetModel? borrow;

  ///
  ContractorNetModel? contractor;

  ///
  WageNetModel? wage;

  ///
  HourNetModel? hour;

  ///
  double? unsettled;

  ///
  double? income;

  ///
  double? num;

  CountNetModel();

  factory CountNetModel.fromJson(Map<String, dynamic> json) =>
      _$CountModelFromJson(json);

  Map<String, dynamic> toJson() => _$CountModelToJson(this);

  CountBizModel transform() {
    return CountBizModel(
      id: id ?? 0.0,
      name: name ?? "",
      spotWork: spot_work?.transform(),
      unit: unit?.transform(),
      workMoney: work_money?.transform(),
      borrow: borrow?.transform(),
      contractor: contractor?.transform(),
      wage: wage?.transform(),
      hour: hour?.transform(),
      unsettled: unsettled ?? 0.0,
      income: income ?? 0.0,
      num: num ?? 0.0,
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class SpotWorkNetModel {
  /// 上班（工）
  String? work_time;

  /// 上班（小时）
  String? work_time_hour;

  /// 加班（工）
  String? overtime;

  /// 加班（小时）
  String? overtime_work;

  ///
  double? spot_work_fee_money;

  ///
  double? num;

  SpotWorkNetModel();

  factory SpotWorkNetModel.fromJson(Map<String, dynamic> json) =>
      _$SpotWorkModelFromJson(json);

  Map<String, dynamic> toJson() => _$SpotWorkModelToJson(this);

  SpotWorkBizModel transform() {
    return SpotWorkBizModel(
      workTime: work_time ?? "",
      workTimeHour: work_time_hour ?? "",
      overtime: overtime ?? "",
      overtimeWork: overtime_work ?? "",
      spotWorkFeeMoney: spot_work_fee_money ?? 0.0,
      num: num ?? 0.0,
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class UnitNetModel {
  ///
  List<CountUnitModel>? count_unit;

  ///
  double? num;

  UnitNetModel();

  factory UnitNetModel.fromJson(Map<String, dynamic> json) =>
      _$UnitModelFromJson(json);

  Map<String, dynamic> toJson() => _$UnitModelToJson(this);

  UnitBizModel transform() {
    return UnitBizModel(
      countUnit: count_unit?.map((e) => e.transform()).toList() ?? [],
      num: num ?? 0.0,
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class CountUnitModel {
  String? unit;
  String? count;
  double? num;
  String? unitWorkType;
  String? unitWorkTypeName;
  String? unitWorkTypeUnit;
  String? unitMoney;
  String? lastUnitPrice;

  CountUnitModel({
    this.unit,
    this.count,
    this.num,
    this.unitWorkType,
    this.unitWorkTypeName,
    this.unitWorkTypeUnit,
    this.unitMoney,
    this.lastUnitPrice,
  });

  factory CountUnitModel.fromJson(Map<String, dynamic> json) =>
      _$CountUnitModelFromJson(json);

  Map<String, dynamic> toJson() => _$CountUnitModelToJson(this);

  CountUnitEntity transform() {
    return CountUnitEntity(
      unit: unit ?? "",
      count: count ?? "",
      num: num?.toDouble() ?? 0.00,
      unitWorkType: unitWorkType ?? "",
      unitWorkTypeName: unitWorkTypeName ?? "",
      unitWorkTypeUnit: unitWorkTypeUnit ?? "",
      unitMoney: unitMoney ?? "",
      lastUnitPrice: lastUnitPrice ?? "",
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class WorkMoneyNetModel {
  ///
  String? work_money;

  ///
  double? num;

  WorkMoneyNetModel();

  factory WorkMoneyNetModel.fromJson(Map<String, dynamic> json) =>
      _$WorkMoneyModelFromJson(json);

  Map<String, dynamic> toJson() => _$WorkMoneyModelToJson(this);

  WorkMoneyBizModel transform() {
    return WorkMoneyBizModel(
      workMoney: work_money ?? "",
      num: num ?? 0.0,
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class BorrowNetModel {
  ///
  double? borrow_count;

  ///
  double? num;

  BorrowNetModel();

  factory BorrowNetModel.fromJson(Map<String, dynamic> json) =>
      _$BorrowModelFromJson(json);

  Map<String, dynamic> toJson() => _$BorrowModelToJson(this);

  BorrowBizModel transform() {
    return BorrowBizModel(
      borrowCount: borrow_count ?? 0.0,
      num: num ?? 0.0,
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class ContractorNetModel {
  ///
  String? contractor_work_time;

  ///
  String? contractor_work_time_hour;

  ///
  String? contractor_overtime;

  double? contractor_money;

  ///
  double? num;

  ContractorNetModel();

  factory ContractorNetModel.fromJson(Map<String, dynamic> json) =>
      _$ContractorModelFromJson(json);

  Map<String, dynamic> toJson() => _$ContractorModelToJson(this);

  ContractorBizModel transform() {
    return ContractorBizModel(
      contractorWorkTime: contractor_work_time ?? "0.0",
      contractorWorkTimeHour: contractor_work_time_hour ?? "0.0",
      num: num ?? 0.0,
      contractorMoney: contractor_money ?? 0.0,
      contractorOvertime: contractor_overtime ?? "0.0",
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class WageNetModel {
  ///
  double? wage_count;

  ///
  double? num;

  WageNetModel();

  factory WageNetModel.fromJson(Map<String, dynamic> json) =>
      _$WageModelFromJson(json);

  Map<String, dynamic> toJson() => _$WageModelToJson(this);

  WageBizModel transform() {
    return WageBizModel(
      wageCount: wage_count ?? 0.0,
      num: num ?? 0.0,
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class HourNetModel {
  ///
  double? hour_time;

  ///
  double? hour_overtime;

  ///
  double? num;

  HourNetModel();

  factory HourNetModel.fromJson(Map<String, dynamic> json) =>
      _$HourModelFromJson(json);

  Map<String, dynamic> toJson() => _$HourModelToJson(this);

  HourBizModel transform() {
    return HourBizModel(
      hourTime: hour_time ?? 0.0,
      hourOvertime: hour_overtime ?? 0.0,
      num: num ?? 0.0,
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class CalendarModel {
  /// 是否有备注
  double? is_note;

  /// 日期
  String? day;

  /// 工量
  UnitNetModel? unit;

  /// 短工
  WorkMoneyNetModel? work_money;

  /// 小时工
  String? hour;

  /// 包工
  ContractorNetModel? contractor;

  /// 所有记工类型
  List<Int>? business_type;

  /// 点工
  SpotWorkNetModel? spot_work;

  CalendarModel();

  factory CalendarModel.fromJson(Map<String, dynamic> json) =>
      _$CalendarModelFromJson(json);

  Map<String, dynamic> toJson() => _$CalendarModelToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class ProjectModel {
  ///
  double? id;

  ///
  double? dept_id;

  ///
  String? name;

  ProjectModel();

  factory ProjectModel.fromJson(Map<String, dynamic> json) =>
      _$ProjectModelFromJson(json);

  Map<String, dynamic> toJson() => _$ProjectModelToJson(this);

  ProjectEntity transform() {
    return ProjectEntity(
      id: id ?? 0.0,
      deptId: dept_id ?? 0.0,
      name: name ?? "",
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class SummaryModel {
  /// 上班工时
  double? work_time;

  /// 上班小时
  double? work_hour;

  /// 加班工时
  double? overtime_work;

  /// 加班小时
  double? overtime;

  /// 借支金额
  String? borrow;

  /// 结算金额
  String? wage;

  SummaryModel();

  factory SummaryModel.fromJson(Map<String, dynamic> json) =>
      _$SummaryModelFromJson(json);

  Map<String, dynamic> toJson() => _$SummaryModelToJson(this);

  SummaryEntity transform() {
    return SummaryEntity(
      workTime: work_time ?? 0.0,
      workHour: work_hour ?? 0.0,
      overtimeWork: overtime_work ?? 0.0,
      overtime: overtime ?? 0.0,
      borrow: borrow ?? "",
      wage: wage ?? "",
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}
