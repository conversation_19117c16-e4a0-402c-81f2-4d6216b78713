import 'dart:convert';
import 'dart:ffi';

import 'package:gdjg_pure_flutter/utils/system_util/string_util.dart';

class GroupCalendarBizModel {
  /// 统计数据
  final CountBizModel? count;

  // /// 日历数据
  // List<CalendarEntity> calendar;
  //
  // /// 项目列表
  // List<ProjectEntity> project;
  //
  // /// [4.9]统计汇总
  // final SummaryEntity? summary;

  GroupCalendarBizModel({
    this.count,
    // this.calendar = const [],
    // this.project = const [],
    // this.summary,
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class CountBizModel {

  ///
  double id;
  ///
  String name;

  ///
  final SpotWorkBizModel? spotWork;

  ///
  final UnitBizModel? unit;

  ///
  final WorkMoneyBizModel? workMoney;

  ///
  final BorrowBizModel? borrow;

  ///
  final ContractorBizModel? contractor;

  ///
  final WageBizModel? wage;

  ///
  final HourBizModel? hour;

  ///
  double? unsettled;

  ///
  double? income;

  ///
  double num;

  CountBizModel({
    this.id = 0.0,
    this.name = "",
    this.spotWork,
    this.unit,
    this.workMoney,
    this.borrow,
    this.contractor,
    this.wage,
    this.hour,
    this.unsettled = 0.0,
    this.income = 0.0,
    this.num = 0.0,
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class SpotWorkBizModel {
  /// 上班（工）
  String workTime;

  /// 上班（小时）
  String workTimeHour;

  /// 加班（工）
  String overtime;

  /// 加班（小时）
  String overtimeWork;

  ///
  double spotWorkFeeMoney;

  ///
  double num;

  SpotWorkBizModel({
    this.workTime = "",
    this.workTimeHour = "",
    this.overtime = "",
    this.overtimeWork = "",
    this.spotWorkFeeMoney = 0.0,
    this.num = 0.0,
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class UnitBizModel {
  ///
  List<CountUnitEntity> countUnit;

  ///
  double num;

  UnitBizModel({
    this.countUnit = const [],
    this.num = 0.0,
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class CountUnitEntity {
  String unit;
  String count;
  double num;
  String unitWorkType;
  String unitWorkTypeName;
  String unitWorkTypeUnit;
  String unitMoney;
  String lastUnitPrice;

  CountUnitEntity({
    this.unit = "",
    this.count = "",
    this.num = 0.0,
    this.unitWorkType = "",
    this.unitWorkTypeName = "",
    this.unitWorkTypeUnit = "",
    this.unitMoney = "",
    this.lastUnitPrice = "",
  });

  String getTitle() {
    return "工量 ${getTypeNameStr()}";
  }

  String getCountStr() {
    return "总计:$count ${getTypeUnitStr()}";
  }

  String getTypeUnitStr() {
    return unitWorkTypeUnit.isEmpty ? "" : unitWorkTypeUnit;
  }

  String getTypeNameStr() {
    return unitWorkTypeName.isEmpty ? "" : unitWorkTypeName;
  }

  ///最后的工量工价
  String getLastUnitPrice() {
    return lastUnitPrice.isEmpty ? "0.00" : lastUnitPrice.formatStringToMoney();
  }
}

class WorkMoneyBizModel {
  ///
  String workMoney;

  ///
  double num;

  WorkMoneyBizModel({
    this.workMoney = "",
    this.num = 0.0,
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class BorrowBizModel {
  ///
  double borrowCount;

  ///
  double num;

  BorrowBizModel({
    this.borrowCount = 0.0,
    this.num = 0.0,
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class ContractorBizModel {
  /// 包工（工）
  String contractorWorkTime;

  /// 包工（小时）
  String contractorWorkTimeHour;

  ///
  String contractorOvertime;

  ///
  double contractorMoney;

  ///
  double num;

  ContractorBizModel({
    this.contractorWorkTime = "0",
    this.contractorWorkTimeHour = "0",
    this.num = 0.0,
    this.contractorMoney = 0.0,
    this.contractorOvertime = "0",
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class WageBizModel {
  ///
  double wageCount;

  ///
  double num;

  WageBizModel({
    this.wageCount = 0.0,
    this.num = 0.0,
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class HourBizModel {
  ///
  double hourTime;

  ///
  double hourOvertime;

  ///
  double num;

  HourBizModel({
    this.hourTime = 0.0,
    this.hourOvertime = 0.0,
    this.num = 0.0,
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class CalendarEntity {
  /// 是否有备注
  double isNote;

  /// 日期
  String day;

  /// 工量
  final UnitBizModel? unit;

  /// 短工
  final WorkMoneyBizModel? workMoney;

  /// 小时工
  String hour;

  /// 包工
  final ContractorBizModel? contractor;

  /// 所有记工类型
  List<Int> businessType;

  /// 点工
  final SpotWorkBizModel? spotWork;

  CalendarEntity({
    this.isNote = 0.0,
    this.day = "",
    this.unit,
    this.workMoney,
    this.hour = "",
    this.contractor,
    this.businessType = const [],
    this.spotWork,
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class ProjectEntity {
  ///
  double id;

  ///
  double deptId;

  ///
  String name;

  ProjectEntity({
    this.id = 0.0,
    this.deptId = 0.0,
    this.name = "",
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class SummaryEntity {
  /// 上班工时
  double workTime;

  /// 上班小时
  double workHour;

  /// 加班工时
  double overtimeWork;

  /// 加班小时
  double overtime;

  /// 借支金额
  String borrow;

  /// 结算金额
  String wage;

  SummaryEntity({
    this.workTime = 0.0,
    this.workHour = 0.0,
    this.overtimeWork = 0.0,
    this.overtime = 0.0,
    this.borrow = "",
    this.wage = "",
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}
