import 'dart:convert';

import 'package:gdjg_pure_flutter/data/common_data/biz/calendar_biz_model.dart';
import 'package:gdjg_pure_flutter/data/common_data/biz/count_biz_model.dart';

class GroupCalendarBizModel {
  /// 统计数据
  final CountBizModel? count;

  /// 日历数据
  List<CalendarEntity> calendar;


  /// 项目列表
  List<ProjectEntity> project;

  /// [4.9]统计汇总
  final SummaryEntity? summary;

  GroupCalendarBizModel({
    this.count,
    this.calendar = const [],
    this.project = const [],
    this.summary,
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class ProjectEntity {
  ///
  double id;

  ///
  double deptId;

  ///
  String name;

  ProjectEntity({
    this.id = 0.0,
    this.deptId = 0.0,
    this.name = "",
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class SummaryEntity {
  /// 上班工时
  double workTime;

  /// 上班小时
  double workHour;

  /// 加班工时
  double overtimeWork;

  /// 加班小时
  double overtime;

  /// 借支金额
  String borrow;

  /// 结算金额
  String wage;

  SummaryEntity({
    this.workTime = 0.0,
    this.workHour = 0.0,
    this.overtimeWork = 0.0,
    this.overtime = 0.0,
    this.borrow = "",
    this.wage = "",
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}
