
import 'package:gdjg_pure_flutter/data/group_data/group_calendar/ds/group_rds.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_calendar/ds/model/net/group_calendar_net_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_calendar/ds/model/param/group_calendar_param_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_calendar/repo/model/group_calendar_biz_model.dart';
import 'package:net_plugin/pigeon/net_result/resp_result.dart';

class GroupRepo {
  final _groupRds = GroupRds();

  Future<RespResult<GroupCalendarBizModel>> getGroupCalendar(
      GroupCalendarParamModel param) async {
    final result = await _groupRds.getGroupCalendar(param);
    return result.map(_transform);
  }

  GroupCalendarBizModel _transform(GroupBusinessGetGroupCalendarCountAModel? netModel) {
    return netModel?.transform();
  }
}
