/// 工友休假参数模型
class WorkerRestParamModel {
  /// 项目id
  String? work_note;

  /// 工人ID 多个工人用,隔开
  String? worker_ids;

  /// 是否休假 0否 1是
  int? rest;

  WorkerRestParamModel({
    this.work_note,
    this.worker_ids,
    this.rest,
  });

  Map<String, Object> toMap() {
    var map = <String, Object>{};
    if (work_note != null) map["work_note"] = work_note!;
    if (worker_ids != null) map["worker_ids"] = worker_ids!;
    if (rest != null) map["rest"] = rest!;
    return map;
  }
}
