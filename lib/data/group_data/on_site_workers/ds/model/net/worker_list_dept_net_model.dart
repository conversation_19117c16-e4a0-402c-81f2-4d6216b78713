import '../../../repo/model/worker_list_dept_biz_model.dart';

class WorkerListDeptNetModel {
  List<NoteWorkerNetModel>? note_worker;
  String? share_token;
  double? del_num;

  WorkerListDeptNetModel();

  Map<String, dynamic> toJson() {
    var map = <String, Object>{};
    if (note_worker != null) map["note_worker"] = note_worker!;
    if (share_token != null) map["share_token"] = share_token!;
    if (del_num != null) map["del_num"] = del_num!;
    return map;
  }

  factory WorkerListDeptNetModel.fromJson(Map<String, dynamic> json) {
    var netModel = WorkerListDeptNetModel();
    netModel.note_worker = (json["note_worker"] as List<dynamic>?)
        ?.map((e) => NoteWorkerNetModel.fromJson(e as Map<String, dynamic>))
        .toList();
    netModel.share_token = json["share_token"]?.toString();
    netModel.del_num = double.tryParse(json["del_num"].toString());
    return netModel;
  }

  WorkerListDeptBizModel transform() {
    return WorkerListDeptBizModel(
      noteWorker: note_worker?.map((e) => e.transform()).toList() ?? [],
      shareToken: share_token ?? "",
      delNum: del_num ?? 0.0,
    );
  }
}

class NoteWorkerNetModel {
  double? worker_id;
  String? name;
  String? tel;
  String? name_py;
  String? name_color;
  String? avatar;
  double? quit_time;
  double? is_bind;
  double? member_id;
  double? user_id;
  double? is_rest;
  double? is_self;
  double? is_self_created;
  double? is_agent;
  double? contract_employee_status;
  double? is_grant;
  dynamic occ;

  NoteWorkerNetModel();

  Map<String, dynamic> toJson() {
    var map = <String, Object>{};
    if (worker_id != null) map["worker_id"] = worker_id!;
    if (name != null) map["name"] = name!;
    if (tel != null) map["tel"] = tel!;
    if (name_py != null) map["name_py"] = name_py!;
    if (name_color != null) map["name_color"] = name_color!;
    if (avatar != null) map["avatar"] = avatar!;
    if (quit_time != null) map["quit_time"] = quit_time!;
    if (is_bind != null) map["is_bind"] = is_bind!;
    if (member_id != null) map["member_id"] = member_id!;
    if (user_id != null) map["user_id"] = user_id!;
    if (is_rest != null) map["is_rest"] = is_rest!;
    if (is_self != null) map["is_self"] = is_self!;
    if (is_self_created != null) map["is_self_created"] = is_self_created!;
    if (is_agent != null) map["is_agent"] = is_agent!;
    if (contract_employee_status != null) map["contract_employee_status"] = contract_employee_status!;
    if (is_grant != null) map["is_grant"] = is_grant!;
    if (occ != null) map["occ"] = occ!;
    return map;
  }

  factory NoteWorkerNetModel.fromJson(Map<String, dynamic> json) {
    var netModel = NoteWorkerNetModel();
    netModel.worker_id = double.tryParse(json["worker_id"].toString());
    netModel.name = json["name"]?.toString();
    netModel.tel = json["tel"]?.toString();
    netModel.name_py = json["name_py"]?.toString();
    netModel.name_color = json["name_color"]?.toString();
    netModel.avatar = json["avatar"]?.toString();
    netModel.quit_time = double.tryParse(json["quit_time"].toString());
    netModel.is_bind = double.tryParse(json["is_bind"].toString());
    netModel.member_id = double.tryParse(json["member_id"].toString());
    netModel.user_id = double.tryParse(json["user_id"].toString());
    netModel.is_rest = double.tryParse(json["is_rest"].toString());
    netModel.is_self = double.tryParse(json["is_self"].toString());
    netModel.is_self_created = double.tryParse(json["is_self_created"].toString());
    netModel.is_agent = double.tryParse(json["is_agent"].toString());
    netModel.contract_employee_status = double.tryParse(json["contract_employee_status"].toString());
    netModel.is_grant = double.tryParse(json["is_grant"].toString());
    netModel.occ = json["occ"];
    return netModel;
  }

  NoteWorkerBizModel transform() {
    return NoteWorkerBizModel(
      workerId: worker_id ?? 0.0,
      name: name ?? "",
      tel: tel ?? "",
      namePy: name_py ?? "",
      nameColor: name_color ?? "",
      avatar: avatar ?? "",
      quitTime: quit_time ?? 0.0,
      isBind: is_bind ?? 0.0,
      memberId: member_id ?? 0.0,
      userId: user_id ?? 0.0,
      isRest: is_rest ?? 0.0,
      isSelf: is_self ?? 0.0,
      isSelfCreated: is_self_created ?? 0.0,
      isAgent: is_agent ?? 0.0,
      contractEmployeeStatus: contract_employee_status ?? 0.0,
      isGrant: is_grant ?? 0.0,
      occ: occ,
    );
  }
}
