import 'package:gdjg_pure_flutter/data/group_data/group_statistics/ds/model/group_statistics_list_rds.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_statistics/ds/model/net/cors_note_group_record_work_statistics_v2_biz_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_statistics/ds/model/net/cors_note_group_record_work_statistics_v2_net_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_statistics/ds/model/param/cors_note_group_record_work_statistics_v2_param_model.dart';
import 'package:net_plugin/pigeon/net_result/resp_result.dart';

/// 统计列表请求仓库
class GroupStatisticsListRepo {
  GroupStatisticsListRds rds = GroupStatisticsListRds();

  /// 查询统计列表数据
  Future<RespResult<CorsNoteGroupRecordWorkStatisticsV2BizModel>>
      queryGroupStatisticsList(
      CorsNoteGroupRecordWorkStatisticsV2ParamModel req,
  ) async {
    print('LINer网络请求结果=======${await rds.queryGroupStatisticsList(req)}');
    final res = await rds.queryGroupStatisticsList(req);
    return res.map(_transform);
  }
  CorsNoteGroupRecordWorkStatisticsV2BizModel _transform(CorsNoteGroupRecordWorkStatisticsV2NetModel? netModel) {
    if(netModel == null){
      return CorsNoteGroupRecordWorkStatisticsV2BizModel();
    }
    return netModel.transform();
  }
}
