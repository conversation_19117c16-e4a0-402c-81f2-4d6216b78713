// GENERATED CODE - DO NOT MODIFY BY HAND

part of '../net/cors_note_worker_in_group_note_net_model.dart';
// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CorsNoteWorkerInGroupNoteNetModel _$CorsNoteWorkerInGroupNoteNetModelFromJson(
        Map<String, dynamic> json) =>
    CorsNoteWorkerInGroupNoteNetModel()
      ..list = (json['list'] as List<dynamic>?)
          ?.map((e) => CorsNoteWorkerInGroupNoteANetModel.fromJson(
              e as Map<String, dynamic>))
          .toList();

Map<String, dynamic> _$CorsNoteWorkerInGroupNoteNetModelToJson(
        CorsNoteWorkerInGroupNoteNetModel instance) =>
    <String, dynamic>{
      'list': instance.list,
    };

CorsNoteWorkerInGroupNoteANetModel _$CorsNoteWorkerInGroupNoteANetModelFromJson(
        Map<String, dynamic> json) =>
    CorsNoteWorkerInGroupNoteANetModel()
      ..id = (json['id'] as num?)?.toDouble()
      ..name = json['name'] as String?
      ..identity = (json['identity'] as num?)?.toDouble()
      ..dept_id = (json['dept_id'] as num?)?.toDouble()
      ..corp_id = (json['corp_id'] as num?)?.toDouble()
      ..spot_work_no_fee_num =
          (json['spot_work_no_fee_num'] as num?)?.toDouble()
      ..spot_work_no_fee_money = json['spot_work_no_fee_money'] as String?;

Map<String, dynamic> _$CorsNoteWorkerInGroupNoteANetModelToJson(
        CorsNoteWorkerInGroupNoteANetModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'identity': instance.identity,
      'dept_id': instance.dept_id,
      'corp_id': instance.corp_id,
      'spot_work_no_fee_num': instance.spot_work_no_fee_num,
      'spot_work_no_fee_money': instance.spot_work_no_fee_money,
    };
