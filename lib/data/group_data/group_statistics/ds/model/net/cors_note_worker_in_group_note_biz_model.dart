import 'dart:convert';

class CorsNoteWorkerInGroupNoteBizModel {
  /// 
  List<CorsNoteWorkerInGroupNoteABizModel> list;

  CorsNoteWorkerInGroupNoteBizModel({
    this.list = const [],
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class CorsNoteWorkerInGroupNoteABizModel {
  /// 
  double id;
  /// 记功本名称
  String name;
  /// 1个人 2班组
  double identity;
  /// 部门id
  double deptId;
  /// 企业id
  double corpId;
  /// 未算工资的笔数
  double spotWorkNoFeeNum;
  /// 未结清的钱
  String spotWorkNoFeeMoney;

  CorsNoteWorkerInGroupNoteABizModel({
    this.id = 0.0,
    this.name = "",
    this.identity = 0.0,
    this.deptId = 0.0,
    this.corpId = 0.0,
    this.spotWorkNoFeeNum = 0.0,
    this.spotWorkNoFeeMoney = "",
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}

