import 'dart:convert';
import 'package:json_annotation/json_annotation.dart';
import 'cors_note_worker_in_group_note_biz_model.dart';

part '../../model/net/cors_note_worker_in_group_note_net_model.g.dart';
@JsonSerializable()
class CorsNoteWorkerInGroupNoteNetModel {
  /// 
  List<CorsNoteWorkerInGroupNoteANetModel>? list;

  CorsNoteWorkerInGroupNoteNetModel();

  factory CorsNoteWorkerInGroupNoteNetModel.fromJson(Map<String, dynamic> json) => _$CorsNoteWorkerInGroupNoteNetModelFromJson(json);

  Map<String, dynamic> toJson() => _$CorsNoteWorkerInGroupNoteNetModelToJson(this);

  CorsNoteWorkerInGroupNoteBizModel transform() {
    return CorsNoteWorkerInGroupNoteBizModel(
      list: list?.map((e) => e.transform()).toList() ?? [],
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class CorsNoteWorkerInGroupNoteANetModel {
  /// 
  double? id;
  /// 记功本名称
  String? name;
  /// 1个人 2班组
  double? identity;
  /// 部门id
  double? dept_id;
  /// 企业id
  double? corp_id;
  /// 未算工资的笔数
  double? spot_work_no_fee_num;
  /// 未结清的钱
  String? spot_work_no_fee_money;

  CorsNoteWorkerInGroupNoteANetModel();

  factory CorsNoteWorkerInGroupNoteANetModel.fromJson(Map<String, dynamic> json) => _$CorsNoteWorkerInGroupNoteANetModelFromJson(json);

  Map<String, dynamic> toJson() => _$CorsNoteWorkerInGroupNoteANetModelToJson(this);

  CorsNoteWorkerInGroupNoteABizModel transform() {
    return CorsNoteWorkerInGroupNoteABizModel(
      id: id ?? 0.0,
      name: name ?? "",
      identity: identity ?? 0.0,
      deptId: dept_id ?? 0.0,
      corpId: corp_id ?? 0.0,
      spotWorkNoFeeNum: spot_work_no_fee_num ?? 0.0,
      spotWorkNoFeeMoney: spot_work_no_fee_money ?? "",
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

