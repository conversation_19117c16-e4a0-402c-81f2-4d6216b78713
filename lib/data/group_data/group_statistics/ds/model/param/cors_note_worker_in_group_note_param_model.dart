class CorsNoteWorkerInGroupNoteParamModel {

  /// 	工人id
   String worker_id;

  /// 	1 返回工人点工未结 2全部未接
   String no_fee;

  /// 	1,2,3 说明：1点工，2工量，3短工，4借支, 5支出, 6包工, 7小时工  使用此参数no_fee为必填
   String business_type;

   CorsNoteWorkerInGroupNoteParamModel({
    this.worker_id = "",
    this.no_fee = "",
    this.business_type = "",
  });

  Map<String, String> toMap() {
    return {
      "worker_id": worker_id,
      "no_fee": no_fee,
      "business_type": business_type,
    };
  }
}

