import 'package:gdjg_pure_flutter/data/group_data/group_statistics/ds/model/net/cors_note_group_record_work_statistics_v2_net_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_statistics/ds/model/net/cors_note_worker_in_group_note_net_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_statistics/ds/model/param/cors_note_worker_in_group_note_param_model.dart';
import 'package:net_plugin/pigeon/api_generated/net_channel.g.dart';
import 'package:net_plugin/pigeon/net_result/resp_result.dart';
import 'package:net_plugin/pigeon/net_service/net_core.dart';
import 'param/cors_note_group_record_work_statistics_v2_param_model.dart';

/// 统计列表网络请求
class GroupStatisticsListRds {
  /// 统计列表数据
  Future<RespResult<CorsNoteGroupRecordWorkStatisticsV2NetModel>>
      queryGroupStatisticsList(
          CorsNoteGroupRecordWorkStatisticsV2ParamModel params) async {
    final queryBody = {
      'start_business_time': params.start_business_time,
      'end_business_time': params.end_business_time,
      'business_type': params.business_type,
      'worker_id': params.worker_id
    };
    return await NetCore.requestJGPHP(
        BaseBizRequestEntity(
            requestExtra: RequestExtra(printResp: true),
            url: '/api/cors_note/group-record-work-statistics-v2',
            method: HTTP_METHOD.GET,
            content: queryBody),
        (json) => CorsNoteGroupRecordWorkStatisticsV2NetModel.fromJson(json));
  }

  /// 工人所在的班组本（用于统计选择项目）
  Future<RespResult<CorsNoteWorkerInGroupNoteNetModel>> queryWorkerInGroupNote(
      CorsNoteWorkerInGroupNoteParamModel params) async {
    final queryBody = {
      'worker_id': params.worker_id,
      'no_fee': params.no_fee,
      'business_type': params.business_type
    };
    return await NetCore.requestJGPHP(
        BaseBizRequestEntity(
          requestExtra: RequestExtra(printResp: true),
          url: '/api/cors_note/worker-in-group-note',
          method: HTTP_METHOD.GET,
          content: queryBody,
        ),
        (json) => CorsNoteWorkerInGroupNoteNetModel.fromJson(json));
  }
}
