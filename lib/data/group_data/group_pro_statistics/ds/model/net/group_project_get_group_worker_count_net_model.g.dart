// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'group_project_get_group_worker_count_net_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

GroupProjectGetGroupWorkerCountNetModel
    _$GroupProjectGetGroupWorkerCountNetModelFromJson(
            Map<String, dynamic> json) =>
        GroupProjectGetGroupWorkerCountNetModel()
          ..all = json['all'] == null
              ? null
              : CountNetModel.fromJson(json['all'] as Map<String, dynamic>)
          ..worker = (json['worker'] as List<dynamic>?)
              ?.map((e) => CountNetModel.fromJson(e as Map<String, dynamic>))
              .toList();

Map<String, dynamic> _$GroupProjectGetGroupWorkerCountNetModelToJson(
        GroupProjectGetGroupWorkerCountNetModel instance) =>
    <String, dynamic>{
      'all': instance.all,
      'worker': instance.worker,
    };
