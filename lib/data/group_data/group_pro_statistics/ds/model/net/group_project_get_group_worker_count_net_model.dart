import 'dart:convert';

import 'package:gdjg_pure_flutter/data/common_data/net/count_net_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_pro_statistics/repo/model/group_project_get_group_worker_count_biz_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'group_project_get_group_worker_count_net_model.g.dart';

@JsonSerializable()
class GroupProjectGetGroupWorkerCountNetModel {
  /// 全部
  CountNetModel? all;

  /// 工人统计
  List<CountNetModel>? worker;

  GroupProjectGetGroupWorkerCountNetModel();

  factory GroupProjectGetGroupWorkerCountNetModel.fromJson(
          Map<String, dynamic> json) =>
      _$GroupProjectGetGroupWorkerCountNetModelFromJson(json);

  Map<String, dynamic> toJson() =>
      _$GroupProjectGetGroupWorkerCountNetModelToJson(this);

  GroupProjectGetGroupWorkerCountBizModel transform() {
    return GroupProjectGetGroupWorkerCountBizModel(
      all: all?.transform(),
      worker: worker?.map((e) => e.transform()).toList() ?? [],
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}
