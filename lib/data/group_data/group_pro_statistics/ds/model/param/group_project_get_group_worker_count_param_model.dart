class GroupProjectGetGroupWorkerCountParamModel {
  ///
  String work_note;

  ///
  String start_time;

  ///
  String end_time;

  ///
  String? business_type;

  ///
  String? worker_ids;

  GroupProjectGetGroupWorkerCountParamModel({
    this.work_note = "",
    this.start_time = "",
    this.end_time = "",
    this.business_type = "",
    this.worker_ids = "",
  });

  Map<String, Object> toMap() {
    return {
      "work_note": work_note,
      "start_time": start_time,
      "end_time": end_time,
      "business_type": business_type ?? "", // 确保可空字段有默认值
      "worker_ids": worker_ids ?? "",
    };
  }
}
