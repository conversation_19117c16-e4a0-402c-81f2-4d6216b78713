import 'package:gdjg_pure_flutter/data/group_data/group_pro_statistics/ds/model/net/group_project_get_group_worker_count_net_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_pro_statistics/ds/model/param/group_project_get_group_worker_count_param_model.dart';
import 'package:net_plugin/pigeon/api_generated/net_channel.g.dart';
import 'package:net_plugin/pigeon/net_result/resp_result.dart';
import 'package:net_plugin/pigeon/net_service/net_core.dart';

class GroupProRds {
  Future<RespResult<GroupProjectGetGroupWorkerCountNetModel>>
      getGroupWorkerCount(
          GroupProjectGetGroupWorkerCountParamModel params) async {
    return await NetCore.requestJGPHP(
        BaseBizRequestEntity(
            url: '/api/v3/group-project/get-group-worker-count',
            method: HTTP_METHOD.GET,
            content: params.toMap()),
        (json) => GroupProjectGetGroupWorkerCountNetModel.fromJson(json));
  }
}
