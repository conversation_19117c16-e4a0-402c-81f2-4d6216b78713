import 'dart:convert';

import 'package:gdjg_pure_flutter/data/group_data/group_calendar/repo/model/group_calendar_biz_model.dart';

class GroupProjectGetGroupWorkerCountBizModel {
  /// 
  final CountBizModel? all;
  /// 
  List<CountBizModel>? worker;

  GroupProjectGetGroupWorkerCountBizModel({
    this.all,
    this.worker = const [],
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}


