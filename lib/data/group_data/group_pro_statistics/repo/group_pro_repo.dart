import 'package:gdjg_pure_flutter/data/group_data/group_pro_statistics/ds/group_pro_rds.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_pro_statistics/ds/model/net/group_project_get_group_worker_count_net_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_pro_statistics/ds/model/param/group_project_get_group_worker_count_param_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_pro_statistics/repo/model/group_project_get_group_worker_count_biz_model.dart';
import 'package:net_plugin/pigeon/net_result/resp_result.dart';

class GroupProRepo {
  final _groupRds = GroupProRds();

  Future<RespResult<GroupProjectGetGroupWorkerCountBizModel?>> getGroupWorkerCount(GroupProjectGetGroupWorkerCountParamModel param) async {
    final result = await _groupRds.getGroupWorkerCount(param);
    return result.map(_transform);
  }

  GroupProjectGetGroupWorkerCountBizModel? _transform(GroupProjectGetGroupWorkerCountNetModel? netModel) {
    return netModel?.transform();
  }
}
