import 'package:gdjg_pure_flutter/data/group_data/group_pro_update/ds/group_pro_update_rds.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_pro_update/ds/model/param/group_pro_update_param_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_pro_update/repo/group_pro_transform.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_pro_update/repo/model/update_pro_info_biz_models.dart';
import 'package:gdjg_pure_flutter/utils/system_util/string_util.dart';
import 'package:net_plugin/pigeon/net_result/resp_fail.dart';
import 'package:net_plugin/pigeon/net_result/resp_result.dart';

class GroupProUpdateRepo {
  final _groupProUpdateRds = GroupProUpdateRds();

  Future<RespResult<UpdateProInfoBizModel>> getUpdateProInfo(
      double? billId, String? workNoteId) async {
    if (billId == null || workNoteId == null) {
      return RespFail.buildProcessFail("参数不能为空");
    }
    final result = await _groupProUpdateRds.getOneGroupPro(
        billId.trimTrailingZeros(), workNoteId);
    return result.map(transform);
  }

  Future<RespResult<dynamic>> updateGroupPro(double? billId, String? workNoteId,
      GroupProUpdateParamModel? params) async {
    if (billId == null || workNoteId == null || params == null) {
      return RespFail.buildProcessFail("参数不能为空");
    }
    final result = await _groupProUpdateRds.updateGroupPro(
        billId.trimTrailingZeros(), workNoteId, params);
    return result;
  }

  ///删除记工
  Future<RespResult<dynamic>> deleteGroupPro(
      double? billId, String? workNoteId) async {
    if (billId == null || workNoteId == null) {
      return RespFail.buildProcessFail("参数不能为空");
    }
    final result = await _groupProUpdateRds.deleteGroupPro(
        billId.trimTrailingZeros(), workNoteId);
    return result;
  }
}
