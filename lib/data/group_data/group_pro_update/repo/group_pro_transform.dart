import 'package:gdjg_pure_flutter/data/fee_standard_data/repo/fee_standard_transform.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_pro_update/ds/model/net/update_pro_info_net_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_pro_update/repo/group_pro_update.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_pro_update/repo/model/update_pro_info_biz_models.dart';

extension UpdateProInfoTransform on GroupProUpdateRepo {
  UpdateProInfoBizModel transform(UpdateProInfoNetModel? netModel) {
    return UpdateProInfoBizModel(
      unitWorkType: double.parse(netModel?.unit_work_type ?? "0.0"),
      workTime: double.parse(netModel?.work_time ?? "0.0"),
      workTimeHour: double.parse(netModel?.work_time_hour ?? "0.0"),
      overtime: double.parse(netModel?.overtime ?? "0.0"),
      overtimeWork: double.parse(netModel?.overtime_work ?? "0.0"),
      morningWorkTime: netModel?.morning_work_time ?? "",
      morningWorkTimeHour: netModel?.morning_work_time_hour ?? "",
      afternoonWorkTime: netModel?.afternoon_work_time ?? "",
      afternoonWorkTimeHour: netModel?.afternoon_work_time_hour ?? "",
      isRest: double.parse(netModel?.is_rest ?? "0.0"),
      unit: double.parse(netModel?.unit ?? "0.0"),
      unitNum: netModel?.unit_num ?? "",
      unitPrice: netModel?.unit_price ?? "",
      money: netModel?.money ?? "",
      note: netModel?.note ?? "",
      feeStandardId: netModel?.fee_standard_id ?? "",
      feeMoney: double.parse(netModel?.fee_money ?? "0.0"),
      isDeleted: double.parse(netModel?.is_deleted ?? "0.0"),
      imgUrl: netModel?.img_url ?? "",
      feeInfo: netModel?.fee_info != null
          ? FeeStandardTransform.feeStandardTransform(netModel?.fee_info)
          : null,
      feeSwitch: double.parse(netModel?.fee_switch ?? "0.0"),
      unitName: netModel?.unit_name ?? "",
      expendTypeName: netModel?.expend_type_name ?? "",
      unitWorkTypeName: netModel?.unit_work_type_name ?? "",
      unitWorkTypeUnit: netModel?.unit_work_type_unit ?? "",
      imgInfo: netModel?.img_info ?? [],
      confirm: double.parse(netModel?.confirm ?? "0.0"),
      otherExpenses: netModel?.other_expenses ?? "",
    );
  }
}
