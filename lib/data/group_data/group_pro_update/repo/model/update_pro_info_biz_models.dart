import 'package:gdjg_pure_flutter/data/fee_standard_data/repo/model/fee_standard_biz_model.dart';
import 'package:gdjg_pure_flutter/utils/system_util/string_util.dart';

class UpdateProInfoBizModel {
  double unitWorkType;
  double workTime;
  double workTimeHour;
  double overtime;
  double overtimeWork;
  String morningWorkTime;
  String morningWorkTimeHour;
  String afternoonWorkTime;
  String afternoonWorkTimeHour;
  double userChooseSpotwork;
  double isRest;
  double unit;
  String unitNum;
  String unitPrice;
  String money;
  String note;
  String feeStandardId;
  double feeMoney;
  double isDeleted;
  String imgUrl;
  FeeStandardBizModel? feeInfo;
  double feeSwitch;
  String unitName;
  String expendTypeName;
  String unitWorkTypeName;
  String unitWorkTypeUnit;
  List<String> imgInfo;
  double confirm;
  String workerName;
  dynamic otherExpenses;

  UpdateProInfoBizModel({
    this.unitWorkType = 0.0,
    this.workTime = 0.0,
    this.workTimeHour = 0.0,
    this.overtime = 0.0,
    this.overtimeWork = 0.0,
    this.morningWorkTime = "",
    this.morningWorkTimeHour = "",
    this.afternoonWorkTime = "",
    this.afternoonWorkTimeHour = "",
    this.userChooseSpotwork = 0.0,
    this.isRest = 0.0,
    this.unit = 0.0,
    this.unitNum = "",
    this.unitPrice = "",
    this.money = "",
    this.note = "",
    this.feeStandardId = "",
    this.feeMoney = 0.0,
    this.isDeleted = 0.0,
    this.imgUrl = "",
    this.feeInfo,
    this.feeSwitch = 0.0,
    this.unitName = "",
    this.expendTypeName = "",
    this.unitWorkTypeName = "",
    this.unitWorkTypeUnit = "",
    this.imgInfo = const [],
    this.confirm = 0.0,
    this.otherExpenses = "",
    this.workerName = "",
  });


  ///是否有上班
  hasWorkTime(){
    return workTime > 0;
  }

  ///是否有上班小时
  hasWorkHour(){
    return workTimeHour > 0;
  }

  ///是否是半个工
  isHardTime() {
    return workTime == 0.5;
  }

  ///是否有工资规则
  hasFeeStandard() {
    return feeInfo?.hasFeeStandard() == true;
  }

  ///是否存在上下午
  hasMorningAndAfternoon() {
    return morningWorkTime.isNotEmpty ||
        morningWorkTimeHour.isNotEmpty ||
        afternoonWorkTime.isNotEmpty ||
        afternoonWorkTimeHour.isNotEmpty;
  }

  ///是否休息
  hasRest(){
    return isRest == 2 || (workTime<=0 && workTimeHour<=0 && !hasMorningAndAfternoon()) ;
  }

  ///是否有加班
  hasOverTime() {
    return overtime != 0 || overtimeWork != 0;
  }

  String getWorkerName() {
    return workerName.length > 4 ? "${workerName.substring(0,4)}..." : workerName;
  }
}
