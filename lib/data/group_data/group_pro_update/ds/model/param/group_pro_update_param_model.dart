class GroupProUpdateParamModel {
  final double identity;
  final int business_type;
  final String? work_note;
  String? worker_id;
  String? business_time;
  final double? work_time;
  final double? work_time_hour;
  final double? overtime;
  final double? overtime_work;
  final String? morning_work_time;
  final String? morning_work_time_hour;
  final String? afternoon_work_time;
  final String? afternoon_work_time_hour;
  final String? note;
  final String? img_url;
  final List<String>? resource_ext;
  final String? money;
  ///工量
  final String? unit_num;
  ///分项 id
  final double? unit_work_type;
  ///分项名称
  final String? unit_work_type_name;
  ///工量单价
  final String? unit_price;
  ///工量单价
  final String? unit_work_type_unit;
  ///记工方式:camera 快速记工
  final String? action;
  ///其他费用id
  final String? other_expenses_id;
  ///上班工时类型(工天和休息: 1, 小时: 2, 上下午: 3),该字段主要作为产品面板的统计使用
  final int? user_choose_spotwork;

  ///工资规则id
  final String? fee_standard_id;

  GroupProUpdateParamModel({
    this.business_type = 0,
    this.identity = 1,
    this.work_note,
    this.note,
    this.img_url,
    this.resource_ext,
    this.business_time,
    this.morning_work_time,
    this.morning_work_time_hour,
    this.afternoon_work_time,
    this.afternoon_work_time_hour,
    this.work_time = 0,
    this.work_time_hour = 0,
    this.overtime,
    this.overtime_work,
    this.money = "0.00",
    this.unit_num,
    this.unit_work_type,
    this.unit_work_type_name,
    this.worker_id,
    this.unit_price,
    this.unit_work_type_unit,
    this.action,
    this.other_expenses_id,
    this.user_choose_spotwork,
    this.fee_standard_id,
  });

  Map<String, Object> toMap() {
    return {
      'identity': identity,
      'business_type': business_type,
      'worker_id': worker_id ?? "",
      'note': note ?? "",
      'img_url': img_url ?? "",
      'business_time': business_time ?? "",
      'morning_work_time': morning_work_time ?? "",
      'morning_work_time_hour': morning_work_time_hour ?? "",
      'afternoon_work_time': afternoon_work_time ?? "",
      'afternoon_work_time_hour': afternoon_work_time_hour ?? "",
      'work_time': work_time ?? 0,
      'work_time_hour': work_time_hour ?? 0,
      'overtime': overtime ?? 0,
      'overtime_work': overtime_work ?? 0,
      'money': money ?? "0.00",
      'unit_num': unit_num ?? "",
      'unit_work_type': unit_work_type ?? "",
      'unit_work_type_name': unit_work_type_name ?? "",
      'unit_work_type_unit': unit_work_type_unit ?? "",
      'action': action ?? "",
      'other_expenses_id': other_expenses_id ?? "",
      'unit_price': unit_price ?? "",
      'user_choose_spotwork': user_choose_spotwork ?? 1,
      'resource_ext': resource_ext ?? [],
      'fee_standard_id': fee_standard_id ?? "",
    };
  }
}
