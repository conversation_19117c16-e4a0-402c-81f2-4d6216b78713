import 'dart:convert';
import 'package:gdjg_pure_flutter/data/fee_standard_data/ds/model/net/fee_standard_net_model.dart';

class UpdateProInfoNetModel {
  String? id;
  String? work_note;
  String? group_leader;
  String? worker_id;
  String? business_type;
  String? expend_type;
  String? bookkeeping_source;
  String? bookkeeping_type;
  String? source_fix_id;
  String? unit_work_type;
  String? work_time;
  String? work_time_hour;
  String? overtime;
  String? overtime_work;
  String? morning_work_time;
  String? morning_work_time_hour;
  String? afternoon_work_time;
  String? afternoon_work_time_hour;
  String? user_choose_spotwork;
  String? is_rest;
  String? unit;
  String? unit_num;
  String? unit_price;
  String? money;
  String? real_money;
  String? business_time;
  String? note;
  String? is_note;
  String? fee_standard_id;
  String? fee_money;
  String? is_deleted;
  String? updated_time;
  String? identity;
  String? img_url;
  String? expense_account;
  String? member_source;
  String? quick_business;
  String? app_version;
  String? device_id;
  String? has_fee;
  FeeStandardNetModel? fee_info;
  String? worker_name;
  String? group_leader_name;
  String? dept_name;
  String? fee_switch;
  String? work_note_name;
  String? unit_name;
  String? expend_type_name;
  String? unit_work_type_name;
  String? unit_work_type_unit;
  List<String>? img_info;
  String? confirm;
  String? is_share_single;
  String? busienss_time_string;
  String? created_time_string;
  dynamic? other_expenses;

  UpdateProInfoNetModel();

  Map<String, dynamic> toJson(UpdateProInfoNetModel instance) => <String, dynamic>{
    "id": instance.id,
    "work_note": instance.work_note,
    "group_leader": instance.group_leader,
    "worker_id": instance.worker_id,
    "business_type": instance.business_type,
    "expend_type": instance.expend_type,
    "bookkeeping_source": instance.bookkeeping_source,
    "bookkeeping_type": instance.bookkeeping_type,
    "source_fix_id": instance.source_fix_id,
    "unit_work_type": instance.unit_work_type,
    "work_time": instance.work_time,
    "work_time_hour": instance.work_time_hour,
    "overtime": instance.overtime,
    "overtime_work": instance.overtime_work,
    "morning_work_time": instance.morning_work_time,
    "morning_work_time_hour": instance.morning_work_time_hour,
    "afternoon_work_time": instance.afternoon_work_time,
    "afternoon_work_time_hour": instance.afternoon_work_time_hour,
    "user_choose_spotwork": instance.user_choose_spotwork,
    "is_rest": instance.is_rest,
    "unit": instance.unit,
    "unit_num": instance.unit_num,
    "unit_price": instance.unit_price,
    "money": instance.money,
    "real_money": instance.real_money,
    "business_time": instance.business_time,
    "note": instance.note,
    "is_note": instance.is_note,
    "fee_standard_id": instance.fee_standard_id,
    "fee_money": instance.fee_money,
    "is_deleted": instance.is_deleted,
    "updated_time": instance.updated_time,
    "identity": instance.identity,
    "img_url": instance.img_url,
    "expense_account": instance.expense_account,
    "member_source": instance.member_source,
    "quick_business": instance.quick_business,
    "app_version": instance.app_version,
    "device_id": instance.device_id,
    "has_fee": instance.has_fee,
    "fee_info": instance.fee_info,
    "worker_name": instance.worker_name,
    "group_leader_name": instance.group_leader_name,
    "dept_name": instance.dept_name,
    "fee_switch": instance.fee_switch,
    "work_note_name": instance.work_note_name,
    "unit_name": instance.unit_name,
    "expend_type_name": instance.expend_type_name,
    "unit_work_type_name": instance.unit_work_type_name,
    "unit_work_type_unit": instance.unit_work_type_unit,
    "img_info": instance.img_info,
    "confirm": instance.confirm,
    "is_share_single": instance.is_share_single,
    "busienss_time_string": instance.busienss_time_string,
    "created_time_string": instance.created_time_string,
    "other_expenses": instance.other_expenses,
  };

  factory UpdateProInfoNetModel.fromJson(Map<String, dynamic> json) {
    var netModel = UpdateProInfoNetModel();
    netModel.id = json["id"]?.toString();
    netModel.work_note = json["work_note"]?.toString();
    netModel.group_leader = json["group_leader"]?.toString();
    netModel.worker_id = json["worker_id"]?.toString();
    netModel.business_type = json["business_type"]?.toString();
    netModel.expend_type = json["expend_type"]?.toString();
    netModel.bookkeeping_source = json["bookkeeping_source"]?.toString();
    netModel.bookkeeping_type = json["bookkeeping_type"]?.toString();
    netModel.source_fix_id = json["source_fix_id"]?.toString();
    netModel.unit_work_type = json["unit_work_type"]?.toString();
    netModel.work_time = json["work_time"]?.toString();
    netModel.work_time_hour = json["work_time_hour"]?.toString();
    netModel.overtime = json["overtime"]?.toString();
    netModel.overtime_work = json["overtime_work"]?.toString();
    netModel.morning_work_time = json["morning_work_time"]?.toString();
    netModel.morning_work_time_hour = json["morning_work_time_hour"]?.toString();
    netModel.afternoon_work_time = json["afternoon_work_time"]?.toString();
    netModel.afternoon_work_time_hour = json["afternoon_work_time_hour"]?.toString();
    netModel.user_choose_spotwork = json["user_choose_spotwork"]?.toString();
    netModel.is_rest = json["is_rest"]?.toString();
    netModel.unit = json["unit"]?.toString();
    netModel.unit_num = json["unit_num"]?.toString();
    netModel.unit_price = json["unit_price"]?.toString();
    netModel.money = json["money"]?.toString();
    netModel.real_money = json["real_money"]?.toString();
    netModel.business_time = json["business_time"]?.toString();
    netModel.note = json["note"]?.toString();
    netModel.is_note = json["is_note"]?.toString();
    netModel.fee_standard_id = json["fee_standard_id"]?.toString();
    netModel.fee_money = json["fee_money"]?.toString();
    netModel.is_deleted = json["is_deleted"]?.toString();
    netModel.updated_time = json["updated_time"]?.toString();
    netModel.identity = json["identity"]?.toString();
    netModel.img_url = json["img_url"]?.toString();
    netModel.expense_account = json["expense_account"]?.toString();
    netModel.member_source = json["member_source"]?.toString();
    netModel.quick_business = json["quick_business"]?.toString();
    netModel.app_version = json["app_version"]?.toString();
    netModel.device_id = json["device_id"]?.toString();
    netModel.has_fee = json["has_fee"]?.toString();
    netModel.fee_info = json["fee_info"] == null
        ? null
        : FeeStandardNetModel.fromJson(json["fee_info"] as Map<String, dynamic>);
    netModel.worker_name = json["worker_name"]?.toString();
    netModel.group_leader_name = json["group_leader_name"]?.toString();
    netModel.dept_name = json["dept_name"]?.toString();
    netModel.fee_switch = json["fee_switch"]?.toString();
    netModel.work_note_name = json["work_note_name"]?.toString();
    netModel.unit_name = json["unit_name"]?.toString();
    netModel.expend_type_name = json["expend_type_name"]?.toString();
    netModel.unit_work_type_name = json["unit_work_type_name"]?.toString();
    netModel.unit_work_type_unit = json["unit_work_type_unit"]?.toString();
    netModel.img_info = (json["img_info"] as List<dynamic>?)
        ?.map((e) => e as String)
        .toList();
    netModel.confirm = json["confirm"]?.toString();
    netModel.is_share_single = json["is_share_single"]?.toString();
    netModel.busienss_time_string = json["busienss_time_string"]?.toString();
    netModel.created_time_string = json["created_time_string"]?.toString();
    netModel.other_expenses = json["other_expenses"]?.toString();
    return netModel;
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}
