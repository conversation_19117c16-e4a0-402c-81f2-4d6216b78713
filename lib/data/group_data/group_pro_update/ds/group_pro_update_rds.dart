import 'package:gdjg_pure_flutter/data/group_data/group_pro_update/ds/model/net/update_pro_info_net_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_pro_update/ds/model/param/group_pro_update_param_model.dart';
import 'package:net_plugin/pigeon/api_generated/net_channel.g.dart';
import 'package:net_plugin/pigeon/net_result/resp_result.dart';
import 'package:net_plugin/pigeon/net_service/net_core.dart';

class GroupProUpdateRds {
  Future<RespResult<UpdateProInfoNetModel>> getOneGroupPro(
      String billId, String workNoteId) async {
    return await NetCore.requestJGPHP(
        BaseBizRequestEntity(
          url: '/api/business/get-one/$billId?note_id=$workNoteId',
          method: HTTP_METHOD.GET,
          requestExtra: RequestExtra(showLoading: true),
        ),
        (json) => UpdateProInfoNetModel.fromJson(json));
  }

  Future<RespResult<dynamic>> updateGroupPro(
      String billId, String workNoteId, GroupProUpdateParamModel params) async {
    return await NetCore.requestJGPHP(
        BaseBizRequestEntity(
          url: '/api/business/update/$billId?note_id=$workNoteId',
          method: HTTP_METHOD.POST,
          content: params.toMap(),
          requestExtra: RequestExtra(showLoading: true),
        ),
        (json) => json);
  }

  ///删除记工
  Future<RespResult<dynamic>> deleteGroupPro(
      String billId, String workNoteId) async {
    return await NetCore.requestJGPHP(
        BaseBizRequestEntity(
            url: '/api/business/delete/$billId',
            method: HTTP_METHOD.POST,
            content: {"note_id": workNoteId},
            requestExtra: RequestExtra(showLoading: true)),
        (json) => json);
  }
}
