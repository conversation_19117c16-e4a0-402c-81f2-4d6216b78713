import 'package:gdjg_pure_flutter/data/group_data/group_project/ds/model/net/net_model_group_net_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_project/ds/model/param/dept_list_group_param_model.dart';
import 'package:net_plugin/pigeon/api_generated/net_channel.g.dart';
import 'package:net_plugin/pigeon/net_result/resp_result.dart';
import 'package:net_plugin/pigeon/net_service/net_core.dart';

/// 班组项目远程数据源
class GroupProjectRds {
  /// 获取班组项目列表
  Future<RespResult<NetModelGroupNetModel>> getDeptListGroup(
      DeptListGroupParamModel params) async {
    return await NetCore.requestJGPHP(
        BaseBizRequestEntity(
            url: '/api/v3/dept/list/group',
            method: HTTP_METHOD.GET,
            content: params.toMap()),
        (json) => NetModelGroupNetModel.fromJson(json));
  }
}
