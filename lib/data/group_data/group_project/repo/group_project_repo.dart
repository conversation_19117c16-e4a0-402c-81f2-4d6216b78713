import 'package:gdjg_pure_flutter/data/group_data/group_project/ds/group_project_rds.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_project/ds/model/param/dept_list_group_param_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_project/repo/model/net_model_group_biz_model.dart';
import 'package:net_plugin/pigeon/net_result/resp_result.dart';

/// 班组项目仓库
class GroupProjectRepo {
  final _groupProjectRds = GroupProjectRds();

  /// 获取班组项目列表
  Future<RespResult<NetModelGroupBizModel>> getDeptListGroup({
    String isIgnore = "0",
    String type = "created",
  }) async {
    final params = DeptListGroupParamModel(
      isIgnore: isIgnore,
      type: type,
    );
    
    final result = await _groupProjectRds.getDeptListGroup(params);
    return result.map((netModel) => netModel?.transform() ?? NetModelGroupBizModel());
  }
}
