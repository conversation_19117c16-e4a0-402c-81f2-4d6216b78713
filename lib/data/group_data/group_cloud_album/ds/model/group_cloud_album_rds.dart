import 'package:gdjg_pure_flutter/data/group_data/group_cloud_album/ds/model/net/album_attendance_list_net_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_cloud_album/ds/model/net/album_net_model_net_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_cloud_album/ds/model/net/album_stat_net_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_cloud_album/ds/model/param/album_attendance_list_param_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_cloud_album/ds/model/param/album_net_model_param_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_cloud_album/ds/model/param/album_stat_param_model.dart';
import 'package:net_plugin/pigeon/api_generated/net_channel.g.dart';
import 'package:net_plugin/pigeon/net_result/resp_result.dart';
import 'package:net_plugin/pigeon/net_service/net_core.dart';

class GroupCloudAlbumRds {
  /// 统计数据接口
  Future<RespResult<AlbumStatNetModel>> getAlbumStatData(
      AlbumStatParamModel params) async {
    return await NetCore.requestJGPHP(
        BaseBizRequestEntity(
            url: '/api/v3/album/stat',
            method: HTTP_METHOD.GET,
            content: params.toMap()),
        (json) => AlbumStatNetModel.fromJson(json));
  }
  /// 相册列表数据
  Future<RespResult<AlbumNetModelANetModel>> getAlbumListData(
      AlbumNetModelParamModel params) async {
    return await NetCore.requestJGPHP(
        BaseBizRequestEntity(
            url: '/api/v3/album/list',
            method: HTTP_METHOD.GET,
            content: params.toMap()),
            (json) => AlbumNetModelANetModel.fromJson(json));
  }

  /// 打卡数据
  Future<RespResult<AlbumAttendanceListNetModel>> getAlbumAttendanceListData(
      AlbumAttendanceListParamModel params) async {
    return await NetCore.requestJGPHP(
        BaseBizRequestEntity(
            url: '/api/v3/album/attendance-list',
            method: HTTP_METHOD.GET,
            content: params.toMap().cast()),
            (json) => AlbumAttendanceListNetModel.fromJson(json));
  }
}
