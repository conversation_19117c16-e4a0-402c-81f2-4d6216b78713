import 'dart:convert';

import 'package:gdjg_pure_flutter/data/group_data/group_cloud_album/repo/model/album_attendance_list_biz_model.dart';

class AlbumAttendanceListNetModel {
  PageNetModel? page;
  List<AlbumAttendanceListANetModel>? list;

  AlbumAttendanceListNetModel();

  Map<String, dynamic> toJson(AlbumAttendanceListNetModel instance) {
    var map = <String, Object>{};
    if (page != null) map["page"] = page!;
    if (list != null) map["list"] = list!;
    return map;
  }

  factory AlbumAttendanceListNetModel.fromJson(Map<String, dynamic> json) {
    var netModel = AlbumAttendanceListNetModel();
    netModel.page = json["page"] == null
      ? null
      : PageNetModel.fromJson(json["page"] as Map<String, dynamic>);
    netModel.list = (json["list"] as List<dynamic>?)
      ?.map((e) => AlbumAttendanceListANetModel.fromJson(e as Map<String, dynamic>))
      .toList();
    return netModel;
  }

  AlbumAttendanceListBizModel transform() {
    return AlbumAttendanceListBizModel(
      page: page?.transform(),
      list: list?.map((e) => e.transform()).toList() ?? [],
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class PageNetModel {

  /// 当前页数
  String? page;

  /// 是否有下一页 0-无 1-有
  double? if_next_page;

  PageNetModel();

  Map<String, dynamic> toJson(PageNetModel instance) {
    var map = <String, Object>{};
    if (page != null) map["page"] = page!;
    if (if_next_page != null) map["if_next_page"] = if_next_page!;
    return map;
  }

  factory PageNetModel.fromJson(Map<String, dynamic> json) {
    var netModel = PageNetModel();
    netModel.page = json["page"]?.toString();
    netModel.if_next_page = double.tryParse(json["if_next_page"].toString());
    return netModel;
  }

  PageBizModel transform() {
    return PageBizModel(
      page: page ?? "",
      ifNextPage: if_next_page ?? 0.0,
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class AlbumAttendanceListANetModel {

  /// 日期
  String? date;
  List<AttendanceNetModel>? attendance;

  /// 已打卡人数 当值为0时 显示"全部未打卡" 当值为-1时 不显示"n人已打卡"
  double? atte_num;

  /// 未打卡人数 当值为-1时 不显示"n人未打卡"
  double? not_atte_num;

  /// 拼接好的统计文案
  String? atte_text;

  AlbumAttendanceListANetModel();

  Map<String, dynamic> toJson(AlbumAttendanceListANetModel instance) {
    var map = <String, Object>{};
    if (date != null) map["date"] = date!;
    if (attendance != null) map["attendance"] = attendance!;
    if (atte_num != null) map["atte_num"] = atte_num!;
    if (not_atte_num != null) map["not_atte_num"] = not_atte_num!;
    if (atte_text != null) map["atte_text"] = atte_text!;
    return map;
  }

  factory AlbumAttendanceListANetModel.fromJson(Map<String, dynamic> json) {
    var netModel = AlbumAttendanceListANetModel();
    netModel.date = json["date"]?.toString();
    netModel.attendance = (json["attendance"] as List<dynamic>?)
      ?.map((e) => AttendanceNetModel.fromJson(e as Map<String, dynamic>))
      .toList();
    netModel.atte_num = double.tryParse(json["atte_num"].toString());
    netModel.not_atte_num = double.tryParse(json["not_atte_num"].toString());
    netModel.atte_text = json["atte_text"]?.toString();
    return netModel;
  }

  AlbumAttendanceListABizModel transform() {
    return AlbumAttendanceListABizModel(
      date: date ?? "",
      attendance: attendance?.map((e) => e.transform()).toList() ?? [],
      atteNum: atte_num ?? 0.0,
      notAtteNum: not_atte_num ?? 0.0,
      atteText: atte_text ?? "",
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class AttendanceNetModel {

  /// 工人id
  double? worker_id;

  /// 工人姓名
  String? name;

  /// 日期
  String? date;
  List<RecordNetModel>? record;

  AttendanceNetModel();

  Map<String, dynamic> toJson(AttendanceNetModel instance) {
    var map = <String, Object>{};
    if (worker_id != null) map["worker_id"] = worker_id!;
    if (name != null) map["name"] = name!;
    if (date != null) map["date"] = date!;
    if (record != null) map["record"] = record!;
    return map;
  }

  factory AttendanceNetModel.fromJson(Map<String, dynamic> json) {
    var netModel = AttendanceNetModel();
    netModel.worker_id = double.tryParse(json["worker_id"].toString());
    netModel.name = json["name"]?.toString();
    netModel.date = json["date"]?.toString();
    netModel.record = (json["record"] as List<dynamic>?)
      ?.map((e) => RecordNetModel.fromJson(e as Map<String, dynamic>))
      .toList();
    return netModel;
  }

  AttendanceBizModel transform() {
    return AttendanceBizModel(
      workerId: worker_id ?? 0.0,
      name: name ?? "",
      date: date ?? "",
      record: record?.map((e) => e.transform()).toList() ?? [],
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class RecordNetModel {

  /// 打卡id
  double? id;

  /// 打卡地点
  String? area_name;

  /// 打卡时间
  double? created_time;

  /// 打卡备注
  String? note;

  /// 打卡图片
  String? img;

  RecordNetModel();

  Map<String, dynamic> toJson(RecordNetModel instance) {
    var map = <String, Object>{};
    if (id != null) map["id"] = id!;
    if (area_name != null) map["area_name"] = area_name!;
    if (created_time != null) map["created_time"] = created_time!;
    if (note != null) map["note"] = note!;
    if (img != null) map["img"] = img!;
    return map;
  }

  factory RecordNetModel.fromJson(Map<String, dynamic> json) {
    var netModel = RecordNetModel();
    netModel.id = double.tryParse(json["id"].toString());
    netModel.area_name = json["area_name"]?.toString();
    netModel.created_time = double.tryParse(json["created_time"].toString());
    netModel.note = json["note"]?.toString();
    netModel.img = json["img"]?.toString();
    return netModel;
  }

  RecordBizModel transform() {
    return RecordBizModel(
      id: id ?? 0.0,
      areaName: area_name ?? "",
      createdTime: created_time ?? 0.0,
      note: note ?? "",
      img: img ?? "",
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

