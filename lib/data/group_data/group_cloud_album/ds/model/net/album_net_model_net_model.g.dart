// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'album_net_model_net_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AlbumNetModelNetModel _$AlbumNetModelNetModelFromJson(
        Map<String, dynamic> json) =>
    AlbumNetModelNetModel()
      ..data = json['data'] == null
          ? null
          : AlbumNetModelANetModel.fromJson(
              json['data'] as Map<String, dynamic>);

Map<String, dynamic> _$AlbumNetModelNetModelToJson(
        AlbumNetModelNetModel instance) =>
    <String, dynamic>{
      'data': instance.data,
    };

AlbumNetModelANetModel _$AlbumNetModelANetModelFromJson(
        Map<String, dynamic> json) =>
    AlbumNetModelANetModel()
      ..pageNum = (json['pageNum'] as num?)?.toDouble()
      ..pageSize = (json['pageSize'] as num?)?.toDouble()
      ..size = (json['size'] as num?)?.toDouble()
      ..startRow = (json['startRow'] as num?)?.toDouble()
      ..endRow = (json['endRow'] as num?)?.toDouble()
      ..pages = (json['pages'] as num?)?.toDouble()
      ..prePage = (json['prePage'] as num?)?.toDouble()
      ..nextPage = (json['nextPage'] as num?)?.toDouble()
      ..isFirstPage = json['isFirstPage'] as bool?
      ..isLastPage = json['isLastPage'] as bool?
      ..hasPreviousPage = json['hasPreviousPage'] as bool?
      ..hasNextPage = json['hasNextPage'] as bool?
      ..navigatePages = (json['navigatePages'] as num?)?.toDouble()
      ..navigatepageNums = (json['navigatepageNums'] as List<dynamic>?)
          ?.map((e) => (e as num).toInt())
          .toList()
      ..navigateFirstPage = (json['navigateFirstPage'] as num?)?.toDouble()
      ..navigateLastPage = (json['navigateLastPage'] as num?)?.toDouble()
      ..total = (json['total'] as num?)?.toDouble()
      ..list = (json['list'] as List<dynamic>?)
          ?.map(
              (e) => AlbumNetModelBNetModel.fromJson(e as Map<String, dynamic>))
          .toList();

Map<String, dynamic> _$AlbumNetModelANetModelToJson(
        AlbumNetModelANetModel instance) =>
    <String, dynamic>{
      'pageNum': instance.pageNum,
      'pageSize': instance.pageSize,
      'size': instance.size,
      'startRow': instance.startRow,
      'endRow': instance.endRow,
      'pages': instance.pages,
      'prePage': instance.prePage,
      'nextPage': instance.nextPage,
      'isFirstPage': instance.isFirstPage,
      'isLastPage': instance.isLastPage,
      'hasPreviousPage': instance.hasPreviousPage,
      'hasNextPage': instance.hasNextPage,
      'navigatePages': instance.navigatePages,
      'navigatepageNums': instance.navigatepageNums,
      'navigateFirstPage': instance.navigateFirstPage,
      'navigateLastPage': instance.navigateLastPage,
      'total': instance.total,
      'list': instance.list,
    };

AlbumNetModelBNetModel _$AlbumNetModelBNetModelFromJson(
        Map<String, dynamic> json) =>
    AlbumNetModelBNetModel()
      ..currentDate = json['currentDate'] as String?
      ..photoNum = (json['photoNum'] as num?)?.toInt()
      ..photoDetailList = (json['photoDetailList'] as List<dynamic>?)
          ?.map((e) =>
              PhotoDetailListNetModel.fromJson(e as Map<String, dynamic>))
          .toList();

Map<String, dynamic> _$AlbumNetModelBNetModelToJson(
        AlbumNetModelBNetModel instance) =>
    <String, dynamic>{
      'currentDate': instance.currentDate,
      'photoNum': instance.photoNum,
      'photoDetailList': instance.photoDetailList,
    };

PhotoDetailListNetModel _$PhotoDetailListNetModelFromJson(
        Map<String, dynamic> json) =>
    PhotoDetailListNetModel()
      ..url = json['url'] as String?
      ..photoId = (json['photoId'] as num?)?.toDouble()
      ..uid = (json['uid'] as num?)?.toDouble()
      ..ownerIdList = (json['ownerIdList'] as List<dynamic>?)
          ?.map((e) => (e as num).toInt())
          .toList()
      ..labelList = (json['labelList'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList()
      ..resourceId = json['resourceId'] as String?
      ..resourceType = (json['resourceType'] as num?)?.toDouble()
      ..extraInfo = json['extraInfo'] as String?;

Map<String, dynamic> _$PhotoDetailListNetModelToJson(
        PhotoDetailListNetModel instance) =>
    <String, dynamic>{
      'url': instance.url,
      'photoId': instance.photoId,
      'uid': instance.uid,
      'ownerIdList': instance.ownerIdList,
      'labelList': instance.labelList,
      'resourceId': instance.resourceId,
      'resourceType': instance.resourceType,
      'extraInfo': instance.extraInfo,
    };
