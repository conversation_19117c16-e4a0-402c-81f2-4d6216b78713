// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'album_stat_net_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AlbumStatNetModel _$AlbumStatNetModelFromJson(Map<String, dynamic> json) =>
    AlbumStatNetModel()
      ..data = (json['list'] as List<dynamic>?)
          ?.map((e) => AlbumStatANetModel.fromJson(e as Map<String, dynamic>))
          .toList();

Map<String, dynamic> _$AlbumStatNetModelToJson(AlbumStatNetModel instance) =>
    <String, dynamic>{
      'data': instance.data,
    };

AlbumStatANetModel _$AlbumStatANetModelFromJson(Map<String, dynamic> json) =>
    AlbumStatANetModel()
      ..key = json['key'] as String?
      ..name = json['name'] as String?
      ..icon = json['icon'] as String?
      ..icon_chosen = json['icon_chosen'] as String?
      ..num = json['num'] as int?
      ..is_upload = json['is_upload'] as int?
      ..is_public = json['is_public'] as int?
      ..id = json['id'] as int?
      ..tags = (json['tags'] as List<dynamic>?)?.map((e) => e as String).toList() ?? [];

Map<String, dynamic> _$AlbumStatANetModelToJson(AlbumStatANetModel instance) =>
    <String, dynamic>{
      'key': instance.key,
      'name': instance.name,
      'icon': instance.icon,
      'icon_chosen': instance.icon_chosen,
      'num': instance.num,
      'is_upload': instance.is_upload,
      'is_public': instance.is_public,
      'id': instance.id,
      'tags': instance.tags,
    };
