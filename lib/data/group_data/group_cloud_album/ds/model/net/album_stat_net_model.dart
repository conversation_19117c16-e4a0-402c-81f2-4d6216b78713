import 'dart:convert';
import 'package:json_annotation/json_annotation.dart';

import 'package:gdjg_pure_flutter/data/group_data/group_cloud_album/repo/model/album_stat_biz_model.dart';

part 'album_stat_net_model.g.dart';

@JsonSerializable()
class AlbumStatNetModel {
  List<AlbumStatANetModel>? data;

  AlbumStatNetModel();

  factory AlbumStatNetModel.fromJson(Map<String, dynamic> json) => _$AlbumStatNetModelFromJson(json);

  Map<String, dynamic> toJson() => _$AlbumStatNetModelToJson(this);

  transform() {
    return AlbumStatBizModel(
      data: data?.map((e) => e.transform()).toList() ?? [],
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class AlbumStatANetModel {
  /// 分类标识
  String? key;
  /// 分类名称
  String? name;
  /// 图标全路径
  String? icon;
  /// [4.7]选中时图标全路径
  String? icon_chosen;
  /// 照片数量
  int? num;
  /// 是否允许上传 1是 0否
  int? is_upload;
  /// 是否允许查看照片 1是 0否
  int? is_public;
  /// 分类id
  int? id;
  /// 标签列表
  List<String>? tags;

  AlbumStatANetModel();

  factory AlbumStatANetModel.fromJson(Map<String, dynamic> json) => _$AlbumStatANetModelFromJson(json);

  Map<String, dynamic> toJson() => _$AlbumStatANetModelToJson(this);

  AlbumStatABizModel transform() {
    return AlbumStatABizModel(
      key: key ?? "",
      name: name ?? "",
      icon: icon ?? "",
      iconChosen: icon_chosen ?? "",
      num: num ?? 0,
      isUpload: is_upload ?? 0,
      isPublic: is_public ?? 0,
      id: id ?? 0,
      tags: tags ?? [],
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

