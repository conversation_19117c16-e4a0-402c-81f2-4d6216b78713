import 'dart:convert';
import 'package:json_annotation/json_annotation.dart';

import 'package:gdjg_pure_flutter/data/group_data/group_cloud_album/repo/model/album_net_model_biz_model.dart';

part 'album_net_model_net_model.g.dart';
@JsonSerializable()
class AlbumNetModelNetModel {

  AlbumNetModelANetModel? data;

  AlbumNetModelNetModel();

  factory AlbumNetModelNetModel.fromJson(Map<String, dynamic> json) => _$AlbumNetModelNetModelFromJson(json);

  Map<String, dynamic> toJson() => _$AlbumNetModelNetModelToJson(this);

  transform() {
    return AlbumNetModelBizModel(
      data: data?.transform(),
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class AlbumNetModelANetModel {
  double? pageNum;
  double? pageSize;
  double? size;
  double? startRow;
  double? endRow;
  double? pages;
  double? prePage;
  double? nextPage;
  bool? isFirstPage;
  bool? isLastPage;
  bool? hasPreviousPage;
  bool? hasNextPage;
  double? navigatePages;
  List<int>? navigatepageNums;
  double? navigateFirstPage;
  double? navigateLastPage;
  double? total;
  List<AlbumNetModelBNetModel>? list;

  AlbumNetModelANetModel();

  factory AlbumNetModelANetModel.fromJson(Map<String, dynamic> json) => _$AlbumNetModelANetModelFromJson(json);

  Map<String, dynamic> toJson() => _$AlbumNetModelANetModelToJson(this);

  transform() {
    return AlbumNetModelABizModel(
      pageNum: pageNum ?? 0.0,
      pageSize: pageSize ?? 0.0,
      size: size ?? 0.0,
      startRow: startRow ?? 0.0,
      endRow: endRow ?? 0.0,
      pages: pages ?? 0.0,
      prePage: prePage ?? 0.0,
      nextPage: nextPage ?? 0.0,
      isFirstPage: isFirstPage ?? false,
      isLastPage: isLastPage ?? false,
      hasPreviousPage: hasPreviousPage ?? false,
      hasNextPage: hasNextPage ?? false,
      navigatePages: navigatePages ?? 0.0,
      navigatepageNums: navigatepageNums?.map((e) => e).toList() ?? [],
      navigateFirstPage: navigateFirstPage ?? 0.0,
      navigateLastPage: navigateLastPage ?? 0.0,
      total: total ?? 0.0,
      list: list?.map((e) => e.transform()).toList() ?? [],
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class AlbumNetModelBNetModel {
  String? currentDate;
  int? photoNum;
  List<PhotoDetailListNetModel>? photoDetailList;

  AlbumNetModelBNetModel();

  factory AlbumNetModelBNetModel.fromJson(Map<String, dynamic> json) => _$AlbumNetModelBNetModelFromJson(json);

  Map<String, dynamic> toJson() => _$AlbumNetModelBNetModelToJson(this);

  AlbumNetModelBBizModel transform() {
    return AlbumNetModelBBizModel(
      currentDate: currentDate ?? "",
      photoNum: photoNum ?? 0,
      photoDetailList: photoDetailList?.map((e) => e.transform()).toList() ?? [],
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class PhotoDetailListNetModel {
  String? url;
  double? photoId;
  double? uid;
  List<int>? ownerIdList;
  List<String>? labelList;
  String? resourceId;
  double? resourceType;
  String? extraInfo;

  PhotoDetailListNetModel();

  factory PhotoDetailListNetModel.fromJson(Map<String, dynamic> json) => _$PhotoDetailListNetModelFromJson(json);

  Map<String, dynamic> toJson() => _$PhotoDetailListNetModelToJson(this);

  PhotoDetailListBizModel transform() {
    return PhotoDetailListBizModel(
      url: url ?? "",
      photoId: photoId ?? 0.0,
      uid: uid ?? 0.0,
      ownerIdList: ownerIdList?.map((e) => e).toList() ?? [],
      labelList: labelList?.map((e) => e).toList() ?? [],
      resourceId: resourceId ?? "",
      resourceType: resourceType ?? 0.0,
      extraInfo: extraInfo ?? "",
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

