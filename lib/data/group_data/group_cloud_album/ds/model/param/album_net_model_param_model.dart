class AlbumNetModelParamModel {

  /// 开始日期YYYY-mm-dd
  final String start_date;

  /// 结束日期YYYY-mm-dd
  final String end_date;

  /// 筛选项目，多个以英文逗号分隔
  final String work_notes;

  /// 筛选工友，多个以英文逗号分隔
  final String worker_ids;

  /// 分类标识，从分类计数中返回的key
  final String key;

  /// 每页大小，默认20
  final String limit;

  /// 游标，首次可为空。取值列表中返回的nextSearchAfter字段。需要以英文逗号组装成字符串传过来
  final String next;

   AlbumNetModelParamModel({
     this.start_date = "",
     this.end_date = "",
     this.work_notes = "",
     this.worker_ids = "",
     this.key = "",
     this.limit = "",
     this.next = "",
  });

   toMap() {
    return {
      "start_date": start_date,
      "end_date": end_date,
      "work_notes": work_notes,
      "worker_ids": worker_ids, // 确保可空字段有默认值
      "key": key,
      "limit": limit,
      "next": next,
    };
  }
}

