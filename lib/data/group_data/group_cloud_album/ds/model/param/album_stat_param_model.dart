class AlbumStatParamModel {

  /// 开始日期YYYY-mm-dd
  final String start_date;

  /// 结束日期YYYY-mm-dd
  final String end_date;

  /// 筛选项目，多个以英文逗号分隔
  final String work_notes;

  /// 筛选工友，多个以英文逗号分隔
  final String worker_ids;

  /// token
  final String wechat_token;

   AlbumStatParamModel({
     this.start_date = "",
     this.end_date = "",
     this.work_notes = "",
     this.worker_ids = "",
     this.wechat_token = "",
  });

  Map<String, Object> toMap() {
    return {
      "start_date": start_date,
      "end_date": end_date,
      "work_notes": work_notes,
      "worker_ids": worker_ids, // 确保可空字段有默认值
      "wechat_token": wechat_token,
    };
  }
}

