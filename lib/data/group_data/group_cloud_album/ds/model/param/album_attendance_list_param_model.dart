class AlbumAttendanceListParamModel {

  /// 筛选项目，多个以英文逗号分隔
  final String? work_notes;

  /// 开始日期YYYY-mm-dd
  final String? start_date;

  /// 结束日期YYYY-mm-dd
  final String? end_date;

  /// 筛选工友，多个以英文逗号分隔
  final String? worker_ids;

  /// 页码 默认为1
  final String? page;

  /// 每页数量 默认为10
  final String? limit;

   AlbumAttendanceListParamModel({
    this.work_notes,
    this.start_date,
    this.end_date,
    this.worker_ids,
    this.page,
    this.limit,
  });

  Map<String, dynamic> toMap() {
    var map = <String, Object>{};
      if (work_notes != null) map["work_notes"] = work_notes!;
      if (start_date != null) map["start_date"] = start_date!;
      if (end_date != null) map["end_date"] = end_date!;
      if (worker_ids != null) map["worker_ids"] = worker_ids!;
      if (page != null) map["page"] = page!;
      if (limit != null) map["limit"] = limit!;
    return map;
  }
}

