import 'dart:convert';

class AlbumAttendanceListBizModel {
  PageBizModel? page;
  List<AlbumAttendanceListABizModel> list;

  AlbumAttendanceListBizModel({
    this.page,
    this.list = const [],
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class PageBizModel {

  /// 当前页数
  String page;

  /// 是否有下一页 0-无 1-有
  double ifNextPage;

  PageBizModel({
    this.page = "",
    this.ifNextPage = 0.0,
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class AlbumAttendanceListABizModel {

  /// 日期
  String date;
  List<AttendanceBizModel> attendance;

  /// 已打卡人数 当值为0时 显示"全部未打卡" 当值为-1时 不显示"n人已打卡"
  double atteNum;

  /// 未打卡人数 当值为-1时 不显示"n人未打卡"
  double notAtteNum;

  /// 拼接好的统计文案
  String atteText;

  AlbumAttendanceListABizModel({
    this.date = "",
    this.attendance = const [],
    this.atteNum = 0.0,
    this.notAtteNum = 0.0,
    this.atteText = "",
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class AttendanceBizModel {

  /// 工人id
  double workerId;

  /// 工人姓名
  String name;

  /// 日期
  String date;
  List<RecordBizModel> record;

  AttendanceBizModel({
    this.workerId = 0.0,
    this.name = "",
    this.date = "",
    this.record = const [],
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class RecordBizModel {

  /// 打卡id
  double id;

  /// 打卡地点
  String areaName;

  /// 打卡时间
  double createdTime;

  /// 打卡备注
  String note;

  /// 打卡图片
  String img;

  RecordBizModel({
    this.id = 0.0,
    this.areaName = "",
    this.createdTime = 0.0,
    this.note = "",
    this.img = "",
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}

