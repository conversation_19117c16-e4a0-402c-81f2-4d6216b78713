import 'dart:convert';

class AlbumStatBizModel {
  double code;
  String message;
  List<AlbumStatABizModel> data;

  AlbumStatBizModel({
    this.code = 0.0,
    this.message = "",
    this.data = const [],
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class AlbumStatABizModel {
  /// 分类标识
  String key;
  /// 分类名称
  String name;
  /// 图标全路径
  String icon;
  /// [4.7]选中时图标全路径
  String iconChosen;
  /// 照片数量
  int num;
  /// 是否允许上传 1是 0否
  int isUpload;
  /// 是否允许查看照片 1是 0否
  int isPublic;
  int id;
  List<String> tags;

  AlbumStatABizModel({
    this.key = "",
    this.name = "",
    this.icon = "",
    this.iconChosen = "",
    this.num = 0,
    this.isUpload = 0,
    this.isPublic = 0,
    this.id = 0,
    this.tags = const [],
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}

