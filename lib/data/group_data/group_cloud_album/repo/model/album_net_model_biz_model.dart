import 'dart:convert';

class AlbumNetModelBizModel {
  double code;
  String message;
  final AlbumNetModelABizModel? data;

  AlbumNetModelBizModel({
    this.code = 0.0,
    this.message = "",
    this.data,
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class AlbumNetModelABizModel {
  double pageNum;
  double pageSize;
  double size;
  double startRow;
  double endRow;
  double pages;
  double prePage;
  double nextPage;
  bool isFirstPage;
  bool isLastPage;
  bool hasPreviousPage;
  bool hasNextPage;
  double navigatePages;
  List<int> navigatepageNums;
  double navigateFirstPage;
  double navigateLastPage;
  double total;
  List<AlbumNetModelBBizModel> list;

  AlbumNetModelABizModel({
    this.pageNum = 0.0,
    this.pageSize = 0.0,
    this.size = 0.0,
    this.startRow = 0.0,
    this.endRow = 0.0,
    this.pages = 0.0,
    this.prePage = 0.0,
    this.nextPage = 0.0,
    this.isFirstPage = false,
    this.isLastPage = false,
    this.hasPreviousPage = false,
    this.hasNextPage = false,
    this.navigatePages = 0.0,
    this.navigatepageNums = const [],
    this.navigateFirstPage = 0.0,
    this.navigateLastPage = 0.0,
    this.total = 0.0,
    this.list = const [],
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class AlbumNetModelBBizModel {
  String currentDate;
  int photoNum;
  List<PhotoDetailListBizModel> photoDetailList;

  AlbumNetModelBBizModel({
    this.currentDate = "",
    this.photoNum = 0,
    this.photoDetailList = const [],
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class PhotoDetailListBizModel {
  String url;
  double photoId;
  double uid;
  List<int> ownerIdList;
  List<String> labelList;
  String resourceId;
  double resourceType;
  String extraInfo;

  PhotoDetailListBizModel({
    this.url = "",
    this.photoId = 0.0,
    this.uid = 0.0,
    this.ownerIdList = const [],
    this.labelList = const [],
    this.resourceId = "",
    this.resourceType = 0.0,
    this.extraInfo = "",
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}

