import 'package:gdjg_pure_flutter/data/group_data/group_cloud_album/ds/model/group_cloud_album_rds.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_cloud_album/ds/model/net/album_net_model_net_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_cloud_album/ds/model/net/album_stat_net_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_cloud_album/ds/model/param/album_attendance_list_param_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_cloud_album/ds/model/param/album_net_model_param_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_cloud_album/ds/model/param/album_stat_param_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_cloud_album/repo/model/album_attendance_list_biz_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_cloud_album/repo/model/album_net_model_biz_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_cloud_album/repo/model/album_stat_biz_model.dart';
import 'package:net_plugin/pigeon/net_result/resp_result.dart';

class GroupCloudAlbumRepo {
  final GroupCloudAlbumRds _groupCloudAlbumRds = GroupCloudAlbumRds();

  /// 获取班组云相册统计数据
  Future<RespResult<AlbumStatBizModel>> getGroupStatData(
      AlbumStatParamModel params) async {
    final result = await _groupCloudAlbumRds.getAlbumStatData(params);
    return result.map(_transform);
  }

  AlbumStatBizModel _transform(AlbumStatNetModel? netModel) {
    return netModel?.transform();
  }

  /// 获取班组云相册列表数据
  Future<RespResult<AlbumNetModelABizModel>> getGroupListData(
      AlbumNetModelParamModel params) async {
    final result = await _groupCloudAlbumRds.getAlbumListData(params);
    return result.map(_transformList);
  }

  AlbumNetModelABizModel _transformList(AlbumNetModelANetModel? netModel) {
    return netModel?.transform();
  }

  Future<RespResult<AlbumAttendanceListBizModel?>> getAlbumAttendanceListData(
      AlbumAttendanceListParamModel params) async {
    final result = await _groupCloudAlbumRds.getAlbumAttendanceListData(params);
    return result.map((result) => result?.transform());
  }
}
