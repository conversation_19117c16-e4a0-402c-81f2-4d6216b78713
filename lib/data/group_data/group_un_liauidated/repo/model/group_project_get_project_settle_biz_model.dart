import 'dart:convert';

class GroupProjectGetProjectSettleBizModel {
  /// 
  final SettleDataBizModel? settleData;
  /// 
  final NotSettleDataBizModel? notSettleData;

  GroupProjectGetProjectSettleBizModel({
    this.settleData,
    this.notSettleData,
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class SettleDataBizModel {
  /// 
  double allSettle;
  /// 
  List<GroupProjectGetProjectSettleABizModel> list;
  /// 
  double num;

  SettleDataBizModel({
    this.allSettle = 0.0,
    this.list = const [],
    this.num = 0.0,
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class GroupProjectGetProjectSettleABizModel {
  /// 注意这个就是工友名字
  String name;
  /// 
  double workerId;
  /// 
  double isSettle;
  /// 
  double isDeleted;
  ///
  double settle;
  /// 
  String startTime;
  /// 
  String endTime;

  GroupProjectGetProjectSettleABizModel({
    this.workerId = 0,
    this.isSettle = 0.0,
    this.isDeleted = 0.0,
    this.name = "",
    this.settle = 0.0,
    this.startTime = "",
    this.endTime = "",
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class NotSettleDataBizModel {
  /// 
  double allSettle;
  /// 
  List<GroupProjectGetProjectSettleBBizModel> list;
  /// 
  double num;

  NotSettleDataBizModel({
    this.allSettle = 0.0,
    this.list = const [],
    this.num = 0.0,
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class GroupProjectGetProjectSettleBBizModel {
  /// 注意这个就是工友名字
  String name;
  ///
  double workerId;
  /// 
  double isSettle;
  /// 
  int isDeleted;
  ///
  double settle;
  /// 
  String startTime;
  /// 
  String endTime;

  GroupProjectGetProjectSettleBBizModel({
    this.workerId = 0,
    this.isSettle = 0.0,
    this.isDeleted = 0,
    this.name = "",
    this.settle = 0.0,
    this.startTime = "",
    this.endTime = "",
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}

