import 'package:gdjg_pure_flutter/data/group_data/group_un_liauidated/ds/model/param/group_business_get_group_business_count_param_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_un_liauidated/ds/model/param/group_project_get_group_worker_settle_param_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_un_liauidated/ds/model/param/group_project_get_project_settle_param_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_un_liauidated/ds/model/param/group_project_update_worker_settle_param_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_un_liauidated/ds/model/project_settle_rds.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_un_liauidated/repo/model/group_business_get_group_business_count_biz_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_un_liauidated/repo/model/group_project_get_group_worker_settle_biz_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_un_liauidated/repo/model/group_project_get_project_settle_biz_model.dart';
import 'package:net_plugin/pigeon/net_result/resp_result.dart';
import 'model/group_project_get_all_group_worker_settle_biz_model.dart';

class ProjectSettleRepo {
  final _contactRds = ProjectSettleRds();

  ///手动添加工友
  Future<RespResult<GroupProjectGetProjectSettleBizModel?>> getProjectSettle(
      GroupProjectGetProjectSettleParamModel param) async {
    final result = await _contactRds.getProjectSettle(param);
    return result.map((result) => result?.transform());
  }

  /// 获取班组内工人未结钱数
  Future<RespResult<GroupProjectGetGroupWorkerSettleBizModel?>>
      getGroupProjectWorkerSettle(
          GroupProjectGetGroupWorkerSettleParamModel param) async {
    final result = await _contactRds.getGroupProjectWorkerSettle(param);
    return result.map((result) => result?.transform());
  }

  /// 班组统计
  Future<RespResult<GroupBusinessGetGroupBusinessCountBizModel?>>
      getGroupBusinessCount(
          GroupBusinessGetGroupBusinessCountParamModel param) async {
    final result = await _contactRds.groupBusinessCount(param);
    return result.map((result) => result?.transform());
  }

  ///  修改班组 工人 结清状态
  Future<RespResult<dynamic>> updateWorkerSettle(
      GroupProjectUpdateWorkerSettleParamModel param) async {
    return await _contactRds.updateWorkerSettle(param);
  }

  /// 获取班组所有工人未结钱数总和
  Future<RespResult<GroupProjectGetAllGroupWorkerSettleBizModel?>>
      getAllGroupWorkerSettle(String workNote) async {
    final result = await _contactRds.getAllGroupWorkerSettle(workNote);
    return result.map((result) => result?.transform());
  }
}
