class GroupBusinessGetGroupBusinessCountParamModel {
  ///
  final String? worker_ids;

  /// 有默认值，默认为当前月
  final String? start_time;

  /// 有默认值，默认为当前月
  final String? end_time;

  /// 记工类型 多选, 隔开
  final String? business_type;

  /// 记工本id, 默认全部记工本
  final String? work_note;

  /// 点工统计展示  1 上班工天，加班小时 2 上班加班 工天 3 上班加班小时
  final String? show_type;

  GroupBusinessGetGroupBusinessCountParamModel({
    this.worker_ids,
    this.start_time,
    this.end_time,
    this.business_type,
    this.work_note,
    this.show_type,
  });

  Map<String, dynamic> toMap() {
    var map = <String, Object>{};
    if (worker_ids != null) map['worker_ids'] = worker_ids!;
    if (start_time != null) map['start_time'] = start_time!;
    if (end_time != null) map['end_time'] = end_time!;
    if (work_note != null) map['work_note'] = work_note!;
    if (business_type != null) map['business_type'] = business_type!;
    if (show_type != null) map['show_type'] = show_type!;
    return map;
  }
}
