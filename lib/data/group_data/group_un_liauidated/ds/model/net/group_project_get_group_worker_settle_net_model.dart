import 'dart:convert';

import 'package:gdjg_pure_flutter/data/group_data/group_un_liauidated/repo/model/group_project_get_group_worker_settle_biz_model.dart';

class GroupProjectGetGroupWorkerSettleNetModel {
  /// 未结金额
  String? not_settle_money;

  /// 开始时间
  String? start_time;

  /// 结束时间
  String? end_time;

  /// 是否已结清
  String? is_settle;

  /// 是否已退场
  String? is_deleted;

  ///
  String? worker_name;

  /// 点工工资规则id
  String? fee_standard_id;

  /// 包工工资规则id
  String? fee_standard_id_contractor;

  GroupProjectGetGroupWorkerSettleNetModel();

  Map<String, dynamic> toJson(
          GroupProjectGetGroupWorkerSettleNetModel instance) =>
      <String, dynamic>{
        "not_settle_money": instance.not_settle_money,
        "start_time": instance.start_time,
        "end_time": instance.end_time,
        "is_settle": instance.is_settle,
        "worker_name": instance.worker_name,
        "fee_standard_id": instance.fee_standard_id,
        "fee_standard_id_contractor": instance.fee_standard_id_contractor,
      };

  factory GroupProjectGetGroupWorkerSettleNetModel.fromJson(
      Map<String, dynamic> json) {
    var netModel = GroupProjectGetGroupWorkerSettleNetModel();
    netModel.not_settle_money = json["not_settle_money"]?.toString();
    netModel.start_time = json["start_time"]?.toString();
    netModel.end_time = json["end_time"]?.toString();
    netModel.is_settle = json["is_settle"]?.toString();
    netModel.worker_name = json["worker_name"]?.toString();
    netModel.fee_standard_id = json["fee_standard_id"]?.toString();
    netModel.fee_standard_id_contractor =
        json["fee_standard_id_contractor"]?.toString();
    return netModel;
  }

  GroupProjectGetGroupWorkerSettleBizModel transform() {
    return GroupProjectGetGroupWorkerSettleBizModel(
      notSettleMoney: double.parse(not_settle_money ?? "0.00"),
      startTime: start_time ?? "",
      endTime: end_time ?? "",
      isDeleted: is_deleted ?? "0",
      isSettle: is_settle ?? "0",
      workerName: worker_name ?? "",
      feeStandardId: fee_standard_id ?? "",
      feeStandardIdContractor: fee_standard_id_contractor ?? "",
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}
