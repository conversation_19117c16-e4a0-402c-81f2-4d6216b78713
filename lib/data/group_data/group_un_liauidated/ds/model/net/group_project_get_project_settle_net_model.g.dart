// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'group_project_get_project_settle_net_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

GroupProjectGetProjectSettleNetModel
    _$GroupProjectGetProjectSettleNetModelFromJson(Map<String, dynamic> json) =>
        GroupProjectGetProjectSettleNetModel()
          ..settle_data = json['settle_data'] == null
              ? null
              : SettleDataNetModel.from<PERSON>son(
                  json['settle_data'] as Map<String, dynamic>)
          ..not_settle_data = json['not_settle_data'] == null
              ? null
              : NotSettleDataNetModel.fromJson(
                  json['not_settle_data'] as Map<String, dynamic>);

Map<String, dynamic> _$GroupProjectGetProjectSettleNetModelToJson(
        GroupProjectGetProjectSettleNetModel instance) =>
    <String, dynamic>{
      'settle_data': instance.settle_data,
      'not_settle_data': instance.not_settle_data,
    };

SettleDataNetModel _$SettleDataNetModelFromJson(Map<String, dynamic> json) =>
    SettleDataNetModel()
      ..all_settle = (json['all_settle'] as num?)?.toDouble()
      ..list = (json['list'] as List<dynamic>?)
          ?.map((e) => GroupProjectGetProjectSettleANetModel.fromJson(
              e as Map<String, dynamic>))
          .toList()
      ..num = (json['num'] as num?)?.toDouble();

Map<String, dynamic> _$SettleDataNetModelToJson(SettleDataNetModel instance) =>
    <String, dynamic>{
      'all_settle': instance.all_settle,
      'list': instance.list,
      'num': instance.num,
    };

GroupProjectGetProjectSettleANetModel
    _$GroupProjectGetProjectSettleANetModelFromJson(
            Map<String, dynamic> json) =>
        GroupProjectGetProjectSettleANetModel()
          ..name = json['name'] as String?
          ..worker_id = (json['worker_id'] as num?)?.toDouble()
          ..is_settle = (json['is_settle'] as num?)?.toDouble()
          ..is_deleted = (json['is_deleted'] as num?)?.toDouble()
          ..settle = (json['settle'] as num?)?.toDouble()
          ..start_time = json['start_time'] as String?
          ..end_time = json['end_time'] as String?;

Map<String, dynamic> _$GroupProjectGetProjectSettleANetModelToJson(
        GroupProjectGetProjectSettleANetModel instance) =>
    <String, dynamic>{
      'worker_id': instance.worker_id,
      'is_settle': instance.is_settle,
      'is_deleted': instance.is_deleted,
      'name': instance.name,
      'settle': instance.settle,
      'start_time': instance.start_time,
      'end_time': instance.end_time,
    };

NotSettleDataNetModel _$NotSettleDataNetModelFromJson(
        Map<String, dynamic> json) =>
    NotSettleDataNetModel()
      ..all_settle = (json['all_settle'] as num?)?.toDouble()
      ..list = (json['list'] as List<dynamic>?)
          ?.map((e) => GroupProjectGetProjectSettleBNetModel.fromJson(
              e as Map<String, dynamic>))
          .toList()
      ..num = (json['num'] as num?)?.toDouble();

Map<String, dynamic> _$NotSettleDataNetModelToJson(
        NotSettleDataNetModel instance) =>
    <String, dynamic>{
      'all_settle': instance.all_settle,
      'list': instance.list,
      'num': instance.num,
    };

GroupProjectGetProjectSettleBNetModel
    _$GroupProjectGetProjectSettleBNetModelFromJson(
            Map<String, dynamic> json) =>
        GroupProjectGetProjectSettleBNetModel()
          ..name = json['name'] as String?
          ..worker_id = (json['worker_id'] as num?)?.toDouble()
          ..is_settle = (json['is_settle'] as num?)?.toDouble()
          ..is_deleted = (json['is_deleted'] as num?)?.toInt()
          ..settle = (json['settle'] as num?)?.toDouble()
          ..start_time = json['start_time'] as String?
          ..end_time = json['end_time'] as String?;

Map<String, dynamic> _$GroupProjectGetProjectSettleBNetModelToJson(
        GroupProjectGetProjectSettleBNetModel instance) =>
    <String, dynamic>{
      'name': instance.name,
      'worker_id': instance.worker_id,
      'is_settle': instance.is_settle,
      'is_deleted': instance.is_deleted,
      'settle': instance.settle,
      'start_time': instance.start_time,
      'end_time': instance.end_time,
    };
