import 'dart:convert';
import 'package:gdjg_pure_flutter/data/group_data/group_un_liauidated/repo/model/group_project_get_project_settle_biz_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'group_project_get_project_settle_net_model.g.dart';

@JsonSerializable()
class GroupProjectGetProjectSettleNetModel {
  ///已结算
  SettleDataNetModel? settle_data;

  ///未结算
  NotSettleDataNetModel? not_settle_data;

  GroupProjectGetProjectSettleNetModel();

  factory GroupProjectGetProjectSettleNetModel.fromJson(
          Map<String, dynamic> json) =>
      _$GroupProjectGetProjectSettleNetModelFromJson(json);

  Map<String, dynamic> toJson() =>
      _$GroupProjectGetProjectSettleNetModelToJson(this);

  GroupProjectGetProjectSettleBizModel transform() {
    return GroupProjectGetProjectSettleBizModel(
      settleData: settle_data?.transform(),
      notSettleData: not_settle_data?.transform(),
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class SettleDataNetModel {
  ///
  double? all_settle;

  ///
  List<GroupProjectGetProjectSettleANetModel>? list;

  ///
  double? num;

  SettleDataNetModel();

  factory SettleDataNetModel.fromJson(Map<String, dynamic> json) =>
      _$SettleDataNetModelFromJson(json);

  Map<String, dynamic> toJson() => _$SettleDataNetModelToJson(this);

  SettleDataBizModel transform() {
    return SettleDataBizModel(
      allSettle: all_settle ?? 0.0,
      list: list?.map((e) => e.transform()).toList() ?? [],
      num: num ?? 0.0,
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class GroupProjectGetProjectSettleANetModel {
  ///
  double? worker_id;

  ///
  double? is_settle;

  ///
  double? is_deleted;

  ///
  String? name;

  ///
  double? settle;

  ///
  String? start_time;

  ///
  String? end_time;

  GroupProjectGetProjectSettleANetModel();

  factory GroupProjectGetProjectSettleANetModel.fromJson(
          Map<String, dynamic> json) =>
      _$GroupProjectGetProjectSettleANetModelFromJson(json);

  Map<String, dynamic> toJson() =>
      _$GroupProjectGetProjectSettleANetModelToJson(this);

  GroupProjectGetProjectSettleABizModel transform() {
    return GroupProjectGetProjectSettleABizModel(
      workerId: worker_id ?? 0,
      isSettle: is_settle ?? 0.0,
      isDeleted: is_deleted ?? 0.0,
      name: name ?? "",
      settle: settle ?? 0.0,
      startTime: start_time ?? "",
      endTime: end_time ?? "",
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class NotSettleDataNetModel {
  ///
  double? all_settle;

  ///
  List<GroupProjectGetProjectSettleBNetModel>? list;

  ///
  double? num;

  NotSettleDataNetModel();

  factory NotSettleDataNetModel.fromJson(Map<String, dynamic> json) =>
      _$NotSettleDataNetModelFromJson(json);

  Map<String, dynamic> toJson() => _$NotSettleDataNetModelToJson(this);

  NotSettleDataBizModel transform() {
    return NotSettleDataBizModel(
      allSettle: all_settle ?? 0.0,
      list: list?.map((e) => e.transform()).toList() ?? [],
      num: num ?? 0.0,
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class GroupProjectGetProjectSettleBNetModel {
  ///注意这个就是工友名字
  String? name;
  ///
  double? worker_id;

  ///
  double? is_settle;

  ///
  int? is_deleted;

  ///
  double? settle;

  ///
  String? start_time;

  ///
  String? end_time;

  GroupProjectGetProjectSettleBNetModel();

  factory GroupProjectGetProjectSettleBNetModel.fromJson(
          Map<String, dynamic> json) =>
      _$GroupProjectGetProjectSettleBNetModelFromJson(json);

  Map<String, dynamic> toJson() =>
      _$GroupProjectGetProjectSettleBNetModelToJson(this);

  GroupProjectGetProjectSettleBBizModel transform() {
    return GroupProjectGetProjectSettleBBizModel(
      name: name ?? "",
      workerId: worker_id ?? 0.0,
      isSettle: is_settle ?? 0.0,
      isDeleted: is_deleted ?? 0,
      settle: settle ?? 0.0,
      startTime: start_time ?? "",
      endTime: end_time ?? "",
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}
