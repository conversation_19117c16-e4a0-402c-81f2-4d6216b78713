import 'dart:convert';
import '../../../repo/model/group_project_get_all_group_worker_settle_biz_model.dart';

class GroupProjectGetAllGroupWorkerSettleNetModel {

  /// 
  double? not_settle_money;

  GroupProjectGetAllGroupWorkerSettleNetModel();

  Map<String, dynamic> toJson(GroupProjectGetAllGroupWorkerSettleNetModel instance) {
    var map = <String, Object>{};
    if (not_settle_money != null) map["not_settle_money"] = not_settle_money!;
    return map;
  }

  factory GroupProjectGetAllGroupWorkerSettleNetModel.fromJson(Map<String, dynamic> json) {
    var netModel = GroupProjectGetAllGroupWorkerSettleNetModel();
    netModel.not_settle_money = double.tryParse(json["not_settle_money"].toString());
    return netModel;
  }

  GroupProjectGetAllGroupWorkerSettleBizModel transform() {
    return GroupProjectGetAllGroupWorkerSettleBizModel(
      notSettleMoney: not_settle_money ?? 0.0,
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

