import 'package:gdjg_pure_flutter/data/group_data/group_un_liauidated/ds/model/net/group_business_get_group_business_count_net_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_un_liauidated/ds/model/net/group_project_get_group_worker_settle_net_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_un_liauidated/ds/model/net/group_project_get_project_settle_net_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_un_liauidated/ds/model/param/group_business_get_group_business_count_param_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_un_liauidated/ds/model/param/group_project_get_group_worker_settle_param_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_un_liauidated/ds/model/param/group_project_get_project_settle_param_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_un_liauidated/ds/model/param/group_project_update_worker_settle_param_model.dart';
import 'package:net_plugin/pigeon/api_generated/net_channel.g.dart';
import 'package:net_plugin/pigeon/net_result/resp_result.dart';
import 'package:net_plugin/pigeon/net_service/net_core.dart';
import 'net/group_project_get_all_group_worker_settle_net_model.dart';

class ProjectSettleRds {
  ///班组人员已结/未结界面
  Future<RespResult<GroupProjectGetProjectSettleNetModel>> getProjectSettle(
      GroupProjectGetProjectSettleParamModel params) async {
    return await NetCore.requestJGPHP(
        BaseBizRequestEntity(
            url: 'api/v3/group-project/get-project-settle',
            method: HTTP_METHOD.GET,
            content: params.toMap(),
            requestExtra: RequestExtra(showLoading: true)),
        (json) => GroupProjectGetProjectSettleNetModel.fromJson(json));
  }

  /// 获取班组内工人未结钱数
  Future<RespResult<GroupProjectGetGroupWorkerSettleNetModel>>
      getGroupProjectWorkerSettle(
          GroupProjectGetGroupWorkerSettleParamModel params) async {
    return await NetCore.requestJGPHP(
        BaseBizRequestEntity(
            url: 'api/v3/group-project/get-group-worker-settle',
            method: HTTP_METHOD.GET,
            content: params.toMap().cast(),
            requestExtra: RequestExtra(showLoading: true)),
        (json) => GroupProjectGetGroupWorkerSettleNetModel.fromJson(json));
  }

  /// 班组统计
  Future<RespResult<GroupBusinessGetGroupBusinessCountNetModel>>
      groupBusinessCount(
          GroupBusinessGetGroupBusinessCountParamModel params) async {
    return await NetCore.requestJGPHP(
        BaseBizRequestEntity(
            url: 'api/v3/group-business/get-group-business-count',
            method: HTTP_METHOD.GET,
            content: params.toMap().cast(),
            requestExtra: RequestExtra(showLoading: true)),
        (json) => GroupBusinessGetGroupBusinessCountNetModel.fromJson(json));
  }

  /// 修改班组 工人 结清状态
  Future<RespResult<dynamic>> updateWorkerSettle(
      GroupProjectUpdateWorkerSettleParamModel params) async {
    return await NetCore.requestJGPHP(
        BaseBizRequestEntity(
            url: 'api/v3/group-project/update-worker-settle',
            method: HTTP_METHOD.POST,
            content: params.toMap().cast(),
            requestExtra: RequestExtra(showLoading: true)),
        (json) => null);
  }

  /// 获取班组所有工人未结钱数总和
  Future<RespResult<GroupProjectGetAllGroupWorkerSettleNetModel>>
      getAllGroupWorkerSettle(String workNote) async {
    return await NetCore.requestJGPHP(
        BaseBizRequestEntity(
            url: 'api/v3/group-project/get-all-group-worker-settle',
            method: HTTP_METHOD.GET,
            content: {'work_note': workNote},
            requestExtra: RequestExtra(showLoading: true)),
        (json) => GroupProjectGetAllGroupWorkerSettleNetModel.fromJson(json));
  }
}
