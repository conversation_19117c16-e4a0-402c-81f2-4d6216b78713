import 'dart:convert';

class BusinessGetBkWageIndexBizModel {
  double bookkeepingSource;
  double sourceFixId;

  BusinessGetBkWageIndexBizModel({
    this.bookkeepingSource = 0.0,
    this.sourceFixId = 0.0,
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class BusinessGetBkWageIndexABizModel {
  double workerId;
  String workerName;
  BusinessGetBkWageIndexBBizModel? data;

  BusinessGetBkWageIndexABizModel({
    this.workerId = 0.0,
    this.workerName = "",
    this.data,
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class BusinessGetBkWageIndexBBizModel {
  BorrowBizModel? borrow;
  WageBizModel? wage;
  String all;

  BusinessGetBkWageIndexBBizModel({
    this.borrow,
    this.wage,
    this.all = "",
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class BorrowBizModel {
  String money;
  double count;
  double businessType;
  dynamic bookkeepingType;

  BorrowBizModel({
    this.money = "",
    this.count = 0.0,
    this.businessType = 0.0,
    this.bookkeepingType = "",
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class WageBizModel {
  String money;
  double count;
  double businessType;
  double bookkeepingType;

  WageBizModel({
    this.money = "",
    this.count = 0.0,
    this.businessType = 0.0,
    this.bookkeepingType = 0.0,
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}

