import 'package:gdjg_pure_flutter/data/group_data/group_settlement/ds/group_settlement_rds.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_settlement/ds/model/net/business_get_bk_wage_index_net_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_settlement/ds/model/param/group_settlement_param_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_settlement/repo/model/business_get_bk_wage_index_biz_model.dart';
import 'package:net_plugin/pigeon/net_result/resp_fail.dart';
import 'package:net_plugin/pigeon/net_result/resp_result.dart';

class ProjectSettleRepo {
  final _rds = GroupSettlementRds();

  ///班组结算 获取结算信息
  Future<RespResult<BusinessGetBkWageIndexBizModel>> getBkWageIndex(
      String? workNoteId) async {
    if (workNoteId == null) {
      return RespFail.buildProcessFail("参数不能为空");
    }
    final result = await _rds.getBkWageIndex(workNoteId);
    return result.map((result) => transform(result));
  }

  BusinessGetBkWageIndexBizModel transform(
      BusinessGetBkWageIndexNetModel? netModel) {
    if (netModel == null) {
      return BusinessGetBkWageIndexBizModel();
    }
    return netModel.transform();
  }

  ///班组结算 确认记账
  Future<RespResult<dynamic>> addMyself(
      GroupSettlementParamModel params) async {
    if (params.work_note.isEmpty) {
      return RespFail.buildProcessFail("参数不能为空");
    }
    if (params.bookkeeping_source <= 0.0) {
      return RespFail.buildProcessFail("参数不正确");
    }
    if (params.source_fix_id <= 0.0) {
      return RespFail.buildProcessFail("参数不正确");
    }
    final result = await _rds.addMyself(params);
    return result.map((result) => result);
  }
}
