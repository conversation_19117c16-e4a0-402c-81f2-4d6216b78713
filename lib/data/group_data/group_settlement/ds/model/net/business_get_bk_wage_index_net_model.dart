import 'dart:convert';

import 'package:gdjg_pure_flutter/data/group_data/group_settlement/repo/model/business_get_bk_wage_index_biz_model.dart';

class BusinessGetBkWageIndexNetModel {
  List<BusinessGetBkWageIndexANetModel>? data;
  String? all;
  String? bookkeeping_source;
  String? source_fix_id;
  String? bookkeeping_type;
  String? first_bookkeeping_day;

  BusinessGetBkWageIndexNetModel();

  Map<String, dynamic> toJson(BusinessGetBkWageIndexNetModel instance) => <String, dynamic>{
      "data": instance.data,
      "all": instance.all,
      "bookkeeping_source": instance.bookkeeping_source,
      "source_fix_id": instance.source_fix_id,
      "bookkeeping_type": instance.bookkeeping_type,
      "first_bookkeeping_day": instance.first_bookkeeping_day,
    };

  factory BusinessGetBkWageIndexNetModel.fromJson(Map<String, dynamic> json) {
    var netModel = BusinessGetBkWageIndexNetModel();
    netModel.data = (json["data"] as List<dynamic>?)
      ?.map((e) => BusinessGetBkWageIndexANetModel.fromJson(e as Map<String, dynamic>))
      .toList();
    netModel.all = json["all"]?.toString();
    netModel.bookkeeping_source = json["bookkeeping_source"]?.toString();
    netModel.source_fix_id = json["source_fix_id"]?.toString();
    netModel.bookkeeping_type = json["bookkeeping_type"]?.toString();
    netModel.first_bookkeeping_day = json["first_bookkeeping_day"]?.toString();
    return netModel;
  }

  BusinessGetBkWageIndexBizModel transform() {
    return BusinessGetBkWageIndexBizModel(
      bookkeepingSource: double.parse(bookkeeping_source ?? "0.0"),
      sourceFixId: double.parse(source_fix_id ?? "0.0"),
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class BusinessGetBkWageIndexANetModel {
  String? worker_id;
  String? worker_name;
  BusinessGetBkWageIndexBNetModel? data;

  BusinessGetBkWageIndexANetModel();

  Map<String, dynamic> toJson(BusinessGetBkWageIndexANetModel instance) => <String, dynamic>{
      "worker_id": instance.worker_id,
      "worker_name": instance.worker_name,
      "data": instance.data,
    };

  factory BusinessGetBkWageIndexANetModel.fromJson(Map<String, dynamic> json) {
    var netModel = BusinessGetBkWageIndexANetModel();
    netModel.worker_id = json["worker_id"]?.toString();
    netModel.worker_name = json["worker_name"]?.toString();
    netModel.data = json["data"] == null
      ? null
      : BusinessGetBkWageIndexBNetModel.fromJson(json["data"] as Map<String, dynamic>);
    return netModel;
  }

  BusinessGetBkWageIndexABizModel transform() {
    return BusinessGetBkWageIndexABizModel(
      workerId: double.parse(worker_id ?? "0.0"),
      workerName: worker_name ?? "",
      data: data?.transform(),
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class BusinessGetBkWageIndexBNetModel {
  BorrowNetModel? borrow;
  WageNetModel? wage;
  String? all;

  BusinessGetBkWageIndexBNetModel();

  Map<String, dynamic> toJson(BusinessGetBkWageIndexBNetModel instance) => <String, dynamic>{
      "borrow": instance.borrow,
      "wage": instance.wage,
      "all": instance.all,
    };

  factory BusinessGetBkWageIndexBNetModel.fromJson(Map<String, dynamic> json) {
    var netModel = BusinessGetBkWageIndexBNetModel();
    netModel.borrow = json["borrow"] == null
      ? null
      : BorrowNetModel.fromJson(json["borrow"] as Map<String, dynamic>);
    netModel.wage = json["wage"] == null
      ? null
      : WageNetModel.fromJson(json["wage"] as Map<String, dynamic>);
    netModel.all = json["all"]?.toString();
    return netModel;
  }

  BusinessGetBkWageIndexBBizModel transform() {
    return BusinessGetBkWageIndexBBizModel(
      borrow: borrow?.transform(),
      wage: wage?.transform(),
      all: all ?? "",
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class BorrowNetModel {
  String? money;
  String? count;
  String? business_type;
  dynamic? bookkeeping_type;

  BorrowNetModel();

  Map<String, dynamic> toJson(BorrowNetModel instance) => <String, dynamic>{
      "money": instance.money,
      "count": instance.count,
      "business_type": instance.business_type,
      "bookkeeping_type": instance.bookkeeping_type,
    };

  factory BorrowNetModel.fromJson(Map<String, dynamic> json) {
    var netModel = BorrowNetModel();
    netModel.money = json["money"]?.toString();
    netModel.count = json["count"]?.toString();
    netModel.business_type = json["business_type"]?.toString();
    netModel.bookkeeping_type = json["bookkeeping_type"]?.toString();
    return netModel;
  }

  BorrowBizModel transform() {
    return BorrowBizModel(
      money: money ?? "",
      count: double.parse(count ?? "0.0"),
      businessType: double.parse(business_type ?? "0.0"),
      bookkeepingType: bookkeeping_type ?? "",
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class WageNetModel {
  String? money;
  String? count;
  String? business_type;
  String? bookkeeping_type;

  WageNetModel();

  Map<String, dynamic> toJson(WageNetModel instance) => <String, dynamic>{
      "money": instance.money,
      "count": instance.count,
      "business_type": instance.business_type,
      "bookkeeping_type": instance.bookkeeping_type,
    };

  factory WageNetModel.fromJson(Map<String, dynamic> json) {
    var netModel = WageNetModel();
    netModel.money = json["money"]?.toString();
    netModel.count = json["count"]?.toString();
    netModel.business_type = json["business_type"]?.toString();
    netModel.bookkeeping_type = json["bookkeeping_type"]?.toString();
    return netModel;
  }

  WageBizModel transform() {
    return WageBizModel(
      money: money ?? "",
      count: double.parse(count ?? "0.0"),
      businessType: double.parse(business_type ?? "0.0"),
      bookkeepingType: double.parse(bookkeeping_type ?? "0.0"),
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

