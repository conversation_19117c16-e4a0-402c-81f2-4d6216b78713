
class GroupSettlementParamModel {
  final String work_note;
  final String worker_id;
  final double bookkeeping_source;
  final String business_type;
  final String img_url;
  final String money;
  final double source_fix_id;
  final String business_time;
  final String identity;
  final String user_choose_spotwork;

  GroupSettlementParamModel({
     this.work_note = '',
     this.worker_id = '',
     this.bookkeeping_source= 0.0,
     this.business_type= '9',
     this.img_url= '',
     this.money= '',
     this.source_fix_id= 0.0,
     this.business_time= '',
     this.identity= '1',
     this.user_choose_spotwork= '0',
  });

  Map<String, Object> toMap() {
    return {
      "work_note": work_note,
      "worker_id": worker_id,
      "bookkeeping_source": bookkeeping_source,
      "business_type": business_type,
      "img_url": img_url,
      "money": money,
      "source_fix_id": source_fix_id,
      "business_time": business_time,
      "identity": identity,
      "user_choose_spotwork":user_choose_spotwork,
    };
  }
}