import 'package:gdjg_pure_flutter/data/group_data/group_settlement/ds/model/net/business_get_bk_wage_index_net_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_settlement/ds/model/param/group_settlement_param_model.dart';
import 'package:net_plugin/pigeon/api_generated/net_channel.g.dart';
import 'package:net_plugin/pigeon/net_result/resp_result.dart';
import 'package:net_plugin/pigeon/net_service/net_core.dart';

class GroupSettlementRds {
  ///班组结算 获取结算信息
  Future<RespResult<BusinessGetBkWageIndexNetModel>> getBkWageIndex(String workNoteId) async {
    return await NetCore.requestJGPHP(
        BaseBizRequestEntity(
            url: '/api/business/get-bk-wage-index',
            method: HTTP_METHOD.GET,
            content: {'work_note': workNoteId},
            requestExtra: RequestExtra(showLoading: true)),
        (json) => BusinessGetBkWageIndexNetModel.fromJson(json));
  }

  ///班组结算 确认记账
  Future<RespResult<dynamic>> addMyself(
      GroupSettlementParamModel params) async {
    return await NetCore.requestJGPHP(
        BaseBizRequestEntity(
            url: '/api/business/add-myself',
            method: HTTP_METHOD.POST,
            content: params.toMap(),
            requestExtra: RequestExtra(showLoading: true)),
        (json) => json);
  }
}
