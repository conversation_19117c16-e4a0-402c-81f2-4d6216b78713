import 'package:gdjg_pure_flutter/data/group_data/group_construction_log/ds/group_construction_log_rds.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_construction_log/ds/model/param/report_list_param_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_construction_log/repo/model/report_list_biz_model.dart';
import 'package:net_plugin/pigeon/net_result/resp_result.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_construction_log/repo/model/logs_getdetail_biz_model.dart';

/// 施工日志仓库
class GroupConstructionLogRepo {
  final _groupConstructionLogRds = GroupConstructionLogRds();

  /// 获取施工日志列表
  Future<RespResult<ReportNetModelBizModel>> getReportList({
    String page = "1",
    String startTime = "",
    String endTime = "",
    String keyword = "",
    String deptId = "",
  }) async {
    final params = ReportNetModelParamModel(
      page: page,
      per_page: "20",
      start_time: startTime,
      end_time: endTime,
      keyword: keyword,
      dept_id: deptId,
    );

    final result = await _groupConstructionLogRds.getReportList(params);
    return result.map((netModel) => netModel?.transform() ?? ReportNetModelBizModel());
  }

  /// 获取施工日志详情
  Future<RespResult<LogsGetdetailBizModel>> getLogDetail(double logId) async {
    final result = await _groupConstructionLogRds.getLogDetail(logId);
    return result.map((netModel) => netModel?.transform() ?? LogsGetdetailBizModel());
  }

  /// 删除施工日志
  Future<RespResult<bool>> deleteLog(double logId) async {
    final result = await _groupConstructionLogRds.deleteLog(logId);
    return result;
  }
}
