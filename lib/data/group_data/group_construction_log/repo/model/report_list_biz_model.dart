class ReportNetModelBizModel {

  /// 分页数据
  PaginationBizModel? pagination;

  ///
  List<ReportNetModelABizModel> list;

  /// 部门名称
  String deptName;

  ReportNetModelBizModel({
    this.pagination,
    this.list = const [],
    this.deptName = "",
  });
}

class PaginationBizModel {

  /// 总条数
  double total;

  /// 当前页
  double page;

  /// 每页显示条数
  double pageSize;

  /// 最后一页
  double lastPage;

  PaginationBizModel({
    this.total = 0.0,
    this.page = 0.0,
    this.pageSize = 0.0,
    this.lastPage = 0.0,
  });
}

class ReportNetModelABizModel {

  /// id
  double id;

  /// 上午天气
  String dayweather;

  /// 下午天气
  String nightweather;

  /// 上午温度
  String daytemp;

  /// 下午温度
  String nighttemp;

  /// 内容
  String contents;

  /// 时间
  double editTime;

  ///
  double memberId;

  /// 企业用户id
  double userId;

  /// 是否是代班创建
  double isAgent;

  ///
  List<ImgBizModel> imgs;

  ///
  XtUserBizModel? xtUser;

  ReportNetModelABizModel({
    this.id = 0.0,
    this.dayweather = "",
    this.nightweather = "",
    this.daytemp = "",
    this.nighttemp = "",
    this.contents = "",
    this.editTime = 0.0,
    this.memberId = 0.0,
    this.userId = 0.0,
    this.isAgent = 0.0,
    this.imgs = const [],
    this.xtUser,
  });
}



class ImgBizModel {

  ///
  double id;

  /// 日志id
  double logId;

  /// 图片地址
  String imgUrl;

  ///
  double deleteTime;

  ImgBizModel({
    this.id = 0.0,
    this.logId = 0.0,
    this.imgUrl = "",
    this.deleteTime = 0.0,
  });
}

class XtUserBizModel {

  /// 用户id
  double userId;

  /// 用户名
  String name;

  XtUserBizModel({
    this.userId = 0.0,
    this.name = "",
  });
}
