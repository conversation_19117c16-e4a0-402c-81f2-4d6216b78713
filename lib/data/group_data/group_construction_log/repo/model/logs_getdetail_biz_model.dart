class LogsGetdetailBizModel {
  double id;
  double memberId;
  double deptId;
  String source;
  String version;
  String dayweather;
  String nightweather;
  String daytemp;
  String nighttemp;
  String contents;
  double createTime;
  double editTime;
  double deleteTime;
  double userId;
  double isAgent;
  double recorderUserId;
  double constructionPeoNum;
  List<String> imgs;
  dynamic username;

  LogsGetdetailBizModel({
    this.id = 0.0,
    this.memberId = 0.0,
    this.deptId = 0.0,
    this.source = "",
    this.version = "",
    this.dayweather = "",
    this.nightweather = "",
    this.daytemp = "",
    this.nighttemp = "",
    this.contents = "",
    this.createTime = 0.0,
    this.editTime = 0.0,
    this.deleteTime = 0.0,
    this.userId = 0.0,
    this.isAgent = 0.0,
    this.recorderUserId = 0.0,
    this.constructionPeoNum = 0.0,
    this.imgs = const [],
    this.username = "",
  });
}

