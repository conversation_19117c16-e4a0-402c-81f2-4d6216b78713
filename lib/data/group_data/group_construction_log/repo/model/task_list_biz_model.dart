class TaskListBizModel {

  /// 列表信息
  List<TaskListABizModel> list;

  /// 通用分页信息
  PageBizModel? page;

  TaskListBizModel({
    this.list = const [],
    this.page,
  });
}

class TaskListABizModel {

  /// 任务ID，既task_id
  double id;

  /// 记工本ID
  double workNote;

  /// 显示标题
  String title;

  /// 显示副标题
  String subtitle;

  /// 任务类型 log施工日志
  String processType;

  /// 任务状态 waiting待处理 processing处理中 finished处理完成
  String processStatus;

  /// 任务状态显示文本
  String processStatusText;

  /// 处理中信息
  ProcessDataBizModel? processData;

  /// 处理完成文件信息
  FileDataBizModel? fileData;

  /// 冗余字段
  String createdAt;

  TaskListABizModel({
    this.id = 0.0,
    this.workNote = 0.0,
    this.title = "",
    this.subtitle = "",
    this.processType = "",
    this.processStatus = "",
    this.processStatusText = "",
    this.processData,
    this.fileData,
    this.createdAt = "",
  });
}

class ProcessDataBizModel {

  /// 显示预估耗时
  String estimate;

  /// 显示总计耗时
  String cost;

  /// 显示当前进度百分比 0-100
  double percent;

  ProcessDataBizModel({
    this.estimate = "",
    this.cost = "",
    this.percent = 0.0,
  });
}

class FileDataBizModel {

  /// 显示文件大小
  String size;

  /// 显示文件扩展名
  String extension;

  /// 显示文件名
  String name;

  /// 下载链接
  String url;

  FileDataBizModel({
    this.size = "",
    this.extension = "",
    this.name = "",
    this.url = "",
  });
}

class PageBizModel {
  double curPage;
  double pageSize;
  bool hasMore;

  PageBizModel({
    this.curPage = 0.0,
    this.pageSize = 0.0,
    this.hasMore = false,
  });
}

/// TaskListABizModel扩展方法
extension TaskListABizModelExtension on TaskListABizModel {
  /// 判断任务是否未完成（等待中或处理中）
  bool get isUnfinished => processStatus == 'waiting' || processStatus == 'processing';
}
