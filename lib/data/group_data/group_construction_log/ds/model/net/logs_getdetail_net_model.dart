import 'package:gdjg_pure_flutter/data/group_data/group_construction_log/repo/model/logs_getdetail_biz_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_construction_log/ds/model/net/report_list_net_model.dart';

class LogsGetdetailNetModel {
  double? id;
  double? member_id;
  double? dept_id;
  String? source;
  String? version;
  String? dayweather;
  String? nightweather;
  String? daytemp;
  String? nighttemp;
  String? contents;
  double? create_time;
  double? edit_time;
  double? delete_time;
  double? user_id;
  double? is_agent;
  double? recorder_user_id;
  double? construction_peo_num;
  List<ImgNetModel>? imgs;
  dynamic? username;

  LogsGetdetailNetModel();

  Map<String, dynamic> toJson(LogsGetdetailNetModel instance) {
    var map = <String, Object>{};
    if (id != null) map["id"] = id!;
    if (member_id != null) map["member_id"] = member_id!;
    if (dept_id != null) map["dept_id"] = dept_id!;
    if (source != null) map["source"] = source!;
    if (version != null) map["version"] = version!;
    if (dayweather != null) map["dayweather"] = dayweather!;
    if (nightweather != null) map["nightweather"] = nightweather!;
    if (daytemp != null) map["daytemp"] = daytemp!;
    if (nighttemp != null) map["nighttemp"] = nighttemp!;
    if (contents != null) map["contents"] = contents!;
    if (create_time != null) map["create_time"] = create_time!;
    if (edit_time != null) map["edit_time"] = edit_time!;
    if (delete_time != null) map["delete_time"] = delete_time!;
    if (user_id != null) map["user_id"] = user_id!;
    if (is_agent != null) map["is_agent"] = is_agent!;
    if (recorder_user_id != null) map["recorder_user_id"] = recorder_user_id!;
    if (construction_peo_num != null) map["construction_peo_num"] = construction_peo_num!;
    if (imgs != null) map["imgs"] = imgs!;
    if (username != null) map["username"] = username!;
    return map;
  }

  factory LogsGetdetailNetModel.fromJson(Map<String, dynamic> json) {
    var netModel = LogsGetdetailNetModel();
    netModel.id = double.tryParse(json["id"].toString());
    netModel.member_id = double.tryParse(json["member_id"].toString());
    netModel.dept_id = double.tryParse(json["dept_id"].toString());
    netModel.source = json["source"]?.toString();
    netModel.version = json["version"]?.toString();
    netModel.dayweather = json["dayweather"]?.toString();
    netModel.nightweather = json["nightweather"]?.toString();
    netModel.daytemp = json["daytemp"]?.toString();
    netModel.nighttemp = json["nighttemp"]?.toString();
    netModel.contents = json["contents"]?.toString();
    netModel.create_time = double.tryParse(json["create_time"].toString());
    netModel.edit_time = double.tryParse(json["edit_time"].toString());
    netModel.delete_time = double.tryParse(json["delete_time"].toString());
    netModel.user_id = double.tryParse(json["user_id"].toString());
    netModel.is_agent = double.tryParse(json["is_agent"].toString());
    netModel.recorder_user_id = double.tryParse(json["recorder_user_id"].toString());
    netModel.construction_peo_num = double.tryParse(json["construction_peo_num"].toString());
    netModel.imgs = (json["imgs"] as List<dynamic>?)
        ?.map((e) => ImgNetModel.fromJson(e as Map<String, dynamic>))
        .toList();
    netModel.username = json["username"]?.toString();
    return netModel;
  }

  LogsGetdetailBizModel transform() {
    return LogsGetdetailBizModel(
      id: id ?? 0.0,
      memberId: member_id ?? 0.0,
      deptId: dept_id ?? 0.0,
      source: source ?? "",
      version: version ?? "",
      dayweather: dayweather ?? "",
      nightweather: nightweather ?? "",
      daytemp: daytemp ?? "",
      nighttemp: nighttemp ?? "",
      contents: contents ?? "",
      createTime: create_time ?? 0.0,
      editTime: edit_time ?? 0.0,
      deleteTime: delete_time ?? 0.0,
      userId: user_id ?? 0.0,
      isAgent: is_agent ?? 0.0,
      recorderUserId: recorder_user_id ?? 0.0,
      constructionPeoNum: construction_peo_num ?? 0.0,
      imgs: imgs?.map((e) => e.base_url ?? "").toList() ?? [],
      username: username ?? "",
    );
  }
}

