import 'package:gdjg_pure_flutter/data/group_data/group_construction_log/repo/model/report_list_biz_model.dart';

class ReportNetModelNetModel {

  /// 分页数据
  PaginationNetModel? pagination;

  ///
  List<ReportNetModelANetModel>? list;

  /// 部门名称
  String? dept_name;

  ReportNetModelNetModel();

  Map<String, dynamic> toJson(ReportNetModelNetModel instance) {
    var map = <String, Object>{};
    if (pagination != null) map["pagination"] = pagination!;
    if (list != null) map["list"] = list!;
    if (dept_name != null) map["dept_name"] = dept_name!;
    return map;
  }

  factory ReportNetModelNetModel.fromJson(Map<String, dynamic> json) {
    var netModel = ReportNetModelNetModel();
    netModel.pagination = json["pagination"] == null
      ? null
      : PaginationNetModel.fromJson(json["pagination"] as Map<String, dynamic>);
    netModel.list = (json["list"] as List<dynamic>?)
        ?.map((e) => ReportNetModelANetModel.fromJson(e as Map<String, dynamic>))
        .toList();
    netModel.dept_name = json["dept_name"]?.toString();
    return netModel;
  }

  ReportNetModelBizModel transform() {
    return ReportNetModelBizModel(
      pagination: pagination?.transform(),
      list: list?.map((e) => e.transform()).toList() ?? [],
      deptName: dept_name ?? "",
    );
  }
}

class PaginationNetModel {

  /// 总条数
  double? total;

  /// 当前页
  double? page;

  /// 每页显示条数
  double? page_size;

  /// 最后一页
  double? last_page;

  PaginationNetModel();

  Map<String, dynamic> toJson(PaginationNetModel instance) {
    var map = <String, Object>{};
    if (total != null) map["total"] = total!;
    if (page != null) map["page"] = page!;
    if (page_size != null) map["page_size"] = page_size!;
    if (last_page != null) map["last_page"] = last_page!;
    return map;
  }

  factory PaginationNetModel.fromJson(Map<String, dynamic> json) {
    var netModel = PaginationNetModel();
    netModel.total = double.tryParse(json["total"].toString());
    netModel.page = double.tryParse(json["page"].toString());
    netModel.page_size = double.tryParse(json["page_size"].toString());
    netModel.last_page = double.tryParse(json["last_page"].toString());
    return netModel;
  }

  PaginationBizModel transform() {
    return PaginationBizModel(
      total: total ?? 0.0,
      page: page ?? 0.0,
      pageSize: page_size ?? 0.0,
      lastPage: last_page ?? 0.0,
    );
  }
}

class ReportNetModelANetModel {

  /// id
  double? id;

  /// 上午天气
  String? dayweather;

  /// 下午天气
  String? nightweather;

  /// 上午温度
  String? daytemp;

  /// 下午温度
  String? nighttemp;

  /// 内容
  String? contents;

  /// 时间
  double? edit_time;

  ///
  double? member_id;

  /// 企业用户id
  double? user_id;

  /// 是否是代班创建
  double? is_agent;

  ///
  List<ImgNetModel>? imgs;

  ///
  XtUserNetModel? xt_user;

  ReportNetModelANetModel();

  Map<String, dynamic> toJson(ReportNetModelANetModel instance) {
    var map = <String, Object>{};
    if (id != null) map["id"] = id!;
    if (dayweather != null) map["dayweather"] = dayweather!;
    if (nightweather != null) map["nightweather"] = nightweather!;
    if (daytemp != null) map["daytemp"] = daytemp!;
    if (nighttemp != null) map["nighttemp"] = nighttemp!;
    if (contents != null) map["contents"] = contents!;
    if (edit_time != null) map["edit_time"] = edit_time!;
    if (member_id != null) map["member_id"] = member_id!;
    if (user_id != null) map["user_id"] = user_id!;
    if (is_agent != null) map["is_agent"] = is_agent!;
    if (imgs != null) map["imgs"] = imgs!;
    if (xt_user != null) map["xt_user"] = xt_user!;
    return map;
  }

  factory ReportNetModelANetModel.fromJson(Map<String, dynamic> json) {
    var netModel = ReportNetModelANetModel();
    netModel.id = double.tryParse(json["id"].toString());
    netModel.dayweather = json["dayweather"]?.toString();
    netModel.nightweather = json["nightweather"]?.toString();
    netModel.daytemp = json["daytemp"]?.toString();
    netModel.nighttemp = json["nighttemp"]?.toString();
    netModel.contents = json["contents"]?.toString();
    netModel.edit_time = double.tryParse(json["edit_time"].toString());
    netModel.member_id = double.tryParse(json["member_id"].toString());
    netModel.user_id = double.tryParse(json["user_id"].toString());
    netModel.is_agent = double.tryParse(json["is_agent"].toString());
    netModel.imgs = (json["imgs"] as List<dynamic>?)
        ?.map((e) => ImgNetModel.fromJson(e as Map<String, dynamic>))
        .toList();
    netModel.xt_user = json["xt_user"] == null
      ? null
      : XtUserNetModel.fromJson(json["xt_user"] as Map<String, dynamic>);
    return netModel;
  }

  ReportNetModelABizModel transform() {
    return ReportNetModelABizModel(
      id: id ?? 0.0,
      dayweather: dayweather ?? "",
      nightweather: nightweather ?? "",
      daytemp: daytemp ?? "",
      nighttemp: nighttemp ?? "",
      contents: contents ?? "",
      editTime: edit_time ?? 0.0,
      memberId: member_id ?? 0.0,
      userId: user_id ?? 0.0,
      isAgent: is_agent ?? 0.0,
      imgs: imgs?.map((e) => e.transform()).toList() ?? [],
      xtUser: xt_user?.transform(),
    );
  }
}



class ImgNetModel {

  ///
  double? id;

  /// 日志id
  double? log_id;

  /// 图片地址
  String? img_url;

  /// 图片完整地址
  String? base_url;

  ///
  double? delete_time;

  ImgNetModel();

  Map<String, dynamic> toJson(ImgNetModel instance) {
    var map = <String, Object>{};
    if (id != null) map["id"] = id!;
    if (log_id != null) map["log_id"] = log_id!;
    if (img_url != null) map["img_url"] = img_url!;
    if (base_url != null) map["base_url"] = base_url!;
    if (delete_time != null) map["delete_time"] = delete_time!;
    return map;
  }

  factory ImgNetModel.fromJson(Map<String, dynamic> json) {
    var netModel = ImgNetModel();
    netModel.id = double.tryParse(json["id"].toString());
    netModel.log_id = double.tryParse(json["log_id"].toString());
    netModel.img_url = json["img_url"]?.toString();
    netModel.base_url = json["base_url"]?.toString();
    netModel.delete_time = double.tryParse(json["delete_time"].toString());
    return netModel;
  }

  ImgBizModel transform() {
    return ImgBizModel(
      id: id ?? 0.0,
      logId: log_id ?? 0.0,
      imgUrl: img_url ?? "",
      deleteTime: delete_time ?? 0.0,
    );
  }
}

class XtUserNetModel {

  /// 用户id
  double? user_id;

  /// 用户名
  String? name;

  XtUserNetModel();

  Map<String, dynamic> toJson(XtUserNetModel instance) {
    var map = <String, Object>{};
    if (user_id != null) map["user_id"] = user_id!;
    if (name != null) map["name"] = name!;
    return map;
  }

  factory XtUserNetModel.fromJson(Map<String, dynamic> json) {
    var netModel = XtUserNetModel();
    netModel.user_id = double.tryParse(json["user_id"].toString());
    netModel.name = json["name"]?.toString();
    return netModel;
  }

  XtUserBizModel transform() {
    return XtUserBizModel(
      userId: user_id ?? 0.0,
      name: name ?? "",
    );
  }
}
