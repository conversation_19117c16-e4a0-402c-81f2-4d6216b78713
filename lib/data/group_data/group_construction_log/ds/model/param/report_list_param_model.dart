class ReportNetModelParamModel {

  /// 部门id
  final String? dept_id;

  /// 默认1
  final String? page;

  /// 默认10 ，可自己传
  final String? per_page;

  /// 关键词搜索，搜索日志
  final String? keyword;

  /// 根据用户id筛选，如： ,1,2,4,6
  final String? user_ids;

  /// 时间筛选开始
  final String? start_time;

  /// 时间筛选截止
  final String? end_time;

  ReportNetModelParamModel({
    this.dept_id,
    this.page,
    this.per_page,
    this.keyword,
    this.user_ids,
    this.start_time,
    this.end_time,
  });

  Map<String, Object> toMap() {
    var map = <String, Object>{};
    if (dept_id != null) map["dept_id"] = dept_id!;
    if (page != null) map["page"] = page!;
    if (per_page != null) map["per_page"] = per_page!;
    if (keyword != null) map["keyword"] = keyword!;
    if (user_ids != null) map["user_ids"] = user_ids!;
    if (start_time != null) map["start_time"] = start_time!;
    if (end_time != null) map["end_time"] = end_time!;
    return map;
  }
}
