import 'package:gdjg_pure_flutter/data/group_data/group_construction_log/ds/model/net/report_list_net_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_construction_log/ds/model/param/report_list_param_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_construction_log/ds/model/net/logs_getdetail_net_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_construction_log/ds/model/param/logs_getdetail_param_model.dart';
import 'package:net_plugin/pigeon/api_generated/net_channel.g.dart';
import 'package:net_plugin/pigeon/net_result/resp_result.dart';
import 'package:net_plugin/pigeon/net_service/net_core.dart';

/// 施工日志远程数据源
class GroupConstructionLogRds {
  /// 获取施工日志列表
  Future<RespResult<ReportNetModelNetModel>> getReportList(
      ReportNetModelParamModel params) async {
    return await NetCore.requestJGPHP(
        BaseBizRequestEntity(
            url: '/api/v3/report/list',
            method: HTTP_METHOD.GET,
            content: params.toMap()),
        (json) => ReportNetModelNetModel.fromJson(json));
  }

  /// 获取施工日志详情
  Future<RespResult<LogsGetdetailNetModel>> getLogDetail(double logId) async {
    final param = LogsGetdetailParamModel(log_id: logId.toString());
    return await NetCore.requestJGPHP(
        BaseBizRequestEntity(
            url: '/api/logs/getdetail',
            method: HTTP_METHOD.GET,
            content: param.toMap(),
            requestExtra: RequestExtra(showLoading: true)),
        (json) => LogsGetdetailNetModel.fromJson(json));
  }

  /// 删除施工日志
  Future<RespResult<bool>> deleteLog(double logId) async {
    return await NetCore.requestJGPHP(
        BaseBizRequestEntity(
            url: '/api/logs/delete',
            method: HTTP_METHOD.POST,
            content: {'log_id': logId},
            requestExtra: RequestExtra(showLoading: true)),
        (json) => true);
  }
}
