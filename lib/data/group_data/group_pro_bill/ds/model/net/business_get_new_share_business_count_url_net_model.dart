import 'dart:convert';
import 'package:gdjg_pure_flutter/data/group_data/group_pro_bill/repo/model/business_get_new_share_business_count_url_biz_model.dart';

class BusinessGetNewShareBusinessCountUrlNetModel {

  /// 小程序原始id
  String? original_id;

  /// 请求路径
  String? path;

  /// 请求域名
  String? web_url;

  /// 分享code
  String? code;

  /// 小程序地址
  String? mini_path;

  /// 姓名
  String? worker_name;

  /// [4.4]新增 分享标题
  String? title;

  /// [4.4]新增 带参数的完整小程序地址
  String? mini_path_full;

  BusinessGetNewShareBusinessCountUrlNetModel();

  Map<String, dynamic> toJson(BusinessGetNewShareBusinessCountUrlNetModel instance) {
    var map = <String, Object>{};
    if (original_id != null) map["original_id"] = original_id!;
    if (path != null) map["path"] = path!;
    if (web_url != null) map["web_url"] = web_url!;
    if (code != null) map["code"] = code!;
    if (mini_path != null) map["mini_path"] = mini_path!;
    if (worker_name != null) map["worker_name"] = worker_name!;
    if (title != null) map["title"] = title!;
    if (mini_path_full != null) map["mini_path_full"] = mini_path_full!;
    return map;
  }

  factory BusinessGetNewShareBusinessCountUrlNetModel.fromJson(Map<String, dynamic> json) {
    var netModel = BusinessGetNewShareBusinessCountUrlNetModel();
    netModel.original_id = json["original_id"]?.toString();
    netModel.path = json["path"]?.toString();
    netModel.web_url = json["web_url"]?.toString();
    netModel.code = json["code"]?.toString();
    netModel.mini_path = json["mini_path"]?.toString();
    netModel.worker_name = json["worker_name"]?.toString();
    netModel.title = json["title"]?.toString();
    netModel.mini_path_full = json["mini_path_full"]?.toString();
    return netModel;
  }

  BusinessGetNewShareBusinessCountUrlBizModel transform() {
    return BusinessGetNewShareBusinessCountUrlBizModel(
      originalId: original_id ?? "",
      path: path ?? "",
      webUrl: web_url ?? "",
      code: code ?? "",
      miniPath: mini_path ?? "",
      workerName: worker_name ?? "",
      title: title ?? "",
      miniPathFull: mini_path_full ?? "",
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

