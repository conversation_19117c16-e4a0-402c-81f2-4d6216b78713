import 'dart:convert';
import 'package:gdjg_pure_flutter/data/group_data/group_pro_bill/repo/model/group_business_get_group_business_list_biz_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'group_business_get_group_business_list_net_model.g.dart';

@JsonSerializable()
class GroupBusinessGetGroupBusinessListNetModel {
  /// 
  List<GroupBusinessGetGroupBusinessListANetModel>? list;

  GroupBusinessGetGroupBusinessListNetModel();

  factory GroupBusinessGetGroupBusinessListNetModel.fromJson(Map<String, dynamic> json) => _$GroupBusinessGetGroupBusinessListNetModelFromJson(json);

  Map<String, dynamic> toJson() => _$GroupBusinessGetGroupBusinessListNetModelToJson(this);

  GroupBusinessGetGroupBusinessListBizModel transform() {
    return GroupBusinessGetGroupBusinessListBizModel(
      list: list?.map((e) => e.transform()).toList() ?? [],
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class GroupBusinessGetGroupBusinessListANetModel {
  /// 
  String? date;
  /// 
  List<GroupBusinessGetGroupBusinessListBNetModel>? list;

  GroupBusinessGetGroupBusinessListANetModel();

  factory GroupBusinessGetGroupBusinessListANetModel.fromJson(Map<String, dynamic> json) => _$GroupBusinessGetGroupBusinessListANetModelFromJson(json);

  Map<String, dynamic> toJson() => _$GroupBusinessGetGroupBusinessListANetModelToJson(this);

  GroupBusinessGetGroupBusinessListABizModel transform() {
    return GroupBusinessGetGroupBusinessListABizModel(
      date: date ?? "",
      list: list?.map((e) => e.transform()).toList() ?? [],
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class GroupBusinessGetGroupBusinessListBNetModel {
  /// 
  double? has_img;
  /// 
  double? id;
  /// 记工类型 流水类型（1记工天，2记工量，3记短工，4借支, 5支出, 6包工， 7小时工 8收入 9工资发放/收入） 
  double? business_type;
  /// 记录时间
  String? business_time;
  /// 账本
  double? work_note;
  /// 工人id
  double? worker_id;
  /// 工量
  double? unit_work_type;
  /// 工时
  double? work_time;
  /// 
  double? work_time_hour;
  /// 
  double? overtime;
  /// 
  double? overtime_work;
  /// 
  double? morning_work_time;
  /// 
  double? morning_work_time_hour;
  /// 
  double? afternoon_work_time;
  /// 
  double? afternoon_work_time_hour;
  /// 
  double? user_choose_spotwork;
  /// 
  String? unit_num;
  /// 
  String? unit;
  /// 
  String? note;
  /// 
  double? fee_money;
  /// 创建时间
  double? created_time;
  /// 账本名
  String? work_note_name;
  /// 工人名字
  String? worker_name;
  /// 记的钱
  String? money;
  /// 
  String? unit_work_type_name;
  /// 
  String? unit_work_type_unit;
  /// [4.5]新增 手动对工状态 0-未对工 1-对工无成 2-对工有误
  double? confirm;

  double? fee_standard_id;

  GroupBusinessGetGroupBusinessListBNetModel();

  factory GroupBusinessGetGroupBusinessListBNetModel.fromJson(Map<String, dynamic> json) => _$GroupBusinessGetGroupBusinessListBNetModelFromJson(json);

  Map<String, dynamic> toJson() => _$GroupBusinessGetGroupBusinessListBNetModelToJson(this);

  GroupBusinessGetGroupBusinessListBBizModel transform() {
    return GroupBusinessGetGroupBusinessListBBizModel(
      hasImg: has_img ?? 0.0,
      id: id ?? 0.0,
      businessType: business_type ?? 0.0,
      businessTime: business_time ?? "",
      workNote: work_note ?? 0.0,
      workerId: worker_id ?? 0.0,
      unitWorkType: unit_work_type ?? 0.0,
      workTime: work_time ?? 0.0,
      workTimeHour: work_time_hour ?? 0.0,
      overtime: overtime ?? 0.0,
      overtimeWork: overtime_work ?? 0.0,
      morningWorkTime: morning_work_time,
      morningWorkTimeHour: morning_work_time_hour,
      afternoonWorkTime: afternoon_work_time,
      afternoonWorkTimeHour: afternoon_work_time_hour,
      userChooseSpotwork: user_choose_spotwork,
      unitNum: unit_num ?? "",
      unit: unit ?? "",
      note: note ?? "",
      feeMoney: fee_money ?? 0.0,
      createdTime: created_time ?? 0.0,
      workNoteName: work_note_name ?? "",
      workerName: worker_name ?? "",
      money: money ?? "",
      unitWorkTypeName: unit_work_type_name ?? "",
      unitWorkTypeUnit: unit_work_type_unit ?? "",
      confirm: confirm ?? 0.0,
      feeStandardId: fee_standard_id,
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

