// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'group_business_get_group_business_list_net_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

GroupBusinessGetGroupBusinessListNetModel
    _$GroupBusinessGetGroupBusinessListNetModelFromJson(
            Map<String, dynamic> json) =>
        GroupBusinessGetGroupBusinessListNetModel()
          ..list = (json['list'] as List<dynamic>?)
              ?.map((e) => GroupBusinessGetGroupBusinessListANetModel.fromJson(
                  e as Map<String, dynamic>))
              .toList();

Map<String, dynamic> _$GroupBusinessGetGroupBusinessListNetModelToJson(
        GroupBusinessGetGroupBusinessListNetModel instance) =>
    <String, dynamic>{
      'list': instance.list,
    };

GroupBusinessGetGroupBusinessListANetModel
    _$GroupBusinessGetGroupBusinessListANetModelFromJson(
            Map<String, dynamic> json) =>
        GroupBusinessGetGroupBusinessListANetModel()
          ..date = json['date']?.toString()
          ..list = (json['list'] as List<dynamic>?)
              ?.map((e) => GroupBusinessGetGroupBusinessListBNetModel.fromJson(
                  e as Map<String, dynamic>))
              .toList();

Map<String, dynamic> _$GroupBusinessGetGroupBusinessListANetModelToJson(
        GroupBusinessGetGroupBusinessListANetModel instance) =>
    <String, dynamic>{
      'date': instance.date,
      'list': instance.list,
    };

GroupBusinessGetGroupBusinessListBNetModel
    _$GroupBusinessGetGroupBusinessListBNetModelFromJson(
            Map<String, dynamic> json) =>
        GroupBusinessGetGroupBusinessListBNetModel()
          ..has_img = json['has_img']?.toDouble()
          ..id = json['id']?.toDouble()
          ..business_type = json['business_type']?.toDouble()
          ..business_time = json['business_time']?.toString()
          ..work_note = json['work_note']?.toDouble()
          ..worker_id = json['worker_id']?.toDouble()
          ..unit_work_type = json['unit_work_type']?.toDouble()
          ..work_time = json['work_time']?.toDouble()
          ..work_time_hour = json['work_time_hour']?.toDouble()
          ..overtime = json['overtime']?.toDouble()
          ..overtime_work = json['overtime_work']?.toDouble()
          ..morning_work_time = json['morning_work_time']?.toDouble()
          ..morning_work_time_hour = json['morning_work_time_hour']?.toString()
          ..afternoon_work_time = json['afternoon_work_time']?.toDouble()
          ..afternoon_work_time_hour = json['afternoon_work_time_hour']?.toString()
          ..user_choose_spotwork = json['user_choose_spotwork']?.toDouble()
          ..unit_num = json['unit_num']?.toString()
          ..unit = json['unit']?.toString()
          ..note = json['note']?.toString()
          ..fee_money = json['fee_money']?.toDouble()
          ..created_time = json['created_time']?.toDouble()
          ..work_note_name = json['work_note_name']?.toString()
          ..worker_name = json['worker_name']?.toString()
          ..money = json['money']?.toString()
          ..unit_work_type_name = json['unit_work_type_name']?.toString()
          ..unit_work_type_unit = json['unit_work_type_unit']?.toString()
          ..fee_standard_id = json['fee_standard_id']?.toDouble()
          ..confirm = json['confirm']?.toDouble();

Map<String, dynamic> _$GroupBusinessGetGroupBusinessListBNetModelToJson(
        GroupBusinessGetGroupBusinessListBNetModel instance) =>
    <String, dynamic>{
      'has_img': instance.has_img,
      'id': instance.id,
      'business_type': instance.business_type,
      'business_time': instance.business_time,
      'work_note': instance.work_note,
      'worker_id': instance.worker_id,
      'unit_work_type': instance.unit_work_type,
      'work_time': instance.work_time,
      'work_time_hour': instance.work_time_hour,
      'overtime': instance.overtime,
      'overtime_work': instance.overtime_work,
      'morning_work_time': instance.morning_work_time,
      'morning_work_time_hour': instance.morning_work_time_hour,
      'afternoon_work_time': instance.afternoon_work_time,
      'afternoon_work_time_hour': instance.afternoon_work_time_hour,
      'user_choose_spotwork': instance.user_choose_spotwork,
      'unit_num': instance.unit_num,
      'unit': instance.unit,
      'note': instance.note,
      'fee_money': instance.fee_money,
      'created_time': instance.created_time,
      'work_note_name': instance.work_note_name,
      'worker_name': instance.worker_name,
      'money': instance.money,
      'unit_work_type_name': instance.unit_work_type_name,
      'unit_work_type_unit': instance.unit_work_type_unit,
      'fee_standard_id': instance.fee_standard_id,
      'confirm': instance.confirm,
    };
