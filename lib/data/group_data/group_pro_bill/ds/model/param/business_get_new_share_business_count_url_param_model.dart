class BusinessGetNewShareBusinessCountUrlParamModel {

  /// 分享记工开始时间
  final String? start_business_time;

  /// 分享记工结束时间
  final String? end_business_time;

  /// 记工本id，多个用逗号隔开（全部可以不传）
  final String? work_note;

  /// 记工类型。多个用逗号隔开
  final String? business_type;

  /// 工人id，多个用逗号隔开
  final String? worker_id;

  /// 分享途径 0 默认，1班组流水统计页，2班组个人流水统计页，3个人流水统计页
  final String? share_channel;

  /// 分享形式 1下载，2图片 3微信对账
  final String? share_type;

  /// 身份 1班组 2工人
  final String? identity;

  /// 是否显示钱：1展示 2不展示
  final String? is_show;

  /// 是否工人确认 0-否 1-是 默认否
  final String? confirm;

   BusinessGetNewShareBusinessCountUrlParamModel({
    this.start_business_time,
    this.end_business_time,
    this.work_note,
    this.business_type,
    this.worker_id,
    this.share_channel,
    this.share_type,
    this.identity,
    this.is_show,
    this.confirm,
  });

  Map<String, dynamic> toMap() {
    var map = <String, Object>{};
      if (start_business_time != null) map["start_business_time"] = start_business_time!;
      if (end_business_time != null) map["end_business_time"] = end_business_time!;
      if (work_note != null) map["work_note"] = work_note!;
      if (business_type != null) map["business_type"] = business_type!;
      if (worker_id != null) map["worker_id"] = worker_id!;
      if (share_channel != null) map["share_channel"] = share_channel!;
      if (share_type != null) map["share_type"] = share_type!;
      if (identity != null) map["identity"] = identity!;
      if (is_show != null) map["is_show"] = is_show!;
      if (confirm != null) map["confirm"] = confirm!;
    return map;
  }
}

