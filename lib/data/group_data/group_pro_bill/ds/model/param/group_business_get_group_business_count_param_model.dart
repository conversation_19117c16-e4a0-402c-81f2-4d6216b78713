class GroupBusinessGetGroupBusinessCountParamModel {

  /// 记工本id, 默认全部记工本
   String work_note;

  /// 有默认值，默认为当前月 
   String start_time;

  /// 有默认值，默认为当前月 
   String end_time;

  /// 记工类型 多选, 隔开 
   String? business_type;

  ///
   String? worker_ids;

  /// 点工统计展示  1 上班工天，加班小时 2 上班加班 工天 3 上班加班小时
   String show_type;

   GroupBusinessGetGroupBusinessCountParamModel({
     this.worker_ids = "",
     this.start_time = "",
     this.end_time = "",
     this.business_type = "",
     this.work_note = "",
     this.show_type = "1",
  });

   toMap() {
     return {
       "worker_ids": worker_ids ?? "",
       "start_time": start_time,
       "end_time": end_time,
       "business_type": business_type ?? "",
       "work_note": work_note,
       "show_type": show_type,
     };
   }
}

