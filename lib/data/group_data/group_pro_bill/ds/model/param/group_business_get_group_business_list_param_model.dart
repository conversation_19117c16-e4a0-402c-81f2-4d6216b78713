class GroupBusinessGetGroupBusinessListParamModel {

  /// 有默认值，默认为当前月 
   String start_time;

  /// 有默认值，默认为当前月 
   String end_time;

  /// 记工类型 多选, 隔开
  String? business_type;

  ///
  String? worker_ids;

  /// 记工本id, 默认全部记工本 
   String work_note;

  /// 分页字段，一页多少条，不传为不分页 
   int limit;

  /// 页数，1页，2页。配合limit 使用 
   int page;

  /// 查询指定日期 多日期用,隔开 值为空则忽略该参数 值不为空则忽略start_time及end_time
   String? business_times;

   GroupBusinessGetGroupBusinessListParamModel({
     this.start_time = "",
     this.end_time = "",
     this.business_type = "",
     this.work_note = "",
     this.limit = 20,
     this.page = 1,
     this.business_times = "",
     this.worker_ids = "",
  });

   toMap() {
     return {
       "start_time": start_time,
       "end_time": end_time,
       "business_type": business_type ?? "",
       "work_note": work_note,
       "limit": limit,
       "page": page,
       "business_times": business_times ?? "",
       "worker_ids": worker_ids ?? "",
     };
   }
}

