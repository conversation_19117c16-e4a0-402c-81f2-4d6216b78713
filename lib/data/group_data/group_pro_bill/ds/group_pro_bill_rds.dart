import 'package:gdjg_pure_flutter/data/common_data/net/count_net_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_pro_bill/ds/model/net/business_get_new_share_business_count_url_net_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_pro_bill/ds/model/net/group_business_get_group_business_list_net_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_pro_bill/ds/model/param/business_get_new_share_business_count_url_param_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_pro_bill/ds/model/param/group_business_get_group_business_count_param_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_pro_bill/ds/model/param/group_business_get_group_business_list_param_model.dart';
import 'package:net_plugin/pigeon/api_generated/net_channel.g.dart';
import 'package:net_plugin/pigeon/net_result/resp_result.dart';
import 'package:net_plugin/pigeon/net_service/net_core.dart';

class GroupProBillRds {
  ///班组统计
  Future<RespResult<CountNetModel>> getGroupBusinessCount(
      GroupBusinessGetGroupBusinessCountParamModel params) async {
    return await NetCore.requestJGPHP(
        BaseBizRequestEntity(
            url: '/api/v3/group-business/get-group-business-count',
            method: HTTP_METHOD.GET,
            content: params.toMap(),
            requestExtra: RequestExtra(showLoading: true)),
        (json) => CountNetModel.fromJson(json));
  }

  ///班组项目流水
  Future<RespResult<GroupBusinessGetGroupBusinessListNetModel>>
      getGroupBusinessList(
          GroupBusinessGetGroupBusinessListParamModel params) async {
    return await NetCore.requestJGPHP(
        BaseBizRequestEntity(
            url: '/api/v3/group-business/get-group-business-list',
            method: HTTP_METHOD.GET,
            content: params.toMap()),
        (json) => GroupBusinessGetGroupBusinessListNetModel.fromJson(json));
  }

  /// 生成图片分享
  Future<RespResult<BusinessGetNewShareBusinessCountUrlNetModel>>
      generateShareImage(
          BusinessGetNewShareBusinessCountUrlParamModel params) async {
    return await NetCore.requestJGPHP(
        BaseBizRequestEntity(
            url: '/api/business/get_new_share_business_count_url',
            method: HTTP_METHOD.GET,
            requestExtra: RequestExtra(showLoading: true, printResp: true),
            content: params.toMap().cast()),
        (json) => BusinessGetNewShareBusinessCountUrlNetModel.fromJson(json));
  }
}
