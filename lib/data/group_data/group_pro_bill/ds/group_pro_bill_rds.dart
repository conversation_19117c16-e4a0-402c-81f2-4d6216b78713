import 'package:gdjg_pure_flutter/data/group_data/group_calendar/ds/model/net/group_calendar_net_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_pro_bill/ds/model/net/group_business_get_group_business_list_net_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_pro_bill/ds/model/param/group_business_get_group_business_count_param_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_pro_bill/ds/model/param/group_business_get_group_business_list_param_model.dart';
import 'package:net_plugin/pigeon/api_generated/net_channel.g.dart';
import 'package:net_plugin/pigeon/net_result/resp_result.dart';
import 'package:net_plugin/pigeon/net_service/net_core.dart';

class GroupProBillRds {
  ///班组统计
  Future<RespResult<CountNetModel>> getGroupBusinessCount(
      GroupBusinessGetGroupBusinessCountParamModel params) async {
    return await NetCore.requestJGPHP(
        BaseBizRequestEntity(
            url: '/api/v3/group-business/get-group-business-count',
            method: HTTP_METHOD.GET,
            content: params.toMap()),
        (json) => CountNetModel.fromJson(json));
  }

  ///班组项目流水
  Future<RespResult<GroupBusinessGetGroupBusinessListNetModel>> getGroupBusinessList(
      GroupBusinessGetGroupBusinessListParamModel params) async {
    return await NetCore.requestJGPHP(
        BaseBizRequestEntity(
            url: '/api/v3/group-business/get-group-business-list',
            method: HTTP_METHOD.GET,
            content: params.toMap()),
        (json) => GroupBusinessGetGroupBusinessListNetModel.fromJson(json));
  }
}
