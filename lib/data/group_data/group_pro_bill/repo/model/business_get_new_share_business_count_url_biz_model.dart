import 'dart:convert';

class BusinessGetNewShareBusinessCountUrlBizModel {

  /// 小程序原始id
  String originalId;

  /// 请求路径
  String path;

  /// 请求域名
  String webUrl;

  /// 分享code
  String code;

  /// 小程序地址
  String miniPath;

  /// 姓名
  String workerName;

  /// [4.4]新增 分享标题
  String title;

  /// [4.4]新增 带参数的完整小程序地址
  String miniPathFull;

  BusinessGetNewShareBusinessCountUrlBizModel({
    this.originalId = "",
    this.path = "",
    this.webUrl = "",
    this.code = "",
    this.miniPath = "",
    this.workerName = "",
    this.title = "",
    this.miniPathFull = "",
  });
  Map<String, dynamic> toJson() {
    return {
      'originalId': originalId,
      'path': path,
      'webUrl': webUrl,
      'code': code,
      'miniPath': miniPath,
      'workerName': workerName,
      'title': title,
      'miniPathFull': miniPathFull,
    };
  }
  @override
  String toString() {
    return jsonEncode(this);
  }
}

