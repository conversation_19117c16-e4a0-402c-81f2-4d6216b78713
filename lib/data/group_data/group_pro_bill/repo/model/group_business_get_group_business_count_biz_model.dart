import 'dart:convert';

class GroupBusinessGetGroupBusinessCountBizModel {
  /// 点工
  final SpotWorkBizModel? spotWork;
  /// 工量
  final UnitBizModel? unit;
  /// 记工钱/短工
  final WorkMoneyBizModel? workMoney;
  /// 借支
  final BorrowBizModel? borrow;
  /// 包工
  final ContractorBizModel? contractor;
  /// 工资
  final WageBizModel? wage;
  /// 
  final HourBizModel? hour;
  /// 
  double unsettled;
  /// 
  double income;

  GroupBusinessGetGroupBusinessCountBizModel({
    this.spotWork,
    this.unit,
    this.workMoney,
    this.borrow,
    this.contractor,
    this.wage,
    this.hour,
    this.unsettled = 0.0,
    this.income = 0.0,
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class SpotWorkBizModel {
  /// 上班工时
  String workTime;
  /// 上班小时
  String workTimeHour;
  /// 加班 小时
  String overtime;
  /// 加班工
  String overtimeWork;
  /// 点工工钱
  double spotWorkFeeMoney;
  /// 数量
  double num;

  SpotWorkBizModel({
    this.workTime = "",
    this.workTimeHour = "",
    this.overtime = "",
    this.overtimeWork = "",
    this.spotWorkFeeMoney = 0.0,
    this.num = 0.0,
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class UnitBizModel {
  /// 
  List<CountUnitBizModel> countUnit;
  /// 
  double num;

  UnitBizModel({
    this.countUnit = const [],
    this.num = 0.0,
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class CountUnitBizModel {
  /// 总量
  String count;
  /// 最后单价
  String lastUnitPrice;
  /// 工程量
  double num;
  /// 
  String unitMoney;
  /// 
  double unitWorkType;
  /// 
  String unitWorkTypeName;
  /// 
  String unitWorkTypeUnit;
  /// 
  String unit;

  CountUnitBizModel({
    this.count = "",
    this.lastUnitPrice = "",
    this.num = 0.0,
    this.unitMoney = "",
    this.unitWorkType = 0.0,
    this.unitWorkTypeName = "",
    this.unitWorkTypeUnit = "",
    this.unit = "",
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class WorkMoneyBizModel {
  /// 
  double workMoney;
  /// 
  double num;

  WorkMoneyBizModel({
    this.workMoney = 0.0,
    this.num = 0.0,
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class BorrowBizModel {
  /// 
  String borrowCount;
  /// 
  double num;

  BorrowBizModel({
    this.borrowCount = "",
    this.num = 0.0,
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class ContractorBizModel {
  /// 
  double contractorWorkTime;
  /// 
  double contractorWorkTimeHour;
  /// 
  double num;
  /// 
  String contractorOvertime;

  ContractorBizModel({
    this.contractorWorkTime = 0.0,
    this.contractorWorkTimeHour = 0.0,
    this.num = 0.0,
    this.contractorOvertime = "",
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class WageBizModel {
  /// 
  String wageCount;
  /// 
  double num;

  WageBizModel({
    this.wageCount = "",
    this.num = 0.0,
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class HourBizModel {
  /// 小时工
  double hourTime;
  /// 
  double hourOvertime;
  /// 
  double num;

  HourBizModel({
    this.hourTime = 0.0,
    this.hourOvertime = 0.0,
    this.num = 0.0,
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}

