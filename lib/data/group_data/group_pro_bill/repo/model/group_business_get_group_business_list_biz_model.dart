import 'dart:convert';

class GroupBusinessGetGroupBusinessListBizModel {
  /// 
  List<GroupBusinessGetGroupBusinessListABizModel> list;

  GroupBusinessGetGroupBusinessListBizModel({
    this.list = const [],
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class GroupBusinessGetGroupBusinessListABizModel {
  /// 
  String date;
  /// 
  List<GroupBusinessGetGroupBusinessListBBizModel> list;

  GroupBusinessGetGroupBusinessListABizModel({
    this.date = "",
    this.list = const [],
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class GroupBusinessGetGroupBusinessListBBizModel {
  /// 
  double hasImg;
  /// 
  double id;
  /// 记工类型 流水类型（1记工天，2记工量，3记短工，4借支, 5支出, 6包工， 7小时工 8收入 9工资发放/收入） 
  double businessType;
  /// 记录时间
  String businessTime;
  /// 账本
  double workNote;
  /// 工人id
  double workerId;
  /// 工量
  double unitWorkType;
  /// 工时
  double workTime;
  /// 
  double workTimeHour;
  /// 
  double overtime;
  /// 
  double overtimeWork;
  /// 
  double? morningWorkTime;
  /// 
  double? morningWorkTimeHour;
  /// 
  double? afternoonWorkTime;
  /// 
  double? afternoonWorkTimeHour;
  /// 
  double? userChooseSpotwork;
  /// 
  String unitNum;
  /// 
  String unit;
  /// 
  String note;
  /// 
  double feeMoney;
  /// 创建时间
  double createdTime;
  /// 账本名
  String workNoteName;
  /// 工人名字
  String workerName;
  /// 记的钱
  String money;
  /// 
  String unitWorkTypeName;
  /// 
  String unitWorkTypeUnit;
  ///工资规则ID
  double? feeStandardId;
  /// [4.5]新增 手动对工状态 0-未对工 1-对工无成 2-对工有误
  double? confirm;


  GroupBusinessGetGroupBusinessListBBizModel({
    this.hasImg = 0.0,
    this.id = 0.0,
    this.businessType = 0.0,
    this.businessTime = "",
    this.workNote = 0.0,
    this.workerId = 0.0,
    this.unitWorkType = 0.0,
    this.workTime = 0.0,
    this.workTimeHour = 0.0,
    this.overtime = 0.0,
    this.overtimeWork = 0.0,
    this.morningWorkTime,
    this.morningWorkTimeHour,
    this.afternoonWorkTime,
    this.afternoonWorkTimeHour,
    this.userChooseSpotwork,
    this.unitNum = "",
    this.unit = "",
    this.note = "",
    this.feeMoney = 0.0,
    this.createdTime = 0.0,
    this.workNoteName = "",
    this.workerName = "",
    this.money = "",
    this.unitWorkTypeName = "",
    this.unitWorkTypeUnit = "",
    this.confirm,
    this.feeStandardId,
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}

