import 'package:gdjg_pure_flutter/data/common_data/biz/count_biz_model.dart';
import 'package:gdjg_pure_flutter/data/common_data/net/count_net_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_pro_bill/ds/group_pro_bill_rds.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_pro_bill/ds/model/net/group_business_get_group_business_list_net_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_pro_bill/ds/model/param/business_get_new_share_business_count_url_param_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_pro_bill/ds/model/param/group_business_get_group_business_count_param_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_pro_bill/ds/model/param/group_business_get_group_business_list_param_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_pro_bill/repo/model/business_get_new_share_business_count_url_biz_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_pro_bill/repo/model/group_business_get_group_business_list_biz_model.dart';
import 'package:net_plugin/pigeon/net_result/resp_result.dart';

class GroupProBillRepo {
  final _groupProBillRds = GroupProBillRds();

  /// 获取班组流水统计
  Future<RespResult<CountBizModel>> getGroupBusinessCount(
      GroupBusinessGetGroupBusinessCountParamModel param) async {
    final result = await _groupProBillRds.getGroupBusinessCount(param);
    return result.map(_transformCountBizModel);
  }

  CountBizModel _transformCountBizModel(CountNetModel? netModel) {
    return CountBizModel(
      id: netModel?.id ?? 0.0,
      name: netModel?.name ?? "",
      spotWork: netModel?.spot_work?.transform(),
      unit: netModel?.unit?.transform(),
      workMoney: netModel?.work_money?.transform(),
      borrow: netModel?.borrow?.transform(),
      contractor: netModel?.contractor?.transform(),
      wage: netModel?.wage?.transform(),
      hour: netModel?.hour?.transform(),
      unsettled: netModel?.unsettled ?? 0.0,
      income: netModel?.income ?? 0.0,
      num: netModel?.num ?? 0.0,
    );
  }

  /// 获取班组流水列表
  Future<RespResult<GroupBusinessGetGroupBusinessListBizModel>>
      getGroupBusinessList(
          GroupBusinessGetGroupBusinessListParamModel param) async {
    final result = await _groupProBillRds.getGroupBusinessList(param);
    return result.map(_transformBillListBizModel);
  }

  GroupBusinessGetGroupBusinessListBizModel _transformBillListBizModel(
      GroupBusinessGetGroupBusinessListNetModel? netModel) {
    return GroupBusinessGetGroupBusinessListBizModel(
      list: netModel?.list?.map((e) => e.transform()).toList() ?? [],
    );
  }

  /// 生成图片分享
  Future<RespResult<BusinessGetNewShareBusinessCountUrlBizModel?>>
      generateShareImage(
          BusinessGetNewShareBusinessCountUrlParamModel param) async {
    final result = await _groupProBillRds.generateShareImage(param);
    return result.map((result) => result?.transform());
  }
}
