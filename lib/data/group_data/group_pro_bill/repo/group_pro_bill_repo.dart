import 'package:gdjg_pure_flutter/data/group_data/group_calendar/ds/model/net/group_calendar_net_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_calendar/repo/model/group_calendar_biz_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_pro_bill/ds/group_pro_bill_rds.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_pro_bill/ds/model/net/group_business_get_group_business_list_net_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_pro_bill/ds/model/param/group_business_get_group_business_count_param_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_pro_bill/ds/model/param/group_business_get_group_business_list_param_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_pro_bill/repo/model/group_business_get_group_business_list_biz_model.dart';
import 'package:net_plugin/pigeon/net_result/resp_result.dart';

class GroupProBillRepo {
  final _groupProBillRds = GroupProBillRds();

  /// 获取班组流水统计
  Future<RespResult<CountBizModel?>> getGroupBusinessCount(
      GroupBusinessGetGroupBusinessCountParamModel param) async {
    final result = await _groupProBillRds.getGroupBusinessCount(param);
    return result.map(_transform);
  }

  CountBizModel? _transform(CountNetModel? netModel) {
    return netModel?.transform();
  }

  /// 获取班组流水列表
  Future<RespResult<GroupBusinessGetGroupBusinessListBizModel?>>
      getGroupBusinessList(
          GroupBusinessGetGroupBusinessListParamModel param) async {
    final result = await _groupProBillRds.getGroupBusinessList(param);
    return result.map(_transform2);
  }

  GroupBusinessGetGroupBusinessListBizModel? _transform2(
      GroupBusinessGetGroupBusinessListNetModel? netModel) {
    return netModel?.transform();
  }
}
