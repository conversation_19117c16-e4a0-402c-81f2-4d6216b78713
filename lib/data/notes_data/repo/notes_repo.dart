
import 'package:gdjg_pure_flutter/data/notes_data/ds/notes_lds.dart';

class NotesRepo {
  final _lds = NotesLds();

  void setPageSource(String value) {
    _lds.setPageSource(value);
  }

  /// 保存备注到本地
  /// [note] 要保存的备注内容
  /// [pageSource] 页面来源标识
  void saveNote(String note) {
    if (note.trim().isEmpty) return;

    final existingNotes = _lds.getNotesList();

    // 避免重复添加相同的备注
    if (!existingNotes.contains(note.trim())) {
      existingNotes.insert(0, note.trim()); // 新备注插入到最前面

      // 限制历史记录数量，最多保存20条
      if (existingNotes.length > 20) {
        existingNotes.removeRange(20, existingNotes.length);
      }

      _lds.saveNotesList(existingNotes);
    }
  }

  /// 获取历史备注列表
  /// [pageSource] 页面来源标识
  /// 返回该页面来源的历史备注列表
  List<String> getHistoryNotes() {
    return _lds.getNotesList();
  }

  /// 清空指定页面来源的历史备注
  /// [pageSource] 页面来源标识
  void clearHistoryNotes() {
    _lds.saveNotesList(<String>[]);
  }

  /// 删除指定的备注
  /// [note] 要删除的备注内容
  /// [pageSource] 页面来源标识
  void removeHistoryNote(String note) {
    final existingNotes = _lds.getNotesList();
    existingNotes.remove(note);
    _lds.saveNotesList(existingNotes);
  }
}
