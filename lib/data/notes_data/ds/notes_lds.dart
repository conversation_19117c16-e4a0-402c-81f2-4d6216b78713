
import 'dart:convert';
import 'package:gdjg_pure_flutter/utils/store_util/base_lds.dart';

class NotesLds extends BaseLds<String> {
  String _pageSource = "other";

  setPageSource(String source) {
    _pageSource = source;
  }

  @override
  String getBizName() {
    return "note_$_pageSource";
  }

  /// 保存List<String>为JSON字符串
  void saveNotesList(List<String> notes) {
    final jsonString = jsonEncode(notes);
    save(jsonString);
  }

  /// 获取JSON字符串并转换为List<String>
  List<String> getNotesList() {
    final jsonString = get();
    if (jsonString == null || jsonString.isEmpty) {
      return <String>[];
    }

    try {
      final List<dynamic> decoded = jsonDecode(jsonString);
      return decoded.cast<String>();
    } catch (e) {
      // 如果解析失败，返回空列表
      return <String>[];
    }
  }
}
