import 'package:gdjg_pure_flutter/data/utils_data/course/ds/course_rds.dart';
import 'package:gdjg_pure_flutter/data/utils_data/course/repo/model/course_net_model_biz_model.dart';
import 'package:gdjg_pure_flutter/data/utils_data/course/repo/model/course_detail_biz_model.dart';
import 'package:gdjg_pure_flutter/data/utils_data/course/ds/param/course_net_model_param_model.dart';
import 'package:net_plugin/pigeon/net_result/resp_result.dart';

/// 课程数据仓库
class CourseRepo {
  final CourseRds _courseRds = CourseRds();

  /// 获取课程列表
  Future<RespResult<CourseNetModelBizModel>> getCourseList(
    CourseNetModelParamModel param,
  ) async {
    return await _courseRds.getCourseList(param);
  }

  /// 获取工人项目页课程列表
  Future<RespResult<CourseNetModelBizModel>> getWorkerProjectCourseList() async {
    return await _courseRds.getWorkerProjectCourseList();
  }

  /// 获取班组项目页课程列表
  Future<RespResult<CourseNetModelBizModel>> getGroupProjectCourseList() async {
    return await _courseRds.getGroupProjectCourseList();
  }

  /// 获取课程详情
  Future<RespResult<CourseDetailBizModel>> getCourseDetail(String courseId) async {
    return await _courseRds.getCourseDetail(courseId);
  }
}
