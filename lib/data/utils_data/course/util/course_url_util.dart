import 'dart:io';
import 'package:gdjg_pure_flutter/data/account/repo/auth_repo.dart';
import 'package:gdjg_pure_flutter/feature/feedback/util/feedback_config_util.dart';
import 'package:gdjg_pure_flutter/utils/net_util/net_env.dart';

/// 课程URL处理工具类
class CourseUrlUtil {
  static final AuthRepo _authRepo = AuthRepo();
  static final NetEnv _netEnv = NetEnv();

  /// 处理课程URL中的参数替换
  /// 替换 {token} {identity} {source} {uid} {env} {version} 等参数
  static Future<String> processUrl(String originalUrl) async {
    if (originalUrl.isEmpty) {
      return originalUrl;
    }

    String processedUrl = originalUrl;

    try {
      // 获取用户信息
      final account = _authRepo.getAccount();

      // 替换token
      processedUrl = processedUrl.replaceAll('{token}', account.token);

      // 替换identity (用户身份标识)
      processedUrl = processedUrl.replaceAll('{identity}', account.uid);

      // 替换source (来源标识)
      processedUrl = processedUrl.replaceAll('{source}', _getSource());

      // 替换uid
      processedUrl = processedUrl.replaceAll('{uid}', account.uid);

      // 替换env (环境标识)
      processedUrl = processedUrl.replaceAll('{env}', _getEnv());

      // 替换version (应用版本)
      final deviceInfo = await FeedbackConfigUtil.getDeviceInfo();
      final version = deviceInfo['packageversion'] ?? '7.0.0';
      processedUrl = processedUrl.replaceAll('{version}', version);

    } catch (e) {
      // 处理失败，返回原始URL
      return originalUrl;
    }

    return processedUrl;
  }

  /// 获取资源来源标识
  static String _getSource() {
    if (Platform.isAndroid) {
      return "agd"; // 工地记工Android
    } else if (Platform.isIOS) {
      return "agd"; // 工地记工iOS
    }
    return "agd";
  }

  /// 获取环境标识
  static String _getEnv() {
    return _netEnv.isTestEnv() ? "test" : "prod";
  }


}
