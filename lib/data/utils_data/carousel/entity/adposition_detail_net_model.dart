import 'dart:convert';
import 'package:json_annotation/json_annotation.dart';
import 'adposition_detail_biz_model.dart';

part 'adposition_detail_net_model.g.dart';

@JsonSerializable()
class AdpositionDetailNetModel {
  /// 
  List<AdpositionDetailANetModel>? list;

  AdpositionDetailNetModel();

  factory AdpositionDetailNetModel.fromJson(Map<String, dynamic> json) => _$AdpositionDetailNetModelFromJson(json);

  Map<String, dynamic> toJson() => _$AdpositionDetailNetModelToJson(this);

  AdpositionDetailBizModel transform() {
    return AdpositionDetailBizModel(
      list: this.list?.map((e) => e.transform()).toList() ?? [],
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class AdpositionDetailANetModel {
  /// 位置展示图片
  String? img;
  /// 内容标题
  String? content_title;
  /// 内容链接
  String? content_href;
  /// 内容封面
  String? content_cover;
  /// 内容描述
  String? content_desc;
  /// 内容文本
  dynamic? content;
  /// 1根据code字段自行处理 2链接 3分享 4其他 5小程序 6开屏广告
  double? type;
  /// 标识码
  String? code;
  /// 仅type为5时出现
  String? original_id;
  /// 仅type为5时出现
  String? mini_path;
  /// 第三方广告商 仅type为6时出现
  List<AdvertiserNetModel>? advertiser;
  /// 第三方广告商(补量) 仅type为6时出现
  List<AdvertiserSpareNetModel>? advertiser_spare;
  /// 自定义跳过按钮 仅type为6时出现
  double? skip_button;
  /// [5.0.0]小程序mini_appid
  String? mini_appid;
  /// [5.0.0]跳转前弹窗提示文案 为空则不弹窗
  String? alert;
  /// [5.4.3] 扩展数据
  ExtendNetModel? extend;

  AdpositionDetailANetModel();

  factory AdpositionDetailANetModel.fromJson(Map<String, dynamic> json) => _$AdpositionDetailANetModelFromJson(json);

  Map<String, dynamic> toJson() => _$AdpositionDetailANetModelToJson(this);

  AdpositionDetailABizModel transform() {
    return AdpositionDetailABizModel(
      img: this.img ?? "",
      contentTitle: this.content_title ?? "",
      contentHref: this.content_href ?? "",
      contentCover: this.content_cover ?? "",
      contentDesc: this.content_desc ?? "",
      content: this.content ?? "",
      type: this.type ?? 0.0,
      code: this.code ?? "",
      originalId: this.original_id ?? "",
      miniPath: this.mini_path ?? "",
      skipButton: this.skip_button ?? 0.0,
      miniAppid: this.mini_appid ?? "",
      alert: this.alert ?? "",
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class AdvertiserNetModel {

  AdvertiserNetModel();

  factory AdvertiserNetModel.fromJson(Map<String, dynamic> json) => _$AdvertiserNetModelFromJson(json);

  Map<String, dynamic> toJson() => _$AdvertiserNetModelToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class AdvertiserSpareNetModel {

  AdvertiserSpareNetModel();

  factory AdvertiserSpareNetModel.fromJson(Map<String, dynamic> json) => _$AdvertiserSpareNetModelFromJson(json);

  Map<String, dynamic> toJson() => _$AdvertiserSpareNetModelToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class ExtendNetModel {

  ExtendNetModel();

  factory ExtendNetModel.fromJson(Map<String, dynamic> json) => _$ExtendNetModelFromJson(json);

  Map<String, dynamic> toJson() => _$ExtendNetModelToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
