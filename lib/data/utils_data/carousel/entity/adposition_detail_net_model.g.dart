// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'adposition_detail_net_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AdpositionDetailNetModel _$AdpositionDetailNetModelFromJson(
        Map<String, dynamic> json) =>
    AdpositionDetailNetModel()
      ..list = (json['list'] as List<dynamic>?)
          ?.map((e) =>
              AdpositionDetailANetModel.fromJson(e as Map<String, dynamic>))
          .toList();

Map<String, dynamic> _$AdpositionDetailNetModelToJson(
        AdpositionDetailNetModel instance) =>
    <String, dynamic>{
      'list': instance.list,
    };

AdpositionDetailANetModel _$AdpositionDetailANetModelFromJson(
        Map<String, dynamic> json) =>
    AdpositionDetailANetModel()
      ..img = json['img'] as String?
      ..content_title = json['content_title'] as String?
      ..content_href = json['content_href'] as String?
      ..content_cover = json['content_cover'] as String?
      ..content_desc = json['content_desc'] as String?
      ..content = json['content']
      ..type = (json['type'] as num?)?.toDouble()
      ..code = json['code'] as String?
      ..original_id = json['original_id'] as String?
      ..mini_path = json['mini_path'] as String?
      ..advertiser = (json['advertiser'] as List<dynamic>?)
          ?.map((e) => AdvertiserNetModel.fromJson(e as Map<String, dynamic>))
          .toList()
      ..advertiser_spare = (json['advertiser_spare'] as List<dynamic>?)
          ?.map((e) =>
              AdvertiserSpareNetModel.fromJson(e as Map<String, dynamic>))
          .toList()
      ..skip_button = (json['skip_button'] as num?)?.toDouble()
      ..mini_appid = json['mini_appid'] as String?
      ..alert = json['alert'] as String?
      ..extend = json['extend'] == null
          ? null
          : ExtendNetModel.fromJson(json['extend'] as Map<String, dynamic>);

Map<String, dynamic> _$AdpositionDetailANetModelToJson(
        AdpositionDetailANetModel instance) =>
    <String, dynamic>{
      'img': instance.img,
      'content_title': instance.content_title,
      'content_href': instance.content_href,
      'content_cover': instance.content_cover,
      'content_desc': instance.content_desc,
      'content': instance.content,
      'type': instance.type,
      'code': instance.code,
      'original_id': instance.original_id,
      'mini_path': instance.mini_path,
      'advertiser': instance.advertiser,
      'advertiser_spare': instance.advertiser_spare,
      'skip_button': instance.skip_button,
      'mini_appid': instance.mini_appid,
      'alert': instance.alert,
      'extend': instance.extend,
    };

AdvertiserNetModel _$AdvertiserNetModelFromJson(Map<String, dynamic> json) =>
    AdvertiserNetModel();

Map<String, dynamic> _$AdvertiserNetModelToJson(AdvertiserNetModel instance) =>
    <String, dynamic>{};

AdvertiserSpareNetModel _$AdvertiserSpareNetModelFromJson(
        Map<String, dynamic> json) =>
    AdvertiserSpareNetModel();

Map<String, dynamic> _$AdvertiserSpareNetModelToJson(
        AdvertiserSpareNetModel instance) =>
    <String, dynamic>{};

ExtendNetModel _$ExtendNetModelFromJson(Map<String, dynamic> json) =>
    ExtendNetModel();

Map<String, dynamic> _$ExtendNetModelToJson(ExtendNetModel instance) =>
    <String, dynamic>{};
