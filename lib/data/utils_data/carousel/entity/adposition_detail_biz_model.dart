import 'dart:convert';

class AdpositionDetailBizModel {
  /// 
  List<AdpositionDetailABizModel> list;

  AdpositionDetailBizModel({
    this.list = const [],
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class AdpositionDetailABizModel {
  /// 位置展示图片
  String img;
  /// 内容标题
  String contentTitle;
  /// 内容链接
  String contentHref;
  /// 内容封面
  String contentCover;
  /// 内容描述
  String contentDesc;
  /// 内容文本
  dynamic content;
  /// 1根据code字段自行处理 2链接 3分享 4其他 5小程序 6开屏广告
  double type;
  /// 标识码
  String code;
  /// 仅type为5时出现
  String originalId;
  /// 仅type为5时出现
  String miniPath;
  /// 自定义跳过按钮 仅type为6时出现
  double skipButton;
  /// [5.0.0]小程序mini_appid
  String miniAppid;
  /// [5.0.0]跳转前弹窗提示文案 为空则不弹窗
  String alert;

  AdpositionDetailABizModel({
    this.img = "",
    this.contentTitle = "",
    this.contentHref = "",
    this.contentCover = "",
    this.contentDesc = "",
    this.content = "",
    this.type = 0.0,
    this.code = "",
    this.originalId = "",
    this.miniPath = "",
    this.skipButton = 0.0,
    this.miniAppid = "",
    this.alert = "",
  });



  @override
  String toString() {
    return jsonEncode(this);
  }
}


