import 'package:gdjg_pure_flutter/data/utils_data/carousel/ds/carousel_rds.dart';
import 'package:gdjg_pure_flutter/data/utils_data/carousel/entity/adposition_detail_biz_model.dart';
import 'package:gdjg_pure_flutter/data/utils_data/carousel/entity/adposition_detail_param_model.dart';
import 'package:net_plugin/pigeon/net_result/resp_result.dart';


/// 轮播图仓库
class CarouselRep {
  final CarouselRds _rds = CarouselRds();

  /// 获取广告位详情
  Future<RespResult<AdpositionDetailBizModel>> getAdpositionDetail(
      AdpositionDetailParamModel param,
      ) async {
    return await _rds.getAdpositionDetail(param);
  }

  /// 获取轮播图Banner列表
  /// [code] 广告位标识
  /// [type] 广告类型，可选
  /// [big] 是否大广告，可选
  /// 返回Banner列表
  Future<List<AdpositionDetailABizModel>> getBannerList(
    String code, {
    String? type,
    int? big,
  }) async {
    try {
      final param = AdpositionDetailParamModel(
        code: code,
        type: type ?? "",
        big: big?.toString() ?? "",
      );

      final result = await getAdpositionDetail(param);

      if (result.isOK()) {
        return result.getSucData()?.list ?? [];
      } else {
        return [];
      }
    } catch (e) {
      return [];
    }
  }

  /// 获取首页班组Banner
  Future<List<AdpositionDetailABizModel>> getHomeGroupBanner() async {
    return getBannerList('JGJZ_HOME_GROUP_BANER');
  }

  /// 获取首页个人Banner
  Future<List<AdpositionDetailABizModel>> getHomePersonalBanner() async {
    return getBannerList('JGJZ_HOME_PERSONAL_BANER');
  }

  /// 获取日历页Banner
  Future<List<AdpositionDetailABizModel>> getCalendarBanner() async {
    return getBannerList('JGJZ_CALENDAR_PERSONAL_BANER');
  }

  /// 获取项目工友页Banner
  Future<List<AdpositionDetailABizModel>> getProjectWorkerBanner() async {
    return getBannerList('JGJZ_PROJECT_WORKER_LIST');
  }

  /// 获取批量退场页Banner
  Future<List<AdpositionDetailABizModel>> getProjectBatchQuitBanner() async {
    return getBannerList('JGJZ_PROJECT_BATCH_QUIT');
  }

  /// 根据广告位代码获取Banner
  /// [code] 广告位标识
  /// [type] 广告类型，可选
  /// [big] 是否大广告，可选
  Future<List<AdpositionDetailABizModel>> getBannerByCode(
    String code, {
    String? type,
    int? big,
  }) async {
    return getBannerList(
      code,
      type: type,
      big: big,
    );
  }
}
