import 'package:gdjg_pure_flutter/data/utils_data/carousel/entity/adposition_detail_biz_model.dart';
import 'package:gdjg_pure_flutter/data/utils_data/carousel/entity/adposition_detail_net_model.dart';
import 'package:gdjg_pure_flutter/data/utils_data/carousel/entity/adposition_detail_param_model.dart';
import 'package:net_plugin/pigeon/api_generated/net_channel.g.dart';
import 'package:net_plugin/pigeon/net_result/resp_fail.dart';
import 'package:net_plugin/pigeon/net_result/resp_result.dart';
import 'package:net_plugin/pigeon/net_service/net_core.dart';

/// 轮播图数据源
class CarouselRds {

  /// 获取广告位详情
  Future<RespResult<AdpositionDetailBizModel>> getAdpositionDetail(
    AdpositionDetailParamModel param,
  ) async {
    final queryBody = <String, dynamic>{
      'code': param.code,
    };

    if (param.type.isNotEmpty) {
      queryBody['type'] = param.type;
    }

    if (param.big.isNotEmpty) {
      queryBody['big'] = param.big;
    }

    var reqParam = BaseBizRequestEntity(
        url: 'api/comm/adposition/detail',
        method: HTTP_METHOD.GET,
        content: queryBody.cast<String, Object>(),
        requestExtra: RequestExtra(
          domain: NET_DOMAIN.JG_PHP,
          printResp: false,
          showLoading: false,
        ));

    final RespResult<AdpositionDetailBizModel> result = await NetCore.requestJGPHP(reqParam, (json) {
      return AdpositionDetailNetModel.fromJson(json).transform();
    }).catchError((error) {
      return Future.value(RespResult<AdpositionDetailBizModel>(fail: RespFail(errorMsg: error.toString())));
    });

    return result;
  }

  /// 获取首页班组Banner
  Future<RespResult<AdpositionDetailBizModel>> getHomeGroupBanner() async {
    return getAdpositionDetail(
      AdpositionDetailParamModel(code: 'JGJZ_HOME_GROUP_BANER'),
    );
  }

  /// 获取首页个人Banner
  Future<RespResult<AdpositionDetailBizModel>> getHomePersonalBanner() async {
    return getAdpositionDetail(
      AdpositionDetailParamModel(code: 'JGJZ_HOME_PERSONAL_BANER'),
    );
  }

  /// 获取日历页Banner
  Future<RespResult<AdpositionDetailBizModel>> getCalendarBanner() async {
    return getAdpositionDetail(
      AdpositionDetailParamModel(code: 'JGJZ_CALENDAR_PERSONAL_BANER'),
    );
  }

  /// 获取项目工友页Banner
  Future<RespResult<AdpositionDetailBizModel>> getProjectWorkerBanner() async {
    return getAdpositionDetail(
      AdpositionDetailParamModel(code: 'JGJZ_PROJECT_WORKER_LIST'),
    );
  }

  /// 获取批量退场页Banner
  Future<RespResult<AdpositionDetailBizModel>> getProjectBatchQuitBanner() async {
    return getAdpositionDetail(
      AdpositionDetailParamModel(code: 'JGJZ_PROJECT_BATCH_QUIT'),
    );
  }
}
