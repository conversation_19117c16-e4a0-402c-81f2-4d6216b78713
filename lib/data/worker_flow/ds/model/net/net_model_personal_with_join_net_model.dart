import 'dart:convert';
import 'package:gdjg_pure_flutter/data/worker_flow/repo/net_model_personal_with_join_biz_model.dart';

class NetModelPersonalWithJoinNetModel {
  List<NetModelPersonalWithJoinANetModel>? list;

  NetModelPersonalWithJoinNetModel();

  Map<String, dynamic> toJson(NetModelPersonalWithJoinNetModel instance) {
    var map = <String, Object>{};
    if (list != null) map["list"] = list!;
    return map;
  }

  factory NetModelPersonalWithJoinNetModel.fromJson(Map<String, dynamic> json) {
    var netModel = NetModelPersonalWithJoinNetModel();
    netModel.list = (json["list"] as List<dynamic>?)
        ?.map((e) => NetModelPersonalWithJoinANetModel.fromJson(e as Map<String, dynamic>))
        .toList();
    return netModel;
  }

  NetModelPersonalWithJoinBizModel transform() {
    return NetModelPersonalWithJoinBizModel(
      list: list?.map((e) => e.transform()).toList() ?? [],
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class NetModelPersonalWithJoinANetModel {
  /// 记工本id
  double? work_note_id;

  /// 1班组，2个人
  double? identity;

  /// 账本的工资开关 1 打开， 2关闭
  double? fee_switch;

  /// 0我参与 1个人本
  double? is_self_created;

  /// 0未结清 1已结清
  double? is_ignored;

  /// 0不是工人 1是工人
  double? is_worker;

  /// 0不是代班 1是代班
  double? is_agent;

  /// 部门id
  double? dept_id;

  /// 企业id
  double? corp_id;

  /// 项目名称
  String? name;

  /// 创建时间
  double? create_time;
  double? self_user;

  /// 工人数量
  double? worker_num;

  /// 创建时间
  String? create_time_str;

  /// 工人id
  double? worker_id;

  /// 0今日未记工 1今日记工
  double? business_today;

  /// 首笔记工时间
  String? first_business_date;

  /// 上次记工时间
  String? last_business_date;

  /// 上次操作时间
  String? last_operation_date;

  /// 是否有过记工/记账 0-没有 1-有
  double? has_business;
  double? sort_int;

  /// 1上次记工，0非上次记工
  double? last_bookkeeping;

  /// 0不显示打卡按钮 1显示打卡按钮
  double? show_dk_button;

  /// (我参与的项目)项目是否隐藏工资 0不隐藏 1隐藏
  double? hide_money;

  NetModelPersonalWithJoinANetModel();

  Map<String, dynamic> toJson(NetModelPersonalWithJoinANetModel instance) {
    var map = <String, Object>{};
    if (work_note_id != null) map["work_note_id"] = work_note_id!;
    if (identity != null) map["identity"] = identity!;
    if (fee_switch != null) map["fee_switch"] = fee_switch!;
    if (is_self_created != null) map["is_self_created"] = is_self_created!;
    if (is_ignored != null) map["is_ignored"] = is_ignored!;
    if (is_worker != null) map["is_worker"] = is_worker!;
    if (is_agent != null) map["is_agent"] = is_agent!;
    if (dept_id != null) map["dept_id"] = dept_id!;
    if (corp_id != null) map["corp_id"] = corp_id!;
    if (name != null) map["name"] = name!;
    if (create_time != null) map["create_time"] = create_time!;
    if (self_user != null) map["self_user"] = self_user!;
    if (worker_num != null) map["worker_num"] = worker_num!;
    if (create_time_str != null) map["create_time_str"] = create_time_str!;
    if (worker_id != null) map["worker_id"] = worker_id!;
    if (business_today != null) map["business_today"] = business_today!;
    if (first_business_date != null) map["first_business_date"] = first_business_date!;
    if (last_business_date != null) map["last_business_date"] = last_business_date!;
    if (last_operation_date != null) map["last_operation_date"] = last_operation_date!;
    if (has_business != null) map["has_business"] = has_business!;
    if (sort_int != null) map["sort_int"] = sort_int!;
    if (last_bookkeeping != null) map["last_bookkeeping"] = last_bookkeeping!;
    if (show_dk_button != null) map["show_dk_button"] = show_dk_button!;
    if (hide_money != null) map["hide_money"] = hide_money!;
    return map;
  }

  factory NetModelPersonalWithJoinANetModel.fromJson(Map<String, dynamic> json) {
    var netModel = NetModelPersonalWithJoinANetModel();
    netModel.work_note_id = double.tryParse(json["work_note_id"].toString());
    netModel.identity = double.tryParse(json["identity"].toString());
    netModel.fee_switch = double.tryParse(json["fee_switch"].toString());
    netModel.is_self_created = double.tryParse(json["is_self_created"].toString());
    netModel.is_ignored = double.tryParse(json["is_ignored"].toString());
    netModel.is_worker = double.tryParse(json["is_worker"].toString());
    netModel.is_agent = double.tryParse(json["is_agent"].toString());
    netModel.dept_id = double.tryParse(json["dept_id"].toString());
    netModel.corp_id = double.tryParse(json["corp_id"].toString());
    netModel.name = json["name"]?.toString();
    netModel.create_time = double.tryParse(json["create_time"].toString());
    netModel.self_user = double.tryParse(json["self_user"].toString());
    netModel.worker_num = double.tryParse(json["worker_num"].toString());
    netModel.create_time_str = json["create_time_str"]?.toString();
    netModel.worker_id = double.tryParse(json["worker_id"].toString());
    netModel.business_today = double.tryParse(json["business_today"].toString());
    netModel.first_business_date = json["first_business_date"]?.toString();
    netModel.last_business_date = json["last_business_date"]?.toString();
    netModel.last_operation_date = json["last_operation_date"]?.toString();
    netModel.has_business = double.tryParse(json["has_business"].toString());
    netModel.sort_int = double.tryParse(json["sort_int"].toString());
    netModel.last_bookkeeping = double.tryParse(json["last_bookkeeping"].toString());
    netModel.show_dk_button = double.tryParse(json["show_dk_button"].toString());
    netModel.hide_money = double.tryParse(json["hide_money"].toString());
    return netModel;
  }

  NetModelPersonalWithJoinABizModel transform() {
    return NetModelPersonalWithJoinABizModel(
      workNoteId: work_note_id ?? 0.0,
      identity: identity ?? 0.0,
      feeSwitch: fee_switch ?? 0.0,
      isSelfCreated: is_self_created ?? 0.0,
      isIgnored: is_ignored ?? 0.0,
      isWorker: is_worker ?? 0.0,
      isAgent: is_agent ?? 0.0,
      deptId: dept_id ?? 0.0,
      corpId: corp_id ?? 0.0,
      name: name ?? "",
      createTime: create_time ?? 0.0,
      selfUser: self_user ?? 0.0,
      workerNum: worker_num ?? 0.0,
      createTimeStr: create_time_str ?? "",
      workerId: worker_id ?? 0.0,
      businessToday: business_today ?? 0.0,
      firstBusinessDate: first_business_date ?? "",
      lastBusinessDate: last_business_date ?? "",
      lastOperationDate: last_operation_date ?? "",
      hasBusiness: has_business ?? 0.0,
      sortInt: sort_int ?? 0.0,
      lastBookkeeping: last_bookkeeping ?? 0.0,
      showDkButton: show_dk_button ?? 0.0,
      hideMoney: hide_money ?? 0.0,
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}
