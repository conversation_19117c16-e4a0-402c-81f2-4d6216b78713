import 'dart:convert';

import 'package:gdjg_pure_flutter/data/worker_flow/repo/business_get_index_business_count_biz_model.dart';

class BusinessCountNetModel {
  /// 点工
  SpotWorkNetModel? spot_work;

  /// 工量
  UnitNetModel? unit;

  /// 记工钱/短工
  WorkMoneyNetModel? work_money;

  /// 借支
  BorrowNetModel? borrow;

  /// 包工
  ContractorNetModel? contractor;

  /// 工资
  WageNetModel? wage;

  ///
  HourNetModel? hour;

  List<OtherExpensesCountNetModel>? other_expenses;

  /// [4.9]统计汇总
  SummaryNetModel? summary;

  ///
  String? unsettled;

  ///
  String? income;

  String? num;

  BusinessCountNetModel();

  Map<String, dynamic> toJson(BusinessCountNetModel instance) => <String, dynamic>{
        "spot_work": instance.spot_work,
        "unit": instance.unit,
        "work_money": instance.work_money,
        "borrow": instance.borrow,
        "contractor": instance.contractor,
        "wage": instance.wage,
        "hour": instance.hour,
        "other_expenses": instance.other_expenses,
        "summary": instance.summary,
        "unsettled": instance.unsettled,
        "income": instance.income,
        "num": instance.num,
      };

  factory BusinessCountNetModel.fromJson(Map<String, dynamic> json) {
    var netModel = BusinessCountNetModel();
    netModel.spot_work = json["spot_work"] == null
        ? null
        : SpotWorkNetModel.fromJson(json["spot_work"] as Map<String, dynamic>);
    netModel.unit =
        json["unit"] == null ? null : UnitNetModel.fromJson(json["unit"] as Map<String, dynamic>);
    netModel.work_money = json["work_money"] == null
        ? null
        : WorkMoneyNetModel.fromJson(json["work_money"] as Map<String, dynamic>);
    netModel.borrow = json["borrow"] == null
        ? null
        : BorrowNetModel.fromJson(json["borrow"] as Map<String, dynamic>);
    netModel.contractor = json["contractor"] == null
        ? null
        : ContractorNetModel.fromJson(json["contractor"] as Map<String, dynamic>);
    netModel.wage =
        json["wage"] == null ? null : WageNetModel.fromJson(json["wage"] as Map<String, dynamic>);
    netModel.hour =
        json["hour"] == null ? null : HourNetModel.fromJson(json["hour"] as Map<String, dynamic>);
    netModel.other_expenses = (json["other_expenses"] as List<dynamic>?)
        ?.map((e) => OtherExpensesCountNetModel.fromJson(e as Map<String, dynamic>))
        .toList();
    netModel.summary = json["summary"] == null
        ? null
        : SummaryNetModel.fromJson(json["summary"] as Map<String, dynamic>);
    netModel.unsettled = json["unsettled"]?.toString();
    netModel.income = json["income"]?.toString();
    return netModel;
  }

  BusinessCountBizModel transform() {
    return BusinessCountBizModel(
      spotWork: spot_work?.transform(),
      unit: unit?.transform(),
      workMoney: work_money?.transform(),
      borrow: borrow?.transform(),
      contractor: contractor?.transform(),
      wage: wage?.transform(),
      hour: hour?.transform(),
      otherExpenses: other_expenses?.map((e) => e.transform()).toList() ?? [],
      summary: summary?.transform(),
      unsettled: double.parse(unsettled ?? "0.0"),
      income: double.parse(income ?? "0.0"),
      num: int.parse(num ?? "0"),
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class SpotWorkNetModel {
  /// 上班工时
  String? work_time;

  /// 上班小时
  String? work_time_hour;

  /// 加班 小时
  String? overtime;

  /// 加班工
  String? overtime_work;

  /// 点工工钱
  String? spot_work_fee_money;

  /// 数量
  String? num;

  SpotWorkNetModel();

  Map<String, dynamic> toJson(SpotWorkNetModel instance) => <String, dynamic>{
        "work_time": instance.work_time,
        "work_time_hour": instance.work_time_hour,
        "overtime": instance.overtime,
        "overtime_work": instance.overtime_work,
        "spot_work_fee_money": instance.spot_work_fee_money,
        "num": instance.num,
      };

  factory SpotWorkNetModel.fromJson(Map<String, dynamic> json) {
    var netModel = SpotWorkNetModel();
    netModel.work_time = json["work_time"]?.toString();
    netModel.work_time_hour = json["work_time_hour"]?.toString();
    netModel.overtime = json["overtime"]?.toString();
    netModel.overtime_work = json["overtime_work"]?.toString();
    netModel.spot_work_fee_money = json["spot_work_fee_money"]?.toString();
    netModel.num = json["num"]?.toString();
    return netModel;
  }

  SpotWorkBizModel transform() {
    return SpotWorkBizModel(
      workTime: work_time ?? "",
      workTimeHour: work_time_hour ?? "",
      overtime: overtime ?? "",
      overtimeWork: overtime_work ?? "",
      spotWorkFeeMoney: double.parse(spot_work_fee_money ?? "0.0"),
      num: double.parse(num ?? "0.0"),
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class UnitNetModel {
  /// 工量详情 按分项id汇总
  List<CountUnitNetModel>? count_unit;

  /// 记工笔数
  String? num;

  /// 工量工钱
  String? unit_money;

  UnitNetModel();

  Map<String, dynamic> toJson(UnitNetModel instance) => <String, dynamic>{
        "count_unit": instance.count_unit,
        "num": instance.num,
        "unit_money": instance.unit_money,
      };

  factory UnitNetModel.fromJson(Map<String, dynamic> json) {
    var netModel = UnitNetModel();
    netModel.count_unit = (json["count_unit"] as List<dynamic>?)
        ?.map((e) => CountUnitNetModel.fromJson(e as Map<String, dynamic>))
        .toList();
    netModel.num = json["num"]?.toString();
    netModel.unit_money = json["unit_money"]?.toString();
    return netModel;
  }

  UnitBizModel transform() {
    return UnitBizModel(
      countUnit: count_unit?.map((e) => e.transform()).toList() ?? [],
      num: double.parse(num ?? "0.0"),
      unitMoney: double.parse(unit_money ?? "0.0"),
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class CountUnitNetModel {
  /// 分项id
  String? unit_work_type;

  /// 分项名
  String? unit_work_type_name;

  /// 分项单位
  String? unit_work_type_unit;

  /// 4.0以前旧版单位 4.0后废弃
  String? unit;

  /// 工程量
  String? count;

  /// 工钱
  String? unit_money;

  /// 记工笔数
  String? num;

  ///
  String? last_unit_price;

  CountUnitNetModel();

  Map<String, dynamic> toJson(CountUnitNetModel instance) => <String, dynamic>{
        "unit_work_type": instance.unit_work_type,
        "unit_work_type_name": instance.unit_work_type_name,
        "unit_work_type_unit": instance.unit_work_type_unit,
        "unit": instance.unit,
        "count": instance.count,
        "unit_money": instance.unit_money,
        "num": instance.num,
        "last_unit_price": instance.last_unit_price,
      };

  factory CountUnitNetModel.fromJson(Map<String, dynamic> json) {
    var netModel = CountUnitNetModel();
    netModel.unit_work_type = json["unit_work_type"]?.toString();
    netModel.unit_work_type_name = json["unit_work_type_name"]?.toString();
    netModel.unit_work_type_unit = json["unit_work_type_unit"]?.toString();
    netModel.unit = json["unit"]?.toString();
    netModel.count = json["count"]?.toString();
    netModel.unit_money = json["unit_money"]?.toString();
    netModel.num = json["num"]?.toString();
    netModel.last_unit_price = json["last_unit_price"]?.toString();
    return netModel;
  }

  CountUnitBizModel transform() {
    return CountUnitBizModel(
      unitWorkType: double.parse(unit_work_type ?? "0.0"),
      unitWorkTypeName: unit_work_type_name ?? "",
      unitWorkTypeUnit: unit_work_type_unit ?? "",
      unit: unit ?? "",
      count: count ?? "",
      unitMoney: unit_money ?? "",
      num: double.parse(num ?? "0.0"),
      lastUnitPrice: last_unit_price ?? "",
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class WorkMoneyNetModel {
  ///
  String? work_money;

  ///
  String? num;

  WorkMoneyNetModel();

  Map<String, dynamic> toJson(WorkMoneyNetModel instance) => <String, dynamic>{
        "work_money": instance.work_money,
        "num": instance.num,
      };

  factory WorkMoneyNetModel.fromJson(Map<String, dynamic> json) {
    var netModel = WorkMoneyNetModel();
    netModel.work_money = json["work_money"]?.toString();
    netModel.num = json["num"]?.toString();
    return netModel;
  }

  WorkMoneyBizModel transform() {
    return WorkMoneyBizModel(
      workMoney: double.parse(work_money ?? "0.0"),
      num: double.parse(num ?? "0.0"),
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class BorrowNetModel {
  ///
  String? borrow_count;

  ///
  String? num;

  BorrowNetModel();

  Map<String, dynamic> toJson(BorrowNetModel instance) => <String, dynamic>{
        "borrow_count": instance.borrow_count,
        "num": instance.num,
      };

  factory BorrowNetModel.fromJson(Map<String, dynamic> json) {
    var netModel = BorrowNetModel();
    netModel.borrow_count = json["borrow_count"]?.toString();
    netModel.num = json["num"]?.toString();
    return netModel;
  }

  BorrowBizModel transform() {
    return BorrowBizModel(
      borrowCount: borrow_count ?? "",
      num: double.parse(num ?? "0.0"),
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class ContractorNetModel {
  ///
  String? contractor_work_time;

  ///
  String? contractor_work_time_hour;

  ///
  String? num;

  /// 包工钱
  String? contractor_money;

  ///
  String? contractor_overtime;

  ContractorNetModel();

  Map<String, dynamic> toJson(ContractorNetModel instance) => <String, dynamic>{
        "contractor_work_time": instance.contractor_work_time,
        "contractor_work_time_hour": instance.contractor_work_time_hour,
        "num": instance.num,
        "contractor_money": instance.contractor_money,
        "contractor_overtime": instance.contractor_overtime,
      };

  factory ContractorNetModel.fromJson(Map<String, dynamic> json) {
    var netModel = ContractorNetModel();
    netModel.contractor_work_time = json["contractor_work_time"]?.toString();
    netModel.contractor_work_time_hour = json["contractor_work_time_hour"]?.toString();
    netModel.num = json["num"]?.toString();
    netModel.contractor_money = json["contractor_money"]?.toString();
    netModel.contractor_overtime = json["contractor_overtime"]?.toString();
    return netModel;
  }

  ContractorBizModel transform() {
    return ContractorBizModel(
      contractorWorkTime: double.parse(contractor_work_time ?? "0.0"),
      contractorWorkTimeHour: double.parse(contractor_work_time_hour ?? "0.0"),
      num: double.parse(num ?? "0.0"),
      contractorMoney: contractor_money ?? "",
      contractorOvertime: contractor_overtime ?? "",
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class WageNetModel {
  ///
  String? wage_count;

  ///
  String? num;

  WageNetModel();

  Map<String, dynamic> toJson(WageNetModel instance) => <String, dynamic>{
        "wage_count": instance.wage_count,
        "num": instance.num,
      };

  factory WageNetModel.fromJson(Map<String, dynamic> json) {
    var netModel = WageNetModel();
    netModel.wage_count = json["wage_count"]?.toString();
    netModel.num = json["num"]?.toString();
    return netModel;
  }

  WageBizModel transform() {
    return WageBizModel(
      wageCount: wage_count ?? "",
      num: double.parse(num ?? "0.0"),
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class HourNetModel {
  /// 小时工
  String? hour_time;

  ///
  String? hour_overtime;

  ///
  String? num;

  HourNetModel();

  Map<String, dynamic> toJson(HourNetModel instance) => <String, dynamic>{
        "hour_time": instance.hour_time,
        "hour_overtime": instance.hour_overtime,
        "num": instance.num,
      };

  factory HourNetModel.fromJson(Map<String, dynamic> json) {
    var netModel = HourNetModel();
    netModel.hour_time = json["hour_time"]?.toString();
    netModel.hour_overtime = json["hour_overtime"]?.toString();
    netModel.num = json["num"]?.toString();
    return netModel;
  }

  HourBizModel transform() {
    return HourBizModel(
      hourTime: double.parse(hour_time ?? "0.0"),
      hourOvertime: double.parse(hour_overtime ?? "0.0"),
      num: double.parse(num ?? "0.0"),
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class OtherExpensesCountNetModel {
  ///
  String? money;
  String? real_money;
  String? count;
  String? bk_other_expenses_id;
  String? name;
  String? num;

  OtherExpensesCountNetModel();

  factory OtherExpensesCountNetModel.fromJson(Map<String, dynamic> json) {
    var netModel = OtherExpensesCountNetModel();
    netModel.money = json["money"]?.toString();
    netModel.real_money = json["real_money"]?.toString();
    netModel.count = json["count"]?.toString();
    netModel.bk_other_expenses_id = json["bk_other_expenses_id"]?.toString();
    netModel.name = json["name"]?.toString();
    netModel.num = json["num"]?.toString();
    return netModel;
  }

  Map<String, dynamic> toJson(OtherExpensesCountNetModel instance) => <String, dynamic>{
        "money": instance.money,
        "real_money": instance.real_money,
        "count": instance.count,
        "bk_other_expenses_id": instance.bk_other_expenses_id,
        "name": instance.name,
        "num": instance.num,
      };

  OtherExpensesCountBizModel transform() {
    return OtherExpensesCountBizModel(
      money: money ?? "",
      realMoney: real_money ?? "",
      count: int.parse(count ?? "0"),
      bkOtherExpensesId: int.parse(bk_other_expenses_id ?? "0"),
      name: name ?? "",
      num: int.parse(num ?? "0"),
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class SummaryNetModel {
  /// 上班工时
  String? work_time;

  /// 上班小时
  String? work_hour;

  /// 加班工时
  String? overtime_work;

  /// 加班小时
  String? overtime;

  /// 借支金额
  String? borrow;

  /// 结算金额
  String? wage;

  SummaryNetModel();

  Map<String, dynamic> toJson(SummaryNetModel instance) => <String, dynamic>{
        "work_time": instance.work_time,
        "work_hour": instance.work_hour,
        "overtime_work": instance.overtime_work,
        "overtime": instance.overtime,
        "borrow": instance.borrow,
        "wage": instance.wage,
      };

  factory SummaryNetModel.fromJson(Map<String, dynamic> json) {
    var netModel = SummaryNetModel();
    netModel.work_time = json["work_time"]?.toString();
    netModel.work_hour = json["work_hour"]?.toString();
    netModel.overtime_work = json["overtime_work"]?.toString();
    netModel.overtime = json["overtime"]?.toString();
    netModel.borrow = json["borrow"]?.toString();
    netModel.wage = json["wage"]?.toString();
    return netModel;
  }

  SummaryBizModel transform() {
    return SummaryBizModel(
      workTime: double.parse(work_time ?? "0.0"),
      workHour: double.parse(work_hour ?? "0.0"),
      overtimeWork: double.parse(overtime_work ?? "0.0"),
      overtime: double.parse(overtime ?? "0.0"),
      borrow: borrow ?? "",
      wage: wage ?? "",
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}
