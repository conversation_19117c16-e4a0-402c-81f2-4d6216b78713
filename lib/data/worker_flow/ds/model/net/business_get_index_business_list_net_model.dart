import 'dart:convert';

import 'package:gdjg_pure_flutter/data/worker_flow/repo/business_get_index_business_list_biz_model.dart';

class BusinessListNetModel {
  ///
  List<BusinessGetIndexBusinessListANetModel>? list;

  BusinessListNetModel();

  Map<String, dynamic> toJson(BusinessListNetModel instance) => <String, dynamic>{
        "list": instance.list,
      };

  factory BusinessListNetModel.fromJson(Map<String, dynamic> json) {
    var netModel = BusinessListNetModel();
    netModel.list = (json["list"] as List<dynamic>?)
        ?.map((e) => BusinessGetIndexBusinessListANetModel.fromJson(e as Map<String, dynamic>))
        .toList();
    return netModel;
  }

  BusinessListBizModel transform() {
    return BusinessListBizModel(
      list: list?.map((e) => e.transform()).toList() ?? [],
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class BusinessGetIndexBusinessListANetModel {
  ///
  String? date;

  ///
  List<BusinessGetIndexBusinessListBNetModel>? list;

  BusinessGetIndexBusinessListANetModel();

  Map<String, dynamic> toJson(BusinessGetIndexBusinessListANetModel instance) => <String, dynamic>{
        "date": instance.date,
        "list": instance.list,
      };

  factory BusinessGetIndexBusinessListANetModel.fromJson(Map<String, dynamic> json) {
    var netModel = BusinessGetIndexBusinessListANetModel();
    netModel.date = json["date"]?.toString();
    netModel.list = (json["list"] as List<dynamic>?)
        ?.map((e) => BusinessGetIndexBusinessListBNetModel.fromJson(e as Map<String, dynamic>))
        .toList();
    return netModel;
  }

  BusinessGetIndexBusinessListABizModel transform() {
    return BusinessGetIndexBusinessListABizModel(
      date: date ?? "",
      list: list?.map((e) => e.transform()).toList() ?? [],
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class BusinessGetIndexBusinessListBNetModel {
  ///
  String? id;

  ///
  String? business_type;

  ///
  String? business_time;

  ///
  String? work_note;

  ///
  String? worker_id;

  ///
  String? unit_work_type;

  ///
  String? work_time;

  ///
  String? work_time_hour;

  ///
  String? overtime;

  ///
  String? overtime_work;

  ///
  String? morning_work_time;

  ///
  String? morning_work_time_hour;

  ///
  String? afternoon_work_time;

  ///
  String? afternoon_work_time_hour;

  ///
  String? user_choose_spotwork;

  ///
  String? unit_num;

  ///
  String? unit_price;

  /// 4.0以前旧版本工量单位 4.0后废弃
  String? unit;

  ///
  String? note;

  ///
  String? fee_money;

  ///
  String? created_time;

  ///
  String? work_note_name;

  ///
  String? worker_name;

  ///
  String? money;

  ///
  String? fee_standard_id;

  ///
  String? has_img;

  /// 工量 分项名称
  String? unit_work_type_name;

  /// 工量 分项单位
  String? unit_work_type_unit;

  /// 仅[5.0.0]及以后 图片信息
  List<ImgInfoNetModel>? img_info;

  /// 其他费用
  OtherExpensesNetModel? other_expenses;

  BusinessGetIndexBusinessListBNetModel();

  Map<String, dynamic> toJson(BusinessGetIndexBusinessListBNetModel instance) => <String, dynamic>{
        "id": instance.id,
        "business_type": instance.business_type,
        "business_time": instance.business_time,
        "work_note": instance.work_note,
        "worker_id": instance.worker_id,
        "unit_work_type": instance.unit_work_type,
        "work_time": instance.work_time,
        "work_time_hour": instance.work_time_hour,
        "overtime": instance.overtime,
        "overtime_work": instance.overtime_work,
        "morning_work_time": instance.morning_work_time,
        "morning_work_time_hour": instance.morning_work_time_hour,
        "afternoon_work_time": instance.afternoon_work_time,
        "afternoon_work_time_hour": instance.afternoon_work_time_hour,
        "user_choose_spotwork": instance.user_choose_spotwork,
        "unit_num": instance.unit_num,
        "unit_price": instance.unit_price,
        "unit": instance.unit,
        "note": instance.note,
        "fee_money": instance.fee_money,
        "created_time": instance.created_time,
        "work_note_name": instance.work_note_name,
        "worker_name": instance.worker_name,
        "money": instance.money,
        "fee_standard_id": instance.fee_standard_id,
        "has_img": instance.has_img,
        "unit_work_type_name": instance.unit_work_type_name,
        "unit_work_type_unit": instance.unit_work_type_unit,
        "img_info": instance.img_info,
        "other_expenses": instance.other_expenses,
      };

  factory BusinessGetIndexBusinessListBNetModel.fromJson(Map<String, dynamic> json) {
    var netModel = BusinessGetIndexBusinessListBNetModel();
    netModel.id = json["id"]?.toString();
    netModel.business_type = json["business_type"]?.toString();
    netModel.business_time = json["business_time"]?.toString();
    netModel.work_note = json["work_note"]?.toString();
    netModel.worker_id = json["worker_id"]?.toString();
    netModel.unit_work_type = json["unit_work_type"]?.toString();
    netModel.work_time = json["work_time"]?.toString();
    netModel.work_time_hour = json["work_time_hour"]?.toString();
    netModel.overtime = json["overtime"]?.toString();
    netModel.overtime_work = json["overtime_work"]?.toString();
    netModel.morning_work_time = json["morning_work_time"]?.toString();
    netModel.morning_work_time_hour = json["morning_work_time_hour"]?.toString();
    netModel.afternoon_work_time = json["afternoon_work_time"]?.toString();
    netModel.afternoon_work_time_hour = json["afternoon_work_time_hour"]?.toString();
    netModel.user_choose_spotwork = json["user_choose_spotwork"]?.toString();
    netModel.unit_num = json["unit_num"]?.toString();
    netModel.unit_price = json["unit_price"]?.toString();
    netModel.unit = json["unit"]?.toString();
    netModel.note = json["note"]?.toString();
    netModel.fee_money = json["fee_money"]?.toString();
    netModel.created_time = json["created_time"]?.toString();
    netModel.work_note_name = json["work_note_name"]?.toString();
    netModel.worker_name = json["worker_name"]?.toString();
    netModel.money = json["money"]?.toString();
    netModel.fee_standard_id = json["fee_standard_id"]?.toString();
    netModel.has_img = json["has_img"]?.toString();
    netModel.unit_work_type_name = json["unit_work_type_name"]?.toString();
    netModel.unit_work_type_unit = json["unit_work_type_unit"]?.toString();
    netModel.img_info = (json["img_info"] as List<dynamic>?)
        ?.map((e) => ImgInfoNetModel.fromJson(e as Map<String, dynamic>))
        .toList();
    netModel.other_expenses = json["other_expenses"] == null
        ? null
        : OtherExpensesNetModel.fromJson(json["other_expenses"] as Map<String, dynamic>);
    return netModel;
  }

  BusinessGetIndexBusinessListBBizModel transform() {
    return BusinessGetIndexBusinessListBBizModel(
      id: double.parse(id ?? "0.0"),
      businessType: double.parse(business_type ?? "0.0"),
      businessTime: business_time ?? "",
      workNote: double.parse(work_note ?? "0.0"),
      workerId: double.parse(worker_id ?? "0.0"),
      unitWorkType: double.parse(unit_work_type ?? "0.0"),
      workTime: double.parse(work_time ?? "0.0"),
      workTimeHour: double.parse(work_time_hour ?? "0.0"),
      overtime: double.parse(overtime ?? "0.0"),
      overtimeWork: double.parse(overtime_work ?? "0.0"),
      morningWorkTime: morning_work_time ?? "",
      morningWorkTimeHour: morning_work_time_hour ?? "",
      afternoonWorkTime: afternoon_work_time ?? "",
      afternoonWorkTimeHour: afternoon_work_time_hour ?? "",
      userChooseSpotwork: double.parse(user_choose_spotwork ?? "0.0"),
      unitNum: unit_num ?? "",
      unitPrice: unit_price ?? "",
      unit: unit ?? "",
      note: note ?? "",
      feeMoney: double.parse(fee_money ?? "0.0"),
      createdTime: double.parse(created_time ?? "0.0"),
      workNoteName: work_note_name ?? "",
      workerName: worker_name ?? "",
      money: money ?? "",
      feeStandardId: double.parse(fee_standard_id ?? "0.0"),
      hasImg: double.parse(has_img ?? "0.0"),
      unitWorkTypeName: unit_work_type_name ?? "",
      unitWorkTypeUnit: unit_work_type_unit ?? "",
      imgInfo: img_info?.map((e) => e.transform()).toList() ?? [],
      otherExpenses: other_expenses?.transform(),
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class ImgInfoNetModel {
  /// 图片完整路径
  String? url;
  String? type;
  String? base_url;
  String? cover;

  ImgInfoNetModel();

  Map<String, dynamic> toJson(ImgInfoNetModel instance) => <String, dynamic>{
        "url": instance.url,
        "base_url": instance.base_url,
        "cover": instance.cover,
        "type": instance.type,
      };

  factory ImgInfoNetModel.fromJson(Map<String, dynamic> json) {
    var netModel = ImgInfoNetModel();
    netModel.url = json["url"]?.toString();
    netModel.base_url = json["base_url"]?.toString();
    netModel.cover = json["cover"]?.toString();
    netModel.type = json["type"]?.toString();
    return netModel;
  }

  ImgInfoBizModel transform() {
    return ImgInfoBizModel(
      url: url ?? "",
      type: type ?? "",
      baseUrl: base_url ?? "",
      cover: cover ?? "",
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class OtherExpensesNetModel {
  /// 主键id
  String? id;

  /// 记工表iD
  String? bk_business_id;

  /// 其他费用ID
  String? bk_other_expenses_id;

  /// 记工用户ID
  String? member_id;

  /// 鱼泡ID
  String? yupao_id;

  /// 费用名称
  String? name;

  OtherExpensesNetModel();

  Map<String, dynamic> toJson(OtherExpensesNetModel instance) => <String, dynamic>{
        "id": instance.id,
        "bk_business_id": instance.bk_business_id,
        "bk_other_expenses_id": instance.bk_other_expenses_id,
        "member_id": instance.member_id,
        "yupao_id": instance.yupao_id,
        "name": instance.name,
      };

  factory OtherExpensesNetModel.fromJson(Map<String, dynamic> json) {
    var netModel = OtherExpensesNetModel();
    netModel.id = json["id"]?.toString();
    netModel.bk_business_id = json["bk_business_id"]?.toString();
    netModel.bk_other_expenses_id = json["bk_other_expenses_id"]?.toString();
    netModel.member_id = json["member_id"]?.toString();
    netModel.yupao_id = json["yupao_id"]?.toString();
    netModel.name = json["name"]?.toString();
    return netModel;
  }

  OtherExpensesBizModel transform() {
    return OtherExpensesBizModel(
      id: id ?? "",
      bkBusinessId: bk_business_id ?? "",
      bkOtherExpensesId: bk_other_expenses_id ?? "",
      memberId: member_id ?? "",
      yupaoId: yupao_id ?? "",
      name: name ?? "",
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}
