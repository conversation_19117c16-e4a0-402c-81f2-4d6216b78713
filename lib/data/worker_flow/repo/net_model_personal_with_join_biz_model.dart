import 'dart:convert';

class NetModelPersonalWithJoinBizModel {
  List<NetModelPersonalWithJoinABizModel> list;

  NetModelPersonalWithJoinBizModel({
    this.list = const [],
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class NetModelPersonalWithJoinABizModel {

  /// 记工本id
  double workNoteId;

  /// 1班组，2个人
  double identity;

  /// 账本的工资开关 1 打开， 2关闭
  double feeSwitch;

  /// 0我参与 1个人本
  double isSelfCreated;

  /// 0未结清 1已结清
  double isIgnored;

  /// 0不是工人 1是工人
  double isWorker;

  /// 0不是代班 1是代班
  double isAgent;

  /// 部门id
  double deptId;

  /// 企业id
  double corpId;

  /// 项目名称
  String name;

  /// 创建时间
  double createTime;
  double selfUser;

  /// 工人数量
  double workerNum;

  /// 创建时间
  String createTimeStr;

  /// 工人id
  double workerId;

  /// 0今日未记工 1今日记工
  double businessToday;

  /// 首笔记工时间
  String firstBusinessDate;

  /// 上次记工时间
  String lastBusinessDate;

  /// 上次操作时间
  String lastOperationDate;

  /// 是否有过记工/记账 0-没有 1-有
  double hasBusiness;
  double sortInt;

  /// 1上次记工，0非上次记工
  double lastBookkeeping;

  /// 0不显示打卡按钮 1显示打卡按钮
  double showDkButton;

  /// (我参与的项目)项目是否隐藏工资 0不隐藏 1隐藏
  double hideMoney;

  NetModelPersonalWithJoinABizModel({
    this.workNoteId = 0.0,
    this.identity = 0.0,
    this.feeSwitch = 0.0,
    this.isSelfCreated = 0.0,
    this.isIgnored = 0.0,
    this.isWorker = 0.0,
    this.isAgent = 0.0,
    this.deptId = 0.0,
    this.corpId = 0.0,
    this.name = "",
    this.createTime = 0.0,
    this.selfUser = 0.0,
    this.workerNum = 0.0,
    this.createTimeStr = "",
    this.workerId = 0.0,
    this.businessToday = 0.0,
    this.firstBusinessDate = "",
    this.lastBusinessDate = "",
    this.lastOperationDate = "",
    this.hasBusiness = 0.0,
    this.sortInt = 0.0,
    this.lastBookkeeping = 0.0,
    this.showDkButton = 0.0,
    this.hideMoney = 0.0,
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}

