import 'package:gdjg_pure_flutter/data/worker_flow/ds/model/param/business_get_index_business_count_param_model.dart';
import 'package:gdjg_pure_flutter/data/worker_flow/ds/model/param/business_get_index_business_list_param_model.dart';
import 'package:gdjg_pure_flutter/data/worker_flow/ds/worker_flow_rds.dart';
import 'package:net_plugin/pigeon/net_result/resp_fail.dart';
import 'package:net_plugin/pigeon/net_result/resp_result.dart';
import 'package:net_plugin/pigeon/net_result/resp_suc.dart';

import 'business_get_index_business_count_biz_model.dart';
import 'business_get_index_business_list_biz_model.dart';
import 'net_model_personal_with_join_biz_model.dart';

class WorkerFlowRepo {
  final _workFlowRds = WorkerFlowRds();

  Future<RespResult<BusinessCountBizModel?>> fetchHeaderData(BusinessCountParamModel param) async {
    final resp = await _workFlowRds.fetchHeaderData(param);
    final data = resp.getSucData();
    if (data == null) {
      return RespFail.buildBizFail<BusinessCountBizModel>(
          resp.fail?.code, resp.fail?.errorMsg, null, resp.fail?.askId);
    }
    return resp.map((e) => e?.transform());
  }

  Future<RespResult<BusinessListBizModel?>> fetchFlowList(BusinessListParamModel param) async {
    final resp = await _workFlowRds.fetchListData(param);
    final data = resp.getSucData();
    if (data == null) {
      return RespFail.buildBizFail<BusinessListBizModel>(
          resp.fail?.code, resp.fail?.errorMsg, null, resp.fail?.askId);
    }
    return resp.map((e) => e?.transform());
  }

  Future<RespResult<NetModelPersonalWithJoinBizModel?>> fetchProjectList(
      Map<String, Object> param) async {
    final resp = await _workFlowRds.fetchProjectList(param);
    final data = resp.getSucData();
    if (data == null) {
      return RespFail.buildBizFail<NetModelPersonalWithJoinBizModel>(
          resp.fail?.code, resp.fail?.errorMsg, null, resp.fail?.askId);
    }
    return resp.map((e) => e?.transform());
  }

  Future<RespResult<bool>> fetchDeleteRecord(Map<String, Object> param) async {
    final resp = await _workFlowRds.fetchDeleteRecord(param);
    if (!resp.isOK()) {
      return RespFail.buildBizFail<bool>(
          resp.fail?.code, resp.fail?.errorMsg, null, resp.fail?.askId);
    }
    return RespSuc.buildBizSuccess(true, resp.success?.msg, resp.success?.askId);
  }
}
