class MigrateApplyParamModel {
  /// 验证码
  String code;

  /// 旧手机号
  String old_tel;

  /// 姓名
  String card_name;

  /// 身份证号
  String card_no;

  MigrateApplyParamModel({
    required this.code,
    required this.old_tel,
    required this.card_name,
    required this.card_no,
  });

  Map<String, dynamic> toMap() {
    return {
      'code': code,
      'old_tel': old_tel,
      'card_name': card_name,
      'card_no': card_no,
    };
  }
}