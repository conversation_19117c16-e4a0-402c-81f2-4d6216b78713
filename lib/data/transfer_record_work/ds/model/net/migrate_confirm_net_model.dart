import 'dart:convert';
import '../../../repo/model/migrate_confirm_biz_model.dart';

class MigrateConfirmNetModel {

  /// 是否需要登出 true需要 false不需要
  bool? require_logout;

  MigrateConfirmNetModel();

  Map<String, dynamic> toJson(MigrateConfirmNetModel instance) {
    var map = <String, Object>{};
    if (require_logout != null) map["require_logout"] = require_logout!;
    return map;
  }

  factory MigrateConfirmNetModel.fromJson(Map<String, dynamic> json) {
    var netModel = MigrateConfirmNetModel();
    netModel.require_logout = bool.tryParse(json["require_logout"].toString());
    return netModel;
  }

  MigrateConfirmBizModel transform() {
    return MigrateConfirmBizModel(
      requireLogout: require_logout ?? false,
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

