import 'dart:convert';
import '../../../repo/model/migrate_apply_biz_model.dart';

class MigrateApplyNetModel {

  /// 确认标识。为0时表示未验证通过，refuse_reason为失败原因。大于0时可传入”确认更换“接口
  double? confirm_id;

  /// 未通过原因
  String? refuse_reason;

  /// 验证通过后，当前手机号记工天数
  double? cur_num;

  /// 验证通过后，旧手机号记工天数
  double? old_num;

  MigrateApplyNetModel();

  Map<String, dynamic> toJson(MigrateApplyNetModel instance) {
    var map = <String, Object>{};
    if (confirm_id != null) map["confirm_id"] = confirm_id!;
    if (refuse_reason != null) map["refuse_reason"] = refuse_reason!;
    if (cur_num != null) map["cur_num"] = cur_num!;
    if (old_num != null) map["old_num"] = old_num!;
    return map;
  }

  factory MigrateApplyNetModel.fromJson(Map<String, dynamic> json) {
    var netModel = MigrateApplyNetModel();
    netModel.confirm_id = double.tryParse(json["confirm_id"].toString());
    netModel.refuse_reason = json["refuse_reason"]?.toString();
    netModel.cur_num = double.tryParse(json["cur_num"].toString());
    netModel.old_num = double.tryParse(json["old_num"].toString());
    return netModel;
  }

  MigrateApplyBizModel transform() {
    return MigrateApplyBizModel(
      confirmId: confirm_id ?? 0.0,
      refuseReason: refuse_reason ?? "",
      curNum: cur_num ?? 0.0,
      oldNum: old_num ?? 0.0,
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

