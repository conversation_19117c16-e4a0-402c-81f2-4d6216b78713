import 'dart:convert';

class MigrateApplyBizModel {

  /// 确认标识。为0时表示未验证通过，refuse_reason为失败原因。大于0时可传入”确认更换“接口
  double confirmId;

  /// 未通过原因
  String refuseReason;

  /// 验证通过后，当前手机号记工天数
  double curNum;

  /// 验证通过后，旧手机号记工天数
  double oldNum;

  MigrateApplyBizModel({
    this.confirmId = 0.0,
    this.refuseReason = "",
    this.curNum = 0.0,
    this.oldNum = 0.0,
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}

