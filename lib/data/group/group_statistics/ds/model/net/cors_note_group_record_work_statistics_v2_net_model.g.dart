// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'cors_note_group_record_work_statistics_v2_net_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CorsNoteGroupRecordWorkStatisticsV2NetModel
    _$CorsNoteGroupRecordWorkStatisticsV2NetModelFromJson(
            Map<String, dynamic> json) =>
        CorsNoteGroupRecordWorkStatisticsV2NetModel()
          ..self_worker_id = json['self_worker_id'] as String?
          ..list = (json['list'] as List<dynamic>?)
              ?.map((e) =>
                  CorsNoteGroupRecordWorkStatisticsV2ANetModel.fromJson(
                      e as Map<String, dynamic>))
              .toList()
          ..hasTeam = (json['hasTeam'] as num?)?.toDouble();

Map<String, dynamic> _$CorsNoteGroupRecordWorkStatisticsV2NetModelToJson(
        CorsNoteGroupRecordWorkStatisticsV2NetModel instance) =>
    <String, dynamic>{
      'self_worker_id': instance.self_worker_id,
      'list': instance.list,
      'hasTeam': instance.hasTeam,
    };

CorsNoteGroupRecordWorkStatisticsV2ANetModel
    _$CorsNoteGroupRecordWorkStatisticsV2ANetModelFromJson(
            Map<String, dynamic> json) =>
        CorsNoteGroupRecordWorkStatisticsV2ANetModel()
          ..money_num = (json['money_num'] as num?)?.toDouble()
          ..wage = (json['wage']) as String
          ..wage_num = (json['wage_num'] as num?)?.toDouble()
          ..borrow_money = (json['borrow_money']) as String
          ..borrow_money_num = (json['borrow_money_num'] as num?)?.toDouble()
          ..has_business = (json['has_business'] as num?)?.toDouble()
          ..unit_count = (json['unit_count'] as List<dynamic>?)
              ?.map(
                  (e) => UnitCountNetModel.fromJson(e as Map<String, dynamic>))
              .toList()
          ..spot_work_no_fee_num =
              (json['spot_work_no_fee_num'] as num?)?.toDouble()
          ..spot_work_no_fee_money =
              (json['spot_work_no_fee_money'] )as String
          ..unit_count_num = (json['unit_count_num'] as num?)?.toDouble()
          ..worker_id = (json['worker_id'] as num?)?.toDouble()
          ..name = json['name'] as String?
          ..spot_work = json['spot_work'] == null
              ? null
              : SpotWorkNetModel.fromJson(
                  json['spot_work'] as Map<String, dynamic>)
          ..spot_work_num = (json['spot_work_num'] as num?)?.toDouble()
          ..contractor_work = json['contractor_work'] == null
              ? null
              : ContractorWorkNetModel.fromJson(
                  json['contractor_work'] as Map<String, dynamic>)
          ..contractor_work_num =
              (json['contractor_work_num'] as num?)?.toDouble()
          ..hour = json['hour'] == null
              ? null
              : HourNetModel.fromJson(json['hour'] as Map<String, dynamic>)
          ..hour_num = (json['hour_num'] as num?)?.toDouble()
          ..money = json['money'] == null
              ? null
              : MoneyNetModel.fromJson(json['money'] as Map<String, dynamic>);

Map<String, dynamic> _$CorsNoteGroupRecordWorkStatisticsV2ANetModelToJson(
        CorsNoteGroupRecordWorkStatisticsV2ANetModel instance) =>
    <String, dynamic>{
      'money_num': instance.money_num,
      'wage': instance.wage,
      'wage_num': instance.wage_num,
      'borrow_money': instance.borrow_money,
      'borrow_money_num': instance.borrow_money_num,
      'has_business': instance.has_business,
      'unit_count': instance.unit_count,
      'spot_work_no_fee_num': instance.spot_work_no_fee_num,
      'spot_work_no_fee_money': instance.spot_work_no_fee_money,
      'unit_count_num': instance.unit_count_num,
      'worker_id': instance.worker_id,
      'name': instance.name,
      'spot_work': instance.spot_work,
      'spot_work_num': instance.spot_work_num,
      'contractor_work': instance.contractor_work,
      'contractor_work_num': instance.contractor_work_num,
      'hour': instance.hour,
      'hour_num': instance.hour_num,
      'money': instance.money,
    };

UnitCountNetModel _$UnitCountNetModelFromJson(Map<String, dynamic> json) =>
    UnitCountNetModel()
      ..unit = json['unit'] as String?
      ..num = (json['num'] as num?)?.toDouble()
      ..unit_work_type_unit = json['unit_work_type_unit'] as String?
      ..unit_work_type_name = json['unit_work_type_name'] as String?
      ..unit_work_type = (json['unit_work_type'] as num?)?.toDouble()
      ..unit_money = json['unit_money'] as String?
      ..count = json['count'] as String?;

Map<String, dynamic> _$UnitCountNetModelToJson(UnitCountNetModel instance) =>
    <String, dynamic>{
      'unit': instance.unit,
      'num': instance.num,
      'unit_work_type_unit': instance.unit_work_type_unit,
      'unit_work_type_name': instance.unit_work_type_name,
      'unit_work_type': instance.unit_work_type,
      'unit_money': instance.unit_money,
      'count': instance.count,
    };

SpotWorkNetModel _$SpotWorkNetModelFromJson(Map<String, dynamic> json) =>
    SpotWorkNetModel()
      ..money = (json['money'] )as String
      ..work_time = (json['work_time']) as String
      ..work_time_hour = (json['work_time_hour'] as num?)?.toDouble()
      ..overtime = (json['overtime'] as num?)?.toDouble()
      ..overtime_work = (json['overtime_work'])as String
      ..all_money = (json['all_money'] as num?)?.toDouble()
      ..fee_money = (json['fee_money'])as String;

Map<String, dynamic> _$SpotWorkNetModelToJson(SpotWorkNetModel instance) =>
    <String, dynamic>{
      'money': instance.money,
      'work_time': instance.work_time,
      'work_time_hour': instance.work_time_hour,
      'overtime': instance.overtime,
      'overtime_work': instance.overtime_work,
      'all_money': instance.all_money,
      'fee_money': instance.fee_money,
    };

ContractorWorkNetModel _$ContractorWorkNetModelFromJson(
        Map<String, dynamic> json) =>
    ContractorWorkNetModel()
      ..money = (json['money']) as String
      ..work_time = (json['work_time'] )as String
      ..work_time_hour = (json['work_time_hour'] as num?)?.toDouble()
      ..overtime = (json['overtime'] as num?)?.toDouble()
      ..overtime_work = (json['overtime_work'] as num?)?.toDouble()
      ..all_money = (json['all_money'] as num?)?.toDouble()
      ..fee_money = json['fee_money'] as String?;

Map<String, dynamic> _$ContractorWorkNetModelToJson(
        ContractorWorkNetModel instance) =>
    <String, dynamic>{
      'money': instance.money,
      'work_time': instance.work_time,
      'work_time_hour': instance.work_time_hour,
      'overtime': instance.overtime,
      'overtime_work': instance.overtime_work,
      'all_money': instance.all_money,
      'fee_money': instance.fee_money,
    };

HourNetModel _$HourNetModelFromJson(Map<String, dynamic> json) => HourNetModel()
  ..money = (json['money'] )as String
  ..work_time_hour = (json['work_time_hour'] as num?)?.toDouble()
  ..overtime = (json['overtime'] as num?)?.toDouble()
  ..all_money = (json['all_money'] as num?)?.toDouble();

Map<String, dynamic> _$HourNetModelToJson(HourNetModel instance) =>
    <String, dynamic>{
      'money': instance.money,
      'work_time_hour': instance.work_time_hour,
      'overtime': instance.overtime,
      'all_money': instance.all_money,
    };

MoneyNetModel _$MoneyNetModelFromJson(Map<String, dynamic> json) =>
    MoneyNetModel()..money = (json['money'] )as String;

Map<String, dynamic> _$MoneyNetModelToJson(MoneyNetModel instance) =>
    <String, dynamic>{
      'money': instance.money,
    };
