import 'dart:convert';

import 'package:flutter/services.dart';
import 'package:gdjg_pure_flutter/data/group/group_statistics/ds/model/net/cors_note_group_record_work_statistics_v2_biz_model.dart';
import 'package:gdjg_pure_flutter/data/group/group_statistics/ds/model/net/cors_note_group_record_work_statistics_v2_net_model.dart';
import 'package:gdjg_pure_flutter/data/group/group_statistics/ds/model/param/cors_note_group_record_work_statistics_v2_param_model.dart';
import 'package:net_plugin/pigeon/api_generated/net_channel.g.dart';
import 'package:net_plugin/pigeon/net_result/resp_fail.dart';
import 'package:net_plugin/pigeon/net_result/resp_result.dart';
import 'package:net_plugin/pigeon/net_service/net_core.dart';

/// 统计列表网络请求
class GroupStatisticsListRds {
  Future<RespResult<CorsNoteGroupRecordWorkStatisticsV2BizModel>> queryGroupStatisticsList(
      CorsNoteGroupRecordWorkStatisticsV2ParamModel req,
      ) async {
    final queryBody = {
      'start_business_time': req.start_business_time,
      'end_business_time': req.end_business_time,
      'business_type':req.business_type,
      'worker_id':req.worker_id
    };

    Future<Map<String, dynamic>> loadTestJson() async {
      final jsonString = await rootBundle.loadString('lib/data/group/group_statistics/test_json.json');
      return json.decode(jsonString);
    }

    final testJson = await loadTestJson();
    print('======LINer===数据${testJson}');
    var reqParam = BaseBizRequestEntity(
        url: '/cors_note/group-record-work-statistics-v2',
        method: HTTP_METHOD.GET,
        content: queryBody,
        requestExtra: RequestExtra(domain: NET_DOMAIN.JG_PHP, printResp: false, showLoading: true));
    final RespResult<CorsNoteGroupRecordWorkStatisticsV2BizModel> result = await NetCore.requestJGPHP(reqParam, (json) {
      return CorsNoteGroupRecordWorkStatisticsV2NetModel().transform();
    }).catchError((error) {
      print('错误----${error.toString()}');
      return Future.value(RespResult<CorsNoteGroupRecordWorkStatisticsV2BizModel>(fail: RespFail(errorMsg: error.toString())));
    });
    return result;
  }
}