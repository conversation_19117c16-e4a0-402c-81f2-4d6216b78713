import 'package:gdjg_pure_flutter/data/yu_pao_news_data/ds/model/param/news_get_list_param_model.dart';
import 'package:gdjg_pure_flutter/data/yu_pao_news_data/ds/yu_pao_new_rds.dart';
import 'package:gdjg_pure_flutter/data/yu_pao_news_data/repo/model/news_get_list_biz_model.dart';
import 'package:net_plugin/pigeon/net_result/resp_result.dart';

class YuPaoNewsRepo {
  final YuPaoNewsRDS _rds = YuPaoNewsRDS();

  /// 获取鱼泡资讯列表
  Future<RespResult<NewsGetListBizModel?>> getNewsList(
      NewsGetListParamModel param) async {
    final result = await _rds.getNewsList(param);
    return result.map((result) => result?.transform());
  }
}
