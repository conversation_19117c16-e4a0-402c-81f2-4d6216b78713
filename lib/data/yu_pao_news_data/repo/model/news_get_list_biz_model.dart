import 'dart:convert';

class NewsGetListBizModel {
  List<NewsGetListABizModel> list;

  NewsGetListBizModel({
    this.list = const [],
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class NewsGetListABizModel {

  /// 作者
  String author;

  /// 图片链接
  String coverImg;
  double id;

  /// 跳转链接
  String jumpUrl;

  /// 发布时间
  String time;

  /// 标题
  String title;

  NewsGetListABizModel({
    this.author = "",
    this.coverImg = "",
    this.id = 0.0,
    this.jumpUrl = "",
    this.time = "",
    this.title = "",
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}

