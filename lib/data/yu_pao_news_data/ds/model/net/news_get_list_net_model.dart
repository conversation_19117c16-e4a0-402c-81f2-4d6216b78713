import 'dart:convert';
import 'package:json_annotation/json_annotation.dart';

import '../../../repo/model/news_get_list_biz_model.dart';

@JsonSerializable()
class NewsGetListNetModel {
  List<NewsGetListANetModel>? list;

  NewsGetListNetModel();

  Map<String, dynamic> toJson(NewsGetListNetModel instance) {
    var map = <String, Object>{};
    if (list != null) map["list"] = list!;
    return map;
  }

  factory NewsGetListNetModel.fromJson(Map<String, dynamic> json) {
    var netModel = NewsGetListNetModel();
    netModel.list = (json["list"] as List<dynamic>?)
        ?.map((e) => NewsGetListANetModel.fromJson(e as Map<String, dynamic>))
        .toList();
    return netModel;
  }

  NewsGetListBizModel transform() {
    return NewsGetListBizModel(
      list: list?.map((e) => e.transform()).toList() ?? [],
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class NewsGetListANetModel {
  /// 作者
  String? author;

  /// 图片链接
  String? cover_img;
  double? id;

  /// 跳转链接
  String? jump_url;

  /// 发布时间
  String? time;

  /// 标题
  String? title;

  NewsGetListANetModel();

  Map<String, dynamic> toJson(NewsGetListANetModel instance) {
    var map = <String, Object>{};
    if (author != null) map["author"] = author!;
    if (cover_img != null) map["cover_img"] = cover_img!;
    if (id != null) map["id"] = id!;
    if (jump_url != null) map["jump_url"] = jump_url!;
    if (time != null) map["time"] = time!;
    if (title != null) map["title"] = title!;
    return map;
  }

  factory NewsGetListANetModel.fromJson(Map<String, dynamic> json) {
    var netModel = NewsGetListANetModel();
    netModel.author = json["author"]?.toString();
    netModel.cover_img = json["cover_img"]?.toString();
    netModel.id = double.tryParse(json["id"].toString());
    netModel.jump_url = json["jump_url"]?.toString();
    netModel.time = json["time"]?.toString();
    netModel.title = json["title"]?.toString();
    return netModel;
  }

  NewsGetListABizModel transform() {
    return NewsGetListABizModel(
      author: author ?? "",
      coverImg: cover_img ?? "",
      id: id ?? 0.0,
      jumpUrl: jump_url ?? "",
      time: time ?? "",
      title: title ?? "",
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}
