class NewsGetListParamModel {

  /// 
  final String? page;

  /// 来源 1-首页滚动新闻 为1时会额外判断用户注册时间 大于三天则不展示新闻
  final String? from;

  final String? pages;

  final String? pageSize;


  NewsGetListParamModel({
    this.page,
    this.from,
    this.pages,
    this.pageSize,
  });

  Map<String, dynamic> toMap() {
    var map = <String, Object>{};
      if (page != null) map["page"] = page!;
      if (from != null) map["from"] = from!;
      if (pages != null) map["pages"] = pages!;
      if (pageSize != null) map["pageSize"] = pageSize!;
    return map;
  }
}

