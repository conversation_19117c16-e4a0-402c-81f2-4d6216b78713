import 'package:gdjg_pure_flutter/data/yu_pao_news_data/ds/model/net/news_get_list_net_model.dart';
import 'package:gdjg_pure_flutter/data/yu_pao_news_data/ds/model/param/news_get_list_param_model.dart';
import 'package:net_plugin/pigeon/api_generated/net_channel.g.dart';
import 'package:net_plugin/pigeon/net_result/resp_result.dart';
import 'package:net_plugin/pigeon/net_service/net_core.dart';

/// 鱼泡资讯RDS
class YuPaoNewsRDS {
  /// 获取鱼泡资讯列表
  Future<RespResult<NewsGetListNetModel>> getNewsList(
      NewsGetListParamModel param) async {
    return await NetCore.requestJGPHP(
        BaseBizRequestEntity(
            url: '/api/v3/yupao/news/get_list',
            method: HTTP_METHOD.GET,
            content: param.toMap().cast(),
            requestExtra: RequestExtra(showLoading: false,printResp: true)),
        (json) => NewsGetListNetModel.fromJson(json));
  }
}
