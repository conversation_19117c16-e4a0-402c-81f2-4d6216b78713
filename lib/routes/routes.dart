import 'package:gdjg_pure_flutter/feature/bill_flow/page/note_page.dart';
import 'package:gdjg_pure_flutter/feature/group/worker_selector/worker_selector_page.dart';
import 'package:gdjg_pure_flutter/feature/cloud_album/group/page/cloud_album_group_page.dart';
import 'package:gdjg_pure_flutter/feature/worker/worker_setting/worker_setting_page.dart';
import 'package:get/get.dart';

import '../feature/group/binding/group_binding.dart';
import '../feature/group/page/invite_worker_page.dart';
import '../feature/group/page/worker_resume_page.dart';
import '../feature/tabbar/bottom_tabbar_page.dart';
import '../feature/worker/binding/worker_binding.dart';

abstract class Routes {
  static const main = '/';
  static const worker = '/worker';
  static const group = '/group';
  static const workerCalendar = '/worker/calendar';
  static const note = '/bill_flow/note';
  static const cloudAlbumGroup = '/cloud_album/group';
  static const inviteWorker = '/worker/invite';
  static const workerSetting = '/group/worker_setting';
  static const workerResume = '/group/worker_resume';
  static const workerSelector = '/group/worker_selector';
}

class AppPages {
  // 初始路由
  static const initial = Routes.main;

  static final routes = [
    GetPage(
      name: Routes.main,
      page: () => const BottomTabBarPage(),
      bindings: [
        WorkerBinding(),
        GroupBinding(),
      ],
    ),
    // GetPage(name: Routes.note, page: ()=> NotePage()),
    GetPage(name: Routes.cloudAlbumGroup, page: () => CloudAlbumGroupPage()),
    GetPage(name: Routes.note, page: () => NotePage()),
    GetPage(name: Routes.inviteWorker, page: () => const InviteWorkerPage()),
    GetPage(name: Routes.workerSetting, page: () => WorkerSettingPage()),
    GetPage(name: Routes.workerResume, page: () => const WorkerResumePage()),
    GetPage(name: Routes.workerSelector, page: () => WorkerSelectorPage()),
    // GetPage(
    //   name: Routes.worker,
    //   page: () => const WorkerPage(),
    //   binding: WorkerBinding(),
    //   transition: Transition.noTransition,
    // ),
    // GetPage(
    //   name: Routes.group,
    //   page: () => const GroupPage(),
    //   binding: GroupBinding(),
    //   transition: Transition.noTransition,
    // ),
  ];

// 使用方法
// Get.toNamed(Routes.worker);
// Get.toNamed(Routes.group);
// Get.toNamed(Routes.worker, arguments: {"name": 'xxx'});
}
