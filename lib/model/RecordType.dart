
enum RecordType {
  /// 1工天（点工）
  workDays(1),
  /// 2记工量
  workLoad(2),
  /// 3日结（短工）
  dailyWages(3),
  /// 4借支
  debt(4),
  /// 5支出
  expenditure(5),
  /// 6（包工）
  packageWork(6),
  /// 7（小时工）
  hourlyWork(7),
  /// 8 收入
  incomeLast(8),
  /// 9 工资 （结算和未结是这个类型）
  wageLast(9),
  /// 10 费用
  expense(10);

  final int value;

  const RecordType(this.value);
}

enum RwaRecordType {
  workDays(RecordType.workDays, '点工'),
  workLoad(RecordType.workLoad, '工量'),
  dailyWages(RecordType.dailyWages, '短工'),
  debt(RecordType.debt, '借支'),
  expenditure(RecordType.expenditure, '支出'),
  packageWork(RecordType.packageWork, '包工'),
  hourlyWork(RecordType.hourlyWork, '小时工'),
  incomeLast(RecordType.incomeLast, '收入'),
  wageLast(RecordType.wageLast, '工资'),
  expense(RecordType.expense, '其它费用');

  final RecordType code;
  final String label;

  const RwaRecordType(this.code, this.label);
}


enum AccountRecordType {
  debt(RecordType.debt, '借支'),
  wageLast(RecordType.wageLast, '结算');

  final RecordType code;
  final String label;

  const AccountRecordType(this.code, this.label);
}
