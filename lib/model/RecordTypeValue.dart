class RecordTypeValue {
  //1工天（点工）
  static final int WORK_AND_ACCOUNT_TYPE_DAYS = 1;

  // 2记工量 
  static final int WORK_AND_ACCOUNT_TYPE_LOAD = 2;

  // 3日结（短工） 
  static final int WORK_AND_ACCOUNT_TYPE_WAGES = 3;

  // 4借支 
  static final int WORK_AND_ACCOUNT_TYPE_DEBT = 4;

  // 5支出 
  static final int WORK_AND_ACCOUNT_TYPE_EXPENDITURE = 5;

  // 6（包工） 
  static final int WORK_AND_ACCOUNT_TYPE_PACKAGE = 6;

  // 7（小时工） 
  static final int WORK_AND_ACCOUNT_TYPE_HOURS = 7;

  // 8 收入 
  static final int WORK_AND_ACCOUNT_TYPE_INCOME_LAST = 8;

  // 9 工资 （结算和未结是这个类型） 
  static final int WORK_AND_ACCOUNT_TYPE_WAGE_LAST = 9;

  // 10 费用 
  static final int WORK_AND_ACCOUNT_TYPE_EXPENSE = 10;
}