import 'package:flutter/cupertino.dart';
import 'package:gdjg_pure_flutter/feature/account/login/last_login/last_login_page.dart';
import 'package:gdjg_pure_flutter/feature/account/role/select_role_page.dart';
import 'package:gdjg_pure_flutter/feature/account/transfer/transfer_work_page.dart';
import 'package:gdjg_pure_flutter/feature/demo_setting_feature/page/my_routeInterceptor.dart';
import 'package:gdjg_pure_flutter/feature/demo_setting_feature/page/name_page.dart';
import 'package:gdjg_pure_flutter/feature/demo_setting_feature/page/setting_page.dart';
import 'package:gdjg_pure_flutter/feature/demo_setting_feature/page/third_page.dart';
import 'package:gdjg_pure_flutter/feature/demo_setting_feature/page/version_page.dart';
import 'package:gdjg_pure_flutter/feature/group/common_page/select_type_page/select_type_page.dart';
import 'package:gdjg_pure_flutter/feature/group/contact/contact_page.dart';
import 'package:gdjg_pure_flutter/feature/group/group_edit_wage/group_edit_wage_page.dart';
import 'package:gdjg_pure_flutter/feature/group/group_pro_bill/group_pro_bill_page.dart';
import 'package:gdjg_pure_flutter/feature/group/group_pro_calendar/group_pro_calendar_page.dart';
import 'package:gdjg_pure_flutter/feature/group/group_pro_statistics/group_pro_statistics_page.dart';
import 'package:gdjg_pure_flutter/feature/group/group_un_liquidated/group_un_liquidated_page.dart';
import 'package:gdjg_pure_flutter/feature/media_viewer/media_viewer_page.dart';
import 'package:gdjg_pure_flutter/feature/group/page/invite_worker_page.dart';
import 'package:gdjg_pure_flutter/feature/group/page/worker_resume_page.dart';
import 'package:gdjg_pure_flutter/feature/group/phone_contact/phone_contact_page.dart';
import 'package:gdjg_pure_flutter/feature/group/worker_selector/worker_selector_page.dart';
import 'package:gdjg_pure_flutter/feature/test/page/jg_test_page.dart';
import 'package:gdjg_pure_flutter/feature/worker/worker_setting/worker_setting_page.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/utils/web_util/web_page.dart';
import 'package:get/get.dart';

import '../feature/account/login/code_login/login_page.dart';
import '../feature/tabbar/main_page.dart';
import '../utils/route_util/route_core/yp_page_route.dart';

//各个独立的业务域自己添加对应的方法和路由
void initRoute() {
  _initRouteInterceptor();
  _initMainRoute();
  _initSettingRoutes();
  _initAccountRoutes();
}

Map<String, WidgetBuilder> getInitRouteMap() {
  Map<String, WidgetBuilder> initRoute = {};
  initRoute.addIf(true, appInitRoute.routeName, appInitRoute.widgetBuilder);
  return initRoute;
}

getInitRouteName() {
  return appInitRoute.routeName;
}

YPPageRoute appInitRoute = YPPageRoute(
  routeName: RouteNameCollection.main,
  widgetBuilder: (context) => const MainPage(),
);

void _initMainRoute() {
  YPRoute.registerRoute(appInitRoute);
}

void _initSettingRoutes() {
  YPRoute.registerRoutes([
    YPPageRoute(
      routeName: RouteNameCollection.setting,
      widgetBuilder: (context) => SettingPage(),
    ),
    YPPageRoute(
        routeName: RouteNameCollection.name,
        widgetBuilder: (context) => NamePage(),
        isSingleTask: true),
    YPPageRoute(
      routeName: RouteNameCollection.version,
      widgetBuilder: (context) => VersionPage(),
    ),
    YPPageRoute(
      routeName: RouteNameCollection.test,
      widgetBuilder: (context) => JGTestPage(),
    ),
    YPPageRoute(
      routeName: RouteNameCollection.third,
      widgetBuilder: (context) => ThirdPage(),
    ),
    YPPageRoute(
      routeName: RouteNameCollection.mediaViewer,
      widgetBuilder: (context) => MediaViewerPage(),
    ),
    /*************************** 班组的在这下边 ****************************/
    YPPageRoute(
      routeName: RouteNameCollection.selectRecordType,
      widgetBuilder: (context) => SelectTypePage(),
    ),
    YPPageRoute(
      routeName: RouteNameCollection.groupProCalendar,
      widgetBuilder: (context) => GroupProCalendarPage(),
    ),
    YPPageRoute(
      routeName: RouteNameCollection.groupProStatistics,
      widgetBuilder: (context) => GroupProStatisticsPage(),
    ),
    YPPageRoute(
      routeName: RouteNameCollection.groupProBill,
      widgetBuilder: (context) => GroupProBillPage(),
    ),
    YPPageRoute(
      routeName: RouteNameCollection.contact,
      widgetBuilder: (context) => ContactPage(),
    ),
    YPPageRoute(
      routeName: RouteNameCollection.liquidated,
      widgetBuilder: (context) => GroupUnLiquidatedPage(),
    ),
    YPPageRoute(
      routeName: RouteNameCollection.groupProEditWage,
      widgetBuilder: (context) => GroupEditWagePage(),
    ),
    YPPageRoute(
        routeName: RouteNameCollection.inviteWorker,
        widgetBuilder: (context) => InviteWorkerPage()),
    YPPageRoute(
        routeName: RouteNameCollection.workerSetting,
        widgetBuilder: (context) => WorkerSettingPage()),
    YPPageRoute(
        routeName: RouteNameCollection.workerSelector,
        widgetBuilder: (context) => WorkerSelectorPage()),
    YPPageRoute(
        routeName: RouteNameCollection.workerResume,
        widgetBuilder: (context) => WorkerResumePage()),
    YPPageRoute(
        routeName: RouteNameCollection.phoneContact,
        widgetBuilder: (context) => PhoneContactPage()),
    /*************************** 班组的在这上边 ****************************/

    /*************************** web的在这下边 ****************************/
    YPPageRoute(
      routeName: RouteNameCollection.webPage,
      widgetBuilder: (context) => WebPage(),
    ),
    /*************************** web的在这上边 ****************************/
  ]);
}

_initAccountRoutes() {
  YPRoute.registerRoutes([
    YPPageRoute(
        routeName: RouteNameCollection.login,
        widgetBuilder: (context) => LoginPage()),
    YPPageRoute(
        routeName: RouteNameCollection.lastLogin,
        widgetBuilder: (context) => LastLoginPage()),
    YPPageRoute(
        routeName: RouteNameCollection.selectRole,
        widgetBuilder: (context) => SelectRolePage()),
    YPPageRoute(
        routeName: RouteNameCollection.transferWork,
        widgetBuilder: (context) => TransferWorkPage()),
  ]);
}

void _initRouteInterceptor() {
  YPRoute.registerInterceptor(MyRouteInterceptor());
}

class RouteNameCollection {
  //============路由名字需要 / 开头，名字全小写，太长用下划线连接==========================

  static const String main = "/main";
  static const String webPage = "/web/web_page";
  static const String setting = "/user_center/setting";
  static const String name = "/user_center/name";
  static const String version = "/user_center/version";

  static const test = '/test';
  static const String third = "/user_center/third";
  static const String mediaViewer = "/media/viewer";

  static const String login = '/account/login';
  static const String lastLogin = '/account/login/last_login';
  static const String selectRole = '/account/role';
  static const String transferWork = '/account/transfer';

  /*************************** 班组的在这下边 ****************************/

  ///通讯录页面
  static const contact = "/user_center/contact";

  ///类型选择页面
  static const selectRecordType = '/select_record_type';

  ///班组项目日历页
  static const groupProCalendar = '/group/pro_calendar';

  ///班组项目统计页面
  static const groupProStatistics = '/group/pro_statistics';

  ///班组流水页面
  static const groupProBill = '/group/pro_bill';

  ///修改工资
  static const groupProEditWage = '/group/edit_wage';

  /// 邀请工友
  static const inviteWorker = '/worker/invite';

  /// 工友设置
  static const workerSetting = '/group/worker_setting';

  /// 工友名片
  static const workerResume = '/group/worker_resume';

  /// 工友选择
  static const workerSelector = '/group/worker_selector';

  /// 手机联系人
  static const phoneContact = '/group/phone_contact';

  /// 未结
  static const liquidated = "/group/liquidated";
/*************************** 班组的在这上边 ****************************/
}
