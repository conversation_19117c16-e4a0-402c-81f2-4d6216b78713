import 'package:flutter/cupertino.dart';
import 'package:gdjg_pure_flutter/feature/account/login/last_login/last_login_page.dart';
import 'package:gdjg_pure_flutter/feature/account/role/select_role_page.dart';
import 'package:gdjg_pure_flutter/feature/account/transfer/transfer_work_page.dart';
import 'package:gdjg_pure_flutter/feature/bill_flow/page/worker_account_record_page.dart';
import 'package:gdjg_pure_flutter/feature/cloud_album/export_record/export_record_page.dart';
import 'package:gdjg_pure_flutter/feature/cloud_album/group/group_cloud_album/group_cloud_album_page.dart';
import 'package:gdjg_pure_flutter/feature/cloud_album/group/image_viewer/image_viewer_page.dart';
import 'package:gdjg_pure_flutter/feature/common_page/change_project_name/change_project_name_page.dart';
import 'package:gdjg_pure_flutter/feature/common_page/notes/notes_page.dart';
import 'package:gdjg_pure_flutter/feature/common_page/personal_record_work/personal_record_work_page.dart';
import 'package:gdjg_pure_flutter/feature/demo_setting_feature/page/my_routeInterceptor.dart';
import 'package:gdjg_pure_flutter/feature/demo_setting_feature/page/name_page.dart';
import 'package:gdjg_pure_flutter/feature/demo_setting_feature/page/setting_page.dart';
import 'package:gdjg_pure_flutter/feature/demo_setting_feature/page/third_page.dart';
import 'package:gdjg_pure_flutter/feature/demo_setting_feature/page/version_page.dart';
import 'package:gdjg_pure_flutter/feature/group/common_page/select_type_page/select_type_page.dart';
import 'package:gdjg_pure_flutter/feature/group/contact/contact_page.dart';
import 'package:gdjg_pure_flutter/feature/group/group_edit_record_work/group_edit_record_work_page.dart';
import 'package:gdjg_pure_flutter/feature/group/group_edit_wage/group_edit_wage_page.dart';
import 'package:gdjg_pure_flutter/feature/group/group_liquidated/group_liquidated_page.dart';
import 'package:gdjg_pure_flutter/feature/group/group_liquidated_detail/group_liquidated_detail_page.dart';
import 'package:gdjg_pure_flutter/feature/group/group_pro_bill/group_pro_bill_page.dart';
import 'package:gdjg_pure_flutter/feature/group/group_pro_calendar/group_pro_calendar_page.dart';
import 'package:gdjg_pure_flutter/feature/group/group_pro_statistics/group_pro_statistics_page.dart';
import 'package:gdjg_pure_flutter/feature/group/group_settlement/group_settlement_page.dart';
import 'package:gdjg_pure_flutter/feature/group/page/invite_worker_page.dart';
import 'package:gdjg_pure_flutter/feature/group/page/worker_resume_page.dart';
import 'package:gdjg_pure_flutter/feature/group/phone_contact/phone_contact_page.dart';
import 'package:gdjg_pure_flutter/feature/group/worker_manager/worker_selector/worker_selector_page.dart';
import 'package:gdjg_pure_flutter/feature/group/worker_manager/worker_setting/worker_setting_page.dart';
import 'package:gdjg_pure_flutter/feature/media_viewer/media_viewer_page.dart';
import 'package:gdjg_pure_flutter/feature/test/page/jg_test_page.dart';
import 'package:gdjg_pure_flutter/feature/transfer_record_work/transfer_record_work_page.dart';
import 'package:gdjg_pure_flutter/feature/worker/invite_join/invite_name/invite_name_page.dart';
import 'package:gdjg_pure_flutter/feature/worker/project_detail/my_create_project_detail/my_create_project_detail_page.dart';
import 'package:gdjg_pure_flutter/feature/worker/project_setup/my_created_project_setup/my_created_project_setup_page.dart';
import 'package:gdjg_pure_flutter/feature/worker/project_setup/my_participated_project_setup/my_participated_project_setup_page.dart';
import 'package:gdjg_pure_flutter/feature/worker/qr_scan/qr_scan_page.dart';
import 'package:gdjg_pure_flutter/feature/worker/invite_join/invite_join_page.dart';
import 'package:gdjg_pure_flutter/feature/worker/record_workpoints/personal_record_workpoints_page.dart';
import 'package:gdjg_pure_flutter/feature/worker/worker_calendar/daily_flow/daily_flow_page.dart';
import 'package:gdjg_pure_flutter/feature/worker/worker_flow/batch_delete/batch_delete_page.dart';
import 'package:gdjg_pure_flutter/feature/worker/worker_flow/choose_project/choose_project_page.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/utils/web_util/web_page.dart';
import 'package:get/get.dart';

import '../feature/account/login/code_login/login_page.dart';
import '../feature/tabbar/main_page.dart';
import '../utils/route_util/route_core/yp_page_route.dart';

//各个独立的业务域自己添加对应的方法和路由
void initRoute() {
  _initRouteInterceptor();
  _initMainRoute();
  _initSettingRoutes();
  _initAccountRoutes();
}

Map<String, WidgetBuilder> getInitRouteMap() {
  Map<String, WidgetBuilder> initRoute = {};
  initRoute.addIf(true, appInitRoute.routeName, appInitRoute.widgetBuilder);
  return initRoute;
}

getInitRouteName() {
  return appInitRoute.routeName;
}

YPPageRoute appInitRoute = YPPageRoute(
  routeName: RouteNameCollection.main,
  widgetBuilder: (context) => const MainPage(),
);

void _initMainRoute() {
  YPRoute.registerRoute(appInitRoute);
}

void _initSettingRoutes() {
  YPRoute.registerRoutes([
    YPPageRoute(
      routeName: RouteNameCollection.setting,
      widgetBuilder: (context) => SettingPage(),
    ),
    YPPageRoute(
        routeName: RouteNameCollection.name,
        widgetBuilder: (context) => NamePage(),
        isSingleTask: true),
    YPPageRoute(
      routeName: RouteNameCollection.version,
      widgetBuilder: (context) => VersionPage(),
    ),
    YPPageRoute(
      routeName: RouteNameCollection.test,
      widgetBuilder: (context) => JGTestPage(),
    ),
    YPPageRoute(
      routeName: RouteNameCollection.personalRecordWorkPoints,
      widgetBuilder: (context) => PersonalRecordWorkPoints(),
    ),
    YPPageRoute(
      routeName: RouteNameCollection.third,
      widgetBuilder: (context) => ThirdPage(),
    ),
    YPPageRoute(
      routeName: RouteNameCollection.mediaViewer,
      widgetBuilder: (context) => MediaViewerPage(),
    ),
    YPPageRoute(
      routeName: RouteNameCollection.batchDelete,
      widgetBuilder: (context) => BatchDeletePage(),
    ),
    YPPageRoute(
      routeName: RouteNameCollection.chooseProject,
      widgetBuilder: (context) => ChooseProjectPage(),
    ),
    YPPageRoute(
        routeName: RouteNameCollection.personalRecordWork,
        widgetBuilder: (context) => PersonalRecordWorkPage()),
    YPPageRoute(
      routeName: RouteNameCollection.changeProjectName,
      widgetBuilder: (context) => ChangeProjectNamePage(),
    ),
    YPPageRoute(
        routeName: RouteNameCollection.dailyFlow, widgetBuilder: (context) => DailyFlowPage()),
    YPPageRoute(
        routeName: RouteNameCollection.projectDetail,
        widgetBuilder: (context) => MyCreateProjectDetailPage()),

    /*************************** 公用页面在这下边 ****************************/
    YPPageRoute(
      routeName: RouteNameCollection.selectRecordType,
      widgetBuilder: (context) => SelectTypePage(),
    ),
    YPPageRoute(
      routeName: RouteNameCollection.notes,
      widgetBuilder: (context) => NotesPage(),
    ),
    YPPageRoute(
      routeName: RouteNameCollection.transferRecord,
      widgetBuilder: (context) => TransferRecordWorkPage(),
    ),
    YPPageRoute(
      routeName: RouteNameCollection.exportRecord,
      widgetBuilder: (context) => ExportRecordPage(),
    ),

    /*************************** 公用页面在这上边 ****************************/

    /*************************** 班组的在这下边 ****************************/

    YPPageRoute(
      routeName: RouteNameCollection.groupProCalendar,
      widgetBuilder: (context) => GroupProCalendarPage(),
    ),
    YPPageRoute(
      routeName: RouteNameCollection.groupProStatistics,
      widgetBuilder: (context) => GroupProStatisticsPage(),
    ),
    YPPageRoute(
      routeName: RouteNameCollection.groupProBill,
      widgetBuilder: (context) => GroupProBillPage(),
    ),
    YPPageRoute(
      routeName: RouteNameCollection.groupProEditRecordWork,
      widgetBuilder: (context) => GroupEditRecordWorkPage(),
    ),
    YPPageRoute(
      routeName: RouteNameCollection.contact,
      widgetBuilder: (context) => ContactPage(),
    ),
    YPPageRoute(
      routeName: RouteNameCollection.liquidated,
      widgetBuilder: (context) => GroupLiquidatedPage(),
    ),
    YPPageRoute(
      routeName: RouteNameCollection.liquidatedDetail,
      widgetBuilder: (context) => GroupLiquidatedDetailPage(),
    ),
    YPPageRoute(
      routeName: RouteNameCollection.groupSettlement,
      widgetBuilder: (context) => GroupSettlementPage(),
    ),
    YPPageRoute(
      routeName: RouteNameCollection.groupProEditWage,
      widgetBuilder: (context) => GroupEditWagePage(),
    ),
    YPPageRoute(
        routeName: RouteNameCollection.inviteWorker,
        widgetBuilder: (context) => InviteWorkerPage()),
    YPPageRoute(
        routeName: RouteNameCollection.workerSetting,
        widgetBuilder: (context) => WorkerSettingPage()),
    YPPageRoute(
        routeName: RouteNameCollection.workerSelector,
        widgetBuilder: (context) => WorkerSelectorPage()),
    YPPageRoute(
        routeName: RouteNameCollection.workerResume,
        widgetBuilder: (context) => WorkerResumePage()),
    YPPageRoute(
        routeName: RouteNameCollection.phoneContact,
        widgetBuilder: (context) => PhoneContactPage()),
    YPPageRoute(
      routeName: RouteNameCollection.contact,
      widgetBuilder: (context) => ContactPage(),
    ),
    YPPageRoute(
        routeName: RouteNameCollection.projectSetup,
        widgetBuilder: (context) => MyCreatedProjectSetupPage()),
    YPPageRoute(
        routeName: RouteNameCollection.participatedProjectSetup,
        widgetBuilder: (context) => MyParticipatedProjectSetupPage()),
    YPPageRoute(routeName: RouteNameCollection.qrScan, widgetBuilder: (context) => QRScanPage()),
    YPPageRoute(
        routeName: RouteNameCollection.qrScan,
        widgetBuilder: (context) => QRScanPage()),
    YPPageRoute(
        routeName: RouteNameCollection.inviteJoin,
        widgetBuilder: (context) => InviteJoinPage()),
    YPPageRoute(
        routeName: RouteNameCollection.inviteName,
        widgetBuilder: (context) => InviteNamePage()),
    /*************************** 班组的在这上边 ****************************/
    YPPageRoute(
        routeName: RouteNameCollection.personAccountRecord,
        widgetBuilder: (context) => WorkerAccountRecordPage()),

    /*************************** web的在这下边 ****************************/
    YPPageRoute(
      routeName: RouteNameCollection.webPage,
      widgetBuilder: (context) => WebPage(),
    ),
    /*************************** web的在这上边 ****************************/
  ]);
}

_initAccountRoutes() {
  YPRoute.registerRoutes([
    YPPageRoute(routeName: RouteNameCollection.login, widgetBuilder: (context) => LoginPage()),
    YPPageRoute(
        routeName: RouteNameCollection.lastLogin, widgetBuilder: (context) => LastLoginPage()),
    YPPageRoute(
        routeName: RouteNameCollection.selectRole, widgetBuilder: (context) => SelectRolePage()),
    YPPageRoute(
        routeName: RouteNameCollection.transferWork,
        widgetBuilder: (context) => TransferWorkPage()),
    YPPageRoute(
        routeName: RouteNameCollection.groupCloudAlbum,
        widgetBuilder: (context) => GroupCloudAlbumPage()),
    YPPageRoute(
        routeName: RouteNameCollection.imageViewer, widgetBuilder: (context) => ImageViewerPage()),
  ]);
}

void _initRouteInterceptor() {
  YPRoute.registerInterceptor(MyRouteInterceptor());
}

class RouteNameCollection {
  //============路由名字需要 / 开头，名字全小写，太长用下划线连接==========================

  static const String main = "/main";
  static const String webPage = "/web/web_page";
  static const String setting = "/user_center/setting";
  static const String name = "/user_center/name";
  static const String version = "/user_center/version";

  static const test = '/test';
  static const personalRecordWorkPoints = '/personal/personal_record_workpoints';
  static const String third = "/user_center/third";
  static const String mediaViewer = "/media/viewer";
  static const String batchDelete = "/batch/delete";
  static const String chooseProject = "/choose/project";
  static const String changeProjectName = "/common/change_project_name";

  static const String login = 'account/login';
  static const String lastLogin = 'account/login/last_login';
  static const String selectRole = 'account/role';
  static const String transferWork = 'account/transfer';
  static const String dailyFlow = "/calendar/daily_flow";

  /*************************** 公用页面这下边 ****************************/

  /// 添加备注页面
  static const notes = '/common/notes';

  ///类型选择页面
  static const selectRecordType = '/common/select_record_type';

  /// 转移记工页面
  static const transferRecord = '/common/transfer_record';

  /*************************** 公用页面在这上边 ****************************/

  /*************************** 班组的在这下边 ****************************/

  ///通讯录页面
  static const contact = "/user_center/contact";

  ///班组项目日历页
  static const groupProCalendar = '/group/pro_calendar';

  ///班组项目统计页面
  static const groupProStatistics = '/group/pro_statistics';

  ///班组流水页面
  static const groupProBill = '/group/pro_bill';

  ///修改班组项目记工
  static const groupProEditRecordWork = '/group/edit_record_work';

  ///修改工资
  static const groupProEditWage = '/group/edit_wage';

  /// 邀请工友
  static const inviteWorker = '/worker/invite';

  /// 工友设置
  static const workerSetting = '/group/worker_setting';

  /// 工友名片
  static const workerResume = '/group/worker_resume';

  /// 工友选择
  static const workerSelector = '/group/worker_selector';

  /// 手机联系人
  static const phoneContact = '/group/phone_contact';

  /// 未结列表
  static const liquidated = "/group/liquidated";

  /// 未结详情
  static const liquidatedDetail = "/group/liquidated_detail";

  /// 未结 记结算
  static const groupSettlement = "/group/settlement";

  ///班组云相册页面
  static const groupCloudAlbum = '/group/cloud_album';

  ///班组回收站
  static const groupRecycle = '/group/recycle';

  /*************************** 班组的在这上边 ****************************/
  /// 项目详情
  static const projectDetail = '/worker/project_detail';

  /// 工人创建项目设置
  static const projectSetup = '/worker/project_setup';

  /// 工人参与项目设置
  static const participatedProjectSetup = '/worker/participated_project_setup';

  /// 扫码页面
  static const qrScan = '/worker/qr_scan';

  /// 邀请加入页面
  static const inviteJoin = '/worker/invite_join';

  /// 邀请姓名填写页面
  static const inviteName = '/worker/invite_name';

  /// 个人记工
  static const personalRecordWork = '/common/personal_record_work';

  /// 图片查看
  static const imageViewer = '/worker/image_viewer';

  /// 图片查看
  static const personAccountRecord = '/person/account_record';

  /// 导出打卡记录
  static const exportRecord = '/export/record';
}
