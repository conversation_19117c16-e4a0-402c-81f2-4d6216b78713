

import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:gdjg_pure_flutter/utils/app_init_util/app_init_util.dart';

import 'init_route.dart';

Future<void> initStart() async {
  initRoute();
  await AppInitUtil.appInit();
  _initSmartDialogConfig();
}

/// SmartDialog配置
void _initSmartDialogConfig() {
  SmartDialog.config
    ..toast = SmartConfigToast(
      usePenetrate: true, // 使 Toast 穿透背景，不会被键盘遮挡
    );
}