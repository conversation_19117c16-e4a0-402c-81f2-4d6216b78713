import 'package:flutter/widgets.dart';
import 'package:flutter_svg/flutter_svg.dart';

enum IconNames {
  saasSassRecord, saasJian, saasFuzhi, saasUploadIcon, saasRecordVideoIcon, saasAuthIcon, saasVector, saasBianzu10, saasPaishezhaopian, saasXiangceshangchuan, saasTuichang, saasOcr, saasCopy, saasServiceTel, saasFileIcon, saasAddressLine, saasIconMianYinsizhengce, saasIconMianYinsianquan, saasIconXianYinsizhengce, saasIconXianYinsibaohu, saasYupaoMachine, saasYupaoResume, saasYupaoBuilding, saasClockIcon, saasBellIcon, saasIconLoading, saasIconUserBuilding, saasWorkerOnline, saasWenhao, saasNewsIcon, saasHasnotSign, saasOverdueSign, saasAlreadySign, saasFloatContract, saasAddProject, saasAttendanceAccount, saasCloudAlbum, saasRadioActive, saasRadio, saasIocnProjectMannger, saasAIconxiangongchengguanli, saasShanchu, saasShanchu1, saasCheckFlow, saasClean1, saasAccountManage, saasContract, saasClock, saasClock2, saasCheck, saasWarningCircleFill, saasVideo, saasFeedback, saasShare, saasChange2, saasPlusRound, saasSubRound, saasQrCode, saasDownload1, saasWorkerBook, saasWorkerAdd, saasHandle, saasImage, saasDaiban, saasSettingSolid, saasDaka, saasTongji, saasShigong, saasDoubleArrow, saasDownload, saasTable, saasDuizhangdan, saasZhaoren, saasQuannianshouzhi, saasTabOutstandingActive, saasTabOutstanding, saasChange1, saasTabStatisticsActive, saasTabCalendarActive, saasPlus, saasTabWaterActive, saasTabProjectsActive, saasTabWater, saasTabCalendar, saasTabStatistics, saasTabProjects, saasProjectIcon, saasNextSolid, saasPrevSolid, saasProject, saasTriangle, saasReset, saasChange, saasInput, saasWarning, saasChart, saasStatistics, saasEyeOpen, saasShaixuan, saasCheckboxGou, saasInfo, saasTabbarJoinActive, saasTabbarProfileActive, saasTabbarWorker, saasReduce, saasQrcode, saasCamera1, saasClean, saasDelete, saasSaoma, saasPersonnel, saasBackTop, saasXiangpian, saasIconRili, saasGou1, saasDel, saasXuanzhong, saasWeixin1, saasShuaxin, saasNormalFee, saasMoney, saasSalary, saasBonus, saasAIconYibanzhichubeifen19, saasOther, saasRili, saasWarn, saasDingwei, saasAddress, saasTianjia, saasPhone1, saasDuoren, saasKeyDelete, saasEditPen, saasPrev, saasArrowBottom, saasNext, saasHand, saasWages, saasMore, saasRecover, saasEyeClose, saasCode, saasLock1, saasEye, saasWechat, saasEdit, saasSetting, saasSearch, saasLock, saasClose, saasStar, saasJiantouxia, saasGou, saasArrowLeft, saasArrowRight
}

extension IconNamesExtension on IconNames {
  String get name => toString().split('.').last;
}

/// IconFont widget for iconfont.cn icons
class IconFont extends StatelessWidget {
  const IconFont(
    this.iconName, {
    super.key,
    this.size = 18,
    this.color,
    this.colors,
  });

  final dynamic iconName;
  final double size;
  final String? color;
  final List<String>? colors;

  IconNames get _iconName => _getIconNames(iconName);

  static IconNames _getIconNames(dynamic iconName) {
    if (iconName is IconNames) return iconName;
    
    switch (iconName.toString()) {
      case 'saasSassRecord':
        return IconNames.saasSassRecord;
      case 'saasJian':
        return IconNames.saasJian;
      case 'saasFuzhi':
        return IconNames.saasFuzhi;
      case 'saasUploadIcon':
        return IconNames.saasUploadIcon;
      case 'saasRecordVideoIcon':
        return IconNames.saasRecordVideoIcon;
      case 'saasAuthIcon':
        return IconNames.saasAuthIcon;
      case 'saasVector':
        return IconNames.saasVector;
      case 'saasBianzu10':
        return IconNames.saasBianzu10;
      case 'saasPaishezhaopian':
        return IconNames.saasPaishezhaopian;
      case 'saasXiangceshangchuan':
        return IconNames.saasXiangceshangchuan;
      case 'saasTuichang':
        return IconNames.saasTuichang;
      case 'saasOcr':
        return IconNames.saasOcr;
      case 'saasCopy':
        return IconNames.saasCopy;
      case 'saasServiceTel':
        return IconNames.saasServiceTel;
      case 'saasFileIcon':
        return IconNames.saasFileIcon;
      case 'saasAddressLine':
        return IconNames.saasAddressLine;
      case 'saasIconMianYinsizhengce':
        return IconNames.saasIconMianYinsizhengce;
      case 'saasIconMianYinsianquan':
        return IconNames.saasIconMianYinsianquan;
      case 'saasIconXianYinsizhengce':
        return IconNames.saasIconXianYinsizhengce;
      case 'saasIconXianYinsibaohu':
        return IconNames.saasIconXianYinsibaohu;
      case 'saasYupaoMachine':
        return IconNames.saasYupaoMachine;
      case 'saasYupaoResume':
        return IconNames.saasYupaoResume;
      case 'saasYupaoBuilding':
        return IconNames.saasYupaoBuilding;
      case 'saasClockIcon':
        return IconNames.saasClockIcon;
      case 'saasBellIcon':
        return IconNames.saasBellIcon;
      case 'saasIconLoading':
        return IconNames.saasIconLoading;
      case 'saasIconUserBuilding':
        return IconNames.saasIconUserBuilding;
      case 'saasWorkerOnline':
        return IconNames.saasWorkerOnline;
      case 'saasWenhao':
        return IconNames.saasWenhao;
      case 'saasNewsIcon':
        return IconNames.saasNewsIcon;
      case 'saasHasnotSign':
        return IconNames.saasHasnotSign;
      case 'saasOverdueSign':
        return IconNames.saasOverdueSign;
      case 'saasAlreadySign':
        return IconNames.saasAlreadySign;
      case 'saasFloatContract':
        return IconNames.saasFloatContract;
      case 'saasAddProject':
        return IconNames.saasAddProject;
      case 'saasAttendanceAccount':
        return IconNames.saasAttendanceAccount;
      case 'saasCloudAlbum':
        return IconNames.saasCloudAlbum;
      case 'saasRadioActive':
        return IconNames.saasRadioActive;
      case 'saasRadio':
        return IconNames.saasRadio;
      case 'saasIocnProjectMannger':
        return IconNames.saasIocnProjectMannger;
      case 'saasAIconxiangongchengguanli':
        return IconNames.saasAIconxiangongchengguanli;
      case 'saasShanchu':
        return IconNames.saasShanchu;
      case 'saasShanchu1':
        return IconNames.saasShanchu1;
      case 'saasCheckFlow':
        return IconNames.saasCheckFlow;
      case 'saasClean1':
        return IconNames.saasClean1;
      case 'saasAccountManage':
        return IconNames.saasAccountManage;
      case 'saasContract':
        return IconNames.saasContract;
      case 'saasClock':
        return IconNames.saasClock;
      case 'saasClock2':
        return IconNames.saasClock2;
      case 'saasCheck':
        return IconNames.saasCheck;
      case 'saasWarningCircleFill':
        return IconNames.saasWarningCircleFill;
      case 'saasVideo':
        return IconNames.saasVideo;
      case 'saasFeedback':
        return IconNames.saasFeedback;
      case 'saasShare':
        return IconNames.saasShare;
      case 'saasChange2':
        return IconNames.saasChange2;
      case 'saasPlusRound':
        return IconNames.saasPlusRound;
      case 'saasSubRound':
        return IconNames.saasSubRound;
      case 'saasQrCode':
        return IconNames.saasQrCode;
      case 'saasDownload1':
        return IconNames.saasDownload1;
      case 'saasWorkerBook':
        return IconNames.saasWorkerBook;
      case 'saasWorkerAdd':
        return IconNames.saasWorkerAdd;
      case 'saasHandle':
        return IconNames.saasHandle;
      case 'saasImage':
        return IconNames.saasImage;
      case 'saasDaiban':
        return IconNames.saasDaiban;
      case 'saasSettingSolid':
        return IconNames.saasSettingSolid;
      case 'saasDaka':
        return IconNames.saasDaka;
      case 'saasTongji':
        return IconNames.saasTongji;
      case 'saasShigong':
        return IconNames.saasShigong;
      case 'saasDoubleArrow':
        return IconNames.saasDoubleArrow;
      case 'saasDownload':
        return IconNames.saasDownload;
      case 'saasTable':
        return IconNames.saasTable;
      case 'saasDuizhangdan':
        return IconNames.saasDuizhangdan;
      case 'saasZhaoren':
        return IconNames.saasZhaoren;
      case 'saasQuannianshouzhi':
        return IconNames.saasQuannianshouzhi;
      case 'saasTabOutstandingActive':
        return IconNames.saasTabOutstandingActive;
      case 'saasTabOutstanding':
        return IconNames.saasTabOutstanding;
      case 'saasChange1':
        return IconNames.saasChange1;
      case 'saasTabStatisticsActive':
        return IconNames.saasTabStatisticsActive;
      case 'saasTabCalendarActive':
        return IconNames.saasTabCalendarActive;
      case 'saasPlus':
        return IconNames.saasPlus;
      case 'saasTabWaterActive':
        return IconNames.saasTabWaterActive;
      case 'saasTabProjectsActive':
        return IconNames.saasTabProjectsActive;
      case 'saasTabWater':
        return IconNames.saasTabWater;
      case 'saasTabCalendar':
        return IconNames.saasTabCalendar;
      case 'saasTabStatistics':
        return IconNames.saasTabStatistics;
      case 'saasTabProjects':
        return IconNames.saasTabProjects;
      case 'saasProjectIcon':
        return IconNames.saasProjectIcon;
      case 'saasNextSolid':
        return IconNames.saasNextSolid;
      case 'saasPrevSolid':
        return IconNames.saasPrevSolid;
      case 'saasProject':
        return IconNames.saasProject;
      case 'saasTriangle':
        return IconNames.saasTriangle;
      case 'saasReset':
        return IconNames.saasReset;
      case 'saasChange':
        return IconNames.saasChange;
      case 'saasInput':
        return IconNames.saasInput;
      case 'saasWarning':
        return IconNames.saasWarning;
      case 'saasChart':
        return IconNames.saasChart;
      case 'saasStatistics':
        return IconNames.saasStatistics;
      case 'saasEyeOpen':
        return IconNames.saasEyeOpen;
      case 'saasShaixuan':
        return IconNames.saasShaixuan;
      case 'saasCheckboxGou':
        return IconNames.saasCheckboxGou;
      case 'saasInfo':
        return IconNames.saasInfo;
      case 'saasTabbarJoinActive':
        return IconNames.saasTabbarJoinActive;
      case 'saasTabbarProfileActive':
        return IconNames.saasTabbarProfileActive;
      case 'saasTabbarWorker':
        return IconNames.saasTabbarWorker;
      case 'saasReduce':
        return IconNames.saasReduce;
      case 'saasQrcode':
        return IconNames.saasQrcode;
      case 'saasCamera1':
        return IconNames.saasCamera1;
      case 'saasClean':
        return IconNames.saasClean;
      case 'saasDelete':
        return IconNames.saasDelete;
      case 'saasSaoma':
        return IconNames.saasSaoma;
      case 'saasPersonnel':
        return IconNames.saasPersonnel;
      case 'saasBackTop':
        return IconNames.saasBackTop;
      case 'saasXiangpian':
        return IconNames.saasXiangpian;
      case 'saasIconRili':
        return IconNames.saasIconRili;
      case 'saasGou1':
        return IconNames.saasGou1;
      case 'saasDel':
        return IconNames.saasDel;
      case 'saasXuanzhong':
        return IconNames.saasXuanzhong;
      case 'saasWeixin1':
        return IconNames.saasWeixin1;
      case 'saasShuaxin':
        return IconNames.saasShuaxin;
      case 'saasNormalFee':
        return IconNames.saasNormalFee;
      case 'saasMoney':
        return IconNames.saasMoney;
      case 'saasSalary':
        return IconNames.saasSalary;
      case 'saasBonus':
        return IconNames.saasBonus;
      case 'saasAIconYibanzhichubeifen19':
        return IconNames.saasAIconYibanzhichubeifen19;
      case 'saasOther':
        return IconNames.saasOther;
      case 'saasRili':
        return IconNames.saasRili;
      case 'saasWarn':
        return IconNames.saasWarn;
      case 'saasDingwei':
        return IconNames.saasDingwei;
      case 'saasAddress':
        return IconNames.saasAddress;
      case 'saasTianjia':
        return IconNames.saasTianjia;
      case 'saasPhone1':
        return IconNames.saasPhone1;
      case 'saasDuoren':
        return IconNames.saasDuoren;
      case 'saasKeyDelete':
        return IconNames.saasKeyDelete;
      case 'saasEditPen':
        return IconNames.saasEditPen;
      case 'saasPrev':
        return IconNames.saasPrev;
      case 'saasArrowBottom':
        return IconNames.saasArrowBottom;
      case 'saasNext':
        return IconNames.saasNext;
      case 'saasHand':
        return IconNames.saasHand;
      case 'saasWages':
        return IconNames.saasWages;
      case 'saasMore':
        return IconNames.saasMore;
      case 'saasRecover':
        return IconNames.saasRecover;
      case 'saasEyeClose':
        return IconNames.saasEyeClose;
      case 'saasCode':
        return IconNames.saasCode;
      case 'saasLock1':
        return IconNames.saasLock1;
      case 'saasEye':
        return IconNames.saasEye;
      case 'saasWechat':
        return IconNames.saasWechat;
      case 'saasEdit':
        return IconNames.saasEdit;
      case 'saasSetting':
        return IconNames.saasSetting;
      case 'saasSearch':
        return IconNames.saasSearch;
      case 'saasLock':
        return IconNames.saasLock;
      case 'saasClose':
        return IconNames.saasClose;
      case 'saasStar':
        return IconNames.saasStar;
      case 'saasJiantouxia':
        return IconNames.saasJiantouxia;
      case 'saasGou':
        return IconNames.saasGou;
      case 'saasArrowLeft':
        return IconNames.saasArrowLeft;
      case 'saasArrowRight':
        return IconNames.saasArrowRight;
    }
    return IconNames.values.first;
  }

  static String getColor(int index, String? color, List<String>? colors, String defaultColor) {
    if (color?.isNotEmpty == true) return color!;
    if (colors != null && colors.length > index) return colors[index];
    return defaultColor;
  }

  @override
  Widget build(BuildContext context) {
    final String svgXml;
    
    switch (_iconName) {
      case IconNames.saasSassRecord:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M162.500923 551.227077A349.499077 349.499077 0 1 1 512 900.726154H162.422154V551.227077z m-88.615385 0v393.846154c0 24.418462 19.849846 44.347077 44.347077 44.347077H512A438.114462 438.114462 0 1 0 73.806769 551.148308z m646.695385-67.111385a44.268308 44.268308 0 1 0-62.700308-62.700307L433.230769 646.144 346.427077 559.261538a44.268308 44.268308 0 1 0-62.700308 62.700308l118.153846 118.153846c17.329231 17.329231 45.371077 17.329231 62.700308 0l256-256z" fill="${getColor(0, color, colors, '#333333')}" />
          </svg>''';
        break;
      case IconNames.saasJian:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M200.764235 512a30.117647 30.117647 0 0 1 30.117647-30.117647h562.236236a30.117647 30.117647 0 0 1 0 60.235294H230.881882a30.117647 30.117647 0 0 1-30.117647-30.117647z" fill="${getColor(0, color, colors, '#333333')}" />
          </svg>''';
        break;
      case IconNames.saasFuzhi:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M256 100.224a27.712 27.712 0 0 0 0 55.488h469.312c55.424 0 100.288 44.864 100.288 100.288v512a27.712 27.712 0 1 0 55.488 0V256a155.712 155.712 0 0 0-155.776-155.776H256z" fill="${getColor(0, color, colors, '#5290FD')}" />
            <path d="M128 341.312a128 128 0 0 1 128-128h384a128 128 0 0 1 128 128v469.376a128 128 0 0 1-128 128H256a128 128 0 0 1-128-128V341.312zM256 268.8c-40.064 0-72.512 32.448-72.512 72.512v469.376c0 40.064 32.448 72.512 72.512 72.512h384c40.064 0 72.512-32.448 72.512-72.512V341.312c0-40.064-32.448-72.512-72.512-72.512H256z" fill="${getColor(1, color, colors, '#5290FD')}" />
          </svg>''';
        break;
      case IconNames.saasUploadIcon:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M483.754667 98.432A36.48 36.48 0 0 1 511.786667 85.333333h0.128c9.386667 0 18.773333 3.541333 25.941333 10.709334l219.434667 219.434666a36.565333 36.565333 0 0 1-51.712 51.712l-157.226667-157.184v545.792a36.565333 36.565333 0 0 1-73.130667 0V210.389333L318.421333 367.189333a36.565333 36.565333 0 1 1-51.712-51.712l217.045334-217.045333zM902.101333 426.666667c20.181333 0 36.565333 16.384 36.565334 36.565333v438.869333a36.565333 36.565333 0 0 1-36.565334 36.565334H121.898667A36.565333 36.565333 0 0 1 85.333333 902.101333V463.445333a36.565333 36.565333 0 1 1 73.130667 0v402.090667h707.072v-402.304c0-20.181333 16.341333-36.565333 36.565333-36.565333z" fill="${getColor(0, color, colors, '#333333')}" />
          </svg>''';
        break;
      case IconNames.saasRecordVideoIcon:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M512 938.624A426.688 426.688 0 1 0 512 85.312a426.688 426.688 0 0 0 0 853.312z" fill="${getColor(0, color, colors, '#000000')}" fill-opacity=".25" />
            <path d="M442.688 336.512a32 32 0 0 0-48 27.712v295.68a32 32 0 0 0 48 27.648l256-147.84a32 32 0 0 0 0-55.36l-256-147.84z" fill="${getColor(1, color, colors, '#FFFFFF')}" />
          </svg>''';
        break;
      case IconNames.saasAuthIcon:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M257.883776 877.230085l69.521591 122.51306c5.85137 10.459324 15.286704 18.212389 26.367736 21.796354 11.00789 3.803391 23.003199 3.181682 33.572236-1.755411L512.052663 964.744638l124.707324 55.03945c10.605608 4.937093 22.564346 5.558802 33.572236 1.755411 11.00789-3.693677 20.406653-11.446743 26.367736-21.759783l69.558162-122.51306 131.875253-30.061414a44.982407 44.982407 0 0 0 28.781426-19.492376c6.948502-9.874187 9.9839-22.125493 8.411344-34.267086l-13.202153-141.274016 89.928243-107.518925c7.826207-8.886768 12.105022-20.479795 11.995309-32.548246a47.908092 47.908092 0 0 0-11.995309-32.511675l-89.928243-107.518924 13.165582-141.310587a49.078366 49.078366 0 0 0-8.374773-34.523084 44.72641 44.72641 0 0 0-28.781426-19.236379l-131.875253-29.988271L696.699959 24.42947a48.383516 48.383516 0 0 0-26.367736-22.01578 44.762981 44.762981 0 0 0-33.572236 2.011409L512.052663 59.464548 387.345339 4.46167a43.629278 43.629278 0 0 0-33.572236-2.011409 46.44525 46.44525 0 0 0-26.367736 22.01578L257.883776 146.979102l-131.875253 29.988271a42.788144 42.788144 0 0 0-16.274123 6.619363c-4.937093 3.291396-9.179337 7.606781-12.507303 12.653587a47.396097 47.396097 0 0 0-8.411345 34.523084l13.202154 141.310587-89.928243 107.518924c-7.935921 8.777055-12.251306 20.479795-11.995309 32.548246-0.219426 12.03188 4.059388 23.698049 11.995309 32.475104l89.964814 107.518925-13.202154 141.310587a47.908092 47.908092 0 0 0 8.374774 34.267086c6.765647 10.239898 17.078686 17.1884 28.781426 19.528947l131.875253 29.988272z m144.601983-399.904573l63.414223 66.559335 154.183601-164.569783c3.986246-4.498241 8.777055-8.045634 14.153001-10.386182a38.399616 38.399616 0 0 1 16.895831-3.181682c11.812453 0.365711 23.03977 5.631944 31.268259 14.701567 7.753065 8.923339 12.178164 20.516366 12.470732 32.69453 0.255997 6.070796-0.731421 12.141593-2.925685 17.773537a42.67843 42.67843 0 0 1-9.508476 14.920993l-185.890713 198.434587a42.093293 42.093293 0 0 1-13.970146 10.386182c-5.266233 2.41369-10.971319 3.657106-16.676404 3.620535-5.705086 0-11.373601-1.206845-16.639834-3.620535a41.873867 41.873867 0 0 1-13.970146-10.386182L341.265799 543.884847a44.72641 44.72641 0 0 1-9.179336-14.994136 47.066958 47.066958 0 0 1-2.852543-17.700394c-0.219426-6.034225 0.731421-12.068451 2.815972-17.700395 2.04798-5.595373 5.193091-10.715321 9.215907-14.994136 3.986246-4.498241 8.777055-8.045634 14.153002-10.459324 5.339375-2.41369 11.081032-3.620535 16.895831-3.547393 5.595373-0.182855 11.154174 0.877706 16.347265 3.108541 5.193091 2.194264 9.874187 5.485659 13.823862 9.727902z" fill="${getColor(0, color, colors, '#FFFFFF')}" />
          </svg>''';
        break;
      case IconNames.saasVector:
        svgXml = '''
          <svg viewBox="0 0 1109 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M469.333333 469.333333a85.333333 85.333333 0 1 1-170.666666 0 85.333333 85.333333 0 0 1 170.666666 0z m640-341.333333v768a85.333333 85.333333 0 0 1-85.333333 85.333333H85.333333a85.333333 85.333333 0 0 1-85.333333-85.333333v-768a85.333333 85.333333 0 0 1 85.333333-85.333333h938.666667a85.333333 85.333333 0 0 1 85.333333 85.333333zM595.968 714.666667A212.053333 212.053333 0 0 0 504.32 590.165333a170.666667 170.666667 0 1 0-240.64 0 212.48 212.48 0 0 0-91.648 124.501334 42.666667 42.666667 0 0 0 82.602667 21.333333C268.8 681.386667 324.352 640 384 640c59.733333 0 115.285333 41.216 129.365333 96a42.666667 42.666667 0 0 0 82.602667-21.333333zM938.666667 597.333333a42.666667 42.666667 0 0 0-42.666667-42.666666H682.666667a42.666667 42.666667 0 1 0 0 85.333333h213.333333A42.666667 42.666667 0 0 0 938.666667 597.333333z m0-170.666666a42.666667 42.666667 0 0 0-42.666667-42.666667H682.666667a42.666667 42.666667 0 1 0 0 85.333333h213.333333A42.666667 42.666667 0 0 0 938.666667 426.666667z" fill="${getColor(0, color, colors, '#5290FD')}" />
          </svg>''';
        break;
      case IconNames.saasBianzu10:
        svgXml = '''
          <svg viewBox="0 0 1058 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M763.592499 330.377312H96.35355A79.287743 79.287743 0 0 0 17.065808 409.630923v502.076062a79.287743 79.287743 0 0 0 79.287742 79.287743h667.238949a79.287743 79.287743 0 0 0 79.253611-79.287743V409.630923a79.287743 79.287743 0 0 0-79.253611-79.253611z" fill="${getColor(0, color, colors, '#5290FD')}" />
            <path d="M743.727899 396.421988h-132.089351v82.598509h132.089351v-82.598509z" fill="${getColor(1, color, colors, '#F5F6FA')}" />
            <path d="M570.06624 671.966519l-186.35862 124.648659a39.626805 39.626805 0 0 1-61.675829-32.97114V514.414982a39.626805 39.626805 0 0 1 61.675829-32.937008l186.35862 124.648659a39.626805 39.626805 0 0 1 0 65.874018z" fill="${getColor(2, color, colors, '#F5F6FA')}" />
            <path d="M650.036615 644.149252l333.568277-167.313178a39.626805 39.626805 0 0 1 57.409377 35.428617v263.769123a39.626805 39.626805 0 0 1-57.409377 35.428617l-333.568277-167.313179z" fill="${getColor(3, color, colors, '#5290FD')}" />
            <path d="M512.554468 330.343181a165.162887 165.162887 0 1 0 0-330.291642 165.162887 165.162887 0 0 0 0 330.291642z" fill="${getColor(4, color, colors, '#5290FD')}" />
            <path d="M215.268098 330.343181a132.089352 132.089352 0 1 0 0-264.212835 132.089352 132.089352 0 0 0 0 264.212835z" fill="${getColor(5, color, colors, '#5290FD')}" />
          </svg>''';
        break;
      case IconNames.saasPaishezhaopian:
        svgXml = '''
          <svg viewBox="0 0 1052 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M99.555556 277.333333c0-23.552 19.399111-42.666667 42.325333-42.666666h768.682667c23.381333 0 42.325333 18.972444 42.325333 42.666666v597.333334c0 23.552-19.427556 42.666667-42.325333 42.666666H141.880889a42.382222 42.382222 0 0 1-42.325333-42.666666v-597.333334z m512 512a213.333333 213.333333 0 1 0 0-426.666666 213.333333 213.333333 0 0 0 0 426.666666z m-426.666667-469.333333v85.333333H312.888889v-85.333333H184.888889z m0-213.333333h256v85.333333h-256v-85.333333z" fill="${getColor(0, color, colors, '#5290FD')}" />
          </svg>''';
        break;
      case IconNames.saasXiangceshangchuan:
        svgXml = '''
          <svg viewBox="0 0 1052 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M867.555556 213.333333h-682.666667v597.333334l396.458667-396.544a42.666667 42.666667 0 0 1 60.330666 0l225.877334 226.304V213.333333z m-768-42.951111A42.666667 42.666667 0 0 1 141.880889 128h768.682667c23.381333 0 42.325333 19.000889 42.325333 42.382222v683.235556a42.666667 42.666667 0 0 1-42.325333 42.382222H141.880889A42.382222 42.382222 0 0 1 99.555556 853.617778V170.382222z m256 298.951111a85.333333 85.333333 0 1 1 0-170.666666 85.333333 85.333333 0 0 1 0 170.666666z" fill="${getColor(0, color, colors, '#5290FD')}" />
          </svg>''';
        break;
      case IconNames.saasTuichang:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M555.861333 166.570667H168.789333v-40.618667h40.789334v772.096h-40.789334v-40.618667h387.754667a40.561778 40.561778 0 0 1 33.905778 18.033778 40.675556 40.675556 0 0 1-5.12 51.313778 40.220444 40.220444 0 0 1-20.821334 11.093333 40.561778 40.561778 0 0 1-7.964444 0.796445H168.789333a40.732444 40.732444 0 0 1-40.789333-40.618667V125.952c0-22.414222 18.261333-40.618667 40.789333-40.618667h387.072a40.846222 40.846222 0 0 1 40.049778 48.526223 40.106667 40.106667 0 0 1-17.351111 25.884444 40.903111 40.903111 0 0 1-22.698667 6.826667zM651.093333 691.996444l180.167111-211.456 26.851556 31.459556-26.851556 31.459556-180.167111-211.456a43.861333 43.861333 0 0 1-8.192-14.449778 49.777778 49.777778 0 0 1-2.901333-17.066667 52.110222 52.110222 0 0 1 6.371556-24.689778 43.804444 43.804444 0 0 1 10.467555-12.288 36.408889 36.408889 0 0 1 13.653333-6.656 32.369778 32.369778 0 0 1 21.959112 2.56 36.408889 36.408889 0 0 1 12.288 9.671111l180.167111 211.456c14.791111 17.351111 14.791111 45.511111 0 62.919112l-180.167111 211.512888a39.310222 39.310222 0 0 1-12.288 9.671112 33.564444 33.564444 0 0 1-21.959112 2.503111 33.507556 33.507556 0 0 1-13.653333-6.656 39.651556 39.651556 0 0 1-10.467555-12.288 47.104 47.104 0 0 1-5.688889-16.042667 52.053333 52.053333 0 0 1 5.688889-33.393778 43.804444 43.804444 0 0 1 4.721777-6.769778z" fill="${getColor(0, color, colors, '#5290FD')}" />
            <path d="M857.998222 554.666667H336.668444a33.905778 33.905778 0 0 1-14.563555-3.242667 36.750222 36.750222 0 0 1-12.288-9.272889 41.984 41.984 0 0 1-8.248889-13.824 46.592 46.592 0 0 1-2.161778-24.632889 46.592 46.592 0 0 1 5.688889-15.36 41.984 41.984 0 0 1 10.467556-11.832889 36.807111 36.807111 0 0 1 13.653333-6.371555 33.905778 33.905778 0 0 1 7.395556-0.796445h521.386666a33.848889 33.848889 0 0 1 14.563556 3.242667 36.465778 36.465778 0 0 1 12.288 9.272889 41.813333 41.813333 0 0 1 8.248889 13.824 46.648889 46.648889 0 0 1 0 32.654222 44.430222 44.430222 0 0 1-8.248889 13.824 38.798222 38.798222 0 0 1-12.288 9.272889 34.872889 34.872889 0 0 1-14.563556 3.242667z" fill="${getColor(1, color, colors, '#5290FD')}" />
          </svg>''';
        break;
      case IconNames.saasOcr:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M938.666667 727.210667C938.666667 796.970667 884.224 853.333333 816.768 853.333333H207.232C139.818667 853.333333 85.333333 797.013333 85.333333 727.210667v-378.453334c0-69.802667 54.442667-126.165333 121.898667-126.165333H324.693333l19.882667-41.6A90.154667 90.154667 0 0 1 420.181333 128.426667L426.666667 128h170.666666a90.88 90.88 0 0 1 82.090667 52.992l19.882667 41.6h117.461333c65.408 0 118.613333 52.992 121.898667 126.165333v378.453334zM597.76 191.061333H426.24a30.421333 30.421333 0 0 0-27.221333 17.664l-28.032 59.306667a30.421333 30.421333 0 0 1-27.221334 17.664h-136.533333c-33.706667 0-60.928 28.16-60.928 63.061333v378.453334c0 34.858667 27.221333 63.061333 60.928 63.061333h609.536c33.706667 0 60.928-28.16 60.928-63.061333v-378.453334c0-34.901333-27.221333-63.061333-60.928-63.061333h-136.106667a30.592 30.592 0 0 1-27.648-17.664l-28.032-59.306667a30.421333 30.421333 0 0 0-27.264-17.664zM512 727.210667c-100.778667 0-182.869333-84.949333-182.869333-189.226667 0-104.277333 82.090667-189.226667 182.869333-189.226667 100.778667 0 182.869333 84.906667 182.869333 189.226667 0 104.277333-82.090667 189.226667-182.869333 189.226667z m121.898667-189.226667c0-69.802667-54.442667-126.165333-121.898667-126.165333-67.456 0-121.898667 56.32-121.898667 126.165333 0 69.802667 54.442667 126.122667 121.898667 126.122667 67.456 0 121.898667-56.32 121.898667-126.122667z" fill="${getColor(0, color, colors, '#5290FD')}" />
          </svg>''';
        break;
      case IconNames.saasCopy:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M271.058824 124.446118a26.081882 26.081882 0 0 0 0 52.224h441.705411c52.163765 0 94.388706 42.224941 94.388706 94.388706v481.882352a26.081882 26.081882 0 1 0 52.224 0v-481.882352a146.552471 146.552471 0 0 0-146.612706-146.612706H271.058824zM271.058824 283.105882h361.411764c37.707294 0 68.246588 30.539294 68.246588 68.246589v441.705411c0 37.707294-30.539294 68.306824-68.246588 68.306824H271.058824a68.246588 68.246588 0 0 1-68.246589-68.306824V351.352471C202.812235 313.645176 233.351529 283.105882 271.058824 283.105882zM150.588235 351.352471a120.470588 120.470588 0 0 1 120.470589-120.470589h361.411764a120.470588 120.470588 0 0 1 120.470588 120.470589v441.705411a120.470588 120.470588 0 0 1-120.470588 120.470589H271.058824a120.470588 120.470588 0 0 1-120.470589-120.470589V351.352471z" fill="${getColor(0, color, colors, '#5290FD')}" />
          </svg>''';
        break;
      case IconNames.saasServiceTel:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M731.608615 928.019692c-15.635692 0-29.341538-1.969231-43.047384-7.876923-15.675077-3.899077-156.632615-46.946462-348.475077-236.859077-184.004923-184.004923-230.990769-330.830769-234.929231-348.435692-9.767385-21.543385-11.736615-46.985846-5.868308-74.436923 5.907692-25.403077 17.644308-50.884923 35.249231-66.56l72.467692-72.388923a84.519385 84.519385 0 0 1 60.652308-25.481846c25.442462 0 50.884923 11.776 68.529231 35.24923l105.708308 135.089231c17.604923 23.473231 19.574154 60.652308 5.868307 86.134154l-52.854154 84.164923c3.938462 15.675077 21.543385 56.792615 78.296616 115.515077 62.621538 58.722462 101.809231 74.397538 115.515077 78.296615l86.134154-50.884923c9.767385-5.907692 23.473231-9.806769 37.179077-9.806769 17.644308 0 35.249231 5.907692 46.985846 13.705846l135.08923 105.708308c19.574154 15.675077 33.28 39.187692 33.28 62.660923 1.969231 25.442462-7.837538 48.955077-25.442461 66.56l-72.467692 72.467692c-21.504 23.473231-60.652308 37.179077-97.87077 37.179077z" fill="${getColor(0, color, colors, '#00B8B8')}" />
          </svg>''';
        break;
      case IconNames.saasFileIcon:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M623.908571 475.721143v164.571428h144.091429c35.84 0.731429 65.828571 13.165714 90.697143 37.302858 24.137143 24.137143 36.571429 54.125714 37.302857 90.697142H128c0.731429-36.571429 13.165714-66.56 37.302857-90.697142 24.868571-24.137143 54.857143-36.571429 90.697143-37.302858h144.091429v-164.571428a188.562286 188.562286 0 0 1-69.485715-214.308572c13.165714-40.228571 35.84-72.411429 68.022857-96.548571 32.914286-24.137143 73.142857-36.571429 113.371429-35.84 42.422857 0 80.457143 11.702857 112.64 35.84 32.914286 24.137143 55.588571 56.32 68.754286 96.548571a188.562286 188.562286 0 0 1-69.485715 214.308572z m-495.908571 420.571428v-64.365714h768v64.365714z" fill="${getColor(0, color, colors, '#0092FF')}" />
          </svg>''';
        break;
      case IconNames.saasAddressLine:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M917.333333 437.077333c0 187.733333-127.146667 360.96-382.293333 517.973334-6.826667 4.266667-14.506667 6.826667-17.066667 7.68-0.853333 0-0.853333-0.853333-0.853333-0.853334a42.24 42.24 0 0 1-28.16-6.826666c-255.146667-157.013333-382.293333-330.24-382.293333-517.973334 0-104.106667 39.253333-192.853333 119.466666-266.24 78.506667-72.533333 174.08-109.226667 285.866667-109.226666s207.36 36.693333 285.866667 109.226666c79.36 73.386667 119.466667 162.133333 119.466666 266.24z m-633.173333-209.066666c-63.146667 58.026667-93.013333 128-93.013333 209.066666 0 153.6 106.666667 299.52 320.853333 437.76 214.186667-138.24 320.853333-284.16 320.853333-437.76 0-81.066667-30.72-151.04-93.866666-209.066666-62.293333-58.026667-138.24-87.04-226.986667-87.04s-164.693333 29.013333-227.84 87.04z m409.6 217.6c0 95.573333-81.066667 172.373333-181.76 172.373333s-181.76-76.8-181.76-172.373333 81.066667-172.373333 181.76-172.373334 181.76 77.653333 181.76 172.373334zM512 537.770667c53.76 0 97.28-40.96 97.28-92.16 0-51.2-43.52-92.16-97.28-92.16-54.613333 0-97.28 40.96-97.28 92.16 0 51.2 42.666667 92.16 97.28 92.16z" fill="${getColor(0, color, colors, '#333333')}" />
          </svg>''';
        break;
      case IconNames.saasIconMianYinsizhengce:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M683.84 960H224c-53.12 0-96-42.88-96-96V160c0-53.12 42.88-96 96-96h576c53.12 0 96 42.88 96 96v403.2l-58.56-14.4c-24-6.4-50.24-6.4-75.52 0l-116.48 28.48c-47.68 12.48-70.72 42.24-69.44 89.6 3.84 122.88 37.44 217.92 100.16 285.12 2.56 2.88 5.12 5.44 7.68 8zM288 304c0 16.32 12.48 30.08 28.16 31.68l3.84 0.32h384l3.84-0.32c15.68-1.6 28.16-15.36 28.16-31.68 0-17.6-14.4-32-32-32H320l-3.84 0.32c-15.68 1.6-28.16 15.36-28.16 31.68z m256 192c0-17.6-14.4-32-32-32h-192l-3.84 0.32c-15.68 1.6-28.16 15.36-28.16 31.68s12.48 30.08 28.16 31.68l3.84 0.32h192l3.84-0.32c15.68-1.6 28.16-15.36 28.16-31.68z m233.6 114.88c14.72-3.84 30.08-3.84 44.8 0l117.12 28.48c12.16 3.2 20.8 13.76 20.48 25.92-8 255.36-160 294.72-160 294.72s-152-39.36-160-294.72c-0.32-12.16 8.32-22.72 20.48-25.92z" fill="${getColor(0, color, colors, '#333333')}" />
          </svg>''';
        break;
      case IconNames.saasIconMianYinsianquan:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M512.16 936.672a534.144 534.144 0 0 1-365.44-506.88v-218.56l365.44-106.56 365.12 106.56v218.88c0 229.76-147.2 433.92-365.12 506.56z m-170.56-475.84a31.84 31.84 0 0 0-12.16-2.56c-4.16 0-8.32 0.96-12.16 2.56-4.16 1.6-7.36 3.84-10.56 6.72l-3.84 4.8c-1.28 1.92-2.24 3.84-2.88 5.76-0.96 1.92-1.6 3.84-1.92 5.76-0.32 2.24-0.64 4.16-0.64 6.4s0.32 4.16 0.64 6.4c0.32 1.92 0.96 3.84 1.92 5.76 0.64 1.92 1.6 3.84 2.88 5.76l3.84 4.8 142.08 142.08c12.48 12.48 32.96 12.48 45.44 0l243.52-243.52 3.84-4.8c1.28-1.92 2.24-3.84 2.88-5.76 0.96-1.92 1.6-3.84 1.92-5.76 0.32-2.24 0.64-4.16 0.64-6.4s-0.32-4.16-0.64-6.4a20.352 20.352 0 0 0-1.92-5.76 24.224 24.224 0 0 0-2.88-5.76l-3.84-4.8c-3.2-2.88-6.4-5.12-10.56-6.72a31.84 31.84 0 0 0-12.16-2.56c-4.16 0-8.32 0.96-12.16 2.56-4.16 1.6-7.36 3.84-10.56 6.72l-220.8 221.12-119.36-119.68c-3.2-2.88-6.4-5.12-10.56-6.72z" fill="${getColor(0, color, colors, '#333333')}" />
          </svg>''';
        break;
      case IconNames.saasIconXianYinsizhengce:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M430.506667 861.44a33.322667 33.322667 0 0 1 0 66.56H129.706667c-18.773333 0-33.706667-14.933333-33.706667-33.28V97.28c0-18.773333 14.933333-33.28 33.706667-33.28h512.853333c8.533333 0 17.066667 3.413333 23.466667 9.386667l156.16 152.746666c6.4 5.973333 9.813333 14.933333 9.813333 23.893334v162.56c0 18.773333-14.933333 33.28-33.706667 33.28-18.346667 0-33.28-14.506667-33.28-33.28V329.813333H631.466667c-37.12 0-66.986667-29.866667-67.413334-66.56V130.56h-401.066666v730.88z m334.506666-598.186667l-133.973333-130.56v130.56z m-269.226666 90.453334c0 18.773333-15.36 33.706667-34.133334 33.706666H257.706667c-18.346667 0-33.706667-14.933333-33.706667-33.706666 0-18.773333 15.36-33.706667 33.706667-33.706667h203.946666c18.773333 0 34.133333 14.933333 34.133334 33.706667z m169.813333 168.96c0 18.773333-14.933333 33.706667-34.133333 33.706666H257.706667c-18.773333 0-33.706667-14.933333-33.706667-33.706666 0-18.773333 14.933333-33.706667 33.706667-33.706667h373.76c19.2 0 34.133333 14.933333 34.133333 33.706667z m-121.6 143.36c-0.853333-30.72 14.08-49.92 44.8-57.6l116.906667-28.586667c20.053333-5.12 40.96-5.12 60.16 0l117.76 28.586667c30.293333 7.68 45.226667 27.306667 44.373333 57.6-3.413333 114.773333-34.133333 202.666667-91.733333 264.106666-30.293333 32.426667-61.013333 52.48-92.16 61.013334-2.56 0.426667-5.546667 0.853333-6.4 0.853333-2.986667 0-6.4-0.426667-9.813334-0.853333-31.146667-8.533333-61.866667-28.586667-92.586666-61.013334-57.173333-61.44-87.893333-149.333333-91.306667-264.106666z m245.76 220.586666c45.226667-49.066667 70.4-121.6 74.24-217.173333l-113.493333-27.733333c-9.813333-2.56-19.2-2.56-29.013334 0h-0.426666l-112.64 27.733333c3.413333 95.573333 28.16 168.106667 74.24 217.173333 18.346667 20.053333 36.266667 33.28 53.333333 39.68 17.066667-6.4 34.986667-19.626667 53.76-39.68zM257.706667 657.92h203.946666c18.773333 0 34.133333 14.933333 34.133334 33.706667 0 18.773333-15.36 33.706667-34.133334 33.706666H257.706667c-18.346667 0-33.706667-14.933333-33.706667-33.706666 0-18.773333 15.36-33.706667 33.706667-33.706667z" fill="${getColor(0, color, colors, '#333333')}" />
          </svg>''';
        break;
      case IconNames.saasIconXianYinsibaohu:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M104.106667 429.909333v-218.453333c0-18.773333 12.373333-35.84 30.72-40.96l365.226666-106.666667c7.68-2.133333 16.213333-2.133333 23.893334 0l365.226666 106.666667c18.346667 5.12 30.72 22.186667 30.72 40.96v218.88c0 248.32-158.72 468.48-394.24 546.986667-8.96 2.986667-18.346667 2.986667-27.306666 0a576.853333 576.853333 0 0 1-394.24-547.413334z m730.453333-186.453333L512 149.162667l-322.56 94.293333v186.453333c0 206.933333 129.28 391.253333 322.56 461.653334 193.28-70.4 322.56-254.72 322.56-461.226667z m-474.88 216.746667l111.786667 112.213333 213.333333-213.76c4.266667-3.84 8.533333-6.826667 14.08-8.96 5.12-2.133333 10.666667-3.413333 16.213333-3.413333s11.093333 1.28 16.213334 3.413333c5.546667 2.133333 9.813333 5.12 14.08 8.96l5.12 6.4c1.706667 2.56 2.986667 5.12 3.84 7.68 1.28 2.56 2.133333 5.12 2.56 7.68 0.426667 2.986667 0.853333 5.546667 0.853333 8.533333s-0.426667 5.546667-0.853333 8.533334c-0.426667 2.56-1.28 5.12-2.56 7.68-0.853333 2.56-2.133333 5.12-3.84 7.68l-5.12 6.4-243.626667 243.626666c-16.64 16.64-43.946667 16.64-60.586667 0l-142.08-142.08-5.12-6.4a32.298667 32.298667 0 0 1-3.84-7.68 27.136 27.136 0 0 1-2.56-7.68c-0.426667-2.986667-0.853333-5.546667-0.853333-8.533333s0.426667-5.546667 0.853333-8.533333c0.426667-2.56 1.28-5.12 2.56-7.68 0.853333-2.56 2.133333-5.12 3.84-7.68l5.12-6.4 6.4-5.12c2.56-1.706667 5.12-2.986667 7.68-3.84 2.56-1.28 5.12-2.133333 7.68-2.56 2.986667-0.426667 5.546667-0.853333 8.533334-0.853334s5.546667 0.426667 8.533333 0.853334c2.56 0.426667 5.12 1.28 7.68 2.56 2.56 0.853333 5.12 2.133333 7.68 3.84z" fill="${getColor(0, color, colors, '#333333')}" />
          </svg>''';
        break;
      case IconNames.saasYupaoMachine:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M914.24 536.64L928 713.6c0 38.72-28.16 70.4-64 74.88v68.16c0 28.8-22.4 39.36-50.24 39.36h-35.52c-27.52 0-49.92-10.56-49.92-39.36v-67.2H299.84v67.2c0 28.8-22.4 39.36-49.92 39.36H214.4c-27.84-1.28-50.24-13.44-50.24-39.36l0.32-67.52C126.4 786.88 96 753.92 96 713.6l14.4-176.96c0-41.92 32.64-75.84 72.96-75.84H217.6c0.32-0.96 0-1.6 0.32-2.56L275.2 200.96c3.84-17.28 13.44-30.72 34.56-31.04h152.64l0.32-2.56v-19.2c0-11.2 8.64-20.16 19.52-20.16h76.16c10.88 0 19.52 8.96 19.52 20.16v20.8l0.96 0.96h154.56c16.32 0 28.8 12.8 32.64 31.04l51.52 259.84h23.68c40.32 0 72.96 33.92 72.96 75.84zM292.48 641.28c0-25.6-20.16-46.4-44.8-46.4-24.64 0-44.48 20.8-44.48 46.4 0 25.6 19.84 46.4 44.48 46.4 24.64 0 44.8-20.8 44.8-46.4z m460.8-215.36l-30.4-180.16c-2.56-12.8-11.84-18.88-25.6-18.88H347.84c-13.76 1.28-17.6 4.8-22.72 18.88l-39.36 180.16c0 10.56 8.32 19.2 18.24 19.2h431.04c10.24 0 18.24-8.64 18.24-19.2z m83.2 215.36c0-25.6-19.84-46.4-44.48-46.4-24.64 0-44.48 20.8-44.48 46.4 0 25.6 19.84 46.4 44.48 46.4 24.64 0 44.48-20.8 44.48-46.4z" fill="${getColor(0, color, colors, '#333333')}" />
          </svg>''';
        break;
      case IconNames.saasYupaoResume:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M903.04 813.76h-255.04c-15.36 0-29.76 6.08-40.64 16.96l-54.72 55.68c-22.4 22.72-58.88 22.72-81.28 0l-54.72-55.68c-10.88-10.88-25.28-16.96-40.64-16.96H120.96c-31.36 0-56.96-25.6-56.96-56.96V184.96C64 153.6 89.6 128 120.96 128h782.08C934.4 128 960 153.6 960 184.96v571.84c0 31.36-25.6 56.96-56.96 56.96zM512.32 258.56c-48.96 0-88.64 39.68-88.64 88.96 0 49.28 39.68 88.96 88.64 88.96a88.64 88.64 0 0 0 88.32-88.96 88.64 88.64 0 0 0-88.32-88.96z m227.2 382.72c0-56.96 0-85.44-10.88-107.2-9.92-18.88-25.28-34.56-44.16-44.16-21.76-11.2-49.92-11.2-106.56-11.2h-131.52c-56.64 0-85.12 0-106.56 11.2-19.2 9.6-34.56 25.28-44.48 44.16-10.88 21.76-10.88 50.24-10.88 107.2v15.36h455.04z" fill="${getColor(0, color, colors, '#333333')}" />
          </svg>''';
        break;
      case IconNames.saasYupaoBuilding:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M638.4 303.04a245.696 245.696 0 0 0-127.36-35.52c-135.04 0-244.8 109.44-244.8 244.16a245.12 245.12 0 0 0 122.24 211.84 244.256 244.256 0 0 0 244.8 0 244.224 244.224 0 0 0 87.68-337.6l14.08-14.08 55.04 13.12 81.92-81.92c35.52 61.12 56 132.48 56 208.64v22.72c-3.52 57.6-17.28 109.44-40 157.76C820.16 831.04 678.08 928 512.64 928 281.92 928 96 740.8 96 511.68 96 281.6 281.92 96 512.64 96c76.16 0 147.2 20.48 208.32 56l-83.52 83.52 13.44 55.04z m35.2-57.6l93.12-92.8 20.48 84.8-11.52-47.68 11.84 48 83.52 20.16-91.2 91.2 74.24-74.24-74.24 74.24-55.04-13.12-23.04 22.72 23.04-22.72-202.88 201.6 117.76-117.12-117.76 117.12c-9.6 9.28-26.88 9.28-36.16 0-0.96-0.64-1.6-1.6-2.24-2.24a27.008 27.008 0 0 1 1.92-33.92l201.6-200.64z m-227.84 274.24c0 16 5.44 29.76 16.64 40.64 11.2 11.2 24.96 16.64 41.28 16.64s30.08-5.44 41.28-16.32l111.36-110.72c8.32 19.2 12.8 40.32 12.8 62.4 0 86.72-71.04 157.76-158.08 157.76-87.36 0-158.08-71.04-158.08-157.76 0-87.36 70.72-158.08 158.08-158.08 22.4 0 43.52 4.8 62.72 13.12l-111.36 111.04c-11.2 11.2-16.64 24.96-16.64 41.28z" fill="${getColor(0, color, colors, '#333333')}" />
          </svg>''';
        break;
      case IconNames.saasClockIcon:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M512 977.92C254.293333 977.92 46.08 769.706667 46.08 512S254.293333 46.08 512 46.08 977.92 254.293333 977.92 512 769.706667 977.92 512 977.92zM124.586667 512c0 214.186667 173.226667 387.413333 387.413333 387.413333 214.186667 0 387.413333-173.226667 387.413333-387.413333 0-214.186667-173.226667-387.413333-387.413333-387.413333-214.186667 0-387.413333 173.226667-387.413333 387.413333z" fill="${getColor(0, color, colors, '#B2AFB2')}" />
            <path d="M551.424 256c0-2.56 0-5.12-0.853333-7.68 0-2.56-0.853333-5.12-2.56-7.68-0.853333-1.706667-1.706667-4.266667-3.413334-6.826667-0.853333-1.706667-2.56-3.413333-4.266666-5.973333-2.56-1.706667-4.266667-3.413333-5.973334-4.266667-2.56-1.706667-5.12-2.56-6.826666-3.413333a13.738667 13.738667 0 0 0-7.68-2.56c-2.56-0.853333-5.12-0.853333-7.68-0.853333s-5.12 0-7.68 0.853333c-2.56 0-5.12 0.853333-7.68 2.56-1.706667 0.853333-4.266667 1.706667-6.826667 3.413333-1.706667 0.853333-3.413333 2.56-5.973333 4.266667-1.706667 2.56-3.413333 4.266667-4.266667 5.973333-1.706667 2.56-2.56 5.12-3.413333 6.826667-1.706667 2.56-2.56 5.12-2.56 7.68-0.853333 2.56-0.853333 5.12-0.853334 7.68v256c0 10.24 4.266667 20.48 11.093334 28.16l180.906666 180.906667c4.266667 3.413333 8.533333 5.973333 12.8 7.68 5.12 2.56 10.24 3.413333 15.36 3.413333s10.24-0.853333 15.36-3.413333c4.266667-1.706667 8.533333-4.266667 12.8-7.68 1.706667-2.56 3.413333-4.266667 4.266667-5.973334 1.706667-2.56 2.56-5.12 3.413333-6.826666 1.706667-2.56 2.56-5.12 2.56-7.68 0.853333-2.56 0.853333-5.12 0.853334-7.68s0-5.12-0.853334-7.68c0-2.56-0.853333-5.12-2.56-7.68-0.853333-1.706667-1.706667-4.266667-3.413333-6.826667-0.853333-1.706667-2.56-3.413333-4.266667-5.973333l-169.813333-168.96V256z" fill="${getColor(1, color, colors, '#B2AFB2')}" />
          </svg>''';
        break;
      case IconNames.saasBellIcon:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M656.810667 850.005333A146.517333 146.517333 0 0 1 512 977.834667a146.517333 146.517333 0 0 1-144.810667-127.829334H85.333333c-2.56 0-5.12 0-7.68-0.853333-2.56 0-5.12-0.853333-7.68-2.56-1.706667-0.853333-4.266667-1.706667-6.826666-3.413333-1.706667-0.853333-3.413333-2.56-5.973334-4.266667-1.706667-2.56-3.413333-4.266667-4.266666-5.973333-1.706667-2.56-2.56-5.12-3.413334-6.826667a13.738667 13.738667 0 0 1-2.56-7.68c-0.853333-2.56-0.853333-5.12-0.853333-7.68s0-5.12 0.853333-7.68c0-2.56 0.853333-5.12 2.56-7.68 0.853333-1.706667 1.706667-4.266667 3.413334-6.826667 0.853333-1.706667 2.56-3.413333 4.266666-5.973333 2.56-1.706667 4.266667-3.413333 5.973334-4.266667 2.56-1.706667 5.12-2.56 6.826666-3.413333 2.56-1.706667 5.12-2.56 7.68-2.56 2.56-0.853333 5.12-0.853333 7.68-0.853333h88.746667v-387.413334c0-186.88 151.04-337.92 337.92-337.92s337.92 151.04 337.92 337.92v387.413334H938.666667c2.56 0 5.12 0 7.68 0.853333 2.56 0 5.12 0.853333 7.68 2.56 1.706667 0.853333 4.266667 1.706667 6.826666 3.413333 1.706667 0.853333 3.413333 2.56 5.973334 4.266667 1.706667 2.56 3.413333 4.266667 4.266666 5.973333 1.706667 2.56 2.56 5.12 4.266667 6.826667 0.853333 2.56 1.706667 5.12 1.706667 7.68 0.853333 2.56 0.853333 5.12 0.853333 7.68s0 5.12-0.853333 7.68c0 2.56-0.853333 5.12-1.706667 7.68-1.706667 1.706667-2.56 4.266667-4.266667 6.826667-0.853333 1.706667-2.56 3.413333-4.266666 5.973333-2.56 1.706667-4.266667 3.413333-5.973334 4.266667-2.56 1.706667-5.12 2.56-6.826666 3.413333-2.56 1.706667-5.12 2.56-7.68 2.56-2.56 0.853333-5.12 0.853333-7.68 0.853333zM512 899.328c30.72 0 57.088-21.248 64.853333-49.322667H447.146667c7.765333 28.074667 34.133333 49.322667 64.853333 49.322667z m110.250667-127.829333H771.413333v-387.413334c0-143.36-116.053333-259.413333-259.413333-259.413333s-259.413333 116.053333-259.413333 259.413333v387.413334h149.162666a37.802667 37.802667 0 0 1 3.584-0.170667h213.333334c1.194667 0 2.389333 0 3.584 0.170667z" fill="${getColor(0, color, colors, '#1A9AF9')}" />
          </svg>''';
        break;
      case IconNames.saasIconLoading:
        svgXml = '''
          <svg viewBox="0 0 1365 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M347.989333 71.850667v108.373333c0 23.893333 11.946667 35.84 35.84 35.84 24.32 0 36.266667-11.946667 36.266667-35.84v-108.373333c0-23.893333-11.946667-35.84-36.266667-35.84-23.893333 0-35.84 11.946667-35.84 35.84z" fill="${getColor(0, color, colors, '#5290FD')}" opacity=".25" />
            <path d="M347.989333 575.829333v108.373334c0 23.893333 11.946667 35.84 35.84 35.84 24.32 0 36.266667-11.946667 36.266667-35.84v-108.373334c0-23.893333-11.946667-35.84-36.266667-35.84-23.893333 0-35.84 11.946667-35.84 35.84z" fill="${getColor(1, color, colors, '#5290FD')}" opacity=".75" />
            <path d="M690.218667 342.016h-108.373334a36.053333 36.053333 0 0 0 0 72.106667h108.373334a36.053333 36.053333 0 0 0 0-72.106667z" fill="${getColor(2, color, colors, '#5290FD')}" />
            <path d="M186.197333 342.016h-108.373333a36.053333 36.053333 0 0 0 0 72.106667h108.373333a36.053333 36.053333 0 0 0 0-72.106667z" fill="${getColor(3, color, colors, '#5290FD')}" opacity=".55" />
            <path d="M600.192 125.610667c-9.386667 0-18.346667 3.84-25.173333 10.666666l-76.373334 76.373334c-14.08 14.08-14.08 36.693333 0 50.773333 14.08 14.08 36.693333 14.08 50.773334 0l76.373333-76.373333c14.08-14.08 14.08-36.693333 0-50.773334a35.84 35.84 0 0 0-25.6-10.666666z" fill="${getColor(4, color, colors, '#5290FD')}" opacity=".15" />
            <path d="M243.84 481.962667c-9.386667 0-18.773333 3.84-25.173333 10.666666l-76.373334 76.373334a35.413333 35.413333 0 0 0-9.813333 34.986666c2.986667 12.373333 13.226667 22.186667 25.6 25.6 12.8 3.413333 26.026667-0.426667 34.986667-9.813333l76.373333-76.373333a35.584 35.584 0 0 0 0-50.773334 35.84 35.84 0 0 0-25.6-10.666666z" fill="${getColor(5, color, colors, '#5290FD')}" opacity=".65" />
            <path d="M142.293333 187.050667l76.373334 76.373333c14.08 14.08 36.693333 14.08 50.773333 0 14.08-14.08 14.08-36.693333 0-50.773333l-76.373333-76.373334a35.584 35.584 0 0 0-50.773334 0c-14.08 14.08-14.08 36.693333 0 50.773334z" fill="${getColor(6, color, colors, '#5290FD')}" opacity=".45" />
            <path d="M487.978667 517.802667c0 9.813333 3.84 18.773333 10.666666 25.6l76.373334 76.373333c8.96 9.386667 22.186667 13.226667 34.986666 9.813333 12.373333-3.413333 22.186667-13.226667 25.6-25.6a35.413333 35.413333 0 0 0-9.813333-34.986666l-76.373333-76.373334a35.584 35.584 0 0 0-50.773334 0c-6.826667 6.826667-10.666667 15.786667-10.666666 25.173334z" fill="${getColor(7, color, colors, '#5290FD')}" opacity=".85" />
          </svg>''';
        break;
      case IconNames.saasIconUserBuilding:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M160 352a255.776 255.776 0 0 0 122.208 218.336C154.208 622.976 64 748.896 64 896c0 17.6 14.4 32 32 32s32-14.4 32-32a288 288 0 0 1 576 0c0 17.6 14.4 32 32 32s32-14.4 32-32c0-147.136-90.24-273.024-218.208-325.664A255.776 255.776 0 0 0 672 352c0-141.44-114.56-256-256-256s-256 114.56-256 256z m448 0c0 105.92-86.08 192-192 192s-192-86.08-192-192 86.08-192 192-192 192 86.08 192 192z m124.8-190.016l-3.84-0.32a32 32 0 0 0-4.8 63.68 147.2 147.2 0 0 1-16.96 293.088L704 518.4c-17.6 0-32 14.4-32 32v0.064c0 17.6 14.4 32 32 32l3.424-0.032A223.84 223.84 0 0 1 928 806.4c0 17.6 14.4 32 32 32s32-14.4 32-32a288.064 288.064 0 0 0-167.52-261.664 211.04 211.04 0 0 0 90.72-173.472 211.2 211.2 0 0 0-182.4-209.28z" fill="${getColor(0, color, colors, '#323233')}" />
          </svg>''';
        break;
      case IconNames.saasWorkerOnline:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M448 928h384c35.2 0 64-28.8 64-64V384c0-35.2-28.8-64-64-64H448v608z" fill="${getColor(0, color, colors, '#5290FD')}" />
            <path d="M192 928h480c35.2 0 64-28.8 64-64V256c0-35.2-28.8-64-64-64H192C156.8 192 128 220.8 128 256v608c0 35.2 28.8 64 64 64z" fill="${getColor(1, color, colors, '#9BC4FF')}" opacity=".29" />
            <path d="M544 160h32c17.6 0 32 14.4 32 32v32c0 17.6-14.4 32-32 32H288c-17.6 0-32-14.4-32-32V192c0-17.6 14.4-32 32-32h32V128c0-17.6 14.4-32 32-32h160c17.6 0 32 14.4 32 32v32z m-24.224 314.24l-116.8 107.2-66.56-66.56a31.488 31.488 0 0 0-43.84 0c-11.84 12.16-11.84 31.68 0 43.84l83.84 83.52c3.52 3.84 7.68 6.08 12.16 7.68a30.912 30.912 0 0 0 35.84-4.16l137.28-125.76c12.48-11.52 13.44-31.04 1.92-43.84a31.232 31.232 0 0 0-43.84-1.92z" fill="${getColor(2, color, colors, '#5290FD')}" />
          </svg>''';
        break;
      case IconNames.saasWenhao:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M512.284444 64c-247.466667 0-448.284444 200.817778-448.284444 448.284444 0 246.897778 200.817778 447.715556 448.284444 447.715556 246.954667 0 447.715556-200.817778 447.715556-447.715556 0-247.466667-200.760889-448.284444-447.715556-448.284444zM512 896c-212.195556 0-384-171.804444-384-384S299.804444 128 512 128 896 299.804444 896 512 724.195556 896 512 896z" fill="${getColor(0, color, colors, '#333333')}" />
            <path d="M461.767111 683.975111c0 33.621333 25.6 56.888889 59.733333 56.888889 34.702222 0 60.302222-23.267556 60.302223-56.888889 0-33.564444-25.543111-56.263111-60.302223-56.263111-34.133333 0-59.733333 22.755556-59.733333 56.263111zM454.769778 397.824c20.48-20.48 41.528889-31.288889 67.128889-31.288889 32.426667 0 52.337778 18.773333 52.337777 50.062222 0 19.911111-13.084444 35.84-29.013333 52.337778l-13.653333 13.084445-9.671111 9.102222c-22.755556 23.893333-43.804444 51.825778-37.546667 96.711111h73.955556v-7.964445c-0.568889-31.857778 17.009778-52.337778 37.546666-72.248888l10.24-9.671112 10.752-10.24c20.48-19.911111 38.115556-42.666667 38.115556-79.075555 0-72.817778-52.337778-110.364444-124.017778-110.364445-52.906667 0-96.142222 24.462222-128.568889 60.871112l47.217778 43.804444 5.176889-5.12z" fill="${getColor(1, color, colors, '#333333')}" />
          </svg>''';
        break;
      case IconNames.saasNewsIcon:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M192 928a96 96 0 0 1-95.84-90.368L96 832V192a96 96 0 0 1 90.368-95.84L192 96h448a96 96 0 0 1 95.84 90.368L736 192v160h96l5.632 0.16a96 96 0 0 1 90.208 90.208L928 448v384l-0.16 5.632a96 96 0 0 1-90.208 90.208L832 928H192z m640-64l3.744-0.224a32 32 0 0 0 28.032-28.032L864 832V448l-0.224-3.744a32 32 0 0 0-28.032-28.032L832 416h-96v320a32 32 0 0 1-63.776 3.744L672 736V192a32 32 0 0 0-28.256-31.776L640 160H192a32 32 0 0 0-31.776 28.256L160 192v640a32 32 0 0 0 28.256 31.776L192 864h640z" fill="${getColor(0, color, colors, '#262626')}" />
            <path d="M256 224h96a32 32 0 0 1 32 32v96a32 32 0 0 1-32 32H256a32 32 0 0 1-32-32V256a32 32 0 0 1 32-32z m224 0h96a32 32 0 0 1 0 64h-96a32 32 0 0 1 0-64z m-224 224h320a32 32 0 0 1 0 64H256a32 32 0 0 1 0-64z m0 128h320a32 32 0 0 1 0 64H256a32 32 0 0 1 0-64z m0 128h320a32 32 0 0 1 0 64H256a32 32 0 0 1 0-64z m224-384h96a32 32 0 0 1 0 64h-96a32 32 0 0 1 0-64z" fill="${getColor(1, color, colors, '#000000')}" />
          </svg>''';
        break;
      case IconNames.saasHasnotSign:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M819.2 512a307.2 307.2 0 1 1-614.4 0 307.2 307.2 0 0 1 614.4 0z m-306.5344 87.7568a51.2 51.2 0 1 0-3.1232 102.4 51.2 51.2 0 0 0 3.1232-102.4z m-6.4-285.2864a36.608 36.608 0 0 0-36.4544 39.8336l17.3056 193.4336a24.9856 24.9856 0 0 0 49.7664 0l17.3056-193.4336a36.608 36.608 0 0 0-36.4544-39.8336h-11.4688z" fill="${getColor(0, color, colors, '#000000')}" fill-opacity=".453" />
          </svg>''';
        break;
      case IconNames.saasOverdueSign:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M729.2416 294.7584a307.2 307.2 0 1 1-434.4832 434.4832 307.2 307.2 0 0 1 434.4832-434.4832z m-237.1072 58.368c-21.8624 0-31.6416 17.7152-31.6928 39.5776l-0.1536 175.0016c0 12.8512 6.1952 24.8832 16.6912 32.256a39.3216 39.3216 0 0 0 25.2416 9.1648l179.3024-0.2048c21.8112 0 39.5776-9.8304 39.5776-31.6416 0-21.8624-17.7152-27.648-39.5264-27.648l-161.9456 0.1024 0.1536-157.0816c0-21.8624-5.8368-39.5776-27.648-39.5264z" fill="${getColor(0, color, colors, '#F54A45')}" />
          </svg>''';
        break;
      case IconNames.saasAlreadySign:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M512 204.8a307.2 307.2 0 1 1 0 614.4 307.2 307.2 0 0 1 0-614.4z m-17.3056 164.864l-32.4608 68.864a19.3536 19.3536 0 0 1-14.5408 10.9568l-72.6528 11.008c-15.872 2.4064-22.1696 22.6816-10.752 34.4064l52.6336 53.504a20.992 20.992 0 0 1 5.632 17.92l-12.4928 75.6736c-2.7648 16.4864 13.824 29.0304 28.0064 21.248l65.024-35.7376a18.432 18.432 0 0 1 17.92 0l65.024 35.7376c14.1824 7.7824 30.72-4.7616 28.0064-21.248l-12.4928-75.6736a20.992 20.992 0 0 1 5.632-17.92l52.6336-53.504c11.4176-11.5712 5.12-32-10.752-34.4064l-72.704-11.008a19.456 19.456 0 0 1-14.4896-10.9568l-32.512-68.8128a18.944 18.944 0 0 0-34.6624 0z" fill="${getColor(0, color, colors, '#FFA013')}" fill-opacity=".85" />
          </svg>''';
        break;
      case IconNames.saasFloatContract:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M662.869333 96.028444H210.261333a50.005333 50.005333 0 0 0-50.289777 49.664v732.615112c0 27.477333 22.528 49.664 50.289777 49.664h603.477334c27.761778 0 50.232889-22.186667 50.232889-49.664V307.2l-201.102223-211.171556z m0 81.635556l120.661334 128h-100.579556a19.911111 19.911111 0 0 1-20.081778-19.683556V177.664z m113.152 686.307556H247.978667a23.893333 23.893333 0 0 1-23.950223-23.836445V183.864889a23.893333 23.893333 0 0 1 23.950223-23.893333h372.053333V303.217778c0 26.396444 21.447111 47.786667 47.957333 47.786666h131.982223v489.187556a23.893333 23.893333 0 0 1-23.950223 23.893333z m-456.021333-191.943112h352.028444a31.971556 31.971556 0 1 1 0 63.943112H320a31.971556 31.971556 0 1 1 0-63.943112z m0-192.056888h320a32.028444 32.028444 0 0 1 0 64.056888H320a32.028444 32.028444 0 0 1 0-64.056888z m0-191.943112h160.028444a32.028444 32.028444 0 0 1 0 64H319.943111a32.028444 32.028444 0 0 1 0-64z" fill="${getColor(0, color, colors, '#5290FD')}" />
          </svg>''';
        break;
      case IconNames.saasAddProject:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M725.333333 128a170.666667 170.666667 0 0 1 170.666667 170.666667v426.666666a170.666667 170.666667 0 0 1-170.666667 170.666667H298.666667a170.666667 170.666667 0 0 1-170.666667-170.666667V298.666667a170.666667 170.666667 0 0 1 170.666667-170.666667h426.666666z m-212.992 191.573333a38.4 38.4 0 0 0-38.4 38.4v115.2H358.613333a38.4 38.4 0 0 0 0 76.8h115.242667v115.2a38.357333 38.357333 0 1 0 76.8 0v-115.2h115.242667a38.272 38.272 0 0 0 38.4-38.314666c0-21.333333-17.152-38.485333-38.4-38.485334h-115.2v-115.2c0-21.290667-17.152-38.4-38.4-38.4z" fill="${getColor(0, color, colors, '#333333')}" />
          </svg>''';
        break;
      case IconNames.saasAttendanceAccount:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M320 160v57.6a38.4 38.4 0 0 0 34.464 38.208L358.4 256h307.2a38.4 38.4 0 0 0 38.208-34.464L704 217.6V160h160a64 64 0 0 1 64 64v640a64 64 0 0 1-64 64H160a64 64 0 0 1-64-64V224a64 64 0 0 1 64-64h160z m386.656 273.504a51.2 51.2 0 0 0-67.936 4l-181.12 180.992-72.32-72.384-4.48-4a51.2 51.2 0 0 0-67.936 76.384l108.64 108.64 4.48 3.968a51.2 51.2 0 0 0 67.904-3.968l217.248-217.248 3.968-4.48a51.2 51.2 0 0 0-3.968-67.904zM614.4 96c14.144 0 25.6 10.752 25.6 24v48c0 13.248-11.456 24-25.6 24h-204.8c-14.144 0-25.6-10.752-25.6-24v-48c0-13.248 11.456-24 25.6-24h204.8z" fill="${getColor(0, color, colors, '#333333')}" />
          </svg>''';
        break;
      case IconNames.saasCloudAlbum:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M507.008 128c134.4 0 245.728 105.088 266.336 244.8C881.696 401.6 960 506.912 960 630.4c0 132.896-90.944 243.264-209.184 262.624v2.88L716.608 896H286.976v-2.528C161.312 875.136 64 758.72 64 618.112c0-122.048 74.368-229.056 180.288-265.056C272.32 222.88 380.8 128 507.008 128z m21.152 256h-32.32c-8.928 0-16.16 7.968-16.16 17.76v178.752l-75.392-82.816a15.712 15.712 0 0 0-23.68 0l-23.68 26.048a19.712 19.712 0 0 0 0 26.016l133.472 146.624c0.576 0.64 11.2 7.616 21.76 7.616 10.464 0 20.864-6.976 21.44-7.616l133.504-146.624a19.712 19.712 0 0 0 0-26.016l-23.68-26.048a15.68 15.68 0 0 0-23.68 0l-75.424 82.816v-178.752c0-9.792-7.232-17.76-16.16-17.76z" fill="${getColor(0, color, colors, '#FFFFFF')}" />
          </svg>''';
        break;
      case IconNames.saasRadioActive:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M512 0a512 512 0 1 1 0 1024A512 512 0 0 1 512 0z m0 73.142857a438.857143 438.857143 0 1 0 0 877.714286A438.857143 438.857143 0 0 0 512 73.142857z m0 146.285714a292.571429 292.571429 0 1 1 0 585.142858 292.571429 292.571429 0 0 1 0-585.142858z" fill="${getColor(0, color, colors, '#5290FD')}" />
          </svg>''';
        break;
      case IconNames.saasRadio:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M512 0a512 512 0 1 1 0 1024A512 512 0 0 1 512 0z m0 73.142857a438.857143 438.857143 0 1 0 0 877.714286A438.857143 438.857143 0 0 0 512 73.142857z" fill="${getColor(0, color, colors, '#CCCCCC')}" />
          </svg>''';
        break;
      case IconNames.saasIocnProjectMannger:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M824.713846 509.597538c24.694154 0 47.379692 13.981538 59.982769 36.588308l2.756923 5.356308 70.104616 151.788308c8.861538 19.219692 9.728 41.511385 2.638769 61.36123l-2.678154 6.498462-70.104615 151.788308c-11.027692 23.867077-32.689231 39.581538-57.028923 41.708307l-5.632 0.236308h-210.274462c-24.654769 0-47.340308-14.020923-59.943384-36.588308l-2.756923-5.356307-70.065231-151.788308a81.604923 81.604923 0 0 1-2.638769-61.361231l2.638769-6.498461 70.104615-151.788308c11.027692-23.867077 32.689231-39.581538 57.028923-41.708308l5.632-0.236308h210.274462z m-317.321846-433.230769c71.010462 0 128.945231 60.770462 132.411077 137.216l0.157538 7.207385v72.192h165.730462c52.972308 0 96.256 45.095385 99.249231 101.927384l0.196923 6.380308v36.115692c0 19.928615-14.848 36.076308-33.161846 36.076308-16.974769 0-30.995692-13.942154-32.925539-31.901538l-0.196923-4.17477v-36.115692c0-18.510769-12.8-33.792-29.302154-35.84l-3.859692-0.275692h-198.892308c-16.974769 0-30.995692-13.942154-32.886154-31.901539l-0.236307-4.174769v-108.307692c0-38.084923-27.057231-69.277538-61.361231-72.034462l-4.923077-0.196923H242.215385c-34.973538 0-63.606154 29.459692-66.12677 66.835693l-0.196923 5.395692v649.846154H441.107692c18.313846 0 33.161846 16.147692 33.161846 36.076307s-14.848 36.115692-33.161846 36.115693H109.607385c-18.313846 0-33.122462-16.147692-33.122462-36.115693 0-19.928615 14.808615-36.076308 33.122462-36.076307v-649.846154c0-77.351385 55.808-140.484923 125.991384-144.265846l6.616616-0.157539h265.176615zM829.44 575.015385h-217.403077l-72.467692 163.446153 72.467692 163.446154h217.403077l72.467692-163.446154-72.467692-163.446153z" fill="${getColor(0, color, colors, '#000000')}" fill-opacity=".85" />
            <path d="M726.291692 622.552615a114.215385 114.215385 0 1 0-0.039384 228.509539 114.215385 114.215385 0 0 0 0.039384-228.509539z m0.354462 50.924308c33.713231 0 61.046154 28.199385 61.046154 63.015385 0 34.816-27.332923 63.015385-61.046154 63.015384-33.713231 0-61.046154-28.199385-61.046154-63.015384 0-34.816 27.332923-63.015385 61.046154-63.015385zM269.233231 308.539077h228.509538c21.070769 0 38.124308 12.878769 38.124308 28.750769 0 15.832615-17.053538 28.711385-38.124308 28.711385H269.233231c-21.031385 0-38.084923-12.878769-38.084923-28.750769 0-15.832615 17.053538-28.711385 38.084923-28.711385z m0 229.769846h152.339692c21.031385 0 38.084923 12.878769 38.084923 28.750769 0 15.832615-17.053538 28.711385-38.084923 28.711385H269.233231c-21.031385 0-38.084923-12.878769-38.084923-28.750769 0-15.832615 17.053538-28.711385 38.084923-28.711385z" fill="${getColor(1, color, colors, '#000000')}" />
          </svg>''';
        break;
      case IconNames.saasAIconxiangongchengguanli:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M626.372923 563.357538c-17.526154 0-33.870769 9.452308-42.653538 24.615385l-72.664616 125.873231a49.467077 49.467077 0 0 0 0 49.230769l72.664616 125.873231c8.782769 15.202462 25.127385 24.615385 42.653538 24.615384h145.368615c17.526154 0 33.870769-9.452308 42.614154-24.615384l72.704-125.873231a49.467077 49.467077 0 0 0 0-49.230769l-72.704-125.873231a49.348923 49.348923 0 0 0-42.614154-24.615385h-145.32923z m145.368615 409.284924h-145.32923a108.583385 108.583385 0 0 1-93.814154-54.153847l-72.704-125.87323a108.701538 108.701538 0 0 1 0-108.307693l72.704-125.87323a108.583385 108.583385 0 0 1 93.774769-54.153847h145.368615c38.596923 0 74.515692 20.755692 93.77477 54.153847l72.664615 125.87323c19.298462 33.398154 19.298462 74.909538 0 108.307693l-72.664615 125.87323a108.583385 108.583385 0 0 1-93.735385 54.153847z" fill="${getColor(0, color, colors, '#000000')}" />
            <path d="M608.689231 78.769231a29.538462 29.538462 0 0 1 29.538461 29.538461v178.688h257.772308a29.538462 29.538462 0 0 1 29.538462 29.538462v149.937231a29.538462 29.538462 0 0 1-59.076924 0V346.072615h-257.772307a29.538462 29.538462 0 0 1-29.538462-29.538461V137.846154H137.846154v758.153846a29.223385 29.223385 0 0 1-1.969231 9.846154h287.507692a29.538462 29.538462 0 0 1 0 59.076923h-354.461538a29.538462 29.538462 0 0 1 0-59.076923h11.815385a29.223385 29.223385 0 0 1-1.969231-9.846154v-787.692308A29.538462 29.538462 0 0 1 108.307692 78.769231h500.381539zM381.518769 490.377846c16.305231 0 29.538462 15.123692 29.538462 33.792s-13.233231 33.752615-29.538462 33.752616h-177.230769c-16.305231 0-29.538462-15.084308-29.538462-33.752616s13.233231-33.792 29.538462-33.792h177.230769z m68.923077-196.923077c21.740308 0 39.384615 15.123692 39.384616 33.792s-17.644308 33.752615-39.384616 33.752616h-236.307692c-21.740308 0-39.384615-15.084308-39.384616-33.752616s17.644308-33.792 39.384616-33.792z" fill="${getColor(1, color, colors, '#000000')}" />
          </svg>''';
        break;
      case IconNames.saasShanchu:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M844.101818 256a46.545455 46.545455 0 0 1 46.452364 49.477818l-35.700364 563.805091a139.636364 139.636364 0 0 1-139.403636 131.444364H308.596364a139.636364 139.636364 0 0 1-139.403637-131.444364L133.445818 305.477818a46.545455 46.545455 0 0 1 43.52-49.384727l1.489455-0.093091h665.6z m-63.208727 93.090909H243.944727a13.963636 13.963636 0 0 0-13.963636 14.894546l32.116364 499.851636a46.545455 46.545455 0 0 0 41.239272 43.52l5.213091 0.279273h406.900364a46.545455 46.545455 0 0 0 45.893818-38.632728l0.605091-5.166545 32.023273-499.898182a13.963636 13.963636 0 0 0-13.032728-14.801454zM418.909091 442.181818a46.545455 46.545455 0 0 1 46.545454 46.545455v279.272727a46.545455 46.545455 0 0 1-93.090909 0v-279.272727a46.545455 46.545455 0 0 1 46.545455-46.545455z m186.181818 0a46.545455 46.545455 0 0 1 46.545455 46.545455v279.272727a46.545455 46.545455 0 0 1-93.090909 0v-279.272727a46.545455 46.545455 0 0 1 46.545454-46.545455z m-74.472727-418.909091A74.472727 74.472727 0 0 1 605.090909 97.745455v18.618181h325.818182a46.545455 46.545455 0 0 1 0 93.090909H93.090909a46.545455 46.545455 0 1 1 0-93.090909h325.818182v-18.618181A74.472727 74.472727 0 0 1 493.381818 23.272727h37.236364z" fill="${getColor(0, color, colors, '#333333')}" />
          </svg>''';
        break;
      case IconNames.saasShanchu1:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M888.9856 229.0688h-171.4688l-32.3584-97.6896c-12.8-38.6048-51.5072-64.5632-96.3072-64.5632H418.9696c-44.1856 0-82.7392 25.4464-95.8976 63.3856l-34.3552 98.9184H117.9136c-28.2624 0-51.2 22.9376-51.2 51.2s22.9376 51.2 51.2 51.2h48.9984v523.7248c0 54.528 41.4208 98.7648 92.5184 98.7648h466.1248c51.0976 0 92.5184-44.2368 92.5184-98.7648V331.4688h70.8608c28.2624 0 51.2-22.9376 51.2-51.2s-22.8864-51.2-51.1488-51.2zM417.8944 169.2672c0.3072-0.0512 0.6656-0.0512 1.0752-0.0512h169.9328c0.3584 0 0.6656 0 0.9728 0.0512L608.4096 225.28H398.3872l19.5072-56.0128z" fill="${getColor(0, color, colors, '#8C7BFD')}" />
            <path d="M393.8304 840.0384c-28.2624 0-51.2-22.9376-51.2-51.2V478.4128c0-28.2624 22.9376-51.2 51.2-51.2s51.2 22.9376 51.2 51.2v310.3744c0 28.3136-22.8864 51.2512-51.2 51.2512zM589.9264 840.0384c-28.2624 0-51.2-22.9376-51.2-51.2V478.4128c0-28.2624 22.9376-51.2 51.2-51.2s51.2 22.9376 51.2 51.2v310.3744c0 28.3136-22.9376 51.2512-51.2 51.2512z" fill="${getColor(1, color, colors, '#FFE37B')}" />
          </svg>''';
        break;
      case IconNames.saasCheckFlow:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M306.07409111 134.43865891v61.80828079c0 21.29426377 16.23001298 38.86660797 36.97514096 40.94112203l4.21004082 0.18304526h329.48145422a41.18518177 41.18518177 0 0 0 40.94112204-36.97514096l0.24405974-4.21004079V134.37764439h171.6354322c37.89036722 0 68.64196964 30.75160239 68.64196966 68.64196966v686.41969629c0 37.89036722-30.75160239 68.64196964-68.64196966 68.64196963H134.49967517a68.64196964 68.64196964 0 0 1-68.64196962-68.64196963V203.01961405c0-37.89036722 30.75160239-68.64196964 68.64196962-68.64196966h171.57441594z m315.44798429 385.67634669c-92.49886691 0-165.35087735 63.21162654-165.35087738 140.70078453 0 77.5501725 72.85201044 140.63976829 165.35087738 140.63976831 19.28076597 0 38.86660797-4.82019105 58.26940651-9.64038209l53.32718292 29.1041951-14.58260572-48.44597735c38.98863874-29.22622589 68.03181939-67.90978862 68.03181941-111.59658946 0-77.611187-77.73321775-140.76179907-165.04580312-140.76179904z m-189.69589594-145.39894483c-106.83741289 0-194.39405799 72.60795069-194.39405798 164.86275783 0 53.20515395 29.10419513 96.95296925 77.73321776 130.93836994l-19.40279675 58.26940472 67.9708031-33.98540065c24.28400405 4.75917655 43.80882978 9.70139837 68.09283387 9.70139838 6.10150781 0 12.14200114-0.30507603 18.12148-0.7931973a142.34819185 142.34819185 0 0 1-6.04049333-40.63604603c0-84.68893734 72.91302492-153.39192148 165.22884838-153.39192144 6.28455307 0 12.56910793 0.48812128 18.73163023 1.1592878-16.77914873-78.09930826-100.55285804-136.12465325-196.04146528-136.12465325z m136.063637 223.07114808c14.64362017 0 24.28400405 9.64038388 24.28400408 19.34178048 0 9.76241287-9.64038388 19.34178225-24.28400408 19.34178225-9.64038388 0-19.40279674-9.57936761-19.40279676-19.34178225 0-9.70139837 9.76241287-19.34178225 19.40279676-19.34178048z m106.89842736 0c14.52158941 0 24.28400405 9.64038388 24.28400408 19.34178048 0 9.76241287-9.76241287 19.34178225-24.28400408 19.34178225a20.745128 20.745128 0 0 1-19.28076599-19.34178225c0-9.70139837 9.70139837-19.34178225 19.28076599-19.34178048z m-170.11005392-140.63977009l3.84395031 0.24406156c12.44707716 1.52537651 20.44005378 10.67763914 20.44005375 23.97892805 0 14.52158941-9.70139837 24.22298779-24.34501856 24.22298781-14.58260571 0-29.1652096-9.70139837-29.16521137-24.22298781 0.06101449-14.64362017 14.64362017-24.22298779 29.22622587-24.22298961z m-136.06363697 0c14.58260571 0 24.28400405 9.57936761 24.28400228 24.16197331 0 14.52158941-9.70139837 24.22298779-24.28400228 24.22298959s-29.22622589-9.70139837-29.2262259-24.22298959c0-14.58260571 14.70463469-24.1619733 29.2262259-24.16197331z m280.6693863-391.35074951c18.97569175 0 34.35149295 15.37580119 34.35149298 34.35149298v68.64196961a34.35149295 34.35149295 0 0 1-34.35149298 34.35149296h-274.56787846a34.35149295 34.35149295 0 0 1-34.35149298-34.35149296V100.14818223c0-18.97569175 15.37580119-34.35149295 34.35149298-34.35149298h274.56787846z" fill="${getColor(0, color, colors, '#333333')}" />
          </svg>''';
        break;
      case IconNames.saasClean1:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M512 62c249.00000029 0 450 200.99999971 450 450s-200.99999971 450-450 450S62 761.00000029 62 512 262.99999971 62 512 62z m0 24.********C276.80000029 86.******** 86.******** 276.80000029 86.******** 512c0 235.19999971 189.79999981 425.00000039 425.00000039 425.00000039 235.19999971 0 425.00000039-189.79999981 425.00000039-425.00000039 0-235.19999971-189.79999981-425.00000039-425.00000039-425.00000039z m-142.67499961 282.32500078a24.******** 24.******** 0 0 1 32.99999942-2.0750001l2.******** 2.0750001L512 476.6249999l107.325-107.********a24.******** 24.******** 0 0 1 37.******** 32.99999942l-2.0750001 2.********L547.3750001 512l107.******** 107.325 2.0750001 2.********a24.******** 24.******** 0 0 1-35.******** 35.********l-2.********-2.0750001L512 547.3750001l-107.325 107.********a24.******** 24.******** 0 0 1-37.********-32.99999942l2.0750001-2.********L476.6249999 512l-107.********-107.325-2.0750001-2.********a24.******** 24.******** 0 0 1 2.0750001-32.99999942z" fill="${getColor(0, color, colors, '#333333')}" />
          </svg>''';
        break;
      case IconNames.saasAccountManage:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M480.019692 544.019692v287.980308a95.980308 95.980308 0 0 1-96.019692 96.019692H192a95.980308 95.980308 0 0 1-96.019692-96.019692V640a95.980308 95.980308 0 0 1 96.019692-96.019692h288.019692z m351.980308 0a95.980308 95.980308 0 0 1 96.019692 95.980308v192a95.980308 95.980308 0 0 1-96.019692 96.019692H640a95.980308 95.980308 0 0 1-96.019692-96.019692v-288.019692h288.019692z m-416.019692 63.960616H192a32.019692 32.019692 0 0 0-31.783385 28.278154l-0.196923 3.741538v192c0 16.423385 12.327385 29.932308 28.23877 31.783385l3.741538 0.236307H384c16.423385 0 29.932308-12.366769 31.783385-28.278154l0.196923-3.741538v-224.019692z m416.019692 0h-224.019692v224.019692c0 16.423385 12.366769 29.932308 28.278154 31.783385l3.741538 0.236307h192c16.423385 0 29.932308-12.366769 31.783385-28.278154l0.236307-3.741538V640a32.019692 32.019692 0 0 0-28.278154-31.783385l-3.741538-0.196923z m-448-512a95.980308 95.980308 0 0 1 96.019692 96.019692v288.019692H192A95.980308 95.980308 0 0 1 95.980308 384V192a95.980308 95.980308 0 0 1 96.019692-96.019692H384z m448 0a95.980308 95.980308 0 0 1 96.019692 96.019692V384a95.980308 95.980308 0 0 1-96.019692 96.019692h-288.019692V192a95.980308 95.980308 0 0 1 96.019692-96.019692h192zM384 160.019692H192a32.019692 32.019692 0 0 0-31.783385 28.23877l-0.196923 3.741538V384c0 16.423385 12.327385 29.932308 28.23877 31.783385l3.741538 0.196923h224.019692V192a32.019692 32.019692 0 0 0-24.694154-31.153231l-3.584-0.630154-3.741538-0.196923z m448 0H640a32.019692 32.019692 0 0 0-31.783385 28.23877l-0.196923 3.741538-0.039384 224.019692h224.019692c16.423385 0 29.932308-12.366769 31.783385-28.278154l0.236307-3.741538V192a32.019692 32.019692 0 0 0-28.278154-31.783385l-3.741538-0.196923z" fill="${getColor(0, color, colors, '#333333')}" />
          </svg>''';
        break;
      case IconNames.saasContract:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M323.46919964 166.36019883v56.55924011a37.70616007 37.70616007 0 0 0 33.84127866 37.51762928L361.17535969 260.62559901h301.64928062a37.70616007 37.70616007 0 0 0 37.51762927-33.84127867L700.53080036 222.91943894V166.36019883h157.10900081a62.84360061 62.84360061 0 0 1 62.84359916 62.8436006v628.43600174a62.84360061 62.84360061 0 0 1-62.84359916 62.84359916H166.36019883a62.84360061 62.84360061 0 0 1-62.84359916-62.84359916V229.20379943a62.84360061 62.84360061 0 0 1 62.84359916-62.8436006h157.10900081z m370.93434971 253.73103599l-235.91487536 44.74464377 35.41236918 45.31023472-11.18616131 8.73526091c-58.60165762 46.3785769-99.79563747 88.29525867-123.64478227 125.78146515-25.32597134 39.84284298-38.08322167 77.76895517-38.30317527 113.80976081 17.75331653-25.82871965 41.22540118-50.11777159 70.29056724-72.83573303 2.76511791-2.16810421 5.78161072-4.43047379 9.01805711-6.72426473l10.46345944-7.22701451 11.90886171-7.66691873c4.17909891-2.63943121 8.64099551-5.34170651 13.32284275-8.1696685l14.76824651-8.57815142 16.18222751-9.1123225 8.64099404-4.74469131 18.31891043-9.80360163 19.76431125-10.27492861 21.20971503-10.77767694 22.65511884-11.2490054 36.57497527 46.84990389 100.5183379-218.06729194zM637.68719975 103.51659967a31.42179956 31.42179956 0 0 1 31.42180105 31.42179958v62.84360061a31.42179956 31.42179956 0 0 1-31.42180105 31.42179957h-251.3743995a31.42179956 31.42179956 0 0 1-31.42180105-31.42179957v-62.84360061a31.42179956 31.42179956 0 0 1 31.42180105-31.42179958h251.3743995z" fill="${getColor(0, color, colors, '#333333')}" />
          </svg>''';
        break;
      case IconNames.saasClock:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M512 938.688A426.688 426.688 0 1 1 512 85.376a426.688 426.688 0 0 1 0 853.312zM554.688 512V341.312a42.688 42.688 0 0 0-85.376 0v213.376c0 23.552 19.136 42.624 42.688 42.624h170.688a42.688 42.688 0 0 0 0-85.312h-128z" fill="${getColor(0, color, colors, '#333333')}" />
          </svg>''';
        break;
      case IconNames.saasClock2:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M751.68 254.656l31.872-31.872a42.688 42.688 0 1 1 60.288 60.352l-31.808 31.808a384 384 0 1 1-60.352-60.288zM469.312 341.312v256h85.376v-256H469.312zM384 42.688h256A42.688 42.688 0 0 1 640 128H384A42.688 42.688 0 0 1 384 42.688z" fill="${getColor(0, color, colors, '#333333')}" />
          </svg>''';
        break;
      case IconNames.saasCheck:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M512 85.312a426.688 426.688 0 1 1 0 853.312A426.688 426.688 0 0 1 512 85.312z m243.52 268.544a42.688 42.688 0 0 0-60.352 0L469.312 579.648 371.52 481.856l-4.032-3.584a42.688 42.688 0 0 0-56.32 63.872l128 128 4.032 3.584c16.704 12.992 40.96 11.84 56.32-3.584l256-256 3.52-3.968a42.688 42.688 0 0 0-3.52-56.32z" fill="${getColor(0, color, colors, '#333333')}" />
          </svg>''';
        break;
      case IconNames.saasWarningCircleFill:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64z m-32 232c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v272c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V296z m32 440c-26.5 0-48-21.5-48-48s21.5-48 48-48 48 21.5 48 48-21.5 48-48 48z" fill="${getColor(0, color, colors, '#333333')}" />
          </svg>''';
        break;
      case IconNames.saasVideo:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M512 64c247.414154 0 448 200.585846 448 448S759.414154 960 512 960 64 759.414154 64 512 264.585846 64 512 64z m0 64c-212.086154 0-384 171.913846-384 384s171.913846 384 384 384 384-171.913846 384-384S724.086154 128 512 128z m-83.140923 168.644923c6.301538 0 12.484923 1.851077 17.723077 5.356308l294.321231 196.214154a32.019692 32.019692 0 0 1 0 53.248l-294.321231 196.214153a31.980308 31.980308 0 0 1-49.742769-26.624V328.625231c0-17.683692 14.336-31.980308 32.019692-31.980308z m31.980308 91.766154v272.856615l204.603077-136.428307-204.603077-136.428308z" fill="${getColor(0, color, colors, '#333333')}" />
          </svg>''';
        break;
      case IconNames.saasFeedback:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M805.927385 500.145231c8.664615 0 17.408 3.268923 24.024615 9.846154l120.123077 119.453538a33.673846 33.673846 0 0 1 0 47.773539l-242.254769 240.876307a34.067692 34.067692 0 0 1-24.024616 9.924923h-120.123077a33.870769 33.870769 0 0 1-33.949538-33.792v-119.414154c0-8.979692 3.544615-17.565538 9.924923-23.906461l242.254769-240.876308a33.949538 33.949538 0 0 1 24.024616-9.885538zM642.402462 64c8.782769 0 17.211077 3.426462 23.47323 9.531077L821.956923 226.067692a33.161846 33.161846 0 0 1 10.003692 23.670154v163.170462c0 18.353231-14.966154 33.240615-33.476923 33.240615a33.358769 33.358769 0 0 1-33.437538-33.240615V329.846154h-133.828923a66.796308 66.796308 0 0 1-66.953846-66.363077l-0.07877-133.041231H162.894769v731.096616h267.657846c18.471385 0 33.476923 14.887385 33.476923 33.240615 0 18.353231-15.005538 33.201231-33.476923 33.201231H129.457231a33.358769 33.358769 0 0 1-33.476923-33.201231V97.240615c0-18.353231 14.966154-33.240615 33.476923-33.240615z m163.524923 517.671385l-208.265847 207.123692v71.68h72.073847l208.265846-207.163077-72.073846-71.640615z m-344.14277 76.091077a33.870769 33.870769 0 0 1 33.949539 33.792 33.870769 33.870769 0 0 1-33.949539 33.792H257.969231a33.870769 33.870769 0 0 1-33.949539-33.792 33.870769 33.870769 0 0 1 33.949539-33.792z m169.826462-168.881231a33.870769 33.870769 0 0 1 33.988923 33.792 33.870769 33.870769 0 0 1-33.988923 33.752615H257.969231a33.870769 33.870769 0 0 1-33.949539-33.752615 33.870769 33.870769 0 0 1 33.949539-33.792z m-169.826462-168.881231a33.870769 33.870769 0 0 1 33.949539 33.792 33.870769 33.870769 0 0 1-33.949539 33.752615H257.969231a33.870769 33.870769 0 0 1-33.949539-33.752615 33.870769 33.870769 0 0 1 33.949539-33.792z m169.353847-187.195077l0.118153 130.599385h133.513847l-133.632-130.599385z" fill="${getColor(0, color, colors, '#333333')}" />
          </svg>''';
        break;
      case IconNames.saasShare:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M414.03208213 102.4a31.5076928 31.5076928 0 0 1 3.6969024 62.80533333l-3.6969024 0.2100512H259.93846187a94.52307733 94.52307733 0 0 0-94.35503574 88.97772267L165.41538453 259.93846187v504.12307626a94.52307733 94.52307733 0 0 0 88.97772267 94.35503574L259.93846187 858.58461547h504.12307626a94.52307733 94.52307733 0 0 0 94.35503574-88.97772267L858.58461547 764.06153813v-157.58047146a31.5076928 31.5076928 0 0 1 62.80533333-3.6969024l0.2100512 3.6969024V764.06153813a157.53846187 157.53846187 0 0 1-150.69078933 157.41243094L764.06153813 921.6H259.93846187a157.53846187 157.53846187 0 0 1-157.41243094-150.69078933L102.4 764.06153813V259.93846187a157.53846187 157.53846187 0 0 1 150.69078933-157.41243094L259.93846187 102.4h154.09362026z m302.64188694 6.7636512l2.98272853 2.688656 158.46268693 161.739488c19.11466667 19.4507488 6.9737024 52.17673813-18.8205952 54.99142507l-3.57087146 0.1680416h-253.5318976c-50.62235947 0-92.00246187 40.371856-94.94317974 91.3302976l-0.12602986 5.67138453v258.6991584c0 17.85435947-14.19946667 32.3478976-31.717744 32.3478976a31.9277952 31.9277952 0 0 1-31.46568214-28.56697387l-0.2100512-3.78092373v-258.6991584c0-86.96123093 67.25842027-157.83253333 151.5730048-161.5294368l6.88968214-0.12602987h176.98921066l-104.35347733-106.496a32.85202027 32.85202027 0 0 1-2.60463573-42.68242133l2.60463573-3.0667488a31.21362027 31.21362027 0 0 1 41.84221547-2.688656z" fill="${getColor(0, color, colors, '#333333')}" />
          </svg>''';
        break;
      case IconNames.saasChange2:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M803.17402112 560.54518557c41.36049778 0 62.96310557 47.86555221 38.01088 78.78883555l-3.68943445 4.07779555-194.18074112 194.18073999a48.54518557 48.54518557 0 0 1-72.67214222-64.07964445l4.02925112-4.56324664L685.98594333 657.63555555H220.6317989a48.54518557 48.54518557 0 0 1-48.20536889-42.86539889L172.08661333 609.09036999a48.54518557 48.54518557 0 0 1 42.8653989-48.20536889L220.6317989 560.54518557h582.54222222z m-353.8944-374.13774223a48.54518557 48.54518557 0 0 1 3.98070443 64.07964445l-4.02925 4.56324664L337.91696555 366.36444445H803.27111111a48.54518557 48.54518557 0 0 1 48.20536889 42.86539889L851.81629667 414.90963001a48.54518557 48.54518557 0 0 1-42.86539889 48.20536889L803.27111111 463.45481443H220.72888889c-41.36049778 0-62.96310557-47.86555221-38.01088-78.78883555l3.68943445-4.07779555 194.18073999-194.18073999a48.54518557 48.54518557 0 0 1 68.64289222 0z" fill="${getColor(0, color, colors, '#333333')}" />
          </svg>''';
        break;
      case IconNames.saasPlusRound:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M512 32a480 480 0 1 0 0 960A480 480 0 0 0 512 32z m240 540H572v180a60 60 0 1 1-120 0V572H272a60 60 0 1 1 0-120h180V272a60 60 0 0 1 120 0v180h180a60 60 0 1 1 0 120z" fill="${getColor(0, color, colors, '#333333')}" />
          </svg>''';
        break;
      case IconNames.saasSubRound:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M512 32a480 480 0 1 0 0 960A480 480 0 0 0 512 32z m240 540H272a60 60 0 1 1 0-120h480a60 60 0 1 1 0 120z" fill="${getColor(0, color, colors, '#333333')}" />
          </svg>''';
        break;
      case IconNames.saasQrCode:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M914.286 556.617v212.992H701.294V628.187h-70.949v286.098h-71.022V557.494h212.992v141.385h70.949V556.616h71.022z m-449.609 0.878v353.499H109.715V557.495h354.962z m449.609 282.807v70.693H701.294v-70.693h212.992zM393.691 628.187H180.662v212.114h212.992V628.187z m-70.985 70.73v70.693h-71.022v-70.729h71.022z m141.971-589.203v353.499H109.715V109.714h354.962z m449.609 0v353.499H559.324V109.714h354.962z m-520.595 70.693H180.662v212.114h212.992V180.407z m449.61 0H630.309v212.114h212.992V180.407z m-520.595 70.729v70.693h-71.022v-70.693h71.022z m449.609 0v70.693h-71.022v-70.693h71.022z" fill="${getColor(0, color, colors, '#333333')}" />
          </svg>''';
        break;
      case IconNames.saasDownload1:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M912 672c16.411 0 29.936 12.353 31.785 28.268L944 704v112c0 68.55-53.887 124.515-121.612 127.843L816 944H240c-68.55 0-124.515-53.887-127.843-121.612L112 816V704c0-17.673 14.327-32 32-32 16.411 0 29.936 12.353 31.785 28.268L176 704v112c0 33.74 26.108 61.381 59.224 63.824L240 880h576c33.74 0 61.381-26.108 63.824-59.224L880 816V704c0-17.673 14.327-32 32-32zM642.06 64c17.958 0 32.515 14.557 32.515 32.515l-0.025 260.12h156.935a32.514 32.514 0 0 1 21.16 7.828c13.634 11.687 15.213 32.213 3.527 45.848L536.687 783.043a32.626 32.626 0 0 1-3.527 3.527c-13.634 11.687-34.161 10.108-45.848-3.527L167.827 410.311a32.516 32.516 0 0 1-7.828-21.16c0-17.958 14.557-32.515 32.515-32.515h156.885l0.025-260.12c0-17.958 14.557-32.515 32.515-32.515z" fill="${getColor(0, color, colors, '#333333')}" />
          </svg>''';
        break;
      case IconNames.saasWorkerBook:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M875.959 357.632c8.046 0 15.323-3.291 20.626-8.594 5.281-5.265 8.55-12.545 8.558-20.588v-87.553-0.073c0-16.098-13.05-29.147-29.147-29.147h-0.039 0.002-0.011c-8.054 0-15.345-3.271-20.615-8.557l-0.001-0.001c-5.287-5.271-8.558-12.561-8.558-20.616v-0.011 0.001-87.552c0-32.293-26.185-58.368-58.368-58.368H204.653c-32.183 0-58.368 26.075-58.368 58.368v817.225c0 32.183 26.185 58.368 58.368 58.368h583.753c32.183 0 58.368-26.149 58.368-58.368v-58.368c0-16.128 13.019-29.184 29.184-29.184h0.073c16.077 0 29.111-13.033 29.111-29.111v-0.077 0.004-87.552-0.037c0-16.098-13.05-29.147-29.147-29.147h-0.039 0.002c-8.063-0.007-15.358-3.292-20.625-8.593l-0.002-0.002c-5.281-5.265-8.55-12.545-8.558-20.588v-29.185c0-16.165 13.019-29.221 29.184-29.221h0.073c16.077 0 29.111-13.033 29.111-29.111v-0.077 0.004-87.552-0.037c0-16.098-13.05-29.147-29.147-29.147h-0.039 0.002-0.011c-8.054 0-15.345-3.271-20.615-8.557l-0.001-0.001c-5.287-5.271-8.558-12.561-8.558-20.616V416v0.001-29.184c0-16.165 13.019-29.184 29.184-29.184zM627.858 707.877H365.202c-8.063-0.007-15.358-3.292-20.625-8.593l-0.002-0.002c-5.294-5.26-8.576-12.54-8.594-20.586v-0.003c0-52.078 24.832-98.341 63.232-127.707a7.239 7.239 0 0 0 2.895-5.792 7.225 7.225 0 0 0-2.449-5.429l-0.008-0.007c-30.209-26.858-49.147-65.824-49.147-109.214 0-79.945 64.29-144.874 143.989-145.907l0.098-0.001c0.442-0.005 0.965-0.008 1.488-0.008 79.842 0 144.745 63.965 146.259 143.445l0.002 0.142c0.016 0.8 0.025 1.742 0.025 2.687 0 43.219-18.865 82.032-48.81 108.653l-0.148 0.129a7.302 7.302 0 0 0-2.515 5.52 7.303 7.303 0 0 0 2.863 5.805l0.017 0.013a159.883 159.883 0 0 1 45.934 55.223c11.045 21.723 17.189 46.336 17.189 72.338l0.002 0.366c0 15.956-12.935 28.891-28.891 28.891h-0.157 0.008z" fill="${getColor(0, color, colors, '#333333')}" />
          </svg>''';
        break;
      case IconNames.saasWorkerAdd:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M759.771 882.615h-0.025c-52.966 0-100.948-21.342-135.815-55.895l0.014 0.014c-17.125-17.05-31.041-37.307-40.774-59.796l-0.479-1.242c-9.479-21.565-14.996-46.705-14.996-73.132l0.002-0.936v0.048c0-26.112 5.01-50.798 14.994-74.057 10.118-23.594 24.032-43.709 41.186-60.533l0.03-0.029a197.205 197.205 0 0 1 60.08-40.033l1.287-0.488c21.681-9.412 46.934-14.887 73.465-14.887 0.362 0 0.725 0.001 1.087 0.003h-0.056c0.296-0.002 0.645-0.003 0.995-0.003 26.531 0 51.784 5.475 74.689 15.357l-1.225-0.47c23.406 9.947 43.703 23.442 60.891 40.521s30.72 37.266 40.741 60.562c9.984 23.259 14.994 47.945 14.994 74.057 0 26.075-5.01 50.761-14.994 74.021-10.157 23.677-23.893 43.918-40.766 61.064l0.025-0.026c-16.968 17.091-37.191 30.924-59.666 40.496l-1.226 0.464c-21.704 9.435-46.986 14.923-73.55 14.923l-0.958-0.002h0.049z m94.611-221.696h-69.339v-64.256a32.07 32.07 0 0 0-8.433-22.406l0.022 0.024c-5.113-5.719-12.513-9.301-20.749-9.301-0.291 0-0.581 0.004-0.87 0.013l0.042-0.001a23.92 23.92 0 0 0-1.116-0.025c-7.847 0-14.851 3.614-19.437 9.268l-0.037 0.047c-4.679 5.844-7.508 13.346-7.508 21.508 0 0.307 0.004 0.614 0.012 0.919l-0.001-0.045v64.256h-60.891a30.203 30.203 0 0 0-0.443-0.003c-8.647 0-16.459 3.572-22.041 9.321l-0.007 0.008c-5.776 5.541-9.365 13.323-9.365 21.943l0.003 0.423v-0.021c0 8.704 3.145 15.214 9.362 19.566 6.254 4.352 13.751 6.51 22.491 6.51h60.891v66.158c0 8.667 2.487 16.128 7.497 22.309 5.01 6.217 11.849 9.362 20.626 9.362 0.226 0.007 0.493 0.01 0.76 0.01 8.253 0 15.666-3.597 20.756-9.308l0.024-0.028a32.045 32.045 0 0 0 8.411-22.379l0.001 0.034v-66.194h69.339v1.865c8.741 0 16.238-2.487 22.455-7.461 5.76-4.575 9.421-11.578 9.421-19.436 0-0.367-0.008-0.733-0.024-1.096l0.002 0.052 0.002-0.367c0-8.638-3.604-16.434-9.39-21.967l-0.011-0.011c-5.59-5.756-13.402-9.328-22.048-9.328-0.143 0-0.286 0.001-0.428 0.003h0.022zM622.994 428.983c-2.487 9.911-5.01 18.944-7.497 26.99-3.089 8.339-6.242 15.259-9.817 21.918l0.454-0.926c-3.139 6.344-7.223 11.737-12.137 16.237l-0.041 0.037c-4.971 3.61-8.94 8.304-11.606 13.752l-0.096 0.218a130.644 130.644 0 0 0-5.632 15.36 581.257 581.257 0 0 1-5.12 16.311c-2.312 6.437-5.497 12.002-9.48 16.889l0.081-0.103c-12.491 15.61-23.319 33.393-31.727 52.482l-0.602 1.534c-6.283 14.418-11.478 31.29-14.763 48.836l-0.231 1.486c-1.851 9.987-2.909 21.479-2.909 33.218 0 5.183 0.206 10.318 0.611 15.397l-0.043-0.67c1.243 15.835 3.767 31.232 7.497 46.117 2.523 11.191 6.254 22.821 11.264 34.926 6.166 14.226 13.541 26.493 22.291 37.629l-0.275-0.363c9.655 12.727 22.016 25.161 36.974 37.266 14.994 12.105 33.719 23.771 56.21 34.926-14.994 3.109-32.475 5.888-52.443 8.411-42.597 4.733-91.987 7.433-142.008 7.433-1.787 0-3.573-0.003-5.359-0.01l0.276 0.001c-16.238 0-35.109-0.622-56.686-1.829-30.817-1.763-53.826-3.435-76.756-5.385l10.269 0.703c-22.784-1.865-45.275-4.023-67.438-6.546-28.217-3.048-49.047-5.834-69.745-9.021l8.854 1.122c-19.157-2.779-35.031-5.871-50.649-9.621l3.801 0.77c-12.8-3.109-20.773-5.888-23.881-8.411-5.632-4.937-9.984-18.761-13.129-41.435-3.109-22.674-2.194-52.297 2.816-88.942 3.109-20.48 11.41-36.206 24.832-47.031 13.459-10.787 29.159-19.49 46.205-25.281l1.082-0.319a862.858 862.858 0 0 1 56.686-17.262c19.729-5.152 37.039-12.657 52.837-22.383l-0.832 0.477c11.849-7.424 20.919-14.409 27.173-20.955 6.217-6.51 10.606-13.166 13.093-20.005s3.767-13.97 3.767-21.431-0.329-15.543-0.951-24.21c-1.243-13.056-5.778-23.296-13.568-30.757-7.826-7.461-16.091-14.885-24.869-22.345-5.535-4.475-9.965-10.088-12.973-16.502l-0.12-0.284c-2.996-5.702-6.149-12.768-8.898-20.032l-0.465-1.399c-3.001-7.625-5.598-16.681-7.349-26.035l-0.148-0.954c-4.633-1.331-8.68-3.242-12.334-5.694l0.156 0.099a62.947 62.947 0 0 1-10.188-11.009l-0.125-0.182c-3.73-4.974-7.168-12.434-10.277-22.345-2.257-6.062-3.563-13.067-3.563-20.376 0-2.007 0.098-3.991 0.291-5.948l-0.02 0.248c0.73-7.552 2.544-14.493 5.294-20.939l-0.174 0.459c2.56-6.839 5.961-12.763 10.35-17.701 0-21.138 1.243-42.24 3.73-63.342 2.487-18.03 6.4-37.303 11.703-57.783s13.897-38.766 25.783-54.93c11.227-15.543 23.259-28.27 36.059-38.217 12.8-9.911 26.075-17.701 39.79-23.259 11.863-5.05 25.714-9.109 40.125-11.516l1.091-0.151c13.787-2.158 26.88-3.255 39.387-3.255 16.238 0 31.854 1.829 46.848 5.595 14.994 3.73 28.891 8.704 41.691 14.885 12.8 6.217 24.21 13.202 34.194 20.955 9.984 7.79 18.103 15.726 24.357 23.771 13.63 16.864 24.36 36.724 31.045 58.36l0.333 1.252c6.583 21.723 11.41 42.24 14.519 61.477 3.109 22.345 4.389 44.727 3.73 67.072 3.767 3.109 6.875 6.839 9.399 11.191 2.487 3.73 4.389 8.667 5.595 14.885 1.243 6.217 1.243 13.97 0 23.296-1.243 11.813-3.73 21.102-7.497 27.941-3.211 6.157-7.283 11.396-12.132 15.794l-0.047 0.042a46.438 46.438 0 0 1-15.582 9.226l-0.326 0.1z" fill="${getColor(0, color, colors, '#333333')}" />
          </svg>''';
        break;
      case IconNames.saasHandle:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M136.53333333 273.06666667m34.13333334 0l716.8 0q34.13333333 0 34.13333333 34.13333333l0 0q0 34.13333333-34.13333333 34.13333333l-716.8 0q-34.13333333 0-34.13333334-34.13333333l0 0q0-34.13333333 34.13333334-34.13333333Z" fill="${getColor(0, color, colors, '#333333')}" />
            <path d="M136.53333333 477.86666667m34.13333334 0l716.8 0q34.13333333 0 34.13333333 34.13333333l0 0q0 34.13333333-34.13333333 34.13333333l-716.8 0q-34.13333333 0-34.13333334-34.13333333l0 0q0-34.13333333 34.13333334-34.13333333Z" fill="${getColor(1, color, colors, '#333333')}" />
            <path d="M136.53333333 682.66666667m34.13333334 0l716.8 0q34.13333333 0 34.13333333 34.13333333l0 0q0 34.13333333-34.13333333 34.13333333l-716.8 0q-34.13333333 0-34.13333334-34.13333333l0 0q0-34.13333333 34.13333334-34.13333333Z" fill="${getColor(2, color, colors, '#333333')}" />
          </svg>''';
        break;
      case IconNames.saasImage:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M959.872 249.6A128.064 128.064 0 0 0 832 128H192l-6.4 0.128A128.064 128.064 0 0 0 64 256v512l0.128 6.4A128.064 128.064 0 0 0 192 896h640l8.256-0.256a126.848 126.848 0 0 0 66.368-23.744l4.736-3.648c29.632-23.36 48.64-59.648 48.64-100.352V256l-0.128-6.4zM256 384a96 96 0 1 1 191.936-0.064A96 96 0 0 1 256 384z m640 384l-0.192 4.736-0.192 2.496a63.168 63.168 0 0 1-9.088 26.176l-2.304 3.584-2.944 3.776-3.52 3.968-2.816 2.752-3.648 3.008-3.648 2.688-3.904 2.368-3.136 1.664-5.248 2.368-3.712 1.344-4.224 1.28-5.952 1.152-4.736 0.576L832 832H389.76l2.112-10.624A352.32 352.32 0 0 1 736 544l12.48 0.256 12.416 0.64c45.44 3.2 89.216 14.976 129.408 34.688l5.696 2.88V768z" fill="${getColor(0, color, colors, '#333333')}" />
          </svg>''';
        break;
      case IconNames.saasDaiban:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M512 90.666667c232.704 0 421.333333 188.650667 421.333333 421.333333 0 58.069333-11.754667 113.386667-32.981333 163.733333C836.394667 827.114667 686.634667 933.333333 512 933.333333a420.693333 420.693333 0 0 1-211.882667-57.088H166.208v-123.477333a420.16 420.16 0 0 1-38.464-67.733333v-0.64l-1.6-3.008A419.413333 419.413333 0 0 1 90.666667 512C90.666667 279.317333 279.317333 90.666667 512 90.666667z m200.362667 404.650666H311.189333v101.546667h47.850667v-53.482667h127.616v38.784h-117.781333v169.28h47.850666V629.76h69.909334v153.088H535.466667V629.76h71.786666v57.898667c0 10.794667-3.754667 16.661333-10.794666 16.661333l-38.954667-2.432 12.693333 49.088h41.728c28.629333 0 43.178667-17.664 43.178667-52.522667v-116.266666H535.466667v-38.784h129.024v51.050666h47.872v-99.136z m-178.325334-163.882666H486.186667v46.613333h-74.602667v-44.650667H366.08v44.650667h-64.746667v48.576h64.746667v44.650667h45.504v-44.650667h74.602667v46.613333h47.850666v-46.613333h76.010667v44.650667h45.994667v-44.650667h66.624v-48.576h-66.624v-44.650667h-45.994667v44.650667h-76.010667v-46.613333z" fill="${getColor(0, color, colors, '#333333')}" />
          </svg>''';
        break;
      case IconNames.saasSettingSolid:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M908.704 597.792l-63.52-86.752 63.008-86.016 2.048-3.296a29.632 29.632 0 0 0 2.56-22.272 414.784 414.784 0 0 0-105.248-179.712l-2.88-2.56-3.136-2.112a29.44 29.44 0 0 0-18.176-3.904l-104.736 11.328-42.016-95.328-1.792-3.424a29.696 29.696 0 0 0-17.984-13.344 415.616 415.616 0 0 0-103.424-12.992c-36.352 0-72.128 4.672-106.656 13.856l-3.616 1.216-3.424 1.664a30.016 30.016 0 0 0-12.48 13.856l-41.728 94.528-102.24-11.104-3.84-0.16a29.504 29.504 0 0 0-20.384 8.736 414.592 414.592 0 0 0-105.76 182.336l-0.8 3.744-0.256 3.808a29.664 29.664 0 0 0 5.728 17.792l61.056 83.328-61.568 84.096-2.048 3.232a29.664 29.664 0 0 0-2.656 21.984 414.4 414.4 0 0 0 103.744 181.824l2.88 2.592 3.168 2.144a29.568 29.568 0 0 0 18.368 4l104.672-11.36 43.104 97.632 1.76 3.392a29.76 29.76 0 0 0 17.856 13.312 416.448 416.448 0 0 0 207.2 0.832l3.712-1.216 3.424-1.664a29.536 29.536 0 0 0 12.672-13.952l43.36-98.368 107.136 11.648 3.872 0.16a29.568 29.568 0 0 0 20.512-8.896 414.144 414.144 0 0 0 103.264-179.232l0.768-3.744 0.288-3.808a29.824 29.824 0 0 0-5.792-17.824z m-396.64 91.04a177.792 177.792 0 1 1 0-355.648 177.856 177.856 0 0 1 0 355.648z" fill="${getColor(0, color, colors, '#333333')}" />
            <path d="M512.064 511.008m-118.528 0a118.528 118.528 0 1 0 237.056 0 118.528 118.528 0 1 0-237.056 0Z" fill="${getColor(1, color, colors, '#333333')}" />
          </svg>''';
        break;
      case IconNames.saasDaka:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M512 64c194.4 0 352 157.6 352 352 0 123.136-105.92 283.296-317.696 480.544l-12.576 11.648a31.904 31.904 0 0 1-40.064 2.688l-3.2-2.56C270.144 705.696 160 541.568 160 416 160 221.6 317.6 64 512 64z m0 96a256 256 0 1 0 0 512 256 256 0 0 0 0-512z m0 93.344a32 32 0 0 1 31.776 28.256l0.224 3.744v128h128a32 32 0 0 1 31.776 28.256l0.224 3.744a32 32 0 0 1-28.256 31.776l-3.744 0.224h-160a32 32 0 0 1-31.776-28.256L480 445.344v-160a32 32 0 0 1 32-32z" fill="${getColor(0, color, colors, '#333333')}" />
          </svg>''';
        break;
      case IconNames.saasTongji:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M768 64a128 128 0 0 1 128 128v640a128 128 0 0 1-128 128H256a128 128 0 0 1-128-128V192a128 128 0 0 1 128-128z m-64 640H352l-3.744 0.224A32 32 0 0 0 352 768h352l3.744-0.224A32 32 0 0 0 704 704zM627.712 201.376a32 32 0 0 0-42.464-2.496l-2.816 2.496-67.872 67.872-2.72 3.136a32.32 32.32 0 0 0-2.784-3.136l-67.84-67.872-2.816-2.496a32 32 0 0 0-42.464 47.744L437.312 288H384l-3.744 0.224A32 32 0 0 0 384 352h96v64h-96l-3.744 0.224A32 32 0 0 0 384 480h96v96l0.224 3.744A32 32 0 0 0 544 576v-96h96l3.744-0.224A32 32 0 0 0 640 416h-96v-64h96l3.744-0.224A32 32 0 0 0 640 288h-53.696l41.408-41.376 2.656-3.008a32 32 0 0 0-2.656-42.24z" fill="${getColor(0, color, colors, '#333333')}" />
          </svg>''';
        break;
      case IconNames.saasShigong:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M736.128 64a64 64 0 0 1 63.552 56.832 8.032 8.032 0 0 0 8 7.104h56.352A64 64 0 0 1 928 191.936v696.064A72 72 0 0 1 855.968 960H200A72 72 0 0 1 128 888v-111.936a8.064 8.064 0 0 1 8.128-8.128H192c17.664-0.064 32-14.368 32.032-32.032v-23.744c0-22.144-17.952-40.096-40.128-40.096H136.128A8.064 8.064 0 0 1 128 664v-47.904c0-4.48 3.616-8.096 8.096-8.096H192c17.696 0 32-14.336 32.032-32v-23.968c0-22.144-17.952-40.096-40.128-40.096H136.128A8.064 8.064 0 0 1 128 503.872v-47.776c0-4.48 3.616-8.096 8.096-8.096h47.744A40.128 40.128 0 0 0 224 407.904v-15.872c0-22.144-17.952-40.128-40.128-40.128H136.064A8.064 8.064 0 0 1 128 343.84V296.064a8.096 8.096 0 0 1 8.128-8.096H192c17.664-0.032 32-14.368 32-32.032V232a40.096 40.096 0 0 0-40.128-40.096H136.064A8.064 8.064 0 0 1 128 183.808V136.032A72 72 0 0 1 200.064 64z m72 96.096a8.096 8.096 0 0 0-8.16 8.096v623.584a72 72 0 0 1-72 72H168.704a8.096 8.096 0 0 0-8 9.312c4.672 31.36 31.584 54.592 63.296 54.656h576.064c61.984 0 63.936-28.672 63.936-63.968h-0.096V224.032c0-59.2-24.32-63.584-55.776-63.936zM337.184 657.312a8.064 8.064 0 0 0-11.968 7.712l7.872 94.56a8.064 8.064 0 0 0 10.848 6.88l96.928-36a8.064 8.064 0 0 0 1.12-14.624zM181.696 704c5.696 0 10.304 4.608 10.304 10.304v11.424a10.304 10.304 0 0 1-10.304 10.304H106.304A10.304 10.304 0 0 1 96 725.696v-11.424c0-5.696 4.608-10.304 10.304-10.304h75.392z m448.928-508.16c-13.664-7.52-36.928-1.568-44.8 11.776L344.64 615.04a8.064 8.064 0 0 0 2.944 11.168l119.2 66.688c3.84 2.144 8.64 0.832 10.88-2.944l241.408-407.808c7.84-13.312 1.568-35.84-12.096-43.552zM181.696 544.032a10.272 10.272 0 0 1 10.304 10.304v11.424c0 5.696-4.608 10.272-10.304 10.272H106.304A10.304 10.304 0 0 1 96 565.76v-11.392a10.304 10.304 0 0 1 10.304-10.304h75.392z m0-160c5.696 0 10.304 4.64 10.304 10.336v11.424a10.304 10.304 0 0 1-10.304 10.304H106.304A10.304 10.304 0 0 1 96 405.76v-11.424c0-5.696 4.608-10.304 10.304-10.304h75.392z m0-160.032c5.696 0 10.304 4.608 10.304 10.304v11.456a10.304 10.304 0 0 1-10.304 10.304H106.304A10.304 10.304 0 0 1 96 245.76v-11.456C96 228.608 100.608 224 106.304 224h75.392z" fill="${getColor(0, color, colors, '#333333')}" />
          </svg>''';
        break;
      case IconNames.saasDoubleArrow:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M902.39204939 587.98376812a33.42946539 33.42946539 0 0 0 3.73982609-43.96203757l-2.74762748-3.20556521-383.90459583-400.69565218a33.42946539 33.42946539 0 0 0-46.40437252-1.83175095l-2.90027374 2.97659582-350.62777565 400.69565218a33.3531433 33.3531433 0 0 0 47.320248 46.93863339l2.97659583-2.97659583 326.66236069-373.21937948 358.71801287 374.36422435a33.42946539 33.42946539 0 0 0 43.96203652 3.66350296l3.20556522-2.67130435z" fill="${getColor(0, color, colors, '#333333')}" />
            <path d="M902.39204939 888.54366829a33.42946539 33.42946539 0 0 0 3.73982609-43.96203652l-2.74762748-3.20556522-383.90459583-400.69565217a33.42946539 33.42946539 0 0 0-46.40437252-1.831752l-2.90027374 2.90027374-350.62777565 400.69565217a33.3531433 33.3531433 0 0 0 47.320248 47.01495652l2.97659583-3.05292 326.66236069-373.21937843 358.71801287 374.3642233a33.42946539 33.42946539 0 0 0 43.96203652 3.73982609l3.20556522-2.74762748z" fill="${getColor(1, color, colors, '#333333')}" />
          </svg>''';
        break;
      case IconNames.saasDownload:
        svgXml = '''
          <svg viewBox="0 0 1122 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M528.53045298 126.36407868h65.287616c-2.709436 152.446582 0 304.778911 8.160952 457.013309l155.058087-155.058087c34.390252-13.938906 56.147349-3.068518 65.287615 32.643808a1152.783428 1152.783428 0 0 1-261.150462 252.98951 1152.783428 1152.783428 0 0 1-261.150462-252.98951c9.140266-35.712326 30.897364-46.582714 65.287615-32.643808l155.058087 155.058087c8.160952-152.234398 10.870388-304.566727 8.160952-457.013309z" fill="${getColor(0, color, colors, '#323232')}" />
            <path d="M202.09237498 632.34309968c16.256616-1.403684 29.869084 4.047832 40.80476 16.321903a843.548637 843.548637 0 0 0 24.482856 155.058087 1410.473647 1410.473647 0 0 0 293.79427 24.482856 2513.475269 2513.475269 0 0 0 277.472366-16.321904c26.767922-49.912382 40.38039-104.32961 40.80476-163.219039 16.321904-21.757098 32.643808-21.757098 48.965712 0a497.801747 497.801747 0 0 1 0 179.540943L871.29043498 885.33260968a5895.993989 5895.993989 0 0 1-620.232348 0c-29.91805-8.160952-48.965712-27.208614-57.126664-57.126664-12.437291-66.707621-9.727855-131.995237 8.160952-195.862846z" fill="${getColor(1, color, colors, '#323232')}" />
          </svg>''';
        break;
      case IconNames.saasTable:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M288.019692 128l0.157539 63.960615h-63.409231l-5.592615 0.157539a96.019692 96.019692 0 0 0-90.978462 89.521231l-0.196923 6.380307v511.212308l0.118154 5.592615a96.019692 96.019692 0 0 0 89.521231 90.978462l6.380307 0.196923h575.212308l5.592615-0.118154a96.019692 96.019692 0 0 0 90.978462-89.521231l0.196923-6.380307V288.768l-0.118154-5.592615a96.019692 96.019692 0 0 0-89.521231-90.978462l-6.380307-0.196923H768l-0.157538-64h32.17723c82.747077 0 151.236923 62.936615 159.271385 144.856615l0.590769 7.955693 0.157539 7.207384v512c0 82.707692-62.936615 151.236923-144.856616 159.232l-7.955692 0.59077-7.207385 0.157538H224.019692c-82.707692 0-151.236923-62.936615-159.232-144.856615l-0.590769-7.955693-0.157538-7.207384v-512c0-82.707692 62.936615-151.236923 144.856615-159.232l7.955692-0.59077 7.207385-0.157538h64z m512 560.009846a31.980308 31.980308 0 0 1 3.702154 63.803077l-3.741538 0.196923H224.019692a31.980308 31.980308 0 0 1-3.702154-63.803077l3.741539-0.196923h576zM457.649231 364.110769l2.796307 2.481231c11.618462 11.618462 12.445538 29.932308 2.481231 42.456615l-2.481231 2.796308-135.758769 135.758769a32.019692 32.019692 0 0 1-42.456615 2.520616l-2.796308-2.520616-67.899077-67.859692a31.980308 31.980308 0 0 1 42.496-47.734154l2.756923 2.481231 45.213539 45.252923 113.191384-113.152a32.019692 32.019692 0 0 1 42.456616-2.481231z m342.370461 67.899077a31.980308 31.980308 0 0 1 3.702154 63.803077l-3.741538 0.196923h-223.980308a31.980308 31.980308 0 0 1-3.741538-63.803077l3.741538-0.196923h224.019692zM367.931077 73.137231c16.423385 0 29.932308 12.366769 31.822769 28.278154l0.196923 3.741538v109.686154a31.980308 31.980308 0 0 1-63.803077 3.741538l-0.196923-3.741538V105.156923c0-17.723077 14.336-32.019692 32.019693-32.019692z m320.039385 0c16.384 0 29.932308 12.366769 31.783384 28.278154l0.196923 3.741538v109.686154a31.980308 31.980308 0 0 1-63.803077 3.741538l-0.196923-3.741538V105.156923c0-17.723077 14.336-32.019692 32.019693-32.019692zM608.019692 128l0.157539 63.960615h-160.216616L447.803077 128h160.216615z" fill="${getColor(0, color, colors, '#333333')}" />
          </svg>''';
        break;
      case IconNames.saasDuizhangdan:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M768 96.017067a128 128 0 0 1 128 128v576a128 128 0 0 1-128 128h-512a128 128 0 0 1-128-128V224.017067a128 128 0 0 1 128-128z m-512 63.965866l-4.778667 0.2048a64.2048 64.2048 0 0 0-10.001066 1.536l2.389333-0.512a63.5904 63.5904 0 0 0-3.652267 0.8192l1.262934-0.341333a63.488 63.488 0 0 0-12.014934 4.164267l2.730667-1.160534a63.658667 63.658667 0 0 0-4.369067 1.9456l1.6384-0.785066a63.761067 63.761067 0 0 0-3.7888 1.911466l2.1504-1.1264a63.863467 63.863467 0 0 0-4.676266 2.594134l2.525866-1.467734c-1.262933 0.682667-2.525867 1.4336-3.754666 2.184534l1.2288-0.7168a64.1024 64.1024 0 0 0-3.8912 2.525866l2.696533-1.809066a64.170667 64.170667 0 0 0-3.959467 2.730666l1.262934-0.9216a64.273067 64.273067 0 0 0-3.857067 2.9696l2.594133-2.048c-1.365333 1.024-2.730667 2.116267-3.9936 3.2768l1.399467-1.2288a64.375467 64.375467 0 0 0-3.7888 3.413334l2.389333-2.184534c-1.160533 0.989867-2.2528 2.048-3.310933 3.106134l0.9216-0.9216a64.4096 64.4096 0 0 0-3.345067 3.515733l2.389334-2.594133c-1.1264 1.160533-2.218667 2.389333-3.2768 3.618133l0.887466-1.024a64.3072 64.3072 0 0 0-3.2768 4.027733l2.389334-3.003733c-1.024 1.160533-1.9456 2.389333-2.833067 3.618133l0.443733-0.6144a64.170667 64.170667 0 0 0-2.730666 3.8912l2.286933-3.2768c-1.024 1.365333-1.9456 2.7648-2.833067 4.1984l0.580267-0.9216c-0.853333 1.365333-1.672533 2.730667-2.389333 4.096l1.809066-3.1744c-0.887467 1.467733-1.706667 2.935467-2.491733 4.437334l0.682667-1.2288a63.761067 63.761067 0 0 0-1.774934 3.4816l1.092267-2.218667a63.5904 63.5904 0 0 0-6.212267 19.114667l0.477867-2.594134c-0.8192 4.061867-1.2288 8.226133-1.2288 12.526934v576c0 35.328 28.672 64 64 64V160.017067z m512 0H320v704H768c33.655467 0 61.269333-25.941333 63.829333-58.9824l0.170667-4.983466V224.017067c0-35.362133-28.672-64-64-64zM691.712 329.386667c11.537067 11.537067 12.424533 29.696 2.6624 42.222933l-2.6624 3.003733-41.403733 41.403734h53.691733a31.982933 31.982933 0 0 1 3.754667 63.761066l-3.754667 0.238934h-96.017067v63.965866h96.017067a31.982933 31.982933 0 0 1 3.754667 63.829334l-3.754667 0.2048h-96.017067v95.982933a31.982933 31.982933 0 0 1-63.761066 3.754667l-0.238934-3.754667v-96.017067h-95.982933a31.982933 31.982933 0 0 1-3.754667-63.761066l3.754667-0.238934h96.017067v-63.965866h-96.017067a31.982933 31.982933 0 0 1-3.754667-63.829334l3.754667-0.170666h53.316267l-41.3696-41.403734a32.017067 32.017067 0 0 1 42.461866-47.7184l2.7648 2.491734 67.925334 67.857066c0.955733 1.024 1.877333 2.048 2.730666 3.140267l2.730667-3.140267 67.8912-67.857066 2.798933-2.491734a32.017067 32.017067 0 0 1 42.461867 2.491734z" fill="${getColor(0, color, colors, '#333333')}" />
          </svg>''';
        break;
      case IconNames.saasZhaoren:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M653.824 580.565333c144.981333 0 264.192 115.925333 273.578667 263.808l0.426666 9.301334 0.170667 8.917333c0 69.12-52.650667 125.525333-118.912 129.237333l-6.912 0.170667H252.8c-63.829333 0-116.864-49.408-124.032-114.56l-0.597333-7.04L128 863.658667c0-153.301333 118.442667-278.101333 266.325333-282.922667l8.874667-0.170667h250.624z m74.197333-240c0 113.621333-89.6 205.738667-200.021333 205.738667-107.946667 0-195.925333-87.978667-199.850667-198.016l-0.128-7.68h400zM528 32c17.152 0 33.877333 1.92 50.005333 5.546667v97.28c0 9.514667 7.466667 17.152 16.64 17.152a16.810667 16.810667 0 0 0 16.426667-14.08l0.256-3.029334V47.744c77.738667 30.592 135.509333 102.698667 147.626667 189.952h2.389333c18.389333 0 33.322667 15.36 33.322667 34.304 0 18.944-14.933333 34.304-33.322667 34.304H294.656a33.834667 33.834667 0 0 1-33.322667-34.304c0-18.944 14.933333-34.304 33.322667-34.304h2.389333c12.117333-87.253333 69.888-159.36 147.626667-189.909333v87.04c0 9.514667 7.466667 17.152 16.64 17.152a16.810667 16.810667 0 0 0 16.426667-14.08l0.256-3.029334V37.546667c16.085333-3.626667 32.853333-5.546667 50.005333-5.546667z" fill="${getColor(0, color, colors, '#333333')}" />
          </svg>''';
        break;
      case IconNames.saasQuannianshouzhi:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M823.46666667 160c61.696 0 112.042667 49.408 115.072 111.488l0.128 5.845333v88.021334h-316.8l-6.229333 0.128c-74.538667 3.242667-134.4 64.128-137.642667 139.946666L477.86666667 512l0.128 6.4c3.2 75.861333 62.976 136.789333 137.472 140.117333l6.4 0.170667H938.66666667V746.666667c0 62.848-48.512 114.133333-109.44 117.205333l-5.76 0.128H189.86666667c-61.696 0-112.042667-49.408-115.072-111.488L74.66666667 746.666667v-469.333334c0-62.848 48.512-114.133333 109.44-117.205333l5.76-0.128H823.46666667z m115.2 256v192h-315.605333c-55.466667 0-100.394667-42.965333-100.394667-95.957333 0-52.992 44.928-95.957333 100.394667-96L938.66666667 416z m-320 32a64 64 0 1 0 0 128 64 64 0 0 0 0-128z" fill="${getColor(0, color, colors, '#333333')}" />
          </svg>''';
        break;
      case IconNames.saasTabOutstandingActive:
        svgXml = '''
          <svg viewBox="0 0 1063 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M755.712 64a128 128 0 0 1 127.842462 121.619692l0.118153 6.380308 0.039385 355.997538A239.182769 239.182769 0 0 0 787.692308 527.990154a239.261538 239.261538 0 0 0-169.708308 70.301538 239.261538 239.261538 0 0 0-70.301538 169.708308 239.261538 239.261538 0 0 0 96.098461 192.078769l-400.108308-0.078769a128 128 0 0 1-127.842461-121.619692l-0.118154-6.380308V192a128 128 0 0 1 121.580308-127.842462l6.380307-0.157538h512z m31.980308 512a192 192 0 1 1 0 384 192 192 0 0 1 0-384z m0 64a32.019692 32.019692 0 0 0-31.783385 28.278154l-0.236308 3.741538V768l0.275693 4.096c0.708923 5.435077 2.796308 10.633846 6.06523 14.966154l2.678154 3.150769 68.411077 70.892308 2.993231 2.717538c11.342769 9.137231 27.569231 9.452308 39.187692 0.669539l3.072-2.599385 2.756923-2.953846c9.097846-11.342769 9.412923-27.569231 0.669539-39.227077l-2.638769-3.072-59.47077-61.558154v-83.101538l-0.196923-3.702154A32.019692 32.019692 0 0 0 787.692308 640zM615.384615 233.353846a32.019692 32.019692 0 0 0-42.456615-2.481231l-2.756923 2.481231-67.938462 67.899077-2.717538 3.150769a32.295385 32.295385 0 0 0-2.756923-3.150769L428.898462 233.353846l-2.796308-2.481231A32.019692 32.019692 0 0 0 383.606154 278.646154l41.393231 41.353846H371.672615l-3.702153 0.196923a32.019692 32.019692 0 0 0 3.741538 63.803077h95.980308v64H371.672615l-3.702153 0.196923A32.019692 32.019692 0 0 0 371.712 512h95.980308v96.019692l0.196923 3.702154a32.019692 32.019692 0 0 0 63.803077-3.741538V512h96.019692l3.702154-0.196923a32.019692 32.019692 0 0 0-3.741539-63.803077H531.692308V384h96.019692l3.702154-0.196923a32.019692 32.019692 0 0 0-3.741539-63.803077h-53.68123l41.39323-41.353846 2.678154-3.032616a32.019692 32.019692 0 0 0-2.678154-42.259692z" fill="${getColor(0, color, colors, '#333333')}" />
          </svg>''';
        break;
      case IconNames.saasTabOutstanding:
        svgXml = '''
          <svg viewBox="0 0 1063 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M787.692308 544.019692a224.019692 224.019692 0 1 1 0 448 224.019692 224.019692 0 0 1 0-448z m-32.019693-480.019692a128 128 0 0 1 128 128l0.039385 338.628923a254.306462 254.306462 0 0 0-64-16.659692V192c0-33.752615-26.112-61.400615-59.234462-63.803077l-4.804923-0.196923h-512c-33.713231 0-61.361231 26.112-63.803077 59.234462l-0.157538 4.765538v640c0 33.752615 26.072615 61.400615 59.195077 63.803077l4.765538 0.196923h322.284308a257.181538 257.181538 0 0 0 52.420923 64.039385l-374.705231-0.039385a128 128 0 0 1-128-128V192a128 128 0 0 1 128-128h512zM787.692308 591.990154a176.009846 176.009846 0 1 0 0 352.019692 176.009846 176.009846 0 0 0 0-352.019692z m0 48.009846c15.044923 0 27.648 10.397538 31.113846 24.379077l0.669538 3.899077 0.236308 3.702154v83.101538l59.470769 61.558154 2.599385 3.072a32.019692 32.019692 0 0 1 2.008615 35.288615l-2.717538 3.938462-2.717539 2.953846-3.072 2.599385a32.019692 32.019692 0 0 1-35.24923 2.048l-3.938462-2.717539-2.993231-2.717538-68.411077-70.892308-2.678154-3.150769a32.019692 32.019692 0 0 1-5.277538-10.948923l-0.787692-4.017231-0.236308-4.096v-96.019692l0.196923-3.702154A32.019692 32.019692 0 0 1 787.692308 640zM615.384615 233.353846a32.019692 32.019692 0 0 1 2.678154 42.259692l-2.678154 3.032616-41.353846 41.353846h53.681231a31.980308 31.980308 0 0 1 3.702154 63.803077l-3.741539 0.196923H531.692308v64h96.019692a31.980308 31.980308 0 0 1 3.702154 63.803077l-3.741539 0.196923H531.692308v96.019692a31.980308 31.980308 0 0 1-63.803077 3.702154l-0.196923-3.741538V512H371.672615a31.980308 31.980308 0 0 1-3.702153-63.803077l3.741538-0.196923h95.980308V384H371.672615a31.980308 31.980308 0 0 1-3.702153-63.803077l3.741538-0.196923h53.287385l-41.353847-41.353846a31.980308 31.980308 0 0 1 42.456616-47.773539l2.756923 2.481231 67.899077 67.899077a32.295385 32.295385 0 0 1 2.756923 3.150769l2.756923-3.150769 67.859692-67.899077 2.756923-2.481231a32.019692 32.019692 0 0 1 42.535385 2.481231z" fill="${getColor(0, color, colors, '#333333')}" />
          </svg>''';
        break;
      case IconNames.saasChange1:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M754.688 265.5232c45.943467 0 90.043733 18.5344 122.538667 51.541333a177.493333 177.493333 0 0 1 50.7904 124.5184v105.642667h-69.358934v-105.642667c0-58.368-46.557867-105.642667-104.004266-105.642666H130.730667c-15.086933 0-28.433067-9.898667-33.006934-24.507734a35.498667 35.498667 0 0 1 12.868267-39.355733l242.5856-176.093867 40.2432 57.344-154.624 112.196267h515.8912zM269.312 758.442667a171.997867 171.997867 0 0 1-122.538667-51.541334 177.493333 177.493333 0 0 1-50.756266-124.5184v-105.642666h69.290666v105.642666c0 58.368 46.592 105.642667 104.0384 105.642667h623.957334c15.086933 0 28.433067 9.898667 33.006933 24.507733a35.498667 35.498667 0 0 1-12.868267 39.355734l-242.688 176.093866-40.209066-57.344 154.624-112.196266H269.312h0.068267z" fill="${getColor(0, color, colors, '#333333')}" />
          </svg>''';
        break;
      case IconNames.saasTabStatisticsActive:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M800.019692 96.019692a128 128 0 0 1 128 128v576a128 128 0 0 1-128 128H224.019692a128 128 0 0 1-128-128V224.019692a128 128 0 0 1 128-128zM335.950769 431.931077a31.980308 31.980308 0 0 0-31.980307 32.019692v224.019693l0.196923 3.741538a32.019692 32.019692 0 0 0 63.803077-3.741538V463.950769l-0.236308-3.741538a32.019692 32.019692 0 0 0-31.783385-28.238769z m384 64.039385a31.980308 31.980308 0 0 0-31.980307 31.980307v160.019693l0.196923 3.741538a32.019692 32.019692 0 0 0 63.803077-3.741538v-160.019693l-0.236308-3.741538a32.019692 32.019692 0 0 0-31.783385-28.238769z m-192-192a31.980308 31.980308 0 0 0-31.980307 31.980307v352.019693l0.196923 3.741538a32.019692 32.019692 0 0 0 63.803077-3.741538V335.950769l-0.236308-3.741538a32.019692 32.019692 0 0 0-31.783385-28.238769z" fill="${getColor(0, color, colors, '#333333')}" />
          </svg>''';
        break;
      case IconNames.saasTabCalendarActive:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M927.829333 281.6a128 128 0 0 0-127.829333-121.6h-48V112l-0.213333-3.712a32 32 0 0 0-63.786667 3.712V160h-320V112l-0.213333-3.712a32 32 0 0 0-63.786667 3.712V160H224l-6.4 0.170667A128 128 0 0 0 96 288v512l0.170667 6.4a128 128 0 0 0 127.829333 121.6h576l6.4-0.170667a128 128 0 0 0 121.6-127.829333v-512l-0.170667-6.4z m-205.013333 195.968l-2.517333 2.773333-226.261334 226.304a32.042667 32.042667 0 0 1-42.453333 2.474667l-2.773333-2.474667-113.152-113.152a32 32 0 0 1 42.453333-47.786666l2.773333 2.517333 90.496 90.496 203.648-203.605333a32 32 0 0 1 42.453334-2.517334l2.773333 2.517334a31.957333 31.957333 0 0 1 2.56 42.453333z" fill="${getColor(0, color, colors, '#333333')}" />
          </svg>''';
        break;
      case IconNames.saasPlus:
        svgXml = '''
          <svg viewBox="0 0 1080 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M544.36345629 93.2815963c18.64135111 0 34.04635705 14.04574037 36.18234481 32.16927631l0.25890816 4.27197553v364.08888889h364.02416185a36.37652594 36.37652594 0 0 1 4.27197668 72.55887076l-4.27197668 0.25890702h-364.02416185v364.08888889a36.37652594 36.37652594 0 0 1-72.62359665 4.20724963l-0.25890816-4.20724963V566.62951481H143.96276963a36.44125184 36.44125184 0 0 1-4.27197667-72.55886962l4.27197667-0.25890816h364.02416186V129.72284814c0-20.06534371 16.31118222-36.44125184 36.44125184-36.44125184z" fill="${getColor(0, color, colors, '#333333')}" />
          </svg>''';
        break;
      case IconNames.saasTabWaterActive:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M768 64H256a128 128 0 0 0-128 128v640a128 128 0 0 0 128 128h512a128 128 0 0 0 128-128v-640a128 128 0 0 0-128-128z m-64 208a32 32 0 0 1 3.712 63.786667l-3.712 0.213333h-384a32 32 0 0 1-3.712-63.786667l3.712-0.213333h384z m-128 192a32 32 0 0 1 3.712 63.786667l-3.712 0.213333h-256a32 32 0 0 1-3.712-63.786667l3.712-0.213333h256z" fill="${getColor(0, color, colors, '#333333')}" />
          </svg>''';
        break;
      case IconNames.saasTabProjectsActive:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M947.2 717.917091a32.023273 32.023273 0 0 1-10.146909 41.285818l-3.165091 1.954909-355.141818 187.997091c-42.123636 22.295273-92.113455 23.552-135.121455 3.723636l-7.493818-3.723636-355.095273-187.997091a32.023273 32.023273 0 0 1 26.530909-58.135273l3.397819 1.582546 355.095272 187.950545a88.436364 88.436364 0 0 0 77.172364 2.746182l5.585455-2.746182 355.141818-187.950545a32.023273 32.023273 0 0 1 43.240727 13.312z m0-210.850909a32.023273 32.023273 0 0 1-10.146909 41.332363l-3.165091 1.95491-355.141818 187.950545c-42.123636 22.341818-92.113455 23.552-135.121455 3.723636l-7.493818-3.723636-355.095273-187.950545a32.023273 32.023273 0 0 1 26.530909-58.181819l3.397819 1.582546 355.095272 187.997091a88.436364 88.436364 0 0 0 77.172364 2.746182l5.585455-2.746182 355.141818-187.997091a32.023273 32.023273 0 0 1 43.240727 13.312z m-379.857455-428.218182l345.274182 182.830545a64 64 0 0 1 0 113.105455l-345.274182 182.784a128 128 0 0 1-119.761454 0L102.306909 374.784a64 64 0 0 1 0-113.105455l345.274182-182.784a128 128 0 0 1 119.761454 0z" fill="${getColor(0, color, colors, '#333333')}" />
          </svg>''';
        break;
      case IconNames.saasTabWater:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M768 64H256a128 128 0 0 0-128 128v640a128 128 0 0 0 128 128h512a128 128 0 0 0 128-128v-640a128 128 0 0 0-128-128zM256 128h512a64 64 0 0 1 64 64v640A64 64 0 0 1 768 896H256a64 64 0 0 1-64-64v-640A64 64 0 0 1 256 128z m448 144a32 32 0 0 1 3.712 63.786667l-3.712 0.213333h-384a32 32 0 0 1-3.712-63.786667l3.712-0.213333h384z m-128 192a32 32 0 0 1 3.712 63.786667l-3.712 0.213333h-256a32 32 0 0 1-3.712-63.786667l3.712-0.213333h256z" fill="${getColor(0, color, colors, '#333333')}" />
          </svg>''';
        break;
      case IconNames.saasTabCalendar:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M720 80a32 32 0 0 1 31.786667 28.288l0.213333 3.712V160h48a128 128 0 0 1 127.829333 121.6l0.170667 6.4v512a128 128 0 0 1-121.6 127.829333l-6.4 0.170667H224a128 128 0 0 1-127.829333-121.6l-0.170667-6.4v-512A128 128 0 0 1 217.6 160.170667l6.4-0.170667h80V112a32 32 0 0 1 63.786667-3.712l0.213333 3.712V160h320V112a32 32 0 0 1 32-32zM304 224H224a64 64 0 0 0-63.829333 59.221333l-0.170667 4.778667v512a64 64 0 0 0 59.221333 63.829333l4.778667 0.170667h576a64 64 0 0 0 63.829333-59.221333l0.170667-4.778667v-512a64 64 0 0 0-59.221333-63.829333l-4.778667-0.170667h-48v80a32 32 0 0 1-63.786667 3.712l-0.213333-3.712V224h-320v80a32 32 0 0 1-63.786667 3.712l-0.213333-3.712V224z m413.525333 208.64l2.816 2.474667a32 32 0 0 1 2.474667 42.453333l-2.474667 2.816-226.261333 226.261333a32 32 0 0 1-42.496 2.474667l-2.773333-2.474667-113.152-113.152a32 32 0 0 1 42.453333-47.744l2.816 2.517334 90.496 90.453333 203.648-203.605333a32 32 0 0 1 42.453333-2.474667z" fill="${getColor(0, color, colors, '#333333')}" />
          </svg>''';
        break;
      case IconNames.saasTabStatistics:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M800 96H224a128 128 0 0 0-128 128v576a128 128 0 0 0 128 128h576a128 128 0 0 0 128-128V224a128 128 0 0 0-128-128z m-576 64h576a64 64 0 0 1 64 64v576a64 64 0 0 1-64 64H224a64 64 0 0 1-64-64V224a64 64 0 0 1 64-64z m112 272a32 32 0 0 1 31.786667 28.288l0.213333 3.712v224a32 32 0 0 1-63.786667 3.712l-0.213333-3.712v-224a32 32 0 0 1 32-32z m384 64a32 32 0 0 1 31.786667 28.288l0.213333 3.712v160a32 32 0 0 1-63.786667 3.712l-0.213333-3.712v-160a32 32 0 0 1 32-32z m-192-192a32 32 0 0 1 31.786667 28.288l0.213333 3.712v352a32 32 0 0 1-63.786667 3.712l-0.213333-3.712v-352a32 32 0 0 1 32-32z" fill="${getColor(0, color, colors, '#333333')}" />
          </svg>''';
        break;
      case IconNames.saasTabProjects:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M947.2 717.917091a32.023273 32.023273 0 0 1-10.146909 41.285818l-3.165091 1.954909-355.141818 187.997091c-42.123636 22.295273-92.113455 23.552-135.121455 3.723636l-7.493818-3.723636-355.095273-187.997091a32.023273 32.023273 0 0 1 26.530909-58.135273l3.397819 1.582546 355.095272 187.950545a88.436364 88.436364 0 0 0 77.172364 2.746182l5.585455-2.746182 355.141818-187.950545a32.023273 32.023273 0 0 1 43.240727 13.312z m0-210.850909a32.023273 32.023273 0 0 1-10.146909 41.332363l-3.165091 1.95491-355.141818 187.950545c-42.123636 22.341818-92.113455 23.552-135.121455 3.723636l-7.493818-3.723636-355.095273-187.950545a32.023273 32.023273 0 0 1 26.530909-58.181819l3.397819 1.582546 355.095272 187.997091a88.436364 88.436364 0 0 0 77.172364 2.746182l5.585455-2.746182 355.141818-187.997091a32.023273 32.023273 0 0 1 43.240727 13.312z m-379.857455-428.218182l345.274182 182.830545a64 64 0 0 1 0 113.105455l-345.274182 182.784a128 128 0 0 1-119.761454 0L102.306909 374.784a64 64 0 0 1 0-113.105455l345.274182-182.784a128 128 0 0 1 119.761454 0z m-84.619636 54.132364l-5.213091 2.466909L132.189091 318.231273l345.274182 182.784c17.035636 9.029818 37.096727 9.821091 54.690909 2.466909l5.213091-2.466909 345.274182-182.784-345.274182-182.784a64 64 0 0 0-54.690909-2.466909z" fill="${getColor(0, color, colors, '#333333')}" />
          </svg>''';
        break;
      case IconNames.saasProjectIcon:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M928.75481514 709.16180074a30.64373596 30.64373596 0 0 1-9.68341952 39.59170772l-3.0643746 1.77733601-340.0841868 180.06259509a145.98676066 145.98676066 0 0 1-129.37785575 3.61596043l-7.23192084-3.61596043-340.02290004-180.06259509a30.64373596 30.64373596 0 0 1 25.43430149-55.64902514l3.24823657 1.53218729 340.02289834 180.00130667c22.98280281 12.19620662 50.25572704 13.11551967 73.91269253 2.63536228l5.3932981-2.63536228 340.02290003-180.00130667a30.64373596 30.64373596 0 0 1 41.43033049 12.74779412z m0-201.88093614a30.64373596 30.64373596 0 0 1-9.68341952 39.5917077l-3.0643746 1.89991118-340.0841868 180.00130836a145.80289872 145.80289872 0 0 1-129.37785575 3.55467364l-7.17063408-3.55467364L99.29015759 548.77248348a30.76631115 30.76631115 0 0 1 25.43430149-55.71031187l3.24823657 1.47089889 340.02289834 180.00130832c22.98280281 12.19620662 50.25572704 13.05423124 73.91269253 2.6353606l5.3932981-2.6353606 340.02290003-180.00130832a30.64373596 30.64373596 0 0 1 41.43033049 12.7477941z m-363.7411506-410.07448178l330.64591594 175.03702255a61.2874736 61.2874736 0 0 1 0 108.35625252l-330.64591594 175.09831098a122.94267108 122.94267108 0 0 1-114.73015047 0l-330.64591594-175.09831098a61.2874736 61.2874736 0 0 1 0-108.35625252l330.64591594-175.03702255a122.57494553 122.57494553 0 0 1 114.73015047 0z m-81.08332662 51.84920277l-4.96428578 2.39021191-330.64591598 175.03702256 330.64591598 175.03702255a61.2874736 61.2874736 0 0 0 52.33950181 2.3289235l5.02557254-2.3289235 330.58462921-175.03702255-330.58462921-175.03702256a61.16489841 61.16489841 0 0 0-52.40078857-2.39021191z" fill="${getColor(0, color, colors, '#333333')}" />
          </svg>''';
        break;
      case IconNames.saasNextSolid:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M764.16 512l-465.472 426.688V85.312z" fill="${getColor(0, color, colors, '#333333')}" />
          </svg>''';
        break;
      case IconNames.saasPrevSolid:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M259.84 512l465.472 426.688V85.312z" fill="${getColor(0, color, colors, '#333333')}" />
          </svg>''';
        break;
      case IconNames.saasProject:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M864.017067 246.8864H496.810667a31.982933 31.982933 0 0 1-26.350934-13.858133l-40.7552-59.1872a32.017067 32.017067 0 0 0-26.350933-13.858134H160.017067A96.017067 96.017067 0 0 0 64 256v566.852267a95.982933 95.982933 0 0 0 96.017067 96.017066h704a96.017067 96.017067 0 0 0 96.017066-96.017066V342.869333a96.0512 96.0512 0 0 0-96.017066-95.982933z m34.235733 198.826667H125.713067a29.730133 29.730133 0 0 1 0-59.460267h772.539733a29.730133 29.730133 0 0 1 0 59.460267z" fill="${getColor(0, color, colors, '#333333')}" />
          </svg>''';
        break;
      case IconNames.saasTriangle:
        svgXml = '''
          <svg viewBox="0 0 1707 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M168.52334052 34.08455L852.07191458 32 1539.19656833 38.0679575C1580.96543052 38.0679575 1617.68265646 40.32821656 1629.88246302 49.668755 1659.33770145 72.21942875 1656.11902958 118.27919 1629.88246302 144.39995281L902.59029458 940.77177875C888.72724021 954.69473469 870.22386927 960.64688187 852.01600677 959.75236344 833.80415052 960.64887875 815.35868333 954.69473469 801.43572739 940.77177875L74.1455577 144.40195156C47.96888614 118.28118594 45.11161583 72.64272969 74.1455577 49.73264937 87.65319833 39.08028125 111.16228489 34.08455 168.52334052 34.08455L168.52334052 34.08455Z" fill="${getColor(0, color, colors, '#333333')}" />
          </svg>''';
        break;
      case IconNames.saasReset:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M884.693333 623.914667l0.981334 3.456 41.514666 179.84a30.762667 30.762667 0 0 1-58.922666 17.28l-1.024-3.413334-22.4-97.28a413.738667 413.738667 0 0 1-209.749334 178.176C420.949333 984.149333 180.736 877.226667 98.56 663.04c-15.232-39.68 42.197333-61.738667 57.429333-22.058667a353.749333 353.749333 0 0 0 643.413334 37.845334l-116.608 26.88a30.762667 30.762667 0 0 1-35.882667-19.584l-1.024-3.413334a30.762667 30.762667 0 0 1 19.626667-35.925333l3.413333-0.981333 179.882667-41.514667c15.36-3.541333 30.72 5.12 35.84 19.626667z m14.72-205.952c9.6 41.429333-50.346667 55.296-59.946666 13.824a353.749333 353.749333 0 0 0-631.893334-126.976l119.253334-10.453334a30.762667 30.762667 0 0 1 32.810666 24.405334l0.512 3.584a30.762667 30.762667 0 0 1-24.405333 32.810666l-3.584 0.512-183.893333 16.085334a30.762667 30.762667 0 0 1-32.768-24.405334l-0.512-3.584-16.085334-183.850666a30.762667 30.762667 0 0 1 60.757334-8.96l0.512 3.584 8.704 99.413333a413.738667 413.738667 0 0 1 232.490666-147.2c223.488-51.626667 446.464 87.722667 498.048 311.210667z" fill="${getColor(0, color, colors, '#333333')}" />
          </svg>''';
        break;
      case IconNames.saasChange:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M912.57059555 583.36142222a36.40888889 36.40888889 0 0 1 4.22343112 72.52650667l-4.22343112 0.29127111H199.53891555l194.13219556 194.13219555a36.40888889 36.40888889 0 0 1 2.98552889 48.05973334l-2.98552889 3.49525333a36.40888889 36.40888889 0 0 1-48.05973333 2.98552889l-3.42243556-2.98552889-256.31857777-256.31857777a36.40888889 36.40888889 0 0 1 21.55406222-61.9679289l4.15061333-0.21845333h800.99555555z m-234.10915555-465.66968889l3.42243555 2.98552889 256.31857778 256.31857778a36.40888889 36.40888889 0 0 1-21.55406222 61.96792889L912.49777778 439.18222222h-800.99555556a36.40888889 36.40888889 0 0 1-4.22343111-72.59932444L111.50222222 366.36444445h713.10449778L630.40170667 172.15943111a36.40888889 36.40888889 0 0 1-2.98552889-48.05973333l2.98552889-3.42243556a36.40888889 36.40888889 0 0 1 48.05973333-2.98552889z" fill="${getColor(0, color, colors, '#333333')}" />
          </svg>''';
        break;
      case IconNames.saasInput:
        svgXml = '''
          <svg viewBox="0 0 1075 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M889.6 871.3728a32 32 0 0 1 3.7376 63.7952l-3.7376 0.2048H185.6a32 32 0 0 1-3.7376-63.7952l3.7376-0.2048h704zM553.6768 109.6704a32 32 0 0 1 41.1136-9.984l3.4304 2.0992 209.7152 146.8416c13.312 9.3696 17.408 27.0848 9.9328 41.1136l-2.048 3.4304-307.712 439.8592a32 32 0 0 1-11.6224 10.1376l-3.584 1.536-210.176 77.056a32 32 0 0 1-42.8544-26.4704l-0.1536-3.6352 0.512-223.8976c0-5.2224 1.28-10.3424 3.6864-14.9504l2.048-3.328 307.712-439.808z m34.048 62.8736l-283.5456 405.4528-0.3584 167.8848 157.6448-57.8048 283.5456-405.4016L587.776 172.544z" fill="${getColor(0, color, colors, '#323232')}" />
          </svg>''';
        break;
      case IconNames.saasWarning:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M973.344 761.312L593.76 144.512a96 96 0 0 0-163.488 0L50.656 761.312a96 96 0 0 0 81.76 146.304h759.2a96 96 0 0 0 81.728-146.304zM480 320l0.224-3.744a32 32 0 0 1 63.552 0L544 320v256l-0.224 3.744a32 32 0 0 1-63.552 0L480 576v-256z m32 448a48 48 0 1 1 0-96 48 48 0 0 1 0 96z" fill="${getColor(0, color, colors, '#333333')}" />
          </svg>''';
        break;
      case IconNames.saasChart:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M320.085333 160l0.128 60.8 0.853334 7.04a79.914667 79.914667 0 0 0 78.933333 66.986667 79.786667 79.786667 0 0 0 80-80l-0.128-54.869334h96.213333l0.128 60.842667 0.853334 7.04a79.914667 79.914667 0 0 0 78.933333 66.986667 79.786667 79.786667 0 0 0 80-80l-0.128-54.869334 64.128 0.042667a128 128 0 0 1 127.829333 121.6l0.170667 6.4v512a128 128 0 0 1-121.6 127.829333l-6.4 0.170667H224a128 128 0 0 1-127.829333-121.6l-0.170667-6.4v-512A128 128 0 0 1 217.6 160.170667l6.4-0.170667h96.085333zM800 687.957333H224l-3.712 0.213334a32 32 0 0 0 3.712 63.786666h576l3.712-0.213333a32 32 0 0 0-3.712-63.786667z m-342.357333-323.882666a32 32 0 0 0-42.453334 2.474666l-113.194666 113.152-45.226667-45.226666-2.773333-2.56a32 32 0 0 0-42.453334 47.786666l67.882667 67.84 2.816 2.56a32 32 0 0 0 42.453333-2.56l135.765334-135.722666 2.474666-2.816a32 32 0 0 0-2.474666-42.453334z m342.357333 67.882666H576l-3.712 0.213334a32 32 0 0 0 3.712 63.786666h224l3.712-0.213333a32 32 0 0 0-3.712-63.786667z m-400-358.826666a32 32 0 0 1 31.786667 28.245333l0.213333 3.712v109.738667a32 32 0 0 1-63.786667 3.712l-0.213333-3.712V105.130667a32 32 0 0 1 32-32z m256 0a32 32 0 0 1 31.786667 28.245333l0.213333 3.712v109.738667a32 32 0 0 1-63.786667 3.712l-0.213333-3.712V105.130667a32 32 0 0 1 32-32z" fill="${getColor(0, color, colors, '#333333')}" />
          </svg>''';
        break;
      case IconNames.saasStatistics:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M800 96a128 128 0 0 1 128 128v576a128 128 0 0 1-128 128H224a128 128 0 0 1-128-128V224a128 128 0 0 1 128-128zM336 432a32 32 0 0 0-32 32v224l0.213333 3.712a32 32 0 0 0 63.786667-3.712v-224l-0.213333-3.712a32 32 0 0 0-31.786667-28.288z m384 64a32 32 0 0 0-32 32v160l0.213333 3.712a32 32 0 0 0 63.786667-3.712v-160l-0.213333-3.712a32 32 0 0 0-31.786667-28.288z m-192-192a32 32 0 0 0-32 32v352l0.213333 3.712a32 32 0 0 0 63.786667-3.712v-352l-0.213333-3.712a32 32 0 0 0-31.786667-28.288z" fill="${getColor(0, color, colors, '#333333')}" />
          </svg>''';
        break;
      case IconNames.saasEyeOpen:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M512 128c170.666667 0 341.333333 128 512 384-170.666667 256-341.333333 384-512 384S170.666667 768 0 512c170.666667-256 341.333333-384 512-384z m0 64.034133c-133.9392 0-275.797333 98.781867-422.912 303.650134l-11.605333 16.315733 2.389333 3.413333c147.524267 209.373867 289.723733 312.5248 424.072533 316.484267l8.055467 0.136533c133.9392 0 275.797333-98.850133 422.912-303.786666l11.605333-16.247467-2.389333-3.413333C796.672 299.281067 654.404267 196.130133 520.055467 192.170667L512 191.965867z m0 127.931734a191.965867 191.965867 0 1 1 0 384 191.965867 191.965867 0 0 1 0-383.931734z m0 64.034133a128 128 0 1 0 0 256 128 128 0 0 0 0-256z" fill="${getColor(0, color, colors, '#333333')}" />
          </svg>''';
        break;
      case IconNames.saasShaixuan:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M855.342766 151.46262c-6.056949-11.851932-18.984377-19.393699-33.219589-19.393699L101.903901 132.068921c-14.360056 0-27.412326 7.704472-33.396621 19.744693-5.988388 12.015661-3.845585 26.157753 5.520737 36.192294l255.896134 274.308483 0 309.339324c0 12.847609 7.895831 24.602328 20.389376 30.328749l189.116195 86.432535c5.154393 2.371 10.771321 3.515057 16.33913 3.515057 6.541997 0 13.090133-1.607614 18.926048-4.797259 10.718109-5.945409 17.427928-16.503882 17.809621-28.037567l12.957103-396.767536 245.078765-274.90507C859.543438 177.316451 861.425298 163.313529 855.342766 151.46262zM520.773827 804.275693l-117.384477-53.647851L403.38935 483.628836l127.858016 0L520.773827 804.275693zM550.774095 416.986019c-1.963725-0.299829-3.761674-1.090844-5.809309-1.090844L383.519814 415.895175 181.938726 199.803605l562.427506 0L550.774095 416.986019zM685.454494 524.008498l273.392624 0 0 59.759035-273.392624 0 0-59.759035ZM685.454494 654.104485l273.392624 0 0 59.759035-273.392624 0 0-59.759035ZM685.454494 773.618463l273.392624 0 0 59.759035-273.392624 0 0-59.759035Z" fill="${getColor(0, color, colors, '#333333')}" />
          </svg>''';
        break;
      case IconNames.saasCheckboxGou:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M968.00963514 183.6981321l5.72728256 5.10248809a65.08275641 65.08275641 0 0 1 4.99835566 86.32576807l-4.99835566 5.72728256-552.31830383 552.21417142a65.18688881 65.18688881 0 0 1-86.42990047 5.10248809l-5.62315016-5.10248809-276.15915191-276.05501951a65.08275641 65.08275641 0 0 1 86.42990049-97.05140631l5.62315015 4.9983557 230.02849418 230.1326266 506.18764609-506.29177853a65.39515361 65.39515361 0 0 1 86.5340329-5.10248809z" fill="${getColor(0, color, colors, '#333333')}" />
          </svg>''';
        break;
      case IconNames.saasInfo:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M512 54.272q95.232 0 178.688 36.352t145.408 98.304 98.304 145.92 36.352 179.2-36.352 178.688-98.304 145.408-145.408 98.304-178.688 36.352-179.2-36.352-145.92-98.304-98.304-145.408-36.352-178.688 36.352-179.2 98.304-145.92 145.92-98.304 179.2-36.352zM567.296 450.56q0-24.576-15.872-41.472t-39.424-16.896-39.936 16.896-16.384 41.472l0 343.04q0 24.576 16.384 39.424t39.936 14.848 39.936-15.36 16.384-39.936zM512 323.584q29.696 0 50.688-20.992t20.992-50.688-20.992-50.688-50.688-20.992-51.2 20.992-21.504 50.688 21.504 50.688 51.2 20.992z" fill="${getColor(0, color, colors, '#333333')}" />
          </svg>''';
        break;
      case IconNames.saasTabbarJoinActive:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M733.909333 85.333333l8.32 0.256c74.069333 4.565333 131.584 70.101333 131.584 148.906667v129.621333l-0.170666 84.266667a153.685333 153.685333 0 0 0-37.504-16.085333 175.189333 175.189333 0 0 0-246.186667 107.178666l-2.304 8.618667a175.232 175.232 0 0 0 41.685333 159.872l4.864 4.949333-5.376 3.328a240.554667 240.554667 0 0 0-107.349333 160.768 63.701333 63.701333 0 0 0 24.448 61.696L225.237333 938.666667l-8.277333-0.298667C142.848 933.802667 85.333333 868.266667 85.333333 789.546667V234.453333l0.256-8.704C89.770667 148.053333 149.973333 85.333333 225.237333 85.333333h508.672z m24.064 362.666667a141.056 141.056 0 0 1 76.544 259.498667 12.032 12.032 0 0 0-5.205333 11.008c0.426667 4.352 3.2 8.149333 7.253333 9.813333a206.293333 206.293333 0 0 1 124.416 154.666667 29.44 29.44 0 0 1-29.184 34.346666h-347.52a29.44 29.44 0 0 1-13.184-3.029333l3.242667 1.28a27.989333 27.989333 0 0 1-12.672-8.618667l2.944 2.986667a29.269333 29.269333 0 0 1-9.514667-26.965333 206.378667 206.378667 0 0 1 124.373334-154.794667 11.776 11.776 0 0 0 1.962666-20.693333 141.056 141.056 0 0 1-58.709333-158.378667l0.298667-1.066667c0.768-2.474667 1.578667-4.949333 2.474666-7.381333l-2.773333 8.448a141.738667 141.738667 0 0 1 39.850667-63.957333 141.568 141.568 0 0 1 26.666666-19.285334 140.714667 140.714667 0 0 1 68.736-17.877333z m-282.581333 133.418667H281.216l-4.778667 0.298666a38.186667 38.186667 0 0 0 0.170667 75.776l4.608 0.256h194.176l4.778667-0.341333a38.186667 38.186667 0 0 0-4.778667-75.989333z m194.261333-250.709334H281.216l-4.778667 0.298667a38.186667 38.186667 0 0 0 0.170667 75.776l4.608 0.256h388.437333l4.778667-0.298667a38.186667 38.186667 0 0 0-4.778667-76.032z" fill="${getColor(0, color, colors, '#333333')}" />
          </svg>''';
        break;
      case IconNames.saasTabbarProfileActive:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M641.024 565.546667a237.824 237.824 0 1 0-258.133333 0 19.84 19.84 0 0 1-3.328 34.901333A347.989333 347.989333 0 0 0 169.813333 861.44a49.664 49.664 0 0 0 49.237334 57.941333h586.026666a49.664 49.664 0 0 0 49.152-57.941333 347.904 347.904 0 0 0-209.749333-260.778667 19.84 19.84 0 0 1-3.2-35.029333l-0.256-0.085333z" fill="${getColor(0, color, colors, '#333333')}" />
          </svg>''';
        break;
      case IconNames.saasTabbarWorker:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M632.789333 576a263.296 263.296 0 0 1 262.656 246.229333l0.426667 8.661334 0.128 8.32a120.789333 120.789333 0 0 1-114.176 120.618666l-6.613333 0.170667H247.808a119.893333 119.893333 0 0 1-119.04-106.922667l-0.597333-6.570666L128 840.192a264.192 264.192 0 0 1 255.658667-264.064l8.533333-0.128h240.597333z m0 64H393.216l-7.509333 0.085333c-105.344 3.370667-189.781333 87.978667-193.578667 191.146667l-0.170667 7.210667 0.128 4.352 0.256 3.072c3.072 27.050667 24.32 47.573333 50.474667 49.92l4.992 0.213333h525.653333l4.906667-0.085333a56.746667 56.746667 0 0 0 53.461333-51.114667l0.170667-4.565333-0.042667-6.186667-0.384-7.68A199.296 199.296 0 0 0 632.789333 640zM704 352a192 192 0 0 1-184.789333 191.872L512 544a192 192 0 0 1-191.872-184.789333L320 352h384z m-81.152 64H401.109333l2.986667 4.949333a128 128 0 0 0 101.461333 58.88l6.442667 0.170667a127.872 127.872 0 0 0 106.88-57.514667l3.968-6.528zM512 64c16.469333 0 32.554667 1.792 48 5.12l32 9.6a224.256 224.256 0 0 1 141.738667 177.237333L736 256a32 32 0 1 1 0 64H288a32 32 0 0 1 0-64h2.261333a224.256 224.256 0 0 1 141.696-177.28l32-9.557333c12.373333-2.688 25.173333-4.394667 38.186667-4.949334L512 64zM512 128c-8.789333 0-17.450667 0.682667-25.941333 2.090667l-6.912 1.28-26.197334 7.850666-5.034666 2.133334a160.426667 160.426667 0 0 0-91.434667 108.928L355.2 256h313.6l-1.28-5.717333a160.426667 160.426667 0 0 0-91.434667-108.970667l-5.333333-2.176-26.112-7.765333-6.741333-1.28a160.682667 160.682667 0 0 0-17.152-1.877334L512 128z" fill="${getColor(0, color, colors, '#333333')}" />
          </svg>''';
        break;
      case IconNames.saasReduce:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M512 48.48484867c-257.070545 0-465.454545 208.384-465.454545 465.454545 0 257.070545 208.384 465.454545 465.454545 465.454545 257.070545 0 465.454545-208.384 465.454545-465.454545C977.454545 256.86884867 769.070545 48.48484867 512 48.48484867zM512 932.84848467c-231.377455 0-418.909091-187.531636-418.909091-418.909091 0-231.377455 187.531636-418.909091 418.909091-418.909091 231.377455 0 418.909091 187.531636 418.909091 418.909091C930.909091 745.31684867 743.377455 932.84848467 512 932.84848467zM791.272727 490.66666667L232.727273 490.66666667c-12.846545 0-23.272727 10.426182-23.272727 23.272727C209.454545 526.78593967 219.880727 537.21212167 232.727273 537.21212167l558.545455 0c12.846545 0 23.272727-10.426182 23.272727-23.272727C814.545455 501.09284867 804.119273 490.66666667 791.272727 490.66666667z" fill="${getColor(0, color, colors, '#333333')}" />
          </svg>''';
        break;
      case IconNames.saasQrcode:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M914.2784 556.6464v212.992h-212.992v-141.4656h-70.9632v286.1056h-71.0144V557.568h212.992v141.3632h71.0144v-142.2336h70.9632z m-449.5872 0.8704v353.4848H109.7216V557.568h354.9696z m449.5872 282.7776v70.7072h-212.992v-70.656h212.992zM393.728 628.224H180.736v212.0704h212.992V628.224z m-70.9632 70.656v70.7072H251.6992v-70.656h71.0144zM464.6912 109.7216v353.4848H109.7216V109.7216h354.9696z m449.5872 0v353.4848h-354.9696V109.7216h354.9696zM393.728 180.3776H180.736v212.1216h212.992V180.4288z m449.6384 0h-212.992v212.1216h212.992V180.4288zM322.7136 251.1872v70.656H251.6992v-70.656h71.0144z m449.5872 0v70.656h-71.0144v-70.656h71.0144z" fill="${getColor(0, color, colors, '#333333')}" />
          </svg>''';
        break;
      case IconNames.saasCamera1:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M601.813333 160a96 96 0 0 1 86.186667 53.674667l20.778667 42.325333H832a128 128 0 0 1 127.829333 121.6L960 384v384a128 128 0 0 1-128 128h-640a128 128 0 0 1-128-128V384a128 128 0 0 1 128-128h123.221333l20.778667-42.325333a96 96 0 0 1 79.786667-53.461334l6.4-0.213333z m0 64h-179.626666a32 32 0 0 0-28.714667 17.92l-29.568 60.16a32 32 0 0 1-28.714667 17.92H192A64 64 0 0 0 128 384v384a64 64 0 0 0 64 64h640A64 64 0 0 0 896 768V384a64 64 0 0 0-64-64h-143.146667a32 32 0 0 1-28.757333-17.92l-29.568-60.16a32 32 0 0 0-28.714667-17.92zM512 384a192 192 0 1 1 0 384 192 192 0 0 1 0-384z m0 64a128 128 0 1 0 0 256 128 128 0 0 0 0-256z" fill="${getColor(0, color, colors, '#333333')}" />
          </svg>''';
        break;
      case IconNames.saasClean:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M512 35.45047893a476.54952107 476.54952107 0 1 0 0 953.03479147 476.54952107 476.54952107 0 0 0 0-953.03479147z m193.13844693 669.687968a34.37427413 34.37427413 0 0 1-45.2969408 2.63429014l-3.01979626-2.63429014L512 560.31673707l-144.82170987 144.82170986a34.2457728 34.2457728 0 0 1-48.31673706 0 34.37427413 34.37427413 0 0 1-2.63429014-45.2969408l2.63429014-3.01979626 144.82170986-144.88596054-144.82170986-144.7574592a34.18152107 34.18152107 0 0 1 45.2969408-50.88677653l3.01979626 2.63429013L512 463.68326293l144.82170987-144.82170986a34.1172704 34.1172704 0 0 1 50.9510272 45.2969408l-2.63429014 2.9555456-144.82170986 144.82170986 144.82170986 144.88596054a34.2457728 34.2457728 0 0 1 0 48.31673706z" fill="${getColor(0, color, colors, '#333333')}" />
          </svg>''';
        break;
      case IconNames.saasDelete:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M851.81629629 272.21333334H196.45629629a36.40888889 36.40888889 0 0 0-36.40888889 36.40888888v517.37031112l0.14563555 7.79150222C164.48929184 926.84515556 244.66166517 1000.39111112 342.09185184 1000.39111112h364.0888889l8.08277333-0.14563556c96.41073778-4.07779555 173.96167111-80.09955555 173.9616711-174.25294222V308.62222222l-0.2912711-4.2234311A36.40888889 36.40888889 0 0 0 851.81629629 272.21333334z m-381.12824889 525.67153778a36.40888889 36.40888889 0 0 1-72.52650666 4.29624888l-0.29127112-4.29624888v-327.68a36.40888889 36.40888889 0 0 1 72.52650667-4.2962489l0.29127111 4.2962489v327.68z m180.87936 0a36.40888889 36.40888889 0 0 1-72.52650666 4.29624888l-0.29127112-4.29624888v-327.68a36.40888889 36.40888889 0 0 1 72.52650667-4.2962489l0.29127111 4.2962489v327.68zM961.04296295 126.57777778H706.18074074a37.57397333 37.57397333 0 0 0-1.82044445-11.50520889l-36.40888889-72.81777777-1.60199111-3.93216A36.55452445 36.55452445 0 0 0 633.36296295 17.35111112H414.90962962l-4.22343111 0.21845333a36.26325333 36.26325333 0 0 0-30.29219556 24.68522667l-36.40888888 72.81777777-1.16508445 4.07779556A36.70016 36.70016 0 0 0 342.09185184 126.57777778H87.22962962l-4.22343111 0.21845334A36.40888889 36.40888889 0 0 0 87.22962962 199.39555556h873.81333333l4.29624889-0.21845334A36.40888889 36.40888889 0 0 0 961.04296295 126.57777778z" fill="${getColor(0, color, colors, '#333333')}" />
          </svg>''';
        break;
      case IconNames.saasSaoma:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M960 768c16.420571 0 29.952 12.361143 31.817143 28.269714l0.182857 3.730286v91.428571a100.571429 100.571429 0 0 1-94.866286 100.425143l-5.705143 0.146286h-91.428571a32 32 0 0 1-3.730286-63.780571l3.730286-0.219429h91.428571a36.571429 36.571429 0 0 0 36.352-32.585143l0.219429-3.986286v-91.428571c0-17.664 14.336-32 32-32z m-896 0c16.420571 0 29.952 12.361143 31.817143 28.269714l0.182857 3.730286v91.428571l0.219429 3.986286a36.571429 36.571429 0 0 0 32.365714 32.365714l3.986286 0.219429h91.428571l3.730286 0.219429a32 32 0 0 1 0 63.561142l-3.730286 0.219429h-91.428571l-5.705143-0.146286a100.571429 100.571429 0 0 1-94.72-94.72l-0.146286-5.705143v-91.428571l0.219429-3.730286A32 32 0 0 1 64 768zM800 192c17.664 0 32 14.336 32 32v576a32 32 0 0 1-32 32H224a32 32 0 0 1-32-32V224c0-17.664 14.336-32 32-32h576z m-96 304.018286H320l-3.730286 0.182857-3.620571 0.658286a32 32 0 0 0 3.620571 62.902857l3.730286 0.256h384l3.730286-0.219429 3.620571-0.658286a32 32 0 0 0-7.350857-63.122285zM224 31.963429a32 32 0 0 1 3.730286 63.780571l-3.730286 0.219429h-91.428571a36.571429 36.571429 0 0 0-36.352 32.585142l-0.219429 3.986286v91.428572a32 32 0 0 1-63.780571 3.730285l-0.219429-3.730285v-91.428572A100.571429 100.571429 0 0 1 126.866286 32.146286l5.705143-0.146286h91.428571z m667.428571 0l5.705143 0.146285a100.571429 100.571429 0 0 1 94.72 94.72l0.146286 5.705143v91.428572l-0.219429 3.730285a32 32 0 0 1-63.561142 0l-0.219429-3.730285v-91.428572l-0.219429-3.986286a36.571429 36.571429 0 0 0-32.365714-32.365714l-3.986286-0.219428h-91.428571l-3.730286-0.219429a32 32 0 0 1 0-63.561143l3.730286-0.219428h91.428571z" fill="${getColor(0, color, colors, '#333333')}" />
          </svg>''';
        break;
      case IconNames.saasPersonnel:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M364.7488 540.2624c-60.928-45.056-101.4784-109.5168-101.4784-186.8288 0-122.368 108.2368-225.4336 236.7488-225.4336s236.6976 103.0656 236.6976 225.4336c0 77.312-40.5504 141.7216-101.4272 186.8288 135.2704 51.5072 236.6976 180.3776 236.6976 328.4992 0 19.3536-13.5168 32.256-33.792 32.256-20.3264 0-33.792-12.9024-33.792-32.256 0-161.024-135.3216-289.8432-304.384-289.8432-169.1136 0-304.384 128.8192-304.384 289.8432 0 19.3536-13.5168 32.256-33.792 32.256-20.3264 0-33.8432-12.9024-33.8432-32.256 0-148.1216 101.4784-276.992 236.7488-328.4992z m135.2704-25.7536c94.72 0 169.0624-70.8608 169.0624-161.0752 0-90.1632-74.3936-161.024-169.0624-161.024-94.72 0-169.1136 70.8608-169.1136 161.024 0 90.2144 74.3936 161.0752 169.1136 161.0752z" fill="${getColor(0, color, colors, '#333333')}" fill-opacity=".85" />
          </svg>''';
        break;
      case IconNames.saasBackTop:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M532.8384 231.68l2.816 2.816 320 352a32 32 0 0 1-44.4416 45.824l-2.8672-2.816-264.3456-290.816V896a32 32 0 0 1-63.7952 3.7376l-0.2048-3.7376V338.7392l-264.3456 290.816a32 32 0 0 1-42.0352 4.608l-3.1232-2.5088a32 32 0 0 1-4.7104-42.0352l2.56-3.1232 320-352a32 32 0 0 1 44.4928-2.816z m315.136-151.7056a32 32 0 0 1 3.7376 63.7952l-3.7376 0.2048h-640a32 32 0 0 1-3.6864-63.744l3.6864-0.256h640z" fill="${getColor(0, color, colors, '#323233')}" />
          </svg>''';
        break;
      case IconNames.saasXiangpian:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M514.616889 619.861333l-207.530667-227.555555-141.084444 269.129955v74.160356h700.871111C777.614222 656.156444 589.368889 486.343111 589.368889 486.343111z m243.677867-171.3152c38.934756 0 70.473956-32.494933 70.473955-72.635733s-31.5392-72.658489-70.473955-72.658489-70.394311 32.551822-70.394312 72.658489 31.550578 72.635733 70.394312 72.635733zM56.456533 128.341333v766.407111h910.108445V128.341333z m864.415289 717.687467H105.2672V180.144356h815.604622z m0 0" fill="${getColor(0, color, colors, '#333333')}" />
          </svg>''';
        break;
      case IconNames.saasIconRili:
        svgXml = '''
          <svg viewBox="0 0 1068 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M556.477217 898.80487H217.533217a92.91687 92.91687 0 0 1-73.015652-30.096696 48.172522 48.172522 0 0 1-10.863304-33.035131V263.746783c3.294609-35.08313 33.569391-62.241391 70.477913-63.22087h70.477913c10.863304 0 16.294957-2.982957 16.294956-11.976348v-24.175304c3.383652-22.617043 24.976696-38.600348 48.795827-36.107131 22.394435 0.356174 40.425739 17.630609 40.648347 39.045566v2.982956c2.715826 33.079652 2.715826 33.079652 37.977044 33.079652h295.535304c13.534609 0 18.966261-3.027478 18.966261-15.048347v-21.014261c3.650783-20.880696 22.572522-36.196174 44.744348-36.196174 22.171826 0 41.093565 15.315478 44.744348 36.196174 0 12.02087-5.431652 27.113739 2.671304 33.03513 13.133913 4.096 27.069217 5.164522 40.69287 3.027478h40.737391c38.733913 0.222609 70.745043 29.117217 73.060174 66.114783v566.138435c0 39.045565-35.261217 66.114783-84.057044 69.275826-108.454957-6.099478-225.101913-6.099478-338.944-6.099478z m2.715826-514.671305H220.249043c-13.534609 0-19.010783 2.982957-19.010782 15.048348v415.387826c0 18.075826 8.147478 21.058783 24.397913 21.058783h666.980174c24.397913 0 24.397913 0 24.397913-21.014261V402.253913c0-15.048348-2.671304-18.075826-19.010783-18.075826h-338.810435v-0.044522z" fill="${getColor(0, color, colors, '#333333')}" />
            <path d="M347.358609 732.91687h-43.141566a27.514435 27.514435 0 0 1-20.168347-5.654261 24.709565 24.709565 0 0 1-9.483131-17.808696v-49.864348a22.349913 22.349913 0 0 1 7.746783-17.541565 24.976696 24.976696 0 0 1 19.055304-5.965913h91.981913c18.877217 0 26.802087 8.815304 26.802087 26.445913v46.970435c0 14.514087-8.102957 23.462957-26.802087 23.462956H347.358609v-0.044521z m210.276174 0h-43.186087c-18.788174 0-29.606957-8.815304-29.606957-23.462957V662.483478c0-17.630609 8.102957-23.462957 26.802087-26.445913h88.954435a25.021217 25.021217 0 0 1 19.055304 5.921392c5.12 4.496696 7.969391 10.907826 7.746783 17.541565v49.864348a22.305391 22.305391 0 0 1-7.746783 17.541565 24.976696 24.976696 0 0 1-19.055304 5.921391h-43.141565l0.178087 0.089044z m210.231652-96.879305h45.990956c16.161391 0 26.802087 8.815304 24.264348 23.507478V712.347826a22.305391 22.305391 0 0 1-7.746782 17.541565 24.976696 24.976696 0 0 1-19.055305 5.921392h-91.714782a25.021217 25.021217 0 0 1-19.010783-5.921392 22.349913 22.349913 0 0 1-7.791304-17.541565v-49.864348a22.305391 22.305391 0 0 1 7.791304-17.541565 24.976696 24.976696 0 0 1 19.055304-5.921391c15.716174-3.917913 32.055652-4.897391 48.217044-3.027479z m-210.231652-70.522435h-45.990957c-18.877217 0-26.802087-5.87687-26.802087-23.462956v-52.847304c0-14.514087 8.102957-20.524522 24.264348-20.524522h94.386087a26.490435 26.490435 0 0 1 18.565565 6.41113c5.030957 4.452174 8.013913 10.551652 8.236522 17.051826v49.864348c0 14.558609-8.147478 23.507478-26.846609 23.507478h-45.946435 0.133566z m-210.276174-96.834782h45.990956c16.161391 0 26.802087 8.770783 26.802087 20.524522v52.847304c0 14.558609-8.102957 20.524522-24.264348 20.524522H301.412174c-16.161391 0-24.264348-5.87687-24.264348-20.48v-55.785739c0-14.558609 10.774261-20.524522 26.846609-20.524522 14.202435 2.493217 28.672 3.472696 43.141565 2.893913h0.26713z m417.613913 96.834782H719.026087a25.021217 25.021217 0 0 1-19.055304-5.921391 22.349913 22.349913 0 0 1-7.746783-17.541565v-52.847304c0-14.514087 8.102957-20.524522 24.219826-20.524522h94.564174c6.188522-0.845913 12.466087 0.979478 17.14087 4.897391a19.678609 19.678609 0 0 1 7.123478 15.627131v52.847304a19.72313 19.72313 0 0 1-7.123478 15.582609 22.082783 22.082783 0 0 1-17.14087 4.941913c-15.181913 2.537739-30.586435 3.517217-45.990957 2.938434z" fill="${getColor(1, color, colors, '#333333')}" />
          </svg>''';
        break;
      case IconNames.saasGou1:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M967.79204836 173.65947733a37.09628871 37.09628871 0 0 0-54.75896889 1.81753174l-516.83145955 585.80445866-282.2766592-296.16446578a37.09628871 37.09628871 0 0 0-54.85217565 0.46603378 43.10812445 43.10812445 0 0 0 0.46603378 58.25422222l310.56490951 325.80421405 0.37282702 0.27962027c0.13981013 0.13981013 0.18641351 0.27962027 0.27962027 0.46603378 2.46997902 2.42337565 5.40599182 3.82147698 8.24879787 5.49919857 1.39810133 0.8388608 2.56318578 2.14375538 4.05449386 2.74959929a36.02441102 36.02441102 0 0 0 29.08050774-0.32622364c1.58451485-0.69905067 2.84280605-2.14375538 4.33411413-3.07582294 2.88940942-1.81753173 5.91862898-3.30883982 8.388608-6.01183573 0.13981013-0.09320675 0.13981013-0.32622365 0.27962027-0.46603378l0.32622364-0.27962027L969.46976996 231.91369955a43.06152107 43.06152107 0 0 0-1.6777216-58.25422222z" fill="${getColor(0, color, colors, '#333333')}" />
          </svg>''';
        break;
      case IconNames.saasDel:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M800.028444 298.66666667c16.384 0 29.923556 12.344889 31.744 28.273778l0.227556 3.754666v454.712889c0 82.716444-68.152889 149.560889-152.917333 153.088l-7.054223 0.170667H351.914667c-85.560889 0-156.046222-64.568889-159.857778-146.432l-0.113778-6.826667V330.69511067a32.028444 32.028444 0 0 1 63.772445-3.754666l0.227555 3.754666v454.712889c0 47.160889 39.594667 86.357333 90.339556 89.088l5.688889 0.170667h320c51.484444 0 92.899556-37.546667 95.800888-84.081778l0.170667-5.176889V330.69511067c0-17.692444 14.336-32.028444 32.028445-32.028444z m-368.071111 112.014222c16.497778 0 29.980444 12.344889 31.857778 28.273778l0.170667 3.697778v288.028444a31.971556 31.971556 0 0 1-63.772445 3.697778l-0.227555-3.697778V442.65244467c0-17.635556 14.336-31.971556 32.028444-31.971556z m160.028445 0c16.440889 0 29.923556 12.344889 31.800889 28.273778l0.227555 3.697778v288.028444a31.971556 31.971556 0 0 1-63.772444 3.697778l-0.227556-3.697778V442.65244467c0-17.635556 14.336-31.971556 31.971556-31.971556z m15.985778-336.042666c12.515556 0 23.779556 7.338667 29.013333 18.432l1.365333 3.470222 32.028445 96.028444c1.137778 3.356444 1.649778 6.769778 1.649777 10.126222h223.971556a31.971556 31.971556 0 0 1 3.754667 63.715556l-3.754667 0.284444H128a32.028444 32.028444 0 0 1-3.754667-63.829333l3.754667-0.170667h223.971556a31.857778 31.857778 0 0 1 0.682666-6.542222l0.967111-3.584 32.028445-96.028444a32.028444 32.028444 0 0 1 26.624-21.617778l3.754666-0.227556h192z m-23.04 64H439.068444l-21.390222 64.056888h188.586667l-21.333333-64.056888z" fill="${getColor(0, color, colors, '#606066')}" />
          </svg>''';
        break;
      case IconNames.saasXuanzhong:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M512 0.346901c-282.578844 0-511.653099 229.076302-511.653099 511.653099 0 282.578844 229.074256 511.653099 511.653099 511.653099 282.576797 0 511.653099-229.074256 511.653099-511.653099C1023.653099 229.42218 794.576797 0.346901 512 0.346901zM792.560884 367.299387 448.857914 711.002356c-7.506974 7.506974-17.411555 11.205203-27.306926 11.159154-9.894348 0.045025-19.800975-3.65218-27.307949-11.159154L231.43707 548.195364c-14.923898-14.923898-14.923898-39.3451 0-54.267974 14.923898-14.923898 39.3451-14.923898 54.268998 0l135.843898 135.843898 316.741921-316.741921c14.923898-14.923898 39.3451-14.923898 54.268998 0S807.485805 352.375489 792.560884 367.299387z" fill="${getColor(0, color, colors, '#333333')}" />
          </svg>''';
        break;
      case IconNames.saasWeixin1:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M337.387283 341.82659c-17.757225 0-35.514451 11.83815-35.514451 29.595375s17.757225 29.595376 35.514451 29.595376 29.595376-11.83815 29.595376-29.595376c0-18.49711-11.83815-29.595376-29.595376-29.595375zM577.849711 513.479769c-11.83815 0-22.936416 12.578035-22.936416 23.6763 0 12.578035 11.83815 23.676301 22.936416 23.676301 17.757225 0 29.595376-11.83815 29.595376-23.676301s-11.83815-23.676301-29.595376-23.6763zM501.641618 401.017341c17.757225 0 29.595376-12.578035 29.595376-29.595376 0-17.757225-11.83815-29.595376-29.595376-29.595375s-35.514451 11.83815-35.51445 29.595375 17.757225 29.595376 35.51445 29.595376zM706.589595 513.479769c-11.83815 0-22.936416 12.578035-22.936416 23.6763 0 12.578035 11.83815 23.676301 22.936416 23.676301 17.757225 0 29.595376-11.83815 29.595376-23.676301s-11.83815-23.676301-29.595376-23.6763z" fill="${getColor(0, color, colors, '#28C445')}" />
            <path d="M510.520231 2.959538C228.624277 2.959538 0 231.583815 0 513.479769s228.624277 510.520231 510.520231 510.520231 510.520231-228.624277 510.520231-510.520231-228.624277-510.520231-510.520231-510.520231zM413.595376 644.439306c-29.595376 0-53.271676-5.919075-81.387284-12.578034l-81.387283 41.433526 22.936416-71.768786c-58.450867-41.433526-93.965318-95.445087-93.965317-159.815029 0-113.202312 105.803468-201.988439 233.803468-201.98844 114.682081 0 216.046243 71.028902 236.023121 166.473989-7.398844-0.739884-14.797688-1.479769-22.196532-1.479769-110.982659 1.479769-198.289017 85.086705-198.289017 188.67052 0 17.017341 2.959538 33.294798 7.398844 49.572255-7.398844 0.739884-15.537572 1.479769-22.936416 1.479768z m346.265896 82.867052l17.757225 59.190752-63.630058-35.514451c-22.936416 5.919075-46.612717 11.83815-70.289017 11.83815-111.722543 0-199.768786-76.947977-199.768786-172.393063-0.739884-94.705202 87.306358-171.653179 198.289017-171.65318 105.803468 0 199.028902 77.687861 199.028902 172.393064 0 53.271676-34.774566 100.624277-81.387283 136.138728z" fill="${getColor(1, color, colors, '#28C445')}" />
          </svg>''';
        break;
      case IconNames.saasShuaxin:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M960 416V192l-73.056 73.056a447.712 447.712 0 0 0-373.6-201.088C265.92 63.968 65.312 264.544 65.312 512S265.92 960.032 513.344 960.032a448.064 448.064 0 0 0 415.232-279.488 38.368 38.368 0 1 0-71.136-28.896 371.36 371.36 0 0 1-344.096 231.584C308.32 883.232 142.112 717.024 142.112 512S308.32 140.768 513.344 140.768c132.448 0 251.936 70.08 318.016 179.84L736 416h224z" fill="${getColor(0, color, colors, '#333333')}" />
          </svg>''';
        break;
      case IconNames.saasNormalFee:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M902.9632 231.2192a28.416 28.416 0 0 0-12.288 1.8944l-37.376 15.3088V149.1968c0-51.0464-40.96-92.3136-90.5216-92.3136h-36.4032c-29.5936 0-55.0912 19.456-65.0752 47.7696C638.464 166.2976 580.2496 210.3296 512 210.3296s-126.5152-44.032-148.8384-106.1376c-10.4448-27.8016-35.9424-47.3088-65.536-47.3088h-36.352c-50.1248 0-90.624 41.728-90.624 92.3136v99.2256l-37.3248-15.36a28.416 28.416 0 0 0-12.288-1.8432c-35.9424 3.6864-64.1536 33.3824-64.1536 69.12V898.048c0 37.9904 31.8464 69.0688 71.0144 69.0688h768.2048c39.168 0 71.0144-31.0784 71.0144-69.12V300.3904c0-35.7376-28.2112-65.4336-64.1536-69.12z m-281.7536 474.88c13.2096 0 21.8624 8.8064 21.8624 22.272 0 13.4144-8.6528 22.2208-21.8624 22.2208h-87.3472v86.7328c0 13.4656-8.704 22.272-21.8624 22.272-13.2096 0-21.8624-8.8064-21.8624-22.272V750.592H402.3296c-13.2096 0-21.8624-8.8064-21.8624-22.272 0-13.4144 8.6528-22.2208 21.8624-22.2208h87.3472v-53.76H400.4864c-13.2096 0-21.8112-8.8576-21.8112-22.272s8.6016-22.3232 21.8112-22.3232h65.536L393.728 534.528a21.8624 21.8624 0 0 1 0-31.0272 20.9408 20.9408 0 0 1 30.464 0l87.3984 88.9856 87.3984-88.9856a20.9408 20.9408 0 0 1 30.464 0 21.8624 21.8624 0 0 1 0 31.0272l-72.3456 73.728h65.536c13.2096 0 21.8624 8.8064 21.8624 22.3232 0 13.4144-8.704 22.1696-21.8624 22.1696h-89.6512v53.3504h88.2688zM798.72 270.6944L548.4032 372.736c-23.1936 9.728-49.6128 9.728-72.8064 0L225.28 270.6432V149.1456c0-19.968 16.384-36.608 35.9424-36.608h36.4032c5.9392 0 11.4176 4.1472 14.1312 11.1104 30.464 85.3504 111.0528 142.848 200.2432 142.848s169.7792-57.4976 200.2432-142.848c2.304-6.4512 7.7312-11.1104 14.1312-11.1104h36.352c19.6096 0 35.9936 16.6912 35.9936 36.6592V270.6944z" fill="${getColor(0, color, colors, '#333333')}" />
          </svg>''';
        break;
      case IconNames.saasMoney:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M797.752889 364.373333a418.759111 418.759111 0 0 0-72.533333-62.464c-5.006222-2.503111-7.509333-5.006222-12.515556-7.509333 35.043556-17.521778 60.017778-50.062222 60.017778-87.495111 0-60.017778-57.457778-105.016889-127.488-105.016889-15.018667 0-29.980444 2.503111-42.496 7.509333-7.509333 2.503111-10.012444 5.006222-15.018667 7.509334-2.503111 0-7.509333 0-10.012444-2.503111a117.475556 117.475556 0 0 0-24.974223-27.534223A118.499556 118.499556 0 0 0 485.205333 56.888889c-24.974222 2.503111-47.445333 12.515556-67.470222 29.980444-10.012444 10.012444-20.024889 17.521778-25.031111 27.534223 0-2.503111-4.949333 0-7.452444-2.503112a40.675556 40.675556 0 0 0-15.018667-7.509333 116.792889 116.792889 0 0 0-42.496-7.509333c-67.527111 0-127.488 44.999111-127.488 105.016889 0 37.489778 25.031111 69.973333 59.960889 87.495111-2.446222 2.503111-7.452444 5.006222-12.515556 7.509333-24.974222 17.464889-49.948444 39.992889-72.476444 62.464-67.470222 69.973333-109.966222 150.016-109.966222 237.511111 0 245.020444 190.008889 362.496 417.507555 362.496h9.955556c227.555556 0 417.507556-117.475556 417.507555-362.496-2.503111-84.992-44.999111-164.977778-112.469333-232.504889z m-187.505778 252.529778c14.961778 0 25.031111 9.955556 25.031111 24.974222s-10.069333 25.031111-25.031111 25.031111H510.236444v97.450667c0 15.018667-10.012444 25.031111-25.031111 25.031111-14.961778 0-24.974222-10.012444-24.974222-25.031111v-97.507555H360.220444c-14.961778 0-25.031111-9.955556-25.031111-24.974223s10.069333-25.031111 25.031111-25.031111H460.231111V556.942222H357.717333c-14.961778 0-25.031111-10.012444-25.031111-25.031111 0-14.961778 10.069333-24.974222 25.031111-24.974222h75.036445l-82.488889-82.488889a24.177778 24.177778 0 0 1 0-34.986667 24.177778 24.177778 0 0 1 34.986667 0l99.953777 99.953778 100.010667-99.953778a24.177778 24.177778 0 0 1 34.986667 0 24.177778 24.177778 0 0 1 0 34.986667l-82.488889 82.488889h75.036444c14.961778 0 25.031111 10.012444 25.031111 25.031111 0 14.961778-10.069333 24.974222-25.031111 24.974222H510.236444v60.017778h100.010667z" fill="${getColor(0, color, colors, '#333333')}" />
          </svg>''';
        break;
      case IconNames.saasSalary:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M882.972444 56.888889H197.973333C151.665778 56.888889 113.777778 93.297778 113.777778 137.671111v748.657778c0 44.373333 37.888 80.782222 84.138666 80.782222H882.915556c46.250667 0 84.138667-36.408889 84.138666-80.782222V137.671111c0.113778-44.373333-37.774222-80.782222-84.138666-80.782222zM437.361778 422.286222a32.426667 32.426667 0 0 1-32.995556-31.687111 32.426667 32.426667 0 0 1 32.995556-31.687111h62.008889L429.112889 291.555556a30.890667 30.890667 0 0 1 0-44.771556 34.133333 34.133333 0 0 1 46.648889 0L540.444444 308.792889l64.568889-62.008889a34.133333 34.133333 0 0 1 46.648889 0 30.890667 30.890667 0 0 1 0 44.771556L581.404444 358.968889h62.179556c18.204444 0 32.995556 14.222222 32.995556 31.687111a32.426667 32.426667 0 0 1-32.995556 31.687111h-70.144v28.216889h70.144c18.204444 0 32.995556 14.222222 32.995556 31.687111a32.426667 32.426667 0 0 1-32.995556 31.687111h-70.144v46.933334a32.426667 32.426667 0 0 1-32.995556 31.687111 32.426667 32.426667 0 0 1-32.995555-31.687111v-46.933334H437.361778a32.426667 32.426667 0 0 1-32.995556-31.687111 32.426667 32.426667 0 0 1 32.995556-31.687111H507.448889v-28.216889H437.361778z m258.56 364.145778H385.137778a32.426667 32.426667 0 0 1-32.995556-31.687111 32.426667 32.426667 0 0 1 32.995556-31.687111h310.840889c18.204444 0 32.995556 14.222222 32.995555 31.687111a32.426667 32.426667 0 0 1-32.995555 31.687111z m0-113.493333H385.137778a32.426667 32.426667 0 0 1-32.995556-31.687111 32.426667 32.426667 0 0 1 32.995556-31.687112h310.840889c18.204444 0 32.995556 14.222222 32.995555 31.687112 0 17.521778-14.791111 31.687111-32.995555 31.687111z" fill="${getColor(0, color, colors, '#333333')}" />
          </svg>''';
        break;
      case IconNames.saasBonus:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M697.002667 440.433778a267.946667 267.946667 0 0 0-267.776 267.719111 268.117333 268.117333 0 0 0 268.117333 267.946667 268.174222 268.174222 0 0 0 267.548444-267.548445 267.946667 267.946667 0 0 0-267.946666-268.117333z m37.262222 251.448889c14.165333 0 28.273778 0.170667 42.382222-0.113778 4.551111-0.056889 5.802667 1.479111 5.688889 5.802667-0.341333 8.078222-0.113778 16.213333-0.113778 24.974222H711.111111v39.708444h70.656v30.549334h-70.826667c-0.113778 2.56-0.284444 4.608-0.284444 6.656 0 15.758222-0.113778 31.516444 0.056889 47.217777 0.056889 4.437333-1.308444 5.859556-5.688889 5.688889a403.456 403.456 0 0 0-27.022222 0c-4.380444 0.170667-5.802667-1.137778-5.688889-5.632 0.227556-15.701333 0.056889-31.459556 0.056889-47.217778v-6.371555h-74.126222v-30.606222h73.784888v-39.822223H597.902222c0-9.784889-0.170667-18.887111 0.227556-28.046222 0.056889-0.967111 3.242667-2.616889 5.006222-2.673778 16.952889-0.170667 33.905778-0.113778 50.858667-0.113777h6.883555l-77.255111-132.835556c2.787556-0.170667 4.551111-0.398222 6.428445-0.398222 9.898667 0 19.797333 0.284444 29.639111-0.113778 4.892444-0.170667 7.566222 1.365333 9.841777 5.745778 20.48 39.480889 41.301333 78.791111 62.008889 118.215111 0.170667 0.284444 0.398222 0.568889 0.398223 0.455111 21.390222-39.822222 42.837333-79.473778 64.056888-119.352889 2.048-3.868444 4.380444-5.12 8.590223-5.006222 9.557333 0.284444 19.114667 0 28.615111 0.113778 1.706667 0 3.413333 0.113778 6.087111 0.284444l-78.051556 132.892445h13.084445zM171.52 137.102222c7.111111 5.12 14.392889 11.036444 21.845333 17.635556 7.395556 6.599111 14.108444 14.222222 20.024889 22.755555 2.958222 4.608 6.542222 9.784889 10.695111 15.530667 4.152889 5.688889 8.021333 11.434667 11.605334 17.180444 4.152889 6.257778 8.305778 12.856889 12.515555 19.740445h184.490667c4.152889-5.688889 8.021333-11.719111 11.605333-18.033778 2.958222-5.12 6.257778-10.296889 9.841778-15.473778a526.677333 526.677333 0 0 1 27.648-36.522666c5.916444-7.168 14.563556-14.222222 25.827556-21.048889a39.992889 39.992889 0 0 0 18.261333-24.974222 20.764444 20.764444 0 0 0-1.763556-13.312 20.764444 20.764444 0 0 0-12.060444-9.898667c-5.347556-1.706667-9.955556-2.275556-13.767111-1.706667a56.832 56.832 0 0 0-11.605334 3.015111 356.807111 356.807111 0 0 0-12.060444 4.721778 39.765333 39.765333 0 0 1-15.132444 2.616889 31.630222 31.630222 0 0 1-14.734223-3.015111 54.044444 54.044444 0 0 1-10.695111-7.338667l-10.24-9.045333a36.408889 36.408889 0 0 0-14.222222-7.281778c-11.946667-4.551111-21.447111-5.461333-28.558222-2.56-7.168 2.844444-14.222222 7.395556-21.390222 13.767111-5.973333 5.688889-10.752 9.671111-14.279112 12.003556a8.874667 8.874667 0 0 1-4.494222 1.706666 54.784 54.784 0 0 1-9.784889-6.030222 87.779556 87.779556 0 0 1-8.476444-5.575111 44.771556 44.771556 0 0 1-7.566222-7.281778c-4.721778-4.551111-11.889778-7.623111-21.390223-9.045333a38.058667 38.058667 0 0 0-25.827555 4.721778c-10.126222 6.314667-17.237333 11.605333-21.390222 15.928889a35.271111 35.271111 0 0 1-18.773334 8.988444 29.411556 29.411556 0 0 1-15.587555-0.853333l-17.351111-5.12a100.920889 100.920889 0 0 0-16.952889-3.413334 20.366222 20.366222 0 0 0-14.279111 3.413334c-8.362667 6.257778-10.979556 13.824-8.021334 22.755555 2.958222 8.874667 8.305778 15.928889 16.042667 21.048889z m420.977778 279.04l-1.536-1.706666a504.376889 504.376889 0 0 0-44.145778-42.609778 733.866667 733.866667 0 0 1-45.454222-39.139556c-13.084444-12.288-24.064-23.324444-32.995556-33.109333a351.459556 351.459556 0 0 1-26.737778-30.094222H241.038222a534.186667 534.186667 0 0 1-26.737778 30.947555c-8.305778 9.784889-18.773333 20.48-31.232 32.256-12.458667 11.776-26.453333 24.234667-41.870222 37.432889-17.863111 14.904889-34.304 31.800889-49.493333 50.688A278.641778 278.641778 0 0 0 32.881778 555.804444a239.616 239.616 0 0 0 31.630222 156.899556 256 256 0 0 0 60.188444 69.290667c25.258667 20.593778 56.035556 37.376 92.273778 50.289777 36.238222 12.856889 77.880889 19.342222 124.814222 19.342223 27.477333 0 52.963556-2.275556 76.913778-6.314667a308.224 308.224 0 0 1-32.256-137.159111 310.670222 310.670222 0 0 1 205.994667-292.010667z" fill="${getColor(0, color, colors, '#333333')}" />
          </svg>''';
        break;
      case IconNames.saasAIconYibanzhichubeifen19:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M390.940444 285.297778h229.603556c71.111111 0 125.269333-147.342222 125.269333-147.342222 0-44.088889-4.551111-77.425778-83.512889-79.815112-78.904889-2.389333-97.792 52.736-154.453333 52.736-58.140444 0-87.210667-43.292444-158.72-52.736C277.731556 48.696889 265.671111 93.866667 265.671111 138.012444c0 0 54.158222 147.342222 125.269333 147.342223z m242.176 45.511111H390.940444C122.766222 330.865778 56.888889 834.56 56.888889 834.56 56.888889 900.664889 112.981333 967.111111 182.158222 967.111111h659.683556C911.018667 967.111111 967.111111 900.664889 967.111111 834.56c0 0-65.877333-503.694222-333.994667-503.694222z m27.477334 387.128889c16.156444 0 29.240889 12.515556 29.240889 27.989333 0 15.416889-13.084444 27.989333-29.240889 27.989333H541.866667v73.386667c0 15.473778-13.084444 27.989333-29.240889 27.989333a28.615111 28.615111 0 0 1-29.240889-27.989333v-73.386667H363.861333a28.615111 28.615111 0 0 1-29.240889-27.989333c0-15.473778 13.084444-27.989333 29.240889-27.989333h119.466667v-35.498667h-119.466667a28.615111 28.615111 0 0 1-29.240889-27.989333c0-15.416889 13.084444-27.989333 29.240889-27.989334h80.782223l-82.716445-140.515555a27.306667 27.306667 0 0 1 10.695111-38.229333 29.980444 29.980444 0 0 1 39.936 10.24l99.612445 168.504888h5.063111l99.555555-168.561777a29.980444 29.980444 0 0 1 39.936-10.183111 27.306667 27.306667 0 0 1 10.752 38.229333l-82.716444 140.515555h75.832889c16.156444 0 29.240889 12.515556 29.240889 27.989334s-13.084444 27.989333-29.240889 27.989333H541.866667v35.498667h118.727111z" fill="${getColor(0, color, colors, '#333333')}" />
          </svg>''';
        break;
      case IconNames.saasOther:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M256 56.888889H284.444444a199.111111 199.111111 0 0 1 199.111112 199.111111v227.555556h-227.555556A199.111111 199.111111 0 0 1 56.888889 284.444444v-28.444444A199.111111 199.111111 0 0 1 256 56.888889z m0 483.555555h227.555556v227.555556A199.111111 199.111111 0 0 1 284.444444 967.111111h-28.444444A199.111111 199.111111 0 0 1 56.888889 768V739.555556a199.111111 199.111111 0 0 1 199.111111-199.111112zM739.555556 56.888889h28.444444A199.111111 199.111111 0 0 1 967.111111 256V284.444444a199.111111 199.111111 0 0 1-199.111111 199.111112h-227.555556v-227.555556A199.111111 199.111111 0 0 1 739.555556 56.888889zM540.444444 540.444444h227.555556A199.111111 199.111111 0 0 1 967.111111 739.555556v28.444444a199.111111 199.111111 0 0 1-199.111111 199.111111H739.555556a199.111111 199.111111 0 0 1-199.111112-199.111111v-227.555556z" fill="${getColor(0, color, colors, '#333333')}" />
          </svg>''';
        break;
      case IconNames.saasRili:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M726.45378589 40.67299953a37.70616053 37.70616053 0 0 1 37.70615914 37.70615916v36.52784349h143.75473576c41.65352325 0 75.41231969 33.75879644 75.41231968 75.41231969v717.59535892A75.41231969 75.41231969 0 0 1 907.91468079 983.32700047H116.08531921A75.41231969 75.41231969 0 0 1 40.67299953 907.91468079V190.31932187c0-41.65352325 33.75879644-75.41231969 75.41231968-75.41231969h150.82464077v-36.52784349a37.70616053 37.70616053 0 0 1 75.41231968 0v36.52784349H688.74762534v-36.52784349a37.70616053 37.70616053 0 0 1 37.70616055-37.70615916zM116.08531921 404.06611725v503.84856354h791.82936158V405.9514248H127.86849378a37.70616053 37.70616053 0 0 1-11.78317457-1.88530755z m621.20898677 85.428018a39.886047 39.886047 0 0 1 0 56.08791356l-262.64697127 264.65011149a39.29688916 39.29688916 0 0 1-55.73441693 0.11783129L286.94135659 678.14276782a39.82713204 39.82713204 0 0 1 17.49801491-66.69277108 39.29688916 39.29688916 0 0 1 38.23640342 10.42810989l103.98652005 104.22218399 234.7797614-236.60615537a39.29688916 39.29688916 0 0 1 55.85224961-0.05891496zM266.90995998 190.31932187H116.08531921v142.10509077a37.70616053 37.70616053 0 0 1 11.78317457-1.88530754h780.04618701V190.31932187h-143.75473576v36.52784211a37.70616053 37.70616053 0 0 1-75.41231969 0v-36.52784211H342.32227966v36.52784211a37.70616053 37.70616053 0 0 1-75.41231968 0v-36.52784211z" fill="${getColor(0, color, colors, '#333333')}" />
          </svg>''';
        break;
      case IconNames.saasWarn:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M512 26.56548587A486.05866667 486.05866667 0 0 0 26.56548587 512 485.98064747 485.98064747 0 0 0 512 997.43451413 485.98064747 485.98064747 0 0 0 997.43451413 512 486.05866667 486.05866667 0 0 0 512 26.56548587zM512 943.44533333A431.99146667 431.99146667 0 0 1 80.55466667 512c0-237.95809493 193.4872384-431.44533333 431.44533333-431.44533333 237.95809493 0 431.44533333 193.4872384 431.44533333 431.44533333 0 237.95809493-193.4872384 431.44533333-431.44533333 431.44533333z" fill="${getColor(0, color, colors, '#333333')}" />
            <path d="M458.0888384 781.71184747a53.9111616 53.9111616 0 1 0 107.8223232 0 53.9111616 53.9111616 0 0 0-107.8223232 0z" fill="${getColor(1, color, colors, '#333333')}" />
            <path d="M469.5576384 208.58392427m45.485104 0l0.0780192 0q45.48510507 0 45.48510507 45.485104l0 333.76548586q0 45.48510507-45.48510507 45.48510507l-0.0780192 0q-45.48510507 0-45.485104-45.48510507l0-333.76548586q0-45.48510507 45.485104-45.485104Z" fill="${getColor(2, color, colors, '#333333')}" />
          </svg>''';
        break;
      case IconNames.saasDingwei:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M466.147556 511.886222a45.852444 45.852444 0 1 0 91.704888 0 45.852444 45.852444 0 0 0-91.704888 0z" fill="${getColor(0, color, colors, '#333333')}" />
            <path d="M935.480889 466.261333h-47.786667a377.685333 377.685333 0 0 0-108.145778-221.923555 378.254222 378.254222 0 0 0-221.866666-108.202667v-47.729778A45.852444 45.852444 0 0 0 511.829333 42.666667a45.852444 45.852444 0 0 0-45.738666 45.738666v47.786667a377.685333 377.685333 0 0 0-221.923556 108.145778 378.254222 378.254222 0 0 0-108.145778 221.866666h-47.672889A45.852444 45.852444 0 0 0 42.666667 512c0 25.144889 20.48 45.738667 45.738666 45.738667h47.786667a377.685333 377.685333 0 0 0 108.145778 221.923555 378.254222 378.254222 0 0 0 221.866666 108.202667v47.729778c0 25.088 20.536889 45.738667 45.795556 45.738666 25.144889 0 45.738667-20.48 45.738667-45.738666v-47.786667a376.490667 376.490667 0 0 0 221.923555-108.145778 378.254222 378.254222 0 0 0 108.202667-221.866666h47.729778c25.088 0 45.738667-20.536889 45.738666-45.795556a46.08 46.08 0 0 0-45.852444-45.738667z m-377.856 336.782223v-79.758223a45.852444 45.852444 0 0 0-45.738667-45.795555 45.852444 45.852444 0 0 0-45.738666 45.795555v79.758223A295.310222 295.310222 0 0 1 220.728889 557.624889h79.815111c25.088 0 45.738667-20.48 45.738667-45.738667a45.852444 45.852444 0 0 0-45.738667-45.738666H220.728889A295.310222 295.310222 0 0 1 466.147556 220.728889v79.815111c0 25.088 20.48 45.738667 45.738666 45.738667 25.144889 0 45.738667-20.48 45.738667-45.738667V220.728889a295.310222 295.310222 0 0 1 245.418667 245.418667h-79.758223a45.852444 45.852444 0 0 0-45.795555 45.738666c0 25.144889 20.536889 45.738667 45.795555 45.738667h79.758223a295.310222 295.310222 0 0 1-245.418667 245.418667z" fill="${getColor(1, color, colors, '#333333')}" />
          </svg>''';
        break;
      case IconNames.saasAddress:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M229.16294656 710.80292937a395.995875 395.995875 0 0 1 0-562.31414249c156.19837312-155.31838219 409.47573469-155.31838219 565.67410688 0a395.995875 395.995875 0 0 1 0 562.35414187L512 992l-282.83705344-281.19707062zM512 552.00458375a119.99875031 119.99875031 0 1 0 0-239.99750062 119.99875031 119.99875031 0 0 0 0 239.99750062z" fill="${getColor(0, color, colors, '#3D3D3D')}" />
          </svg>''';
        break;
      case IconNames.saasTianjia:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M925.79310312 512C925.79310312 283.46838031 740.53164125 98.20689688 512 98.20689688 283.46837562 98.20689688 98.20689688 283.46838031 98.20689688 512 98.20689688 740.53161969 283.46837562 925.79310312 512 925.79310312 740.53164125 925.79310312 925.79310312 740.53161969 925.79310312 512ZM545.10344844 478.89655156L545.10344844 280.30471625C545.10344844 261.89122812 530.28253812 247.17241344 512 247.17241344 493.59001344 247.17241344 478.89655156 262.00625094 478.89655156 280.30471625L478.89655156 478.89655156 280.30471156 478.89655156C261.89123281 478.89655156 247.17241344 493.71747031 247.17241344 512 247.17241344 530.40999312 262.00625094 545.10344844 280.30471156 545.10344844L478.89655156 545.10344844 478.89655156 743.69528375C478.89655156 762.10877188 493.71746188 776.82758656 512 776.82758656 530.40998656 776.82758656 545.10344844 761.99374906 545.10344844 743.69528375L545.10344844 545.10344844 743.69533813 545.10344844C762.1088 545.10344844 776.82758656 530.28252969 776.82758656 512 776.82758656 493.59000688 761.99376594 478.89655156 743.69533813 478.89655156L545.10344844 478.89655156ZM32 512C32 246.90332094 246.90331625 32 512 32 777.0967175 32 992 246.90332094 992 512 992 777.09667906 777.0967175 992 512 992 246.90331625 992 32 777.09667906 32 512Z" fill="${getColor(0, color, colors, '#333333')}" />
          </svg>''';
        break;
      case IconNames.saasPhone1:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M************.218 ************.218c-68.473 0-123.982-55.975-123.982-125.054L167.172 136.836c0-69.078 55.512-125.054 123.982-125.054l464.928 0c68.474 0 123.984 55.975 123.984 125.054l0 750.328C880.066 956.242 ************.218 ************.218L************.218zM818.062 136.836c0-34.516-27.751-62.526-61.98-62.526L291.154 74.31c-34.228 0-61.991 28.01-61.991 62.526l0 31.268 588.899 0L818.062 136.836 818.062 136.836zM818.062 230.63 229.163 230.63l0 499.242 588.899 0L818.062 230.63 818.062 230.63zM818.062 792.398 229.163 792.398l0 94.766c0 34.54 27.765 62.526 61.991 62.526l464.928 0c34.229 0 61.98-27.986 61.98-62.526L818.062 792.398 818.062 792.398zM523.623 918.429c-25.668 0-46.482-20.993-46.482-46.896 0-25.903 20.816-46.895 46.482-46.895 25.664 0 46.477 20.993 46.477 46.895S549.287 918.429 523.623 918.429L523.623 918.429z" fill="${getColor(0, color, colors, '#333333')}" />
            <path d="M756.081 1017.218 291.154 1017.218c-71.121 0-128.982-58.342-128.982-130.054L162.172 136.836c0-71.712 57.861-130.054 128.982-130.054l464.928 0c71.122 0 128.984 58.342 128.984 130.054l0 750.328C885.066 958.876 827.204 1017.218 756.081 1017.218zM291.154 16.783c-65.607 0-118.982 53.856-118.982 120.054l0 750.328c0 66.198 53.375 120.054 118.982 120.054l464.927 0c65.608 0 118.985-53.855 118.985-120.054L875.066 136.836c0-66.198-53.376-120.054-118.984-120.054L291.154 16.782zM756.082 954.69 291.154 954.69c-36.939 0-66.991-30.292-66.991-67.526l0-99.766 598.899 0 0.001 99.766C823.063 924.398 793.016 954.69 756.082 954.69zM234.163 797.398l0 89.766c0 31.72 25.566 57.526 56.991 57.526l464.928 0c31.419 0 56.98-25.807 56.98-57.526l0-89.766L234.163 797.398zM523.623 923.429c-28.387 0-51.482-23.28-51.482-51.896s23.096-51.895 51.482-51.895c28.385 0 51.477 23.279 51.477 51.895S552.008 923.429 523.623 923.429zM523.623 829.639c-22.873 0-41.482 18.794-41.482 41.895 0 23.102 18.609 41.896 41.482 41.896 22.871 0 41.477-18.794 41.477-41.896C565.1 848.433 546.494 829.639 523.623 829.639zM823.062 734.872 224.163 734.872 224.163 225.63l598.899 0L823.062 734.872zM234.163 724.872l578.899 0L813.062 235.63 234.163 235.63 234.163 724.872zM823.062 173.104 224.163 173.104l0-36.268c0-37.234 30.052-67.526 66.991-67.526l464.927 0c36.934 0 66.98 30.292 66.98 67.526L823.061 173.104zM234.163 163.104l578.899 0 0-26.268c0-31.72-25.562-57.526-56.98-57.526L291.154 79.31c-31.425 0-56.991 25.806-56.991 57.526L234.163 163.104z" fill="${getColor(1, color, colors, '#333333')}" />
          </svg>''';
        break;
      case IconNames.saasDuoren:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M523.52 555.73333334C593.28 509.65333334 640 430.93333334 640 341.33333334c0-141.44-114.56-256-256-256C242.56 85.33333334 128 199.89333334 128 341.33333334c0 89.6 46.72 168.32 116.48 214.4C101.76 602.45333334 0 722.77333334 0 885.33333334 0 903.25333334 14.08 917.33333334 32 917.33333334S64 903.25333334 64 885.33333334C64 708.69333334 207.36 597.33333334 384 597.33333334c176.64 0 320 111.36 320 288 0 17.92 14.08 32 32 32 17.92 0 32-14.08 32-32C768 722.77333334 666.24 602.45333334 523.52 555.73333334zM384 533.33333334C278.4 533.33333334 192 446.93333334 192 341.33333334s86.4-192 192-192c105.6 0 192 86.4 192 192S489.6 533.33333334 384 533.33333334zM804.48 559.57333334C878.08 514.13333334 928 434.13333334 928 341.33333334c0-141.44-114.56-256-256-256C654.08 85.33333334 640 99.41333334 640 117.33333334 640 135.25333334 654.08 149.33333334 672 149.33333334c105.6 0 192 86.4 192 192 0 71.04-38.4 132.48-96 165.76-56.32 34.56-71.68 83.2 0 109.44 111.36 39.68 192 144 192 269.44 0 17.92 14.08 32 32 32 17.92 0 32-14.08 32-32C1024 738.13333334 933.12 612.05333334 804.48 559.57333334z" fill="${getColor(0, color, colors, '#333333')}" />
          </svg>''';
        break;
      case IconNames.saasKeyDelete:
        svgXml = '''
          <svg viewBox="0 0 1396 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M1274.78533791 114.31784297c22.66297268 0 41.3196677 17.04632668 43.87268773 39.04158688L1318.97224426 158.5047493v706.9905014c0 22.66297268-17.04632668 41.3196677-39.0415869 43.87268772l-5.14531945 0.31421861H435.23411749c-9.78003573 0-19.20657483-3.2207339-26.86563905-9.11232186l-4.35977428-3.84917112-309.30834438-353.49525071a44.18690634 44.18690634 0 0 1-3.6920618-58.32671636l3.6920618-4.12411032 309.30834438-353.49525071c6.87351907-6.9127957 15.82873163-11.31184801 25.41238003-12.5687211l5.8130333-0.39277188h839.55122042z m-44.18690633 88.37381268H453.53731589L188.41587786 512l265.12143803 309.30834435H1230.59843158v-618.6166887zM713.23849386 352.5343653l3.84916974 3.41711983 93.75479609 93.71551946 93.71551946-93.71551946a44.18690634 44.18690634 0 0 1 65.90722455 58.64093499l-3.41711984 3.84916975-93.75479608 93.71551943 93.75479608 93.75479611a44.18690634 44.18690634 0 0 1-58.64093497 65.90722454l-3.84916974-3.41711984-93.75479609-93.75479607-93.71551946 93.75479607a44.18690634 44.18690634 0 0 1-65.90722455-58.64093496l3.41711983-3.84916974 93.7547961-93.75479611-93.7547961-93.71551943a44.18690634 44.18690634 0 0 1 58.64093499-65.90722457z" fill="${getColor(0, color, colors, '#606066')}" />
          </svg>''';
        break;
      case IconNames.saasEditPen:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M119.87367 715.220425l0 148.975986 149.005662 0 431.384961-435.265338-149.056827-148.971893L119.87367 715.220425zM813.943378 315.225381c15.735379-15.679098 15.735379-39.231513 0-54.856375l-90.1236-90.23821c-15.743566-15.654538-39.231513-15.654538-54.977126 0l-70.591755 70.595848 149.056827 149.031245L813.943378 315.225381zM472.797651 785.786597l-78.409814 78.409814 509.73747 0 0-78.409814L472.797651 785.786597z" fill="${getColor(0, color, colors, '#333333')}" />
          </svg>''';
        break;
      case IconNames.saasPrev:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M756.73246037 937.70891037a36.44125184 36.44125184 0 0 1-47.96264334 4.01306851l-3.49525333-2.91271111-436.90666667-418.78313074c-14.36937443-13.65737927-14.88718962-36.05289073-2.00653369-50.48699221l3.23634517-3.17161927 436.90666666-382.27715072a36.44125184 36.44125184 0 0 1 51.13426149 51.58734962l-3.23634516 3.23634517-407.00283223 356.12747889L755.63210297 886.12156075a36.44125184 36.44125184 0 0 1 4.07779556 48.02736924l-2.97743816 3.49525334z" fill="${getColor(0, color, colors, '#323233')}" />
          </svg>''';
        break;
      case IconNames.saasArrowBottom:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M959.0422437 267.26753963a36.44125184 36.44125184 0 0 1 4.01306852 47.96264334l-2.91271112 3.49525333-418.78313073 436.90666667c-13.65737927 14.36937444-36.05289073 14.88718962-50.48699222 2.00653369l-3.17161927-3.23634517-382.27715072-436.90666666a36.44125184 36.44125184 0 0 1 51.58734962-51.13426149l3.23634517 3.23634516 356.12747889 407.00283223L907.45489408 268.36789703a36.44125184 36.44125184 0 0 1 48.02736924-4.07779556l3.49525334 2.97743816z" fill="${getColor(0, color, colors, '#323233')}" />
          </svg>''';
        break;
      case IconNames.saasNext:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M267.26753963 86.29108963a36.44125184 36.44125184 0 0 1 47.96264334-4.01306851l3.49525333 2.91271111 436.90666667 418.78313074c14.36937443 13.65737927 14.88718962 36.05289074 2.00653369 50.48699221l-3.23634517 3.17161927-436.90666667 382.27715072a36.44125184 36.44125184 0 0 1-51.13426148-51.58734962l3.23634517-3.23634517 407.00283222-356.12747889L268.36789703 137.87843925a36.44125184 36.44125184 0 0 1-4.07779556-48.02736924l2.97743816-3.49525334z" fill="${getColor(0, color, colors, '#323233')}" />
          </svg>''';
        break;
      case IconNames.saasHand:
        svgXml = '''
          <svg viewBox="0 0 1450 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M126.464 794.083556c9.500444 0 17.720889-3.527111 24.661333-10.524445 6.912-7.025778 10.382222-15.331556 10.382223-24.917333 0-9.614222-3.470222-17.92-10.382223-24.917334a33.507556 33.507556 0 0 0-24.661333-10.524444c-9.472 0-17.692444 3.498667-24.632889 10.524444a34.247111 34.247111 0 0 0-10.410667 24.917334c0 9.585778 3.470222 17.92 10.410667 24.917333s15.160889 10.524444 24.632889 10.524445z m418.417778 70.855111c68.977778 0 103.480889-30.805333 103.480889-92.444445 0-9.614222-0.910222-19.939556-2.759111-31.004444 10.979556-5.916444 19.626667-15.616 26.026666-29.098667 6.4-13.454222 9.585778-27.022222 9.585778-40.675555s-3.299556-26.396444-9.870222-38.229334c19.342222-18.432 29.013333-40.391111 29.013333-65.877333 0-9.216-1.792-19.484444-5.461333-30.72-3.640889-11.264-8.220444-20.053333-13.681778-26.311111h181.248c19.000889 0 35.413333-7.025778 49.294222-21.048889s20.821333-30.634667 20.821334-49.834667c0-18.830222-7.111111-35.328-21.361778-49.550222-14.222222-14.222222-30.492444-21.333333-48.753778-21.333333H547.043556c0-7.395556 2.730667-16.327111 8.220444-26.851556 5.461333-10.524444 11.491556-20.650667 18.062222-30.435555 6.570667-9.784889 12.600889-22.328889 18.062222-37.660445a138.069333 138.069333 0 0 0 8.248889-46.791111c0-24.746667-8.135111-42.723556-24.376889-53.987556-16.270222-11.264-37.347556-16.896-63.260444-16.896-8.760889 0-25.201778 25.656889-49.294222 76.970667-8.760889 16.241778-15.502222 28.245333-20.252445 36.010667-14.620444 23.608889-35.043556 50.346667-61.326222 80.270222-25.941333 29.895111-44.373333 49.493333-55.324444 58.709333-25.201778 21.048889-50.744889 31.573333-76.657778 31.573334h-17.521778v354.332444h17.521778c26.282667 0 56.775111 5.916444 91.448889 17.720889 34.702222 11.832889 70.001778 23.608889 105.955555 35.441778 35.982222 11.804444 68.721778 17.720889 98.304 17.720889z m2.702222 70.883555c-48.554667 0-107.320889-12.743111-176.327111-38.200889-59.875556-21.788444-100.579556-32.682667-122.112-32.682666H91.420444c-19.342222 0-35.84-6.912-49.550222-20.764445a68.664889 68.664889 0 0 1-20.536889-50.090666V439.694222c0-19.541333 6.826667-36.266667 20.536889-50.090666a67.128889 67.128889 0 0 1 49.550222-20.764445H249.173333c3.640889 0 7.566222-0.853333 11.776-2.503111 4.181333-1.649778 8.476444-4.266667 12.856889-7.736889 4.380444-3.527111 8.504889-6.826667 12.316445-9.955555 3.84-3.157333 8.248889-7.310222 13.141333-12.487112a407.637333 407.637333 0 0 0 29.297778-33.223111c23.751111-27.306667 41.984-51.114667 54.784-71.395555 4.721778-7.765333 10.752-19.228444 18.062222-34.360889 7.310222-15.132444 14.051556-28.444444 20.252444-39.850667 6.229333-11.463111 13.596444-23.096889 22.186667-34.901333a94.378667 94.378667 0 0 1 30.122667-27.392C485.432889 88.576 498.119111 85.333333 512 85.333333c45.624889 0 83.342222 12.373333 113.095111 37.091556 29.752889 24.746667 44.629333 59.619556 44.629333 104.675555 0 25.088-4.010667 48.696889-12.060444 70.855112h204.8c37.973333 0 70.826667 14.023111 98.588444 42.097777 27.733333 28.046222 41.614222 61.070222 41.614223 99.100445 0 38.741333-13.795556 72.163556-41.358223 100.209778-27.562667 28.074667-60.501333 42.097778-98.844444 42.097777h-92.529778a150.528 150.528 0 0 1-20.280889 65.877334c1.109333 7.765333 1.649778 15.701333 1.649778 23.808 0 37.262222-10.951111 70.144-32.853333 98.56 0.369778 51.313778-15.160889 91.818667-46.535111 121.543111-31.402667 29.724444-72.817778 44.572444-124.302223 44.572444z" fill="${getColor(0, color, colors, '#333333')}" />
          </svg>''';
        break;
      case IconNames.saasWages:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M646.272 1000.68266667l308.608-242.88a13.888 13.888 0 0 0 5.12-11.008 13.888 13.888 0 0 0-5.12-11.008l-308.608-243.008a9.6 9.6 0 0 0-11.712-0.256 13.76 13.76 0 0 0-5.248 12.416v172.544H368.256S227.52 681.51466667 192 559.08266667c7.552 89.6 48.256 171.264 111.872 224.832 27.072 21.376 59.008 32.64 91.52 32.064h232.192v172.608c0.256 5.12 2.688 9.856 6.464 12.288 3.84 2.432 8.512 2.368 12.224-0.192zM377.6 44.97066667L69.12 287.78666667a13.888 13.888 0 0 0-5.12 11.072c0 4.48 1.92 8.64 5.12 11.072L377.6 552.49066667a9.6 9.6 0 0 0 11.712 0.32 13.76 13.76 0 0 0 5.312-12.352V367.97866667h261.12s140.672-3.904 176.256 118.4c-7.552-89.536-48.256-171.2-111.872-224.704A143.552 143.552 0 0 0 628.48 229.54666667H394.816V56.93866667a13.76 13.76 0 0 0-5.312-12.416 9.6 9.6 0 0 0-11.84 0.384v0.064z" fill="${getColor(0, color, colors, '#333333')}" />
          </svg>''';
        break;
      case IconNames.saasMore:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M161.11 568.289c-15.596-15.595-23.393-34.359-23.393-56.289s7.797-40.694 23.392-56.289c15.596-15.595 34.358-23.393 56.29-23.393s40.693 7.798 56.288 23.393C289.283 471.306 297.08 490.07 297.08 512s-7.797 40.694-23.393 56.289c-15.595 15.595-34.358 23.393-56.289 23.393s-40.693-7.799-56.289-23.393z m294.601 0c-15.595-15.595-23.393-34.359-23.393-56.289s7.798-40.694 23.393-56.289c15.595-15.595 34.359-23.393 56.289-23.393s40.694 7.798 56.289 23.393c15.595 15.595 23.393 34.359 23.393 56.289s-7.798 40.694-23.393 56.289c-15.595 15.595-34.359 23.393-56.289 23.393s-40.694-7.799-56.289-23.393z m294.602 0C734.717 552.694 726.92 533.93 726.92 512s7.797-40.694 23.393-56.289c15.595-15.595 34.358-23.393 56.289-23.393s40.693 7.798 56.289 23.393c15.595 15.595 23.392 34.359 23.392 56.289s-7.797 40.694-23.392 56.289c-15.596 15.595-34.359 23.393-56.29 23.393s-40.693-7.799-56.288-23.393z" fill="${getColor(0, color, colors, '#333333')}" />
          </svg>''';
        break;
      case IconNames.saasRecover:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M960 864c-37.952-360.32-207.744-540.416-509.312-540.416V200.768a32 32 0 0 0-53.632-23.616L58.496 487.68a32 32 0 0 0-1.92 45.248l2.688 2.56 338.56 291.52a32 32 0 0 0 52.864-24.256v-155.776c193.024-23.424 362.752 48.96 509.312 217.088z" fill="${getColor(0, color, colors, '#333333')}" />
          </svg>''';
        break;
      case IconNames.saasEyeClose:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M130.859008 309.035008c59.551744 63.721472 142.072832 113.575936 223.08352 141.656064 204.224512 70.776832 400.790528-0.528384 558.675968-141.656064 29.75232-26.591232 73.49248 17.729536 43.543552 44.488704-20.455424 18.286592-41.659392 35.634176-63.526912 51.862528 18.356224 36.08064 36.703232 72.1408 55.06048 108.20096 18.302976 35.960832-34.85696 67.776512-53.19168 31.752192-19.87584-39.112704-39.785472-78.168064-59.669504-117.248l6.393856 12.560384c-36.478976 23.02464-74.477568 42.846208-113.641472 58.73664l44.539904 116.064256c14.374912 37.4528-43.885568 53.720064-58.943488 17.829888l-0.443392-1.103872-43.1616-112.452608c-40.722432 12.041216-82.463744 19.688448-124.855296 22.187008l-4.576256 0.249856 0.038912 0.759808c0.01024 0.258048 0.018432 0.518144 0.023552 0.77824l0.008192 0.790528v132.120576c0 40.169472-60.362752 40.57088-61.570048 1.205248l-0.018432-1.205248V544.49152c0-0.999424 0.036864-1.974272 0.109568-2.924544-41.269248-2.881536-82.967552-10.881024-124.76416-24.659968-0.965632-0.31744-1.932288-0.638976-2.89792-0.96256l-47.02208 122.540032c-14.179328 36.954112-72.711168 21.413888-59.800576-15.599616l0.413696-1.128448 47.20128-123.000832a36.702208 36.702208 0 0 1 2.12992-4.595712c-36.094976-15.526912-71.549952-34.24256-104.86784-56.02816-24.143872 31.972352-48.284672 63.9488-72.431616 95.904768-10.22976 13.568-26.24512 20.799488-42.12736 11.29472-12.962816-7.76192-21.16608-29.000704-11.352064-42.628096l0.303104-0.4096 75.836416-100.410368c-18.518016-15.033344-35.97824-31.161344-52.051968-48.359424-27.411456-29.32736 16.0768-73.879552 43.551744-44.488704z m409.28768 233.11872v0.009216-0.01024z" fill="${getColor(0, color, colors, '#323233')}" />
          </svg>''';
        break;
      case IconNames.saasCode:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M943.1 172c-2.4-0.2-245.1-25.3-413.8-147.8-5.1-3.7-11-5.6-17.3-5.6-6.2 0-12.2 1.9-17.3 5.6C326.9 146 83.3 171.8 80.9 172c-15.2 1.4-26.6 14.1-26.6 29.3 0 6.7 0.6 165.8 54.8 344.4 32.1 105.8 76.4 196.4 131.9 269.2 70.3 92.3 158.5 156 262 189.2 2.9 0.9 5.9 1.4 9 1.4s6.1-0.5 8.9-1.4c103.6-33.2 191.7-96.8 262-189.2 55.4-72.7 99.8-163.2 131.9-269.2 54.1-178.6 54.8-337.7 54.8-344.4C969.7 186.1 958.3 173.5 943.1 172zM910.1 227.2l-0.1 1.6c-2.9 58.1-13.4 174.4-51.4 299.9-66.7 220.1-183.1 360.1-346 416.1L512 945l-0.6-0.2C349 888.9 232.7 749.4 165.8 530.1c-39.8-130.5-49.4-254.2-51.8-301.4l-0.1-1.6 1.5-0.2c70.6-10.3 250.5-44.8 395.5-142.4l0.9-0.7 1 0.7C658 182.1 837.9 216.6 908.5 227L910.1 227.2z" fill="${getColor(0, color, colors, '#333333')}" />
            <path d="M641.8 351 467 580.3l-89-76.1c-5.3-4.5-12.1-7-19.1-7-8.6 0-16.8 3.7-22.4 10.3-10.5 12.3-9.1 31 3.3 41.5l112.7 96.4c5.2 4.4 12.4 7 19.6 7 0.9 0 1.8 0 2.7-0.1 8-0.8 15.4-5 20.3-11.4l193.7-254c4.8-6.3 6.8-14 5.7-21.8-1-7.8-5.1-14.7-11.3-19.5C670.1 335.6 651.6 338.1 641.8 351z" fill="${getColor(1, color, colors, '#333333')}" />
          </svg>''';
        break;
      case IconNames.saasLock1:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M791.418 378.812l-9.314 0 0-44.708 0-1.864C780.24 183.218 659.16 64 512 64s-268.242 119.218-270.104 266.378l0 1.862 0 44.708-9.314 0c-57.746 0-104.316 46.57-104.316 104.316l0 141.572 0 143.434 0.1 0c-0.044 0.622-0.1 1.242-0.1 1.864 0 126.67 59.61 191.868 178.828 191.868l409.812 0c117.356 0 178.828-65.198 178.828-191.868L895.734 483.128C895.734 425.38 849.164 378.812 791.418 378.812zM825.882 766.062c0 92.738-33.886 130.192-117.708 130.192L315.826 896.254c-83.822 0-117.708-35.668-117.708-130.192L198.118 628.74 198.118 493.2c0-24.968 21.402-46.37 46.37-46.37l535.028 0c24.964 0 46.368 21.402 46.368 46.37L825.884 766.062zM711.832 376.948 312.168 376.948l0-39.26c0-114.208 90.358-207.004 199.83-207.004 109.474 0 199.834 92.796 199.834 205.22L711.832 376.948z" fill="${getColor(0, color, colors, '#333333')}" />
            <path d="M508.376 799.8c20.492 0 40.982-16.766 40.982-40.982l0-96.864c18.628-13.04 31.668-35.392 31.668-59.61 0-40.982-31.668-70.788-68.924-70.788-37.254 0-68.922 33.532-68.922 70.788-1.862 24.216 9.314 48.432 26.08 59.61l0 96.864C467.394 783.034 486.022 799.8 508.376 799.8z" fill="${getColor(1, color, colors, '#333333')}" />
          </svg>''';
        break;
      case IconNames.saasEye:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M512.004096 170.667008c-307.160064 0-455.114752 298.305536-455.114752 312.88832 0 14.599168 144.003072 312.889344 455.114752 312.889344 311.105536 0 455.10656-298.2912 455.10656-312.889344 0-14.582784-147.954688-312.88832-455.10656-312.88832z m0 483.555328c-94.2592 0-170.67008-76.41088-170.67008-170.669056 0-94.2592 76.41088-170.663936 170.67008-170.663936 94.251008 0 170.662912 76.40576 170.662912 170.663936 0 94.257152-76.411904 170.669056-170.662912 170.669056z m0-256c-47.136768 0-85.337088 38.20544-85.337088 85.32992 0 47.13472 38.20032 85.33504 85.337088 85.33504 47.123456 0 85.32992-38.20032 85.32992-85.33504 0-47.12448-38.206464-85.32992-85.32992-85.32992z" fill="${getColor(0, color, colors, '#323233')}" />
          </svg>''';
        break;
      case IconNames.saasWechat:
        svgXml = '''
          <svg viewBox="0 0 1174 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M353.30000029 293.525q0-20.5875-12.5624997-33.14999971t-33.14999971-12.5624997q-21.6 0-38.17500029 12.80625029t-16.5750003 32.88750029q0 19.59374971 16.5750003 32.4t38.17500029 12.80625029q20.5875 0 33.14999971-12.30000029t12.5624997-32.88750029zM726.46250029 548.16875q0-14.0625-12.80625029-25.10624971t-32.88750029-11.0437497q-13.55625 0-24.8625 11.30625t-11.30625 24.86249999q0 14.0625 11.30625 25.36875001t24.8625 11.30625q20.08125 0 32.88750029-11.04374971t12.80625029-25.61249971zM607.92499971 293.525q0-20.5875-12.3000003-33.14999971t-32.88750029-12.5624997q-21.6 0-38.17500029 12.80625029t-16.57500029 32.88750029q0 19.59374971 16.57500029 32.4t38.17500029 12.80625029q20.5875 0 32.88750029-12.30000029t12.3000003-32.88750029zM926.84375 548.16875q0-14.0625-13.05-25.10624971t-32.64374971-11.0437497q-13.55625 0-24.8625 11.30625t-11.30625 24.86249999q0 14.0625 11.30625 25.36875001t24.8625 11.30625q19.59374971 0 32.64374971-11.04374971t13.05-25.61249971zM793.25 348.78124971q-15.56250029-2.00625029-35.15625-2.0062503-84.88125 0-156.18750029 38.6812503t-112.2562503 104.71875029-40.93125029 144.39375q0 39.16874971 11.54999971 76.33125-17.58750029 1.50000029-34.14375 1.50000029-13.05 0-25.10624971-0.74999971t-27.61875-3.2625-22.34999971-3.52500029-27.37500029-5.26875029-25.10624971-5.26875029l-127.06875 63.7875 36.16875-109.48124971q-145.64999971-101.96250029-145.6499997-246.09375 0-84.88125 48.97500029-156.18750029t132.58125-112.2562503 182.56875029-40.93125029q88.38749971 0 166.98750029 33.14999971t131.58749971 91.64999971 68.5500003 130.8375zM1090.56875029 630.51875q0 58.76250029-34.40625029 112.25625029t-93.16874971 97.1812503l27.61875 90.9-99.93750029-54.74999971q-75.33749971 18.58124971-109.48124971 18.58124971-84.88125 0-156.18750029-35.39999971t-112.25625029-96.16875029-40.9312503-132.58125 40.9312503-132.58125 112.25625029-96.1687503 156.18750029-35.39999971q80.86875029 0 152.17499971 35.39999971t114.26249971 96.43124971 42.9374997 132.33750029z" fill="${getColor(0, color, colors, '#333333')}" />
          </svg>''';
        break;
      case IconNames.saasEdit:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M866.048 112.2816a32.3072 32.3072 0 1 1 45.6704 45.6704L454.656 615.0144a32.3072 32.3072 0 1 1-45.6704-45.6704l457.0624-457.0624zM856.576 447.488a32.3072 32.3072 0 0 1 64.6144 0v355.5328A118.528 118.528 0 0 1 802.7136 921.6H220.928A118.528 118.528 0 0 1 102.4 803.072V221.2864a118.528 118.528 0 0 1 118.528-118.4768h344.7296a32.3072 32.3072 0 0 1 0 64.6144H220.928c-29.7472 0-53.8624 24.1152-53.8624 53.8624v581.7856c0 29.7472 24.064 53.8624 53.8624 53.8624h581.7856c29.696 0 53.8624-24.064 53.8624-53.8624V447.5392z" fill="${getColor(0, color, colors, '#333333')}" />
          </svg>''';
        break;
      case IconNames.saasSetting:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M513.4336 65.4336c38.0416 0 75.4688 4.7616 111.6672 14.08 8.192 2.048 15.104 7.2704 19.456 14.336l1.8944 3.7376 45.3632 102.912 113.1008-12.288a32 32 0 0 1 19.6608 4.2496l3.3792 2.304 3.072 2.7136a447.6416 447.6416 0 0 1 113.664 194.048 32 32 0 0 1-2.7648 24.064l-2.2528 3.584L871.6288 512l68.608 93.696c4.1472 5.632 6.2976 12.3904 6.1952 19.2512l-0.3072 4.096-0.8192 4.096a447.488 447.488 0 0 1-111.4624 193.4848 32 32 0 0 1-22.1696 9.6256l-4.1984-0.2048-115.6608-12.5952-46.7968 106.24a32 32 0 0 1-13.7216 15.0528l-3.7376 1.792-3.9936 1.28a448.8192 448.8192 0 0 1-110.08 13.6192c-38.7584 0-76.8-4.9152-113.664-14.4896a32 32 0 0 1-19.2-14.336l-1.9456-3.7376-46.5408-105.4208-112.9984 12.288a32 32 0 0 1-19.8144-4.3008l-3.4304-2.3552-3.072-2.7648a447.488 447.488 0 0 1-112.0256-196.3008 32 32 0 0 1 2.8672-23.7568l2.2016-3.4816L152.3712 512 86.4256 421.9904a32 32 0 0 1-6.144-19.2l0.256-4.096 0.8704-4.096a447.5392 447.5392 0 0 1 114.176-196.8128 32 32 0 0 1 22.016-9.472l4.1472 0.2048 110.3872 11.9808 45.056-102.0928a32 32 0 0 1 13.4656-14.848l3.6864-1.8432 3.9424-1.28a448.6144 448.6144 0 0 1 115.2-15.0016z m0 64c-19.2512 0-38.2976 1.4336-57.088 4.2496l-14.0288 2.3552-12.9536 2.6624-48.128 109.056a32 32 0 0 1-24.832 18.7904l-4.096 0.3072-3.7888-0.1536-117.9648-12.9024-6.4 7.168a383.8464 383.8464 0 0 0-69.12 114.2784l-4.608 12.6976-3.0208 9.0624L217.8048 493.056a32 32 0 0 1 3.9936 30.6176l-1.7408 3.6864-2.2528 3.4816-70.9632 96.9216 3.4304 10.6496c14.1824 41.2672 35.328 79.6672 62.4128 113.7152l8.2944 10.0864 7.168 8.1408 120.3712-13.1072c11.264-1.1776 22.016 3.584 28.7232 12.1344l2.304 3.4304 1.7408 3.328 49.5104 112.3328 12.544 2.56c13.7728 2.56 27.6992 4.3008 41.7792 5.3248l14.1312 0.768 14.1824 0.256c18.432 0 36.608-1.28 54.5792-3.84l13.4144-2.1504 11.4688-2.2528 49.8176-112.9472a32 32 0 0 1 24.8832-18.7904l4.096-0.3072 3.7888 0.1536 123.0336 13.3632 7.0144-7.8848c27.648-32.3584 49.7664-69.12 65.3824-108.6976l4.9664-13.312 3.3792-10.1376-73.0624-99.7376a32 32 0 0 1-3.9936-30.6176l1.7408-3.6864 2.2528-3.4816L878.5408 394.24l-3.4304-10.1376a383.3344 383.3344 0 0 0-62.5152-111.4624l-9.216-10.9568-7.2704-8.1408-120.6784 13.1584a32 32 0 0 1-28.672-12.1856l-2.304-3.3792-1.7408-3.3792-48.4352-109.7728-11.8784-2.3552a384.3584 384.3584 0 0 0-41.1136-5.1712l-13.824-0.768-14.0288-0.256zM512 320a192 192 0 1 1 0 384 192 192 0 0 1 0-384z m0 64a128 128 0 1 0 0 256 128 128 0 0 0 0-256z" fill="${getColor(0, color, colors, '#323233')}" />
          </svg>''';
        break;
      case IconNames.saasSearch:
        svgXml = '''
          <svg viewBox="0 0 1097 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M416.028444 64a351.971556 351.971556 0 0 1 255.431112 594.204444l252.188444 252.188445a31.971556 31.971556 0 0 1-42.439111 47.786667l-2.844445-2.56-255.089777-255.089778a351.971556 351.971556 0 1 1-207.246223-636.586667z m0 64a287.971556 287.971556 0 1 0 0 576 287.971556 287.971556 0 0 0 0-576z" fill="${getColor(0, color, colors, '#333333')}" />
          </svg>''';
        break;
      case IconNames.saasLock:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M896 426.666667H768V256c0-141.909333-114.176-256-256-256-141.909333 0-256 114.090667-256 256v170.666667H128c-23.466667 0-42.666667 19.2-42.666667 42.666666v512c0 23.466667 19.2 42.666667 42.666667 42.666667h768c23.466667 0 42.666667-19.2 42.666667-42.666667v-512c0-23.466667-19.2-42.666667-42.666667-42.666666zM512 810.666667c-46.933333 0-85.333333-38.4-85.333333-85.333334s38.4-85.333333 85.333333-85.333333 85.333333 38.4 85.333333 85.333333-38.4 85.333333-85.333333 85.333334zM682.666667 426.666667H341.333333V256c0-93.866667 76.8-170.666667 170.666667-170.666667s170.666667 76.8 170.666667 170.666667v170.666667z" fill="${getColor(0, color, colors, '#333333')}" />
          </svg>''';
        break;
      case IconNames.saasClose:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M888.44231444 174.4220785c14.06574653 14.12223613 15.08254727 36.26590147 2.99391386 51.51791513l-2.99391386 3.38933624-302.10286598 302.102866 302.10286598 302.0463776a38.86439295 38.86439295 0 0 1-51.57440473 57.95765483l-3.38933624-2.99391386-302.0463776-302.10286598-302.102866 302.10286598a38.86439295 38.86439295 0 0 1-57.90116523-51.57440473l2.99391386-3.38933624 302.04637639-302.0463776-302.04637639-302.102866a38.86439295 38.86439295 0 0 1 51.51791513-57.90116523l3.38933624 2.99391386 302.102866 302.04637639 302.0463776-302.04637639a38.86439295 38.86439295 0 0 1 54.96374097 0z" fill="${getColor(0, color, colors, '#2c2c2c')}" />
          </svg>''';
        break;
      case IconNames.saasStar:
        svgXml = '''
          <svg viewBox="0 0 1059 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M529.65334599 889.927083L235.625119 1021.17518201a32.485405 32.485405 0 0 1-43.007852-16.94890701 33.474091 33.474091 0 0 1-2.61295701-16.631115l32.37947501-323.86536599-214.08588301-242.757784A33.368161 33.368161 0 0 1 10.875549 374.291896a32.485405 32.485405 0 0 1 14.83029299-7.662319l314.049124-68.890245 161.72082201-281.316547a32.414785 32.414785 0 0 1 56.39042599 0l161.72082201 281.316547 313.978503 68.854935c17.655112 3.884125 28.81314201 21.50392601 24.999638 39.335588a33.22692 33.22692 0 0 1-7.55638701 15.04215499l-214.08588299 242.757784 32.37947499 323.86536601a32.94443799 32.94443799 0 0 1-29.20155499 36.228289 32.23823399 32.23823399 0 0 1-16.454564-2.64826701L529.65334599 889.927083z" fill="${getColor(0, color, colors, '#333333')}" />
          </svg>''';
        break;
      case IconNames.saasJiantouxia:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M468.906667 755.370667l-298.666667-400.725334A64 64 0 0 1 223.914667 256h597.589333a64 64 0 0 1 53.76 98.645333l-298.666667 400.725334a64 64 0 0 1-107.776 0z" fill="${getColor(0, color, colors, '#323233')}" />
          </svg>''';
        break;
      case IconNames.saasGou:
        svgXml = '''
          <svg viewBox="0 0 1397 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M1341.09125 145.454375s-209.4553125 69.8184375-453.8184375 349.09125C660.3640625 747.6359375 608 852.3640625 520.7271875 992 512 983.271875 381.09125 730.1815625 32 546.90875l183.271875-174.5446875S381.0921875 485.8184375 494.5465625 695.271875c0 0 288-445.09125 846.545625-663.2728125v113.454375z m0 0" fill="${getColor(0, color, colors, '#333333')}" />
          </svg>''';
        break;
      case IconNames.saasArrowLeft:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M451.6279899 44.25922259a47.1327001 47.1327001 0 0 1 66.61421515 66.61421516l-388.813353 388.87619853L518.24220505 888.50014666a47.1327001 47.1327001 0 1 1-66.61421515 66.61421515l-411.0599889-410.99714482a62.84360061 62.84360061 0 0 1 0-88.86084957l410.9971448-411.05998893z" fill="${getColor(0, color, colors, '#333333')}" />
          </svg>''';
        break;
      case IconNames.saasArrowRight:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path d="M572.3720101 979.74077741a47.1327001 47.1327001 0 0 1-66.61421515-66.61421516l388.813353-388.87619853L505.75779495 135.49985334a47.1327001 47.1327001 0 1 1 66.61421515-66.61421515l411.0599889 410.99714482a62.84360061 62.84360061 0 0 1 0 88.86084958l-410.9971448 411.05998892z" fill="${getColor(0, color, colors, '#333333')}" />
          </svg>''';
        break;
      default:
        svgXml = '';
    }

    return SvgPicture.string(
      svgXml,
      width: size,
      height: size,
    );
  }
}
