
来源：https://github.com/suyulin/flutter_iconfont_generator/blob/main/README.md

安装
方法一：全局安装（推荐）
全局安装命令行工具：

dart pub global activate flutter_iconfont_generator
方法 2：添加为开发依赖项
添加到您的pubspec.yaml：

dependencies:
flutter:
sdk: flutter
flutter_svg: ^2.0.0

dev_dependencies:
flutter_iconfont_generator: ^2.0.0
配置
将 iconfont 配置添加到您的pubspec.yaml：

# IconFont configuration
iconfont:
symbol_url: "//at.alicdn.com/t/font_xxx.js"  # Get from iconfont.cn
save_dir: "./lib/iconfont"                    # Output directory
trim_icon_prefix: "icon"                      # Remove icon name prefix
default_icon_size: 18                         # Default icon size
null_safety: true                             # Enable null safety
生成图标代码
方法一：命令行工具（推荐）
全局安装后：

# Generate icons using pubspec.yaml configuration
iconfont_generator

# Generate with custom parameters
iconfont_generator --url "//at.alicdn.com/t/font_xxx.js" --output lib/icons

# Show verbose output
iconfont_generator --verbose

# Show help
iconfont_generator --help
方法二：直接 Dart 执行
如果没有全局安装：

# Run from your project root
dart run flutter_iconfont_generator:iconfont_generator

# Or if you added it as a dev dependency
dart run flutter_iconfont_generator
📖 使用方法
基本用法
import 'package:your_app/iconfont/iconfont.dart';

// Basic usage
IconFont(IconNames.home)

// With size
IconFont(IconNames.user, size: 24)

// With color
IconFont(IconNames.settings, size: 32, color: '#ff0000')

// With multiple colors (for multi-color icons)
IconFont(IconNames.logo, size: 48, colors: ['#ff0000', '#00ff00', '#0000ff'])
命令行选项
# Basic usage
iconfont_generator

# Custom options
iconfont_generator \
--url "//at.alicdn.com/t/c/font_4937193_3aohv86wocr.js" \
--output lib/icons \
--prefix icon \
--size 24 \
--verbose

# Options:
#   -h, --help      Show usage help
#   -v, --verbose   Show verbose output
#   -c, --config    Path to config file (default: pubspec.yaml)
#   -u, --url       IconFont symbol URL (overrides config)
#   -o, --output    Output directory (overrides config)
#   -p, --prefix    Icon prefix to trim (overrides config)
#   -s, --size      Default icon size (overrides config)
单色图标
// Using custom color
IconFont(
IconNames.alipay,
size: 32,
color: 'ff0000',  // Without # prefix
)
多色图标
// Multi-color icons
IconFont(
IconNames.colorful_icon,
size: 32,
colors: ['ff0000', '00ff00', '0000ff'],
)
