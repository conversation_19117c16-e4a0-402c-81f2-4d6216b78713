import 'package:gdjg_pure_flutter/data/test/entity/project_get_project_list_req_entity.dart';
import 'package:gdjg_pure_flutter/data/test/rep/test_rep.dart';
import 'package:gdjg_pure_flutter/feature/test/ui_state/entity/test_entity.dart';
import 'package:gdjg_pure_flutter/feature/test/ui_state/test_us.dart';

class TestVM {
  TestRep rep = TestRep();
  TestUS us = TestUS();

  void queryProjectList() {
    rep.queryProjectList(ProjectGetProjectListReqEntity(
      identity: '1',
      is_ignore: '0',
    )).then((result) {
      if (result.isOK()) {
        List<TestEntity> list = [];
        result.getSucData()?.doing?.list.forEach((element) {
          list.add(TestEntity.transform(element));
        });
        us.showProList.value = list;
      }
    });
  }
}
