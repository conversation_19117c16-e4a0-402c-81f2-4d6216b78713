
import 'package:flutter/material.dart';
import 'package:gdjg_pure_flutter/feature/test/view/test_item_card.dart';
import 'package:gdjg_pure_flutter/feature/test/vm/test_vm.dart';
import 'package:gdjg_pure_flutter/utils/route_util/base_page/base_ful_page.dart';
import 'package:get/get.dart';

class JGTestPage extends BaseFulPage {
  @override
  _JGTestPage createState() => _JGTestPage();
}
class _JGTestPage extends BaseFulPageState {

  TestVM vm = TestVM();
  @override
  void initState() {
    super.initState();
    vm.queryProjectList();
  }

  @override
  Widget yBuild(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          title: Text('测试页面'),
        ),
        body: Container(
            child: Obx(
                  () => ListView.builder(
                  itemCount: vm.us.showProList.length,
                  itemBuilder: (context, index) {
                    return TestItemCard(
                      item: vm.us.showProList[index],
                    );
                  }),
            ))
    );
  }
}
