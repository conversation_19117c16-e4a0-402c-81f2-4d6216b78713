
import 'package:flutter/material.dart';
import 'package:gdjg_pure_flutter/feature/test/view/test_item_card.dart';
import 'package:gdjg_pure_flutter/feature/test/vm/test_vm.dart';
import 'package:gdjg_pure_flutter/init_module/init_route.dart';
import 'package:gdjg_pure_flutter/utils/route_util/base_page/base_ful_page.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:get/get.dart';

class JGTestPage extends BaseFulPage {
  const JGTestPage({super.key});

  @override
  State createState() => _JGTestPage();
}
class _JGTestPage extends BaseFulPageState {

  TestVM vm = TestVM();
  @override
  void initState() {
    super.initState();
    vm.queryProjectList();
  }

  @override
  Widget yBuild(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          title: Text('测试页面'),
        ),
        body: Column(
          children: [
            ElevatedButton(
              onPressed: () {
                YPRoute.openPage(RouteNameCollection.login);
              },
              child: Text('刷新'),
            )
          ],
        )
    );
  }
}
