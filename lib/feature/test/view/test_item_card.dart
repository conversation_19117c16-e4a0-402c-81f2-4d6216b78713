import 'package:flutter/material.dart';
import 'package:gdjg_pure_flutter/feature/test/ui_state/entity/test_entity.dart';

class TestItemCard extends StatelessWidget {

  TestEntity item;

  TestItemCard({
    super.key,
    required this.item,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(8.0),
      child:  <PERSON>umn(
        children: [
          Text(item.name),
        ],
      ),
    );
  }
}