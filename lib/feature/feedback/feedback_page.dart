import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:gdjg_pure_flutter/feature/feedback/entity/feedback_page_entity.dart';
import 'package:gdjg_pure_flutter/feature/feedback/util/feedback_config_util.dart';
import 'package:gdjg_pure_flutter/utils/route_util/base_page/base_ful_page.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/photo_picker_util.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/toast_util.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:webview_flutter_android/webview_flutter_android.dart';
import 'package:webview_flutter_wkwebview/webview_flutter_wkwebview.dart';
import 'package:dio/dio.dart';
import 'package:wechat_assets_picker/wechat_assets_picker.dart';

/// 反馈页面
/// 支持反馈提交和历史查看两种模式
/// 使用WebView加载H5页面，并提供native功能支持：
/// - 图片选择和上传
/// - 返回导航处理
/// - 二维码保存
class FeedbackPage extends BaseFulPage {
  const FeedbackPage({super.key}) : super(appBar: null);

  @override
  State createState() => _FeedbackPageState();
}

/// 反馈页面常量
class FeedbackPageConstants {
  /// 图片选择器结果码（对应Android的IMAGE_CHOOSER_RESULT_CODE = 0x12）
  static const int imageChooserResultCode = 0x12;
}

class _FeedbackPageState extends BaseFulPageState<FeedbackPage> {
  /// WebView控制器
  WebViewController? _webViewController;

  /// 反馈分类
  String _classStr = "";

  /// 反馈内容
  String _contentStr = "";

  /// 页面参数
  FeedbackPageEntity? _pageParam;

  /// 初始加载的URL
  String? _initialUrl;

  /// 返回操作锁定标志，防止并发导航
  bool _isHandlingBack = false;

  /// WebView初始化状态
  bool _isWebViewInitialized = false;

  @override
  void onPageRoute(Object? routeParams, bool fromLaunchTask) {
    super.onPageRoute(routeParams, fromLaunchTask);

    if (routeParams != null) {
      _pageParam = routeParams as FeedbackPageEntity;

      // 避免重复初始化
      if (!_isWebViewInitialized && _webViewController == null) {
        _initWebView();
      }
    }
  }

  /// 初始化WebView控制器和配置
  void _initWebView() async {
    // 创建WebView控制器
    late final PlatformWebViewControllerCreationParams params;
    if (WebViewPlatform.instance is WebKitWebViewPlatform) {
      params = WebKitWebViewControllerCreationParams(
        allowsInlineMediaPlayback: true,
        mediaTypesRequiringUserAction: const <PlaybackMediaTypes>{},
      );
    } else {
      params = const PlatformWebViewControllerCreationParams();
    }

    final WebViewController controller = WebViewController.fromPlatformCreationParams(params);

    // 注册JavaScript通道
    await _registerJavaScriptChannels(controller);

    // 配置WebView
    controller
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setBackgroundColor(Colors.white)
      ..setNavigationDelegate(
        NavigationDelegate(
          onNavigationRequest: (NavigationRequest request) async {
            // 处理特殊协议跳转
            if (request.url.startsWith('weixin://') || request.url.startsWith('yupao://')) {
              if (await canLaunchUrl(Uri.parse(request.url))) {
                await launchUrl(Uri.parse(request.url), mode: LaunchMode.externalApplication);
              }
              return NavigationDecision.prevent;
            }
            return NavigationDecision.navigate;
          },
          onPageFinished: (String url) {
            // 页面加载完成后设置H5交互功能
            Future.delayed(const Duration(milliseconds: 500), () {
              _setupH5Navigation();
            });
          },
          onWebResourceError: (WebResourceError error) {
            debugPrint('WebView资源错误: ${error.description}');
          },
        ),
      );

    // 平台特殊配置
    if (controller.platform is AndroidWebViewController) {
      AndroidWebViewController.enableDebugging(true);
      (controller.platform as AndroidWebViewController)
          .setMediaPlaybackRequiresUserGesture(false);

      // 设置文件选择处理
      (controller.platform as AndroidWebViewController).setOnShowFileSelector(_onFileSelector);
    }

    _webViewController = controller;

    // 加载URL
    _loadFeedbackUrl();

    // 标记WebView已初始化
    setState(() {
      _isWebViewInitialized = true;
    });
  }

  /// 注册JavaScript通道，用于H5与native交互
  Future<void> _registerJavaScriptChannels(WebViewController controller) async {
    try {
      await controller.addJavaScriptChannel(
        'ypNative',
        onMessageReceived: (JavaScriptMessage message) {
          try {
            final data = jsonDecode(message.message);
            final method = data['method'] as String?;

            switch (method) {
              case 'back':
                _handleBack();
                break;
              case 'contentCallback':
                _handleContentCallback(data);
                break;
              case 'saveQrCode':
                _handleSaveQrCode(data);
                break;
              case 'feedbackSuccess':
                _handleFeedbackSuccess();
                break;
              default:
                debugPrint('未知的H5调用方法: $method');
            }
          } catch (e) {
            debugPrint('处理H5消息失败: $e');
          }
        },
      );
    } catch (e) {
      debugPrint('注册JavaScript通道失败: $e');
    }
  }

  /// 处理来自H5的反馈成功事件
  void _handleFeedbackSuccess() {
    debugPrint('H5反馈成功，将关闭页面。');
    if (mounted && Navigator.canPop(context)) {
      Navigator.of(context).pop();
    }
  }

  /// 设置H5页面导航功能，拦截返回按钮点击
  void _setupH5Navigation() async {
    if (_webViewController == null) return;

    try {
      final jsCode = '''
        (function() {
          try {
            // 保存原始的history.back方法，避免无限循环
            var originalHistoryBack = window.history.back.bind(window.history);
            
            // 防抖机制，防止频繁调用
            var lastBackCall = 0;
            var backDebounceMs = 500;
            
            // 设置全局的返回处理函数
            window.handleNativeBack = function() {
              try {
                var now = Date.now();
                if (now - lastBackCall < backDebounceMs) {
                  return;
                }
                lastBackCall = now;
                
                if (window.ypNative && window.ypNative.postMessage) {
                  window.ypNative.postMessage(JSON.stringify({method: 'back'}));
                } else {
                  originalHistoryBack();
                }
              } catch (e) {
                originalHistoryBack();
              }
            };
            
            // 查找返回按钮并绑定事件
            var selectors = [
              '.back-btn', '.nav-back', '.header-back', '.navbar-back', '.top-back',
              '.back', '.return', '.goback', '.go-back', '.btn-back',
              '[data-role="back"]', '[role="back"]', '[data-action="back"]',
              '.icon-back', '.icon-arrow-left', '.fa-arrow-left', '.arrow-left',
              'button[onclick*="back"]', 'a[onclick*="back"]',
              'button[onclick*="history.back"]', 'a[onclick*="history.back"]'
            ];
            
            var totalButtons = 0;
            selectors.forEach(function(selector) {
              try {
                var buttons = document.querySelectorAll(selector);
                buttons.forEach(function(btn) {
                  btn.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    window.handleNativeBack();
                  }, true);
                });
                totalButtons += buttons.length;
              } catch (e) {
                // 忽略选择器错误
              }
            });
            
            // 如果没找到返回按钮，使用通用拦截
            if (totalButtons === 0) {
              // 拦截所有可能的返回操作
              document.addEventListener('click', function(e) {
                var target = e.target;
                var text = (target.textContent || target.innerText || '').trim().toLowerCase();
                var className = (target.className || '').toLowerCase();
                
                if (text.includes('返回') || text.includes('back') || className.includes('back')) {
                  e.preventDefault();
                  e.stopPropagation();
                  window.handleNativeBack();
                }
              }, true);
              
              // 监听浏览器返回事件
              window.addEventListener('popstate', function(e) {
                window.handleNativeBack();
              });
              
              // 重写history.back方法
              window.history.back = function() {
                window.handleNativeBack();
              };
            }
          } catch (e) {
            // 静默失败
          }
        })();
      ''';

      await _webViewController!.runJavaScript(jsCode);
    } catch (e) {
      debugPrint('设置H5导航功能失败: $e');
    }
  }

  /// 统一的返回处理逻辑，由 H5 和系统返回手势共同调用
  void _performBackAction() async {
    // 锁定以防止并发操作
    if (_isHandlingBack) {
      return;
    }
    _isHandlingBack = true;

    try {
      final String? currentUrl = await _webViewController?.currentUrl();

      // 检查当前URL是否为初始URL（忽略查询参数）
      final isAtInitialPage = (_initialUrl != null && currentUrl != null)
          ? Uri.parse(currentUrl).path == Uri.parse(_initialUrl!).path
          : false;

      // 如果当前就在初始页面，直接关闭
      if (isAtInitialPage) {
        if (mounted && Navigator.canPop(context)) {
          Navigator.of(context).pop();
        }
        return;
      }

      // 如果不在初始页面，尝试在WebView内部返回
      final canGoBack = await _webViewController?.canGoBack() ?? false;
      if (canGoBack) {
        debugPrint('WebView可以在内部返回。');
        await _webViewController!.goBack();
      } else {
        // 如果WebView无法后退，作为保险措施，直接关闭
        debugPrint('WebView无法后退，将关闭页面。');
        if (mounted && Navigator.canPop(context)) {
          Navigator.of(context).pop();
        }
      }
    } catch (e) {
      debugPrint('处理返回事件时发生错误: $e');
      // 发生错误时，为安全起见，尝试关闭页面
      if (mounted && Navigator.canPop(context)) {
        Navigator.of(context).pop();
      }
    } finally {
      // 稍作延迟后释放锁定，以确保UI操作完成
      Future.delayed(const Duration(milliseconds: 300), () {
        _isHandlingBack = false;
      });
    }
  }

  /// 处理来自H5的返回事件
  void _handleBack() {
    _performBackAction();
  }

  /// 处理来自系统（如手势）的返回事件
  Future<bool> _handleWillPop() async {
    _performBackAction();
    // 始终返回false，因为我们已经手动处理了所有导航逻辑，
    // 防止系统执行默认的pop操作，从而避免冲突
    return false;
  }

  /// 处理H5内容回调
  void _handleContentCallback(Map<String, dynamic> data) {
    _classStr = data['classification'] ?? "";
    _contentStr = data['content'] ?? "";
  }

  /// 处理保存二维码
  void _handleSaveQrCode(Map<String, dynamic> data) async {
    final url = data['url'] as String?;
    if (url == null || url.isEmpty) return;

    // 检查存储权限
    bool hasPermission = await _checkStoragePermission();
    if (!hasPermission) return;

    // 下载并保存图片
    await _saveImageFromUrl(url);
  }

  /// 检查存储权限
  Future<bool> _checkStoragePermission() async {
    if (Platform.isAndroid) {
      final status = await Permission.photos.request();
      if (!status.isGranted) {
        ToastUtil.showToast('需要存储权限才能保存图片');
        return false;
      }
    } else if (Platform.isIOS) {
      final status = await Permission.photos.request();
      if (!status.isGranted) {
        ToastUtil.showToast('需要相册权限才能保存图片');
        return false;
      }
    }
    return true;
  }

  /// 从URL下载并保存图片到相册
  Future<void> _saveImageFromUrl(String url) async {
    try {
      final dio = Dio();
      final response = await dio.get<Uint8List>(
        url,
        options: Options(responseType: ResponseType.bytes),
      );

      if (response.statusCode == 200 && response.data != null) {
        final success = await PhotoPickerUtil.saveImageToGallery(
          response.data!,
          fileName: '_jgjz_kf_${DateTime.now().millisecondsSinceEpoch}.jpg',
        );

        if (success) {
          ToastUtil.showToast('已保存至系统相册');
        } else {
          ToastUtil.showToast('保存失败');
        }
      } else {
        ToastUtil.showToast('下载图片失败');
      }
    } catch (e) {
      debugPrint('保存图片失败: $e');
      ToastUtil.showToast('保存失败');
    }
  }

  /// 文件选择处理（用于H5上传图片）
  Future<List<String>> _onFileSelector(FileSelectorParams params) async {
    try {
      // 检查是否为图片选择
      final acceptTypes = params.acceptTypes;
      if (acceptTypes.isNotEmpty && acceptTypes.first == 'image/*') {
        return await _selectImageWithProperFormat();
      }
    } catch (e) {
      debugPrint('文件选择失败: $e');
      ToastUtil.showToast('选择图片失败');
    }
    return [];
  }

  /// 选择图片并返回正确格式的URI
  Future<List<String>> _selectImageWithProperFormat() async {
    try {
      // 检查权限
      final hasPermission = await PhotoPickerUtil.checkStoragePermission();
      if (!hasPermission) {
        ToastUtil.showToast('需要相册权限才能选择图片');
        return [];
      }

      // 使用图片选择器
      final List<AssetEntity>? result = await AssetPicker.pickAssets(
        context,
        pickerConfig: AssetPickerConfig(
          maxAssets: 1,
          requestType: RequestType.image,
          themeColor: const Color(0xFF3986eb),
          textDelegate: const AssetPickerTextDelegate(),
        ),
      );

      if (result == null || result.isEmpty) {
        return [];
      }

      // 获取文件并转换为正确的URI格式
      final file = await result.first.file;
      if (file != null && await file.exists()) {
        final filePath = file.path;

        // 确保返回正确的文件URI格式
        String fileUri;
        if (Platform.isAndroid) {
          fileUri = filePath.startsWith('file://') ? filePath : 'file://$filePath';
        } else {
          fileUri = filePath;
        }

        return [fileUri];
      } else {
        return [];
      }

    } catch (e) {
      debugPrint('选择图片过程出错: $e');
      ToastUtil.showToast('选择图片失败');
      return [];
    }
  }

  /// 构建并加载反馈URL
  void _loadFeedbackUrl() async {
    if (_pageParam == null || _webViewController == null) return;

    final baseUrl = FeedbackConfigUtil.getMHyWebUrl();
    final typePath = _pageParam!.type == FeedbackPageEntity.typeFeedback ? 'feedback/add' : 'feedback/history';

    // 构建参数
    final params = await _buildUrlParams();
    final finalUrl = '$baseUrl$typePath$params';

    debugPrint('=== 反馈页面加载信息 ===');
    debugPrint('页面类型: ${_pageParam!.type}');
    debugPrint('基础URL: $baseUrl');
    debugPrint('类型路径: $typePath');
    debugPrint('参数: $params');
    debugPrint('最终URL: $finalUrl');
    debugPrint('===================');

    // 记录初始URL
    _initialUrl = finalUrl;

    // 加载URL
    await _webViewController!.loadRequest(Uri.parse(finalUrl));
  }

  /// 构建URL参数
  Future<String> _buildUrlParams() async {
    if (_pageParam == null) return '';

    final deviceInfo = await FeedbackConfigUtil.getDeviceInfo();
    /// todo:LINer参数待完善
    String params = '?configId=${_pageParam!.configId}'
        '&appId=${_pageParam!.appId}'
        '&resource=${FeedbackConfigUtil.getSource()}'
        '&tenatId='
        '&session=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpYXQiOjE3NTI1NDUwMDYsImV4cCI6MTc2MjkxMzAwNiwiZGF0YSI6eyJzaW5nbGUiOiIxMUJDS1NTVElTQ0hVWlZBIiwidWlkIjoyNTEzNzk5OSwiYnVzaW5lc3MiOiIyIiwic3lzdGVtX3R5cGUiOiJhbmRyb2lkIiwibWluaV90b2tlbiI6IjExQkNLU1NUSVNDSFVaVkEiLCJpZCI6MjUxMzc5OTksInV1aWQiOjI1MTM3OTk5fSwidG9rZW4iOnsicmVnUnQiOiJhbmRyb2lkIiwidGVuYW50S2V5IjoiSkdKWiIsInRlbmFudElkIjoyNTEzNzk5OSwicGFja2FnZU5hbWUiOiJjb20ueXVwYW8uZ29uZ2Rpamlnb25nIiwidXNlcklkIjoyNTEzNzk5OSwidG9rZW4iOiIxMUJDS1NTVElTQ0hVWlZBIn19.v3UhYw6PRD_GvJ5lxc6-_l-xgZB3ua3bAqSalBcxeBM'
        '&source=0'
        '&uid=25137999'
        '&userrole=null'
        'headers=%7B%22appVersion%22%3A%226.7.0%22%2C%22business%22%3A%22JGJZ%22%2C%22channel%22%3A%22authority%22%2C%22hybird%22%3A%22H5%22%2C%22os%22%3A%22ANDROID%22%2C%22osversion%22%3A%2212%22%2C%22packagename%22%3A%22com.yupao.gongdijigong%22%2C%22packageversion%22%3A%226.7.0%22%2C%22platform%22%3A%22android%22%2C%22runtime%22%3A%22ANDROID%22%2C%22runtimeversion%22%3A%2212%22%2C%22system%22%3A%22ANDROID%22%2C%22systemVersion%22%3A%2212%22%7D';

    // 添加问题类型参数
    if (_pageParam!.questionType != null && _pageParam!.questionType!.isNotEmpty) {
      params += '&classificationId=${_pageParam!.questionType}';
    }

    return params;
  }

  @override
  Widget yBuild(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        // 处理手势返回和系统返回键
        if (_webViewController != null) {
          return await _handleWillPop();
        }
        return true; // WebView未初始化时允许直接返回
      },
      child: Scaffold(
        backgroundColor: Colors.white,
        body: SafeArea(
          // 确保页面内容不与状态栏重叠
          child: Container(
            width: MediaQuery.of(context).size.width,
            height: double.infinity,
            color: Colors.white,
            child: Stack(
              children: [
                _buildWebView()
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildWebView() {
    if (WebViewPlatform.instance is AndroidWebViewPlatform) {
      return WebViewWidget.fromPlatformCreationParams(
        params: AndroidWebViewWidgetCreationParams(
          controller: _webViewController!.platform,
          displayWithHybridComposition: true,
        ),
      );
    }
    return WebViewWidget(controller: _webViewController!);
  }

  @override
  void dispose() {
    // 重置锁定状态
    _isHandlingBack = false;

    // 清理WebView控制器
    _webViewController = null;
    _isWebViewInitialized = false;

    // 清理反馈内容状态
    _contentStr = "";
    _classStr = "";

    super.dispose();
  }
}
