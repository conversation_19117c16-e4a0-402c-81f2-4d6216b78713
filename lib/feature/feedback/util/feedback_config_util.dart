import 'dart:io';
import 'package:gdjg_pure_flutter/utils/net_util/net_env.dart';
import 'package:gdjg_pure_flutter/data/account/repo/auth_repo.dart';
import 'package:device_info_plus/device_info_plus.dart';

/// 应用配置工具类
class FeedbackConfigUtil {
  FeedbackConfigUtil._();
  
  static final NetEnv _netEnv = NetEnv();
  static final AuthRepo _authRepo = AuthRepo();
  
  /// 获取H5混合网页地址
  static String getMHyWebUrl() {
    if (_netEnv.isTestEnv()) {
      return 'https://h5hybridtest.yupaowang.com/';
    } else {
      return 'https://h5hybridprod.yupaowang.com/';
    }
  }
  
  /// 获取资源来源标识
  static String getSource() {
    // 根据包名判断业务类型
    if (Platform.isAndroid) {
      return "agd"; // 工地记工Android
    } else if (Platform.isIOS) {
      return "agd"; // 工地记工iOS
    }
    return "android";
  }
  
  /// 获取用户Token
  static String getUserToken() {
    try {
      final account = _authRepo.getAccount();
      return account.token;
    } catch (e) {
      return "";
    }
  }
  
  /// 获取用户ID
  static String getUserId() {
    try {
      final account = _authRepo.getAccount();
      return account.uid.toString();
    } catch (e) {
      return "";
    }
  }
  
  /// 获取设备信息
  static Future<Map<String, String>> getDeviceInfo() async {
    final deviceInfo = DeviceInfoPlugin();
    Map<String, String> deviceParams = {};
    
    try {
      if (Platform.isAndroid) {
        final androidInfo = await deviceInfo.androidInfo;
        deviceParams['channel'] = 'authority'; // 默认渠道
        deviceParams['packagename'] = 'com.yupao.gongdijigong';
        deviceParams['business'] = 'gdjg';
        deviceParams['packageversion'] = '7.0.0'; // 从pubspec.yaml获取
      } else if (Platform.isIOS) {
        final iosInfo = await deviceInfo.iosInfo;
        deviceParams['channel'] = 'App Store';
        deviceParams['packagename'] = 'com.yupao.gdjgPureFlutter';
        deviceParams['business'] = 'gdjg';
        deviceParams['packageversion'] = '7.0.0';
      }
    } catch (e) {
      // 设备信息获取失败时使用默认值
      deviceParams['channel'] = 'default';
      deviceParams['packagename'] = 'com.yupao.gongdijigong';
      deviceParams['business'] = 'gdjg';
      deviceParams['packageversion'] = '7.0.0';
    }
    
    return deviceParams;
  }
} 