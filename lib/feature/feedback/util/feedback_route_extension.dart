import 'package:gdjg_pure_flutter/feature/feedback/entity/feedback_page_entity.dart';
import 'package:gdjg_pure_flutter/init_module/init_route.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';

/// 反馈页面路由扩展
extension FeedbackRouteExtension on YPRoute {
  
  /// 打开反馈页面
  /// [type] 页面类型，默认为反馈页面
  /// [configId] 配置ID，默认为"2"
  /// [appId] 应用ID，默认为"103"
  /// [questionType] 问题类型，可选
  static Future<Object?>? openFeedbackPage({
    String type = FeedbackPageEntity.typeFeedback,
    String configId = "2",
    String appId = "103",
    String? questionType,
  }) {
    final param = FeedbackPageEntity(
      type: type,
      configId: configId,
      appId: appId,
      questionType: questionType,
    );
    
    return YPRoute.openPage(
      RouteNameCollection.feedback,
      params: param,
    );
  }

  /// 打开历史反馈页面
  /// [configId] 配置ID，默认为"2"
  /// [appId] 应用ID，默认为"103"
  static Future<Object?>? openFeedbackHistoryPage({
    String configId = "2",
    String appId = "103",
  }) {
    return openFeedbackPage(
      type: FeedbackPageEntity.typeHistory,
      configId: configId,
      appId: appId,
    );
  }

  /// 带问题类型的反馈页面（通常用于特定场景的反馈）
  /// [questionType] 问题类型
  /// [configId] 配置ID，默认为"2"
  /// [appId] 应用ID，默认为"103"
  static Future<Object?>? openFeedbackPageWithQuestionType({
    required String questionType,
    String configId = "2",
    String appId = "103",
  }) {
    return openFeedbackPage(
      type: FeedbackPageEntity.typeFeedback,
      configId: configId,
      appId: appId,
      questionType: questionType,
    );
  }
} 