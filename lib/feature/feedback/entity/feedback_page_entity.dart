/// 反馈页面参数类
class FeedbackPageEntity {
  /// 页面类型：TYPE_HISTORY（历史反馈）或 TYPE_FEEDBACK（反馈页面）
  final String type;
  
  /// 配置ID
  final String configId;
  
  /// 应用ID
  final String appId;
  
  /// 问题类型（可选，主要用于工地记工放弃原因弹窗传递）
  final String? questionType;

  const FeedbackPageEntity({
    this.type = FeedbackPageEntity.typeFeedback,
    this.configId = "2",
    this.appId = "103",
    this.questionType,
  });

  /// 历史反馈
  static const String typeHistory = "TYPE_HISTORY";

  /// 反馈页面
  static const String typeFeedback = "TYPE_FEEDBACK";
} 