import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/feature/group/group_pro_statistics/view/total_statistics_list_view.dart';
import 'package:gdjg_pure_flutter/feature/group/group_statistics/view/empty_view.dart';
import 'package:gdjg_pure_flutter/feature/worker/worker_statistics/view/pro_statistics_list_view.dart';
import 'package:gdjg_pure_flutter/feature/worker/worker_statistics/vm/worker_statistics_viewmodel.dart';
import 'package:gdjg_pure_flutter/utils/route_util/base_page/base_ful_page.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';
import 'package:gdjg_pure_flutter/widget/combined_filter_widget.dart';
import 'package:gdjg_pure_flutter/widget/round_tab_indicator_widget.dart';
import 'package:get/get.dart';

/// 工人统计页面
class WorkerStatisticsPage extends BaseFulPage {
  const WorkerStatisticsPage({super.key}) : super(appBar: null);

  @override
  createState() => _WorkerStatisticsPage();
}

class _WorkerStatisticsPage<WorkerStatisticsPage> extends BaseFulPageState
    with SingleTickerProviderStateMixin {
  final _viewModel = StatisticsViewModel();
  TabController? _tabController;

  @override
  void onPageCreate() {
    super.onPageCreate();
    _tabController = TabController(length: 2, vsync: this);
    _viewModel.fetchData();
  }

  @override
  void onPageDestroy() {
    super.onPageDestroy();
    _tabController?.dispose();
  }

  @override
  Widget yBuild(BuildContext context) {
    // 进行事件处理
    // handleGroupFlowDetailVMEvent(props.vm)
    return Column(
      children: [
        Divider(height: 8.h, color: Color(0xFFF0F0F0), thickness: 8.h),
        Obx(
          () => CombinedFilterWidget(
            showWorkerFilter: false,
            showProjectFilter: true,
            initialStartDate: _viewModel.us.startTime,
            initialEndDate: _viewModel.us.endTime,
            initialRecordTypes: _viewModel.us.selectedTypes,
            onSelectAll: () {
              _viewModel.onSelectAllTap();
            },
            onFilterChanged: (filter) {
              _viewModel.onUpdateDateTimeTap(
                  filter.startDate, filter.endDate, filter.selectedTypes);
            },
          ),
        ),
        Divider(height: 8.h, color: Color(0xFFF0F0F0), thickness: 8.h),
        _builderTabView()
      ],
    );
  }

  Widget _builderTabView() {
    return Flexible(
      child: Column(
        children: [
          Container(
            color: Colors.white,
            height: 44,
            child: TabBar(
                controller: _tabController,
                labelColor: Color(0xFF8A8A99),
                unselectedLabelColor: Color(0xFF8A8A99),
                labelStyle: const TextStyle(
                  fontSize: 18,
                ),
                // unselectedLabelStyle: const TextStyle(fontSize: 16),
                indicator: RoundUnderlineTabIndicator(
                  borderSide:
                      BorderSide(width: 3, color: ColorsUtil.primaryColor),
                ),
                dividerHeight: 1,
                dividerColor: Color(0xFFE6E6E6),
                tabs: [
                  Tab(
                    child: Text('项目'),
                  ),
                  Tab(
                    child: Text('总计'),
                  ),
                ]),
          ),
          Flexible(
            flex: 1,
            child: Container(
              color: Colors.white,
              child: TabBarView(
                  controller: _tabController,
                  // physics: NeverScrollableScrollPhysics(),
                  children: [
                    _buildProStatisticsList(),
                    _buildProStatisticsTotal(),
                  ]),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建项目统计列表
  Widget _buildProStatisticsList() {
    return Obx(() {
      if (_viewModel.us.isProStatisticsEmpty) {
        return const EmptyView(
          subtitle: '暂无数据',
          imagePath: 'https://cdn.yupaowang.com/jgjz/empty-note.png',
        );
      }
      return ProStatisticsListView(
        list: _viewModel.us.proStatisticsList,
        onItemTap: (item) {
          // _viewModel.onJumpToProBillTap(item: item);
        },
      );
    });
  }

  /// 构建项目总计
  Widget _buildProStatisticsTotal() {
    return Obx(() {
      if (_viewModel.us.isTotalStatisticsEmpty) {
        return EmptyView(
          subtitle: '暂无数据',
          imagePath: 'https://cdn.yupaowang.com/jgjz/empty-note.png',
        );
      }
      return TotalStatisticsListView(
        refreshController: _viewModel.refreshController,
        onRefresh: _viewModel.fetchData,
        list: _viewModel.us.totalStatisticsList,
        onItemTap: (item) {
          // _viewModel.onJumpToProBillTap();
        },
      );
    });
  }
}
