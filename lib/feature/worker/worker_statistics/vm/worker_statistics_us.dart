
import 'package:gdjg_pure_flutter/feature/group/common_uistate/statistics_ui_state.dart';
import 'package:gdjg_pure_flutter/feature/worker/worker_statistics/vm/worker_statistics_ui_state.dart';
import 'package:gdjg_pure_flutter/model/RecordType.dart';
import 'package:get/get.dart';

class WorkerStatisticsUS {
  final _totalStatisticsList = <StatisticsItemUIState>[].obs;
  final _isTotalStatisticsEmpty = false.obs;

  final _proStatisticsList = <ProStatisticsUIState>[].obs;
  final _isProStatisticsEmpty = false.obs;

  final _startTime = DateTime.now().obs;

  final _endTime = DateTime.now().obs;

  final _rwaRecordTypeList = <RwaRecordType>[].obs;

  get startTime => _startTime.value;

  get endTime => _endTime.value;

  get selectedTypes => _rwaRecordTypeList.value;

  bool get isTotalStatisticsEmpty => _isTotalStatisticsEmpty.value;

  List<StatisticsItemUIState> get totalStatisticsList => _totalStatisticsList;

  bool get isProStatisticsEmpty => _isProStatisticsEmpty.value;

  List<ProStatisticsUIState> get proStatisticsList =>
      _proStatisticsList;

  setTotalStatisticsList(List<StatisticsItemUIState> list) {
    _totalStatisticsList.value = list;
    _isTotalStatisticsEmpty.value = list.isEmpty;
    _totalStatisticsList.refresh();
    _isTotalStatisticsEmpty.refresh();
  }

  setProStatisticsList(List<ProStatisticsUIState> list) {
    _proStatisticsList.value = list;
    _isProStatisticsEmpty.value = list.isEmpty;
    _proStatisticsList.refresh();
    _isProStatisticsEmpty.refresh();
  }

  setStartTime(DateTime? date) {
    if (date == null) {
      return;
    }
    _startTime.value = date;
    _startTime.refresh();
  }

  setEndTime(DateTime? date) {
    if (date == null) {
      return;
    }
    _endTime.value = date;
    _endTime.refresh();
  }
}