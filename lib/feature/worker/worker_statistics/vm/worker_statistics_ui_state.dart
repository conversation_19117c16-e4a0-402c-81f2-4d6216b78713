import 'package:gdjg_pure_flutter/feature/group/common_uistate/statistics_ui_state.dart';

/// @date 2025/06/21
class ProStatisticsUIState {
  ///项目名字
  String workNoteName;

  /// 项目id
  double? workNoteId;

  bool isCreateByMySelf;

  /// 统计项
  List<StatisticsItemUIState>? statisticsItemList;

  ProStatisticsUIState({
    this.workNoteName = '',
    this.workNoteId,
    this.isCreateByMySelf = true,
    this.statisticsItemList,
  });

  @override
  String toString() {
    return 'WorkerStatisticsUIState{workNoteName: $workNoteName, workNoteId: $workNoteId, statisticsItemList: $statisticsItemList}';
  }
}
