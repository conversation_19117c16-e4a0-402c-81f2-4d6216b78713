import 'package:gdjg_pure_flutter/feature/group/common_uistate/statistice_helper.dart';
import 'package:gdjg_pure_flutter/feature/group/common_uistate/statistics_ui_state.dart';
import 'package:gdjg_pure_flutter/data/common_data/biz/count_biz_model.dart';
import 'package:gdjg_pure_flutter/data/note_time_data/ds/param/project_get_first_business_time_param_model.dart';
import 'package:gdjg_pure_flutter/data/note_time_data/note_time_repo.dart';
import 'package:gdjg_pure_flutter/data/worker_data/worker_statistics/ds/param/business_get_project_count_param_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/worker_statistics/repo/model/business_get_project_count_biz_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/worker_statistics/repo/worker_statistics_repo.dart';
import 'package:gdjg_pure_flutter/feature/worker/worker_statistics/vm/worker_statistics_ui_state.dart';
import 'package:gdjg_pure_flutter/feature/worker/worker_statistics/vm/worker_statistics_us.dart';
import 'package:gdjg_pure_flutter/model/RecordNoteType.dart';
import 'package:gdjg_pure_flutter/model/RecordType.dart';
import 'package:gdjg_pure_flutter/utils/system_util/date_util.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

class StatisticsViewModel {
  final _workerStatisticsRepo = WorkerStatisticsRepo();
  final _noteTimeRepo = NoteTimeRepo();
  final RefreshController refreshController =
      RefreshController(initialRefresh: false);
  var us = WorkerStatisticsUS();
  DateTime? _netStartTime;
  DateTime _startTime = DateTime(2021, 01, 01);
  DateTime _endTime = DateTime.now();
  List<RwaRecordType> _businessType = [];
  final List<String> _projectIds = [];

  bool _isCreateByMySelf() {
    return (_projectIds.firstOrNull ?? 2) == RecordNoteType.personal.value;
  }

  fetchData() async {
    try {
      var params = BusinessGetProjectCountParamModel(
        identity: RecordNoteType.personal.value.toString(),
        start_time: DateUtil.formatStartDate(_startTime),
        end_time: DateUtil.formatEndDate(_endTime),
        business_type:
            _businessType.map((type) => type.code.value.toString()).join(','),
        work_notes: _projectIds.join(','),
      );
      var result =
          await _workerStatisticsRepo.getBusinessGetProjectCount(params);
      if (result.isOK()) {
        convertEntityToUIState(result.getSucData());
      }
      refreshController.refreshCompleted();
    } catch (e) {
      refreshController.refreshFailed();
    }
  }

  ///获取开始时间和结束时间
  Future<void> fetchFirstTime() async {
    DateTime endTime = DateTime.now();
    var params = ProjectGetFirstBusinessTimeParamModel(
      identity: RecordNoteType.personal.value.toString(),
    );
    var resultTime = await _noteTimeRepo.getWorkerFirstBusinessTime(params);
    if (resultTime.isOK()) {
      var time = resultTime.getSucData()?.startTime ?? "2020-01-01";
      var startTime = DateTime.parse(time);
      _startTime = startTime;
      _netStartTime = startTime;
      _endTime = endTime;
      us.setStartTime(startTime);
      us.setEndTime(endTime);
    }
  }

  /// 将实体数据转换为UIState
  void convertEntityToUIState(BusinessGetProjectCountBizModel? data) {
    if (data == null) return;
    _convertWorkerStatistics(data.project);
    _convertTotalStatistics(data.all);
  }

  void _convertWorkerStatistics(List<CountBizModel>? workers) {
    List<ProStatisticsUIState> workerStatisticsList = [];
    workers?.forEach((element) {
      workerStatisticsList.add(ProStatisticsUIState(
          workNoteName: element.name,
          workNoteId: element.id,
          isCreateByMySelf: _isCreateByMySelf(),
          statisticsItemList: StatisticsUIStateHelper.buildStatisticsItem(
              element,
              isShowDailyWages: false)));
    });
    us.setProStatisticsList(workerStatisticsList);
  }

  void _convertTotalStatistics(CountBizModel? total) {
    List<StatisticsItemUIState> totalStatisticsList = [];
    if (total != null) {
      totalStatisticsList = StatisticsUIStateHelper.buildStatisticsItem(total,
          isShowDailyWages: false);
    }
    us.setTotalStatisticsList(totalStatisticsList);
  }

  ///更新开始和结束时间
  void onUpdateDateTimeTap(
      DateTime startTime, DateTime endTime, List<RwaRecordType>? businessType) {
    _startTime = startTime;
    _endTime = endTime;
    _businessType = businessType ?? [];
    fetchData();
  }

  onSelectAllTap() async {
    if (_netStartTime == null) {
      await fetchFirstTime();
    } else {
      DateTime endTime = DateTime.now();
      _startTime = _netStartTime ?? DateTime(2020, 01, 01);
      _endTime = endTime;
      us.setStartTime(_netStartTime);
      us.setEndTime(endTime);
    }
    fetchData();
  }
}
