import 'package:flutter/material.dart';
import 'package:gdjg_pure_flutter/feature/tabbar/view/role_switch_bar_view.dart';
import 'package:gdjg_pure_flutter/feature/worker/worker_project/worker_project_page.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';
import 'package:get/get.dart';

import '../us/worker_us.dart';
import '../worker_calendar/page/worker_calendar_page.dart';
import '../worker_flow/worker_flow_page.dart';
import '../worker_mine/page/worker_mine_page.dart';
import '../worker_statistics/page/workerstatistics_page.dart';

class WorkerPage extends GetView<WorkerUS> {
  const WorkerPage({super.key});

  @override
  Widget build(BuildContext context) {
    final List<Widget> pages = [
      WorkerFlowPage(),
      const WorkerCalendarPage(),
      const WorkerProjectPage(),
      const WorkerStatisticsPage(),
      const WorkerProfilePage(),
    ];

    var titleBarText = [
      "批量删除",
      "工人顶部资源位",
      "工人顶部资源位",
      "工人顶部资源位",
      "工人顶部资源位",
    ];
    var titleBarIconAssets = [
      "",
      "assets/images/tabbar/icon_tabbar_calendar_normal.webp",
      "assets/images/tabbar/icon_tabbar_calendar_normal.webp",
      "assets/images/tabbar/icon_tabbar_calendar_normal.webp",
      "assets/images/tabbar/icon_tabbar_calendar_normal.webp",
    ];
    var titleBarClicks = [
      _onDeleteRecordClick,
      () {},
      () {},
      () {},
      () {},
    ];

    return Scaffold(
      // extendBodyBehindAppBar: true, // 有显示问题
      backgroundColor: Colors.white,
      appBar: AppBar(
        titleSpacing: 0, // 移除title的左右边距
        title: RoleSwitchBar(
          rightText: titleBarText[controller.currentIndex.value],
          rightIconAsset: titleBarIconAssets[controller.currentIndex.value],
          backgroundColor: Colors.white,
          onRightTap: titleBarClicks[controller.currentIndex.value],
        ),
        centerTitle: false,
        backgroundColor: Colors.white,
        elevation: 0,
      ),
      body: Obx(() => IndexedStack(
            index: controller.currentIndex.value,
            children: pages,
          )),
      bottomNavigationBar: Container(
        decoration: BoxDecoration(
            color: Colors.white,
            border: Border(
              top: BorderSide(
                color: Color(0x80CCCCCC),
                width: 0.5,
              ),
            )),
        child: Obx(
          () => BottomNavigationBar(
            backgroundColor: Colors.white,
            type: BottomNavigationBarType.fixed,
            currentIndex: controller.currentIndex.value,
            onTap: controller.changeTabIndex,
            selectedItemColor: ColorsUtil.primaryColor,
            unselectedItemColor: Color(0xFF5A5A66),
            selectedFontSize: 14,
            unselectedFontSize: 14,
            iconSize: 0,
            items: [
              BottomNavigationBarItem(
                label: '流水',
                icon: Container(
                  child: Padding(
                      padding: const EdgeInsets.only(bottom: 4, top: 0),
                      child: Image(
                        fit: BoxFit.fill,
                        width: 26,
                        height: 26,
                        image: controller.currentIndex.value == 0
                            ? const AssetImage(
                                'assets/images/tabbar/icon_tabbar_flow_selected.webp')
                            : const AssetImage('assets/images/tabbar/icon_tabbar_flow_nomal.webp'),
                      )),
                ),
              ),
              BottomNavigationBarItem(
                label: '日历',
                icon: Padding(
                    padding: const EdgeInsets.only(bottom: 4),
                    child: Image(
                      fit: BoxFit.fill,
                      width: 26,
                      height: 26,
                      image: controller.currentIndex.value == 1
                          ? const AssetImage(
                              'assets/images/tabbar/icon_tabbar_calendar_selected.webp')
                          : const AssetImage(
                              'assets/images/tabbar/icon_tabbar_calendar_normal.webp'),
                    )),
              ),
              BottomNavigationBarItem(
                label: '项目',
                icon: Padding(
                    padding: const EdgeInsets.only(bottom: 4),
                    child: Image(
                      fit: BoxFit.fill,
                      width: 26,
                      height: 26,
                      image: controller.currentIndex.value == 2
                          ? const AssetImage(
                              'assets/images/tabbar/icon_tabbar_project_selected.webp')
                          : const AssetImage('assets/images/tabbar/icon_tabbar_project_nomal.webp'),
                    )),
              ),
              BottomNavigationBarItem(
                label: '统计',
                icon: Padding(
                    padding: const EdgeInsets.only(bottom: 4),
                    child: Image(
                      fit: BoxFit.fill,
                      width: 26,
                      height: 26,
                      image: controller.currentIndex.value == 3
                          ? const AssetImage(
                              'assets/images/tabbar/icon_tabbar_statistic_selected.webp')
                          : const AssetImage(
                              'assets/images/tabbar/icon_tabbar_statistic_normal.webp'),
                    )),
              ),
              BottomNavigationBarItem(
                label: '我的',
                icon: Padding(
                    padding: const EdgeInsets.only(bottom: 4),
                    child: Image(
                      fit: BoxFit.fill,
                      width: 26,
                      height: 26,
                      image: controller.currentIndex.value == 4
                          ? const AssetImage('assets/images/tabbar/icon_tabbar_mine_selected.webp')
                          : const AssetImage('assets/images/tabbar/icon_tabbar_mine_normal.webp'),
                    )),
              ),
            ],
          ),
        ),
      ),
    );
  }

  _onDeleteRecordClick() {
    // YPRoute.openPage("/delete_record");
  }
}
