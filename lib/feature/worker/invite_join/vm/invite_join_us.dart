import 'package:get/get.dart';
import 'package:gdjg_pure_flutter/data/worker_data/invite_data/repo/model/invite_info_biz_model.dart';

/// 邀请加入页面UI状态管理
class InviteJoinUS {
  /// 邀请信息
  final _inviteInfo = Rxn<InviteInfoBizModel>();

  /// 邀请token
  final _token = ''.obs;

  // Getters
  Rxn<InviteInfoBizModel> get inviteInfo => _inviteInfo;
  RxString get token => _token;

  /// 设置邀请信息
  void setInviteInfo(InviteInfoBizModel? info) {
    _inviteInfo.value = info;
  }

  /// 设置token
  void setToken(String token) {
    _token.value = token;
  }
}
