import 'package:gdjg_pure_flutter/data/worker_data/invite_data/ds/model/param/invite_accept_param_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/invite_data/repo/invite_repo.dart';
import 'package:gdjg_pure_flutter/data/worker_data/invite_data/repo/model/invite_info_biz_model.dart';
import 'package:gdjg_pure_flutter/feature/worker/invite_join/vm/invite_join_us.dart';
import 'package:gdjg_pure_flutter/init_module/init_route.dart';
import 'package:gdjg_pure_flutter/utils/event_bus_util/event_bus_util.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/toast_util.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';

/// 邀请加入页面参数
class InviteJoinPageParam {
  /// 邀请信息
  final InviteInfoBizModel inviteInfo;

  /// 邀请token
  final String token;

  InviteJoinPageParam({
    required this.inviteInfo,
    required this.token,
  });
}

/// 邀请姓名填写页面参数
class InviteNamePageParam {
  /// 部门名称
  final String deptName;

  /// 邀请token
  final String token;

  InviteNamePageParam({
    required this.deptName,
    required this.token,
  });
}

/// 邀请加入业务逻辑层
class InviteJoinVM {
  final InviteJoinUS us = InviteJoinUS();
  final _inviteRepo = InviteRepo();

  void init(InviteJoinPageParam param) {
    us.setInviteInfo(param.inviteInfo);
    us.setToken(param.token);
  }

  void dispose() {
  }

  /// 接受邀请
  Future<void> acceptInvite() async {
    final param = InviteAcceptParamModel(
      token: us.token.value,
      name: '',
    );

    // 调用接受邀请接口
    final result = await _inviteRepo.acceptInvite(param);

    if (result.isOK()) {
      ToastUtil.showToast('加入成功');
      // 发送邀请加入成功事件，刷新"我参与"页面
      EventBusUtil.emit<String>('invite_success');
      // 关闭当前页面，由于之前的页面已经被关闭，会直接返回到"我参与"页面
      YPRoute.closePage(true);
    } else {
      final errorCode = result.fail?.code;
      final errorMsg = result.fail?.errorMsg;

      // 如果code为16，需要填写姓名，跳转到姓名填写页面
      if (errorCode == 16) {
        final deptName = us.inviteInfo.value?.deptName ?? '';
        final token = us.token.value;

        YPRoute.openPage(RouteNameCollection.inviteName, params: {
          'deptName': deptName,
          'token': token,
        }, closeNumBeforeOpen: 1);
      } else {
        ToastUtil.showToast(errorMsg ?? '加入失败，请重试');
      }
    }
  }
}
