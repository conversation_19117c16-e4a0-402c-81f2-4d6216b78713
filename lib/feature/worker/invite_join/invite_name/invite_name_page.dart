import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/feature/worker/invite_join/invite_name/vm/invite_name_vm.dart';
import 'package:gdjg_pure_flutter/utils/route_util/base_page/base_ful_page.dart';
import 'package:get/get.dart';

/// 邀请填写姓名页面
class InviteNamePage extends BaseFulPage {
  const InviteNamePage({super.key})
      : super(appBar: const YPAppBar(title: '邀请加入'), canBack: false);

  @override
  State createState() => _InviteNamePageState();
}

class _InviteNamePageState extends BaseFulPageState<InviteNamePage> {
  late final InviteNameVM viewModel;
  final TextEditingController _nameController = TextEditingController();

  @override
  void onPageCreate() {
    super.onPageCreate();
    viewModel = InviteNameVM();
  }

  @override
  void onPageRoute(Object? routeParams, bool fromLaunchTask) {
    super.onPageRoute(routeParams, fromLaunchTask);
    if (routeParams != null && routeParams is Map<String, dynamic>) {
      final deptName = routeParams['deptName'] as String? ?? '';
      final token = routeParams['token'] as String? ?? '';
      viewModel.initWithParams(deptName, token);
    }
  }

  @override
  void onPageDestroy() {
    _nameController.dispose();
    viewModel.dispose();
    super.onPageDestroy();
  }

  @override
  Widget yBuild(BuildContext context) {
    return Obx(() {
      return Scaffold(
        backgroundColor: Color(0xFFF5F6FA),
        body: Stack(
          children: [
            // 背景图
            _buildBackgroundLayer(),
            _buildFormLayer(),
          ],
        ),
      );
    });
  }

  /// 背景图
  Widget _buildBackgroundLayer() {
    return Positioned(
      top: 0,
      left: 0,
      right: 0,
      height: 223.h,
      child: Container(
        decoration: BoxDecoration(
          image: DecorationImage(
            image: AssetImage('assets/images/worker/bg_invite_join.png'),
            fit: BoxFit.cover,
          ),
        ),
        child: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                Colors.black.withValues(alpha: 0.3),
                Colors.black.withValues(alpha: 0.1),
              ],
            ),
          ),
          child: SafeArea(
            child: Padding(
              padding: EdgeInsets.only(top: 80.h),
              child: Column(
                children: [
                  Text(
                    '班组名称：',
                    style: TextStyle(
                      fontSize: 16.sp,
                      color: Colors.white,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                  SizedBox(height: 8.h),
                  Text(
                    viewModel.us.deptName.value,
                    style: TextStyle(
                      fontSize: 24.sp,
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// 表单
  Widget _buildFormLayer() {
    return Positioned(
      top: 203.h,
      left: 0,
      right: 0,
      bottom: 0,
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20.r),
            topRight: Radius.circular(20.r),
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 10,
              offset: Offset(0, -2),
            ),
          ],
        ),
        child: SingleChildScrollView(
          padding: EdgeInsets.only(top: 16.h, bottom: 32.h),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildNameRow(),
              _buildDivider(),

              _buildPhoneRow(),
              SizedBox(height: 40.h),

              Container(
                padding: EdgeInsets.symmetric(horizontal: 24.w),
                child: _buildConfirmButton(),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 姓名
  Widget _buildNameRow() {
    return Container(
      padding: EdgeInsets.only(left: 16.w, right: 16.w, top: 10.h, bottom: 0),
      child: Row(
        children: [
          Text(
            '您的姓名',
            style: TextStyle(
              fontSize: 16.sp,
              color: Colors.black87,
              fontWeight: FontWeight.w400,
            ),
          ),
          SizedBox(width: 24.w),
          Expanded(
            child: TextField(
              controller: _nameController,
              onChanged: viewModel.setUserName,
              decoration: InputDecoration(
                hintText: '请输入您的真实姓名',
                hintStyle: TextStyle(
                  fontSize: 16.sp,
                  color: Colors.grey[400],
                ),
                border: InputBorder.none,
                contentPadding: EdgeInsets.zero,
              ),
              style: TextStyle(
                fontSize: 16.sp,
                color: Colors.black,
              ),
              textAlign: TextAlign.left,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDivider() {
    return Container(
      margin: EdgeInsets.only(left: 16.w, right: 16.w, top: 8.h),
      height: 1.h,
      color: Colors.grey[200],
    );
  }

  /// 手机号
  Widget _buildPhoneRow() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
      child: Row(
        children: [
          Text(
            '电话号码',
            style: TextStyle(
              fontSize: 16.sp,
              color: Colors.black87,
              fontWeight: FontWeight.w400,
            ),
          ),
          SizedBox(width: 24.w),
          Expanded(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  //TODO: 改为存的手机号
                  '13265536789',
                  style: TextStyle(
                    fontSize: 16.sp,
                    color: Colors.black,
                  ),
                ),
                Icon(
                  Icons.lock_outline,
                  size: 20.w,
                  color: Colors.black,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 确认按钮
  Widget _buildConfirmButton() {
    return SizedBox(
      width: double.infinity,
      height: 48.h,
      child: ElevatedButton(
        onPressed: viewModel.acceptInvite,
        style: ElevatedButton.styleFrom(
          backgroundColor: Color(0xFF5290FD),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8.r),
          ),
          elevation: 0,
        ),
        child: Text(
          '确认',
          style: TextStyle(
            fontSize: 16.sp,
            fontWeight: FontWeight.w600,
            color: Colors.white,
          ),
        ),
      ),
    );
  }
}
