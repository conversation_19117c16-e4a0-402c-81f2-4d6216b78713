import 'package:get/get.dart';

/// 邀请姓名填写页面UI状态管理
class InviteNameUS {
  /// 部门名称
  final _deptName = ''.obs;

  /// 邀请token
  final _token = ''.obs;

  /// 用户姓名
  final _userName = ''.obs;

  // Getters
  RxString get deptName => _deptName;
  RxString get token => _token;
  RxString get userName => _userName;

  /// 设置部门名称
  void setDeptName(String name) {
    _deptName.value = name;
  }

  /// 设置token
  void setToken(String token) {
    _token.value = token;
  }

  /// 设置用户姓名
  void setUserName(String name) {
    _userName.value = name;
  }
}
