import 'package:gdjg_pure_flutter/data/worker_data/invite_data/ds/model/param/invite_accept_param_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/invite_data/repo/invite_repo.dart';
import 'package:gdjg_pure_flutter/feature/worker/invite_join/invite_name/vm/invite_name_us.dart';
import 'package:gdjg_pure_flutter/utils/event_bus_util/event_bus_util.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/toast_util.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/utils/regex/regex_utils.dart';

/// 邀请姓名填写业务逻辑层
class InviteNameVM {
  final InviteNameUS us = InviteNameUS();
  final _inviteRepo = InviteRepo();

  void initWithParams(String deptName, String token) {
    us.setDeptName(deptName);
    us.setToken(token);
  }

  void dispose() {
  }

  /// 接受邀请
  Future<void> acceptInvite() async {
    final userName = us.userName.value.trim();

    if (userName.isEmpty) {
      ToastUtil.showToast('请输入您的姓名');
      return;
    }

    // 验证姓名格式
    if (!RegexUtils.isValidName(userName)) {
      ToastUtil.showToast('请输入2~5字纯中文姓名');
      return;
    }

    final param = InviteAcceptParamModel(
      token: us.token.value,
      name: userName,
    );

    final result = await _inviteRepo.acceptInvite(param);

    if (result.isOK()) {
      ToastUtil.showToast('加入成功');
      // 发送邀请加入成功事件，刷新"我参与"页面
      EventBusUtil.emit<String>('invite_success');
      YPRoute.closePage(true);
    } else {
      ToastUtil.showToast(result.fail?.errorMsg ?? '加入失败，请重试');
    }
  }

  /// 设置用户姓名
  void setUserName(String name) {
    us.setUserName(name);
  }
}
