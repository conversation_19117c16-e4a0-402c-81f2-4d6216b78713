import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/feature/worker/invite_join/vm/invite_join_vm.dart';
import 'package:gdjg_pure_flutter/utils/route_util/base_page/base_ful_page.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';
import 'package:get/get.dart';

/// 邀请加入页面
class InviteJoinPage extends BaseFulPage {
  const InviteJoinPage({super.key})
      : super(appBar: const YPAppBar(title: '邀请加入'), canBack: false);

  @override
  State createState() => _InviteJoinPageState();
}

class _InviteJoinPageState extends BaseFulPageState<InviteJoinPage> {
  late final InviteJoinVM viewModel;

  @override
  void onPageCreate() {
    super.onPageCreate();
    viewModel = InviteJoinVM();
  }

  @override
  void onPageRoute(Object? routeParams, bool fromLaunchTask) {
    super.onPageRoute(routeParams, fromLaunchTask);
    if (routeParams != null && routeParams is InviteJoinPageParam) {
      viewModel.init(routeParams);
    }
  }

  @override
  void onPageDestroy() {
    viewModel.dispose();
    super.onPageDestroy();
  }

  @override
  Widget yBuild(BuildContext context) {
    return Obx(() {
      return SingleChildScrollView(
        padding: EdgeInsets.symmetric(horizontal: 24.w),
        child: Column(
          children: [
            SizedBox(height: 120.h),
            // 信息
            _buildInfoCard(),
          ],
        ),
      );
    });
  }

  /// 信息
  Widget _buildInfoCard() {
    return Stack(
      clipBehavior: Clip.none,
      children: [
        Container(
          width: double.infinity,
          margin: EdgeInsets.only(top: 45.h),
          padding: EdgeInsets.only(
            top: 65.h,
            left: 24.w,
            right: 24.w,
            bottom: 24.h,
          ),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16.r),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 10.r,
                offset: Offset(0, 2.h),
              ),
            ],
          ),
          child: Column(
            children: [
              // 邀请信息
              _buildInviteInfo(),
              SizedBox(height: 40.h),
              // 立即加入按钮
              _buildJoinButton(),
            ],
          ),
        ),
        Positioned(
          top: 0,
          left: 0,
          right: 0,
          child: _buildLogo(),
        ),
      ],
    );
  }

  /// Logo
  Widget _buildLogo() {
    return Center(
      child: Container(
        width: 90.w,
        height: 90.w,
        decoration: BoxDecoration(
          color: ColorsUtil.primaryColor,
          borderRadius: BorderRadius.circular(16.r),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 8.r,
              offset: Offset(0, 2.h),
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(16.r),
          child: Image.asset(
            'assets/images/login/icon.webp',
            width: 90.w,
            height: 90.w,
            fit: BoxFit.cover,
          ),
        ),
      ),
    );
  }

  /// 构建邀请信息
  Widget _buildInviteInfo() {
    return Column(
      children: [
        Text(
          '邀请您加入',
          style: TextStyle(
            fontSize: 20.sp,
            fontWeight: FontWeight.w400,
            color: Colors.black,
          ),
        ),
        SizedBox(height: 16.h),
        Text(
          viewModel.us.inviteInfo.value?.deptName ?? '',
          style: TextStyle(
            fontSize: 18.sp,
            color: Colors.black,
          ),
        ),
        SizedBox(height: 16.h),
        RichText(
          text: TextSpan(
            children: [
              TextSpan(
                text: '进行',
                style: TextStyle(
                  fontSize: 14.sp,
                  color: ColorsUtil.black65,
                ),
              ),
              TextSpan(
                text: '考勤打卡，实时记工对工',
                style: TextStyle(
                  fontSize: 14.sp,
                  color: ColorsUtil.primaryColor,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }



  /// 构建立即加入按钮
  Widget _buildJoinButton() {
    return SizedBox(
      width: double.infinity,
      height: 40.h,
      child: ElevatedButton(
        onPressed: viewModel.acceptInvite,
        style: ElevatedButton.styleFrom(
          backgroundColor: ColorsUtil.primaryColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(4.r),
          ),
          elevation: 0,
        ),
        child: Text(
          '立即加入',
          style: TextStyle(
            fontSize: 16.sp,
            fontWeight: FontWeight.w600,
            color: Colors.white,
          ),
        ),
      ),
    );
  }
}
