
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/generated/assets.dart';
import 'package:gdjg_pure_flutter/utils/system_util/yprint.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';
import 'package:gdjg_pure_flutter/widget/calendar/utils.dart';

class SelectRecordDayView extends StatelessWidget {
  final int day;
  final DateTime date;
  final String businessTypes;
  // 是否是记工
  final bool isRecordWorkPoints;
  // 是否是多选
  final bool isMultiple;
  // 选择日期列表
  final List<DateTime> dateList;
  final Function(DateTime date) onTap;

  const SelectRecordDayView({
    required this.day,
    required this.date,
    required this.businessTypes,
    required this.onTap,
    required this.isRecordWorkPoints,
    required this.isMultiple,
    required this.dateList,
  });


  /// 获取业务类型文字
  String _getBusinessTypeText() {
    if(businessTypes.isEmpty){
      return '';
    }
    var types = businessTypes.split(',');
    if(isRecordWorkPoints){
      // 记工
      if (types.contains('1') || types.contains('2') || types.contains('3') || types.contains('6') || types.contains('10')) {
        return '工';
      } else if (types.contains('0')) {
        return '休';
      } else {
        return '';
      }
    } else {
      // 记账
      if (types.contains('4') || types.contains('5') || types.contains('8') || types.contains('9')) {
        return '账';
      }else {
        return '';
      }
    }
  }


  /// 获取业务类型背景
  Color _getBusinessTypeColor() {
    var defaultColor = Color(0xFFFFFFFF);
    if(businessTypes.isEmpty){
      return defaultColor;
    }
    var types = businessTypes.split(',');
    if(isRecordWorkPoints){
      // 记工
      if (types.contains('1') || types.contains('2') || types.contains('3') || types.contains('6') || types.contains('10')) {
        return Color(0x1A5290fd);
      } else if (types.contains('0')) {
        return Color(0x1A029855);
      } else {
        return defaultColor;
      }
    } else {
      // 记账
      if (types.contains('4') || types.contains('5') || types.contains('8') || types.contains('9')) {
        return Color(0x1Affa011);
      }else {
        return defaultColor;
      }
    }
  }

  /// 获取业务类型文本颜色
  Color _getBusinessTypeTextColor() {
    var defaultColor = Color(0xFFFFFFFF);
    if(businessTypes.isEmpty){
      return defaultColor;
    }
    var types = businessTypes.split(',');
    if(isRecordWorkPoints){
      // 记工
      if (types.contains('1') || types.contains('2') || types.contains('3') || types.contains('6') || types.contains('10')) {
        return Color(0xFF5290fd);
      } else if (types.contains('0')) {
        return Color(0xFF029855);
      } else {
        return defaultColor;
      }
    } else {
      // 记账
      if (types.contains('4') || types.contains('5') || types.contains('8') || types.contains('9')) {
        return Color(0xFFffa011);
      }else {
        return defaultColor;
      }
    }
  }


  /// 是否是选择日期
  bool _isSelectDate(){
    if (dateList.isEmpty){
      return false;
    }
    return dateList.any((element) => isSameDay(element, date));
  }

  Color _getSelectBorderColor(){
    var types = businessTypes.split(',');
    if(_isSelectDate()){
      // 记账
      if (types.contains('4') || types.contains('5') || types.contains('8') || types.contains('9')) {
        return Color(0xFFffa011);
      }else {
        return Color(0xFF5290fd);
      }
    }else{
      return Colors.transparent;
    }

  }


  @override
  Widget build(BuildContext context) {
    final isToday = isSameDay(date, DateTime.now());
    final isAfter = isAfterToday(date, DateTime.now());

    yprint('day:$day $date businessTypes:${_isSelectDate()}');

    return GestureDetector(
      onTap: () {
        onTap(date);
      },
      child: Container(
        margin: const EdgeInsets.all(1),
        decoration: BoxDecoration(
          color: _getBusinessTypeColor(),
          // color: eventList.isNotEmpty
          //     ? Color(0xFF5290FD).withValues(alpha: 0.1)
          //     : null,
          borderRadius: BorderRadius.circular(4),
          border: Border.all(color: _getSelectBorderColor(), width: 1),
          // border: eventList.isNotEmpty
          //     ? Border.all(color: Color(0xFF5290FD), width: 1)
          //     : null,
        ),
        child: Stack(
          alignment: AlignmentDirectional.center,
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 4.0),
              child: Column(
                mainAxisSize: MainAxisSize.max,
                children: [
                  // 当前日
                  Text(
                    day.toString(),
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: isToday
                          ? ColorsUtil.primaryColor
                          : isAfter ? Color(0xFFD6D6D6) : Color(0xFF333333),
                      fontSize: 15.sp,
                    ),
                  ),
                  Text(_getBusinessTypeText(), style: TextStyle(fontSize: 10.sp, color: _getBusinessTypeTextColor(),),),
                ],
              ),
            ),
            Visibility(
              visible: isMultiple && !isAfter,
                child: Positioned(
                  right: 0,
                  top: 0,
                  child:  Image.asset( _isSelectDate()? Assets.commonWaaIcCalendarSelector:Assets.commonWaaIcCalendarUnselector,width: 10.w,height: 10.h,),
                ))


          ],
        ),
      ),
    );
  }

}