
import 'package:gdjg_pure_flutter/widget/calendar/dayevent/DayEvent.dart';
import 'package:gdjg_pure_flutter/widget/calendar/utils.dart';
import 'package:get/get.dart';

class SelectRecordDateUS {

  // 是否是多选
  final _isMultiple = false.obs;
  get isMultiple => _isMultiple.value;
  setMultiple(value) => _isMultiple.value = value;


  // 是否切换选择
  final _isChangeChoice = false.obs;
  get isChangeChoice => _isChangeChoice.value;
  setChangeChoice(value) => _isChangeChoice.value = value;

  final _currentMonth = DateTime.now().obs;
  get currentMonth => _currentMonth.value;
  setCurrentMonth(DateTime date) {
    _currentMonth.value = date;
    _currentMonth.refresh();
  }

  final _selectDates = <DateTime>[].obs;
  get selectDates => _selectDates.value;
  setSelectDates(DateTime date) {
    if(isMultiple){
      if(_selectDates.any((element) => isSameDay(element, date))){
        var item = _selectDates.firstWhere((item) => isSameDay(item, date));
        _selectDates.remove(item);
      }else{
        _selectDates.add(date);
      }
    }else{
      _selectDates.clear();
      _selectDates.add(date);
    }
    _selectDates.refresh();
  }
  setSelectList(List<DateTime> list) {
    _selectDates.clear();
    _selectDates.addAll(list);
    _selectDates.refresh();
  }





  final RxMap<String, String> _events = <String, String>{}.obs;
  Map<String, String> get events => _events.value;
  setEvents(Map<String, String> value) {
    _events.value = value;
    _events.refresh();
  }
}