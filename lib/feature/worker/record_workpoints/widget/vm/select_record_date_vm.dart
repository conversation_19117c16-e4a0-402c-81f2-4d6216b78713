
import 'package:gdjg_pure_flutter/data/select_record_date/ds/model/param/param_model.dart';
import 'package:gdjg_pure_flutter/data/select_record_date/repo/select_record_date_repo.dart';
import 'package:gdjg_pure_flutter/feature/worker/record_workpoints/widget/vm/select_record_date_us.dart';
import 'package:intl/intl.dart';

class SelectRecordDateVM {
  final us = SelectRecordDateUS();
  final _repo = SelectRecordDateRepo();

  final String _note_id;

  SelectRecordDateVM(this._note_id);

  getCalendarBusinessInfo() async{

    var startDate = DateTime(us.currentMonth.year, us.currentMonth.month, 1);
    String startFormattedDate = DateFormat('yyyy-MM-dd').format(startDate);


    DateTime endDate;
    var nowDate = DateTime.now();
    if(nowDate.year == us.currentMonth.year && nowDate.month == us.currentMonth.month){
      endDate = nowDate;
    }else{
      DateTime lastDayOfMonth = DateTime(us.currentMonth.year, us.currentMonth.month + 1, 0);
      endDate = DateTime(us.currentMonth.year, us.currentMonth.month, lastDayOfMonth.day);
    }
    String endFormattedDate = DateFormat('yyyy-MM-dd').format(endDate);

    var param = BusinessGetCalendarParamModel(
      note_id: _note_id,
      work_note: _note_id,
      start_date: startFormattedDate,
      end_date: endFormattedDate,
    );
    var resp = await _repo.getCalendarBusinessInfo(param);
    if(resp.isOK()){
      var list = resp.getSucData()?.list;
      if(list != null){
        for (var element in list) {
          var date = element.date;
          var cMonth = DateFormat('yyyy-MM').format(us.currentMonth);
          if(date == cMonth){
            var map = <String, String>{};
            for (var item in element.list) {
              map[item.day] = item.businessTypes;
            }
            us.setEvents(map);
          }

        }

      }

    }
  }

  setChangeChoice(bool value){
    us.setChangeChoice(value);
  }
  setMultiple(bool value){
    us.setMultiple(value);
  }

  setCurrentMonth(DateTime date){
    us.setCurrentMonth(date);

    getCalendarBusinessInfo();
  }

  setSelectDates(DateTime date) {
    us.setSelectDates(date);
  }

  setSelectList(List<DateTime> list) {
    us.setSelectList(list);
  }

}