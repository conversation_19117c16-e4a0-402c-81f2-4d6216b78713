
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/feature/worker/record_workpoints/widget/select_record_calender_view.dart';
import 'package:gdjg_pure_flutter/feature/worker/record_workpoints/widget/vm/select_record_date_vm.dart';
import 'package:gdjg_pure_flutter/iconfont/iconfont.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/utils/system_util/yprint.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';
import 'package:gdjg_pure_flutter/widget/calendar/calendar_pager_view.dart';
import 'package:gdjg_pure_flutter/widget/calendar/calendar_week.dart';
import 'package:gdjg_pure_flutter/widget/calendar/dayevent/DayEvent.dart';
import 'package:gdjg_pure_flutter/widget/calendar/dynamic_height_calendar.dart';
import 'package:gdjg_pure_flutter/widget/calendar/utils.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

class SelectRecordDateView extends StatefulWidget {

  // 是否是多选
  final bool isMultiple;
  // 是否切换选择
  final bool isChangeChoice;
  // 是否是记工
  final bool isRecordWorkPoints;
  // 工本id
  final String note_id;
  // 日期列表
  final List<DateTime> dateList;

  final Function(List<DateTime> selectList) onSelect;

  SelectRecordDateView({
    Key? key,
    required this.note_id,
    required this.isRecordWorkPoints,
    required this.dateList,
    required this.onSelect,
    this.isMultiple = false,
    this.isChangeChoice = false,
  }) : super(key: key);

  @override
  State<SelectRecordDateView> createState() => _SelectRecordDateViewState();
}

class _SelectRecordDateViewState extends State<SelectRecordDateView> {
  late final SelectRecordDateVM vm;
  late PageController _pageController;
  DateTime currentDate = DateTime.now();
  // DateTime currentDate = DateTime.now().copyWith(year: 2024, month: 1, day: 1);
  DateTime startDate = DateTime.now().copyWith(year: 2020, month: 1, day: 1);
  DateTime endDate = DateTime.now();

  @override
  void initState() {
    super.initState();
    vm = SelectRecordDateVM(widget.note_id);
    Get.put(vm.us);
    _pageController = PageController(
      initialPage: calculatePageIndex(
        initDate: currentDate,
        startDate: startDate,
        endDate: endDate,
      ),
    );
    vm.setChangeChoice(widget.isChangeChoice);
    vm.setMultiple(widget.isMultiple);
    vm.setSelectList(widget.dateList);
  }

  final GlobalKey<DynamicHeightCalendarState> _calendarKey =
  GlobalKey<DynamicHeightCalendarState>();

  Widget _buildWeekView() {
    return SizedBox(
      height: 30,
      child: CalendarWeek(),
    );
  }

  Widget _buildTitleView() {
    return SizedBox(
      height: 44,
      child: Stack(
        children: [
          SizedBox(
            width: double.infinity,
            height: double.infinity,
            child: Obx(()=>
                Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    GestureDetector(
                      onTap: () {
                        YPRoute.closeDialog();
                      },
                      child: SizedBox(
                        height: double.infinity,
                        child: Center(
                          child: IconFont(IconNames.saasClose,size: 18,)
                        )
                      )
                    ),
                    Visibility(
                        visible: vm.us.isChangeChoice,
                        child: GestureDetector(
                          onTap: () {
                            var isMultiple = !vm.us.isMultiple;
                            vm.setMultiple(isMultiple);
                            // if(!isMultiple){
                              vm.setSelectList([]);
                            // }
                          },
                          child: Row(
                              children: [
                                IconFont(IconNames.saasChange,size: 16,),
                                SizedBox(width: 4,),
                                Text(vm.us.isMultiple?'单选':'多选', style: TextStyle(color:Color(0xFF323233), fontSize: 16.sp))
                              ]
                          ),
                        ))
                  ],
                )
            ),
          ),
          Positioned(
            right: 0,
            top: 0,
            left: 0,
            bottom: 0,
            child: SizedBox(
              width: double.infinity,
              height: double.infinity,
              child: Center(child: Text('请选择日期${vm.us.isMultiple?'(可选多天)':''}', style: TextStyle(color: Color(0xFF323233), fontWeight: FontWeight.w500, fontSize: 16.sp)),),
            ),
          )
        ],
      ),
    );
  }

  Widget _buildChangeMonthView() {
    var df = DateFormat('yyyy年MM月');
    return SizedBox(
      height: 42,
      child: Row(
        children: [
          GestureDetector(
            onTap: () {
              _pageController.previousPage(
                  duration: Duration(milliseconds: 300),
                  curve: Curves.easeInOut);
            },
            child: Row(
              children: [
                IconFont(IconNames.saasArrowLeft,size: 14,color: ColorsUtil.primaryColorStr),
                Text('上一个月', style: TextStyle(color: ColorsUtil.primaryColor, fontSize: 14.sp)),
              ],
            ),
          ),
          Flexible(
              flex: 1,
              child: SizedBox(
                  width: double.infinity,
                  child: Center(
                    child: Obx(()=>
                        Text(df.format(vm.us.currentMonth), style: TextStyle(color:  Color(0xFF323233),fontWeight: FontWeight.w500, fontSize: 16.sp))
                    ),
                  )
              )
          ),
          GestureDetector(
            onTap: () {
              _pageController.nextPage(
                  duration: Duration(milliseconds: 300),
                  curve: Curves.easeInOut);
            },
            child: Row(
              children: [
                Text('下一个月', style: TextStyle(color: ColorsUtil.primaryColor, fontSize: 14.sp)),
                IconFont(IconNames.saasArrowRight,size: 14,color: ColorsUtil.primaryColorStr),
              ],
            ),
          ),
        ],
      ),
    );
  }

  ///大日历
  Widget _buildCalenderView() {
    return Container(
      height: 285,
      width: double.infinity,
      child: CalendarPagerView(
        onPageChanged: (context, date) {
          vm.setCurrentMonth(date);
          yprint('$date');
        },
        viewportFraction: 1,
        initialDate: currentDate,
        pageController: _pageController,
        startDate: DateTime.now().copyWith(year: 2020),
        endDate: DateTime.now(),
        itemBuilder: (context, cur) {
          return Obx(()=>
              SelectRecordCalenderView(
                onTap: (date) {
                  if (vm.us.isMultiple){
                    vm.setSelectDates(date);
                  }else{
                    widget.onSelect([date]);
                    YPRoute.closeDialog();
                  }
                },
                currentDate: cur,
                events: vm.us.events,
                isRecordWorkPoints: widget.isRecordWorkPoints,
                isMultiple: vm.us.isMultiple,
                dateList: vm.us.selectDates,
              )
          );
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(8)),
      ),
      padding: EdgeInsets.symmetric(horizontal: 16),
      child: Column(
          mainAxisSize: MainAxisSize.min,
        children: [
          _buildTitleView(),
          _buildChangeMonthView(),
          _buildWeekView(),
          _buildCalenderView(),
          SizedBox(height: 10),
          Obx(()=>
              Visibility(
                  visible: vm.us.isMultiple,
                  child: GestureDetector(
                    onTap: () {
                      widget.onSelect(vm.us.selectDates);
                      YPRoute.closeDialog();
                    },
                    child: Container(
                      decoration: BoxDecoration(
                          color: ColorsUtil.primaryColor,
                          borderRadius: BorderRadius.circular(4)
                      ),
                      width: 165,
                      height: 40,
                      child: Center(child: Text('确定', style: TextStyle(color: Colors.white, fontSize: 16)),),
                    ),
                  ))
          ),

          SizedBox(height: 12),

        ]
      )

    );
  }
}