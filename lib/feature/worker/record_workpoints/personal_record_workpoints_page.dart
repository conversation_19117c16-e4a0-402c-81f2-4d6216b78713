
import 'package:flutter/material.dart';
import 'package:gdjg_pure_flutter/feature/worker/record_workpoints/vm/personal_record_workpoints_vm.dart';
import 'package:gdjg_pure_flutter/feature/worker/record_workpoints/widget/select_record_date_view.dart';
import 'package:gdjg_pure_flutter/generated/assets.dart';
import 'package:gdjg_pure_flutter/utils/route_util/base_page/base_ful_page.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/utils/system_util/yprint.dart';
import 'package:gdjg_pure_flutter/widget/custom_tab_indicator.dart';
import 'package:get/get.dart';

class PersonalRecordWorkPoints extends BaseFulPage {
  const PersonalRecordWorkPoints({super.key}): super(appBar: const YPAppBar(title: "个人记工"));

  @override
  State createState() => _PersonalRecordWorkPoints();
}
class _PersonalRecordWorkPoints extends BaseFulPageState  with SingleTickerProviderStateMixin {

  final vm = PersonalRecordWorkPointsVM();

  late TabController _tabController;
  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 5, vsync: this);
    _tabController.addListener(() {
      // setState(() {});
      // ToastUtil.showToast('当前选中的 Tab 索引：${_tabController.index}');
    });
  }
  @override
  void dispose() {
    _tabController.removeListener(() {});
    _tabController.dispose();
    super.dispose();
  }

  /// 项目选择
  Widget _buildProjectView(){
    return GestureDetector(
      onTap: () {
        yprint('******************');
        vm.changeProject();
      },
      child: Container(
        width: double.infinity,
        height: 45,
        color: Colors.transparent,
        child:
        Center(
          child: Row(
            children: [
              Text('项目：',style: TextStyle(fontSize: 17,color: Color(0xFF323233))),
              Obx(()=>
                  Text('${vm.us.projectName}',style: TextStyle(fontSize: 17,color: Color(0xFF323233), fontWeight: FontWeight.w500))
              ),
              Spacer(),
              SizedBox(width: 4),
              Image.asset(Assets.commonIconArrowRightGrey,width: 18,height: 18)
            ],
          ),
        ),
      ),
    );
  }


  /// 项目选择
  Widget _buildDateView(){
    return GestureDetector(
      onTap: () {
        showSelectRecordDate();
      },
      child: Container(
        width: double.infinity,
        height: 45,
        child:
        Center(
          child: Row(
            children: [
              Text('日期：',style: TextStyle(fontSize: 17,color: Color(0xFF323233))),
              Text('2021年12月3日',style: TextStyle(fontSize: 17,color: Color(0xFF323233), fontWeight: FontWeight.w500)),
              Spacer(),
              Text('可多选',style: TextStyle(fontSize: 14,color: Color(0xFF323233))),
              SizedBox(width: 4),
              Image.asset(Assets.commonIconArrowRightGrey,width: 18,height: 18)
            ],
          ),
        ),
      ),
    );
  }


  /// 选择记工日期
  void showSelectRecordDate() {

    // var dates = <DateTime>[];
    // dates.add(DateTime.now());

    YPRoute.openDialog(
      builder: (context) => SelectRecordDateView(
        note_id: '780498',
        isRecordWorkPoints: true,
        dateList: vm.us.selectDates,
        isChangeChoice: true,
        onSelect: (dateList) {
          vm.setSelectList(dateList);
        },),
      alignment: Alignment.bottomCenter,
    );
  }
  @override
  Widget yBuild(BuildContext context) {
    return Container(
      width: double.infinity,
      height: double.infinity,
        color: Colors.white,
      child:Column(
        children: [
          Divider(thickness: 8,color: Color(0xFFF0F0F0),),
          Container(
            padding: EdgeInsets.symmetric(horizontal: 16),
            width: double.infinity,
            child: Column(
                children: [
                  _buildDateView(),
                  Divider(thickness: 1,color: Color(0xFFF5F5F5),),
                  _buildProjectView(),

                ]
            ),
          ),
          Divider(thickness: 8,color: Color(0xFFF0F0F0),),

          SizedBox(
            height: 32,
            child: TabBar(
                isScrollable: true,
                labelColor: Color.fromRGBO(0, 0, 0, 0.85),
                unselectedLabelColor: Color.fromRGBO(0, 0, 0, 0.65),
                labelStyle: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w500,
                ),
                labelPadding: EdgeInsets.symmetric(horizontal: 14),
                unselectedLabelStyle: const TextStyle(fontSize: 18),
                tabAlignment: TabAlignment.start,
                // indicator: BoxDecoration(
                //   color: Colors.blue,
                //   borderRadius: BorderRadius.circular(16),
                // ),
                indicatorColor: Colors.blue,
                indicatorWeight: 3,
                // indicatorSize: TabBarIndicatorSize.tab,
                indicator: CustomTabIndicator(
                    borderSide: BorderSide(color: Colors.blue, width: 3)),
                dividerColor: Colors.transparent,
                controller: _tabController,
                tabs: [
                  Tab(text: '点工'),
                  Tab(text: '包工'),
                  Tab(text: '短工'),
                  Tab(text: '工量'),
                  Tab(text: '其他费用'),
                ]),
          ),
          Flexible(
            flex: 1,
            child: Container(
              // color: Colors.red,
              child: TabBarView(
                  controller: _tabController,
                  physics: NeverScrollableScrollPhysics(),
                  children: [
                    Container(child: Text('点工'),),
                    Container(child: Text('包工'),),
                    Container(child: Text('短工'),),
                    Container(child: Text('工量'),),
                    Container(child: Text('其他费用'),),
                  ]
              ),
            ),
          ),
        ],
      )


    );
  }
}