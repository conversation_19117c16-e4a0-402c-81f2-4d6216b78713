
import 'package:get/get.dart';

class PersonalRecordWorkPointsUS {

  final  _projectName = ''.obs;
  get projectName => _projectName.value;

  setProjectName(value) {
    _projectName.value = value;
  }


  final _selectDates = <DateTime>[].obs;
  get selectDates => _selectDates.value;
  setSelectList(List<DateTime> list) {
    _selectDates.clear();
    _selectDates.addAll(list);
    _selectDates.refresh();
  }

}