
import 'package:gdjg_pure_flutter/data/change_project/repo/model/biz_model.dart';
import 'package:gdjg_pure_flutter/feature/worker/record_workpoints/vm/personal_record_workpoints_us.dart';
import 'package:gdjg_pure_flutter/init_module/init_route.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/utils/system_util/yprint.dart';

class PersonalRecordWorkPointsVM {
  final us = PersonalRecordWorkPointsUS();


  changeProject() {
    // us.projectName.value = '项目名';
    // us.projectName.refresh();
    YPRoute.openPage(RouteNameCollection.changeProjectPage)?.then((result){
      if(result != null){
        var model = result as ProjectGetAllProjectNameABizModel;
        us.setProjectName(model.name);
        // us.projectName.value = model.name;
        yprint('----------result:${model.name}');
      }

    });
  }


  setSelectList(List<DateTime> list) {
    us.setSelectList(list);
  }
}