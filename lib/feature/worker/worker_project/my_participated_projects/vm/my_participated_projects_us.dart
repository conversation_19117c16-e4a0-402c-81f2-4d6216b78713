import 'package:gdjg_pure_flutter/data/worker_data/worker_project_data/repo/model/dept_list_group_biz_model.dart';
import 'package:get/get.dart';

/// 我参与的项目UI状态管理
class MyParticipatedProjectsUS {
  final _showProList = <NetModelGroupABizModel>[].obs;

  /// 每个项目的金额显示状态（项目ID 默认false隐藏）
  final _projectAmountVisibilityMap = <String, bool>{}.obs;

  /// 每个项目的金额数据（项目ID 金额）
  final _projectAmountMap = <String, double>{}.obs;

  // Getters
  RxList<NetModelGroupABizModel> get showProList => _showProList;
  RxMap<String, bool> get projectAmountVisibilityMap => _projectAmountVisibilityMap;
  RxMap<String, double> get projectAmountMap => _projectAmountMap;

  /// 设置项目列表
  void setShowProList(List<NetModelGroupABizModel> list) {
    _showProList.value = list;
    // 为新项目初始化显示状态（默认隐藏）
    for (var project in list) {
      final projectId = project.workNoteId.toString();
      if (!_projectAmountVisibilityMap.containsKey(projectId)) {
        _projectAmountVisibilityMap[projectId] = false;
      }
    }
  }

  /// 获取指定项目的金额显示状态
  bool getProjectAmountVisibility(String projectId) {
    return _projectAmountVisibilityMap[projectId] ?? false;
  }

  /// 切换指定项目的金额显示状态
  void toggleProjectAmountVisibility(String projectId) {
    final currentState = _projectAmountVisibilityMap[projectId] ?? false;
    _projectAmountVisibilityMap[projectId] = !currentState;
  }

  /// 获取指定项目的金额
  double getProjectAmount(String projectId) {
    return _projectAmountMap[projectId] ?? 0.0;
  }

  /// 设置指定项目的金额
  void setProjectAmount(String projectId, double amount) {
    _projectAmountMap[projectId] = amount;
  }
}
