import 'package:gdjg_pure_flutter/data/worker_data/worker_project_data/repo/model/dept_list_group_biz_model.dart';
import 'package:get/get.dart';

/// 我参与的项目UI状态管理
class MyParticipatedProjectsUS {
  final _showProList = <DeptListGroupABizModel>[].obs;

  /// 金额显示状态（true-显示，false-隐藏）
  final _isAmountVisible = true.obs;

  // Getters
  RxList<DeptListGroupABizModel> get showProList => _showProList;
  RxBool get isAmountVisible => _isAmountVisible;

  /// 设置项目列表
  void setShowProList(List<DeptListGroupABizModel> list) {
    _showProList.value = list;
  }

  /// 设置金额显示状态
  void setAmountVisibility(bool visible) {
    _isAmountVisible.value = visible;
  }
}
