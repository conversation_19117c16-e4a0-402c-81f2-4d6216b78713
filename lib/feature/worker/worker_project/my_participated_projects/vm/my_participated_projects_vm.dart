import 'package:gdjg_pure_flutter/data/worker_data/worker_project_data/repo/worker_project_repo.dart';
import 'package:gdjg_pure_flutter/feature/worker/worker_project/my_participated_projects/vm/my_participated_projects_us.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/toast_util.dart';

/// 我参与的项目ViewModel
class MyParticipatedProjectsVM {
  WorkerProjectRep rep = WorkerProjectRep();
  MyParticipatedProjectsUS us = MyParticipatedProjectsUS();

  /// 查询项目列表
  Future<void> queryProjectList() async {
    final result = await rep.queryParticipatedProjectList();

    if (result.isOK()) {
      us.setShowProList(result.getSucData()?.list ?? []);
    } else {
      ToastUtil.showToast(result.fail?.errorMsg ?? '获取项目列表失败');
    }
  }

  /// 刷新数据
  Future<void> onRefresh() async {
    await queryProjectList();
  }

  /// 切换金额显示状态
  void toggleAmountVisibility() {
    us.setAmountVisibility(!us.isAmountVisible.value);
  }
}
