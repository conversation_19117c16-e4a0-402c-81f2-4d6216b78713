import 'package:gdjg_pure_flutter/data/worker_data/worker_project_data/repo/worker_project_repo.dart';
import 'package:gdjg_pure_flutter/feature/worker/worker_project/my_participated_projects/vm/my_participated_projects_us.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/toast_util.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:gdjg_pure_flutter/init_module/init_route.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_un_liauidated/ds/model/param/group_project_get_group_worker_settle_param_model.dart';

/// 我参与的项目ViewModel
class MyParticipatedProjectsVM {
  WorkerProjectRepo rep = WorkerProjectRepo();
  MyParticipatedProjectsUS us = MyParticipatedProjectsUS();

  /// 查询项目列表
  Future<void> queryProjectList() async {
    final result = await rep.queryParticipatedProjectList();

    if (result.isOK()) {
      us.setShowProList(result.getSucData()?.list ?? []);
    } else {
      ToastUtil.showToast(result.fail?.errorMsg ?? '获取项目列表失败');
    }
  }

  /// 刷新数据
  Future<void> onRefresh() async {
    await queryProjectList();
  }

  /// 切换金额显示状态
  void toggleProjectAmountVisibility(String projectId) {
    // 切换显示状态
    us.toggleProjectAmountVisibility(projectId);

    // 打开状态，调用接口获取金额
    if (us.getProjectAmountVisibility(projectId)) {
      // 找到对应的项目数据
      try {
        final project = us.showProList.firstWhere(
          (p) => p.workNoteId.toString() == projectId,
        );
        fetchProjectAmount(projectId, project.workerId.toString());
      } catch (e) {
        // 如果找不到项目，使用默认值
        us.setProjectAmount(projectId, 0.0);
      }
    }
  }

  /// 获取项目金额
  Future<void> fetchProjectAmount(String projectId, String workerId) async {
    final result = await rep.getGroupWorkerSettle(
      GroupProjectGetGroupWorkerSettleParamModel(
        workNote: projectId,
        workerId: workerId,
      ),
    );

    if (result.isOK()) {
      final amount = result.getSucData()?.notSettleMoney ?? 0.0;
      us.setProjectAmount(projectId, amount);
    } else {
      ToastUtil.showToast(result.fail?.errorMsg ?? '获取金额失败');
      us.setProjectAmount(projectId, 0.0);
    }
  }

  /// 检查相机权限状态
  Future<String> checkCameraPermissionStatus() async {
    // 刷新权限状态
    final status = await Permission.camera.status;

    if (status.isGranted) {
      return 'granted';
    } else if (status.isPermanentlyDenied) {
      return 'permanentlyDenied';
    } else {
      return 'needRequest';
    }
  }

  /// 请求相机权限并处理扫码
  Future<void> requestCameraPermissionAndScan() async {
    try {
      final status = await Permission.camera.request();

      if (status.isGranted) {
        // 权限获取成功，跳转到扫码页面
        YPRoute.openPage(RouteNameCollection.qrScan);
      } else if (status.isPermanentlyDenied) {
        // 权限被永久拒绝
        return Future.error('permanentlyDenied');
      }
    } catch (e) {
      // 权限请求异常
      return Future.error('requestError');
    }
  }
}
