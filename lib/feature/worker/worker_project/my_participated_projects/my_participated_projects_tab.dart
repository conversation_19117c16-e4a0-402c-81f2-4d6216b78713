import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/feature/worker/worker_project/my_participated_projects/vm/my_participated_projects_vm.dart';
import 'package:gdjg_pure_flutter/feature/worker/worker_project/my_participated_projects/my_participated_project_item_card.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

/// 我参与的项目Tab页面
class MyParticipatedProjectsTab extends StatefulWidget {
  const MyParticipatedProjectsTab({super.key});

  @override
  State<MyParticipatedProjectsTab> createState() => _MyParticipatedProjectsTabState();
}

class _MyParticipatedProjectsTabState extends State<MyParticipatedProjectsTab> with AutomaticKeepAliveClientMixin {
  late MyParticipatedProjectsVM _viewModel;
  late RefreshController _refreshController;

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    _viewModel = MyParticipatedProjectsVM();
    _refreshController = RefreshController(initialRefresh: false);
    _viewModel.queryProjectList();
  }

  @override
  void dispose() {
    _refreshController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // 必须调用以保持页面状态
    return Stack(
      children: [
        SmartRefresher(
          controller: _refreshController,
          enablePullDown: true,
          enablePullUp: false,
          onRefresh: _onRefresh,
          child: CustomScrollView(
            physics: const AlwaysScrollableScrollPhysics(),
            slivers: [
              // 项目列表区域
              _buildProjectListSliver(),
            ],
          ),
        ),
        // 扫码按钮
        _buildFloatingButton(),
      ],
    );
  }

  /// 项目列表 Sliver
  Widget _buildProjectListSliver() {
    return Obx(() {
      return SliverPadding(
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 10.h),
        sliver: SliverList(
          delegate: SliverChildBuilderDelegate(
            (context, index) {
              final project = _viewModel.us.showProList[index];
              return Obx(() => MyParticipatedProjectItemCard(
                item: project,
                isAmountVisible: _viewModel.us.isAmountVisible.value,
                onToggleVisibility: () => _viewModel.toggleAmountVisibility(),
              ));
            },
            childCount: _viewModel.us.showProList.length,
          ),
        ),
      );
    });
  }

  /// 扫码按钮
  Widget _buildFloatingButton() {
    return Positioned(
      left: 0,
      right: 0,
      bottom: 20.h, // 距离底部的距离
      child: Center(
        child: GestureDetector(
          onTap: () {
            // TODO: 实现扫码加入项目功能
          },
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: 70.w, vertical: 8.h),
            decoration: BoxDecoration(
              color: const Color(0xFF1984FF),
              borderRadius: BorderRadius.circular(25.r),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.qr_code_scanner,
                  color: Colors.white,
                  size: 20.w,
                ),
                SizedBox(width: 8.w),
                Text(
                  '扫码加入项目',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 处理刷新
  Future<void> _onRefresh() async {
    try {
      await _viewModel.onRefresh();
      _refreshController.refreshCompleted();
    } catch (e) {
      _refreshController.refreshFailed();
    }
  }
}
