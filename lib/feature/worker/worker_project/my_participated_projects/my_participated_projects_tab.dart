import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/feature/worker/worker_project/my_participated_projects/vm/my_participated_projects_vm.dart';
import 'package:gdjg_pure_flutter/feature/worker/worker_project/my_participated_projects/my_participated_project_item_card.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:gdjg_pure_flutter/generated/assets.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/init_module/init_route.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/common_dialog.dart';
import 'package:permission_handler/permission_handler.dart';


/// 我参与的项目Tab页面
class MyParticipatedProjectsTab extends StatefulWidget {
  final Function(VoidCallback)? onRefreshCallback;

  const MyParticipatedProjectsTab({super.key, this.onRefreshCallback});

  @override
  State<MyParticipatedProjectsTab> createState() => MyParticipatedProjectsTabState();
}

class MyParticipatedProjectsTabState extends State<MyParticipatedProjectsTab> with AutomaticKeepAliveClientMixin {
  late MyParticipatedProjectsVM _viewModel;
  late RefreshController _refreshController;

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    _viewModel = MyParticipatedProjectsVM();
    _refreshController = RefreshController(initialRefresh: false);

    // 注册刷新回调
    widget.onRefreshCallback?.call(_refreshData);
  }

  /// 获取ViewModel引用，给page使用检查是否为空
  MyParticipatedProjectsVM getViewModel() {
    return _viewModel;
  }

  @override
  void dispose() {
    _refreshController.dispose();
    super.dispose();
  }

  /// 刷新数据回调
  Future<void> _refreshData() async {
    await _viewModel.onRefresh();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // 必须调用以保持页面状态
    return Stack(
      children: [
        SmartRefresher(
          controller: _refreshController,
          enablePullDown: true,
          enablePullUp: false,
          onRefresh: _onRefresh,
          child: CustomScrollView(
            physics: const AlwaysScrollableScrollPhysics(),
            slivers: [
              // 项目列表区域
              _buildProjectListSliver(),
            ],
          ),
        ),
        // 扫码按钮
        _buildFloatingButton(),
      ],
    );
  }

  /// 项目列表 Sliver
  Widget _buildProjectListSliver() {
    return Obx(() {
      // 空列表
      if (_viewModel.us.showProList.isEmpty) {
        return SliverToBoxAdapter(
          child: Container(
            width: double.infinity,
            padding: EdgeInsets.only(top: 90.h, bottom: 50.h),
            child: Column(
              children: [
                Image.asset(
                  Assets.commonIconEmptyTeamProject,
                  width: 120.w,
                  height: 120.w,
                  fit: BoxFit.contain,
                ),
                Text(
                  '暂未加入班组项目',
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: const Color(0xFF8A8A99),
                  ),
                ),
              ],
            ),
          ),
        );
      }

      // 有数据时显示项目列表
      return SliverPadding(
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 10.h),
        sliver: SliverList(
          delegate: SliverChildBuilderDelegate(
            (context, index) {
              final project = _viewModel.us.showProList[index];
              final projectId = project.workNoteId.toString();
              return Obx(() => MyParticipatedProjectItemCard(
                item: project,
                isAmountVisible: _viewModel.us.getProjectAmountVisibility(projectId),
                amount: _viewModel.us.getProjectAmount(projectId),
                onToggleVisibility: () => _viewModel.toggleProjectAmountVisibility(projectId),
              ));
            },
            childCount: _viewModel.us.showProList.length,
          ),
        ),
      );
    });
  }

  /// 扫码按钮
  Widget _buildFloatingButton() {
    return Positioned(
      left: 0,
      right: 0,
      bottom: 20.h,
      child: Center(
        child: GestureDetector(
          onTap: _handleQRScanClick,
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: 70.w, vertical: 8.h),
            decoration: BoxDecoration(
              color: const Color(0xFF1984FF),
              borderRadius: BorderRadius.circular(25.r),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Image.asset(
                  Assets.workerIcQrScan,
                  height: 24.w,
                  width: 24.w,
                ),
                SizedBox(width: 12.w),
                Text(
                  '扫码加入项目',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 扫码按钮
  void _handleQRScanClick() async {
    // 检查权限状态
    final permissionStatus = await _viewModel.checkCameraPermissionStatus();

    if (permissionStatus == 'granted') {
      // 权限已获取，直接跳转扫码页面
      YPRoute.openPage(RouteNameCollection.qrScan);
    } else if (permissionStatus == 'permanentlyDenied') {
      // 权限被永久拒绝显示去设置弹窗
      _showPermissionSettingsDialog();
    } else {
      // 请求权限
      _showCameraPermissionExplanationAndRequest();
    }
  }

  /// 相机权限说明弹窗并请求权限
  void _showCameraPermissionExplanationAndRequest() {
    YPRoute.openDialog(
      tag: 'cameraPermissionDialog',
      clickMaskDismiss: false,
      alignment: Alignment.topCenter,
      builder: (context) => _buildCameraPermissionDialog(),
    );

    Future.delayed(const Duration(milliseconds: 100), () {
      _requestCameraPermissionFromVM();
    });
  }

  /// 相机权限说明弹窗
  Widget _buildCameraPermissionDialog() {
    return Builder(
      builder: (context) => Material(
        color: Colors.transparent,
        child: Container(
          width: double.infinity,
          margin: EdgeInsets.only(
            top: MediaQuery.of(context).padding.top,
            left: 0,
            right: 0,
          ),
          padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 16.h),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
              bottomLeft: Radius.circular(12.r),
              bottomRight: Radius.circular(12.r),
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '相机权限使用说明',
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w600,
                  color: Colors.black,
                ),
              ),
              SizedBox(height: 8.h),
              Text(
                '在您使用扫码功能时，需要您同意获取相机权限',
                style: TextStyle(
                  fontSize: 14.sp,
                  color: Colors.black54,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 请求相机权限
  Future<void> _requestCameraPermissionFromVM() async {
    try {
      await _viewModel.requestCameraPermissionAndScan();

      // 权限获取成功，关闭弹窗
      if (mounted) {
        YPRoute.closeDialog();
      }
    } catch (e) {
      // 关闭权限说明弹窗
      if (mounted) {
        YPRoute.closeDialog();
      }

      if (e.toString() == 'permanentlyDenied') {
        // 权限被永久拒绝，显示设置弹窗
        if (mounted) {
          _showPermissionSettingsDialog();
        }
      }
    }
  }

  /// 显示权限设置弹窗
  void _showPermissionSettingsDialog() {
    showCommonDialog(CommonDialogConfig(
      title: '相机权限使用说明',
      content: '在您使用扫码功能时，需要您同意授权相机权限',
      negative: '取消',
      positive: '去开启',
      onPositive: () async {
        // 打开应用设置页面
        await openAppSettings();
        await Future.delayed(const Duration(milliseconds: 500));
        // 强制退出应用
        exit(0);
      },
    ));
  }

  /// 处理刷新
  Future<void> _onRefresh() async {
    try {
      await _viewModel.onRefresh();
      _refreshController.refreshCompleted();
    } catch (e) {
      _refreshController.refreshFailed();
    }
  }
}
