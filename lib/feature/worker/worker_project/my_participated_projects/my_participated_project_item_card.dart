import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/data/worker_data/worker_project_data/repo/model/dept_list_group_biz_model.dart';


class MyParticipatedProjectItemCard extends StatelessWidget {

  final DeptListGroupABizModel item;
  final bool isAmountVisible;
  final VoidCallback? onToggleVisibility;

  const MyParticipatedProjectItemCard({
    super.key,
    required this.item,
    this.isAmountVisible = true,
    this.onToggleVisibility,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(bottom: 12.h),
      padding: EdgeInsets.only(left: 12.w, right: 12.w, top: 10.h, bottom: 8.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Column(
        children: [
          // 项目名称和日期
          Row(
            children: [
              Expanded(
                child: Text(
                  item.name,
                  style: TextStyle(
                    fontSize: 22.sp,
                    fontWeight: FontWeight.w600,
                    color: const Color(0xFF333333),
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              Text(
                _formatDate(item.lastOperationDate),
                style: TextStyle(
                  fontSize: 14.sp,
                  color: const Color(0xFF999999),
                ),
              ),
            ],
          ),

          SizedBox(height: 7.h),

          // 未结显示
          Row(
            children: [
              Text(
                '未结：',
                style: TextStyle(
                  fontSize: 14.sp,
                  color: const Color(0xD9000000),
                ),
              ),
              Text(
                isAmountVisible ? '0.00' : '****',
                style: TextStyle(
                  fontSize: 20.sp,
                  color: const Color(0xFF8A8A99),
                  letterSpacing: -1.5
                ),
              ),
              SizedBox(width: 4.w),
              GestureDetector(
                onTap: onToggleVisibility,
                child: Icon(
                  isAmountVisible
                      ? Icons.visibility
                      : Icons.visibility_off,
                  size: 16.w,
                  color: const Color(0xFF1984FF),
                ),
              ),
              const Spacer(),
            ],
          ),

          SizedBox(height: 8.h),

          // 分割线
          Container(
            height: 0.5.h,
            color: Colors.grey[300],
          ),

          SizedBox(height: 6.h),

          // 设置
          Row(
            children: [
              GestureDetector(
                onTap: null,
                child: Icon(
                  Icons.settings,
                  size: 21.w,
                  color: Colors.grey[600],
                ),
              ),

              const Spacer(),

              // 查看记工按钮
              GestureDetector(
                onTap: () {
                },
                child: Container(
                  padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 4.h),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    border: Border.all(color: const Color(0xFF1984FF)),
                    borderRadius: BorderRadius.circular(4.r),
                  ),
                  child: Text(
                    '查看记工',
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: const Color(0xFF1984FF),
                    ),
                  ),
                ),
              ),

              SizedBox(width: 8.w),

              // 打卡按钮
              GestureDetector(
                onTap: () {
                  // TODO: 实现打卡功能
                },
                child: Container(
                  padding: EdgeInsets.symmetric(horizontal: 18.w, vertical: 4.h),
                  decoration: BoxDecoration(
                    color: const Color(0xFF1984FF),
                    borderRadius: BorderRadius.circular(4.r),
                  ),
                  child: Text(
                    '打卡',
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: Colors.white,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 格式化日期显示
  String _formatDate(String? dateStr) {
    if (dateStr == null || dateStr.isEmpty) return '';

    try {
      final date = DateTime.parse(dateStr);
      return '${date.month.toString().padLeft(2, '0')}月${date.day.toString().padLeft(2, '0')}日';
    } catch (e) {
      return dateStr;
    }
  }
}
