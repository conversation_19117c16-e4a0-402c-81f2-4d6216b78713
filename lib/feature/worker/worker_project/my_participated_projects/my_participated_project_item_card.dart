import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/data/worker_data/worker_project_data/repo/model/dept_list_group_biz_model.dart';
import 'package:gdjg_pure_flutter/feature/group/group_pro_bill/entity/group_pro_bill_props.dart';
import 'package:gdjg_pure_flutter/feature/worker/project_detail/my_participated_project_detail/my_participated_project_detail_page.dart';
import 'package:gdjg_pure_flutter/feature/worker/project_setup/my_participated_project_setup/my_participated_project_setup_page.dart';
import 'package:gdjg_pure_flutter/init_module/init_route.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/utils/system_util/date_util.dart';

class MyParticipatedProjectItemCard extends StatelessWidget {
  final NetModelGroupABizModel item;
  final bool isAmountVisible;
  final double amount;
  final VoidCallback? onToggleVisibility;

  const MyParticipatedProjectItemCard({
    super.key,
    required this.item,
    this.isAmountVisible = false, // 默认隐藏
    this.amount = 0.0,
    this.onToggleVisibility,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        // 点击整个卡片进入我参与的项目详情页面
        YPRoute.openPage(
          RouteNameCollection.myParticipatedProjectDetail,
          params: MyParticipatedProjectDetailProps(deptId: item.deptId.toString()),
        );
      },
      child: Container(
        margin: EdgeInsets.only(bottom: 12.h),
        padding: EdgeInsets.only(left: 12.w, right: 12.w, top: 10.h, bottom: 8.h),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8.r),
        ),
        child: Column(
          children: [
            // 项目名称和日期
            Row(
              children: [
                Expanded(
                  child: Text(
                    item.name,
                    style: TextStyle(
                      fontSize: 22.sp,
                      fontWeight: FontWeight.w600,
                      color: const Color(0xFF333333),
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                Text(
                  DateUtil.formatRelativeDate(item.lastOperationDate),
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: const Color(0xFF999999),
                  ),
                ),
              ],
            ),

            SizedBox(height: 7.h),

            // 未结显示
            Row(
              children: [
                Text(
                  '未结：',
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: const Color(0xD9000000),
                  ),
                ),
                Text(
                  isAmountVisible ? amount.toStringAsFixed(2) : '****',
                  style: TextStyle(
                      fontSize: 20.sp, color: const Color(0xFF8A8A99), letterSpacing: -1.5),
                ),
                SizedBox(width: 4.w),
                GestureDetector(
                  onTap: onToggleVisibility,
                  child: Icon(
                    isAmountVisible ? Icons.visibility : Icons.visibility_off,
                    size: 16.w,
                    color: const Color(0xFF1984FF),
                  ),
                ),
                const Spacer(),
                // 向右箭头
                Icon(
                  Icons.arrow_forward_ios,
                  size: 14.w,
                  color: const Color(0xFF8A8A99),
                ),
              ],
            ),

            SizedBox(height: 8.h),

            // 分割线
            Container(
              height: 0.5.h,
              color: Colors.grey[300],
            ),

            SizedBox(height: 6.h),

            // 设置
            Row(
              children: [
                GestureDetector(
                  onTap: () {
                    YPRoute.openPage(
                      RouteNameCollection.participatedProjectSetup,
                      params: MyParticipatedProjectSetupProps(deptId: item.deptId.toString()),
                    );
                  },
                  child: Icon(
                    Icons.settings,
                    size: 21.w,
                    color: Colors.grey[600],
                  ),
                ),

                const Spacer(),

                // 查看记工按钮
                GestureDetector(
                  onTap: () {
                    if (item.workNoteId <= 0) {
                      return;
                    }

                    final params = GroupProBillProps(
                      workNoteId: item.workNoteId.toString(),
                      workNoteName: item.name,
                      deptId: item.deptId,
                      isJoin: true,
                    );

                    // 导航到统计页面
                    YPRoute.openPage(RouteNameCollection.groupProBill, params: params);
                  },
                  child: Container(
                    padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 4.h),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      border: Border.all(color: const Color(0xFF1984FF)),
                      borderRadius: BorderRadius.circular(4.r),
                    ),
                    child: Text(
                      '查看记工',
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: const Color(0xFF1984FF),
                      ),
                    ),
                  ),
                ),

                SizedBox(width: 8.w),

                // 打卡按钮
                GestureDetector(
                  onTap: () {
                    // TODO: 实现打卡功能（水印相机）
                  },
                  child: Container(
                    padding: EdgeInsets.symmetric(horizontal: 18.w, vertical: 4.h),
                    decoration: BoxDecoration(
                      color: const Color(0xFF1984FF),
                      borderRadius: BorderRadius.circular(4.r),
                    ),
                    child: Text(
                      '打卡',
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
