import 'package:get/get.dart';

/// 工人项目页面UI状态管理
/// 管理页面级别的UI状态
class WorkerProjectUS {
  /// 当前选中的Tab索引
  final _currentTabIndex = 0.obs;

  /// 轮播图加载状态
  final _bannerLoading = false.obs;

  // Getters
  int get currentTabIndex => _currentTabIndex.value;
  bool get bannerLoading => _bannerLoading.value;

  /// 设置当前Tab索引
  void setCurrentTabIndex(int index) {
    _currentTabIndex.value = index;
  }

  /// 设置轮播图加载状态
  void setBannerLoading(bool loading) {
    _bannerLoading.value = loading;
  }
}
