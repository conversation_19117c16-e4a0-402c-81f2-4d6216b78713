import 'dart:async';
import 'package:flutter/material.dart';
import 'package:gdjg_pure_flutter/feature/worker/worker_project/vm/worker_project_us.dart';
import 'package:gdjg_pure_flutter/utils/event_bus_util/event_bus_util.dart';

/// 项目页面ViewModel
/// 1. 处理页面级别的业务逻辑
/// 2. 管理TabController相关逻辑
/// 3. 处理轮播图相关业务
/// 4. 统一管理事件监听和分发
class WorkerProjectVM {
  final us = WorkerProjectUS();

  // 事件监听订阅
  StreamSubscription? _workerProjectEventSubscription;

  // 回调函数
  VoidCallback? _onMyParticipatedRefresh;
  VoidCallback? _onMyCreatedRefresh;
  VoidCallback? _onCompletedRefresh;

  /// 初始化
  void init() {
    _setupEventListeners();
  }

  /// 销毁
  void dispose() {
    _workerProjectEventSubscription?.cancel();
  }

  /// 设置各Tab页面的刷新回调
  void setRefreshCallbacks({
    VoidCallback? onMyParticipatedRefresh,
    VoidCallback? onMyCreatedRefresh,
    VoidCallback? onCompletedRefresh,
  }) {
    _onMyParticipatedRefresh = onMyParticipatedRefresh;
    _onMyCreatedRefresh = onMyCreatedRefresh;
    _onCompletedRefresh = onCompletedRefresh;
  }

  /// 设置事件监听
  void _setupEventListeners() {
    // 监听worker项目相关事件
    _workerProjectEventSubscription = EventBusUtil.collect<String>((eventType) {
      switch (eventType) {
        case 'invite_success':
          // 邀请加入成功，刷新"我参与"页面
          _onMyParticipatedRefresh?.call();
          break;
        case 'project_recover':
        case 'project_settle':
          // 项目恢复或设为已结清，刷新"我创建"、"我参与"和"已结清"页面
          _onMyCreatedRefresh?.call();
          _onMyParticipatedRefresh?.call();
          _onCompletedRefresh?.call();
          break;
        case 'project_update':
          // 项目名称更新，刷新"我创建"和"我参与"页面
          _onMyCreatedRefresh?.call();
          _onMyParticipatedRefresh?.call();
          break;
        case 'project_delete':
          // 项目删除，只刷新"已结清"页面
          _onCompletedRefresh?.call();
          break;
      }
    });
  }

  /// 切换Tab
  void onTabChanged(int index) {
    us.setCurrentTabIndex(index);
  }
}
