import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/feature/worker/worker_project/completed_projects/vm/completed_projects_vm.dart';
import 'package:get/get.dart';

/// 已结清项目Tab页面
class CompletedProjectsTab extends StatefulWidget {
  const CompletedProjectsTab({super.key});

  @override
  State<CompletedProjectsTab> createState() => _CompletedProjectsTabState();
}

class _CompletedProjectsTabState extends State<CompletedProjectsTab> with AutomaticKeepAliveClientMixin {
  late CompletedProjectsVM _viewModel;

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    _viewModel = CompletedProjectsVM();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // 必须调用以保持页面状态
    return Obx(() {
      if (_viewModel.completedProjectsUS.isLoading.value) {
        return const Center(
          child: CircularProgressIndicator(),
        );
      }

      if (_viewModel.completedProjectsUS.errorMessage.value.isNotEmpty) {
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [

              SizedBox(height: 16.h),
              Text(
                _viewModel.completedProjectsUS.errorMessage.value,
                style: TextStyle(
                  fontSize: 16.w,
                  color: Colors.grey[600],
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 16.h),
              ElevatedButton(
                onPressed: () {},
                child: const Text('重试'),
              ),
            ],
          ),
        );
      }

      // 空状态显示
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SizedBox(height: 16.h),
            Text(
              '已结清项目',
              style: TextStyle(
                fontSize: 18.w,
                fontWeight: FontWeight.w600,
                color: Colors.grey[600],
              ),
            ),
            SizedBox(height: 8.h),
            Text(
              '功能开发中，敬请期待',
              style: TextStyle(
                fontSize: 14.w,
                color: Colors.grey[500],
              ),
            ),
          ],
        ),
      );
    });
  }
}
