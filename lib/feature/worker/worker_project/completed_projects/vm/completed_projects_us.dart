import 'package:get/get.dart';

/// 已结清项目UI状态管理
class CompletedProjectsUS extends GetxController {

  /// 加载状态
  final isLoading = false.obs;

  /// 错误信息
  final errorMessage = ''.obs;

  /// 设置加载状态
  void setLoading(bool loading) {
    isLoading.value = loading;
  }

  /// 设置错误信息
  void setErrorMessage(String message) {
    errorMessage.value = message;
  }

  /// 清除错误信息
  void clearError() {
    errorMessage.value = '';
  }

  /// 重置状态
  void reset() {
    isLoading.value = false;
    errorMessage.value = '';
  }
}
