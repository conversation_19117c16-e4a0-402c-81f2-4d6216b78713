import 'package:get/get.dart';
import 'package:gdjg_pure_flutter/data/worker_data/worker_project_data/repo/model/personal_with_join_biz_model.dart';

/// 已结清项目UI状态管理
class CompletedProjectsUS extends GetxController {
  final _showProList = <NetModelPersonalWithJoinABizModel>[].obs;

  /// 金额显示状态（true-显示，false-隐藏）
  final _isAmountVisible = true.obs;

  // Getters
  RxList<NetModelPersonalWithJoinABizModel> get showProList => _showProList;
  RxBool get isAmountVisible => _isAmountVisible;

  /// 设置项目列表
  void setShowProList(List<NetModelPersonalWithJoinABizModel> list) {
    _showProList.value = list;
  }

  /// 设置金额显示状态
  void setAmountVisibility(bool visible) {
    _isAmountVisible.value = visible;
  }
}
