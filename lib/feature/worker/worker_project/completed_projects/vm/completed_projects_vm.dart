import 'package:gdjg_pure_flutter/feature/worker/worker_project/completed_projects/vm/completed_projects_us.dart';

/// 已结清项目ViewModel
class CompletedProjectsVM {

  /// UI状态管理
  final CompletedProjectsUS completedProjectsUS = CompletedProjectsUS();

  /// 初始化
  CompletedProjectsVM() {
    // 暂不加载数据
  }

  /// 获取数据（占位方法）
  Future<void> fetchData() async {
    try {
      completedProjectsUS.setLoading(true);
      completedProjectsUS.clearError();

      // TODO: 后续实现具体的数据获取逻辑
      await Future.delayed(const Duration(milliseconds: 500));

    } catch (e) {
      completedProjectsUS.setErrorMessage('网络错误：$e');
    } finally {
      completedProjectsUS.setLoading(false);
    }
  }

  /// 刷新数据
  Future<void> refreshData() async {
    await fetchData();
  }
}
