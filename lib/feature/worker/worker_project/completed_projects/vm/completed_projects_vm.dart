import 'package:gdjg_pure_flutter/data/worker_data/worker_project_data/ds/param/dept_put_away_dept_param_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/worker_project_data/repo/worker_project_repo.dart';
import 'package:gdjg_pure_flutter/data/worker_data/corp_select/repo/corp_select_repo.dart';
import 'package:gdjg_pure_flutter/data/worker_data/corp_select/ds/param/corp_select_param_model.dart';
import 'package:gdjg_pure_flutter/feature/worker/worker_project/completed_projects/vm/completed_projects_us.dart';
import 'package:gdjg_pure_flutter/utils/event_bus_util/event_bus_util.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/toast_util.dart';

/// 已结清项目ViewModel
class CompletedProjectsVM {
  WorkerProjectRepo rep = WorkerProjectRepo();
  final _corpSelectRepo = CorpSelectRepo();
  final CompletedProjectsUS completedProjectsUS = CompletedProjectsUS();

  CompletedProjectsVM();

  /// 查询已结清项目列表
  Future<void> queryProjectList() async {
    final result = await rep.queryCompletedProjectList();
    if (result.isOK()) {
      final list = result.getSucData()?.list ?? [];
      completedProjectsUS.setShowProList(list);
    } else {
      ToastUtil.showToast(result.fail?.errorMsg ?? '获取已结清项目列表失败');
    }
  }

  /// 刷新数据
  Future<void> onRefresh() async {
    await queryProjectList();
  }

  /// 切换金额显示状态
  void toggleAmountVisibility() {
    completedProjectsUS.setAmountVisibility(!completedProjectsUS.isAmountVisible.value);
  }

  /// 删除项目
  Future<void> deleteProject(String projectId, String corpId) async {
    // 先调用企业切换接口
    if (corpId.isNotEmpty) {
      final corpSelectParam = CorpSelectParamModel(
        corp_id: corpId,
      );

      // 调用企业切换接口，只关注成功状态
      await _corpSelectRepo.fetchCorpSelect(corpSelectParam);
    }

    // 再执行删除操作
    final result = await rep.deleteProject(projectId);

    if (result.isOK()) {
      ToastUtil.showToast('删除项目成功');
      EventBusUtil.emit<String>('project_delete');
    } else {
      ToastUtil.showToast('删除项目失败：${result.fail?.errorMsg ?? "未知错误"}');
    }
  }

  /// 恢复在建
  Future<void> recoverProject(String projectId, String corpId) async {
    // 企业切换
    if (corpId.isNotEmpty) {
      final corpSelectParam = CorpSelectParamModel(
        corp_id: corpId,
      );

      await _corpSelectRepo.fetchCorpSelect(corpSelectParam);
    }

    // 再执行恢复在建操作
    final param = DeptPutAwayDeptParamModel(
      dept_id: projectId,
      is_ignored: '0', // 0表示恢复在建
    );

    final result = await rep.putAwayDept(param);

    if (result.isOK()) {
      ToastUtil.showToast('恢复在建成功');
      EventBusUtil.emit<String>('project_recover');
    } else {
      ToastUtil.showToast('恢复在建失败：${result.fail?.errorMsg ?? "未知错误"}');
    }
  }
}
