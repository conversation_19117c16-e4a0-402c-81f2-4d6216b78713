import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/data/worker_data/worker_project_data/repo/model/personal_with_join_biz_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/worker_project_data/repo/model/project_get_project_list_biz_model.dart';
import 'package:gdjg_pure_flutter/feature/worker/project_detail/my_participated_project_detail/my_participated_project_detail_page.dart';
import 'package:gdjg_pure_flutter/feature/worker/project_detail/worker_project_detail/worker_project_detail_page.dart';
import 'package:gdjg_pure_flutter/generated/assets.dart';
import 'package:gdjg_pure_flutter/init_module/init_route.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/utils/system_util/date_util.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/common_dialog.dart';
import 'package:gdjg_pure_flutter/widget/show_delete_confirm_dialog.dart';

/// 已结清项目卡片
class CompletedProjectItemCard extends StatelessWidget {
  final NetModelPersonalWithJoinABizModel item;
  final bool isAmountVisible;
  final Function(String projectId, String corpId)? onDeleteProject;
  final Function(String projectId, String corpId)? onRecoverProject;

  const CompletedProjectItemCard({
    super.key,
    required this.item,
    required this.isAmountVisible,
    this.onDeleteProject,
    this.onRecoverProject,
  });

  /// 获取记工时间文本
  String _getBusinessDateRange() {
    if (item.firstBusinessDate.isEmpty) {
      return '无流水';
    }

    // 使用DateUtil格式化日期为年月日格式
    final firstDate = DateUtil.formatChineseDate(
      DateTime.tryParse(item.firstBusinessDate),
      showYear: true,
    );
    final lastDate = DateUtil.formatChineseDate(
      DateTime.tryParse(item.lastBusinessDate),
      showYear: true,
    );

    // 首笔记工时间-上次记工时间
    return '$firstDate-$lastDate';
  }

  /// 显示删除确认弹窗
  void _showDeleteConfirmDialog() {
    showDeleteConfirmDialog(
      onConfirm: () {
        _onDeleteProject();
      },
    );
  }

  /// 删除项目
  void _onDeleteProject() {
    final projectId = item.deptId.toInt().toString();
    final corpId = item.corpId.toInt().toString();
    onDeleteProject?.call(projectId, corpId);
  }

  /// 恢复在建确认弹窗
  void _showRecoverConfirmDialog() {
    showCommonDialog(CommonDialogConfig(
      title: '确认设为在建吗？',
      content: '设为在建后，将参与统计和展示',
      negative: '取消',
      positive: '确定',
      onPositive: () => _onRecoverProject(),
    ));
  }

  /// 恢复在建
  void _onRecoverProject() {
    final projectId = item.deptId.toInt().toString();
    final corpId = item.corpId.toInt().toString();
    onRecoverProject?.call(projectId, corpId);
  }

  /// 将已结清项目模型转换为项目详情所需模型
  ProjectGetProjectListABizModel _convertToProjectDetailModel() {
    return ProjectGetProjectListABizModel(
      id: item.workNoteId,
      name: item.name,
      deptId: item.deptId,
      corpId: item.corpId,
      status: 1.0, // 已结清状态
      lastBusinessDate: item.lastBusinessDate,
      lastOperationDate: item.lastOperationDate,
      startTime: item.firstBusinessDate,
      endTime: item.lastBusinessDate,
      hasBusiness: item.hasBusiness,
      workerNum: item.workerNum,
    );
  }

  /// 跳转到项目详情页面
  void _navigateToProjectDetail() {
    if (item.isSelfCreated == 1) {
      // 跳转到我创建项目详情页面
      final projectModel = _convertToProjectDetailModel();
      YPRoute.openPage(
        RouteNameCollection.myCreateProjectDetail,
        params: MyCreatedProjectDetailProps(
          projectModel: projectModel,
          isFromCompleted: true,
        ),
      );
    } else {
      // 跳转到我参与项目详情页面
      YPRoute.openPage(
        RouteNameCollection.myParticipatedProjectDetail,
        params: MyParticipatedProjectDetailProps(
          deptId: item.deptId.toString(),
          isFromCompleted: true,
        ),
      );
    }
  }

  Widget _buildActionButton({
    required String icon,
    required String text,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Image.asset(
            icon,
            width: 16.w,
            height: 16.h,
          ),
          SizedBox(width: 4.w),
          Text(
            text,
            style: TextStyle(
              fontSize: 12.sp,
              color: const Color(0xFF666666),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: _navigateToProjectDetail,
        borderRadius: BorderRadius.circular(8.r),
        child: Container(
          margin: EdgeInsets.only(bottom: 12.h),
          padding: EdgeInsets.only(top: 12.h, left: 12.w, right: 12.w, bottom: 8.h),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8.r),
          ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Container(
                height: 30.h,
                width: 60.w,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(4.r),
                  color: ColorsUtil.primaryColor15,
                  border: Border.all(
                    color: ColorsUtil.primaryColor,
                    width: 1.w,
                  ),
                ),
                alignment: Alignment.center,
                child: FittedBox(
                  fit: BoxFit.scaleDown,
                  child: Text(
                    item.isSelfCreated == 0 ? '我参与' : '我创建',
                    style: TextStyle(
                      color: ColorsUtil.primaryColor,
                      fontSize: 14.sp,
                      fontWeight: FontWeight.w600,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ),
              SizedBox(width: 6.w),
              Expanded(
                child: Text(
                  item.name,
                  style: TextStyle(
                    fontSize: 20.sp,
                    fontWeight: FontWeight.w800,
                    color: const Color(0xFF333333),
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),

          SizedBox(height: 12.h),

          Row(
            children: [
              // 记工时间范围
              Expanded(
                child: Text(
                  _getBusinessDateRange(),
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: const Color(0xFF666666),
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),

              SizedBox(width: 8.w),

              Icon(
                Icons.arrow_forward_ios,
                size: 14.w,
                color: const Color(0xFF999999),
              ),
            ],
          ),

          SizedBox(height: 8.h),

          Container(
            height: 1.h,
            color: const Color(0xFFE0E0E0),
          ),

          SizedBox(height: 8.h),

          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              // 恢复在建按钮
              _buildActionButton(
                icon: Assets.workerIconRecover,
                text: '恢复在建',
                onTap: () {
                  _showRecoverConfirmDialog();
                },
              ),

              // 删除项目按钮 - 只有我创建的项目才显示
              if (item.isSelfCreated == 1) ...[
                SizedBox(width: 16.w),
                _buildActionButton(
                  icon: Assets.workerIconDelete,
                  text: '删除项目',
                  onTap: () {
                    _showDeleteConfirmDialog();
                  },
                ),
              ],
            ],
          ),
        ],
      ),
        ),
      ),
    );
  }
}
