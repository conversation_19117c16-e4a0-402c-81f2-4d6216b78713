import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/feature/worker/worker_project/my_created_projects/my_created_projects_tab.dart';
import 'package:gdjg_pure_flutter/feature/worker/worker_project/my_participated_projects/my_participated_projects_tab.dart';
import 'package:gdjg_pure_flutter/feature/worker/worker_project/completed_projects/completed_projects_tab.dart';
import 'package:gdjg_pure_flutter/feature/worker/worker_project/vm/worker_project_vm.dart';
import 'package:gdjg_pure_flutter/utils/route_util/base_page/base_ful_page.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';
import 'package:gdjg_pure_flutter/widget/network_carousel_widget.dart';

/// 工人项目页面
class WorkerProjectPage extends BaseFulPage {
  const WorkerProjectPage({super.key}) : super(appBar: null);

  @override
  State<WorkerProjectPage> createState() => _WorkerProjectPageState();
}

class _WorkerProjectPageState extends BaseFulPageState<WorkerProjectPage> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  late WorkerProjectVM _viewModel;
  final List<String> _tabLabels = ['我创建', '我参与', '已结清'];

  // 刷新回调函数
  VoidCallback? _myCreatedProjectsRefreshCallback;

  @override
  void onPageCreate() {
    super.onPageCreate();
    _viewModel = WorkerProjectVM();
    _tabController = TabController(
      length: _tabLabels.length,
      vsync: this,
    );
    _tabController.addListener(() {
      if (!_tabController.indexIsChanging) {
        _viewModel.onTabChanged(_tabController.index);
      }
    });
    _viewModel.init();
  }

  @override
  void onPageDestroy() {
    _tabController.dispose();
    super.onPageDestroy();
  }

  @override
  void onReceiveFromNext(Object? backData) {
    super.onReceiveFromNext(backData);
    // 从项目设置页面返回的数据
    if (backData is Map && backData['refresh'] == true) {
      if (_tabController.index == 0) {
        // 刷新我创建页面列表数据
        _myCreatedProjectsRefreshCallback?.call();
      }
    }
  }

  @override
  Widget yBuild(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      body: SafeArea(
        child: Column(
          children: [
            // 轮播图
            _buildBannerSection(),

            Expanded(
              child: Column(
                children: [
                  // Tab栏
                  _buildTabBar(),

                  // Tab内容
                  Expanded(
                    child: _buildTabContent(),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建轮播图区域
  Widget _buildBannerSection() {
    return Container(
      color: Colors.white,
      padding: EdgeInsets.only(top: 14.h, left: 16.w, right: 16.w, bottom: 2.h),
      child: NetworkCarouselWidget(
        code: 'JGJZ_HOME_PERSONAL_BANER',
        height: 48.h,
      ),
    );
  }

  /// 构建Tab栏
  Widget _buildTabBar() {
    return Container(
      color: Colors.white,
      padding: EdgeInsets.symmetric(horizontal: 10.w),
      child: TabBar(
        controller: _tabController,
        dividerColor: Colors.transparent,
        tabs: _tabLabels.map((label) => Tab(text: label)).toList(),
        labelColor: ColorsUtil.primaryColor,
        unselectedLabelColor: const Color(0xFF666666),
        labelStyle: TextStyle(
          fontSize: 18.w,
          fontWeight: FontWeight.w600,
        ),
        unselectedLabelStyle: TextStyle(
          fontSize: 18.sp,
          fontWeight: FontWeight.w400,
        ),
        indicatorColor: ColorsUtil.primaryColor,
        indicatorSize: TabBarIndicatorSize.label,
      ),
    );
  }

  /// 构建Tab内容
  Widget _buildTabContent() {
    return Container(
      color: const Color(0xFFF5F5F5),
      child: TabBarView(
        controller: _tabController,
        children: [
          // 我创建
          MyCreatedProjectsTab(
            onRefreshCallback: (callback) => _myCreatedProjectsRefreshCallback = callback,
          ),

          // 我参与
          const MyParticipatedProjectsTab(),

          // 已结清
          const CompletedProjectsTab(),
        ],
      ),
    );
  }
}