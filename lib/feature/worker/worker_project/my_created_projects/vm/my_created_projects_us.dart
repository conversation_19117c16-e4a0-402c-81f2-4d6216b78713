import 'package:gdjg_pure_flutter/data/worker_data/worker_project_data/repo/model/project_get_project_list_biz_model.dart';
import 'package:get/get.dart';

class MyCreatedProjectsUS {
  final _showProList = <ProjectGetProjectListABizModel>[].obs;

  /// 金额显示状态（true-显示，false-隐藏）
  final _isAmountVisible = true.obs;

  /// 总未结金额
  final _totalUnsettledAmount = 0.0.obs;

  // Getters
  RxList<ProjectGetProjectListABizModel> get showProList => _showProList;
  RxBool get isAmountVisible => _isAmountVisible;
  RxDouble get totalUnsettledAmount => _totalUnsettledAmount;

  /// 设置项目列表
  void setShowProList(List<ProjectGetProjectListABizModel> list) {
    _showProList.value = list;
  }

  /// 设置金额显示状态
  void setAmountVisibility(bool visible) {
    _isAmountVisible.value = visible;
  }

  /// 设置总未结金额
  void setTotalUnsettledAmount(double amount) {
    _totalUnsettledAmount.value = amount;
  }
}
