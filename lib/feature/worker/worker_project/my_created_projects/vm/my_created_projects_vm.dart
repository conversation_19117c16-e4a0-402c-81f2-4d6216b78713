import 'package:flutter/material.dart';
import 'package:gdjg_pure_flutter/data/worker_data/worker_project_data/ds/model/param/dept_create_dept_req_entity.dart';
import 'package:gdjg_pure_flutter/data/worker_data/worker_project_data/ds/model/param/project_get_project_list_param_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/worker_project_data/repo/worker_project_repo.dart';

import 'package:gdjg_pure_flutter/feature/worker/worker_project/my_created_projects/vm/my_created_projects_us.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/toast_util.dart';

class MyCreatedProjectsVM {
  WorkerProjectRep rep = WorkerProjectRep();
  MyCreatedProjectsUS us = MyCreatedProjectsUS();

  /// 查询项目列表
  Future<void> queryProjectList() async {
    final result = await rep.queryProjectList(ProjectGetProjectListParamModel(
      identity: '2',
      is_ignore: '0',
    ));

    if (result.isOK()) {
      us.setShowProList(result.getSucData()?.doing?.list ?? []);
    } else {
      ToastUtil.showToast(result.fail?.errorMsg ?? '获取项目列表失败');
    }
  }

  /// 创建新项目
  Future<void> createNewProject(String projectName, {VoidCallback? onSuccess}) async {
    final result = await rep.createProject(DeptCreateDeptReqEntity(
      name: projectName,
      identity: '2', // 个人项目
    ));

    if (result.isOK()) {
      // 刷新项目列表
      await queryProjectList();
      // 执行成功回调
      onSuccess?.call();
      ToastUtil.showToast('项目创建成功');
    } else {
      ToastUtil.showToast(result.fail?.errorMsg ?? '创建失败');
    }
  }

  /// 刷新数据
  Future<void> onRefresh() async {
    await queryProjectList();
  }

  /// 切换金额显示状态
  void toggleAmountVisibility() {
    us.setAmountVisibility(!us.isAmountVisible.value);
  }
}


