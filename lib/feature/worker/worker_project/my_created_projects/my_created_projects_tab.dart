import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:gdjg_pure_flutter/feature/worker/worker_project/my_created_projects/vm/my_created_projects_vm.dart';
import 'package:gdjg_pure_flutter/feature/worker/worker_project/my_created_projects/create_project_dialog.dart';
import 'package:gdjg_pure_flutter/feature/worker/worker_project/my_created_projects/my_created_project_item_card.dart';
import 'package:get/get.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/generated/assets.dart';


/// 我创建的项目Tab页面
class MyCreatedProjectsTab extends StatefulWidget {
  final Function(VoidCallback)? onRefreshCallback;

  const MyCreatedProjectsTab({super.key, this.onRefreshCallback});

  @override
  State<MyCreatedProjectsTab> createState() => MyCreatedProjectsTabState();
}

class MyCreatedProjectsTabState extends State<MyCreatedProjectsTab> with AutomaticKeepAliveClientMixin {
  late MyCreatedProjectsVM _viewModel;
  late RefreshController _refreshController;

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    _viewModel = MyCreatedProjectsVM();
    _refreshController = RefreshController(initialRefresh: false);

    // 注册刷新回调
    widget.onRefreshCallback?.call(_refreshData);
  }

  /// 获取ViewModel引用，给page使用检查是否为空
  MyCreatedProjectsVM getViewModel() {
    return _viewModel;
  }

  @override
  void dispose() {
    _refreshController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // 必须调用以保持页面状态
    return SmartRefresher(
      controller: _refreshController,
      enablePullDown: true,
      enablePullUp: false,
      onRefresh: _onRefresh,
      child: CustomScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        slivers: [
          // 新建项目按钮区域
          SliverToBoxAdapter(
            child: _buildCreateProjectSection(),
          ),

          // 项目列表区域
          _buildProjectListSliver(),

          // 使用教程
          SliverToBoxAdapter(
            child: _buildReservedSpace(),
          ),
        ],
      ),
    );
  }

  /// 新建项目行
  Widget _buildCreateProjectSection() {
    return Container(
      padding: EdgeInsets.only(left: 16.w, top: 10.h, bottom: 10.h, right: 16.w),
      child: Row(
        children: [
          _buildNewProjectButton(),
          const Spacer(),
          _buildTotalAmountSection(),
        ],
      ),
    );
  }

  /// 新建项目按钮
  Widget _buildNewProjectButton() {
    return GestureDetector(
      onTap: _showCreateProjectDialog,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(5.w),
          border: Border.all(
            color: ColorsUtil.primaryColor,
            width: 1,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.add,
              color: ColorsUtil.primaryColor,
              size: 20.w,
            ),
            SizedBox(width: 4.w),
            Text(
              '新建项目',
              style: TextStyle(
                color: ColorsUtil.primaryColor,
                fontSize: 14.w,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 总未结金额
  Widget _buildTotalAmountSection() {
    return Obx(() => Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          '总未结：',
          style: TextStyle(
            fontSize: 14.sp,
            color: const Color(0xFF7B7D81),
            fontWeight: FontWeight.w500,
            height: 1.0,
          ),
        ),
        Text(
          _viewModel.us.isAmountVisible.value
              ? _viewModel.us.totalUnsettledAmount.value.toStringAsFixed(2)
              : '****',
          style: TextStyle(
            fontSize: 22.sp,
            color: const Color(0xFF1984FF),
            letterSpacing: -1.5,
          ),
        ),
        SizedBox(width: 2.w),
        GestureDetector(
          onTap: () {
            _viewModel.toggleAmountVisibility();
          },
          child: _viewModel.us.isAmountVisible.value
              ? Icon(
                  Icons.visibility,
                  size: 18.w,
                  color: const Color(0xFF1984FF),
                )
              : Image.asset(
                  Assets.groupWaaSvgProEyeClose,
                  width: 18.w,
                  height: 18.w,
                ),
        ),
      ],
    ));
  }

  /// 项目列表 Sliver
  Widget _buildProjectListSliver() {
    return Obx(() {
      // 空列表
      if (_viewModel.us.showProList.isEmpty) {
        return SliverToBoxAdapter(
          child: Container(
            width: double.infinity,
            padding: EdgeInsets.only(top: 70.h, bottom: 50.h),
            child: Column(
              children: [
                Image.asset(
                  Assets.commonIconEmptyTeamProject,
                  width: 120.w,
                  height: 120.w,
                  fit: BoxFit.contain,
                ),
                Text(
                  '暂未创建项目',
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: const Color(0xFF8A8A99),
                  ),
                ),
              ],
            ),
          ),
        );
      }

      // 有数据时显示项目列表
      return SliverPadding(
        padding: EdgeInsets.symmetric(horizontal: 16.w),
        sliver: SliverList(
          delegate: SliverChildBuilderDelegate(
            (context, index) {
              final project = _viewModel.us.showProList[index];
              return Obx(() => MyCreatedProjectItemCard(
                item: project,
                isAmountVisible: _viewModel.us.isAmountVisible.value,
              ));
            },
            childCount: _viewModel.us.showProList.length,
          ),
        ),
      );
    });
  }



  /// 使用教程
  Widget _buildReservedSpace() {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w),
      padding: EdgeInsets.only(bottom: 20.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题区域
          Row(
            children: [
              Container(
                width: 2.w,
                height:20.h,
                decoration: BoxDecoration(
                  color: const Color(0xFF1984FF),
                  borderRadius: BorderRadius.circular(2.w),
                ),
              ),
              SizedBox(width: 8.w),
              // 标题文字
              Text(
                '这些教程轻松帮你解决记工问题',
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w600,
                  color: const Color(0xFF333333),
                ),
              ),
            ],
          ),

          SizedBox(height: 16.h),

          //使用视频
          _buildTutorialGrid(),
        ],
      ),
    );
  }

  /// 教程视频
  Widget _buildTutorialGrid() {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 12.w,
        mainAxisSpacing: 12.h,
        childAspectRatio: 2.5,
      ),
      itemCount: 4,
      itemBuilder: (context, index) {
        return _buildTutorialItem(index);
      },
    );
  }

  /// 构建教程
  Widget _buildTutorialItem(int index) {
    final tutorials = [
      '如何创建项目',
      '记工操作指南',
      '工资结算流程',
      '数据统计查看',
    ];

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(6.r),
        border: Border.all(
          color: Colors.grey[300]!,
          width: 1,
        ),
      ),
      child: Center(
        child: Text(
          tutorials[index],
          style: TextStyle(
            fontSize: 14.sp,
            color: const Color(0xFF666666),
            fontWeight: FontWeight.w500,
          ),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }

  /// 处理刷新
  Future<void> _onRefresh() async {
    try {
      await _viewModel.onRefresh();
      _refreshController.refreshCompleted();
    } catch (e) {
      _refreshController.refreshFailed();
    }
  }

  //外部刷新数据回调
  Future<void> _refreshData() async {
    await _viewModel.onRefresh();
  }

  /// 显示新建项目弹窗
  void _showCreateProjectDialog() {
    YPRoute.openDialog(
      builder: (context) => CreateProjectDialog(viewModel: _viewModel),
      maskColor: ColorsUtil.black50,
      clickMaskDismiss: false,
      backType: SmartBackType.ignore,
    );
  }
}
