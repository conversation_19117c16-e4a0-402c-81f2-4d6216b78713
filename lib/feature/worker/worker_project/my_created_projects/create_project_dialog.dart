import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/toast_util.dart';

import 'vm/my_created_projects_vm.dart';

/// 新建个人项目弹窗
class CreateProjectDialog extends StatefulWidget {
  final MyCreatedProjectsVM viewModel;

  const CreateProjectDialog({
    super.key,
    required this.viewModel,
  });

  @override
  State<CreateProjectDialog> createState() => _CreateProjectDialogState();
}

class _CreateProjectDialogState extends State<CreateProjectDialog> {
  final TextEditingController _projectNameController = TextEditingController();
  final FocusNode _focusNode = FocusNode();
  late MyCreatedProjectsVM _viewModel;

  @override
  void initState() {
    super.initState();
    _viewModel = widget.viewModel;
    // 弹窗显示后自动聚焦
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _focusNode.requestFocus();
    });
  }

  @override
  void dispose() {
    _projectNameController.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 320.w,
      margin: EdgeInsets.symmetric(horizontal: 20.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 标题
          _buildTitle(),
          
          // 输入框区域
          _buildInputSection(),
          
          // 按钮区域
          _buildButtonSection(),
        ],
      ),
    );
  }

  /// 构建标题
  Widget _buildTitle() {
    return Container(
      padding: EdgeInsets.only(top: 24.h, bottom: 16.h),
      child: Text(
        '新建个人项目',
        style: TextStyle(
          fontSize: 18.sp,
          color: ColorsUtil.black85,
        ),
      ),
    );
  }

  /// 构建输入区域
  Widget _buildInputSection() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 20.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 项目名称标签
          Text(
            '项目名称：',
            style: TextStyle(
              fontSize: 18.sp,
              color: ColorsUtil.black85,
            ),
          ),
          
          SizedBox(height: 8.h),
          
          // 输入框
          Container(
            height: 40.h,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(6.r),
              border: Border.all(
                color: Colors.grey[300]!,
                width: 1,
              ),
            ),
            child: TextField(
              controller: _projectNameController,
              focusNode: _focusNode,
              style: TextStyle(
                fontSize: 14.sp,
                color: ColorsUtil.black85,
              ),
              decoration: InputDecoration(
                hintText: '请输入项目名称',
                hintStyle: TextStyle(
                  fontSize: 14.sp,
                  color: ColorsUtil.hintFontColor,
                ),
                border: InputBorder.none,
                contentPadding: EdgeInsets.symmetric(
                  horizontal: 12.w,
                  vertical: 10.h,
                ),
              ),
              maxLength: 50,
              buildCounter: (context, {required currentLength, required isFocused, maxLength}) {
                return null;
              },
            ),
          ),
          
          SizedBox(height: 24.h),
        ],
      ),
    );
  }

  /// 构建按钮区域
  Widget _buildButtonSection() {
    return SizedBox(
      height: 44.h,
      child: Row(
        children: [
          // 取消按钮
          Expanded(
            child: _buildCancelButton(),
          ),

          // 创建按钮
          Expanded(
            child: _buildCreateButton(),
          ),
        ],
      ),
    );
  }

  /// 取消按钮
  Widget _buildCancelButton() {
    return GestureDetector(
      onTap: () {
        YPRoute.closeDialog();
      },
      child: Container(
        height:44.h,
        decoration: BoxDecoration(
          color: Colors.white,
          border: Border(
            top: BorderSide(
              color: Colors.grey[300]!,
              width: 1,
            ),
            right: BorderSide(
              color: Colors.grey[300]!,
              width: 0.5,
            ),
          ),
          borderRadius: BorderRadius.only(
            bottomLeft: Radius.circular(12.r),
          ),
        ),
        child: Center(
          child: Text(
            '取消',
            style: TextStyle(
              fontSize: 14.sp,
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ),
    );
  }

  /// 创建按钮
  Widget _buildCreateButton() {
    return GestureDetector(
      onTap: _handleCreateProject,
      child: Container(
        height: 44.h,
        decoration: BoxDecoration(
          color: Colors.white,
          border: Border.all(
            color: Colors.grey[300]!,
            width: 1,
          ),
          borderRadius: BorderRadius.only(
            bottomRight: Radius.circular(12.r),
          ),
        ),
        child: Center(
          child: Text(
            '创建',
            style: TextStyle(
              fontSize: 14.sp,
              color: ColorsUtil.primaryColor,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ),
    );
  }

  /// 处理创建项目
  void _handleCreateProject() {
    final projectName = _projectNameController.text.trim();

    if (projectName.isEmpty) {
      ToastUtil.showToast('请输入项目名称');
      return;
    }

    if (projectName.length < 2) {
      ToastUtil.showToast('项目名称至少需要2个字符');
      return;
    }

    // 创建项目，成功后关闭弹窗
    _viewModel.createNewProject(projectName, onSuccess: () {
      YPRoute.closeDialog();
    });
  }
}
