import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/data/worker_data/worker_project_data/repo/model/project_get_project_list_biz_model.dart';

class MyCreatedProjectItemCard extends StatelessWidget {

  final ProjectGetProjectListABizModel item;
  final bool isAmountVisible;

  const MyCreatedProjectItemCard({
    super.key,
    required this.item,
    this.isAmountVisible = true,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(bottom: 12.h),
      padding: EdgeInsets.only(left: 12.w, right: 12.w, top: 10.h, bottom: 8.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Column(
        children: [
          // 项目名称和日期
          Row(
            children: [
              Expanded(
                child: Text(
                  item.name,
                  style: TextStyle(
                    fontSize: 22.sp,
                    fontWeight: FontWeight.w600,
                    color: const Color(0xFF333333),
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              Text(
                _formatDate(item.lastOperationDate),
                style: TextStyle(
                  fontSize: 14.sp,
                  color: const Color(0xFF999999),
                ),
              ),
            ],
          ),

          SizedBox(height: 7.h),

          // 未结显示
          Row(
            children: [
              Text(
                '未结：',
                style: TextStyle(
                  fontSize: 14.sp,
                  color: const Color(0xD9000000),
                ),
              ),
              Text(
                isAmountVisible ? item.unsettled.toStringAsFixed(2) : '****',
                style: TextStyle(
                  fontSize: 20.sp,
                  color: const Color(0xFF8A8A99),
                  letterSpacing: -1.5
                ),
              ),
              const Spacer(),
            ],
          ),

          SizedBox(height: 8.h),

          // 分割线
          Container(
            height: 0.5.h,
            color: Colors.grey[300],
          ),

          SizedBox(height: 6.h),

          // 设置
          Row(
            children: [
              GestureDetector(
                onTap: null,
                child: Icon(
                  Icons.settings,
                  size: 21.w,
                  color: Colors.grey[600],
                ),
              ),

              const Spacer(),

              GestureDetector(
                onTap: null,
                child: Container(
                  padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 4.h),
                  decoration: BoxDecoration(
                    border: Border.all(color: const Color(0xFFFF9500)),
                    borderRadius: BorderRadius.circular(4.r),
                  ),
                  child: Text(
                    '借支/结算',
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: const Color(0xFFFF9500),
                    ),
                  ),
                ),
              ),

              SizedBox(width: 8.w),

              // 记工按钮
              GestureDetector(
                onTap: () {
                  // 跳转到个人记工页面
                  //YPRoute.openPage(RouteNameCollection.personalRecordWork, params: {'deptId': item.deptId});
                },
                child: Container(
                  padding: EdgeInsets.symmetric(horizontal: 18.w, vertical: 4.h),
                  decoration: BoxDecoration(
                    color: const Color(0xFF007AFF),
                    borderRadius: BorderRadius.circular(4.r),
                  ),
                  child: Text(
                    '记工',
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: Colors.white,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 格式化日期显示
  String _formatDate(String? dateStr) {
    if (dateStr == null || dateStr.isEmpty) return '';

    try {
      final date = DateTime.parse(dateStr);
      return '${date.month.toString().padLeft(2, '0')}月${date.day.toString().padLeft(2, '0')}日';
    } catch (e) {
      return dateStr;
    }
  }
}
