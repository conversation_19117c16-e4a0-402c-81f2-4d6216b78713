import 'package:gdjg_pure_flutter/feature/group/common_uistate/statistics_ui_state.dart';
import 'package:gdjg_pure_flutter/data/calendar/model/job/net_model_job_biz_model.dart';
import 'package:gdjg_pure_flutter/data/calendar/model/machine/net_model_machine_biz_model.dart';
import 'package:gdjg_pure_flutter/data/quick_link_data/repo/model/quick_link_entity.dart';
import 'package:gdjg_pure_flutter/feature/worker/worker_calendar/page/calendar_tab/widget/machine_list_item.dart';
import 'package:gdjg_pure_flutter/feature/worker/worker_calendar/page/calendar_tab/widget/quick_link/quick_link_ui_state.dart';
import 'package:gdjg_pure_flutter/feature/worker/worker_calendar/page/calendar_tab/widget/recruit_list_item.dart';
import 'package:gdjg_pure_flutter/widget/calendar/dayevent/DayEvent.dart';
import 'package:get/get.dart';

class WorkCalendarUS {
  final _currentDate = DateTime.now().obs;
  final _startDate = DateTime.now().copyWith(year: 2000).obs;
  final _endDate = DateTime.now().obs;
  final RxMap<DateTime, List<DayEvent>> _events =
      <DateTime, List<DayEvent>>{}.obs;

  final _statisticsList = List<StatisticsItemUIState>.empty().obs;

  /// 当前选中的
  final Rx<QuickLinkDest?> _curQuickLink = QuickLinkDest.show_job_list.obs;

  final _quickLinkList = List<QuickLinkEntity>.empty().obs;

  RxList<RecruitItemUiState> _recruitList =
      List<RecruitItemUiState>.empty().obs;

  final _machineList = List<MachineItemUiState>.empty().obs;

  var recruitListPage = 1;

  final _recruitHasMore = true.obs;

  var machineListPage = 1;

  final _machineHasMore = true.obs;

  DateTime get currentDate => _currentDate.value;

  DateTime get startDate => _startDate.value;

  DateTime get endDate => _endDate.value;

  Map<DateTime, List<DayEvent>> get events => _events.value;

  List<StatisticsItemUIState> get statisticsList => _statisticsList.value;

  List<QuickLinkUiState> get quickLinkList => _quickLinkList.value.map((i) {
        return QuickLinkUiState(
            id: i.code, title: i.title, icon: i.icon, original: i);
      }).toList();

  List<RecruitItemUiState> get recruitList => _recruitList.value;

  List<MachineItemUiState> get machineList => _machineList.value;

  QuickLinkDest? get curQuickLink => _curQuickLink.value;

  bool get showRecruitList =>
      _curQuickLink.value == QuickLinkDest.show_job_list;

  bool get showMachineList =>
      _curQuickLink.value == QuickLinkDest.show_machine_list;

  bool get hasMore => _curQuickLink.value == QuickLinkDest.show_job_list
      ? _recruitHasMore.value
      : _machineHasMore.value;

  void setCurrentDate(DateTime date) {
    _currentDate.value = date;
  }

  void setStartDate(DateTime date) {
    _startDate.value = date;
  }

  void setEvents(Map<DateTime, List<DayEvent>> events) {
    _events.value = events;
  }

  void setStatisticsList(List<StatisticsItemUIState> list) {
    _statisticsList.value = list;
  }

  void setQuickLinkList(List<QuickLinkEntity> list) {
    _quickLinkList.value = list;
  }

  void setRecruitList(int page, List<NetModelJobABizModel> data) {
    this.recruitListPage = page;
    if (page > 1 && data.isEmpty) {
      _recruitHasMore.value = false;
      return;
    }
    final list = data.map((i) {
      return RecruitItemUiState(
          id: i.id.toString(),
          title: i.title,
          tag: i.showLabelV2.map((t) {
            return RecruitTag(
              name: t.name,
              type: t.type.toString(),
            );
          }).toList(),
          location: i.showAddress,
          time: i.timeStr);
    }).toList();
    if (page == 1) {
      _recruitList.value = list;
      return;
    }
    _recruitList.value = [..._recruitList.value, ...list];
  }

  void setMachineList(int page, List<NetModelMachineABizModel> data) {
    this.machineListPage = page;
    if (page > 1 && data.isEmpty) {
      _recruitHasMore.value = false;
      return;
    }
    final list = data.map((i) {
      return MachineItemUiState(
          id: i.uu.toString(),
          title: i.title,
          tag: i.tag.map((t) {
            return MachineTag(
              name: t,
              type: t,
            );
          }).toList(),
          mode: i.mode,
          location: i.area,
          time: i.time);
    }).toList();
    if (page == 1) {
      _machineList.value = list;
      return;
    }
    _machineList.value = [..._machineList.value, ...list];
  }

  void setCurQuickLink(QuickLinkDest? dest) {
    _curQuickLink.value = dest;
  }
}
