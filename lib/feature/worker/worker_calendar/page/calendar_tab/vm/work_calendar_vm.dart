import 'package:gdjg_pure_flutter/data/calendar/repo/model/business_get_project_calendar_count_biz_model.dart';
import 'package:gdjg_pure_flutter/feature/group/common_uistate/statistice_helper.dart';
import 'package:gdjg_pure_flutter/feature/group/common_uistate/statistics_ui_state.dart';
import 'package:gdjg_pure_flutter/data/calendar/ds/model/param/business_get_project_calendar_count_param_model.dart';
import 'package:gdjg_pure_flutter/data/calendar/model/job/net_model_job_param_model.dart';
import 'package:gdjg_pure_flutter/data/calendar/model/machine/net_model_machine_param_model.dart';
import 'package:gdjg_pure_flutter/data/calendar/repo/calendar_repo.dart';
import 'package:gdjg_pure_flutter/data/quick_link_data/repo/model/quick_link_entity.dart';
import 'package:gdjg_pure_flutter/data/quick_link_data/repo/quick_link_repo.dart';
import 'package:gdjg_pure_flutter/feature/worker/worker_calendar/page/calendar_tab/vm/work_calendar_us.dart';
import 'package:gdjg_pure_flutter/feature/worker/worker_calendar/page/calendar_tab/widget/machine_list_item.dart';
import 'package:gdjg_pure_flutter/feature/worker/worker_calendar/page/calendar_tab/widget/quick_link/quick_link_ui_state.dart';
import 'package:gdjg_pure_flutter/feature/worker/worker_calendar/page/calendar_tab/widget/recruit_list_item.dart';
import 'package:gdjg_pure_flutter/model/RecordNoteType.dart';
import 'package:gdjg_pure_flutter/model/RecordType.dart';
import 'package:gdjg_pure_flutter/utils/refresh_loadmore/refresh_loadmore_vvm.dart';
import 'package:gdjg_pure_flutter/utils/system_util/date_util.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/calendar_uistate_helper.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/toast_util.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

class WorkCalendarVm {
  final us = WorkCalendarUS();
  final RefreshController refreshController =
      RefreshController(initialRefresh: false);
  late RefreshLoadMoreVvm<RecruitItemUiState> recruitRefresh;

  late RefreshLoadMoreVvm<MachineItemUiState> machineRefresh;

  final calendarRepo = CalendarRepo();

  final quickLinkRepo = QuickLinkRepo();

  WorkCalendarVm() {
    // 招工列表刷新
    recruitRefresh = RefreshLoadMoreVvm(
        refreshController: this.refreshController,
        dataFetcher: (page, pageSize) {
          return queryRecruitList(page: page);
        });
    // 机器机械列表刷新
    machineRefresh = RefreshLoadMoreVvm(
        refreshController: this.refreshController,
        dataFetcher: (page, pageSize) {
          return queryMechanicalList(page: page);
        });
    sync();
  }

  sync() async {
    ToastUtil.showLoading();
    // 调用接口更新事件
    us.setStatisticsList([
      StatisticsItemUIState(recordType: RwaRecordType.workDays),
      StatisticsItemUIState(recordType: RwaRecordType.workLoad),
      StatisticsItemUIState(recordType: RwaRecordType.dailyWages),
      StatisticsItemUIState(recordType: RwaRecordType.debt),
      StatisticsItemUIState(recordType: RwaRecordType.expenditure),
    ]);
    queryCalendarCount();
    // 获取金刚区数据
    queryQuickLinkList();
    // 初始列表数据
    recruitRefresh.refresh();
    machineRefresh.refresh();

    ToastUtil.hideLoading();
  }

  void onRefresh() async {
    // 判断当前是哪个tab，执行刷新时仅刷新此tab的列表数据
    if (us.curQuickLink == QuickLinkDest.show_job_list) {
      recruitRefresh.refresh();
    } else if (us.curQuickLink == QuickLinkDest.show_machine_list) {
      machineRefresh.refresh();
    }
    queryCalendarCount();
  }

  void onLoading() async {
    // 判断当前是哪个tab，执行刷新时仅刷新此tab的列表数据
    if (us.curQuickLink == QuickLinkDest.show_job_list) {
      recruitRefresh.loadMore();
    } else if (us.curQuickLink == QuickLinkDest.show_machine_list) {
      machineRefresh.loadMore();
    }
  }

  /// 获取金刚区数据
  queryQuickLinkList() async {
    final resp = await quickLinkRepo.getQuickLinkData(RecordNoteType.personal);
    if (resp.isOK()) {
      us.setQuickLinkList(resp.getSucData() ?? []);
    }
  }

  /// 获取招工列表数据
  Future<List<RecruitItemUiState>> queryRecruitList({required int page}) async {
    final resp = await calendarRepo
        .queryRecruitList(NetModelJobParamModel(page: page.toString()));
    if (resp.isOK()) {
      final data = resp.getSucData()?.list ?? [];
      final list = data.map((i) {
        return RecruitItemUiState(
            id: i.id.toString(),
            title: i.title,
            tag: i.showLabelV2.map((t) {
              return RecruitTag(
                name: t.name,
                type: t.type.toString(),
              );
            }).toList(),
            location: i.showAddress,
            time: i.timeStr);
      }).toList();
      return list;
    }
    return List.empty();
  }

  /// 获取招工列表数据
  Future<void> queryCalendarCount() async {
    var param = BusinessGetProjectCalendarCountParamModel(
      start_time: DateUtil.formatStartDate(us.endDate),
      end_time: DateUtil.formatEndDate(us.endDate),
    );
    final result = await calendarRepo.getProjectCalendarCount(param);
    if (result.isOK()) {
      //统计数据转换
      _convertEntityToUIState(result.getSucData());

      //大日历数据转换
      var events = CalendarUIStateHelper.convertEntityToCalendarUIState(
          result.getSucData()?.calendar ?? []);
      us.setEvents(events);
    }
  }

  ///统计数据转换
  void _convertEntityToUIState(BusinessGetProjectCalendarCountBizModel? sucData) {
    List<StatisticsItemUIState> list = StatisticsUIStateHelper.buildStatisticsItem(sucData?.count);
    us.setStatisticsList(list);
  }

  /// 获取鱼泡机械列表数据
  Future<List<MachineItemUiState>> queryMechanicalList(
      {required int page}) async {
    final resp = await calendarRepo
        .queryMechanicalList(NetModelMachineParamModel(page: page.toString()));
    if (resp.isOK()) {
      final data = resp.getSucData()?.list ?? [];
      final list = data.map((i) {
        return MachineItemUiState(
            id: i.uu.toString(),
            title: i.title,
            tag: i.tag.map((t) {
              return MachineTag(
                name: t,
                type: t,
              );
            }).toList(),
            mode: i.mode,
            location: i.area,
            time: i.time);
      }).toList();
      return list;
    }
    return List.empty();
  }

  onDateChange(DateTime date) async {
    us.setCurrentDate(date);
    sync();
  }

  onQuickLinkSelected(QuickLinkUiState item) {
    final dest = QuickLinkDest.values
        .where((i) => i.name == item.original.dest)
        .firstOrNull;
    us.setCurQuickLink(dest);
  }

  dispose() {
    refreshController.dispose();
  }
}
