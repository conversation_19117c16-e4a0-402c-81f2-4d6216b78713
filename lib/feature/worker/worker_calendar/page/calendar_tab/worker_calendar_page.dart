import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/feature/group/group_pro_calendar/view/group_statistics_list_view.dart';
import 'package:gdjg_pure_flutter/feature/worker/worker_calendar/page/calendar_tab/vm/work_calendar_vm.dart';
import 'package:gdjg_pure_flutter/feature/worker/worker_calendar/page/calendar_tab/widget/calendar_action_button.dart';
import 'package:gdjg_pure_flutter/feature/worker/worker_calendar/page/calendar_tab/widget/machine_list_item.dart';
import 'package:gdjg_pure_flutter/feature/worker/worker_calendar/page/calendar_tab/widget/quick_link/quick_link_content.dart';
import 'package:gdjg_pure_flutter/feature/worker/worker_calendar/page/calendar_tab/widget/recruit_list_item.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/toast_util.dart';
import 'package:gdjg_pure_flutter/widget/calendar/calendar_week.dart';
import 'package:gdjg_pure_flutter/widget/calendar/dynamic_height_calendar.dart';
import 'package:gdjg_pure_flutter/widget/select_month/select_month.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

class WorkerCalendarPage extends StatelessWidget {
  const WorkerCalendarPage({super.key});

  @override
  Widget build(BuildContext context) {
    return const DynamicPageViewInScrollView();
  }
}

class DynamicPageViewInScrollView extends StatefulWidget {
  const DynamicPageViewInScrollView({super.key});

  @override
  State<DynamicPageViewInScrollView> createState() =>
      _DynamicPageViewInScrollViewState();
}

class _DynamicPageViewInScrollViewState
    extends State<DynamicPageViewInScrollView> {
  final WorkCalendarVm vm = WorkCalendarVm();

  GlobalKey<DynamicHeightCalendarState> _calendarKey =
      GlobalKey<DynamicHeightCalendarState>();

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
    vm.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Obx(() {
        return SmartRefresher(
          controller: vm.refreshController,
          onRefresh: vm.onRefresh,
          onLoading: vm.onLoading,
          enablePullUp: vm.us.showRecruitList
              ? vm.recruitRefresh.hasMore
              : vm.machineRefresh.hasMore,
          child: CustomScrollView(
            physics: const AlwaysScrollableScrollPhysics(),
            slivers: [
              SliverToBoxAdapter(
                child: Obx(() => SelectMonth(
                      value: vm.us.currentDate,
                      onValueChange: (v) {
                        setState(() {
                          vm.onDateChange(v);
                          _calendarKey.currentState?.changeMonth(v);
                        });
                      },
                    )),
              ),
              SliverToBoxAdapter(
                  child: Obx(() => GroupStatisticsView(
                        items: vm.us.statisticsList,
                        initialVisibleCount: 1,
                        onItemTap: (index) {
                          // 处理点击事件
                          ToastUtil.showToast("暂无工人流水");
                        },
                      ))),
              // 动态高度部分
              SliverToBoxAdapter(
                child: CalendarWeek(),
              ),
              Obx(() {
                return DynamicHeightCalendar(
                  key: _calendarKey,
                  onValueChange: (date) {
                    vm.onDateChange(date);
                  },
                  events: vm.us.events,
                );
              }),
              // 记工、记借支按钮
              SliverToBoxAdapter(
                child: CalendarActionButton(),
              ),
              SliverToBoxAdapter(
                child: SizedBox(
                  height: 8.h,
                ),
              ),
              SliverPersistentHeader(
                  pinned: true, // 吸顶效果
                  delegate: _StickyHeaderDelegate(
                      maxHeight: 128.h,
                      minHeight: 128.h,
                      child: Obx(() {
                        return QuickLinkContent(
                            quickLinkList: vm.us.quickLinkList,
                            curQuickLink: vm.us.curQuickLink,
                            onSelected: (i) {
                              vm.onQuickLinkSelected(i);
                            });
                      }))),

              // 鱼泡招工列表
              Obx(() {
                print('Obx showRecruitList:${vm.us.showRecruitList}');
                return vm.us.showRecruitList
                    ? Obx(() {
                        return SliverList(
                          delegate: SliverChildBuilderDelegate(
                            (context, index) => Container(
                                margin: EdgeInsets.only(bottom: 8.h),
                                child: RecruitListItem(
                                    value: vm.recruitRefresh.list[index])),
                            childCount: vm.recruitRefresh.list.length,
                          ),
                        );
                      })
                    : SliverToBoxAdapter();
              }),
              // 机械列表
              Obx(() {
                print('Obx showMachineList:${vm.us.showMachineList}');
                return vm.us.showMachineList
                    ? Obx(() {
                        return SliverList(
                          delegate: SliverChildBuilderDelegate(
                            (context, index) => Container(
                                margin: EdgeInsets.only(bottom: 8.h),
                                child: MachineListItem(
                                    value: vm.machineRefresh.list[index])),
                            childCount: vm.machineRefresh.list.length,
                          ),
                        );
                      })
                    : SliverToBoxAdapter();
              }),
            ],
          ),
        );
      }),
    );
  }
}

// 自定义Delegate
class _StickyHeaderDelegate extends SliverPersistentHeaderDelegate {
  final Widget child;
  final double? minHeight;
  final double? maxHeight;
  final GlobalKey _childKey = GlobalKey();

  _StickyHeaderDelegate({
    required this.child,
    this.minHeight,
    this.maxHeight,
  });

  @override
  Widget build(
      BuildContext context, double shrinkOffset, bool overlapsContent) {
    return SizedBox.expand(
      child: KeyedSubtree(
        key: _childKey,
        child: child,
      ),
    );
  }

  @override
  double get maxExtent {
    if (maxHeight != null) return maxHeight!;

    // 通过上下文获取渲染对象
    final RenderBox? renderBox =
        _childKey.currentContext?.findRenderObject() as RenderBox?;
    if (renderBox != null && renderBox.hasSize) {
      return renderBox.size.height;
    }

    // 默认高度（当无法计算时）
    return 56.0; // 或者抛出一个错误/使用assert
  }

  @override
  double get minExtent {
    if (minHeight != null) return minHeight!;
    return maxExtent * 0.5; // 默认最小高度为最大高度的一半
  }

  @override
  bool shouldRebuild(covariant _StickyHeaderDelegate oldDelegate) {
    return child != oldDelegate.child ||
        minHeight != oldDelegate.minHeight ||
        maxHeight != oldDelegate.maxHeight;
  }
}
