import 'package:flutter/material.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/toast_util.dart';

class CalendarActionButton extends StatelessWidget {
  const CalendarActionButton({super.key});

  @override
  Widget build(BuildContext context) {
    final ColorScheme colorScheme = Theme.of(context).colorScheme;
    final TextTheme textTheme = Theme.of(context).textTheme;

    return Container(
      color: Colors.white,
      padding: const EdgeInsets.all(8.0),
      child: Row(
        children: [
          Expanded(
              child: _buildBtn(
                  text: "记借支/结算",
                  onPressed: () => {ToastUtil.showToast("跳转记借支/结算")},
                  color: ColorsUtil.yellowMedium,
                  textStyle: textTheme.titleMedium!)),
          SizedBox(
            width: 12,
          ),
          Expanded(
              child: _buildBtn(
                  text: "记工",
                  onPressed: () => {ToastUtil.showToast("跳转记工")},
                  color: colorScheme.primary,
                  textStyle: textTheme.titleMedium!)),
        ],
      ),
    );
  }

  Widget _buildBtn(
      {required String text,
      required VoidCallback onPressed,
      required Color color,
      required TextStyle textStyle}) {
    return OutlinedButton(
        onPressed: onPressed,
        style: OutlinedButton.styleFrom(
          side: BorderSide(color: color),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
        child: Text(text, style: textStyle.copyWith(color: color)));
  }
}
