import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/feature/worker/worker_calendar/page/calendar_tab/widget/recruit_tag_item.dart';
import 'package:gdjg_pure_flutter/generated/assets.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';

class RecruitItemUiState {
  final String id;
  final String title;
  final List<RecruitTag> tag;
  final String location;
  final String time;

  RecruitItemUiState({
    required this.id,
    required this.title,
    required this.tag,
    required this.location,
    required this.time,
  });
}

class RecruitTag {
  final String name;
  final String type;

  RecruitTag({
    required this.name,
    required this.type,
  });
}

class RecruitListItem extends StatelessWidget {
  final RecruitItemUiState value;

  const RecruitListItem({super.key, required this.value});

  @override
  Widget build(BuildContext context) {
    TextTheme textTheme = Theme.of(context).textTheme;

    return Container(
      key: Key(value.id),
      color: Colors.white,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            value.title,
            style: textTheme.titleLarge!.copyWith(color: ColorsUtil.black85),
          ),
          //标签
          SizedBox(
            height: 8.h,
          ),
          Wrap(
            children: [
              ...value.tag.map((i) => RecruitTagItem(value: i)),
            ],
          ),
          SizedBox(
            height: 12.h,
          ),
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Image.asset(
                Assets.workerIconLocation,
                width: 12.w,
                height: 12.h,
              ),
              SizedBox(
                width: 3.w,
              ),
              Text(
                value.location,
                style:
                    textTheme.bodyMedium!.copyWith(color: ColorsUtil.black65),
              ),
              Spacer(),
              Text(
                value.time,
                style:
                    textTheme.bodyMedium!.copyWith(color: ColorsUtil.black65),
              ),
              SizedBox(
                width: 12.w,
              ),
              Container(
                decoration: BoxDecoration(
                  color: ColorsUtil.blueLight,
                  borderRadius: BorderRadius.circular(4),
                ),
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 3),
                child: Row(
                  children: [
                    Image.asset(
                      Assets.workerIconCallTel,
                      width: 16.w,
                      height: 16.h,
                    ),
                    SizedBox(
                      width: 2.w,
                    ),
                    Text(
                      "联系老板",
                      style: textTheme.titleMedium!
                          .copyWith(color: ColorsUtil.primaryColor),
                    )
                  ],
                ),
              )
            ],
          )
        ],
      ),
    );
  }
}
