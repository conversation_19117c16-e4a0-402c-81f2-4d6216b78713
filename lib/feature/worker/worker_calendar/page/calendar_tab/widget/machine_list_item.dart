import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/feature/worker/worker_calendar/page/calendar_tab/widget/machine_tag_item.dart';
import 'package:gdjg_pure_flutter/generated/assets.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';
import 'package:gdjg_pure_flutter/widget/label/text_label.dart';

class MachineItemUiState {
  final String id;
  final String title;
  final String mode;
  final List<MachineTag> tag;
  final String location;
  final String time;

  MachineItemUiState({
    required this.id,
    required this.title,
    required this.tag,
    required this.mode,
    required this.location,
    required this.time,
  });

  String get typeName {
    switch (mode) {
      case "2":
        return "出租";

      case "3":
        return "出售";

      case "4":
        return "求购";

      default:
        return "求租";
    }
  }

  Color get bg {
    switch (mode) {
      case "2":
      case "3":
        return ColorsUtil.yellowMedium;
      default:
        return ColorsUtil.blueLight;
    }
  }
}

class MachineTag {
  final String name;
  final String type;

  MachineTag({
    required this.name,
    required this.type,
  });
}

class MachineListItem extends StatelessWidget {
  final MachineItemUiState value;

  const MachineListItem({super.key, required this.value});

  @override
  Widget build(BuildContext context) {
    TextTheme textTheme = Theme.of(context).textTheme;

    return Container(
      key: Key(value.id),
      color: Colors.white,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          RichText(
            maxLines: 2, // 限制最多两行
            overflow: TextOverflow.ellipsis, // 超出部分显示省略号
            text: TextSpan(
              children: [
                // 首行标签（例如一个红色标签）
                WidgetSpan(
                  child: Container(
                      margin: EdgeInsets.only(right: 4.w),
                      child: _buildLineTag(context)),
                ),
                // 正文文本（换行时对齐到标签起始位置）
                TextSpan(
                  text: value.title,
                  style: textTheme.titleLarge!
                      .copyWith(color: ColorsUtil.black85, height: 1.3),
                ),
              ],
            ),
          ),
          //标签
          SizedBox(
            height: 8.h,
          ),
          Wrap(
            children: [
              ...value.tag.map((i) => MachineTagItem(value: i)),
            ],
          ),
          SizedBox(
            height: 12.h,
          ),
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Text(
                value.location,
                style:
                    textTheme.bodyMedium!.copyWith(color: ColorsUtil.black65),
              ),
              Spacer(),
              Text(
                value.time,
                style:
                    textTheme.bodyMedium!.copyWith(color: ColorsUtil.black25),
              ),
              SizedBox(
                width: 12.w,
              ),
              Container(
                decoration: BoxDecoration(
                  color: ColorsUtil.blueLight,
                  borderRadius: BorderRadius.circular(4),
                ),
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 3),
                child: Row(
                  children: [
                    Image.asset(
                      Assets.workerIconCallTel,
                      width: 16.w,
                      height: 16.h,
                    ),
                    SizedBox(
                      width: 2.w,
                    ),
                    Text(
                      "联系老板",
                      style: textTheme.titleMedium!
                          .copyWith(color: ColorsUtil.primaryColor),
                    )
                  ],
                ),
              )
            ],
          )
        ],
      ),
    );
  }

  Widget _buildLineTag(BuildContext context) {
    return TextLabel(
      label: value.typeName,
      height: 19.h,
      width: 34.w,
      labelSize: 13.sp,
      bgColor: value.bg,
      labelColor: Colors.white,
    );
  }
}
