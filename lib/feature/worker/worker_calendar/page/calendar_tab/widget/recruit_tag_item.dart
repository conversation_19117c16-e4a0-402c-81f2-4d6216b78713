import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/feature/worker/worker_calendar/page/calendar_tab/widget/recruit_list_item.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';
import 'package:gdjg_pure_flutter/widget/label/text_label.dart';

class RecruitTagItem extends StatelessWidget {
  final RecruitTag value;

  const RecruitTagItem({super.key, required this.value});

  @override
  Widget build(BuildContext context) {
    TextTheme textTheme = Theme.of(context).textTheme;
    return Container(
      margin: EdgeInsets.only(right: 4, bottom: 4),
      child: TextLabel(
        id: value.name,
        label: value.name,
        bgColor: bg,
        isBold: isBold,
        style: textTheme.bodyMedium!
            .copyWith(color: textColor, fontSize: 12.sp, height: 1.67),
        padding: EdgeInsets.symmetric(horizontal: 4, vertical: 2),
      ),
    );
  }

  Color get bg {
    switch (value.type) {
      case "1":
        return ColorsUtil.yellowLight;
      case "999":
        return ColorsUtil.blueLight;
      default:
        return ColorsUtil.ypBgColor;
    }
  }

  Color get textColor {
    switch (value.type) {
      case "999":
        return Color(0xFF5290FD);
      default:
        return ColorsUtil.black65;
    }
  }

  bool get isBold {
    switch (value.type) {
      case "999":
        return true;
      default:
        return false;
    }
  }
}
