import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/feature/worker/worker_calendar/page/calendar_tab/widget/quick_link/quick_link_ui_state.dart';

class QuickLinkItem extends StatelessWidget {
  final QuickLinkUiState value;
  final bool isSelected;
  final VoidCallback onTap;

  const QuickLinkItem({
    super.key,
    required this.value,
    required this.onTap,
    this.isSelected = false,
  });

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;
    return SizedBox(
      height: 64.h,
      child: GestureDetector(
        onTap: onTap,
        behavior: HitTestBehavior.opaque,
        child: Container(
          padding: EdgeInsets.symmetric(horizontal: 12.w),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Image.network(
                value.icon,
                width: 32.w,
                height: 32.h,
              ),
              SizedBox(
                height: 4,
              ),
              Text(
                value.title,
                style: textTheme.bodyMedium!.copyWith(
                    color: isSelected ? colorScheme.primary : Color(0xFF8A8A99),
                    fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400),
              )
            ],
          ),
        ),
      ),
    );
  }
}
