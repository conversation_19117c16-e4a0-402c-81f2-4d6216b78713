import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/data/quick_link_data/repo/model/quick_link_entity.dart';
import 'package:gdjg_pure_flutter/feature/worker/worker_calendar/page/calendar_tab/widget/quick_link/quick_link_item.dart';
import 'package:gdjg_pure_flutter/feature/worker/worker_calendar/page/calendar_tab/widget/quick_link/quick_link_ui_state.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/button_util.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/common_dialog.dart';

class QuickLinkContent extends StatelessWidget {
  final List<QuickLinkUiState> quickLinkList;

  final QuickLinkDest? curQuickLink;

  final ValueChanged<QuickLinkUiState> onSelected;

  const QuickLinkContent(
      {super.key,
      required this.quickLinkList,
      required this.onSelected,
      this.curQuickLink});

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.white,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Padding(
              padding: const EdgeInsets.symmetric(vertical: 8.0),
              child: Row(
                children: [
                  ...quickLinkList.map((e) {
                    return QuickLinkItem(
                      value: e,
                      isSelected: e.original.dest == curQuickLink?.name,
                      onTap: () {
                        // 处理点击逻辑
                        if (e.original.alert.isEmpty) {
                          // 处理点击事件
                          handleClick(e);
                          return;
                        }
                        showCommonDialog(CommonDialogConfig(onPositive: () {
                          // 处理点击事件
                          handleClick(e);
                        }));
                      },
                    );
                  }),
                ],
              ),
            ),
          ),
          Row(
            children: [
              SizedBox(
                width: 12.w,
              ),
              ButtonUtil.buildGreyButtonSelect(
                text: "全部工种",
                onPressed: () {},
              ),
              SizedBox(
                width: 8.w,
              ),
              ButtonUtil.buildGreyButtonSelect(
                text: "中国",
                onPressed: () {},
              )
            ],
          ),
          Spacer(),
          Container(
            height: 8.h,
            color: ColorsUtil.ypBgColor,
          )
        ],
      ),
    );
  }

  /// 处理金刚区点击事件
  void handleClick(QuickLinkUiState item) {
    final dest = item.original.dest;
    if (dest == QuickLinkDest.show_job_list.name) {
      onSelected(item);
      return;
    }
    if (dest == QuickLinkDest.show_machine_list.name) {
      onSelected(item);
      return;
    }
  }
}
