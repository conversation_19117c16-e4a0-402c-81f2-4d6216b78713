import 'package:flutter/material.dart';
import 'package:gdjg_pure_flutter/widget/calendar/calendar_week.dart';
import 'package:gdjg_pure_flutter/widget/calendar/dayevent/DayEvent.dart';
import 'package:gdjg_pure_flutter/widget/calendar/dynamic_height_calendar.dart';

class WorkerCalendarPage extends StatelessWidget {
  const WorkerCalendarPage({super.key});

  @override
  Widget build(BuildContext context) {
    return const DynamicPageViewInScrollView();
  }
}

class DynamicPageViewInScrollView extends StatefulWidget {
  const DynamicPageViewInScrollView({super.key});

  @override
  State<DynamicPageViewInScrollView> createState() =>
      _DynamicPageViewInScrollViewState();
}

class _DynamicPageViewInScrollViewState
    extends State<DynamicPageViewInScrollView> {
  GlobalKey<DynamicHeightCalendarState> _calendarKey =
      GlobalKey<DynamicHeightCalendarState>();

  Map<DateTime, List<DayEvent>> events = {};

  DateTime _currentDate = DateTime.now();
  DateTime startDate = DateTime.now().copyWith(year: 2000);
  DateTime endDate = DateTime.now();

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: CustomScrollView(
        slivers: [
          SliverToBoxAdapter(
            child: Row(
              children: [
                ElevatedButton(
                    onPressed: () {
                      _calendarKey.currentState?.changeMonth(
                          _currentDate.copyWith(month: _currentDate.month - 1));
                    },
                    child: Text("上个月")),
                Text("${_currentDate.year}年${_currentDate.month}月"),
                ElevatedButton(
                    onPressed: () {
                      _calendarKey.currentState?.changeMonth(
                          _currentDate.copyWith(month: _currentDate.month + 1));
                    },
                    child: Text("下个月")),
              ],
            ),
          ),
          // 动态高度部分
          SliverToBoxAdapter(
            child: CalendarWeek(),
          ),
          DynamicHeightCalendar(
            key: _calendarKey,
            onValueChange: (date) {
              setState(() {
                _currentDate = date;
              });
              Future.delayed(Duration(seconds: 2), () {
                setState(() {
                  events = {
                    DateTime(_currentDate.year, _currentDate.month, 15): [
                      DayEvent(type: DayEventType.Remark),
                      DayEvent(type: DayEventType.Borrowing),
                      DayEvent(type: DayEventType.Settlement),
                      DayEvent(type: DayEventType.DailyPaid, value: 1),
                      DayEvent(type: DayEventType.LumpSum, value: 99),
                      DayEvent(type: DayEventType.Temporary, value: 11),
                      DayEvent(type: DayEventType.Workload, value: 15),
                    ],
                    DateTime(_currentDate.year, _currentDate.month, 16): [
                      DayEvent(type: DayEventType.Remark),
                    ],
                    DateTime(_currentDate.year, _currentDate.month, 29): [
                      DayEvent(type: DayEventType.Remark),
                      DayEvent(type: DayEventType.Workload, value: 15),
                      DayEvent(type: DayEventType.Temporary, value: 11),
                    ],
                    DateTime(DateTime.now().year, DateTime.now().month,
                        DateTime.now().day): [
                      DayEvent(type: DayEventType.Workload, value: 15),
                    ],
                  };
                });
              });
            },
            events: events,
          ),
          SliverPersistentHeader(
              pinned: true, // 吸顶效果
              delegate: _StickyHeaderDelegate(
                  minHeight: 60.0,
                  maxHeight: 150.0,
                  child: Container(
                    color: Colors.lightBlue,
                    child: Center(child: Text('自定义吸顶内容')),
                  ))),

          // 列表项内容
          SliverList(
            delegate: SliverChildBuilderDelegate(
              (_, index) => ListTile(title: Text('列表项 $index')),
              childCount: 100,
            ),
          ),
        ],
      ),
    );
  }
}

// 自定义Delegate
class _StickyHeaderDelegate extends SliverPersistentHeaderDelegate {
  final double minHeight;
  final double maxHeight;
  final Widget child;

  _StickyHeaderDelegate({
    required this.minHeight,
    required this.maxHeight,
    required this.child,
  });

  @override
  Widget build(
      BuildContext context, double shrinkOffset, bool overlapsContent) {
    return SizedBox.expand(child: child);
  }

  @override
  double get maxExtent => maxHeight;

  @override
  double get minExtent => minHeight;

  @override
  bool shouldRebuild(_StickyHeaderDelegate oldDelegate) {
    return maxHeight != oldDelegate.maxHeight ||
        minHeight != oldDelegate.minHeight ||
        child != oldDelegate.child;
  }
}
