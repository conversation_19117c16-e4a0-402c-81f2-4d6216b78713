import "package:gdjg_pure_flutter/utils/refresh_loadmore/refresh_loadmore_vvm.dart";
import "package:pull_to_refresh/pull_to_refresh.dart";

class DailyFlowViewModel {
  final RefreshController refreshController =
      RefreshController(initialRefresh: false);

  late RefreshLoadMoreVvm<String> recruitRefresh;

  DailyFlowViewModel() {
    recruitRefresh = RefreshLoadMoreVvm(
        refreshController: this.refreshController,
        dataFetcher: (page, pageSize) {
          return fetchList(page);
        });
  }

  Future<List<String>> fetchList(int page) async {
    return Future.delayed(Duration(seconds: 1), () {
      return List.generate(10, (index) => "item $index");
    });
  }

  void onRefresh() async {
    recruitRefresh.refresh();
  }
}
