import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/feature/worker/worker_calendar/daily_flow/vm/daily_flow_viewmodel.dart';
import 'package:gdjg_pure_flutter/utils/route_util/base_page/base_ful_page.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

class DailyFlowPage extends BaseFulPage {
  const DailyFlowPage({super.key})
      : super(appBar: const YPAppBar(title: "个人流水"));

  @override
  State createState() => _DailyFlowPageState();
}

class _DailyFlowPageState<DailyFlowPage> extends BaseFulPageState {
  late DailyFlowViewModel vm;

  @override
  void initState() {
    super.initState();
    vm = DailyFlowViewModel();
  }

  @override
  Widget yBuild(BuildContext context) {
    final TextTheme textTheme = Theme.of(context).textTheme;
    return SmartRefresher(
      controller: vm.refreshController,
      onRefresh: vm.onRefresh,
      child: Container(
        color: Colors.white,
        child: Stack(
          children: [
            Column(
              // 移除 Expanded，因为 SingleChildScrollView 需要自己管理高度
              children: [
                ElevatedButton(
                  onPressed: () {
                    Navigator.pushNamed(context, "/personalRecordWorkPage");
                  },
                  child: const Text("个人记工"),
                ),
                ElevatedButton(
                  onPressed: () {
                    Navigator.pushNamed(context, "/personalRecordBorrowPage");
                  },
                  child: const Text("个人记借支"),
                ),
                // 添加一个 SizedBox 确保内容足够高，避免悬浮按钮遮挡
                SizedBox(height: 100), // 可根据需要调整
              ],
            ),
            // 悬浮按钮
            Positioned(
              left: 0,
              right: 0,
              bottom: 120.h,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  _buildBtn(
                      text: "记借支/结算",
                      onPressed: () {},
                      color: ColorsUtil.yellowMedium,
                      textStyle: textTheme.titleMedium!),
                  SizedBox(width: 10),
                  _buildBtn(
                      text: "记工",
                      onPressed: () {},
                      color: ColorsUtil.ypPrimaryColor,
                      textStyle: textTheme.titleMedium!),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBtn(
      {required String text,
      required VoidCallback onPressed,
      required Color color,
      required TextStyle textStyle}) {
    return OutlinedButton(
        onPressed: onPressed,
        style: OutlinedButton.styleFrom(
          side: BorderSide(color: color),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(24),
          ),
        ),
        child: Text(text, style: textStyle.copyWith(color: color)));
  }
}
