import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:gdjg_pure_flutter/feature/worker/qr_scan/vm/qr_scan_vm.dart';
import 'package:gdjg_pure_flutter/utils/route_util/base_page/base_ful_page.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/photo_picker_util.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/toast_util.dart';
import 'package:mobile_scanner/mobile_scanner.dart';

/// 扫码加入项目页面
class QRScanPage extends BaseFulPage {
  const QRScanPage({super.key})
      : super(appBar: null);

  @override
  State createState() => _QRScanPageState();
}

class _QRScanPageState extends BaseFulPageState<QRScanPage> {
  late final QRScanVM viewModel;

  @override
  void onPageCreate() {
    super.onPageCreate();
    viewModel = QRScanVM();
    viewModel.init();
  }

  @override
  void onPageDestroy() {
    viewModel.dispose();
    super.onPageDestroy();
  }

  @override
  Widget yBuild(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        automaticallyImplyLeading: false,
        leading: IconButton(
          onPressed: () => YPRoute.closePage(),
          icon: const Icon(
            Icons.arrow_back,
            color: Colors.white,
            size: 24,
          ),
        ),
        title: const Text(
          '扫码加入',
          style: TextStyle(
            color: Colors.white,
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        centerTitle: false,
      ),
      body: _buildScanView(),
    );
  }

  /// 从相册选择图片识别二维码
  Future<void> _selectFromAlbum() async {
    try {
      await PhotoPickerUtil.handlePhotoSelection(
        context,
        (imagePath) async {
          await viewModel.analyzeImageFromPath(imagePath);
        },
        maxAssets: 1,
      );
    } catch (e) {
      ToastUtil.showToast('选择图片失败');
    }
  }

  /// 构建扫码视图
  Widget _buildScanView() {
    return Stack(
      children: [
        // 扫码区域
        MobileScanner(
          controller: viewModel.scannerController,
          onDetect: viewModel.onScanResult,
        ),
        // 扫码框覆盖层
        _buildScanOverlay(),
        // 底部功能按钮
        _buildBottomButtons(),
        // 中间帮助按钮
        _buildHelpButton(),
        // 加载指示器
        if (viewModel.us.isProcessingResult.value) _buildLoadingOverlay(),
      ],
    );
  }

  /// 扫码框
  Widget _buildScanOverlay() {
    return Container(
      decoration: ShapeDecoration(
        shape: QRScannerOverlayShape(
          borderColor: const Color(0xFF1984FF),
          borderRadius: 0,
          borderLength: 20,
          borderWidth: 2,
          cutOutSize: 270.w,
          topOffset: 50.h,
        ),
      ),
    );
  }



  /// 构建帮助按钮
  Widget _buildHelpButton() {
    return Positioned(
      left: 0,
      right: 0,
      bottom: 200.h,
      child: Center(
        child: GestureDetector(
          onTap: viewModel.showHelpInfo,
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 12.h),
            decoration: BoxDecoration(
              color: const Color(0xFF1984FF),
              borderRadius: BorderRadius.circular(20.r),
            ),
            child: Text(
              '需要扫什么码？',
              style: TextStyle(
                color: Colors.white,
                fontSize: 14.sp,
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// 手电相册按钮
  Widget _buildBottomButtons() {
    return Positioned(
      left: 0,
      right: 0,
      bottom: 80.h,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          // 手电筒按钮
          Obx(() => _buildBottomButton(
            icon: viewModel.us.isFlashlightOn.value
                ? Icons.flashlight_on
                : Icons.flashlight_off,
            label: '手电筒',
            onTap: viewModel.toggleFlashlight,
          )),
          // 相册按钮
          _buildBottomButton(
            icon: Icons.photo_library,
            label: '相册',
            onTap: _selectFromAlbum,
          ),
        ],
      ),
    );
  }

  Widget _buildBottomButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 60.w,
            height: 60.w,
            decoration: const BoxDecoration(
              color: Colors.black54,
              shape: BoxShape.circle,
            ),
            child: Icon(
              icon,
              color: Colors.white,
              size: 28.w,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            label,
            style: TextStyle(
              color: Colors.white,
              fontSize: 12.sp,
            ),
          ),
        ],
      ),
    );
  }

  /// 加载覆盖层
  Widget _buildLoadingOverlay() {
    return Container(
      color: Colors.black54,
      child: Center(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF1984FF)),
            ),
            SizedBox(height: 16.h),
            Text(
              '正在处理...',
              style: TextStyle(
                color: Colors.white,
                fontSize: 16.sp,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// 扫码框形状
class QRScannerOverlayShape extends ShapeBorder {
  const QRScannerOverlayShape({
    this.borderColor = Colors.blue,
    this.borderWidth = 3.0,
    this.overlayColor = const Color.fromRGBO(0, 0, 0, 80),
    this.borderRadius = 0,
    this.borderLength = 40,
    this.cutOutSize = 250,
    this.topOffset = 0,
  });

  final Color borderColor;
  final double borderWidth;
  final Color overlayColor;
  final double borderRadius;
  final double borderLength;
  final double cutOutSize;
  final double topOffset;

  @override
  EdgeInsetsGeometry get dimensions => const EdgeInsets.all(10);

  @override
  Path getInnerPath(Rect rect, {TextDirection? textDirection}) {
    return Path()
      ..fillType = PathFillType.evenOdd
      ..addPath(getOuterPath(rect), Offset.zero);
  }

  @override
  Path getOuterPath(Rect rect, {TextDirection? textDirection}) {
    Path getLeftTopPath(Rect rect) {
      return Path()
        ..moveTo(rect.left, rect.bottom)
        ..lineTo(rect.left, rect.top + borderRadius)
        ..quadraticBezierTo(rect.left, rect.top, rect.left + borderRadius, rect.top)
        ..lineTo(rect.right, rect.top);
    }

    return getLeftTopPath(rect)
      ..lineTo(rect.right, rect.bottom)
      ..lineTo(rect.left, rect.bottom)
      ..lineTo(rect.left, rect.top);
  }

  @override
  void paint(Canvas canvas, Rect rect, {TextDirection? textDirection}) {
    final width = rect.width;
    final borderWidthSize = width / 2;
    final borderOffset = borderWidth / 2;
    final mBorderLength = borderLength > borderWidthSize / 2 ? borderWidthSize / 2 : borderLength;
    final mCutOutSize = cutOutSize < width ? cutOutSize : width - borderOffset;

    final backgroundPaint = Paint()
      ..color = overlayColor
      ..style = PaintingStyle.fill;

    final borderPaint = Paint()
      ..color = borderColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = borderWidth;

    final boxPaint = Paint()
      ..color = borderColor
      ..style = PaintingStyle.fill
      ..blendMode = BlendMode.dstOut;

    // AppBar高度
    final appBarHeight = kToolbarHeight;
    final statusBarHeight = 44.0;
    final totalTopOffset = appBarHeight + statusBarHeight + topOffset;

    final cutOutRect = Rect.fromLTWH(
      rect.left + width / 2 - mCutOutSize / 2 + borderOffset,
      rect.top + totalTopOffset + borderOffset,
      mCutOutSize - borderOffset * 2,
      mCutOutSize - borderOffset * 2,
    );

    canvas
      ..saveLayer(rect, backgroundPaint)
      ..drawRect(rect, backgroundPaint)
      ..drawRRect(
          RRect.fromRectAndRadius(cutOutRect, Radius.circular(borderRadius)), boxPaint);
    canvas.restore();

    // Draw the border
    canvas.drawRRect(
        RRect.fromRectAndRadius(cutOutRect, Radius.circular(borderRadius)), borderPaint);

    // Draw the corners
    final path = Path();
    // Top left corner
    path.moveTo(cutOutRect.left - borderOffset, cutOutRect.top + mBorderLength);
    path.lineTo(cutOutRect.left - borderOffset, cutOutRect.top + borderRadius);
    path.quadraticBezierTo(cutOutRect.left - borderOffset, cutOutRect.top - borderOffset,
        cutOutRect.left + borderRadius, cutOutRect.top - borderOffset);
    path.lineTo(cutOutRect.left + mBorderLength, cutOutRect.top - borderOffset);

    // Top right corner
    path.moveTo(cutOutRect.right - mBorderLength, cutOutRect.top - borderOffset);
    path.lineTo(cutOutRect.right - borderRadius, cutOutRect.top - borderOffset);
    path.quadraticBezierTo(cutOutRect.right + borderOffset, cutOutRect.top - borderOffset,
        cutOutRect.right + borderOffset, cutOutRect.top + borderRadius);
    path.lineTo(cutOutRect.right + borderOffset, cutOutRect.top + mBorderLength);

    // Bottom right corner
    path.moveTo(cutOutRect.right + borderOffset, cutOutRect.bottom - mBorderLength);
    path.lineTo(cutOutRect.right + borderOffset, cutOutRect.bottom - borderRadius);
    path.quadraticBezierTo(cutOutRect.right + borderOffset, cutOutRect.bottom + borderOffset,
        cutOutRect.right - borderRadius, cutOutRect.bottom + borderOffset);
    path.lineTo(cutOutRect.right - mBorderLength, cutOutRect.bottom + borderOffset);

    // Bottom left corner
    path.moveTo(cutOutRect.left + mBorderLength, cutOutRect.bottom + borderOffset);
    path.lineTo(cutOutRect.left + borderRadius, cutOutRect.bottom + borderOffset);
    path.quadraticBezierTo(cutOutRect.left - borderOffset, cutOutRect.bottom + borderOffset,
        cutOutRect.left - borderOffset, cutOutRect.bottom - borderRadius);
    path.lineTo(cutOutRect.left - borderOffset, cutOutRect.bottom - mBorderLength);

    canvas.drawPath(path, borderPaint);
  }

  @override
  ShapeBorder scale(double t) {
    return QRScannerOverlayShape(
      borderColor: borderColor,
      borderWidth: borderWidth,
      overlayColor: overlayColor,
      borderRadius: borderRadius,
      borderLength: borderLength,
      cutOutSize: cutOutSize,
      topOffset: topOffset,
    );
  }
}


