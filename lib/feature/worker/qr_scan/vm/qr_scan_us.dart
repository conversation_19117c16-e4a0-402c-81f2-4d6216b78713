import 'package:get/get.dart';

/// 扫码页面UI状态管理
class QRScanUS {
  /// 手电筒是否开启
  final _isFlashlightOn = false.obs;

  /// 是否正在处理扫码结果
  final _isProcessingResult = false.obs;

  // Getters
  RxBool get isFlashlightOn => _isFlashlightOn;
  RxBool get isProcessingResult => _isProcessingResult;

  /// 设置手电筒状态
  void setFlashlight(bool isOn) {
    _isFlashlightOn.value = isOn;
  }

  /// 设置处理结果状态
  void setProcessingResult(bool isProcessing) {
    _isProcessingResult.value = isProcessing;
  }
}
