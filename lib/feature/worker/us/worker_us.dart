import 'package:get/get.dart';

class WorkerUS extends GetxController {
  static WorkerUS get to => Get.find();
  
  final currentIndex = 0.obs;
  final previousIndex = 0.obs;

  @override
  void onInit() {
    super.onInit();
    resetState();
  }

  void resetState() {
    currentIndex.value = 0;  // 重置为流水页面
    previousIndex.value = 0;
  }

  void changeTabIndex(int index) {
    previousIndex.value = currentIndex.value;
    currentIndex.value = index;
  }
} 