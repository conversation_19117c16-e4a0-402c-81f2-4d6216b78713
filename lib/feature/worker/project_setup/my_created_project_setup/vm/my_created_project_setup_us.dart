import 'package:get/get.dart';

/// 我创建的项目设置UI状态管理
class MyCreatedProjectSetupUS {
  /// 项目名称
  final _projectName = '在建项目'.obs;

  /// 点工工价显示文本
  final _perWorkPrice = '未设置'.obs;

  /// 包工工价显示文本
  final _contractPrice = '未设置'.obs;
  
  /// 分享时是否隐藏我的工资
  final _hideMyWageWhenShare = false.obs;

  /// 总未结金额
  final _totalUnsettledAmount = 0.0.obs;

  /// 点工加班类型
  final _perWorkOvertimeType = 0.0.obs;

  /// 点工加班标准小时
  final _perWorkOvertimeHoursStandard = ''.obs;

  /// 点工加班价格
  final _perWorkOvertimeHoursPrice = ''.obs;

  /// 包工加班类型
  final _contractOvertimeType = 0.0.obs;

  /// 包工加班标准小时
  final _contractOvertimeHoursStandard = ''.obs;

  /// 包工加班价格
  final _contractOvertimeHoursPrice = ''.obs;

  // Getters
  String get projectName => _projectName.value;
  String get perWorkPrice => _perWorkPrice.value;
  String get contractPrice => _contractPrice.value;
  bool get hideMyWageWhenShare => _hideMyWageWhenShare.value;
  double get totalUnsettledAmount => _totalUnsettledAmount.value;
  double get perWorkOvertimeType => _perWorkOvertimeType.value;
  String get perWorkOvertimeHoursStandard => _perWorkOvertimeHoursStandard.value;
  String get perWorkOvertimeHoursPrice => _perWorkOvertimeHoursPrice.value;
  double get contractOvertimeType => _contractOvertimeType.value;
  String get contractOvertimeHoursStandard => _contractOvertimeHoursStandard.value;
  String get contractOvertimeHoursPrice => _contractOvertimeHoursPrice.value;

  // Setters
  void setProjectName(String name) {
    _projectName.value = name;
  }

  void setPerWorkPrice(String price) {
    _perWorkPrice.value = price;
  }

  void setContractPrice(String price) {
    _contractPrice.value = price;
  }

  void setHideMyWageWhenShare(bool hide) {
    _hideMyWageWhenShare.value = hide;
  }

  void setTotalUnsettledAmount(double amount) {
    _totalUnsettledAmount.value = amount;
  }

  void setPerWorkOvertimeType(double type) {
    _perWorkOvertimeType.value = type;
  }

  void setPerWorkOvertimeHoursStandard(String standard) {
    _perWorkOvertimeHoursStandard.value = standard;
  }

  void setPerWorkOvertimeHoursPrice(String price) {
    _perWorkOvertimeHoursPrice.value = price;
  }

  void setContractOvertimeType(double type) {
    _contractOvertimeType.value = type;
  }

  void setContractOvertimeHoursStandard(String standard) {
    _contractOvertimeHoursStandard.value = standard;
  }

  void setContractOvertimeHoursPrice(String price) {
    _contractOvertimeHoursPrice.value = price;
  }
}
