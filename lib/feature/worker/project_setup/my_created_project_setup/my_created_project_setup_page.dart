import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/utils/event_bus_util/event_bus_util.dart';
import 'package:gdjg_pure_flutter/utils/route_util/base_page/base_ful_page.dart';
import 'package:get/get.dart';

import 'vm/my_created_project_setup_vm.dart';

class MyCreatedProjectSetupProps {
  final String? projectId;
  final bool isFromCompleted;

  MyCreatedProjectSetupProps({
    this.projectId,
    this.isFromCompleted = false,
  });
}

/// 我创建的项目页面
class MyCreatedProjectSetupPage extends BaseFulPage {
  const MyCreatedProjectSetupPage({super.key})
      : super(appBar: const YPAppBar(title: "我创建的项目"));

  @override
  State createState() => _MyCreatedProjectSetupPageState();
}

class _MyCreatedProjectSetupPageState extends BaseFulPageState {
  late MyCreatedProjectSetupProps? props;
  final MyCreatedProjectSetupVM viewModel = MyCreatedProjectSetupVM();
  StreamSubscription? _eventSubscription;

  @override
  void onPageRoute(Object? routeParams, bool fromLaunchTask) {
    super.onPageRoute(routeParams, fromLaunchTask);
    props = routeParams as MyCreatedProjectSetupProps?;
    viewModel.init(props?.projectId, isFromCompleted: props?.isFromCompleted ?? false);

    // 监听项目状态变更事件
    _eventSubscription = EventBusUtil.collect<String>((eventType) {
      // 项目更新
      if (eventType == 'project_update') {
        viewModel.fetchData();
      }
    });
  }

  @override
  void onPageDestroy() {
    super.onPageDestroy();
    _eventSubscription?.cancel();
  }

  @override
  Widget yBuild(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F6FA),
      body: _buildContent(),
    );
  }

  /// 格式化数字
  String _formatNumber(String? numberStr) {
    if (numberStr == null || numberStr.isEmpty) {
      return '';
    }

    try {
      double number = double.parse(numberStr);
      if (number == number.toInt()) {
        return number.toInt().toString();
      }
      String formatted = number.toStringAsFixed(2);
      formatted = formatted.replaceAll(RegExp(r'\.?0+$'), '');
      return formatted;
    } catch (e) {
      return numberStr;
    }
  }

  /// 工价Widget
  Widget _buildWageDisplayWidget({
    required String baseWageText,
    required double overtimeType,
    required String overtimeHoursStandard,
    required String overtimeHoursPrice,
  }) {
    if (overtimeType == 0.0 ||
        (overtimeHoursStandard.isEmpty && overtimeHoursPrice.isEmpty) ||
        (overtimeHoursStandard == "0" && overtimeHoursPrice == "0")) {
      return Text(
        baseWageText,
        style: TextStyle(
          letterSpacing: 0,
          fontSize: 13.sp,
          fontWeight: FontWeight.w400,
          color: const Color(0xFF666666),
        ),
      );
    }

    // 加班信息
    String overtimeText = '';
    if (overtimeType == 1.0) {
      String formattedStandard = _formatNumber(overtimeHoursStandard);
      overtimeText = '加班$formattedStandard小时1个工';
    } else if (overtimeType == 2.0) {
      String formattedPrice = _formatNumber(overtimeHoursPrice);
      overtimeText = '加班1小时$formattedPrice元';
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          baseWageText,
          style: TextStyle(
            letterSpacing: 0,
            fontSize: 13.sp,
            fontWeight: FontWeight.w400,
            color: const Color(0xFF666666),
          ),
        ),
        if (overtimeText.isNotEmpty) ...[
          Text(
            overtimeText,
            style: TextStyle(
              letterSpacing: 0,
              fontSize: 13.sp,
              fontWeight: FontWeight.w400,
              color: const Color(0xFF666666),
            ),
          ),
        ],
      ],
    );
  }

  /// 构建页面内容
  Widget _buildContent() {
    return SingleChildScrollView(
      child: Column(
        children: [
          /// 项目、工价、展示工资开关
          _buildMainSettingsSection(),
          // 统计和总未结
          _buildStatisticsAndSettlementSection(),
          // 记结算
          _buildRecordSettlementSection(),
          // 设为已结清
          _buildMarkAsSettledSection(),
        ],
      ),
    );
  }

  /// 项目、工价、展示工资开关
  Widget _buildMainSettingsSection() {
    return Container(
      color: Colors.white,
      margin: EdgeInsets.only(top: 8.h),
      child: Column(
        children: [
          ListTile(
            contentPadding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 2.h),
            title: Text(
              '项目',
              style: TextStyle(
                fontSize: 17.sp,
                color: const Color(0xFF222222),
              ),
            ),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Obx(() => Text(
                  viewModel.us.projectName,
                  style: TextStyle(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w400,
                    color: const Color(0xFF666666),
                  ),
                )),
                SizedBox(width: 8.w),
                Icon(
                  Icons.arrow_forward_ios,
                  size: 16.w,
                  color: const Color(0xFF999999),
                ),
              ],
            ),
            onTap: viewModel.onProjectInfoTap,
          ),
          Divider(
            height: 1.h,
            color: const Color(0xFFF5F6FA),
            indent: 16.w,
            endIndent: 16.w,
          ),
          // 非已结清项目显示点工工价和包工工价
          if (!viewModel.isFromCompleted) ...[
            Divider(
              height: 1.h,
              color: const Color(0xFFF5F6FA),
              indent: 16.w,
              endIndent: 16.w,
            ),
            ListTile(
              contentPadding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 2.h),
              title: Text(
                '点工工价',
                style: TextStyle(
                  fontSize: 17.sp,
                  color: const Color(0xFF222222),
                ),
              ),
              trailing: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Obx(() => _buildWageDisplayWidget(
                    baseWageText: viewModel.us.perWorkPrice,
                    overtimeType: viewModel.us.perWorkOvertimeType,
                    overtimeHoursStandard: viewModel.us.perWorkOvertimeHoursStandard,
                    overtimeHoursPrice: viewModel.us.perWorkOvertimeHoursPrice,
                  )),
                  SizedBox(width: 8.w),
                  Icon(
                    Icons.arrow_forward_ios,
                    size: 16.w,
                    color: const Color(0xFF999999),
                  ),
                ],
              ),
              onTap: viewModel.onPointWorkWageTap,
            ),
            Divider(
              height: 1.h,
              color: const Color(0xFFF5F6FA),
              indent: 16.w,
              endIndent: 16.w,
            ),
            ListTile(
              contentPadding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 2.h),
              title: Text(
                '包工工价',
                style: TextStyle(
                  fontSize: 17.sp,
                  color: const Color(0xFF222222),
                ),
              ),
              trailing: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Obx(() => _buildWageDisplayWidget(
                    baseWageText: viewModel.us.contractPrice,
                    overtimeType: viewModel.us.contractOvertimeType,
                    overtimeHoursStandard: viewModel.us.contractOvertimeHoursStandard,
                    overtimeHoursPrice: viewModel.us.contractOvertimeHoursPrice,
                  )),
                  SizedBox(width: 8.w),
                  Icon(
                    Icons.arrow_forward_ios,
                    size: 16.w,
                    color: const Color(0xFF999999),
                  ),
                ],
              ),
              onTap: viewModel.onContractWorkWageTap,
            ),
            Divider(
              height: 1.h,
              color: const Color(0xFFF5F6FA),
              indent: 16.w,
              endIndent: 16.w,
            ),
          ],
          ListTile(
            contentPadding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 2.h),
            title: Text(
              '分享时，不展示我的工资',
              style: TextStyle(
                fontSize: 17.sp,
                color: const Color(0xFF222222),
              ),
            ),
            trailing: Obx(() => _buildCustomSwitch()),
          ),
        ],
      ),
    );
  }

  /// 统计和总未结
  Widget _buildStatisticsAndSettlementSection() {
    return Container(
      color: Colors.white,
      margin: EdgeInsets.only(top: 8.h),
      child: Column(
        children: [
          ListTile(
            contentPadding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 2.h),
            title: Text(
              '统计',
              style: TextStyle(
                fontSize: 17.sp,
                color: const Color(0xFF222222),
              ),
            ),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  '对工',
                  style: TextStyle(
                    fontSize: 18.sp,
                    fontWeight: FontWeight.w400,
                    color: const Color(0xFF666666),
                  ),
                ),
                SizedBox(width: 8.w),
                Icon(
                  Icons.arrow_forward_ios,
                  size: 16.w,
                  color: const Color(0xFF999999),
                ),
              ],
            ),
            onTap: viewModel.onStatisticsTap,
          ),
          // 非已结清项目显示总未结
          if (!viewModel.isFromCompleted) ...[
            Divider(
              height: 1.h,
              color: const Color(0xFFF5F6FA),
              indent: 16.w,
              endIndent: 16.w,
            ),
            ListTile(
              contentPadding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 2.h),
              title: Text(
                '总未结',
                style: TextStyle(
                  fontSize: 17.sp,
                  color: const Color(0xFF222222),
                ),
              ),
              trailing: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Obx(() => Text(
                    viewModel.us.totalUnsettledAmount.toStringAsFixed(2),
                    style: TextStyle(
                      fontSize: 22.sp,
                      color: const Color(0xFF5290FD),
                      fontWeight: FontWeight.w400,
                    ),
                  )),
                  SizedBox(width: 8.w),
                  Icon(
                    Icons.arrow_forward_ios,
                    size: 16.w,
                    color: const Color(0xFF999999),
                  ),
                ],
              ),
              onTap: viewModel.onSettlementTap,
            ),
          ],
        ],
      ),
    );
  }

  /// 记结算/恢复为在建
  Widget _buildRecordSettlementSection() {
    return Container(
      color: Colors.white,
      margin: EdgeInsets.only(top: 8.h),
      child: ListTile(
        contentPadding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 2.h),
        title: Center(
          child: Text(
            viewModel.isFromCompleted ? '恢复为在建' : '记结算',
            style: TextStyle(
              fontSize: 15.sp,
              color: Colors.blueAccent,
            ),
          ),
        ),
        onTap: viewModel.isFromCompleted
            ? viewModel.onRecoverToActiveTap
            : viewModel.onRecordSettlementTap,
      ),
    );
  }

  /// 设为已结清/彻底删除
  Widget _buildMarkAsSettledSection() {
    return Container(
      color: Colors.white,
      margin: EdgeInsets.only(top: 1.h),
      child: ListTile(
        contentPadding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 2.h),
        title: Center(
          child: Text(
            viewModel.isFromCompleted ? '彻底删除' : '设为已结清',
            style: TextStyle(
              fontSize: 15.sp,
              color: Colors.redAccent,
            ),
          ),
        ),
        onTap: viewModel.isFromCompleted
            ? viewModel.onDeleteProjectTap
            : viewModel.onMarkAsSettledTap,
      ),
    );
  }

  /// Switch
  Widget _buildCustomSwitch() {
    return GestureDetector(
      onTap: () => viewModel.onPrivacyToggle(!viewModel.us.hideMyWageWhenShare),
      child: SizedBox(
        width: 50.w,
        height: 30.h,
        child: Stack(
          alignment: Alignment.center,
          children: [
            // 导轨
            Container(
              width: 24.w,
              height: 14.h,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10.h),
                color: viewModel.us.hideMyWageWhenShare
                    ? const Color(0xFF4CAF50)
                    : Colors.grey.shade400,
              ),
            ),
            AnimatedAlign(
              duration: const Duration(milliseconds: 200),
              alignment: viewModel.us.hideMyWageWhenShare
                  ? Alignment.centerRight
                  : Alignment.centerLeft,
              child: Container(
                width: 22.w,
                height: 22.h,
                margin: EdgeInsets.symmetric(horizontal: 2.w),
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: Colors.white,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.4),
                      blurRadius: 2,
                      offset: const Offset(0, 1),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
