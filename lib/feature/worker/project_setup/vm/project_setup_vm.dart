import 'package:gdjg_pure_flutter/data/worker_data/worker_project_data/ds/model/param/dept_put_away_dept_param_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/worker_project_data/repo/worker_project_repo.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/common_dialog.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/toast_util.dart';

import 'project_setup_us.dart';

/// 项目设置ViewModel
class ProjectSetupVM {
  final _workerProjectRep = WorkerProjectRep();
  final us = ProjectSetupUS();

  String? _projectId;
  double? _deptId;

  /// 初始化数据
  void init(String? projectId) {
    _projectId = projectId;
    fetchData();
  }

  /// 获取项目设置数据
  void fetchData() async {
    if (_projectId == null || _projectId!.isEmpty) {
      return;
    }

    final result = await _workerProjectRep.getProject(_projectId!);

    if (result.isOK() && result.getSucData() != null) {
      final data = result.getSucData()!;

      // 部门ID用于设为已结清
      _deptId = data.deptId;

      // 更新UI状态
      us.setProjectName(data.name);
      us.setPerWorkPrice(_buildWageDisplayText(
        data.feeStandard?.workingHoursStandard,
        data.feeStandard?.workingHoursPrice
      ));
      us.setContractPrice(_buildWageDisplayText(
        data.contractorFeeStandard?.workingHoursStandard,
        data.contractorFeeStandard?.workingHoursPrice
      ));

      // 设置点工加班相关数据
      us.setPerWorkOvertimeType(data.feeStandard?.overtimeType ?? 0.0);
      us.setPerWorkOvertimeHoursStandard(data.feeStandard?.overtimeHoursStandard ?? '');
      us.setPerWorkOvertimeHoursPrice(data.feeStandard?.overtimeHoursPrice ?? '');

      // 设置包工加班相关数据
      us.setContractOvertimeType(data.contractorFeeStandard?.overtimeType ?? 0.0);
      us.setContractOvertimeHoursStandard(data.contractorFeeStandard?.overtimeHoursStandard ?? '');
      us.setContractOvertimeHoursPrice(data.contractorFeeStandard?.overtimeHoursPrice ?? '');

      us.setHideWageWhenShare(false);
      us.setTotalUnsettledAmount(0.0);
    }
  }

  /// 格式化数字显示
  String _formatNumber(String? numberStr) {
    if (numberStr == null || numberStr.isEmpty) {
      return '';
    }

    try {
      double number = double.parse(numberStr);
      // 如果是整数，直接显示整数
      if (number == number.toInt()) {
        return number.toInt().toString();
      }
      // 如果有小数，保留小数最多两位
      String formatted = number.toStringAsFixed(2);
      formatted = formatted.replaceAll(RegExp(r'\.?0+$'), '');
      return formatted;
    } catch (e) {
      return numberStr;
    }
  }

  /// 工价文本
  String _buildWageDisplayText(String? hoursStandard, String? hoursPrice) {
    if (hoursStandard == null || hoursPrice == null ||
        hoursStandard.isEmpty || hoursPrice.isEmpty ||
        hoursStandard == "0" || hoursStandard == "0.0" ||
        hoursPrice == "0" || hoursPrice == "0.0") {
      return '未设置';
    }

    String formattedStandard = _formatNumber(hoursStandard);
    String formattedPrice = _formatNumber(hoursPrice);

    return '1个工$formattedStandard小时$formattedPrice元';
  }



  /// 点击项目信息
  void onProjectInfoTap() {
    ToastUtil.showToast('项目信息功能开发中');
  }

  /// 点击点工工价设置
  void onPointWorkWageTap() {
    ToastUtil.showToast('点工工价设置功能开发中');
  }

  /// 点击包工工价设置
  void onContractWorkWageTap() {
    ToastUtil.showToast('包工工价设置功能开发中');
  }

  /// 切换隐私设置
  void onPrivacyToggle(bool value) {
    us.setHideWageWhenShare(value);
  }

  /// 点击统计
  void onStatisticsTap() {
    ToastUtil.showToast('统计功能开发中');
  }

  /// 点击总未结
  void onSettlementTap() {
    ToastUtil.showToast('结算详情功能开发中');
  }

  /// 点击记结算
  void onRecordSettlementTap() async {
    if (_projectId == null) return;

    ToastUtil.showToast('记结算功能开发中');
  }

  /// 点击设为已结清
  void onMarkAsSettledTap() async {
    if (_deptId == null) return;

    showCommonDialog(CommonDialogConfig(
      title: '确认设为已结清吗？',
      content: '设为已结清后，不参与统计和展示',
      negative: '取消',
      positive: '确定',
      onPositive: () => _confirmMarkAsSettled(),
    ));
  }

  /// 确认设为已结清
  void _confirmMarkAsSettled() async {
    if (_deptId == null) return;

    final param = DeptPutAwayDeptParamModel(
      dept_id: _deptId!.toInt().toString(),
      is_ignored: '1',
    );

    final result = await _workerProjectRep.putAwayDept(param);

    if (result.isOK()) {
      ToastUtil.showToast('设为已结清成功');
      YPRoute.closeDialog();
      YPRoute.closePage({'refresh': true});
    } else {
      ToastUtil.showToast('设为已结清失败：${result.fail?.errorMsg ?? "未知错误"}');
    }
  }
}
