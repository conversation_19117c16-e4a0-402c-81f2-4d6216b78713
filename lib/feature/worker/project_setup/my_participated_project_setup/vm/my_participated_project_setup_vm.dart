import 'package:gdjg_pure_flutter/data/group_data/group_un_liauidated/repo/project_settle_repo.dart';
import 'package:gdjg_pure_flutter/data/worker_data/worker_project_data/ds/param/dept_put_away_dept_param_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/worker_project_data/repo/worker_project_repo.dart';
import 'package:gdjg_pure_flutter/data/worker_data/corp_select/repo/corp_select_repo.dart';
import 'package:gdjg_pure_flutter/data/worker_data/corp_select/ds/param/corp_select_param_model.dart';
import 'package:gdjg_pure_flutter/feature/group/group_pro_bill/entity/group_pro_bill_props.dart';
import 'package:gdjg_pure_flutter/init_module/init_route.dart';
import 'package:gdjg_pure_flutter/utils/event_bus_util/event_bus_util.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/common_dialog.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/toast_util.dart';

import 'my_participated_project_setup_us.dart';

/// 我参与的项目设置ViewModel
class MyParticipatedProjectSetupVM {
  final _workerProjectRep = WorkerProjectRepo();
  final _corpSelectRepo = CorpSelectRepo();
  final _projectSettleRepo = ProjectSettleRepo();
  final us = MyParticipatedProjectSetupUS();

  String? _deptId;
  double? _corpId;
  double? _workNoteId;
  bool _isFromCompleted = false;

  /// 获取是否来自已结清项目
  bool get isFromCompleted => _isFromCompleted;

  /// 初始化数据
  void init(String? deptId, {bool isFromCompleted = false}) {
    _deptId = deptId;
    _isFromCompleted = isFromCompleted;
    _loadPrivacySettings();
    fetchData();
  }

  /// 获取项目设置数据
  void fetchData() async {
    if (_deptId == null || _deptId!.isEmpty) {
      return;
    }

    // 获取部门详情
    final deptDetailResult = await _workerProjectRep.getDeptDetail(_deptId!);

    if (deptDetailResult.isOK() && deptDetailResult.getSucData() != null) {
      final deptData = deptDetailResult.getSucData()!;

      _corpId = deptData.corpId;
      _workNoteId = deptData.workNoteId;

      // 更新UI状态
      us.setProjectName(deptData.name);

      // 切换企业
      if (_corpId != null && _corpId! > 0) {
        final corpSelectParam = CorpSelectParamModel(
          corp_id: _corpId!.toInt().toString(),
        );
        await _corpSelectRepo.fetchCorpSelect(corpSelectParam);
      }

      // 获取总未结金额
      _fetchTotalUnsettledAmount();
    }
  }

  /// 加载隐私设置
  void _loadPrivacySettings() {
    final hideWorkerWage = _workerProjectRep.getHideWorkerWageWhenShare();
    us.setHideWorkerWageWhenShare(hideWorkerWage);
  }

  /// 保存隐私设置
  void _savePrivacySettings() {
    _workerProjectRep.setHideWorkerWageWhenShare(us.hideWorkerWageWhenShare);
  }

  /// 获取总未结金额
  void _fetchTotalUnsettledAmount() async {
    if (_workNoteId == null || _workNoteId! <= 0) {
      return;
    }

    final result = await _projectSettleRepo.getAllGroupWorkerSettle(_workNoteId!.toInt().toString());

    if (result.isOK() && result.getSucData() != null) {
      final data = result.getSucData()!;
      us.setTotalUnsettledAmount(data.notSettleMoney);
    } else {
      us.setTotalUnsettledAmount(0.0);
    }
  }

  /// 切换隐私设置
  void onPrivacyToggle(bool value) {
    us.setHideWorkerWageWhenShare(value);
    _savePrivacySettings();
  }

  /// 点击统计
  void onStatisticsTap() {
    // 验证参数
    if (_workNoteId == null || _workNoteId! <= 0) {
      ToastUtil.showToast('项目信息错误');
      return;
    }

    if (_deptId == null || _deptId!.isEmpty) {
      ToastUtil.showToast('部门信息错误');
      return;
    }

    // 统计页面参数
    final params = GroupProBillProps(
      workNoteId: _workNoteId!.toInt().toString(),
      workNoteName: us.projectName,
      deptId: double.parse(_deptId!),
      isJoin: true,
    );

    // 导航到统计页面
    YPRoute.openPage(RouteNameCollection.groupProBill, params: params);
  }

  /// 点击总未结
  void onSettlementTap() {
    ToastUtil.showToast('结算详情功能开发中');
  }

  /// 点击设为已结清
  void onMarkAsSettledTap() async {
    if (_deptId == null || _deptId!.isEmpty) return;

    showCommonDialog(CommonDialogConfig(
      title: '确认设为已结清吗？',
      content: '设为已结清后，不参与统计和展示',
      negative: '取消',
      positive: '确定',
      onPositive: () => _confirmMarkAsSettled(),
    ));
  }

  /// 确认设为已结清
  void _confirmMarkAsSettled() async {
    if (_deptId == null || _deptId!.isEmpty) return;

    // 企业切换
    if (_corpId != null && _corpId! > 0) {
      final corpSelectParam = CorpSelectParamModel(
        corp_id: _corpId!.toInt().toString(),
      );

      await _corpSelectRepo.fetchCorpSelect(corpSelectParam);
    }

    // 已结清操作
    final param = DeptPutAwayDeptParamModel(
      dept_id: _deptId!,
      is_ignored: '1',
    );

    final result = await _workerProjectRep.putAwayDept(param);

    if (result.isOK()) {
      ToastUtil.showToast('设为已结清成功');
      EventBusUtil.emit<String>('project_settle');
      YPRoute.closeDialog();
      YPRoute.closePage({'refresh': true});
    } else {
      ToastUtil.showToast('设为已结清失败：${result.fail?.errorMsg ?? "未知错误"}');
    }
  }

  /// 点击恢复为在建
  void onRecoverToActiveTap() async {
    if (_deptId == null || _deptId!.isEmpty) return;

    showCommonDialog(CommonDialogConfig(
      title: '确认恢复为在建吗？',
      content: '恢复为在建后，将参与统计和展示',
      negative: '取消',
      positive: '确定',
      onPositive: () => _confirmRecoverToActive(),
    ));
  }

  /// 确认恢复为在建
  void _confirmRecoverToActive() async {
    if (_deptId == null || _deptId!.isEmpty) return;

    // 企业切换
    if (_corpId != null && _corpId! > 0) {
      final corpSelectParam = CorpSelectParamModel(
        corp_id: _corpId!.toInt().toString(),
      );

      await _corpSelectRepo.fetchCorpSelect(corpSelectParam);
    }

    // 恢复为在建操作
    final param = DeptPutAwayDeptParamModel(
      dept_id: _deptId!,
      is_ignored: '0', // 0表示恢复为在建
    );

    final result = await _workerProjectRep.putAwayDept(param);

    if (result.isOK()) {
      ToastUtil.showToast('恢复为在建成功');
      EventBusUtil.emit<String>('project_recover');
      YPRoute.closeDialog();

      // 如果是从已结清项目进入的，需要关闭两个页面回到已结清tab
      if (_isFromCompleted) {
        YPRoute.closePage({'refresh': true});
        YPRoute.closePage({'refresh': true});
      } else {
        // 普通情况只关闭设置页面
        YPRoute.closePage({'refresh': true});
      }
    } else {
      ToastUtil.showToast('恢复为在建失败：${result.fail?.errorMsg ?? "未知错误"}');
    }
  }
}
