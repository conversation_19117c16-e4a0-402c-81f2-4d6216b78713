import 'package:get/get.dart';

/// 我参与的项目设置UI状态管理
class MyParticipatedProjectSetupUS {
  /// 项目名称
  final _projectName = '项目'.obs;
  
  /// 分享时是否隐藏工友工资
  final _hideWorkerWageWhenShare = false.obs;

  /// 总未结金额
  final _totalUnsettledAmount = 0.0.obs;

  // Getters
  String get projectName => _projectName.value;
  bool get hideWorkerWageWhenShare => _hideWorkerWageWhenShare.value;
  double get totalUnsettledAmount => _totalUnsettledAmount.value;

  // Setters
  void setProjectName(String name) {
    _projectName.value = name;
  }

  void setHideWorkerWageWhenShare(bool hide) {
    _hideWorkerWageWhenShare.value = hide;
  }

  void setTotalUnsettledAmount(double amount) {
    _totalUnsettledAmount.value = amount;
  }
}
