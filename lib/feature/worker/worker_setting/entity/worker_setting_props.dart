/// @date 2025/06/20
/// @description WorkerSetting页入参
class WorkerSettingProps {
  WorkerSettingPropsEntity? data;

  WorkerSettingProps({this.data});

  @override
  String toString() {
    return 'WorkerSettingProps{data: ${data.toString()}';
  }
}

/// @param workerId 工友id
/// @param source 从哪些页面进入[WorkerSettingSource]：更多页面的工友通讯录 ，项目在场工友， 在场工友页面的通讯录
/// @param isSelf 是否是自己
class WorkerSettingPropsEntity {
  WorkerSettingSource? source;
  String? projectName;
  bool? isSelf;
  int? workerId;

  WorkerSettingPropsEntity(
      {this.source, this.isSelf, this.workerId, this.projectName});

  @override
  String toString() {
    return 'WorkerSettingPropsEntity{source: $source, isSelf: $isSelf, workerId: $workerId, projectName: $projectName}';
  }
}

/// 工友设置页来源
/// @description 从哪些页面进入
/// @date 2025/06/20
/// @param moreBook 更多-工友通讯录
/// @param projectWorker 项目在场工友
/// @param bookList 添加工友-工友通讯录
/// @param defaultPage 默认页
enum WorkerSettingSource {
  moreBook,
  projectWorker,
  bookList,
  defaultPage,
}
