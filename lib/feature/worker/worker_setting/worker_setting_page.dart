import 'package:flutter/material.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/appbar_util.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/toast_util.dart';
import 'package:gdjg_pure_flutter/widget/page_error_view.dart';
import 'package:gdjg_pure_flutter/widget/page_loading_view.dart';
import 'package:get/get.dart';
import 'entity/worker_setting_props.dart';
import 'vm/worker_setting_viewmodel.dart';

/// @date 2025/06/20 工友设置页面
/// @param props 页面路由参数
/// @returns
/// @description WorkerSetting页面入口
class WorkerSettingPage extends StatelessWidget {
  WorkerSettingPage({super.key, this.props});

  final WorkerSettingProps? props;
  final WorkerSettingViewModel viewModel = WorkerSettingViewModel();

  /// 获取路由参数
  WorkerSettingProps? get routeProps => Get.arguments as WorkerSettingProps?;

  /// 优先使用路由参数，其次使用构造函数参数
  WorkerSettingProps? get finalProps => routeProps ?? props;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      appBar: _buildAppBar(),
      body: Obx(() {
        if (viewModel.isLoading.value) {
          return const PageLoadingView();
        }
        if (viewModel.isShowError.value == true) {
          return PageErrorView(onReload: () => viewModel.fetchData());
        }
        return contentView();
      }),
    );
  }

  /// 构建AppBar
  PreferredSizeWidget _buildAppBar() {
    final source = finalProps?.data?.source;
    final projectName = finalProps?.data?.projectName;

    // 根据source值确定标题
    String title = "工友设置";
    if (source == WorkerSettingSource.projectWorker && projectName != null) {
      title = projectName;
    }

    // 如果是项目工友，显示退场按钮
    if (source == WorkerSettingSource.projectWorker) {
      return AppBar(
        backgroundColor: Colors.white,
        leading: GestureDetector(
          onTap: () => Get.back(),
          child: Container(
            padding: const EdgeInsets.only(left: 8.0),
            child: const Icon(
              Icons.arrow_back,
              color: Colors.black,
            ),
          ),
        ),
        leadingWidth: 30,
        titleSpacing: 8,
        title: Text(
          title,
          style: const TextStyle(
            color: Colors.black,
            fontSize: 22,
            fontWeight: FontWeight.w600,
          ),
        ),
        centerTitle: false,
        actions: [
          GestureDetector(
            onTap: _onRemoveFromProject,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                decoration: const BoxDecoration(
                  borderRadius: BorderRadius.all(Radius.circular(12)),
                  color: Color(0xfffee5e4),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.logout,
                      size: 16,
                      color: Colors.red,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      '将工友从项目中退场',
                      style: TextStyle(
                        fontSize: 13,
                        color: Colors.red,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      );
    } else {
      return AppBarUtil.buildCommonAppBar(
        title: title,
        backgroundColor: Colors.white,
        iconColor: Colors.black,
      );
    }
  }

  /// 实际展示的视图
  Widget contentView() {
    return SingleChildScrollView(
      child: ColoredBox(
        color: Color(0xfff5f6fa),
        child: Column(
          children: [
            // 基础信息区域
            _buildBasicInfoSection(),
            // 工价设置区域 - 仅在项目工友时显示
            if (finalProps?.data?.source == WorkerSettingSource.projectWorker)
              _buildSalarySection(),
            // 记工统计区域 - 仅在项目工友时显示
            if (finalProps?.data?.source == WorkerSettingSource.projectWorker)
              _buildStatisticsSection(),
            // 工友对工权限区域
            _buildPermissionSection(),
          ],
        ),
      ),
    );
  }

  /// 基础信息区域
  Widget _buildBasicInfoSection() {
    return Obx(() {
      return Container(
        color: Colors.white,
        padding: const EdgeInsets.all(16),
        margin: const EdgeInsets.only(top: 8),
        child: Row(
          children: [
            // 头像
            _buildAvatar(),
            const SizedBox(width: 12),
            // 姓名和电话
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    viewModel.uiState.value.name ?? '',
                    style: const TextStyle(
                      fontSize: 17,
                      fontWeight: FontWeight.w500,
                      color: Colors.black,
                      height: 1.2,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    viewModel.uiState.value.phone ?? '',
                    style: TextStyle(
                      fontSize: 14,
                      color: ColorsUtil.black65,
                      height: 1.2,
                    ),
                  ),
                ],
              ),
            ),
            // 编辑按钮
            GestureDetector(
              onTap: _onEditWorker,
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    '编辑',
                    style: TextStyle(
                      fontSize: 16,
                      color: ColorsUtil.black65,
                    ),
                  ),
                  const SizedBox(width: 4),
                  Icon(
                    Icons.chevron_right,
                    size: 20,
                    color: ColorsUtil.black65,
                  ),
                ],
              ),
            ),
          ],
        ),
      );
    });
  }

  /// 头像组件
  Widget _buildAvatar() {
    return Obx(() {
      final avatar = viewModel.uiState.value.avatar;
      return Container(
        width: 47,
        height: 47,
        decoration: BoxDecoration(
          color: ColorsUtil.primaryColor,
          borderRadius: BorderRadius.circular(8),
        ),
        child: avatar?.isNotEmpty == true
            ? ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: Image.network(
                  avatar!,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    return _buildDefaultAvatar();
                  },
                ),
              )
            : _buildDefaultAvatar(),
      );
    });
  }

  /// 默认头像
  Widget _buildDefaultAvatar() {
    return Obx(() {
      final name = viewModel.uiState.value.name ?? '';
      return Container(
        width: 47,
        height: 47,
        decoration: BoxDecoration(
          color: ColorsUtil.primaryColor,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Center(
          child: Text(
            name.isNotEmpty ? getPortraitName(name) : '',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      );
    });
  }

  String getPortraitName(String name) {
    final nameLength = name.length;
    if (nameLength > 2) {
      // 获取最后两个字符
      return name.substring(nameLength - 2);
    }
    return name;
  }

  /// 工价设置区域
  Widget _buildSalarySection() {
    return Obx(() {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题
          Container(
            padding: const EdgeInsets.all(16),
            height: 50,
            child: Text(
              '工价设置',
              style: TextStyle(
                fontSize: 14,
                color: ColorsUtil.black65,
              ),
            ),
          ),
          // 点工工价
          _buildSalaryItem(
            title: '点工工价',
            salary: viewModel.uiState.value.partTimeSalary ?? '',
            overtime: viewModel.uiState.value.partOverTime ?? '',
            onTap: _onEditPartTimeSalary,
          ),
          // 包工工价
          _buildSalaryItem(
            title: '包工工价',
            salary: viewModel.uiState.value.allTimeSalary ?? '',
            overtime: viewModel.uiState.value.allOverTime ?? '',
            onTap: _onEditAllTimeSalary,
            showDivider: false,
          ),
        ],
      );
    });
  }

  /// 工价项
  Widget _buildSalaryItem({
    required String title,
    required String salary,
    required String overtime,
    required VoidCallback onTap,
    bool showDivider = true,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          color: Colors.white,
          border: showDivider
              ? const Border(
                  bottom: BorderSide(
                    color: Color(0xFFF0F0F0),
                    width: 0.5,
                  ),
                )
              : null,
        ),
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 16,
                      color: Colors.black,
                    ),
                  ),
                ],
              ),
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  salary,
                  style: TextStyle(
                    fontSize: 14,
                    color: ColorsUtil.black65,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  overtime,
                  style: TextStyle(
                    fontSize: 14,
                    color: ColorsUtil.black65,
                  ),
                ),
              ],
            ),
            const SizedBox(width: 8),
            Icon(
              Icons.chevron_right,
              size: 20,
              color: ColorsUtil.black65,
            ),
          ],
        ),
      ),
    );
  }

  /// 记工统计区域
  Widget _buildStatisticsSection() {
    return Obx(() {
      return Container(
        color: Color(0xfff5f6fa),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 标题
            Container(
              padding: const EdgeInsets.all(16),
              height: 50,
              child: Text(
                '记工统计',
                style: TextStyle(
                  fontSize: 14,
                  color: ColorsUtil.black65,
                ),
              ),
            ),
            // 统计项
            _buildStatisticsItem(
              title: '统计',
              subtitle: '对工',
              onTap: _onViewStatistics,
            ),
            // 未结项
            _buildStatisticsItem(
              title: '未结',
              subtitle: viewModel.notSettleMoney.value.isNotEmpty
                  ? viewModel.notSettleMoney.value
                  : '-200.00',
              subtitleColor: Colors.blue,
              onTap: _onViewUnsettled,
              showDivider: false,
            ),
          ],
        ),
      );
    });
  }

  /// 统计项
  Widget _buildStatisticsItem({
    required String title,
    required String subtitle,
    Color? subtitleColor,
    required VoidCallback onTap,
    bool showDivider = true,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          color: Colors.white,
          border: showDivider
              ? const Border(
                  bottom: BorderSide(
                    color: Color(0xFFF0F0F0),
                    width: 0.5,
                  ),
                )
              : null,
        ),
        child: Row(
          children: [
            Expanded(
              child: Text(
                title,
                style: const TextStyle(
                  fontSize: 16,
                  color: Colors.black,
                ),
              ),
            ),
            Text(
              subtitle,
              style: TextStyle(
                fontSize: 16,
                color: subtitleColor ?? ColorsUtil.black65,
              ),
            ),
            const SizedBox(width: 8),
            Icon(
              Icons.chevron_right,
              size: 20,
              color: ColorsUtil.black65,
            ),
          ],
        ),
      ),
    );
  }

  /// 工友对工权限区域
  Widget _buildPermissionSection() {
    return Obx(() {
      return Container(
        color: Color(0xfff5f6fa),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 标题
            Container(
              padding: const EdgeInsets.all(16),
              height: 50,
              child: Text(
                '工友对工权限',
                style: TextStyle(
                  fontSize: 14,
                  color: ColorsUtil.black65,
                ),
              ),
            ),
            // 查看记工数据权限
            _buildPermissionItem(
              title: '允许在线查看记工数据',
              value: viewModel.uiState.value.hasBookPermission ?? false,
              onChanged: _onBookPermissionChanged,
            ),
            // 查看记工工资权限
            _buildPermissionItem(
              title: '允许工人查看记工工资',
              value: (viewModel.uiState.value.hasBookPermission ?? false) &&
                  (viewModel.uiState.value.hasSalaryPermission ?? false),
              onChanged: (viewModel.uiState.value.hasBookPermission ?? false)
                  ? _onSalaryPermissionChanged
                  : null,
              showDivider: false,
            ),
          ],
        ),
      );
    });
  }

  /// 权限项
  Widget _buildPermissionItem({
    required String title,
    required bool value,
    required ValueChanged<bool>? onChanged,
    bool showDivider = true,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        border: showDivider
            ? const Border(
                bottom: BorderSide(
                  color: Color(0xFFF0F0F0),
                  width: 0.5,
                ),
              )
            : null,
      ),
      child: Row(
        children: [
          Expanded(
            child: Text(
              title,
              style: TextStyle(
                fontSize: 16,
                color: onChanged != null ? Colors.black : ColorsUtil.black45,
              ),
            ),
          ),
          Switch(
            value: value,
            onChanged: onChanged,
            activeColor: Colors.green,
          ),
        ],
      ),
    );
  }

  /// 事件处理方法

  /// 从项目中退场
  void _onRemoveFromProject() {
    ToastUtil.showToast('将工友从项目中退场');
    // TODO: 实现退场逻辑
  }

  /// 编辑工友信息
  void _onEditWorker() {
    ToastUtil.showToast('编辑工友信息');
    // TODO: 跳转到编辑页面
  }

  /// 编辑点工工价
  void _onEditPartTimeSalary() {
    ToastUtil.showToast('编辑点工工价');
    // TODO: 跳转到工价设置页面
  }

  /// 编辑包工工价
  void _onEditAllTimeSalary() {
    ToastUtil.showToast('编辑包工工价');
    // TODO: 跳转到工价设置页面
  }

  /// 查看统计
  void _onViewStatistics() {
    ToastUtil.showToast('查看统计');
    // TODO: 跳转到统计页面
  }

  /// 查看未结工资
  void _onViewUnsettled() {
    ToastUtil.showToast('查看未结工资');
    // TODO: 跳转到未结工资页面
  }

  /// 记工数据权限变化
  void _onBookPermissionChanged(bool value) {
    ToastUtil.showToast('记工数据权限: $value');
    // TODO: 调用接口更新权限
    // 如果关闭记工数据权限，同时关闭工资权限
    if (!value) {
      _onSalaryPermissionChanged(false);
    }
  }

  /// 工资权限变化
  void _onSalaryPermissionChanged(bool value) {
    ToastUtil.showToast('工资权限: $value');
    // TODO: 调用接口更新权限
  }
}
