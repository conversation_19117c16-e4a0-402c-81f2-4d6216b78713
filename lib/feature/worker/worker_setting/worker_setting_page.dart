import 'package:flutter/material.dart';
import 'package:gdjg_pure_flutter/init_module/init_route.dart';
import 'package:gdjg_pure_flutter/utils/route_util/base_page/base_ful_page.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/appbar_util.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/toast_util.dart';
import 'package:gdjg_pure_flutter/widget/page_error_view.dart';
import 'package:gdjg_pure_flutter/widget/page_loading_view.dart';
import 'package:get/get.dart';

import 'entity/worker_setting_props.dart';
import 'view/basic_info_section.dart';
import 'view/permission_section.dart';
import 'view/salary_section.dart';
import 'view/statistics_section.dart';
import 'vm/worker_setting_viewmodel.dart';

/// @date 2025/06/20 工友设置页面
/// @param props 页面路由参数
/// @returns
/// @description WorkerSetting页面入口
class WorkerSettingPage extends BaseFulPage {
  const WorkerSettingPage({super.key, this.props});

  final WorkerSettingProps? props;

  @override
  State createState() => WorkerSettingPageState();
}

class WorkerSettingPageState<WorkSettingPage> extends BaseFulPageState {
  late WorkerSettingProps? props;
  final WorkerSettingViewModel viewModel = WorkerSettingViewModel();

  @override
  void onPageRoute(Object? routeParams, bool fromLaunchTask) {
    super.onPageRoute(routeParams, fromLaunchTask);
    props = routeParams as WorkerSettingProps?;
  }

  @override
  Widget yBuild(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      appBar: _buildAppBar(),
      body: Obx(() {
        if (viewModel.isLoading.value) {
          return const PageLoadingView();
        }
        if (viewModel.isShowError.value == true) {
          return PageErrorView(onReload: () => viewModel.fetchData());
        }
        return contentView();
      }),
    );
  }

  /// 构建AppBar
  PreferredSizeWidget _buildAppBar() {
    final source = props?.data?.source;
    final projectName = props?.data?.projectName;

    // 根据source值确定标题
    String title = "工友设置";
    if (source == WorkerSettingSource.projectWorker && projectName != null) {
      title = projectName;
    }

    // 如果是项目工友，显示退场按钮
    if (source == WorkerSettingSource.projectWorker) {
      return AppBar(
        backgroundColor: Colors.white,
        leading: GestureDetector(
          onTap: () => YPRoute.closePage(null),
          child: Container(
            padding: const EdgeInsets.only(left: 8.0),
            child: const Icon(
              Icons.arrow_back,
              color: Colors.black,
            ),
          ),
        ),
        leadingWidth: 30,
        titleSpacing: 8,
        title: Text(
          title,
          style: const TextStyle(
            color: Colors.black,
            fontSize: 22,
            fontWeight: FontWeight.w600,
          ),
        ),
        centerTitle: false,
        actions: [
          GestureDetector(
            onTap: viewModel.onRemoveFromProject,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              child: Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                decoration: const BoxDecoration(
                  borderRadius: BorderRadius.all(Radius.circular(12)),
                  color: Color(0xfffee5e4),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.logout,
                      size: 16,
                      color: Colors.red,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      '将工友从项目中退场',
                      style: TextStyle(
                        fontSize: 13,
                        color: Colors.red,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      );
    } else {
      return AppBarUtil.buildCommonAppBar(
        title: title,
        backgroundColor: Colors.white,
        iconColor: Colors.black,
      );
    }
  }

  /// 实际展示的视图
  Widget contentView() {
    return SingleChildScrollView(
      child: ColoredBox(
        color: Color(0xfff5f6fa),
        child: Column(
          children: [
            // 基础信息区域
            BasicInfoSection(viewModel: viewModel),
            // 工价设置区域 - 仅在项目工友时显示
            if (props?.data?.source == WorkerSettingSource.projectWorker)
              SalarySection(viewModel: viewModel),
            // 记工统计区域 - 仅在项目工友时显示
            if (props?.data?.source == WorkerSettingSource.projectWorker)
              StatisticsSection(viewModel: viewModel),
            // 工友对工权限区域
            PermissionSection(viewModel: viewModel),
          ],
        ),
      ),
    );
  }
}
