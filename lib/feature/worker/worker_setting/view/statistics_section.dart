import 'package:flutter/material.dart';
import 'package:gdjg_pure_flutter/feature/worker/worker_setting/view/item_title_view.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';
import 'package:get/get.dart';

import '../vm/worker_setting_viewmodel.dart';

/// 记工统计区域组件
class StatisticsSection extends StatelessWidget {
  final WorkerSettingViewModel viewModel;

  const StatisticsSection({
    super.key,
    required this.viewModel,
  });

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      return Container(
        color: Color(0xfff5f6fa),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 标题
            ItemTitleView(title: '记工统计'),
            // 统计项
            _StatisticsItem(
              title: '统计',
              subtitle: '对工',
              onTap: viewModel.onViewStatistics,
            ),
            // 未结项
            _StatisticsItem(
              title: '未结',
              subtitle: viewModel.notSettleMoney.value.isNotEmpty
                  ? viewModel.notSettleMoney.value
                  : '-200.00',
              subtitleColor: Colors.blue,
              onTap: viewModel.onViewUnsettled,
              showDivider: false,
            ),
          ],
        ),
      );
    });
  }
}

/// 统计项组件
class _StatisticsItem extends StatelessWidget {
  final String title;
  final String subtitle;
  final Color? subtitleColor;
  final VoidCallback onTap;
  final bool showDivider;

  const _StatisticsItem({
    required this.title,
    required this.subtitle,
    this.subtitleColor,
    required this.onTap,
    this.showDivider = true,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          color: Colors.white,
          border: showDivider
              ? const Border(
                  bottom: BorderSide(
                    color: Color(0xFFF0F0F0),
                    width: 0.5,
                  ),
                )
              : null,
        ),
        child: Row(
          children: [
            Expanded(
              child: Text(
                title,
                style: const TextStyle(
                  fontSize: 16,
                  color: Colors.black,
                ),
              ),
            ),
            Text(
              subtitle,
              style: TextStyle(
                fontSize: 16,
                color: subtitleColor ?? ColorsUtil.black65,
              ),
            ),
            const SizedBox(width: 8),
            Icon(
              Icons.chevron_right,
              size: 20,
              color: ColorsUtil.black65,
            ),
          ],
        ),
      ),
    );
  }
}
