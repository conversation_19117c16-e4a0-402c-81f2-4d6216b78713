import 'package:flutter/material.dart';
import 'package:gdjg_pure_flutter/feature/worker/worker_setting/view/item_title_view.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';
import 'package:get/get.dart';

import '../vm/worker_setting_viewmodel.dart';

/// 工价设置区域组件
class SalarySection extends StatelessWidget {
  final WorkerSettingViewModel viewModel;

  const SalarySection({
    super.key,
    required this.viewModel,
  });

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题
          ItemTitleView(title: '工价设置'),
          // 点工工价
          _SalaryItem(
            title: '点工工价',
            salary: viewModel.salaryUiState.value.partTimeSalary ?? '',
            overtime: viewModel.salaryUiState.value.partOverTime ?? '',
            onTap: () => {viewModel.onEditPartTimeSalary(context)},
          ),
          // 包工工价
          _SalaryItem(
            title: '包工工价',
            salary: viewModel.salaryUiState.value.allTimeSalary ?? '',
            overtime: viewModel.salaryUiState.value.allOverTime ?? '',
            onTap: () => {viewModel.onEditAllTimeSalary(context)},
            showDivider: false,
          ),
        ],
      );
    });
  }
}

/// 工价项组件
class _SalaryItem extends StatelessWidget {
  final String title;
  final String salary;
  final String overtime;
  final VoidCallback onTap;
  final bool showDivider;

  const _SalaryItem({
    required this.title,
    required this.salary,
    required this.overtime,
    required this.onTap,
    this.showDivider = true,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          color: Colors.white,
          border: showDivider
              ? const Border(
                  bottom: BorderSide(
                    color: Color(0xFFF0F0F0),
                    width: 0.5,
                  ),
                )
              : null,
        ),
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 16,
                      color: Colors.black,
                    ),
                  ),
                ],
              ),
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  salary,
                  style: TextStyle(
                    fontSize: 14,
                    color: ColorsUtil.black65,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  overtime,
                  style: TextStyle(
                    fontSize: 14,
                    color: ColorsUtil.black65,
                  ),
                ),
              ],
            ),
            const SizedBox(width: 8),
            Icon(
              Icons.chevron_right,
              size: 20,
              color: ColorsUtil.black65,
            ),
          ],
        ),
      ),
    );
  }
}
