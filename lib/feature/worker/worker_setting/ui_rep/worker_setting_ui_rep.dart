import 'package:gdjg_pure_flutter/data/demo_setting_data/entity/worker_info/group_worker/group_project_get_group_worker_settle_entity.dart';
import 'package:gdjg_pure_flutter/data/demo_setting_data/entity/worker_info/worker/workers_get_worker_info_entity.dart';
import 'package:gdjg_pure_flutter/data/demo_setting_data/repo/worker_statistics_repo.dart';
import 'package:get/get.dart';

/// @date 2025/06/20
/// @description WorkerSetting页UI仓库
/// 1.为vm提供业务数据
/// 2.解耦视图层与业务仓库之间的耦合
class WorkerSettingUIRep {
  /// 实体数据
  var entity = WorkersGetWorkerInfoAEntity().obs;

  var noSettleMoney = ''.obs;

  final repo = WorkerStatisticsRepo();

  bool getStatus() {
    return true;
  }

  /// 初始化数据同步，返回同步是否成功的状态给UI层
  /// 如果成功，则将业务数据存储在entity中
  Future<WorkersGetWorkerInfoAEntity> fetchWorkerInfo() async {
    // 调用网络的方法获取数据
    await Future.delayed(const Duration(milliseconds: 500));
    var result = await repo.fetchWorkerInfo();
    var workerInfo = WorkersGetWorkerInfoAEntity().transform(result);
    // 返回成功的情况
    entity.value = workerInfo;
    // 模拟异常情况
    // throw Exception("error");
    return entity.value;
  }

  /// 获取工友所在项目中未结工资信息
  Future<String> fetchSettleInfo() async {
    // 调用网络的方法获取数据
    await Future.delayed(const Duration(milliseconds: 500));
    var result = await repo.fetchGroupWorkerSettle();
    var settleInfo =
        GroupProjectGetGroupWorkerSettleAEntity().transform(result);
    noSettleMoney.value = settleInfo.notSettleMoney.toString();

    return noSettleMoney.value;
  }
}
