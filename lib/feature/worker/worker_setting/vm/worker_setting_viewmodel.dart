import "package:gdjg_pure_flutter/data/demo_setting_data/entity/worker_info/worker/workers_get_worker_info_entity.dart";
import "package:get/get.dart";

import "../ui_rep/worker_setting_ui_rep.dart";
import "protocol/worker_setting_ui_state.dart";

/// @date 2025/06/20
/// @description WorkerSetting页ViewModel
/// 1. 处理数据的加载状态，如loading和错误视图等
/// 2. 观测UIRep中的业务实体转为UIState
/// 3. 接受来自view的意图并分发事件进行处理（暂无）
/// 4. 对View层暴露事件（操作视图和处理一些与系统权限交互等场景）
class WorkerSettingViewModel {
  var isLoading = false.obs;
  var isShowError = false.obs;
  var uiState = WorkerSettingUIState().obs;

  /// 未结工资
  var notSettleMoney = ''.obs;
  var uiRep = WorkerSettingUIRep();

  WorkerSettingViewModel() {
    fetchData();
    fetchSettleData();
    ever(uiRep.entity, (value) {
      convertEntityToUIState(value);
    });
    ever(uiRep.noSettleMoney, (value) {
      notSettleMoney.value = value;
    });
  }

  /// 初始化数据同步，如果不需要loading，则在这里去掉
  void fetchData() async {
    isLoading.value = true;
    try {
      // 同步获取数据
      var result = await uiRep.fetchWorkerInfo();
      // 这里的result仅做弹窗之类的处理，不在此处转为UI实体
    } catch (e) {
      isShowError.value = true;
    } finally {
      isLoading.value = false;
    }
  }

  void fetchSettleData() async {
    try {
      var settle = await uiRep.fetchSettleInfo();
    } catch (e) {
      // 次要信息接口不需要loading，不关注报错
    }
  }

  /// 将实体数据转换为UIState
  /// @param entity
  /// @returns
  void convertEntityToUIState(WorkersGetWorkerInfoAEntity entity) {
    uiState.value.hasBookPermission = entity.isShow == 1 || entity.isShow == 2;
    uiState.value.hasSalaryPermission = entity.isShow == 2;
    uiState.value.partTimeSalary =
        '1个共${entity.feeStandardInfo?.workingHoursStandard}小时${entity.feeStandardInfo?.workingHoursPrice}元';
    uiState.value.allTimeSalary =
        '1个共${entity.contractorFeeStandardInfo?.workingHoursStandard}小时${entity.contractorFeeStandardInfo?.workingHoursPrice}元';
    uiState.value.partOverTime =
        '加班${entity.feeStandardInfo?.overtimeHoursStandard}小时1个工';
    uiState.value.allOverTime =
        '加班1小时${entity.contractorFeeStandardInfo?.overtimeHoursPrice}元';
    uiState.value.name = entity.name;
    uiState.value.avatar = entity.avatar;
    uiState.value.phone = entity.tel;
  }
}
