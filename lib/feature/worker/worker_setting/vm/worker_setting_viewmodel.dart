import "package:gdjg_pure_flutter/data/demo_setting_data/entity/worker_info/worker/workers_get_worker_info_entity.dart";
import "package:gdjg_pure_flutter/init_module/init_route.dart";
import "package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart";
import "package:get/get.dart";

import "../../../../utils/ui_util/toast_util.dart";
import "../ui_rep/worker_setting_ui_rep.dart";
import "protocol/worker_setting_ui_state.dart";

/// @date 2025/06/20
/// @description WorkerSetting页ViewModel
/// 1. 处理数据的加载状态，如loading和错误视图等
/// 2. 观测UIRep中的业务实体转为UIState
/// 3. 接受来自view的意图并分发事件进行处理（暂无）
/// 4. 对View层暴露事件（操作视图和处理一些与系统权限交互等场景）
class WorkerSettingViewModel {
  var isLoading = false.obs;
  var isShowError = false.obs;
  var baseUiState = WorkerSettingUIState().obs;
  var permissionUiState = WSPermissionUIState().obs;
  var salaryUiState = WSSalaryUIState().obs;

  /// 未结工资
  var notSettleMoney = ''.obs;
  var uiRep = WorkerSettingUIRep();

  WorkerSettingViewModel() {
    fetchData();
    fetchSettleData();
    ever(uiRep.entity, (value) {
      convertEntityToUIState(value);
    });
    ever(uiRep.noSettleMoney, (value) {
      notSettleMoney.value = value;
    });
  }

  /// 初始化数据同步，如果不需要loading，则在这里去掉
  void fetchData() async {
    isLoading.value = true;
    try {
      // 同步获取数据
      var result = await uiRep.fetchWorkerInfo();
      // 这里的result仅做弹窗之类的处理，不在此处转为UI实体
    } catch (e) {
      isShowError.value = true;
    } finally {
      isLoading.value = false;
    }
  }

  void fetchSettleData() async {
    try {
      var settle = await uiRep.fetchSettleInfo();
    } catch (e) {
      // 次要信息接口不需要loading，不关注报错
    }
  }

  /// 事件处理方法

  /// 从项目中退场
  void onRemoveFromProject() {
    ToastUtil.showToast('将工友从项目中退场');
    // TODO: 实现退场逻辑
  }

  /// 编辑工友信息
  void onEditWorker() {
    YPRoute.openPage(RouteNameCollection.workerResume);
  }

  /// 编辑点工工价
  void onEditPartTimeSalary() {
    ToastUtil.showToast('编辑点工工价');
    // TODO: 跳转到工价设置页面
  }

  /// 编辑包工工价
  void onEditAllTimeSalary() {
    ToastUtil.showToast('编辑包工工价');
    // TODO: 跳转到工价设置页面
  }

  /// 查看统计
  void onViewStatistics() {
    ToastUtil.showToast('班组流水页');
    // TODO: 班组流水页
  }

  /// 查看未结工资
  void onViewUnsettled() {
    ToastUtil.showToast('未结算详情页');
    // TODO: 未结算详情页
  }

  /// 记工数据权限变化：关闭记工数据查看权限时，如果有记工工资，则同步关闭记工工资查看权限
  void onBookPermissionChanged(bool value) {
    // 如果关闭记工数据权限，同时关闭工资权限
    if (!value) {
      updatePermissionStatus(true, false);
      return;
    }
    updatePermissionStatus(true, true);
  }

  /// 工资权限变化：开启查看工资权限时，需要同时前置开启记工数据权限
  void onSalaryPermissionChanged(bool value) {
    if (value) {
      updatePermissionStatus(false, true);
      return;
    }
    updatePermissionStatus(false, false);
  }

  /// 更新权限状态:不允许查看记工数据type=0,仅允许查看记工数据 type = 2， 都允许type = 1
  void updatePermissionStatus(bool isBook, bool value) async {
    try {
      final hasSalary = permissionUiState.value.hasSalaryPermission ?? false;
      final hasBook = permissionUiState.value.hasBookPermission ?? false;
      var type = 1;
      if (isBook) {
        // 仅允许查看记工数据 type = 2， 都允许type = 1
        type = value ? (hasSalary ? 1 : 2) : 0;
      } else {
        // 工资数据查看权限关闭时，记工数据查看权限也关闭-置为0；工资数据查看权限开启时，记工数据查看权限也一定是开启的-置为1；
        type = value ? 1 : (hasBook ? 2 : 0);
      }
      var result = await uiRep.updatePermission(type);
      if (result >= 0) {
        // 更新成功则刷新UI
        convertEntityToSalaryUIState(result.toDouble());
      }
    } catch (e) {
      // 次要信息接口不需要loading
    }
  }

  /// 将实体数据转换为UIState
  /// @param entity
  /// @returns
  void convertEntityToUIState(WorkersGetWorkerInfoAEntity entity) {
    convertEntityToSalaryUIState(entity.isShow);
    salaryUiState.value.partTimeSalary =
        '1个共${entity.feeStandardInfo?.workingHoursStandard}小时${entity.feeStandardInfo?.workingHoursPrice}元';
    salaryUiState.value.allTimeSalary =
        '1个共${entity.contractorFeeStandardInfo?.workingHoursStandard}小时${entity.contractorFeeStandardInfo?.workingHoursPrice}元';
    salaryUiState.value.partOverTime =
        '加班${entity.feeStandardInfo?.overtimeHoursStandard}小时1个工';
    salaryUiState.value.allOverTime =
        '加班1小时${entity.contractorFeeStandardInfo?.overtimeHoursPrice}元';
    baseUiState.value.name = entity.name;
    baseUiState.value.avatar = entity.avatar;
    baseUiState.value.phone = entity.tel;
  }

  void convertEntityToSalaryUIState(double isShow) {
    final bookPermission = isShow == 1 || isShow == 2;
    final salaryPermission = isShow == 1;
    permissionUiState.value = WSPermissionUIState(
      hasBookPermission: bookPermission,
      hasSalaryPermission: salaryPermission,
    );
  }
}
