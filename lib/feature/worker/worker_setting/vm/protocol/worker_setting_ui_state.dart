/// @date 2025/06/20
/// @description JDetail页UI状态
/// @param notSettleMoney 未结工资
/// @param hasBookPermission 是否有记工数据查看权限
/// @param hasSalaryPermission 是否有工资查看权限：需要前置权限-记工数据查看权限
/// @param partTimeSalary 点工工资
/// @param allTimeSalary 包工工资
/// @param partOverTime 点工加班
/// @param allOverTime 包工加班
/// @param workerName 工友姓名
/// @param avatar 头像
/// @param phone 手机号
class WorkerSettingUIState {
  bool? hasBookPermission;
  bool? hasSalaryPermission;
  String? partTimeSalary;
  String? partOverTime;
  String? allTimeSalary;
  String? allOverTime;
  String? name;
  String? avatar;
  String? phone;

  WorkerSettingUIState({
    this.hasBookPermission,
    this.hasSalaryPermission,
    this.partTimeSalary,
    this.allTimeSalary,
    this.partOverTime,
    this.allOverTime,
    this.name,
    this.avatar,
    this.phone,
  });

  @override
  String toString() {
    return 'WorkerSettingUIState{hasBookPermission: $hasBookPermission, hasSalaryPermission: $hasSalaryPermission, partTimeSalary: $partTimeSalary, allTimeSalary: $allTimeSalary, workerName: $name, avatar: $avatar, phone: $phone}, partOverTime: $partOverTime, allOverTime: $allOverTime}';
  }
}
