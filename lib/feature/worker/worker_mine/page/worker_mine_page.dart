import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/feature/bill_flow/page/person_account_record_page.dart';
import 'package:gdjg_pure_flutter/feature/cloud_album/group/page/cloud_album_group_page.dart';
import 'package:gdjg_pure_flutter/init_module/init_route.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:get/get.dart';

import '../../../demo_setting_feature/page/setting_page.dart';

class WorkerProfilePage extends StatelessWidget {
  const WorkerProfilePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      color: const Color(0xFFF5F5F5),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            ElevatedButton(
              onPressed: () {
                // 第一个按钮的点击事件
                // Navigator.push(
                //   context,
                //   MaterialPageRoute(builder: (context) => CloudAlbumGroupPage()),
                // );
                // Get.toNamed(Routes.cloudAlbumGroup);
              },
              child: Text('云相册'),
            ),
            SizedBox(height: 20.h), // 增加两个按钮之间的间距
            ElevatedButton(
              onPressed: () {
                // 第一个按钮的点击事件
                Navigator.push(
                  context,
                  MaterialPageRoute(
                      builder: (context) => PersonAccountRecordPage()),
                );
              },
              child: Text('记/借支'),
            ),
            SizedBox(height: 20.h), // 增加两个按钮之间的间距
            ElevatedButton(
              onPressed: () {
                Navigator.pushNamed(context, '/worker_mine_page');
              },
              child: Text(
                '我的页面',
                style: TextStyle(
                  fontSize: 16.sp,
                  color: const Color(0xFF222222),
                ),
              ),
            ),
            SizedBox(height: 20.h), // 增加两个按钮之间的间距
            ElevatedButton(
              onPressed: () {
                // 第二个按钮的点击事件
                // Navigator.push(
                //   context,
                //   MaterialPageRoute(builder: (context) => SettingPage()),
                // );
                YPRoute.openPage(RouteNameCollection.setting);
              },
              child: Text(
                '测试设置页面',
                style: TextStyle(
                  fontSize: 16.sp,
                  color: const Color(0xFF222222),
                ),
              ),
            ),
            SizedBox(height: 20.h), // 增加两个按钮之间的间距
            ElevatedButton(
              onPressed: () {
                // 第二个按钮的点击事件
                // Navigator.push(
                //   context,
                //   MaterialPageRoute(builder: (context) => SettingPage()),
                // );
                YPRoute.openPage(RouteNameCollection.contact);
              },
              child: Text(
                '通讯录',
                style: TextStyle(
                  fontSize: 16.sp,
                  color: const Color(0xFF222222),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
