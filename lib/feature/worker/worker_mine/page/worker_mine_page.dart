import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/feature/bill_flow/page/worker_account_record_page.dart';
import 'package:gdjg_pure_flutter/feature/group/group_liquidated/group_liquidated_page.dart';
import 'package:gdjg_pure_flutter/init_module/init_route.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/feature/feedback/util/feedback_route_extension.dart';

class WorkerProfilePage extends StatelessWidget {
  const WorkerProfilePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      body: SingleChildScrollView(
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              ElevatedButton(
                onPressed: () {
                  // 第一个按钮的点击事件
                  YPRoute.openPage(RouteNameCollection.accountManage);
                },
                child: Text('账号管理'),
              ),
              SizedBox(height: 20.h),
              ElevatedButton(
                onPressed: () {
                  // 第一个按钮的点击事件
                  FeedbackRouteExtension.openFeedbackPage();
                },
                child: Text('意见反馈'),
              ),
              SizedBox(height: 20.h),
              ElevatedButton(
                onPressed: () {
                  // 第一个按钮的点击事件
                  YPRoute.openPage(RouteNameCollection.groupCloudAlbum);
                },
                child: Text('云相册'),
              ),
              SizedBox(height: 20.h), // 增加两个按钮之间的间距
              ElevatedButton(
                onPressed: () {
                  // 第一个按钮的点击事件
                  YPRoute.openPage(RouteNameCollection.transferRecord);
                },
                child: Text('转移记工'),
              ),
              SizedBox(height: 20.h),
              ElevatedButton(
                onPressed: () {
                  // 第一个按钮的点击事件
                  YPRoute.openPage(RouteNameCollection.yuPaoNews);
                },
                child: Text('鱼泡资讯'),
              ),
              SizedBox(height: 20.h),
              ElevatedButton(
                onPressed: () {
                  // 第一个按钮的点击事件
                  // todo:LINer跳转页面前请求接口https://yapi.3pvr.com/project/714/interface/api/12051 拿到url
                  YPRoute.openWebPage(
                      url:
                          "http://cdn.cdypkj.cn/h5-dev/video-tutorial/index.html#/?version=5.1.2&source=android&identity=2&token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpYXQiOjE3NTIyMjYyNDEsImV4cCI6MTc2MjU5NDI0MSwiZGF0YSI6eyJzaW5nbGUiOiJGRlVYNkZBNkROQ0o0VjhKIiwidWlkIjoyNTEzNzk5OSwiYnVzaW5lc3MiOiIxIiwic3lzdGVtX3R5cGUiOiJhbmRyb2lkIiwibWluaV90b2tlbiI6IkZGVVg2RkE2RE5DSjRWOEoiLCJpZCI6MjUxMzc5OTksInV1aWQiOjI1MTM3OTk5fSwidG9rZW4iOnsicmVnUnQiOiJhbmRyb2lkIiwidGVuYW50S2V5IjoiWVBaUCIsInRlbmFudElkIjoyNTEzNzk5OSwicGFja2FnZU5hbWUiOiJpby5kY2xvdWQuSDU3NkU2Q0M3IiwidXNlcklkIjoyNTEzNzk5OSwidG9rZW4iOiJGRlVYNkZBNkROQ0o0VjhKIn19.BqaD-Mtu7nHd1bLWHl1Ma0pq94yPwJbzx17yfaO64b8&uid=25137999",
                      title: "视频教程");
                },
                child: Text('视频教程'),
              ),
              SizedBox(height: 20.h), // 增加两个按钮之间的间距
              ElevatedButton(
                onPressed: () {
                  Navigator.pushNamed(context, '/worker_mine_page');
                },
                child: Text(
                  '我的页面',
                  style: TextStyle(
                    fontSize: 16.sp,
                    color: const Color(0xFF222222),
                  ),
                ),
              ),
              SizedBox(height: 20.h), // 增加两个按钮之间的间距
              ElevatedButton(
                onPressed: () {
                  // 第二个按钮的点击事件
                  // Navigator.push(
                  //   context,
                  //   MaterialPageRoute(builder: (context) => SettingPage()),
                  // );
                  YPRoute.openPage(RouteNameCollection.setting);
                },
                child: Text(
                  '测试设置页面',
                  style: TextStyle(
                    fontSize: 16.sp,
                    color: const Color(0xFF222222),
                  ),
                ),
              ),
              SizedBox(height: 20.h), // 增加两个按钮之间的间距
              ElevatedButton(
                onPressed: () {
                  // 第二个按钮的点击事件
                  // Navigator.push(
                  //   context,
                  //   MaterialPageRoute(builder: (context) => SettingPage()),
                  // );
                  YPRoute.openPage(RouteNameCollection.contact);
                },
                child: Text(
                  '通讯录',
                  style: TextStyle(
                    fontSize: 16.sp,
                    color: const Color(0xFF222222),
                  ),
                ),
              ),
              SizedBox(height: 20.h), // 增加两个按钮之间的间距
              ElevatedButton(
                onPressed: () {
                  // 第二个按钮的点击事件
                  // Navigator.push(
                  //   context,
                  //   MaterialPageRoute(builder: (context) => SettingPage()),
                  // );
                  startPageGroupUnLiquidated(
                      workNoteName: '测试项目名称1',
                      workNote: '781317',
                      isJoin: false,
                      deptId: 0,
                      isAgent: 0);
                },
                child: Text(
                  '未结',
                  style: TextStyle(
                    fontSize: 16.sp,
                    color: const Color(0xFF222222),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
