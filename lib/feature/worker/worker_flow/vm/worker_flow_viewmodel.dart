import "package:gdjg_pure_flutter/data/demo_setting_data/entity/worker_flow/count/business_get_index_business_count_biz_model.dart";
import "package:gdjg_pure_flutter/feature/worker/worker_flow/vm/protocol/worker_flow_header_ui_state.dart";
import "package:get/get.dart";
import "../ui_rep/worker_flow_ui_rep.dart";
import "protocol/worker_flow_ui_state.dart";

/// @date 2025/06/12
/// @description WorkerFlow页ViewModel
/// 1. 处理数据的加载状态，如loading和错误视图等
/// 2. 观测UIRep中的业务实体转为UIState
/// 3. 接受来自view的意图并分发事件进行处理（暂无）
/// 4. 对View层暴露事件（操作视图和处理一些与系统权限交互等场景）
class WorkerFlowViewModel {
  var isLoading = false.obs;
  var isShowError = false.obs;
  var uiFlowState = WorkerFlowUIState().obs;
  var uiHeaderState = WorkerFlowHeaderUIState().obs;
  var isHiddenUIState = false.obs;
  var uiRep = WorkerFlowUIRep();

  WorkerFlowViewModel() {
    fetchData();
    fetchData();
    ever(uiRep.flowEntity, (value) {
      convertFlowEntityToUIState(value);
    });
    ever(uiRep.headerEntity, (value) {
      convertHeaderEntityToUIState(value);
    });
  }

  /// 初始化数据同步，如果不需要loading，则在这里去掉
  void fetchData({bool isShowLoading = true}) async {
    if (isShowLoading) {
      isLoading.value = true;
    }
    try {
      // 同步获取数据
      // var result = uiRep.getStatus();
      await uiRep.fetchHeaderData();
      await uiRep.fetchListData();
      // 这里的result仅做弹窗之类的处理，不在此处转为UI实体
    } catch (e, p) {
      print("e: $e \np: $p");
      uiFlowState.value.data = [];
      uiFlowState.value.isShowError = true;
    } finally {
      isLoading.value = false;
    }
  }

  /// 将实体数据转换为UIState
  /// @param entity
  /// @returns
  void convertFlowEntityToUIState(WorkerFlowUIRepEntity entity) {
    if (entity.data == null) return;
    List<BusinessUIEntity> tempList = [];
    for (var i = 0; i < entity.data!.length; i++) {
      var item = entity.data![i];
      tempList.add(BusinessUIEntity(WorkerFlowType.date, date: item.date));
      var dayList = item.list;
      if (dayList.isEmpty) {
        // 添加无数据Item
        tempList.add(BusinessUIEntity(WorkerFlowType.recordable, date: item.date));
      } else {
        for (var j = 0; j < dayList.length; j++) {
          final mItem = dayList[j];
          var subWorkList = <SubWorkEntity>[];
          var mMoney = mItem.money.isEmpty ? "0" : mItem.money;

          /// 点工、包工
          if (BusinessType.workAndAccountTypeDays.code == mItem.businessType ||
              BusinessType.workAndAccountTypePackage.code == mItem.businessType) {
            var content = "";
            if (mItem.workTime > 0) {
              String wTimes = mItem.workTime.toString();
              if (wTimes.endsWith(".0")) {
                wTimes = wTimes.substring(0, wTimes.length - 2);
              }
              content = "上班：$wTimes个工";
            }
            if (mItem.workTimeHour > 0 && content.isEmpty) {
              content = "上班：${mItem.workTimeHour}小时";
            }

            if (mItem.morningWorkTime.isNotEmpty ||
                mItem.morningWorkTimeHour.isNotEmpty ||
                mItem.afternoonWorkTime.isNotEmpty ||
                mItem.afternoonWorkTimeHour.isNotEmpty) {
              var morningWork = '';
              if (mItem.morningWorkTime.isNotEmpty) {
                morningWork = "上午: ${mItem.morningWorkTime.toString()}个工";
              } else {
                morningWork = "上午: 休息";
              }

              var afternoonWork = '';
              if (mItem.afternoonWorkTime.isNotEmpty) {
                afternoonWork = "下午: ${mItem.afternoonWorkTime.toString()}个工";
              } else {
                afternoonWork = '下午: 休息';
              }
              content = (morningWork.isNotEmpty && afternoonWork.isNotEmpty)
                  ? "$morningWork\n$afternoonWork"
                  : "$morningWork$afternoonWork";
            }
            if (content.isEmpty) {
              content = "上班：休息";
            }
            subWorkList.add(SubWorkEntity(title: "上班", content: content));
          }

          /// 加班
          if (mItem.overtime > 0) {
            subWorkList.add(SubWorkEntity(title: "加班", content: "${mItem.overtime}个小时"));
          }
          if (mItem.overtimeWork > 0) {
            subWorkList.add(SubWorkEntity(title: "加班", content: "${mItem.overtime}个工"));
          }

          /// 工量
          if (mItem.businessType == BusinessType.workAndAccountTypeLoad.code) {
            if (mItem.unitWorkTypeName.isNotEmpty) {
              subWorkList.add(SubWorkEntity(title: "分项", content: mItem.unitWorkTypeName));
            }
            if (mItem.unitPrice.isNotEmpty) {
              var content = "";
              if (mItem.unitWorkTypeUnit.isNotEmpty) {
                content = "${mItem.unitPrice}元/${mItem.unitWorkTypeUnit}";
              } else {
                content = mItem.unitPrice;
              }
              subWorkList.add(SubWorkEntity(title: "单价", content: content));
            }
            if (mItem.unitNum.isNotEmpty) {
              subWorkList.add(
                SubWorkEntity(title: "工程量", content: mItem.unitNum + mItem.unitWorkTypeUnit),
              );
            }
          }

          /// 其他费用
          if (mItem.businessType == BusinessType.workAndAccountTypeExpense.code) {
            subWorkList.add(
              SubWorkEntity(title: "费用名称", content: mItem.otherExpenses?.name ?? "无"),
            );
          }
          var resList = <ResourceEntity>[];

          /// 图片
          if (mItem.imgInfo.isNotEmpty) {
            for (final (index, img) in mItem.imgInfo.indexed) {
              resList.add(ResourceEntity(
                url: img.url,
                isVideo: img.type == "video",
                cover: img.cover,
                index: index,
              ));
            }
            subWorkList.add(SubWorkEntity(title: "工作证据", content: "${resList.length}张"));
          }

          /// 钱
          var doubleMoney = double.parse(mMoney);
          mMoney = (doubleMoney + mItem.feeMoney).toStringAsFixed(2);

          var isHighLight = mItem.businessType == BusinessType.workAndAccountTypeDebt.code ||
              mItem.businessType == BusinessType.workAndAccountTypeWageLast.code;
          tempList.add(BusinessUIEntity(
            WorkerFlowType.info,
            subWorkList: subWorkList,
            tag: BusinessType.values[mItem.businessType.toInt() - 1].desc,
            projectName: mItem.workNoteName,
            totalMoney: mMoney,
            resList: resList,
            isHighLight: isHighLight,
            remake: mItem.note,
            isFirstInfoItem: tempList.last.type == WorkerFlowType.date,
          ));
        }
      }
    }
    uiFlowState.value = WorkerFlowUIState(data: tempList);
  }

  /// 头部统计数据
  void convertHeaderEntityToUIState(BusinessCountBizModel bizModel) {
    List<BusinessCountUIState> tempEntity = [];

    /// 点工
    var mSpotWork = bizModel.spotWork;
    if (mSpotWork != null && mSpotWork.num > 0) {
      var content = "上班:";
      var mWorkTime = double.parse(mSpotWork.workTime);
      if (mWorkTime > 0) {
        content += "${mSpotWork.workTime}个工";
      }
      var mWorkTimeHour = double.parse(mSpotWork.workTimeHour);
      if (mWorkTimeHour > 0) {
        content += "${mSpotWork.workTimeHour}小时";
      }
      if (double.parse(mSpotWork.overtime) > 0) {
        content += "\n加班:${mSpotWork.overtime}小时";
      }
      var mOvertimeWork = double.parse(mSpotWork.overtimeWork);
      if (mOvertimeWork > 0) {
        content += "\n加班:${mSpotWork.overtimeWork}个工";
      }
      var mMoney = mSpotWork.spotWorkFeeMoney.toStringAsFixed(2);
      tempEntity.add(BusinessCountUIState(
        title: "点工",
        content: content,
        moneyText: mMoney,
        type: "1",
        key: "spot_work",
        isFirst: tempEntity.isEmpty,
        identity: 2,
        businessType: BusinessType.workAndAccountTypeDays.code.toString(),
      ));
    }

    /// 包工
    var mContractor = bizModel.contractor;
    if (mContractor != null && mContractor.num > 0) {
      var content = "上班:";
      var mWorkTime = double.parse(mContractor.contractorWorkTime);
      if (mWorkTime > 0) {
        content += "${mContractor.contractorWorkTime}个工";
      }
      var mWorkTimeHour = double.parse(mContractor.contractorWorkTimeHour);
      if (mWorkTimeHour > 0) {
        content += "${mContractor.contractorWorkTimeHour}小时";
      }
      var mOverTime = double.parse(mContractor.contractorOvertime);
      if (mOverTime > 0) {
        content += "\n加班:${mContractor.contractorOvertime}小时";
      }
      var mMoney = mContractor.contractorMoney.toStringAsFixed(2);
      tempEntity.add(BusinessCountUIState(
        title: "包工",
        content: content,
        moneyText: mMoney,
        type: "1",
        key: "contractor",
        isFirst: tempEntity.isEmpty,
        identity: 2,
        businessType: BusinessType.workAndAccountTypePackage.code.toString(),
      ));
    }

    /// 工量
    var mUnit = bizModel.unit;
    if (mUnit != null && mUnit.num > 0) {
      for (final (index, mItem) in mUnit.countUnit.indexed) {
        var title = "工量 ${mItem.unitWorkTypeName}";
        var subTitle = "${mItem.num}笔";
        var mMoney = mItem.unitMoney;
        var mSubMoney = "总计:${mItem.count}${mItem.unitWorkTypeUnit}";
        tempEntity.add(BusinessCountUIState(
          title: title,
          subTitle: subTitle,
          moneyText: mMoney,
          subMoneyText: mSubMoney,
          type: "1",
          key: "countUnit_$index",
          isFirst: tempEntity.isEmpty,
          identity: 2,
          businessType: BusinessType.workAndAccountTypeLoad.code.toString(),
          workType: 'unit',
        ));
      }
    }

    /// 短工
    var mMoney = bizModel.workMoney;
    if (mMoney != null && mMoney.num > 0) {
      var content = "${mMoney.num}笔";
      tempEntity.add(BusinessCountUIState(
        title: "短工",
        content: content,
        moneyText: mMoney.workMoney,
        type: "1",
        key: "work_money",
        isFirst: tempEntity.isEmpty,
        identity: 2,
        businessType: BusinessType.workAndAccountTypeWages.code.toString(),
      ));
    }

    /// 其他费用
    var mOtherExpenses = bizModel.otherExpenses;
    for (final (index, mItem) in mOtherExpenses.indexed) {
      var title = "其他费用 ${mItem.name}";
      var subTitle = "${mItem.count}笔";
      tempEntity.add(BusinessCountUIState(
        title: title,
        subTitle: subTitle,
        moneyText: mItem.money,
        type: "1",
        key: "other_expense_$index",
        isFirst: tempEntity.isEmpty,
        identity: 2,
        businessType: BusinessType.workAndAccountTypeExpense.code.toString(),
        workType: "other_expense",
      ));
    }

    /// 借支
    var mBorrow = bizModel.borrow;
    if (mBorrow != null && mBorrow.num > 0) {
      tempEntity.add(BusinessCountUIState(
        title: "借支",
        content: "${mBorrow.num}笔",
        moneyText: mBorrow.borrowCount.toStringAsFixed(2),
        type: "2",
        key: "borrow",
        isFirst: tempEntity.isEmpty,
        identity: 2,
        businessType: BusinessType.workAndAccountTypeDebt.code.toString(),
      ));
    }

    /// 结算
    var mWage = bizModel.wage;
    if (mWage != null && mWage.num > 0) {
      tempEntity.add(BusinessCountUIState(
        title: "结算",
        content: "${mWage.num}笔",
        moneyText: mWage.wageCount.toStringAsFixed(2),
        type: "2",
        key: "wage",
        isFirst: tempEntity.isEmpty,
        identity: 2,
        businessType: BusinessType.workAndAccountTypeWageLast.code.toString(),
      ));
    }

    /// 未结算
    var mUnsettled = bizModel.unsettled;
    if (mUnsettled > 0) {
      tempEntity.add(BusinessCountUIState(
        title: "未结",
        moneyText: mUnsettled.toStringAsFixed(2),
        type: "2",
        key: "unsettled",
        isFirst: tempEntity.isEmpty,
        identity: 2,
      ));
    }
    uiHeaderState.value = WorkerFlowHeaderUIState(list: tempEntity);
  }
}

extension AhaExtension on String? {
  double get tDouble {
    var result = double.parse(this ?? "0");
    return result;
  }
}
