import "package:get/get.dart";
import "../ui_rep/worker_flow_ui_rep.dart";
import "protocol/worker_flow_ui_state.dart";

/// @date 2025/06/12
/// @description WorkerFlow页ViewModel
/// 1. 处理数据的加载状态，如loading和错误视图等
/// 2. 观测UIRep中的业务实体转为UIState
/// 3. 接受来自view的意图并分发事件进行处理（暂无）
/// 4. 对View层暴露事件（操作视图和处理一些与系统权限交互等场景）
class WorkerFlowViewModel {
  var isLoading = false.obs;
  var isShowError = false.obs;
  var uiState = WorkerFlowUIState().obs;
  var uiRep = WorkerFlowUIRep();

  WorkerFlowViewModel() {
    fetchData();
    ever(uiRep.entity, (value) {
      convertEntityToUIState(value);
    });
  }

  /// 初始化数据同步，如果不需要loading，则在这里去掉
  void fetchData({bool isShowLoading = true}) async {
    if (isShowLoading) {
      isLoading.value = true;
    }
    try {
      // 同步获取数据
      // var result = uiRep.getStatus();
      // await uiRep.fetchHeaderData();
      await uiRep.fetchListData();
      // 这里的result仅做弹窗之类的处理，不在此处转为UI实体
    } catch (e) {
      uiState.value.data = [];
      uiState.value.isShowError = true;
    } finally {
      isLoading.value = false;
    }
  }

  /// 将实体数据转换为UIState
  /// @param entity
  /// @returns
  void convertEntityToUIState(WorkerFlowUIRepEntity entity) {
    if (entity.data == null) return;
    List<BusinessUIStateEntity> tempList = [];
    for (var i = 0; i < entity.data!.length; i++) {
      var item = entity.data![i];
      tempList.add(BusinessUIStateEntity(WorkerFlowType.date, date: item.date));
      var dayList = item.list;
      if (dayList.isEmpty) {
        // 添加无数据Item
        tempList.add(BusinessUIStateEntity(WorkerFlowType.recordable, date: item.date));
      } else {
        for (var j = 0; j < dayList.length; j++) {
          final mItem = dayList[j];
          var subWorkList = <SubWorkEntity>[];
          var mMoney = mItem.money.isEmpty ? "0" : mItem.money;

          /// 点工、包工
          if (RwaRecordType.workAndAccountTypeDays.code == mItem.businessType ||
              RwaRecordType.workAndAccountTypePackage.code == mItem.businessType) {
            var content = "";
            if (mItem.workTime > 0) {
              String wTimes = mItem.workTime.toString();
              if (wTimes.endsWith(".0")) {
                wTimes = wTimes.substring(0, wTimes.length - 2);
              }
              content = "上班：$wTimes个工";
            }
            if (mItem.workTimeHour > 0 && content.isEmpty) {
              content = "上班：${mItem.workTimeHour}小时";
            }

            if (mItem.morningWorkTime.isNotEmpty ||
                mItem.morningWorkTimeHour.isNotEmpty ||
                mItem.afternoonWorkTime.isNotEmpty ||
                mItem.afternoonWorkTimeHour.isNotEmpty) {
              var morningWork = '';
              if (mItem.morningWorkTime.isNotEmpty) {
                morningWork = "上午: ${mItem.morningWorkTime.toString()}个工";
              } else {
                morningWork = "上午: 休息";
              }

              var afternoonWork = '';
              if (mItem.afternoonWorkTime.isNotEmpty) {
                afternoonWork = "下午: ${mItem.afternoonWorkTime.toString()}个工";
              } else {
                afternoonWork = '下午: 休息';
              }
              content = (morningWork.isNotEmpty && afternoonWork.isNotEmpty)
                  ? "$morningWork\n$afternoonWork"
                  : "$morningWork$afternoonWork";
            }
            if (content.isEmpty) {
              content = "上班：休息";
            }
            subWorkList.add(SubWorkEntity(title: "上班", content: content));
          }

          /// 加班
          if (mItem.overtime > 0) {
            subWorkList.add(SubWorkEntity(title: "加班", content: "${mItem.overtime}个小时"));
          }
          if (mItem.overtimeWork > 0) {
            subWorkList.add(SubWorkEntity(title: "加班", content: "${mItem.overtime}个工"));
          }

          /// 工量
          if (mItem.businessType == RwaRecordType.workAndAccountTypeLoad.code) {
            if (mItem.unitWorkTypeName.isNotEmpty) {
              subWorkList.add(SubWorkEntity(title: "分项", content: mItem.unitWorkTypeName));
            }
            if (mItem.unitPrice.isNotEmpty) {
              var content = "";
              if (mItem.unitWorkTypeUnit.isNotEmpty) {
                content = "${mItem.unitPrice}元/${mItem.unitWorkTypeUnit}";
              } else {
                content = mItem.unitPrice;
              }
              subWorkList.add(SubWorkEntity(title: "单价", content: content));
            }
            if (mItem.unitNum.isNotEmpty) {
              subWorkList.add(
                SubWorkEntity(title: "工程量", content: mItem.unitNum + mItem.unitWorkTypeUnit),
              );
            }
          }

          /// 其他费用
          if (mItem.businessType == RwaRecordType.workAndAccountTypeExpense.code) {
            subWorkList.add(
              SubWorkEntity(title: "费用名称", content: mItem.otherExpenses?.name ?? "无"),
            );
          }
          var resList = <ResourceEntity>[];

          /// 图片
          if (mItem.imgInfo.isNotEmpty) {
            for (final (index, img) in mItem.imgInfo.indexed) {
              resList.add(ResourceEntity(
                url: img.url,
                isVideo: img.type == "video",
                cover: img.cover,
                index: index,
              ));
            }
            subWorkList.add(SubWorkEntity(title: "工作证据", content: "${resList.length}张"));
          }

          /// 钱
          var doubleMoney = double.parse(mMoney);
          mMoney = (doubleMoney + mItem.feeMoney).toStringAsFixed(2);

          var isHighLight = mItem.businessType == RwaRecordType.workAndAccountTypeDebt.code ||
              mItem.businessType == RwaRecordType.workAndAccountTypeWageLast.code;
          tempList.add(BusinessUIStateEntity(
            WorkerFlowType.info,
            subWorkList: subWorkList,
            tag: RwaRecordType.values[mItem.businessType.toInt() - 1].desc,
            projectName: mItem.workNoteName,
            totalMoney: mMoney,
            resList: resList,
            isHighLight: isHighLight,
            remake: mItem.note,
            isFirstInfoItem: tempList.last.type == WorkerFlowType.date,
          ));
        }
      }
    }
    uiState.value = WorkerFlowUIState(data: tempList);
  }
}
