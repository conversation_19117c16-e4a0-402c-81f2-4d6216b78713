/// @date 2025/06/12
/// @description JDetail页UI状态
class WorkerFlowHeaderUIState {
  List<BusinessCountUIState> list;

  WorkerFlowHeaderUIState({
    this.list = const [],
  });
}

class BusinessCountUIState {
  String title;
  String subTitle;
  String content;
  String moneyText;
  String subMoneyText;
  String type;
  String key;
  bool isFirst;
  int identity;
  String businessType;
  String workNote;
  String workType;

  BusinessCountUIState({
    this.title = "",
    this.subTitle = "",
    this.content = "",
    this.moneyText = "",
    this.subMoneyText = "",
    this.type = "",
    this.key = "",
    this.isFirst = false,
    this.identity = 0,
    this.businessType = "",
    this.workNote = "",
    this.workType = "",
  });
}
