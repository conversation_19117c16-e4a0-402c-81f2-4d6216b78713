/// @date 2025/06/12
/// @description JDetail页UI状态
class WorkerFlowUIState {
  bool isShowError = false;
  List<BusinessUIEntity> data;

  WorkerFlowUIState({
    this.isShowError = false,
    this.data = const [],
  });
}

class BusinessUIEntity {
  WorkerFlowType type;
  int id;
  String date;
  String tag;
  String projectName;
  String totalMoney;
  String remake;
  bool isHighLight;
  List<SubWorkEntity> subWorkList;
  List<ResourceEntity> resList;
  // 是否第一个流水信息View
  bool isFirstInfoItem;
  bool isSelected;

  BusinessUIEntity(
    this.type, {
    this.id = 0,
    this.date = "",
    this.tag = "",
    this.projectName = "",
    this.totalMoney = "",
    this.remake = "",
    this.isHighLight = false,
    this.subWorkList = const [],
    this.resList = const [],
    this.isFirstInfoItem = false,
    this.isSelected = false,
  });
}

/// 记工类型的子项
class SubWorkEntity {
  String title;
  String content;

  SubWorkEntity({
    this.title = "",
    this.content = "",
  });
}

/// 资源，图片或视频
class ResourceEntity {
  String url;
  String cover;
  bool isVideo;
  int index;

  ResourceEntity({
    this.url = "",
    this.cover = "",
    this.isVideo = false,
    this.index = 0,
  });
}

enum WorkerFlowType {
  /// 日期
  date,

  /// 详情
  info,

  ///可记工
  recordable,

  /// 广告
  ad,

  mDefault,
}

enum BusinessType {
  /// 1工天（点工）
  workAndAccountTypeDays(1, "点工"),

  /// 2记工量
  workAndAccountTypeLoad(2, "工量"),

  /// 3日结（短工）
  workAndAccountTypeWages(3, "短工"),

  /// 4借支
  workAndAccountTypeDebt(4, "借支"),

  /// 5支出
  workAndAccountTypeExpenditure(5, "支出"),

  /// 6（包工）
  workAndAccountTypePackage(6, "包工"),

  /// 7（小时工）
  workAndAccountTypeHours(7, "小时工"),

  /// 8 收入
  workAndAccountTypeIncomeLast(8, "收入"),

  /// 9 工资 （结算和未结是这个类型）
  workAndAccountTypeWageLast(9, "结算"),

  /// 10 其他费用
  workAndAccountTypeExpense(10, "其他费用"),

  /// RN里的代码还有个101，未结
  workAndAccountTypeUnFinish(101, "未结");

  final int code;
  final String desc;
  const BusinessType(this.code, this.desc);
}
