import 'package:flutter/material.dart';
import 'package:gdjg_pure_flutter/feature/worker/worker_flow/vm/protocol/worker_flow_ui_state.dart';
import 'package:gdjg_pure_flutter/feature/worker/worker_flow/widget/flow_date_view.dart';
import 'package:gdjg_pure_flutter/feature/worker/worker_flow/widget/flow_info_view.dart';
import 'package:gdjg_pure_flutter/feature/worker/worker_flow/widget/flow_recordable_view.dart';
import 'package:gdjg_pure_flutter/feature/worker/worker_flow/widget/worker_flow_header_view.dart';
import 'package:gdjg_pure_flutter/utils/route_util/base_page/base_ful_page.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

import 'entity/worker_flow_props.dart';
import 'vm/worker_flow_viewmodel.dart';

/// @date 2025/06/12
/// @param props 页面路由参数
/// @returns
/// @description WorkerFlow页面入口
class WorkerFlowPage extends BaseFulPage {
  const WorkerFlowPage({super.key, this.props}) : super(appBar: null);

  final WorkerFlowProps? props;

  @override
  State<StatefulWidget> createState() => _WorkerFlowPageState();
}

class _WorkerFlowPageState extends BaseFulPageState<WorkerFlowPage> {
  final WorkerFlowViewModel viewModel = WorkerFlowViewModel();
  final RefreshController _refreshController = RefreshController(initialRefresh: false);

  @override
  Widget yBuild(BuildContext context) {
    return Container(
      color: Color(0xFFF5F6FA),
      child: contentView(context),
    );
  }

  /// 实际展示的视图，需要处理：
  /// 1. 画出UI
  /// 2. 绑定UIState
  /// 3. 处理事件监听
  /// 4. 向VM传递来自View层的事件
  Widget contentView(BuildContext context) {
    // 进行事件处理
    // handleWorkerFlowVMEvent(props.vm)
    return Stack(
      children: [
        Column(
          children: [
            buildMonthView(),
            Expanded(
              child: SmartRefresher(
                enablePullUp: false,
                enablePullDown: true,
                controller: _refreshController,
                onRefresh: _onRefresh,
                child: CustomScrollView(
                  slivers: [
                    SliverToBoxAdapter(
                      child: Obx(() {
                        return WorkerFlowHeaderView(viewModel, viewModel.uiHeaderState.value.list);
                      }),
                    ),
                    Obx(() {
                      return SliverList.builder(
                        itemBuilder: _buildItemView,
                        itemCount: viewModel.uiFlowState.value.data.length,
                      );
                    }),
                    SliverToBoxAdapter(child: Container(height: 100)),
                  ],
                ),
              ),
            ),
          ],
        ),
        Positioned(
          bottom: 16,
          child: SizedBox(
            width: MediaQuery.of(context).size.width,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                buildStackButton(1),
                SizedBox(width: 16),
                buildStackButton(2),
              ],
            ),
          ),
        )
      ],
    );
  }

  /// 构建悬浮按钮
  Widget buildStackButton(int type) {
    var buttonText = "";
    var mWidth = 0.0;
    Color? mColor;
    Function()? mClick;
    if (type == 1) {
      buttonText = "记借支/结算";
      mColor = Color(0xFFCC7F1B);
      mWidth = 116;
      mClick = () {};
    } else if (type == 2) {
      buttonText = "记工";
      mColor = Color(0xFF1983FF);
      mWidth = 85;
      mClick = () {};
    }
    if (mColor == null || mWidth == 0 || buttonText.isEmpty || mClick == null) return SizedBox();
    return InkWell(
      onTap: mClick,
      child: Container(
        width: mWidth,
        height: 40,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(20),
          border: Border.all(color: mColor, width: 1),
        ),
        alignment: Alignment.center,
        child: Text(
          buttonText,
          style: TextStyle(color: mColor, fontSize: 16),
        ),
      ),
    );
  }

  void _onRefresh() async {
    viewModel.fetchData(isShowLoading: false);
    _refreshController.refreshCompleted();
  }

  /// 构建子项视图
  Widget _buildItemView(BuildContext context, int index) {
    final businessData = viewModel.uiFlowState.value.data[index];
    var dayDate = businessData.date;
    var dayType = businessData.type;
    Widget itemView = Container();
    switch (dayType) {
      case WorkerFlowType.date:
        itemView = FlowDateView(dayDate);
        break;
      case WorkerFlowType.info:
        itemView = FlowInfoView(businessData);
        break;
      case WorkerFlowType.recordable:
        itemView = FlowRecordableView(dayDate);
        break;
      case WorkerFlowType.ad:
        itemView = buildAdWidget();
        break;
      default:
    }
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: itemView,
    );
  }

  Widget buildAdWidget() {
    return Container(
      height: 100,
      color: Colors.grey.withAlpha(10),
      child: Text("广告"),
    );
  }

  Widget buildMonthView() {
    return Container(
      height: 60,
      color: Colors.grey.withAlpha(10),
      child: Stack(
        alignment: Alignment.bottomCenter,
        children: [
          Container(
            height: double.infinity,
            color: Colors.grey.withAlpha(10),
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text("上一月"),
                Text("下一月"),
              ],
            ),
          ),
          Container(
            width: 150,
            height: 54,
            alignment: Alignment.center,
            color: Colors.white,
            child: Text("2025年6月"),
          )
        ],
      ),
    );
  }
}
