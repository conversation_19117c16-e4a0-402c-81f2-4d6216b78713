import 'package:flutter/material.dart';
import 'package:gdjg_pure_flutter/feature/worker/worker_flow/vm/protocol/worker_flow_ui_state.dart';
import 'package:gdjg_pure_flutter/init_module/init_route.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/utils/system_util/font_util.dart';

class FlowInfoView extends StatelessWidget {
  final BusinessUIEntity data;
  const FlowInfoView(this.data, {super.key});

  @override
  Widget build(BuildContext context) {
    var dataList = [];
    for (var e in data.subWorkList) {
      dataList.add(_buildItemView(e.title, e.content));
    }
    var mScreenWidth = MediaQuery.of(context).size.width;
    if (data.remake.isNotEmpty || data.resList.isNotEmpty) {
      dataList.add(_buildLineWidget(mScreenWidth));
      if (data.remake.isNotEmpty) {
        dataList.add(_buildRemakeWidget(data.remake));
      }
      if (data.resList.isNotEmpty) {
        dataList.add(_buildMediaWidget(data.resList));
      }
    }

    return Container(
      margin: EdgeInsets.only(top: data.isFirstInfoItem ? 0 : 8),
      padding: EdgeInsets.symmetric(vertical: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        spacing: 8,
        children: [
          _buildTitleView(data.isHighLight),
          ...dataList,
        ],
      ),
    );
  }

  /// 构建标题视图
  Widget _buildTitleView(bool isHighLight) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 5, vertical: 2),
            decoration: BoxDecoration(
              color: isHighLight ? Color(0xFFFFF6EA) : Color(0xFFF2F9FF),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Text(data.tag, style: TextStyle(fontSize: 12, color: Color(0xFF5290FD))),
          ),
          SizedBox(width: 5),
          Expanded(
            child: Text(
              data.projectName,
              maxLines: 1,
              style: TextStyle(fontSize: 17, fontWeight: FontWeight.w800, color: Color(0xFF323233)),
            ),
          ),
          Container(
            constraints: BoxConstraints(maxWidth: 100),
            margin: EdgeInsets.only(left: 5),
            child: Text(
              maxLines: 1,
              data.totalMoney,
              style: TextStyle(
                fontFamily: FontUtil.fontCondMedium,
                color: isHighLight ? Color(0xFFFFA011) : Color(0xFF1983FF),
                fontSize: 20,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建子项视图
  Widget _buildItemView(String title, String content) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8),
      child: Row(
        children: [
          Text(
            title,
            style: TextStyle(fontSize: 15, color: Color(0xFF8C8C8C)),
            maxLines: 1,
          ),
          Expanded(
            child: Container(
              alignment: Alignment.centerRight,
              margin: EdgeInsets.only(left: 4),
              child: Text(
                content,
                style: TextStyle(
                  fontSize: 15,
                  color: Color(0xFF323233),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 横线
  Widget _buildLineWidget(double mScreenWidth) {
    return Container(
      width: mScreenWidth,
      height: 1,
      color: Color(0xFFF5F5F5),
    );
  }

  /// 备注
  Widget _buildRemakeWidget(String remake) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8),
      alignment: Alignment.centerLeft,
      child: Text(
        "备注：$remake",
        style: TextStyle(fontSize: 13, color: Color(0xFF8A8A99)),
      ),
    );
  }

  /// 媒体资源
  Widget _buildMediaWidget(List<ResourceEntity> resList) {
    var mSpacing = 8.0;
    var parentWidth = 0.0;
    return LayoutBuilder(builder: (BuildContext context, BoxConstraints constraints) {
      parentWidth = constraints.maxWidth - mSpacing * 5;
      var mediaList = resList
          .take(4)
          .map((res) => _buildMediaItem(res, parentWidth / 4, resList.length - 3))
          .toList();
      return Container(
        padding: EdgeInsets.symmetric(horizontal: mSpacing),
        height: 72,
        child: Row(spacing: mSpacing, children: mediaList),
      );
    });
  }

  Widget _buildMediaItem(ResourceEntity res, double width, int moreCount) {
    var mBorderRadius = 4.0;
    return Expanded(
      child: InkWell(
        onTap: () {
          YPRoute.openPage(
            RouteNameCollection.mediaViewer,
            params: {"mediaList": data.resList, "index": res.index},
          );
        },
        child: Stack(
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(mBorderRadius),
              clipBehavior: Clip.antiAlias,
              child: Image.network(
                res.cover.isNotEmpty ? res.cover : res.url,
                scale: 1,
                width: width,
                height: 72,
                fit: BoxFit.cover,
              ),
            ),
            if (res.isVideo)
              Align(
                alignment: Alignment.center,
                child: Image.asset("assets/images/common/net_loading.png", width: 20, height: 20),
              ),
            if (res.index == 3)
              Container(
                alignment: Alignment.center,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.all(Radius.circular(mBorderRadius)),
                  color: Colors.black.withValues(alpha: 0.5),
                ),
                child: Text(
                  "+$moreCount",
                  style: TextStyle(color: Colors.white, fontSize: 16, fontWeight: FontWeight.w800),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
