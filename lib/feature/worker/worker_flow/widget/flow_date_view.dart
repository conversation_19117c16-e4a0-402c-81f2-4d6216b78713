import 'package:flutter/material.dart';

class FlowDateView extends StatelessWidget {
  final String date;
  const FlowDateView(this.date, {super.key});

  @override
  Widget build(BuildContext context) {
    var mDay = calculateDate(date);
    var mWeek = calculateWeek(date);
    return Container(
      height: 48,
      alignment: Alignment.centerLeft,
      color: Colors.transparent,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          Text(
            mDay,
            style: TextStyle(
              color: Color(0xFF323233),
              fontSize: 16,
              fontWeight: FontWeight.w800,
            ),
          ),
          SizedBox(width: 2),
          Text(mWeek, style: TextStyle(color: Color(0xFF8A8A99), fontSize: 14)),
        ],
      ),
    );
  }

  String calculateDate(String date) {
    var content = date;
    var dateList = date.split("-");
    if (dateList.isNotEmpty) {
      var mDay = dateList[dateList.length - 1];
      if (mDay.startsWith("0")) {
        mDay = mDay.substring(1);
      }
      content = "$mDay日";
    }
    var currentDate = DateTime.now();
    if (dateList.isNotEmpty &&
        currentDate.year == int.parse(dateList[0]) &&
        currentDate.month == int.parse(dateList[1]) &&
        currentDate.day == int.parse(dateList[2])) {
      content += "(今天)";
    }
    return content;
  }

  String calculateWeek(String date) {
    var content = date;
    var dateList = date.split("-");
    var mWeekDay = 0;
    var year = int.parse(dateList[0]);
    var month = int.parse(dateList[1]);
    var day = int.parse(dateList[2]);
    mWeekDay = DateTime(year, month, day).weekday;
    switch (mWeekDay) {
      case 1:
        content = "周一";
        break;
      case 2:
        content = "周二";
        break;
      case 3:
        content = "周三";
        break;
      case 4:
        content = "周四";
        break;
      case 5:
        content = "周五";
        break;
      case 6:
        content = "周六";
        break;
      case 7:
        content = "周日";
        break;
    }
    return content;
  }
}
