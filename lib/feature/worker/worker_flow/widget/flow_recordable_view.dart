import 'package:flutter/material.dart';

class FlowRecordableView extends StatelessWidget {
  final String date;
  const FlowRecordableView(this.date, {super.key});

  @override
  Widget build(BuildContext context) {
    var isToday = calculateDate(date);
    return Container(
      height: 48,
      padding: EdgeInsets.symmetric(horizontal: 12),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(4),
        color: Colors.white,
      ),
      child: Row(
        children: [
          Expanded(
            child: Text(
              "当日暂无记工数据",
              style: TextStyle(fontSize: 16, color: Color(0xFF323233)),
            ),
          ),
          InkWell(
            onTap: () {
              print("补记");
            },
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(2),
                color: Color(0x1a1983ff),
              ),
              padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              child: Row(
                children: [
                  Image.asset(
                    "assets/images/common/icon_flow_recordable.png",
                    width: 12,
                    height: 12,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    isToday ? "记工" : "补记",
                    style: TextStyle(
                      fontSize: 14,
                      color: Color(0xFF1983FF),
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  bool calculateDate(String date) {
    var isToday = false;
    var dateList = date.split("-");
    if (dateList.isNotEmpty) {
      var mYear = int.parse(dateList[0]);
      var mMonth = int.parse(dateList[1]);
      var mDay = int.parse(dateList[2]);
      var currentDate = DateTime.now();
      isToday = mYear == currentDate.year && mMonth == currentDate.month && mDay == currentDate.day;
    }
    return isToday;
  }
}
