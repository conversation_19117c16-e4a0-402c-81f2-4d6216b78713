import 'package:flutter/material.dart';
import 'package:gdjg_pure_flutter/feature/worker/worker_flow/vm/protocol/worker_flow_header_us.dart';
import 'package:gdjg_pure_flutter/feature/worker/worker_flow/vm/worker_flow_vm.dart';
import 'package:gdjg_pure_flutter/iconfont/iconfont.dart';
import 'package:gdjg_pure_flutter/utils/system_util/font_util.dart';
import 'package:get/get.dart';

class WorkerFlowHeaderView extends StatelessWidget {
  final WorkerFlowViewModel viewModel;
  final List<BusinessCountUIState> list;
  const WorkerFlowHeaderView(this.viewModel, this.list, {super.key});

  @override
  Widget build(BuildContext context) {
    var widgetList = list.map((e) {
      return _buildItemWidget(e);
    });
    if (widgetList.isEmpty) return SizedBox();
    return Container(
      color: Colors.white,
      padding: EdgeInsets.all(8),
      child: Column(
        spacing: 4,
        children: [...widgetList],
      ),
    );
  }

  Widget _buildItemWidget(BusinessCountUIState data) {
    var bgColor = Colors.white;
    var moneyTextColor = Color(0xFF1983FF);
    switch (data.type) {
      case "1":
        bgColor = Color(0xFFF2F9FF);
        moneyTextColor = Color(0xFF1983FF);
        break;
      case "2":
        bgColor = Color(0xFFFFF6EA);
        moneyTextColor = Color(0xFFFFA011);
        break;
    }
    return Container(
      decoration: BoxDecoration(color: bgColor, borderRadius: BorderRadius.circular(4)),
      padding: EdgeInsets.symmetric(horizontal: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            flex: (data.workType == "unit" || data.workType == "other_expense") ? 1 : 0,
            child: Container(
              padding: EdgeInsets.symmetric(vertical: 8),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    data.title,
                    style: TextStyle(
                      overflow: TextOverflow.ellipsis,
                      fontSize: 16,
                      color: Color(0xFF323232),
                      fontWeight: FontWeight.w800,
                    ),
                  ),
                  if (data.subTitle.isNotEmpty)
                    Text(
                      data.subTitle,
                      style: TextStyle(
                        overflow: TextOverflow.ellipsis,
                        fontSize: 11,
                        color: Color(0xFF323232),
                      ),
                    ),
                ],
              ),
            ),
          ),
          if (data.content.isNotEmpty)
            Expanded(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Center(
                    child: Text(
                      data.content,
                      style: TextStyle(fontSize: 15, color: Color(0xFF323232)),
                    ),
                  ),
                ],
              ),
            ),
          Row(
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Obx(() {
                    return Text(
                      viewModel.isHiddenUIState.value ? "****" : data.moneyText,
                      style: TextStyle(
                        color: moneyTextColor,
                        fontSize: 22,
                        fontFamily: FontUtil.fontCondMedium,
                      ),
                    );
                  }),
                  if (data.subMoneyText.isNotEmpty && !data.isFirst)
                    Text(
                      data.subMoneyText,
                      style: TextStyle(fontSize: 12, color: Color(0xFF323232)),
                    ),
                ],
              ),
              if (data.isFirst && data.type != "0")
                InkWell(
                  onTap: _onHiddenClick,
                  child: Container(
                    margin: EdgeInsets.only(left: 2),
                    child: Obx(() {
                      var icon = viewModel.isHiddenUIState.value
                          ? IconNames.saasEyeClose
                          : IconNames.saasEyeOpen;
                      return IconFont(icon, size: 18, color: "#1983FF");
                    }),
                  ),
                ),
              if (!data.isFirst) IconFont(IconNames.saasArrowRight, size: 17, color: "#8A8A99"),
            ],
          ),
        ],
      ),
    );
  }

  _onHiddenClick() {
    viewModel.isHiddenUIState.value = !viewModel.isHiddenUIState.value;
  }
}
