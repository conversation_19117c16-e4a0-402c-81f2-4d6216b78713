import 'package:gdjg_pure_flutter/data/worker_flow/repo/worker_flow_repo.dart';
import 'package:get/get.dart';

/// @date 2025/07/05
/// @description BatchDelete页UI仓库
/// 1.为vm提供业务数据
/// 2.解耦视图层与业务仓库之间的耦合
class BatchDeleteUIRep {
  /// 实体数据
  var entity = BatchDeleteUIRepEntity().obs;

  /// 初始化数据同步，返回同步是否成功的状态给UI层
  /// 如果成功，则将业务数据存储在entity中
  Future<bool> fetchDeleteRecord(Map<String, Object> params) async {
    var result = await WorkerFlowRepo().fetchDeleteRecord(params);
    return result.isOK();
  }
}

class BatchDeleteUIRepEntity {
  String? data;
  BatchDeleteUIRepEntity({this.data});

  @override
  String toString() {
    return 'BatchDeleteUIRepEntity{data: $data}';
  }
}
