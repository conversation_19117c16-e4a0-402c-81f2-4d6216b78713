import 'package:flutter/material.dart';
import 'package:gdjg_pure_flutter/feature/worker/worker_flow/vm/protocol/worker_flow_us.dart';
import 'package:gdjg_pure_flutter/feature/worker/worker_flow/widget/flow_date_view.dart';
import 'package:gdjg_pure_flutter/feature/worker/worker_flow/widget/flow_info_view.dart';
import 'package:gdjg_pure_flutter/iconfont/iconfont.dart';
import 'package:gdjg_pure_flutter/init_module/init_route.dart';
import 'package:gdjg_pure_flutter/utils/route_util/base_page/base_ful_page.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/widget/month_switcher_view.dart';
import 'package:get/get.dart';
import 'package:gdjg_pure_flutter/widget/page_error_view.dart';
import 'package:gdjg_pure_flutter/widget/page_loading_view.dart';
import 'entity/batch_delete_props.dart';
import 'vm/batch_delete_vm.dart';

/// @date 2025/07/05
/// @param props 页面路由参数
/// @returns
/// @description BatchDelete页面入口
class BatchDeletePage extends BaseFulPage {
  BatchDeletePage({super.key}) : super(appBar: YPAppBar(title: "批量删除"));

  @override
  State<BatchDeletePage> createState() => _BatchDeletePageState();
}

class _BatchDeletePageState extends BaseFulPageState<BatchDeletePage> {
  final BatchDeleteViewModel viewModel = BatchDeleteViewModel();

  @override
  Widget yBuild(BuildContext context) {
    return contentView();
  }

  Widget contentView() {
    return Container(
      color: Colors.white,
      child: Column(
        children: [
          MonthSwitcherView(
            initialTime: DateTime.now(),
            marginTop: 6,
            onYearMonthSelected: (DateTime currentTime) {
              print("currentTime:$currentTime");
              viewModel.currentDateTime = currentTime;
              viewModel.fetchData();
            },
          ),
          Container(
            height: 48,
            padding: EdgeInsets.symmetric(horizontal: 12),
            color: Colors.white,
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    "项目",
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.w700,
                      color: Color(0xFF000000),
                    ),
                  ),
                ),
                InkWell(
                  onTap: () async {
                    await YPRoute.openPage(RouteNameCollection.chooseProject);
                  },
                  child: Obx(() {
                    return Text(viewModel.projectName.value ?? "请选择要删除数据的项目");
                  }),
                ),
                IconFont(IconNames.saasArrowRight, size: 18),
              ],
            ),
          ),
          Expanded(
            child: Obx(() {
              return viewModel.flowUS.value.data.isNotEmpty
                  ? Container(
                      color: Color(0xFFF5F5F5),
                      child: ListView.builder(
                        itemBuilder: (context, index) {
                          return _buildItemView(context, index);
                        },
                        itemCount: viewModel.flowUS.value.data.length,
                      ),
                    )
                  : _buildEmptyView();
            }),
          ),
          SafeArea(
            bottom: true,
            child: Container(
              padding: EdgeInsets.fromLTRB(8, 16, 16, 16),
              decoration: BoxDecoration(
                color: Colors.white,
                border: Border(top: BorderSide(color: Color(0xFFF5F5F5), width: 1)),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  InkWell(
                    onTap: viewModel.setSelectedAll,
                    child: Container(
                      padding: EdgeInsets.all(8),
                      child: Row(
                        children: [
                          Obx(() => IconFont(
                              viewModel.isSelectedAll.value
                                  ? IconNames.saasCheck
                                  : IconNames.saasRadio,
                              size: 20,
                              color: "#1983FF")),
                          Container(
                            margin: EdgeInsets.only(left: 8),
                            child: Text("全选",
                                style: TextStyle(fontSize: 16, color: Color(0xFF323232))),
                          ),
                        ],
                      ),
                    ),
                  ),
                  InkWell(
                    onTap: viewModel.fetchDeleteRecord,
                    child: Obx(
                      () => Container(
                        alignment: Alignment.center,
                        decoration: BoxDecoration(
                          color: viewModel.selectedIdList.isNotEmpty
                              ? Color(0xFF1983FF)
                              : Color(0x801983FF),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        constraints: BoxConstraints(minWidth: 200),
                        height: 44,
                        child: Text("批量删除", style: TextStyle(fontSize: 16, color: Colors.white)),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建子项视图
  Widget _buildItemView(BuildContext context, int index) {
    final businessData = viewModel.flowUS.value.data[index];
    var dayDate = businessData.date;
    var dayType = businessData.type;
    Widget itemView = Container();
    switch (dayType) {
      case WorkerFlowType.date:
        itemView = FlowDateView(dayDate);
        break;
      case WorkerFlowType.info:
        itemView = InkWell(
          onTap: () {
            viewModel.setChecked(businessData.id);
          },
          child: Row(
            children: [
              IconFont(
                businessData.isSelected ? IconNames.saasCheck : IconNames.saasRadio,
                size: 20,
                color: "#1983FF",
              ),
              SizedBox(width: 12),
              Expanded(child: FlowInfoView(businessData)),
            ],
          ),
        );
        break;
      default:
    }
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: itemView,
    );
  }

  @override
  void onReceiveFromNext(Object? backData) {
    if (backData is Map) {
      var params = backData;
      print("====params:$params");
      viewModel.projectName.value = params["name"];
      viewModel.projectId = params["id"];
      viewModel.fetchData();
    }
  }

  Widget _buildEmptyView() {
    return Column(
      children: [
        SizedBox(height: 80),
        Image.network("https://cdn.yupaowang.com/jgjz/empty-note.png", width: 120, height: 120),
        Text("暂无数据")
      ],
    );
  }
}
