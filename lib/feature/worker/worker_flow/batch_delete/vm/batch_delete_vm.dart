import "package:collection/collection.dart";
import "package:flutter/material.dart";
import "package:gdjg_pure_flutter/data/worker_flow/ds/model/param/business_get_index_business_list_param_model.dart";
import "package:gdjg_pure_flutter/data/worker_flow/repo/business_get_index_business_list_biz_model.dart";
import "package:gdjg_pure_flutter/feature/worker/worker_flow/batch_delete/ui_rep/batch_delete_ui_rep.dart";
import "package:gdjg_pure_flutter/feature/worker/worker_flow/ui_rep/worker_flow_ui_rep.dart";
import "package:gdjg_pure_flutter/feature/worker/worker_flow/vm/protocol/worker_flow_us.dart";
import "package:get/get.dart";
import "protocol/batch_delete_us.dart";

/// @date 2025/07/05
/// @description BatchDelete页ViewModel
/// 1. 处理数据的加载状态，如loading和错误视图等
/// 2. 观测UIRep中的业务实体转为UIState
/// 3. 接受来自view的意图并分发事件进行处理（暂无）
/// 4. 对View层暴露事件（操作视图和处理一些与系统权限交互等场景）
class BatchDeleteViewModel {
  var isLoading = false.obs;
  var isShowError = false.obs;
  var uiState = BatchDeleteUIState().obs;
  var flowUS = WorkerFlowUIState().obs;
  var uiRep = WorkerFlowUIRep();
  var batchDeleteUIRep = BatchDeleteUIRep();
  var currentDateTime = DateTime.now();
  var projectId = "";
  var selectedIdList = <int>[].obs;
  Rxn<String> projectName = Rxn<String>();
  var isSelectedAll = false.obs;

  BatchDeleteViewModel() {
    fetchData();
    ever(uiRep.flowEntity, (value) {
      convertFlowEntityToUIState(value);
    });
  }

  setChecked(int id) {
    if (selectedIdList.contains(id)) {
      selectedIdList.remove(id);
    } else {
      selectedIdList.add(id);
    }
    // 是否全选
    isSelectedAll.value = selectedIdList.length == flowUS.value.data.length;
    // 通过更改数据刷新UI
    var newList = flowUS.value.data.map((e) {
      e.isSelected = selectedIdList.contains(e.id);
      return e;
    }).toList();
    flowUS.value = WorkerFlowUIState(data: newList);
  }

  setSelectedAll() {
    isSelectedAll.value = !isSelectedAll.value;
    var idList = flowUS.value.data.map((e) => e.id).toList();
    if (isSelectedAll.value) {
      selectedIdList.addAll(idList);
    } else {
      selectedIdList.clear();
    }
    // 通过更改数据刷新UI
    var newList = flowUS.value.data.mapIndexed((index, e) {
      e.isSelected = isSelectedAll.value;
      return e;
    }).toList();
    flowUS.value = WorkerFlowUIState(data: newList);
  }

  /// 初始化数据同步，如果不需要loading，则在这里去掉
  void fetchData() async {
    var mDateTime = currentDateTime;
    final startTime = "${mDateTime.year}-${mDateTime.month}-01";
    var daysCount = DateUtils.getDaysInMonth(mDateTime.year, mDateTime.month);
    final endTime = "${mDateTime.year}-${mDateTime.month}-$daysCount";
    try {
      await uiRep.fetchListData(
        BusinessListParamModel(
          identity: "2",
          start_time: startTime,
          end_time: endTime,
          work_notes: projectId,
        ),
      );
    } catch (e) {
      flowUS.value.data = [];
    } finally {}
  }

  void fetchDeleteRecord() async {
    var result = await batchDeleteUIRep.fetchDeleteRecord({"ids": selectedIdList});
    if (result) {
      fetchData();
    }
  }

  /// 将实体数据转换为UIState
  /// @param entity
  /// @returns
  void convertFlowEntityToUIState(List<BusinessGetIndexBusinessListABizModel> data) {
    List<BusinessUIEntity> tempList = [];
    for (var i = 0; i < data.length; i++) {
      var item = data[i];
      var dayList = item.list;
      if (dayList.isNotEmpty) {
        tempList.add(BusinessUIEntity(WorkerFlowType.date, date: item.date));
        for (var j = 0; j < dayList.length; j++) {
          final mItem = dayList[j];
          var subWorkList = <SubWorkEntity>[];
          var mMoney = mItem.money.isEmpty ? "0" : mItem.money;

          /// 点工、包工
          if (BusinessType.workAndAccountTypeDays.code == mItem.businessType ||
              BusinessType.workAndAccountTypePackage.code == mItem.businessType) {
            var content = "";
            if (mItem.workTime > 0) {
              String wTimes = mItem.workTime.toString();
              if (wTimes.endsWith(".0")) {
                wTimes = wTimes.substring(0, wTimes.length - 2);
              }
              content = "上班：$wTimes个工";
            }
            if (mItem.workTimeHour > 0 && content.isEmpty) {
              content = "上班：${mItem.workTimeHour}小时";
            }

            if (mItem.morningWorkTime.isNotEmpty ||
                mItem.morningWorkTimeHour.isNotEmpty ||
                mItem.afternoonWorkTime.isNotEmpty ||
                mItem.afternoonWorkTimeHour.isNotEmpty) {
              var morningWork = '';
              if (mItem.morningWorkTime.isNotEmpty) {
                morningWork = "上午: ${mItem.morningWorkTime.toString()}个工";
              } else {
                morningWork = "上午: 休息";
              }

              var afternoonWork = '';
              if (mItem.afternoonWorkTime.isNotEmpty) {
                afternoonWork = "下午: ${mItem.afternoonWorkTime.toString()}个工";
              } else {
                afternoonWork = '下午: 休息';
              }
              content = (morningWork.isNotEmpty && afternoonWork.isNotEmpty)
                  ? "$morningWork\n$afternoonWork"
                  : "$morningWork$afternoonWork";
            }
            if (content.isEmpty) {
              content = "上班：休息";
            }
            subWorkList.add(SubWorkEntity(title: "上班", content: content));
          }

          /// 加班
          if (mItem.overtime > 0) {
            subWorkList.add(SubWorkEntity(title: "加班", content: "${mItem.overtime}个小时"));
          }
          if (mItem.overtimeWork > 0) {
            subWorkList.add(SubWorkEntity(title: "加班", content: "${mItem.overtime}个工"));
          }

          /// 工量
          if (mItem.businessType == BusinessType.workAndAccountTypeLoad.code) {
            if (mItem.unitWorkTypeName.isNotEmpty) {
              subWorkList.add(SubWorkEntity(title: "分项", content: mItem.unitWorkTypeName));
            }
            if (mItem.unitPrice.isNotEmpty) {
              var content = "";
              if (mItem.unitWorkTypeUnit.isNotEmpty) {
                content = "${mItem.unitPrice}元/${mItem.unitWorkTypeUnit}";
              } else {
                content = mItem.unitPrice;
              }
              subWorkList.add(SubWorkEntity(title: "单价", content: content));
            }
            if (mItem.unitNum.isNotEmpty) {
              subWorkList.add(
                SubWorkEntity(title: "工程量", content: mItem.unitNum + mItem.unitWorkTypeUnit),
              );
            }
          }

          /// 其他费用
          if (mItem.businessType == BusinessType.workAndAccountTypeExpense.code) {
            subWorkList.add(
              SubWorkEntity(title: "费用名称", content: mItem.otherExpenses?.name ?? "无"),
            );
          }
          var resList = <ResourceEntity>[];

          /// 图片
          if (mItem.imgInfo.isNotEmpty) {
            for (final (index, img) in mItem.imgInfo.indexed) {
              resList.add(ResourceEntity(
                url: img.url,
                isVideo: img.type == "video",
                cover: img.cover,
                index: index,
              ));
            }
            subWorkList.add(SubWorkEntity(title: "工作证据", content: "${resList.length}张"));
          }

          /// 钱
          var doubleMoney = double.parse(mMoney);
          mMoney = (doubleMoney + mItem.feeMoney).toStringAsFixed(2);

          var isHighLight = mItem.businessType == BusinessType.workAndAccountTypeDebt.code ||
              mItem.businessType == BusinessType.workAndAccountTypeWageLast.code;
          print("=====mItem.id.toInt():${mItem.id.toInt()}");
          tempList.add(BusinessUIEntity(
            WorkerFlowType.info,
            id: mItem.id.toInt(),
            subWorkList: subWorkList,
            tag: BusinessType.values[mItem.businessType.toInt() - 1].desc,
            projectName: mItem.workNoteName,
            totalMoney: mMoney,
            resList: resList,
            isHighLight: isHighLight,
            remake: mItem.note,
            isFirstInfoItem: tempList.last.type == WorkerFlowType.date,
          ));
        }
      }
    }
    flowUS.value = WorkerFlowUIState(data: tempList);
  }
}
