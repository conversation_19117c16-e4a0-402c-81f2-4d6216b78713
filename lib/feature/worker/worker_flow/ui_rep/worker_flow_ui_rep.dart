import 'package:gdjg_pure_flutter/data/worker_flow/ds/model/param/business_get_index_business_count_param_model.dart';
import 'package:gdjg_pure_flutter/data/worker_flow/ds/model/param/business_get_index_business_list_param_model.dart';
import 'package:gdjg_pure_flutter/data/worker_flow/repo/business_get_index_business_list_biz_model.dart';
import 'package:gdjg_pure_flutter/data/worker_flow/repo/business_get_index_business_count_biz_model.dart';
import 'package:gdjg_pure_flutter/data/worker_flow/repo/worker_flow_repo.dart';
import 'package:get/get.dart' hide Response;

/// @date 2025/06/12
/// @description WorkerFlow页UI仓库
/// 1.为vm提供业务数据
/// 2.解耦视图层与业务仓库之间的耦合
class WorkerFlowUIRep {
  /// 实体数据
  var headerEntity = BusinessCountBizModel().obs;
  var flowEntity = <BusinessGetIndexBusinessListABizModel>[].obs;

  /// 初始化数据同步，返回同步是否成功的状态给UI层
  /// 如果成功，则将业务数据存储在entity中
  Future<List<BusinessGetIndexBusinessListABizModel>> fetchListData(
      BusinessListParamModel params) async {
    var result = await WorkerFlowRepo().fetchFlowList(params);
    if (result.isOK()) {
      final data = result.getSucData();
      if (data?.list.isNotEmpty == true) {
        flowEntity.value = data!.list;
      }
    }
    return flowEntity.value;
  }

  Future<BusinessCountBizModel> fetchHeaderData(BusinessCountParamModel parasm) async {
    var result = await WorkerFlowRepo().fetchHeaderData(parasm);
    if (result.isOK()) {
      final data = result.getSucData();
      if (data != null) {
        headerEntity.value = data;
      }
    }
    return headerEntity.value;
  }
}
