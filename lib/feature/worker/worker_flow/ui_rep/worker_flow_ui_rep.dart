import 'package:gdjg_pure_flutter/data/demo_setting_data/entity/worker_flow/business_get_index_business_list_biz_model.dart';
import 'package:gdjg_pure_flutter/data/demo_setting_data/entity/worker_flow/count/business_get_index_business_count_biz_model.dart';
import 'package:gdjg_pure_flutter/data/demo_setting_data/repo/worker_flow.dart';
import 'package:get/get.dart' hide Response;

/// @date 2025/06/12
/// @description WorkerFlow页UI仓库
/// 1.为vm提供业务数据
/// 2.解耦视图层与业务仓库之间的耦合
class WorkerFlowUIRep {
  /// 实体数据
  var headerEntity = BusinessCountBizModel().obs;
  var flowEntity = WorkerFlowUIRepEntity().obs;

  /// 初始化数据同步，返回同步是否成功的状态给UI层
  /// 如果成功，则将业务数据存储在entity中
  Future<WorkerFlowUIRepEntity> fetchListData() async {
    var result = await WorkerFlowRepo().fetchListData();
    flowEntity.value = WorkerFlowUIRepEntity(data: result?.list);
    return flowEntity.value;
  }

  Future<BusinessCountBizModel> fetchHeaderData() async {
    var result = await WorkerFlowRepo().fetchHeaderData();
    headerEntity.value = result ?? BusinessCountBizModel();
    return headerEntity.value;
  }
}

class WorkerFlowUIRepEntity {
  List<BusinessGetIndexBusinessListABizModel>? data;
  WorkerFlowUIRepEntity({this.data});

  @override
  String toString() {
    return 'WorkerFlowUIRepEntity{data: $data}';
  }
}
