import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:gdjg_pure_flutter/iconfont/iconfont.dart';
import 'package:gdjg_pure_flutter/utils/route_util/base_page/base_ful_page.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:get/get.dart';
import 'vm/choose_project_vml.dart';

/// @date 2025/07/07
/// @param props 页面路由参数
/// @returns
/// @description ChooseProject页面入口
class ChooseProjectPage extends BaseFulPage {
  ChooseProjectPage({super.key})
      : super(
          appBar: YPAppBar(title: "选择项目"),
          canBack: false,
        );

  @override
  State<ChooseProjectPage> createState() => _ChooseProjectPageState();
}

class _ChooseProjectPageState extends BaseFulPageState<ChooseProjectPage> {
  final ChooseProjectViewModel viewModel = ChooseProjectViewModel();
  var screenWidth = 0.0;

  @override
  Widget yBuild(BuildContext context) {
    screenWidth = MediaQuery.of(context).size.width;
    return Container(
      color: Colors.white,
      child: Column(
        children: [
          Container(
            height: 40,
            decoration: BoxDecoration(
              color: Color(0xFFF0F0F0),
              borderRadius: BorderRadius.circular(8),
            ),
            alignment: Alignment.centerLeft,
            padding: EdgeInsets.only(left: 12),
            margin: EdgeInsets.symmetric(horizontal: 24, vertical: 24),
            child: Row(
              children: [
                IconFont(IconNames.saasSearch, size: 20),
                SizedBox(width: 6),
                Expanded(
                  child: TextField(
                    decoration: InputDecoration(
                      hintText: "请输入项目名称",
                      border: InputBorder.none,
                      isCollapsed: true,
                    ),
                  ),
                ),
              ],
            ),
          ),
          Container(height: 12, color: Color(0xFFF5F5F5)),
          SizedBox(height: 16),
          Obx(() => Expanded(
                child: ListView.separated(
                  itemBuilder: _buildItemView,
                  separatorBuilder: _buildSeparatorView,
                  itemCount: viewModel.uiState.value.list.length,
                ),
              )),
          Container(height: 10, color: Color(0xFFF5F5F5)),
          SafeArea(
            bottom: true,
            child: InkWell(
              onTap: () => YPRoute.closePage(onSend2Previous()),
              child: Container(
                width: double.infinity,
                height: 44,
                padding: EdgeInsets.symmetric(vertical: 8),
                margin: EdgeInsets.all(16),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(4),
                  color: Color(0xFF1983FF),
                ),
                child: Center(
                  child: Text("确定", style: TextStyle(color: Colors.white, fontSize: 16)),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Object? onSend2Previous() => viewModel.backParams;

  Widget _buildSeparatorView(BuildContext context, int index) {
    return Container(
      height: 1,
      color: Color(0xFFF5F5F5),
      margin: EdgeInsets.symmetric(horizontal: 16),
    );
  }

  Widget _buildItemView(BuildContext context, int index) {
    final itemData = viewModel.uiState.value.list[index];
    return InkWell(
      onTap: () => viewModel.setChecked(index),
      child: Container(
        alignment: Alignment.centerLeft,
        padding: EdgeInsets.all(16),
        child: Row(
          children: [
            IconFont(
              itemData.isChecked ? IconNames.saasCheck : IconNames.saasRadio,
              size: 20,
              color: "#1983FF",
            ),
            SizedBox(width: 8),
            Expanded(
              child: Text(
                itemData.title,
                maxLines: 1,
                style: TextStyle(fontSize: 18, color: Color(0xFF323233)),
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
