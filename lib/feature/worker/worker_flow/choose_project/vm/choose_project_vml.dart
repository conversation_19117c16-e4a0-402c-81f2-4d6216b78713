import "package:collection/collection.dart";
import "package:gdjg_pure_flutter/data/worker_flow/repo/net_model_personal_with_join_biz_model.dart";
import "package:get/get.dart";
import "../ui_rep/choose_project_ui_rep.dart";
import "protocol/choose_project_us.dart";

/// @date 2025/07/07
/// @description ChooseProject页ViewModel
/// 1. 处理数据的加载状态，如loading和错误视图等
/// 2. 观测UIRep中的业务实体转为UIState
/// 3. 接受来自view的意图并分发事件进行处理（暂无）
/// 4. 对View层暴露事件（操作视图和处理一些与系统权限交互等场景）
class ChooseProjectViewModel {
  var backParams = {};
  var dataList = {}.obs;
  var uiState = ChooseProjectUIState().obs;
  var uiRep = ChooseProjectUIRep();

  ChooseProjectViewModel() {
    fetchData();
    ever(uiRep.bizModel, (value) {
      convertEntityToUIState(value);
    });
  }

  /// 初始化数据同步，如果不需要loading，则在这里去掉
  void fetchData() async {
    try {
      await uiRep.fetchListData({"is_ignore": 0});
    } catch (e) {
      uiState.value.list = [];
    } finally {}
  }

  void setChecked(int index) {
    var mList = uiState.value.list.mapIndexed((idx, item) {
      if (index == idx) {
        item.isChecked = true;
        backParams = {"name": item.title, "id": item.projectId};
      } else {
        item.isChecked = false;
      }
      return item;
    }).toList();
    uiState.value = ChooseProjectUIState(list: mList);
  }

  /// 将实体数据转换为UIState
  /// @param entity
  /// @returns
  void convertEntityToUIState(List<NetModelPersonalWithJoinABizModel> entity) {
    var projectList = <ProjectUIState>[];
    projectList = entity.map((e) {
      return ProjectUIState(title: e.name, projectId: e.workNoteId.toStringAsFixed(0));
    }).toList();
    uiState.value = ChooseProjectUIState(list: projectList);
  }
}
