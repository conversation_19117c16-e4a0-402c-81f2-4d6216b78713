import 'package:gdjg_pure_flutter/data/worker_flow/repo/net_model_personal_with_join_biz_model.dart';
import 'package:gdjg_pure_flutter/data/worker_flow/repo/worker_flow_repo.dart';
import 'package:get/get.dart';

/// @date 2025/07/07
/// @description ChooseProject页UI仓库
/// 1.为vm提供业务数据
/// 2.解耦视图层与业务仓库之间的耦合
class ChooseProjectUIRep {
  /// 实体数据
  var bizModel = <NetModelPersonalWithJoinABizModel>[].obs;

  /// 初始化数据同步，返回同步是否成功的状态给UI层
  /// 如果成功，则将业务数据存储在entity中
  Future<List<NetModelPersonalWithJoinABizModel>> fetchListData(Map<String, Object> params) async {
    var result = await WorkerFlowRepo().fetchProjectList(params);
    if (result.isOK()) {
      final data = result.getSucData();
      if (data?.list.isNotEmpty == true) {
        bizModel.value = data!.list;
      }
    }
    return bizModel;
  }
}
