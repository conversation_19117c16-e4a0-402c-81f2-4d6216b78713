
import 'package:flutter/material.dart';
import 'package:gdjg_pure_flutter/feature/worker/change_project/vm/change_project_vm.dart';
import 'package:gdjg_pure_flutter/feature/worker/change_project/widget/project_item_card.dart';
import 'package:gdjg_pure_flutter/iconfont/iconfont.dart';
import 'package:gdjg_pure_flutter/utils/route_util/base_page/base_ful_page.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';
import 'package:get/get.dart';

class ChangeProjectPage extends BaseFulPage {

  const ChangeProjectPage({super.key}): super(appBar: const YPAppBar(title: "切换项目"));

  @override
  State createState() => _ChangeProjectPage();
}
class _ChangeProjectPage extends BaseFulPageState {

  final vm = ChangeProjectVM();

  TextEditingController _controller = TextEditingController();

  @override
  onPageCreate() {
    vm.getProjectList();
  }

  @override
  onPageRoute(Object? routeParams, bool fromLaunchTask) {
    super.onPageRoute(routeParams, fromLaunchTask);
    var  projectId = routeParams as double?;
    vm.setSelectProId(projectId ?? 0);
  }


  InputBorder _inputBorder() {
    return const OutlineInputBorder(
      borderSide: BorderSide(
        color: Color.fromARGB(0, 0, 0, 0),
        width: 0,
      ),
    );
  }


  Widget _buildSearchView(){
    return Container(
      height: 36,
      width: double.infinity,
      // margin: const EdgeInsets.fromLTRB(12, 0, 12, 0),
      decoration: BoxDecoration(
        color: Color(0xFFF0F0F0), // 背景颜色
        borderRadius: BorderRadius.circular(4), // 圆角
      ),
      child: Row(
        children: [
          const SizedBox(width: 12),
          const IconFont(IconNames.saasSearch, size: 18),
          const SizedBox(width: 5),
          Expanded(
            // 添加 Expanded 以解决溢出问题
            child: TextField(
              controller: _controller,
              maxLines: 1,
              style: const TextStyle(
                fontSize: 14,
                color: Color.fromARGB(217, 0, 0, 0),
              ),
              decoration: InputDecoration(
                hintText: "请输入项目名称",
                hintStyle: const TextStyle(
                  color: Color.fromARGB(166, 0, 0, 0),
                  fontSize: 14,
                ),
                // filled: true, // 去除默认的填充颜色
                // fillColor: Colors.red,
                focusedBorder: _inputBorder(),
                enabledBorder: _inputBorder(),
                disabledBorder: _inputBorder(),
                errorBorder: _inputBorder(),
                focusedErrorBorder: _inputBorder(),
                border: _inputBorder(),
                contentPadding: EdgeInsets.all(0),
              ),
              onSubmitted: (value) {
                // vm.search(value);
                // 如果你想自动关闭键盘，可以使用下面的代码：
                FocusScope.of(context).unfocus();
              },
              onChanged: (value) {
                // vm.searchAreaOfKeyword(value);
                vm.keywordsValueChanged(value);
              },
            ),
          ),
          Obx(() => Visibility(
              visible: vm.us.isShowClear,
              child: GestureDetector(
                onTap: () {

                },
                child: Container(
                  margin: const EdgeInsets.fromLTRB(0, 0, 12, 0),
                  child: const IconFont(IconNames.saasClean),
                ),
              )))
        ],
      ),
    );
  }

  @override
  Widget yBuild(BuildContext context) {
    return Container(
      width: double.infinity,
      height: double.infinity,
      color: Colors.white,
      padding: const EdgeInsets.fromLTRB(16, 8, 18, 8),
      child:Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Divider(thickness: 8,color: Color(0xFFF0F0F0),),
          SizedBox(height: 8),
          _buildSearchView(),
          SizedBox(height: 16),
          Obx(()=>
              Text("在建(${vm.us.projectList.length})",style: TextStyle(color: Color(0xFF000000),fontSize: 18),),
          ),
          Flexible(
              child: Container(
                width: double.infinity,
                height: double.infinity,
                child: Obx(()=>
                    ListView.builder(
                      itemCount: vm.us.projectList.length,
                      itemBuilder: (context, index) {
                        return ProjectItemCard(item: vm.us.projectList[index],type: 1,isSelected: vm.us.projectList[index].id == vm.getSelectProId(),onClickItem: (item) {
                          YPRoute.closePage(item);

                        },);
                      },
                    )
                ),
              )
          ),
          Container(
            width: double.infinity,
            height: 44,
            decoration:  BoxDecoration(
              color: ColorsUtil.primaryColor,
              borderRadius: BorderRadius.circular(4), // 圆角
            ),
            child: Center(
              child: Text("新建项目",style: TextStyle(color: Colors.white,fontSize: 16),),
            ),
          )
        ]
      )
    );
  }
}
