
import 'package:flutter/material.dart';
import 'package:gdjg_pure_flutter/data/change_project/repo/model/biz_model.dart';
import 'package:gdjg_pure_flutter/iconfont/iconfont.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';

class ProjectItemCard extends StatelessWidget {
  ProjectGetAllProjectNameABizModel item;
  // 1 记工， 2 记账
  int type;

  Function(ProjectGetAllProjectNameABizModel item) onClickItem;

  ProjectItemCard({
    super.key,
    required this.item,
    required this.type,
    required this.onClickItem,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        onClickItem(item);
      },
      child: SizedBox(
        height: 48,
        width: double.infinity,
        child:  Row(
          children: [
            IconFont(IconNames.saasProjectIcon,size: 24,color: '#7DADFD',),
            SizedBox(width: 8),
            Flexible(
                flex: 1,
                child: SizedBox(
                  width: double.infinity,
                  child: Text(item.name,style: TextStyle(fontSize: 16,color: Color(0xFF000000)), maxLines: 1,overflow: TextOverflow.ellipsis,),
                )
            ),
            Row(
                children: [
                  Visibility(
                      visible: item.isLastBookkeeping==1,
                      child: Container(
                        padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: Color(0xFFECF4FD),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Text('上次${type == 1 ? '记工' : '记账'}', style: TextStyle(fontSize: 16,color: ColorsUtil.primaryColor)),
                      )),
                  SizedBox(width: 16),
                  IconFont(IconNames.saasSetting,size: 24,color: '#8d8c93',),
                ]
            )
          ],
        ),
      ),
    );
  }
}