
import 'package:gdjg_pure_flutter/data/change_project/repo/model/biz_model.dart';
import 'package:get/get.dart';

class ChangeProjectUS {
  final _isShowClear = false.obs;
  get isShowClear => _isShowClear.value;

  final _projectList = <ProjectGetAllProjectNameABizModel>[].obs;
  get projectList => _projectList.value;

  setShowClear(bool value){
    _isShowClear.value = value;
    // _isShowClear.refresh();
  }

  setProjectList(List<ProjectGetAllProjectNameABizModel> value){
    _projectList.value = value;
    // _projectList.refresh();
  }

}