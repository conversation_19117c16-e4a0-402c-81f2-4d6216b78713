
import 'package:gdjg_pure_flutter/data/change_project/ds/model/param/param_model.dart';
import 'package:gdjg_pure_flutter/data/change_project/repo/change_project_repo.dart';
import 'package:gdjg_pure_flutter/feature/worker/change_project/vm/change_project_us.dart';

class ChangeProjectVM {

  final us = ChangeProjectUS();
  final repo = ChangeProjectRepo();

  keywordsValueChanged(String value){
    us.setShowClear(value.isNotEmpty);
    // us.isShowClear.refresh();

  }

  getProjectList() async {
    var resp = await repo.getAllProjectList(ProjectGetAllProjectNameParamModel());
    if(resp.isOK()){
      var list = resp.getSucData()?.list;
      if(list != null){
        us.setProjectList(list);
      }

    }
  }

}