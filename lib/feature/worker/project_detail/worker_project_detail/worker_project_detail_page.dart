import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/data/worker_data/worker_project_data/repo/model/project_get_project_list_biz_model.dart';
import 'package:gdjg_pure_flutter/feature/worker/project_detail/worker_project_detail/vm/worker_project_detail_vm.dart';
import 'package:gdjg_pure_flutter/generated/assets.dart';
import 'package:gdjg_pure_flutter/utils/route_util/base_page/base_ful_page.dart';
import 'package:gdjg_pure_flutter/widget/network_carousel_widget.dart';
import 'package:gdjg_pure_flutter/widget/calendar/dynamic_height_calendar.dart';
import 'package:gdjg_pure_flutter/widget/month_switcher_view.dart';
import 'package:gdjg_pure_flutter/feature/group/group_pro_calendar/view/group_statistics_list_view.dart';

import 'package:get/get.dart';

/// 我创建项目详情页面参数
class MyCreatedProjectDetailProps {
  final ProjectGetProjectListABizModel projectModel;
  final bool isFromCompleted;

  MyCreatedProjectDetailProps({
    required this.projectModel,
    this.isFromCompleted = false,
  });
}

/// 工人项目详情页面
class WorkerProjectDetailPage extends BaseFulPage {
  const WorkerProjectDetailPage({super.key}) : super(appBar: const YPAppBar(title: '项目详情'));

  @override
  State createState() => _WorkerProjectDetailPageState();
}

class _WorkerProjectDetailPageState extends BaseFulPageState {
  late WorkerProjectDetailVM _viewModel;
  final GlobalKey<DynamicHeightCalendarState> _calendarKey =
      GlobalKey<DynamicHeightCalendarState>();

  @override
  void onPageCreate() {
    super.onPageCreate();
    _viewModel = WorkerProjectDetailVM();
  }

  @override
  void onPageRoute(Object? routeParams, bool fromLaunchTask) {
    super.onPageRoute(routeParams, fromLaunchTask);
    if (routeParams is MyCreatedProjectDetailProps) {
      _viewModel.initProjectInfo(routeParams.projectModel, isFromCompleted: routeParams.isFromCompleted);
      // 动态设置AppBar标题
      dynamicTitle = routeParams.projectModel.name;
    } else if (routeParams is ProjectGetProjectListABizModel) {
      _viewModel.initProjectInfo(routeParams);
      // 动态设置AppBar标题
      dynamicTitle = routeParams.name;
    }
  }

  @override
  Widget yBuild(BuildContext context) {
    return Container(
      color: Colors.white,
      child: Column(
        children: [
          // 轮播图
          _buildCarouselSection(),

          // 分割线
          Divider(height: 6.h, color: Color(0xFFF0F0F0), thickness: 6.h),

          // 月份切换器
          _buildMonthSwitchView(),

          Expanded(
            child: CustomScrollView(
              slivers: [
                // 统计数据
                SliverToBoxAdapter(
                  child: _buildStatisticsView(),
                ),

                // 大日历
                _buildCalendarView(),
              ],
            ),
          ),

          // 底部按钮区域
          _buildBottomButtons(),
        ],
      ),
    );
  }



  /// 构建轮播图区域
  Widget _buildCarouselSection() {
    return Container(
      color: Color(0xFFF0F0F0),
      padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 6.h),
      child: NetworkCarouselWidget(
        height: 48.h,
        code: 'JGJZ_PROJECT_CALENDAR_PERSONAL_B',
      ),
    );
  }

  /// 月份切换控件
  Widget _buildMonthSwitchView() {
    return Obx(
      () => MonthSwitcherView(
        key: Key("MonthSwitcherView"),
        initialTime: _viewModel.us.dataTime.value,
        onYearMonthSelected: (selectTime) {
          _viewModel.updateDateRange(selectTime);
        },
      ),
    );
  }

  /// 统计布局
  Widget _buildStatisticsView() {
    return Obx(() {
      if (_viewModel.us.isStatisticsEmpty) {
        return SizedBox(
          width: double.infinity,
          height: 56.h,
          child: Center(
            child: Text(
              "当月无记工",
              style: TextStyle(color: Color(0xFF323232), fontSize: 16.sp),
            ),
          ),
        );
      }
      return GroupStatisticsView(
        items: _viewModel.us.statisticsList,
        initialVisibleCount: 1,
        onItemTap: (index) {
          // TODO: 实现统计点击功能
        },
      );
    });
  }

  /// 大日历
  Widget _buildCalendarView() {
    return Obx(
      () => DynamicHeightCalendar(
        key: _calendarKey,
        onValueChange: (date) {
          _viewModel.updateDateRange(date);
        },
        events: _viewModel.us.events.value,
      ),
    );
  }

  /// 构建底部按钮区域
  Widget _buildBottomButtons() {
    return Container(
      color: Colors.white,
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 6.h),
      child: Row(
        children: [
          _buildBottomButton(
            iconPath: Assets.tabbarIconTabbarStatisticNormal,
            text: '统计',
            onTap: _viewModel.onStatisticsTap,
          ),
          _buildBottomButton(
            iconPath: Assets.workerIconAttendanceSheetTab,
            text: '考勤表',
            onTap: _viewModel.onAttendanceClick,
          ),
          _buildBottomButton(
            iconPath: Assets.tabbarIconTabbarNotSettledNormal,
            text: '未结',
            onTap: _viewModel.onUnsettledClick,
          ),
          _buildBottomButton(
            iconPath: Assets.workerIconSettingTab,
            text: '设置',
            onTap: _viewModel.onSettingsClick,
          ),
        ],
      ),
    );
  }

  /// 构建单个底部按钮
  Widget _buildBottomButton({
    required String iconPath,
    required String text,
    required VoidCallback onTap,
  }) {
    return Expanded(
      child: GestureDetector(
        onTap: onTap,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Image.asset(
              iconPath,
              width: 26.w,
              height: 26.w,
            ),
            Text(
              text,
              style: TextStyle(
                fontSize: 14.sp,
                color: const Color(0xFF666666),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
