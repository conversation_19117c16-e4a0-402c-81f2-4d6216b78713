import 'package:gdjg_pure_flutter/feature/group/common_uistate/statistics_ui_state.dart';
import 'package:get/get.dart';
import 'package:gdjg_pure_flutter/data/worker_data/worker_project_data/repo/model/project_get_project_list_biz_model.dart';
import 'package:gdjg_pure_flutter/widget/calendar/dayevent/DayEvent.dart';

/// 工人项目详情页面UIState
class WorkerProjectDetailUS {
  /// 项目信息
  final Rx<ProjectGetProjectListABizModel?> projectInfo = Rx<ProjectGetProjectListABizModel?>(null);

  /// 项目名称
  final RxString projectName = ''.obs;

  /// 工作记录ID
  final RxString workNoteId = ''.obs;

  /// 企业ID
  final RxDouble corpId = 0.0.obs;

  /// 部门ID
  final RxString deptId = ''.obs;

  /// 当前日期
  final Rx<DateTime> dataTime = DateTime.now().obs;

  /// 统计数据列表
  final RxList<StatisticsItemUIState> statisticsList = <StatisticsItemUIState>[].obs;

  /// 日历事件数据
  final Rx<Map<DateTime, List<DayEvent>>> events = Rx<Map<DateTime, List<DayEvent>>>({});

  /// 设置项目名称
  void setProjectName(String name) {
    projectName.value = name;
  }

  /// 设置工作记录ID
  void setWorkNoteId(String id) {
    workNoteId.value = id;
  }

  /// 设置企业ID
  void setCorpId(double id) {
    corpId.value = id;
  }

  /// 设置部门ID
  void setDeptId(String id) {
    deptId.value = id;
  }

  /// 设置当前日期
  void setCurrentDate(DateTime date) {
    dataTime.value = date;
  }

  /// 设置统计数据列表
  void setStatisticsList(List<StatisticsItemUIState> list) {
    statisticsList.value = list;
  }

  /// 设置日历事件数据
  void setEvents(Map<DateTime, List<DayEvent>> eventMap) {
    events.value = eventMap;
  }



  /// 统计数据是否为空
  bool get isStatisticsEmpty => statisticsList.isEmpty;
}
