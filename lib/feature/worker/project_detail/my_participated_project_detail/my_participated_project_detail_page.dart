import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/feature/group/group_event_bus/group_edit_wage_event_bus_model.dart';
import 'package:gdjg_pure_flutter/feature/group/group_pro_calendar/view/group_statistics_list_view.dart';
import 'package:gdjg_pure_flutter/feature/worker/project_detail/my_participated_project_detail/view/my_participated_project_bench_view.dart';
import 'package:gdjg_pure_flutter/feature/worker/project_detail/my_participated_project_detail/vm/my_participated_project_detail_vm.dart';
import 'package:gdjg_pure_flutter/utils/event_bus_util/event_bus_util.dart';
import 'package:gdjg_pure_flutter/utils/route_util/base_page/base_ful_page.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/appbar_util.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';
import 'package:gdjg_pure_flutter/widget/calendar/calendar_week.dart';
import 'package:gdjg_pure_flutter/widget/calendar/dynamic_height_calendar.dart';
import 'package:gdjg_pure_flutter/widget/month_switcher_view.dart';
import 'package:get/get.dart';

/// 我参与的项目详情页面参数
class MyParticipatedProjectDetailProps {
  final String deptId;
  final bool isFromCompleted;

  MyParticipatedProjectDetailProps({
    required this.deptId,
    this.isFromCompleted = false,
  });
}

/// 我参与的项目详情页面
class MyParticipatedProjectDetailPage extends BaseFulPage {
  const MyParticipatedProjectDetailPage({super.key}) : super(appBar: null);

  @override
  createState() => _MyParticipatedProjectDetailPageState();
}

class _MyParticipatedProjectDetailPageState<MyParticipatedProjectDetailPage>
    extends BaseFulPageState {
  final GlobalKey<DynamicHeightCalendarState> _calendarKey =
      GlobalKey<DynamicHeightCalendarState>();
  final MyParticipatedProjectDetailVM _viewModel = MyParticipatedProjectDetailVM();
  StreamSubscription? _stream;

  @override
  onPageRoute(Object? routeParams, bool fromLaunchTask) {
    if (routeParams is MyParticipatedProjectDetailProps) {
      _viewModel.init(routeParams.deptId, isFromCompleted: routeParams.isFromCompleted);
    } else if (routeParams is String) {
      // 兼容直接传递 deptId 字符串的情况
      _viewModel.init(routeParams);
    }
  }

  @override
  void onPageCreate() {
    super.onPageCreate();
    _stream = EventBusUtil.collect<GroupEditWageEventBusModel>((data) {
      _viewModel.refresh();
    });
  }

  @override
  void onPageDestroy() {
    super.onPageDestroy();
    _stream?.cancel();
  }

  @override
  Widget yBuild(BuildContext context) {
    return Obx(() => Scaffold(
        appBar: AppBarUtil.buildWithResourceWidget(
            title: _viewModel.us.projectName.isNotEmpty
                ? _viewModel.us.projectName
                : '项目详情',
            resourceText: '拍证据工资有保障',
            resourceIcon: "assets/images/common/ic_take_phone_small.webp",
            onBackTap: () => YPRoute.closePage()),
        body: contentView()));
  }

  /// 页面内容视图
  Widget contentView() {
    return Obx(() {
      if (_viewModel.us.isLoading) {
        return Center(
          child: CircularProgressIndicator(
            color: ColorsUtil.primaryColor,
          ),
        );
      }

      return Container(
        color: Colors.white,
        height: double.infinity,
        child: Column(
          children: [
            Divider(height: 6.h, color: Color(0xFFF0F0F0), thickness: 6.h),
            _buildMonthSwitchView(),
            Expanded(
              child: CustomScrollView(
                slivers: [
                  SliverToBoxAdapter(
                    child: _buildStatisticsView(),
                  ),
                  SliverToBoxAdapter(
                    child: _buildWeekView(),
                  ),
                  _buildCalenderView(),
                  SliverToBoxAdapter(
                    child: _buildFunctionView(),
                  ),
                ],
              ),
            ),
            _buildBottomView(),
          ],
        ),
      );
    });
  }

  /// 统计布局
  Widget _buildStatisticsView() {
    return Obx(() {
      if (_viewModel.us.isStatisticsEmpty) {
        return SizedBox(
          width: double.infinity,
          height: 56.h,
          child: Center(
            child: Text(
              "当月无记工",
              style: TextStyle(color: Color(0xFF323232), fontSize: 16.sp),
            ),
          ),
        );
      }
      return GroupStatisticsView(
        items: _viewModel.us.statisticsList,
        initialVisibleCount: 1,
        onItemTap: (index) {
          _viewModel.onStatisticsTap(index: index);
        },
      );
    });
  }

  /// 月份切换控件
  Widget _buildMonthSwitchView() {
    return Obx(
      () => MonthSwitcherView(
        key: Key("MonthSwitcherView"),
        initialTime: _viewModel.us.dataTime,
        onYearMonthSelected: (selectTime) {
          _viewModel.updateDateRange(selectTime);
        },
      ),
    );
  }

  Widget _buildWeekView() {
    return CalendarWeek();
  }

  /// 大日历
  Widget _buildCalenderView() {
    return Obx(
      () => DynamicHeightCalendar(
        key: _calendarKey,
        onValueChange: (date) {
          _viewModel.updateDateRange(date);
        },
        events: _viewModel.us.events,
      ),
    );
  }

  /// 功能按钮区域
  Widget _buildFunctionView() {
    return MyParticipatedProjectBenchView(viewModel: _viewModel);
  }

  /// 底部打卡按钮
  Widget _buildBottomView() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 16.w),
      decoration: BoxDecoration(
        border: Border(
          top: BorderSide(color: Color(0xFFE6E6E6), width: 1),
        ),
      ),
      child: SizedBox(
        height: 44.h,
        child: TextButton(
          style: TextButton.styleFrom(
            backgroundColor: ColorsUtil.primaryColor,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(6.r),
              side: BorderSide(color: ColorsUtil.primaryColor, width: 1),
            ),
            textStyle: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.w500,
              height: 20 / 16,
            ),
          ),
          onPressed: () {
            _viewModel.onClockInTap();
          },
          child: Text('打卡',
              style: TextStyle(
                  color: Colors.white,
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w500)),
        ),
      ),
    );
  }
}
