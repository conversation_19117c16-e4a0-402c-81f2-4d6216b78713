import 'package:gdjg_pure_flutter/feature/group/common_uistate/statistics_ui_state.dart';
import 'package:get/get.dart';
import 'package:gdjg_pure_flutter/widget/calendar/dayevent/DayEvent.dart';

/// 我参与的项目详情页面UI状态
class MyParticipatedProjectDetailUS {
  /// 当前日期
  final _dataTime = DateTime.now().obs;
  
  /// 项目名称
  final _projectName = ''.obs;
  
  /// 工作本ID
  final _workNoteId = ''.obs;
  
  /// 部门ID
  final _deptId = ''.obs;
  
  /// 企业ID
  final _corpId = 0.0.obs;
  
  /// 统计数据列表
  final _statisticsList = <StatisticsItemUIState>[].obs;
  
  /// 统计数据是否为空
  final _isStatisticsEmpty = false.obs;
  
  /// 日历事件数据
  final RxMap<DateTime, List<DayEvent>> _events = <DateTime, List<DayEvent>>{}.obs;
  
  /// 页面加载状态
  final _isLoading = false.obs;

  // Getters
  DateTime get dataTime => _dataTime.value;
  String get projectName => _projectName.value;
  String get workNoteId => _workNoteId.value;
  String get deptId => _deptId.value;
  double get corpId => _corpId.value;
  RxList<StatisticsItemUIState> get statisticsList => _statisticsList;
  bool get isStatisticsEmpty => _isStatisticsEmpty.value;
  Map<DateTime, List<DayEvent>> get events => Map<DateTime, List<DayEvent>>.from(_events);
  bool get isLoading => _isLoading.value;

  // Setters
  void setCurrentDate(DateTime date) {
    _dataTime.value = date;
    _dataTime.refresh();
  }

  void setProjectName(String name) {
    _projectName.value = name;
  }

  void setWorkNoteId(String id) {
    _workNoteId.value = id;
  }

  void setDeptId(String id) {
    _deptId.value = id;
  }

  void setCorpId(double id) {
    _corpId.value = id;
  }

  void setStatisticsList(List<StatisticsItemUIState> list) {
    _statisticsList.value = list;
    _isStatisticsEmpty.value = list.isEmpty;
  }

  void setEvents(Map<DateTime, List<DayEvent>> events) {
    _events.assignAll(events);
  }

  void setLoading(bool loading) {
    _isLoading.value = loading;
  }

  /// 清空统计数据
  void clearStatistics() {
    _statisticsList.clear();
    _isStatisticsEmpty.value = true;
  }

  /// 清空日历事件
  void clearEvents() {
    _events.clear();
  }
}
