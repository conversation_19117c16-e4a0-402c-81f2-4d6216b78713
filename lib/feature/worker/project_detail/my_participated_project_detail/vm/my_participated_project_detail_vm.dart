import 'package:gdjg_pure_flutter/data/group_data/group_calendar/ds/model/param/group_calendar_param_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_calendar/repo/group_repo.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_calendar/repo/model/group_calendar_biz_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/corp_select/ds/param/corp_select_param_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/corp_select/repo/corp_select_repo.dart';
import 'package:gdjg_pure_flutter/data/worker_data/worker_project_data/repo/worker_project_repo.dart';
import 'package:gdjg_pure_flutter/feature/group/common_uistate/statistice_helper.dart';
import 'package:gdjg_pure_flutter/feature/group/common_uistate/statistics_ui_state.dart';
import 'package:gdjg_pure_flutter/feature/group/group_pro_bill/entity/group_pro_bill_props.dart';
import 'package:gdjg_pure_flutter/feature/worker/project_detail/my_participated_project_detail/vm/my_participated_project_detail_us.dart';
import 'package:gdjg_pure_flutter/feature/worker/project_setup/my_participated_project_setup/my_participated_project_setup_page.dart';
import 'package:gdjg_pure_flutter/init_module/init_route.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/utils/system_util/date_util.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/toast_util.dart';
import 'package:gdjg_pure_flutter/widget/calendar/dayevent/DayEvent.dart';

/// 我参与的项目详情页面ViewModel
class MyParticipatedProjectDetailVM {
  final _workerProjectRepo = WorkerProjectRepo();
  final _corpSelectRepo = CorpSelectRepo();
  final _groupRepo = GroupRepo();
  final us = MyParticipatedProjectDetailUS();
  
  String? _deptId;
  bool _isFromCompleted = false;
  var _dateTime = DateTime.now();

  /// 初始化页面数据
  void init(String deptId, {bool isFromCompleted = false}) {
    _deptId = deptId;
    _isFromCompleted = isFromCompleted;
    us.setDeptId(deptId);
    _fetchProjectDetail();
  }

  /// 刷新数据
  void refresh() {
    _fetchProjectDetail();
  }

  /// 更新日期范围
  void updateDateRange(DateTime dateTime) {
    _dateTime = dateTime;
    us.setCurrentDate(dateTime);
    _fetchCalendarData(dateTime);
  }

  /// 获取项目详情
  void _fetchProjectDetail() async {
    if (_deptId == null || _deptId!.isEmpty) {
      ToastUtil.showToast('项目参数错误');
      return;
    }

    us.setLoading(true);

    try {
      final result = await _workerProjectRepo.getDeptDetail(_deptId!);

      if (result.isOK()) {
        final deptData = result.getSucData();
        if (deptData != null) {
          // 设置项目基本信息
          us.setProjectName(deptData.name);
          us.setWorkNoteId(deptData.workNoteId.toString());
          us.setCorpId(deptData.corpId);

          // 切换企业
          _fetchCorpSelect(deptData.corpId);

          // 获取日历数据
          _fetchCalendarData(_dateTime);
        }
      } else {
        ToastUtil.showToast(result.fail?.errorMsg ?? '获取项目详情失败');
      }
    } catch (e) {
      ToastUtil.showToast('获取项目详情失败');
    } finally {
      us.setLoading(false);
    }
  }

  /// 切换企业
  void _fetchCorpSelect(double corpId) async {
    final param = CorpSelectParamModel(corp_id: corpId.toString());
    await _corpSelectRepo.fetchCorpSelect(param);
  }

  /// 获取日历数据
  void _fetchCalendarData(DateTime dateTime) async {
    if (us.workNoteId.isEmpty) return;

    final params = GroupCalendarParamModel();
    params.work_note = us.workNoteId;
    params.start_time = DateUtil.formatStartDate(dateTime);
    params.end_time = DateUtil.formatEndDate(dateTime);

    final result = await _groupRepo.getGroupCalendar(params);
    if (result.isOK()) {
      _convertEntityToUIState(result.getSucData());
      _convertEntityToCalendarUIState(result.getSucData());
    }
  }

  /// 转换统计数据
  void _convertEntityToUIState(GroupCalendarBizModel? data) {
    List<StatisticsItemUIState> list =
        StatisticsUIStateHelper.buildStatisticsItem(data?.count);
    us.setStatisticsList(list);
  }

  /// 转换日历事件数据
  void _convertEntityToCalendarUIState(GroupCalendarBizModel? data) {
    Map<DateTime, List<DayEvent>> events = {};
    // 注意：当前 GroupCalendarBizModel 中的 calendar 字段被注释了
    // 如果需要日历事件，需要在 GroupCalendarBizModel 中启用 calendar 字段
    // 这里暂时设置为空的事件映射
    us.setEvents(events);
  }

  /// 统计按钮点击
  void onStatisticsTap({int? index}) {
    if (us.workNoteId.isEmpty || _deptId == null) {
      ToastUtil.showToast('项目信息错误');
      return;
    }

    final params = GroupProBillProps(
      workNoteId: us.workNoteId,
      workNoteName: us.projectName,
      deptId: double.tryParse(_deptId!) ?? 0,
      isJoin: true,
    );

    YPRoute.openPage(RouteNameCollection.groupProBill, params: params);
  }

  /// 未结按钮点击
  void onUnsettledTap() {
    ToastUtil.showToast('未结功能开发中');
  }

  /// 记工表按钮点击
  void onWorkScheduleTap() {
    ToastUtil.showToast('记工表功能开发中');
  }

  /// 打卡按钮点击
  void onClockInTap() {
    ToastUtil.showToast('打卡功能开发中');
  }

  /// 打卡统计按钮点击
  void onClockStatisticsTap() {
    ToastUtil.showToast('打卡统计功能开发中');
  }

  /// 水印相机按钮点击
  void onWatermarkCameraTap() {
    ToastUtil.showToast('水印相机功能开发中');
  }

  /// 项目设置按钮点击
  void onProjectSettingsTap() {
    if (_deptId == null || _deptId!.isEmpty) {
      ToastUtil.showToast('项目信息错误');
      return;
    }

    final params = MyParticipatedProjectSetupProps(
      deptId: _deptId,
      isFromCompleted: _isFromCompleted,
    );
    YPRoute.openPage(RouteNameCollection.participatedProjectSetup, params: params);
  }

  /// 班组云相册按钮点击
  void onTeamCloudAlbumTap() {
    ToastUtil.showToast('班组云相册功能开发中');
  }

  /// 我的授权按钮点击
  void onMyAuthorizationTap() {
    ToastUtil.showToast('我的授权功能开发中');
  }
}
