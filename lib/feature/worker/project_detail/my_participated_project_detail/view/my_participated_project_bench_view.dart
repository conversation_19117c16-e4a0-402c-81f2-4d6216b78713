import 'package:flutter/material.dart';
import 'package:gdjg_pure_flutter/feature/worker/project_detail/my_participated_project_detail/vm/my_participated_project_detail_vm.dart';
import 'package:gdjg_pure_flutter/generated/assets.dart';

/// 我参与的项目详情页面功能按钮组件
class MyParticipatedProjectBenchView extends StatelessWidget {
  const MyParticipatedProjectBenchView({super.key, required this.viewModel});

  final MyParticipatedProjectDetailVM viewModel;

  @override
  Widget build(BuildContext context) {
    return _buildProBenchView();
  }

  /// 底部功能管理区
  Widget _buildProBenchView() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Divider(height: 4, color: Color(0xFFF0F0F0), thickness: 4),
        _buildSectionTitle("记工管理"),
        _buildFunctionGrid([
          _buildFunctionItem(
              "统计", Assets.groupIcCalendarMenuStatistics, () {
            viewModel.onStatisticsTap();
          }),
          _buildFunctionItem("未结",
              Assets.groupIcCalendarMenuNotCheckout, () {
            viewModel.onUnsettledTap();
          }),
          _buildFunctionItem(
              "记工表",
              Assets.groupIcCalendarMenuWorkSchedules,
              () {
            viewModel.onWorkScheduleTap();
          }),
        ]),
        Divider(height: 4, color: Color(0xFFF0F0F0), thickness: 4),
        _buildSectionTitle("日常管理"),
        _buildFunctionGrid([
          _buildFunctionItem(
              "打卡", Assets.groupIcCalendarMenuClock, () {
            viewModel.onClockInTap();
          }),
          _buildFunctionItem(
              "打卡统计",
              Assets.groupIcCalendarMenuClockStatistic,
              () {
            viewModel.onClockStatisticsTap();
          }),
          _buildFunctionItem("水印相机",
              Assets.groupIcCalendarMenuWatermarkCamera, () {
            viewModel.onWatermarkCameraTap();
          }),
        ]),
        Divider(height: 4, color: Color(0xFFF0F0F0), thickness: 4),
        _buildSectionTitle("项目信息"),
        _buildFunctionGrid([
          _buildFunctionItem(
              "项目设置",
              Assets.groupIcCalendarMenuProjectSetting,
              () {
            viewModel.onProjectSettingsTap();
          }),
          _buildFunctionItem(
              "班组云相册",
              Assets.groupIcCalendarMenuTeamCloudAlbum,
              () {
            viewModel.onTeamCloudAlbumTap();
          }),
          _buildFunctionItem("我的授权",
              Assets.groupIcCalendarMenuMandate, () {
            viewModel.onMyAuthorizationTap();
          }),
        ]),
      ],
    );
  }

  /// 标题部分
  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: EdgeInsets.fromLTRB(16, 16, 0, 16),
      child: Text(
        title,
        style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Color(0xFF323232)),
      ),
    );
  }

  /// 功能按钮网格
  Widget _buildFunctionGrid(List<Widget> items) {
    return Padding(
      padding: EdgeInsets.fromLTRB(0, 0, 0, 16),
      child: Wrap(
        runSpacing: 16,
        children: items,
      ),
    );
  }

  /// 单个功能项
  Widget _buildFunctionItem(String label, String icon, VoidCallback onTap) {
    return FractionallySizedBox(
      widthFactor: 0.25,
      child: InkWell(
        onTap: onTap,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Image.asset(icon, width: 48, height: 48),
            const SizedBox(height: 4),
            Text(
              label,
              textAlign: TextAlign.center,
              style: const TextStyle(fontSize: 14, color: Color(0xFF323232)),
            ),
          ],
        ),
      ),
    );
  }
}
