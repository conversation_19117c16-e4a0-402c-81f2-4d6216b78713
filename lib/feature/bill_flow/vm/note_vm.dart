import 'package:flutter/cupertino.dart';
import 'package:get/get_state_manager/src/simple/get_controllers.dart';

class NoteViewModel extends GetxController {

  final TextEditingController _controller = TextEditingController();
  TextEditingController get controller => _controller;

  // 历史记录
  List<String> historyNoteList = [
    '1',
    '2',
    '3',
    '4',
    '5',
  ];

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void updateHistoryNote() {
    historyNoteList.add(_controller.text);
  }
}