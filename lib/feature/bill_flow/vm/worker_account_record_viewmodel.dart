import 'package:gdjg_pure_flutter/data/worker_data/work_account_record/ds/param/project_get_last_business_project_param_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/work_account_record/repo/worker_account_record_repo.dart';
import 'package:gdjg_pure_flutter/data/worker_data/worker_project_data/repo/worker_project_repo.dart';
import 'package:gdjg_pure_flutter/feature/bill_flow/ui_state/worker_account_record_us.dart';

class WorkerAccountRecordViewmodel {
  final _accountRecordRepo = WorkerAccountRecordRepo();
  final _workerProjectRepo = WorkerProjectRepo();
  final uiState = WorkerAccountRecordUs();

  /// 初始化数据，加载项目名称
  void initData(String workNoteId) async {
    var res = await _workerProjectRepo.getProject(workNoteId);
    if (res.isOK()) {
      uiState.projectName.value = res.getSucData()?.name ?? '';
      print('-----------projectName:${uiState.projectName.value}---------------');
    }
  }
}