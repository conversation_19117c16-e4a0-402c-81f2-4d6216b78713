import 'package:gdjg_pure_flutter/data/group_data/group_pro_update/repo/group_pro_update.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_settlement/ds/model/param/group_settlement_param_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_settlement/repo/project_settle_repo.dart';
import 'package:gdjg_pure_flutter/feature/bill_flow/ui_state/cash_advance_us.dart';
import 'package:gdjg_pure_flutter/feature/group/group_edit_record_work/view/vm/edit_base_us.dart';
import 'package:gdjg_pure_flutter/feature/group/group_edit_record_work/view/vm/edit_base_vm.dart';
import 'package:gdjg_pure_flutter/feature/group/group_event_bus/group_edit_wage_event_bus_model.dart';
import 'package:gdjg_pure_flutter/feature/group/group_settlement/vm/group_settlement_us.dart';
import 'package:gdjg_pure_flutter/utils/event_bus_util/event_bus_util.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/utils/system_util/date_util.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/common_dialog.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/toast_util.dart';

class CashAdvanceVM {
  final _groupProUpdateRepo = GroupProUpdateRepo();
  var us = GroupSettlementUS();
  final _repo = ProjectSettleRepo();
  final baseVm = EditBaseVM();
  var _dateTime = DateTime.now();
  var _workNoteId = '';
  var _workerId = '';
  var _money = '';
  var _bookkeepingSource = 0.0;
  var _sourceFixId = 0.0;


  void setDateTime(DateTime date) {
    _dateTime = date;
    us.setDateTime(date);
  }

  void setWorkerId(String value) {
    _workerId = value;
    us.setWorkerName(value);
  }

  void onConfirmTap() {
    if (_money.isEmpty || (double.tryParse(_money) ?? 0) <= 0) {
      showCommonDialog(CommonDialogConfig(
        title: '提示?',
        content: '当前结算金额为0.00元，确定要记为结算吗？',
        negative: '取消',
        positive: '确认',
        onPositive: () {
          _commit();
        },
      ));
    } else {
      _commit();
    }
  }

  _commit() async {
    var params = GroupSettlementParamModel(
      work_note: _workNoteId,
      worker_id: _workerId,
      bookkeeping_source: _bookkeepingSource,
      img_url: baseVm.baseUs.photoUrls.join(','),
      money: _money,
      source_fix_id: _sourceFixId,
      business_time: DateUtil.formatDate(_dateTime),
    );
    var result = await _repo.addMyself(params);
    if (result.isOK()) {
      // 刷新流水，日历
      EventBusUtil.emit(GroupEditWageEventBusModel());
      YPRoute.closePage();
    } else {
      ToastUtil.showToast(result.fail?.errorMsg ?? "");
    }
  }
}