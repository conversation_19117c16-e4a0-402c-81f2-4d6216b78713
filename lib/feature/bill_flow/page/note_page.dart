import 'package:flutter/material.dart';
import 'package:gdjg_pure_flutter/feature/bill_flow/vm/note_vm.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/appbar_util.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/button_util.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';
import 'package:get/get.dart';

class NotePage extends StatefulWidget {
  @override
  _NotePageState createState() => _NotePageState();
}

class _NotePageState extends State<NotePage> {
  final NoteViewModel _vm = Get.put(NoteViewModel());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      backgroundColor: Colors.white,
      appBar: AppBarUtil.buildCommonAppBar(title: '备注'),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: ColorsUtil.inputBgColor,
                borderRadius: BorderRadius.circular(4),
              ),
              child: TextField(
                autofocus: true,
                controller: _vm.controller,
                obscureText: false,
                cursorOpacityAnimates: true,
                maxLines: 7,
                decoration: InputDecoration(
                  border: InputBorder.none,
                  hintText: '请输入备注',
                  counter: Container(
                    width: double.infinity,
                    child: Row(
                      children: [
                        Text('${_vm.controller.text.length}/200'),
                        Spacer(),
                        GestureDetector(
                            onTap: () {
                              _vm.controller.clear();
                            },
                            child:
                                Text('清空内容', style: TextStyle(fontSize: 12))),
                      ],
                    ),
                  ),
                  counterStyle: TextStyle(fontSize: 12, color: Colors.grey),
                  hintStyle: TextStyle(fontSize: 16, color: Colors.grey),
                ),
              ),
            ),
            SizedBox(height: 20),
            ButtonUtil.buildCommonButton(
                text: '确认',
                onPressed: () {
                  Get.back(result: _vm.controller.text);
                }),
            SizedBox(height: 20),
            Text('历史备注'),
            Expanded(
              child: ListView.builder(
                  itemCount: _vm.historyNoteList.length,
                  itemBuilder: (context, index) {
                    return _buildItem(_vm.historyNoteList[index]);
                  }),
            )
          ],
        ),
      ),
    );
  }

  Widget _buildItem(String address) {
    return Container(
      margin: const EdgeInsets.only(bottom: 10),
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: ColorsUtil.inputBgColor,
        borderRadius: BorderRadius.circular(4),
      ),
      child: Text(address),
    );
  }
}
