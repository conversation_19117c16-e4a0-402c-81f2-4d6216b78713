import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/feature/bill_flow/vm/cash_advance_vm.dart';
import 'package:gdjg_pure_flutter/init_module/init_route.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/button_util.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/dialog_util.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/toast_util.dart';
import 'package:get/get.dart';
import 'package:gdjg_pure_flutter/generated/assets.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';

class CashAdvanceView extends StatefulWidget {
  const CashAdvanceView({Key? key}) : super(key: key);

  @override
  State<CashAdvanceView> createState() => _CashAdvanceViewState();
}

class _CashAdvanceViewState extends State<CashAdvanceView>
    with AutomaticKeepAliveClientMixin {
  final TextEditingController _controller = TextEditingController();
  final CashAdvanceVM _vm = CashAdvanceVM();

  // 存储已上传图片的URL列表
  final RxList<String> _uploadedImageUrls = <String>[].obs;

  @override
  void initState() {
    super.initState();
    Get.put(_vm.us);
  }

  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context); // 必须调用以保持页面状态
    return Column(
      children: [
        Expanded(
          child: SingleChildScrollView(
            padding: EdgeInsets.all(16),
            controller: ScrollController(),
            child: Column(
              children: [
                Row(
                  children: [
                    Text(
                      '金额',
                      style: TextStyle(fontSize: 17),
                    ),
                    Expanded(
                      child: TextField(
                        controller: _controller,
                        inputFormatters: [
                          FilteringTextInputFormatter.allow(
                              RegExp(r'^\d*\.?\d{0,2}')),
                        ],
                        keyboardType: TextInputType.number,
                        style: TextStyle(
                            fontSize: 34.sp, color: ColorsUtil.black85),
                        textAlign: TextAlign.left,
                        obscureText: false,
                        cursorColor: ColorsUtil.primaryColor,
                        cursorRadius: const Radius.circular(2),
                        cursorOpacityAnimates: true,
                        maxLength: 10,
                        decoration: InputDecoration(
                          focusColor: ColorsUtil.primaryColor,
                          border: InputBorder.none,
                          hintText: '0.00',
                          hintStyle: TextStyle(
                              fontSize: 34.sp, color: ColorsUtil.black25),
                          contentPadding: const EdgeInsets.symmetric(
                              horizontal: 16, vertical: 10),
                          counterText: '',
                        ),
                      ),
                    ),
                  ],
                ),
                Divider(height: 1, color: Color(0xFFF5F5F5)),
                SizedBox(height: 16),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '照片：',
                      style: TextStyle(fontSize: 17),
                    ),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text.rich(
                          TextSpan(
                            children: [
                              TextSpan(
                                text: '上传',
                                style: TextStyle(fontSize: 12),
                              ),
                              TextSpan(
                                text: '【转账记录】',
                                style:
                                    TextStyle(fontSize: 12, color: Colors.blue),
                              ),
                              TextSpan(
                                text: '截图，方便以后查询转账情况',
                                style: TextStyle(fontSize: 12),
                              ),
                            ],
                          ),
                          textAlign: TextAlign.center,
                        ),
                        SizedBox(
                          height: 8,
                        ),
                        ImagePicker(
                          maxImages: 9,
                          onImagesUploaded: (urls) {
                            // 存储已上传图片的URL列表
                            _uploadedImageUrls.value = urls;
                          },
                        )
                      ],
                    ),
                  ],
                ),
                SizedBox(height: 16),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '备注：',
                      style: TextStyle(fontSize: 17),
                    ),
                    Expanded(
                      child: GestureDetector(
                        onTap: () {
                          YPRoute.openPage(RouteNameCollection.notes)
                              ?.then((res) {
                            if (res != null) {
                              _vm.baseVm.baseUs.setRemark(res as String);
                            }
                          });
                        },
                        child: Container(
                          height: 200,
                          padding:
                              EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                          decoration: BoxDecoration(
                            color: ColorsUtil.inputBgColor,
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Obx(() => Text(
                                _vm.baseVm.baseUs.remark == ''
                                    ? '请输入备注内容...'
                                    : _vm.baseVm.baseUs.remark,
                                style: TextStyle(
                                  color: _vm.baseVm.baseUs.remark == ''
                                      ? ColorsUtil.hintFontColor
                                      : ColorsUtil.black85,
                                ),
                              )),
                        ),
                      ),
                    )
                  ],
                ),
                SizedBox(height: 16),
              ],
            ),
          ),
        ),
        // 按钮
        Container(
          width: double.infinity,
          height: 94,
          color: Colors.white,
          padding: EdgeInsets.only(top: 10, left: 16, right: 16),
          child: GestureDetector(
            onTap: () {},
            child: Column(
              children: [
                ButtonUtil.buildCommonButton(text: '确认', onPressed: () {
                  _vm.onConfirmTap();
                }),
                Spacer()
              ],
            ),
          ),
        ),
      ],
    );
  }
}

class ImagePicker extends StatefulWidget {
  final int maxImages; // 最大图片数量
  final Function(List<String>) onImagesUploaded;

  const ImagePicker({
    Key? key,
    this.maxImages = 9, // 默认最多上传9张图片
    required this.onImagesUploaded,
  }) : super(key: key);

  @override
  State<ImagePicker> createState() => _ImagePickerState();
}

class _ImagePickerState extends State<ImagePicker> {
  // 存储已选择图片的本地路径列表，使用RxList实现响应式更新
  final RxList<String> _selectedImages = <String>[].obs;

  // 存储已上传图片的URL列表，用于传递给父组件
  final RxList<String> _uploadedImageUrls = <String>[].obs;

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      return Container(
        // color: Colors.green,
        constraints: BoxConstraints(
          maxHeight: 240,
          maxWidth: 240,
        ),
        child: GridView.builder(
            shrinkWrap: true,
            physics: NeverScrollableScrollPhysics(),
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 3),
            itemCount: _selectedImages.length + (_selectedImages.length < widget.maxImages ? 1 : 0),
            itemBuilder: (context, index) {
              // 如果是最后一个且未达最大数量，显示上传按钮
              if (index == _selectedImages.length && _selectedImages.length < widget.maxImages) {
                return _buildUploadButton();
              }
              // 否则显示图片项
              return _buildSelectedImageItem(_selectedImages[index], index);
            }),
      );
    });
  }

  Widget _buildSelectedImageItem(String imagePath, int index) {
    return Container(
      height: 78,
      width: 78,
      // color: Colors.red,
      child: Stack(
        children: [
          // 图片容器
          Positioned(
            top: 8,
            child: Container(
              height: 70,
              width: 70,
              decoration: BoxDecoration(
                image: DecorationImage(
                  image: FileImage(File(imagePath)), // 从文件加载图片
                  fit: BoxFit.cover, // 填充模式
                ),
              ),
            ),
          ),
          // 右上角删除按钮
          Positioned(
            top: 0,
            right: 0,
            child: GestureDetector(
              onTap: () {
                setState(() {
                  // 从列表中移除图片
                  _selectedImages.removeAt(index);
                  // 如果已上传，也从上传列表中移除
                  if (index < _uploadedImageUrls.length) {
                    _uploadedImageUrls.removeAt(index);
                  }
                  // 通知父组件图片URL已更新
                  widget.onImagesUploaded(_uploadedImageUrls);
                });
              },
              child: Image.asset(Assets.loginLoginnewIcDelete,
                  width: 16.w, height: 16.h),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建上传图片按钮
  Widget _buildUploadButton() {
    return Container(
      height: 78,
      width: 78,
      padding: EdgeInsets.only(top: 8, right: 8),
      child: GestureDetector(
        onTap: () {
          // 关闭键盘，避免遮挡
          FocusScope.of(context).unfocus();

          // 检查是否已达到最大图片数量限制
          if (_selectedImages.length >= widget.maxImages) {
            ToastUtil.showToast('最多只能上传${widget.maxImages}张图片');
            return;
          }

          DialogUtil.showUploadImageBottomSheet(context, onTap: (urls) {
            print('------------urls----------------');
            print(urls);
            _selectedImages.add(urls);
          });
        },
        child: Image(
          image: AssetImage(Assets.commonImageTakePhoto),
          height: 70,
          width: 70,
        ),
      ),
    );
  }
}
