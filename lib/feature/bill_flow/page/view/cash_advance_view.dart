import 'package:flutter/material.dart';
import 'package:gdjg_pure_flutter/feature/bill_flow/vm/cash_advance_vm.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/button_util.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/dialog_util.dart';
import 'package:get/get.dart';
import 'package:gdjg_pure_flutter/generated/assets.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';

class CashAdvanceView extends StatefulWidget {
  const CashAdvanceView({Key? key}) : super(key: key);

  @override
  State<CashAdvanceView> createState() => _CashAdvanceViewState();
}

class _CashAdvanceViewState extends State<CashAdvanceView> {
  final TextEditingController _controller = TextEditingController();
  final CashAdvanceVM _vm = CashAdvanceVM();

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    Get.put(_vm.us);
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Expanded(
          child: SingleChildScrollView(
            padding: EdgeInsets.all(16),
            controller: ScrollController(),
            child: Column(
              children: [
                Row(
                  children: [
                    Text(
                      '金额',
                      style: TextStyle(fontSize: 17),
                    ),
                    Expanded(
                      child: TextField(
                        controller: _controller,
                        style:
                            TextStyle(fontSize: 17, color: ColorsUtil.black85),
                        textAlign: TextAlign.left,
                        obscureText: false,
                        cursorColor: ColorsUtil.primaryColor,
                        cursorRadius: const Radius.circular(2),
                        cursorOpacityAnimates: true,
                        maxLength: 10,
                        decoration: InputDecoration(
                          focusColor: ColorsUtil.primaryColor,
                          border: InputBorder.none,
                          hintText: '0.00',
                          hintStyle: TextStyle(
                              fontSize: 16, color: ColorsUtil.black25),
                          contentPadding: const EdgeInsets.symmetric(
                              horizontal: 16, vertical: 10),
                          counterText: '',
                        ),
                      ),
                    ),
                  ],
                ),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '照片：',
                      style: TextStyle(fontSize: 17),
                    ),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '上传【转账记录】截图，方便以后查询转账情况',
                          style: TextStyle(fontSize: 12),
                        ),
                        SizedBox(
                          height: 8,
                        ),
                        GestureDetector(
                          onTap: () {
                            if (FocusScope.of(context).hasFocus) {
                              FocusScope.of(context).unfocus();
                              return;
                            }
                            DialogUtil.showUploadImageBottomSheet(
                                ['拍摄', '从相册选择'], (index) async {
                              // print(index);
                            });
                          },
                          child: Image(
                            image: AssetImage(Assets.commonImageTakePhoto),
                            height: 80,
                            width: 80,
                          ),
                        )
                      ],
                    ),
                  ],
                ),
                SizedBox(height: 16),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '备注：',
                      style: TextStyle(fontSize: 17),
                    ),
                    Expanded(
                      child: GestureDetector(
                        onTap: () {
                          if (FocusScope.of(context).hasFocus) {
                            FocusScope.of(context).unfocus();
                            return;
                          }
                          // Get.toNamed(Routes.note)?.then((res) {
                          //   if (res != null) {
                          //     _vm.us.currentNote.value = res;
                          //   }
                          // });
                        },
                        child: Container(
                          height: 200,
                          padding:
                              EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                          decoration: BoxDecoration(
                            color: ColorsUtil.inputBgColor,
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Obx(() => Text(
                                _vm.us.currentNote.value == ''
                                    ? '请输入备注内容...'
                                    : _vm.us.currentNote.value,
                                style: TextStyle(
                                  color: _vm.us.currentNote.value == ''
                                      ? ColorsUtil.hintFontColor
                                      : ColorsUtil.black85,
                                ),
                              )),
                        ),
                      ),
                    )
                  ],
                ),
                SizedBox(height: 16),
              ],
            ),
          ),
        ),
        // 按钮
        Container(
          width: double.infinity,
          height: 94,
          color: Colors.white,
          padding: EdgeInsets.only(top: 10,left: 16,right: 16),
          child: GestureDetector(
            onTap: () {},
            child: Column(
              children: [
                ButtonUtil.buildCommonButton(text: '确认', onPressed: () {}),
                Spacer()
              ],
            ),
          ),
        ),
      ],
    );
  }
}
