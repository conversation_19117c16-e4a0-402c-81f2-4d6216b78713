import 'package:flutter/material.dart';

import 'package:gdjg_pure_flutter/generated/assets.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/button_util.dart';

class StatisticSettleView extends StatefulWidget {
  const StatisticSettleView({Key? key}) : super(key: key);

  @override
  State<StatisticSettleView> createState() => _StatisticSettleView();
}

class _StatisticSettleView extends State<StatisticSettleView> {
  final TextEditingController _controller = TextEditingController();

  @override
  void dispose() {
    super.dispose();
    FocusScope.of(context).unfocus();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Expanded(
          child: SingleChildScrollView(
            padding: EdgeInsets.all(16),
            controller: ScrollController(),
            child: Column(
              children: [
                Row(
                  children: [
                    Text(
                      '金额',
                      style: TextStyle(fontSize: 17),
                    ),
                    Expanded(
                      child: <PERSON><PERSON><PERSON>(
                        controller: _controller,
                        style:
                            TextStyle(fontSize: 17, color: ColorsUtil.black85),
                        textAlign: TextAlign.left,
                        obscureText: false,
                        cursorColor: ColorsUtil.primaryColor,
                        cursorRadius: const Radius.circular(2),
                        cursorOpacityAnimates: true,
                        maxLength: 10,
                        decoration: InputDecoration(
                          focusColor: ColorsUtil.primaryColor,
                          border: InputBorder.none,
                          hintText: '0.00',
                          hintStyle: TextStyle(
                              fontSize: 16, color: ColorsUtil.black25),
                          contentPadding: const EdgeInsets.symmetric(
                              horizontal: 16, vertical: 10),
                          counterText: '',
                        ),
                      ),
                    ),
                  ],
                ),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '照片：',
                      style: TextStyle(fontSize: 17),
                    ),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '上传【转账记录】截图，方便以后查询转账情况',
                          style: TextStyle(fontSize: 12),
                        ),
                        SizedBox(
                          height: 8,
                        ),
                        Image(
                          image: AssetImage(Assets.commonImageTakePhoto),
                          height: 80,
                          width: 80,
                        )
                      ],
                    ),
                  ],
                ),
                SizedBox(height: 16),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '备注：',
                      style: TextStyle(fontSize: 17),
                    ),
                    Expanded(
                      child: Container(
                        padding:
                            EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                        decoration: BoxDecoration(
                          color: ColorsUtil.inputBgColor,
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Text(
                          '请输入备注内容...',
                          style: TextStyle(
                            color: ColorsUtil.hintFontColor,
                          ),
                        ),
                      ),
                    )
                  ],
                ),
                SizedBox(height: 16),
                Container(
                  height: 100,
                  width: double.infinity,
                  color: Colors.blue,
                ),
                Text("data"),
                Container(
                  height: 100,
                  width: double.infinity,
                  color: Colors.blue,
                ),
                Text("data"),
                Container(
                  height: 100,
                  width: double.infinity,
                  color: Colors.blue,
                ),
                Text("data"),
                Container(
                  height: 100,
                  width: double.infinity,
                  color: Colors.blue,
                ),
              ],
            ),
          ),
        ),
        // 按钮
        Container(
          width: double.infinity,
          height: 94,
          color: Colors.white,
          padding: EdgeInsets.only(top: 10,left: 16,right: 16),
          child: GestureDetector(
            onTap: () {},
            child: Column(
              children: [
                ButtonUtil.buildCommonButton(text: '确认', onPressed: () {}),
                Spacer()
              ],
            ),
          ),
        ),
      ],
    );
  }
}
