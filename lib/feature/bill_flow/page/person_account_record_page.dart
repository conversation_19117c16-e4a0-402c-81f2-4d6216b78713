import 'package:flutter/material.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:gdjg_pure_flutter/feature/bill_flow/page/view/cash_advance_view.dart';
import 'package:gdjg_pure_flutter/feature/bill_flow/page/view/statistic_settle_view.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/appbar_util.dart';
import 'package:get/get.dart';

void main() {
  runApp(GetMaterialApp(
    theme: ThemeData(primarySwatch: Colors.blue),
    home: PersonAccountRecordPage(),
    builder: FlutterSmartDialog.init(),
  ));
}

class PersonAccountRecordPage extends StatefulWidget {
  const PersonAccountRecordPage({Key? key}) : super(key: key);

  @override
  State<PersonAccountRecordPage> createState() => _PersonAccountRecordPage();
}

class _PersonAccountRecordPage extends State<PersonAccountRecordPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _tabController.addListener(() {
      setState(() {});
    });
  }

  @override
  void dispose() {
    _tabController.removeListener(() {});
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      appBar: AppBarUtil.buildCommonAppBar(title: '个人记账'),
      body: Container(
        color: Colors.grey[200],
        child: GestureDetector(
          onTap: () {
            FocusScope.of(context).unfocus();
          },
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                color: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                margin: const EdgeInsets.only(top: 8),
                child: Row(
                  children: [
                    const Text('日期：', style: TextStyle(fontSize: 16)),
                    Text('2021年12月3日',
                        style:
                            TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
                    Spacer(),
                    Icon(Icons.chevron_right, color: Colors.grey),
                  ],
                ),
              ),
              Divider(
                height: 1,
                color: Colors.grey[200],
              ),
              Container(
                color: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                margin: const EdgeInsets.only(bottom: 8),
                child: Row(
                  children: [
                    Text('项目：', style: TextStyle(fontSize: 16)),
                    Text('项目1',
                        style:
                            TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
                    Spacer(),
                    Icon(Icons.chevron_right, color: Colors.grey),
                  ],
                ),
              ),
              Flexible(
                child: Column(
                  children: [
                    Container(
                      color: Colors.white,
                      height: 44,
                      child: TabBar(
                          controller: _tabController,
                          labelColor: Color.fromRGBO(0, 0, 0, 0.85),
                          unselectedLabelColor: Color.fromRGBO(0, 0, 0, 0.65),
                          // labelStyle: const TextStyle(
                          //   fontSize: 22,
                          //   fontWeight: FontWeight.w500,
                          // ),
                          // unselectedLabelStyle: const TextStyle(fontSize: 16),
                          // indicator: BoxDecoration(
                          //   color: Colors.blue,
                          //   borderRadius: BorderRadius.circular(16),
                          // ),
                          // indicatorSize: TabBarIndicatorSize.tab,
                          tabs: [
                            Tab(
                              child: Text('借支'),
                            ),
                            Tab(
                              child: Text('结算'),
                            ),
                          ]),
                    ),
                    Flexible(
                      flex: 1,
                      child: Container(
                        color: Colors.white,
                        child: TabBarView(
                            controller: _tabController,
                            // physics: NeverScrollableScrollPhysics(),
                            children: [
                              CashAdvanceView(),
                              StatisticSettleView(),
                            ]),
                      ),
                    ),
                  ],
                ),
              ),
              // Tabs
              // Row(
              //   mainAxisAlignment: MainAxisAlignment.spaceAround,
              //   children: [
              //     Column(
              //       children: [
              //         Text('点工',
              //             style: TextStyle(
              //                 color: Colors.blue,
              //                 fontSize: 16,
              //                 fontWeight: FontWeight.bold)),
              //         Container(
              //             height: 2,
              //             width: 32,
              //             color: Colors.blue,
              //             margin: const EdgeInsets.only(top: 4)),
              //       ],
              //     ),
              //     const Text('包工',
              //         style: TextStyle(color: Colors.grey, fontSize: 16)),
              //     const Text('短工',
              //         style: TextStyle(color: Colors.grey, fontSize: 16)),
              //     const Text('工量',
              //         style: TextStyle(color: Colors.grey, fontSize: 16)),
              //   ],
              // ),
              // const SizedBox(height: 24),
              // // 金额
              // Row(
              //   mainAxisAlignment: MainAxisAlignment.spaceBetween,
              //   children: const [
              //     Text('金额：', style: TextStyle(fontSize: 16)),
              //     Text('0.00',
              //         style: TextStyle(fontSize: 40, color: Colors.grey)),
              //   ],
              // ),
              // const SizedBox(height: 24),
              // // 照片
              // Row(
              //   crossAxisAlignment: CrossAxisAlignment.center,
              //   children: [
              //     const Text('照片：', style: TextStyle(fontSize: 16)),
              //     const SizedBox(width: 8),
              //     Container(
              //       padding:
              //           const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
              //       decoration: BoxDecoration(
              //         color: Colors.blue[50],
              //         borderRadius: BorderRadius.circular(4),
              //       ),
              //       child: Row(
              //         children: const [
              //           Icon(Icons.play_circle_outline,
              //               color: Colors.blue, size: 16),
              //           SizedBox(width: 2),
              //           Text('律师提醒',
              //               style: TextStyle(color: Colors.blue, fontSize: 12)),
              //         ],
              //       ),
              //     ),
              //     const SizedBox(width: 8),
              //     const Text('上传工作照片,留证据,工资有保障',
              //         style: TextStyle(color: Colors.blue, fontSize: 13)),
              //   ],
              // ),
              // const SizedBox(height: 12),
              // Row(
              //   children: [
              //     Stack(
              //       children: [
              //         ClipRRect(
              //           borderRadius: BorderRadius.circular(8),
              //           child: Image.network(
              //             'https://images.unsplash.com/photo-1506744038136-46273834b3fb',
              //             width: 80,
              //             height: 80,
              //             fit: BoxFit.cover,
              //           ),
              //         ),
              //         Positioned(
              //           right: 0,
              //           top: 0,
              //           child: Container(
              //             decoration: BoxDecoration(
              //               color: Colors.black54,
              //               borderRadius: BorderRadius.circular(12),
              //             ),
              //             child: const Icon(Icons.close,
              //                 color: Colors.white, size: 18),
              //           ),
              //         ),
              //       ],
              //     ),
              //     const SizedBox(width: 16),
              //     Container(
              //       width: 80,
              //       height: 80,
              //       decoration: BoxDecoration(
              //         color: Colors.grey[200],
              //         borderRadius: BorderRadius.circular(8),
              //       ),
              //       child: const Icon(Icons.add_photo_alternate,
              //           size: 36, color: Colors.grey),
              //     ),
              //   ],
              // ),
              // const SizedBox(height: 24),
              // // 备注
              // Row(
              //   children: const [
              //     Text('备注：', style: TextStyle(fontSize: 16)),
              //   ],
              // ),
              // const SizedBox(height: 8),
              // TextField(
              //   decoration: InputDecoration(
              //     hintText: '请输入备注内容…',
              //     filled: true,
              //     fillColor: Colors.grey[100],
              //     border: OutlineInputBorder(
              //       borderRadius: BorderRadius.all(Radius.circular(8)),
              //       borderSide: BorderSide.none,
              //     ),
              //     contentPadding:
              //         EdgeInsets.symmetric(vertical: 16, horizontal: 12),
              //   ),
              //   maxLines: 3,
              // ),
              // const SizedBox(height: 32),
              // // 确认修改按钮
              // SizedBox(
              //   width: double.infinity,
              //   height: 56,
              //   child: ElevatedButton(
              //     style: ElevatedButton.styleFrom(
              //       backgroundColor: Colors.blue[500],
              //       shape: RoundedRectangleBorder(
              //         borderRadius: BorderRadius.circular(8),
              //       ),
              //     ),
              //     onPressed: () {},
              //     child: const Text('确认修改', style: TextStyle(fontSize: 22)),
              //   ),
              // ),
            ],
          ),
        ),
      ),
      backgroundColor: const Color(0xFFF6F6F6),
    );
  }
}
