import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/feature/bill_flow/page/view/cash_advance_view.dart';
import 'package:gdjg_pure_flutter/feature/bill_flow/vm/worker_account_record_viewmodel.dart';
import 'package:gdjg_pure_flutter/utils/route_util/base_page/base_ful_page.dart';
import 'package:get/get.dart';

class WorkerAccountRecordPage extends BaseFulPage {
  const WorkerAccountRecordPage({Key? key})
      : super(key: key, appBar: const YPAppBar(title: "个人记账"));

  @override
  _PersonAccountRecordPage createState() => _PersonAccountRecordPage();
}

class _PersonAccountRecordPage extends BaseFulPageState
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final WorkerAccountRecordViewmodel viewModel = WorkerAccountRecordViewmodel();

  @override
  void onPageRoute(Object? routeParams, bool fromLaunchTask) {
    super.onPageRoute(routeParams, fromLaunchTask);
    // var workNoteId = routeParams as String;
    var workNoteId = '781274';
    // 初始化数据
    viewModel.initData(workNoteId);
  }

  @override
  void onPageCreate() {
    _tabController = TabController(length: 2, vsync: this);
    _tabController.addListener(() {
      setState(() {});
    });
  }

  @override
  void onPageDestroy() {
    _tabController.removeListener(() {});
    _tabController.dispose();
  }

  @override
  Widget yBuild(BuildContext context) {
    return Obx(() => Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              color: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              margin: const EdgeInsets.only(top: 8),
              child: Row(
                children: [
                  Text('日期：',
                      style: TextStyle(fontSize: 16.sp, color: Colors.black)),
                  Text('2021年12月3日',
                      style: TextStyle(
                          fontSize: 17.sp,
                          fontWeight: FontWeight.w500,
                          color: Colors.black)),
                  Spacer(),
                  Icon(Icons.chevron_right, color: Colors.grey),
                ],
              ),
            ),
            Divider(
              height: 1,
              color: Colors.grey[200],
            ),
            Container(
              color: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              margin: const EdgeInsets.only(bottom: 8),
              child: Row(
                children: [
                  Text('项目：',
                      style: TextStyle(fontSize: 16.sp, color: Colors.black)),
                  Text(viewModel.uiState.projectName.value,
                      style: TextStyle(
                          fontSize: 17.sp,
                          fontWeight: FontWeight.w500,
                          color: Colors.black)),
                  Spacer(),
                  Icon(Icons.chevron_right, color: Colors.grey),
                ],
              ),
            ),
            Flexible(
              child: Column(
                children: [
                  Container(
                    color: Colors.white,
                    height: 44,
                    child: TabBar(
                        dividerHeight: 0,
                        controller: _tabController,
                        labelColor: Color.fromRGBO(0, 0, 0, 0.85),
                        unselectedLabelColor: Color.fromRGBO(0, 0, 0, 0.65),
                        labelStyle: TextStyle(
                          fontSize: 16.sp,
                          fontWeight: FontWeight.w600,
                        ),
                        unselectedLabelStyle: TextStyle(
                          fontSize: 16.sp,
                          fontWeight: FontWeight.w400,
                        ),
                        indicatorColor: const Color(0xFF5290FD),
                        indicatorSize: TabBarIndicatorSize.label,
                        tabs: [
                          Tab(
                            child: Text('借支'),
                          ),
                          Tab(
                            child: Text('结算'),
                          ),
                        ]),
                  ),
                  Flexible(
                    flex: 1,
                    child: Container(
                      color: Colors.white,
                      child: TabBarView(
                          controller: _tabController,
                          // physics: NeverScrollableScrollPhysics(),
                          children: [
                            CashAdvanceView(),
                            CashAdvanceView(),
                            // StatisticSettleView(),
                          ]),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ));
  }
}
