import 'package:get/get.dart';

class CashAdvanceUs extends GetxController{
  var currentNote = "".obs;
  var money = "".obs;
  /// 照片URL列表
  final _photoUrls = <String>[].obs;

  get photoUrls => _photoUrls.value;

  setPhotoUrls(List<String> urls) {
    _photoUrls.value = urls;
    _photoUrls.refresh();
  }

  void addPhoto(String photoUrl) {
    _photoUrls.add(photoUrl);
  }

  void removePhoto(int index) {
    if (index >= 0 && index < _photoUrls.length) {
      _photoUrls.removeAt(index);
    }
  }
}