import 'package:flutter/material.dart';
import 'package:gdjg_pure_flutter/feature/cloud_album/export_record/vm/export_record_viewmodel.dart';
import 'package:gdjg_pure_flutter/generated/assets.dart';
import 'package:gdjg_pure_flutter/utils/route_util/base_page/base_ful_page.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/toast_util.dart';
import 'package:get/get.dart';

class ExportRecordPage extends BaseFulPage {
  const ExportRecordPage({super.key})
      : super(appBar: const YPAppBar(title: '导出打卡记录'));

  @override
  _ExportRecordPageState createState() => _ExportRecordPageState();
}

class _ExportRecordPageState extends BaseFulPageState {

  final ExportRecordViewmodel vm = ExportRecordViewmodel();

  @override
  void onPageCreate() {
    vm.initData();
  }

  @override
  Widget yBuild(BuildContext context) {
    return SingleChildScrollView(
      child: Obx(() => Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 报表类型标题
              _buildSectionTitle("报表类型"),

              // 每月统计区块
              _buildReportTypeSection(),

              // 导出项目/工人标题
              _buildSectionTitle("导出项目/工人"),

              // 项目选择行
              _buildSelectableRow(
                title: '项目',
                content: vm.projectName.value,
                onTap: () {},
              ),

              // 分割线
              _buildDivider(),

              // 工人选择行
              _buildSelectableRow(
                title: '工人',
                content: vm.workerName.value,
                onTap: () {},
              ),

              // 导出时间范围标题
              _buildSectionTitle("导出时间范围"),

              // 开始时间行
              _buildSelectableRow(
                title: "开始时间",
                content: vm.startDate.value,
                onTap: () {},
              ),

              // 分割线
              _buildDivider(),

              // 结束时间行
              _buildSelectableRow(
                title: "结束时间",
                content: vm.endDate.value,
                onTap: () {},
              ),

              // 分割线
              _buildDivider(),

              // 导出按钮
              Padding(
                padding: const EdgeInsets.symmetric(
                    horizontal: 16.0, vertical: 37.0),
                child: ElevatedButton(
                  onPressed: _exportReport,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF5290FD),
                    minimumSize: const Size(double.infinity, 44),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ),
                  child: const Text(
                    "导出报表",
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                    ),
                  ),
                ),
              ),
            ],
          )),
    );
  }

  // 构建分区标题
  Widget _buildSectionTitle(String title) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 9),
      color: const Color(0xFFF7F7F7),
      child: Text(
        title,
        style: const TextStyle(
          color: Color(0xFF8A8A99),
          fontSize: 14,
        ),
      ),
    );
  }

  // 构建报表类型区块
  Widget _buildReportTypeSection() {
    return Container(
      width: double.infinity,
      color: Colors.white,
      padding: const EdgeInsets.symmetric(vertical: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Padding(
            padding: EdgeInsets.symmetric(horizontal: 16),
            child: Text(
              "每月统计",
              style: TextStyle(
                color: Color(0xFF323233),
                fontSize: 17,
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.only(left: 16, right: 16, top: 4),
            child: Text(
              "打卡天数、打卡时间、打卡明细等",
              style: TextStyle(
                color: Colors.grey[400],
                fontSize: 14,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // 构建可选择的行
  Widget _buildSelectableRow({
    required String title,
    required String content,
    required VoidCallback onTap,
  }) {
    return Container(
      color: Colors.white,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
      child: Row(
        children: [
          Text(
            title,
            style: const TextStyle(
              color: Color(0xFF323233),
              fontSize: 17,
            ),
          ),
          Spacer(),
          GestureDetector(
            onTap: onTap,
            child: Text(
              content,
              style: const TextStyle(
                color: Color(0xFF323233),
                fontSize: 17,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          Image.asset(
            Assets.commonIconArrowRightGrey,
            width: 17,
            height: 17,
            fit: BoxFit.fitWidth,
          ),
        ],
      ),
    );
  }

  // 构建分割线
  Widget _buildDivider() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      height: 1,
      color: const Color(0xFFF5F5F5),
    );
  }

  void _exportReport() {
    // 实现导出逻辑
    ToastUtil.showToast("正在生成报表...");
    // 这里添加实际的导出操作
    vm.getExportRecordLink();
  }
}
