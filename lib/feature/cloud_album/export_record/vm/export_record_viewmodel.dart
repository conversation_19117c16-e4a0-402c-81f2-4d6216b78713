import 'package:gdjg_pure_flutter/data/export_data/ds/model/param/attendance_download_excel_param_model.dart';
import 'package:gdjg_pure_flutter/data/export_data/repo/biz/album_filter_biz_model.dart';
import 'package:gdjg_pure_flutter/data/export_data/repo/biz/attendance_download_excel_biz_model.dart';
import 'package:gdjg_pure_flutter/data/export_data/repo/export_repo.dart';
import 'package:gdjg_pure_flutter/utils/system_util/yprint.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/toast_util.dart';
import 'package:gdjg_pure_flutter/utils/wechat_util/wechat_util.dart';
import 'package:get/get.dart';

class ExportRecordViewmodel{
  var projectName = ''.obs;
  var workerName = '全部工友'.obs;
  var startDate = '2024年12月31日'.obs;
  var endDate = '2025年12月31日'.obs;
  var exportType = ''.obs;

  final _exportRepo = ExportRepo();

  initData() {
    getExportRecordInfo().then((res) {
      if (res != null) {
        projectName.value = res.workerNotesList[0].name;
      }
    });
  }

  // 查询
  Future<AlbumFilterBizModel?> getExportRecordInfo() async {
    final result = await _exportRepo.fetchWorkerAndWorker();
    if (result.isOK()) {
      return result.getSucData();
    } else {
      yprint(result.fail?.errorMsg ?? '');
      yprint('----------获取打卡信息失败-----------');
      return null;
    }
  }

  getExportRecordLink() {
    _getExportRecordLink().then((res) {
      if (res != null) {
        yprint('----------获取下载信息-----------');
        yprint('${res.url}');
        // 跳转到微信分享
        onShareToWechatTap(res.url);
      }
    });
  }

  /// 分享到微信
  /// [contentKey] RepaintBoundary的GlobalKey
  Future<void> onShareToWechatTap(String url) async {
    try {
      // 使用微信工具类分享图片
      bool success = await WeChatUtil.shareFile(filePath: url);
      if (!success) {
        ToastUtil.showToast('分享失败22');
      }
    } catch (e) {
      yprint('分享到微信失败: $e');
    }
  }

  Future<AttendanceDownloadExcelBizModel?> _getExportRecordLink() async {
    var params = AttendanceDownloadExcelParamModel(
      start_date: '2025-07-01',
      end_date: '2025-07-19',
      work_note: '781356',
      type: '2',
      return_url: '1'
    );
    final result = await _exportRepo.fetchReportLink(params);
    if (result.isOK()) {
      return result.getSucData();
    }
    return null;
  }
}