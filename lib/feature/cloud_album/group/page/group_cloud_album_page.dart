import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/feature/cloud_album/group/view/attendance_photo_view.dart';
import 'package:gdjg_pure_flutter/feature/cloud_album/group/view/record_work_photo_view.dart';
import 'package:gdjg_pure_flutter/feature/cloud_album/group/vm/group_cloud_album_vm.dart';
import 'package:gdjg_pure_flutter/generated/assets.dart';
import 'package:gdjg_pure_flutter/utils/route_util/base_page/base_ful_page.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/toast_util.dart';
import 'package:gdjg_pure_flutter/widget/combined_filter_widget.dart';
import 'package:get/get.dart';

class GroupCloudAlbumPage extends BaseFulPage {
  const GroupCloudAlbumPage({super.key})
      : super(
            appBar: const YPAppBar(
                title: '班组云相册',
                rightResText: '个人云相册',
                rightResIcon: 'assets/images/common/ic_take_phone_small.webp'));

  @override
  State createState() => _GroupCloudAlbumPageState();
}

class _GroupCloudAlbumPageState extends BaseFulPageState {
  final GroupCloudAlbumVM _vm = GroupCloudAlbumVM();

  @override
  void onPageCreate() {
    _vm.initData();
  }

  @override
  Widget yBuild(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(top: 8),
      color: ColorsUtil.inputBgColor,
      child: Obx(() => Column(
            children: [
              Container(
                color: Colors.white,
                child: Column(
                  children: [
                    CombinedFilterWidget(
                      showProjectFilter: true,
                      showTypeFilter: false,
                    ),
                    // 统计卡片
                    Container(
                      // color: Colors.green,
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      margin: const EdgeInsets.only(bottom: 8),
                      child: Column(
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              _StatCard(
                                icon: Icons.calendar_today,
                                label: _vm.albumStatUiState.statList[0].name,
                                count: 30,
                                index: 1,
                                vm: _vm,
                              ),
                              _StatCard(
                                icon: Icons.assignment,
                                label: _vm.albumStatUiState.statList[1].name,
                                count: 30,
                                index: 2,
                                vm: _vm,
                              ),
                              _StatCard(
                                icon: Icons.engineering,
                                label: _vm.albumStatUiState.statList[2].name,
                                count: 30,
                                index: 3,
                                vm: _vm,
                              ),
                              _StatCard(
                                icon: Icons.engineering,
                                label: _vm.albumStatUiState.statList[3].name,
                                count: 30,
                                index: 4,
                                vm: _vm,
                              ),
                            ],
                          ),
                          if(_vm.currentCardIndex.value == 4)
                          Container(
                            padding: EdgeInsets.only(top: 10),
                            child: Row(
                              children: [
                                Image.asset(
                                  LoginAssets.loginIcPopSmile,
                                  width: 16.w,
                                  height: 16.w,
                                ),
                                Text(
                                  '工友在项目中拍摄的每张照片，都会更新他的打卡记录',
                                  style: TextStyle(
                                      color: ColorsUtil.primaryColor,
                                      fontSize: 12.sp),
                                )
                              ],
                            ),
                          )
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              // 照片分组列表
              if (_vm.currentCardIndex.value == 1 ||
                  _vm.currentCardIndex.value == 2)
                RecordWorkPhotoView(
                  uiState: _vm.albumListUiState,
                ),
              if (_vm.currentCardIndex.value == 3)
                RecordWorkPhotoView(
                  uiState: _vm.albumListUiState,
                  isShowUpload: true,
                ),
              if (_vm.currentCardIndex.value == 4)
                AttendancePhotoView(
                  uiState: _vm.albumClockListUiState,
                ),
            ],
          )),
    );
  }
}

class _DateSelector extends StatelessWidget {
  final String label;
  final GroupCloudAlbumVM vm;

  const _DateSelector({required this.label, required this.vm});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        ToastUtil.showToast('点击了日期选择');
      },
      child: Row(
        children: [
          Text(label, style: TextStyle(fontSize: 15.sp)),
          const Icon(Icons.arrow_drop_down, size: 20),
        ],
      ),
    );
  }
}

class _StatCard extends StatelessWidget {
  final IconData icon;
  final String label;
  final int count;
  final int index;
  final GroupCloudAlbumVM vm;

  const _StatCard({
    required this.icon,
    required this.label,
    required this.count,
    required this.index,
    required this.vm,
  });

  @override
  Widget build(BuildContext context) {
    Get.put(vm);
    return Obx(() => GestureDetector(
          onTap: () {
            vm.currentCardIndex.value = index;
            vm.fetchData();
          },
          child: Container(
            width: 78,
            padding: const EdgeInsets.only(top: 8, left: 8, right: 8),
            decoration: BoxDecoration(
              border: Border.all(
                  color: vm.currentCardIndex.value == index
                      ? ColorsUtil.primaryColor
                      : ColorsUtil.inputBgColor,
                  width: 1),
              borderRadius: BorderRadius.circular(4),
              color: vm.currentCardIndex.value == index
                  ? ColorsUtil.primaryColor15
                  : Colors.white,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(icon, color: ColorsUtil.primaryColor, size: 10),
                    const SizedBox(height: 6),
                    Text(label,
                        style: TextStyle(
                            fontSize: 12.sp, fontWeight: FontWeight.w500)),
                  ],
                ),
                const SizedBox(height: 2),
                Text('照片数量：',
                    style: TextStyle(fontSize: 12.sp, color: Colors.grey)),
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    Container(
                      alignment: Alignment.centerRight,
                      width: 45,
                      child: Text(
                        '300',
                        style: TextStyle(
                          fontSize: 20.sp,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    Text('张',
                        style: TextStyle(
                          fontSize: 14.sp,
                        )),
                  ],
                ),
              ],
            ),
          ),
        ));
  }
}

class _PhotoGroupSection extends StatelessWidget {
  final String date;
  final int count;
  final List<String> imageUrls;

  const _PhotoGroupSection(
      {required this.date, required this.count, required this.imageUrls});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          width: double.infinity,
          color: Colors.white,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
          child: Text('$date /${count}张照片',
              style: TextStyle(fontSize: 16.sp, fontWeight: FontWeight.bold)),
        ),
        Container(
          color: Colors.white,
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          child: GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 3,
              crossAxisSpacing: 8,
              mainAxisSpacing: 8,
              childAspectRatio: 1,
            ),
            itemCount: imageUrls.length,
            itemBuilder: (context, index) {
              return ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: Image.network(
                  imageUrls[index],
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) => Container(
                    color: Colors.grey[200],
                    child: const Icon(Icons.broken_image,
                        size: 40, color: Colors.grey),
                  ),
                ),
              );
            },
          ),
        ),
        const SizedBox(height: 8),
      ],
    );
  }
}
