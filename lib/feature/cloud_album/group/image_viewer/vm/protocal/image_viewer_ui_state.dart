import 'package:get/get.dart';

class ImageViewerUIState {
  final _album = <String>[].obs;
  final _workNoteName = ''.obs;
  final _typeName = ''.obs;
  final _currentIndex = 0.obs;
  final _buttonName = ''.obs;


  int get currentIndex => _currentIndex.value;
  List<String> get album => _album;
  String get workNoteName => _workNoteName.value;
  String get typeName => _typeName.value;
  String get buttonName => _buttonName.value;

  setWorkNoteName(String name) {
    _workNoteName.value = name;
    _workNoteName.refresh();
  }

  setTypeName(String name) {
    _typeName.value = name;
  }

  setAlbum(List<String> list) {
    _album.value = list;
    _album.refresh();
  }

  setCurrentIndex(int index) {
    _currentIndex.value = index;
  }

  setButtonName(String name) {
    _buttonName.value = name;
  }

}