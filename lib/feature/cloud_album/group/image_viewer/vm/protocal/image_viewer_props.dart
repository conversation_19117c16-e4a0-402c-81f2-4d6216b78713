import 'package:gdjg_pure_flutter/data/group_data/group_cloud_album/repo/model/album_net_model_biz_model.dart';

/// @date 2025/07/03
/// @description ImageViewer页入参
class ImageViewerProps {
  ///
  String workNoteId;

  ///
  String workNoteName;

  ///
  String typeName;

  ///
  List<String> album;

  String buttonName;

  ///
  int index;

  ImageViewerProps({
    this.workNoteId = '',
    this.workNoteName = '',
    this.typeName = '',
    this.album = const [],
    this.index = 0,
    this.buttonName = '',
  });
}
