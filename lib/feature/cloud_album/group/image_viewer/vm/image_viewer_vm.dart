import 'dart:typed_data';

import 'package:dio/dio.dart';
import 'package:flutter/cupertino.dart';
import 'package:gdjg_pure_flutter/feature/cloud_album/group/image_viewer/vm/protocal/image_viewer_props.dart';
import 'package:gdjg_pure_flutter/feature/cloud_album/group/image_viewer/vm/protocal/image_viewer_ui_state.dart';
import 'package:gdjg_pure_flutter/utils/system_util/yprint.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/photo_picker_util.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/toast_util.dart';

class ImageViewerViewModel {
  ImageViewerProps? _props;
  var us = ImageViewerUIState();

  init(ImageViewerProps? props) {
    _props = props;
    us.setAlbum(_props?.album ?? []);
    us.setWorkNoteName(_props?.workNoteName ?? '');
    us.setTypeName(_props?.typeName ?? '');
    us.setCurrentIndex(_props?.index ?? 0);
    us.setButtonName(_props?.buttonName ?? '');
  }

  /// 保存图片到本地相册
  /// [imageBytes] 截图的字节数据，由UI层传入
  Future<void> saveImageToLocal() async {

    Uint8List? imageData = await _fetchImageAsUint8List(us.album[us.currentIndex]);

    if (imageData == null) {
      ToastUtil.showToast('图片加载失败');
      return;
    }

    try {
      // 检查存储权限
      bool hasPermission = await PhotoPickerUtil.checkStoragePermission();
      if (!hasPermission) {
        ToastUtil.showToast('需要存储权限才能保存图片');
        return;
      }
      // 保存到相册
      bool success = await PhotoPickerUtil.saveImageToGallery(
        imageData,
        fileName: '班组流水统计_${DateTime.now().millisecondsSinceEpoch}.png',
      );
      if (success) {
        ToastUtil.showToast('图片保存成功！');
      }
    } catch (e) {
      yprint('保存图片失败: $e');
    }
  }

  Future<Uint8List?> _fetchImageAsUint8List(String? imageUrl) async {
    try {
      if (imageUrl == null) {
        return null;
      }
      final dio = Dio();
      final response = await dio.get<Uint8List>(
        imageUrl,
        options: Options(responseType: ResponseType.bytes),
      );
      if (response.statusCode == 200) {
        return response.data;
      } else {
        debugPrint('Failed to load image: ${response.statusCode}');
        return null;
      }
    } catch (e) {
      debugPrint('Error fetching image: $e');
      return null;
    }
  }

  // 分享到微信
  Future<void> onShareToWechatTap() async {

  }
}