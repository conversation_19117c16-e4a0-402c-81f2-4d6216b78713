import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/feature/cloud_album/group/image_viewer/vm/image_viewer_vm.dart';
import 'package:gdjg_pure_flutter/feature/cloud_album/group/image_viewer/vm/protocal/image_viewer_props.dart';
import 'package:gdjg_pure_flutter/generated/assets.dart';
import 'package:gdjg_pure_flutter/utils/route_util/base_page/base_ful_page.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:get/get.dart';

class ImageViewerPage extends BaseFulPage {
  const ImageViewerPage({super.key}) : super(appBar: null);

  @override
  State createState() => _ImageViewerPageState();
}

class _ImageViewerPageState extends BaseFulPageState {
  final _viewModel = ImageViewerViewModel();

  @override
  void onPageRoute(Object? routeParams, bool fromLaunchTask) {
    var props = routeParams as ImageViewerProps;
    _viewModel.init(props);
  }

  @override
  void onPageCreate() {
    super.onPageCreate();
    _currentIndex = _viewModel.us.currentIndex;
    _pageController = PageController(initialPage: _currentIndex);
  }

  @override
  Widget yBuild(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.white,
        leading: GestureDetector(
            onTap: () => YPRoute.closePage(),
            child: Container(
              padding: const EdgeInsets.only(left: 8.0),
              child: Image(
                image: AssetImage(Assets.commonIconArrowBack),
              ),
            )),
        actions: [_buildMenuButton(), SizedBox(width: 16)],
        leadingWidth: 38,
        titleSpacing: 08,
        title: Obx(() => Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text(_viewModel.us.workNoteName,
                    style: const TextStyle(
                        color: Colors.black,
                        fontSize: 22,
                        fontWeight: FontWeight.w600)),
                Text('(${_viewModel.us.typeName})',
                    style: const TextStyle(
                        color: Colors.black,
                        fontSize: 14,
                        fontWeight: FontWeight.w400)),
              ],
            )),
        centerTitle: false,
      ),
      body: Obx(() => Column(
            children: [
              _buildContent(),
              _buildBottomBar(),
            ],
          )),
    );
  }

  late PageController _pageController;
  late int _currentIndex;

  Widget _buildContent() {
    return Expanded(
      child: Container(
        color: Colors.white,
        child: PageView.builder(
          controller: _pageController,
          itemCount: _viewModel.us.album.length,
          onPageChanged: (index) {
            setState(() {
              _currentIndex = index;
            });
          },
          itemBuilder: (context, index) {
            return GestureDetector(
              onLongPress: () {
                // 长按图片显示保存操作
                // SaveImageUtils.showSaveDialog(context, widget.imageList[index]);
              },
              child: Align(
                alignment: Alignment.center, // 图片居中
                child: SingleChildScrollView(
                  scrollDirection: Axis.vertical,
                  child: GestureDetector(
                    onTap: () {}, // 点击图片不做处理
                    child: Image.network(
                      _viewModel.us.album[index],
                      fit: BoxFit.fitWidth,
                      width: double.infinity,
                      loadingBuilder: (context, child, loadingProgress) {
                        if (loadingProgress == null) {
                          return child;
                        } else {
                          return Container(
                              color: Colors.black,
                              child: Center(
                                  child: CircularProgressIndicator(
                                value: loadingProgress.expectedTotalBytes !=
                                        null
                                    ? loadingProgress.cumulativeBytesLoaded /
                                        loadingProgress.expectedTotalBytes!
                                    : null,
                                color: Colors.white,
                              )));
                        }
                      },
                      errorBuilder: (context, error, stackTrace) {
                        return Container(
                          color: Colors.black,
                          child: Center(
                            child: Text(
                              '加载失败',
                              style: TextStyle(color: Colors.white),
                            ),
                          ),
                        );
                      },
                      // height: screenHeight - appBarHeight, // 计算剩余的高度
                    ),
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildBottomBar() {
    return Container(
      height: _viewModel.us.buttonName.isNotEmpty ? 178 : 90,
      // color: Colors.yellow,
      child: Column(
        children: [
          if (_viewModel.us.buttonName.isNotEmpty)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 22),
              color: Colors.white,
              child: SizedBox(
                height: 44,
                width: double.infinity,
                child: ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF5290FD),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(4),
                    ),
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                  onPressed: () {
                    // Handle look record button press
                  },
                  child: Text(
                    _viewModel.us.buttonName,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                    ),
                  ),
                ),
              ),
            ),
          Divider(height: 1, color: Color(0xFFF5F5F5)),
          Expanded(
            child: Container(
              color: Colors.white,
              child: Stack(
                children: [
                  Center(
                    child: Text(
                      '分享：',
                      style: TextStyle(
                        color: Color(0xFF323232),
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  Container(
                    margin: EdgeInsets.only(top: 22),
                    child: Row(
                      children: [
                        Spacer(),
                        _buildFunctionButton(
                          '朋友圈',
                          Assets.commonIcWatermarkWechatCircle,
                          () {},
                        ),
                        SizedBox(width: 30),
                        _buildFunctionButton(
                          '微信',
                          Assets.commonWtIconWechat,
                          () {},
                        ),
                        SizedBox(width: 38),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFunctionButton(
      String name, String imageUrl, VoidCallback onPressed) {
    return GestureDetector(
      onTap: onPressed,
      child: Column(
        children: [
          Image.asset(
            imageUrl,
            width: 24.w,
            height: 24.w,
          ),
          SizedBox(height: 4.h),
          Text(
            name,
            style: TextStyle(
              fontSize: 14.sp,
              color: Color(0xFF666666),
            ),
          ),
        ],
      ),
    );
  }

  PopupMenuButton _buildMenuButton() {
    return PopupMenuButton(
      child: Icon(Icons.more_horiz_rounded),
      offset: Offset(0, 40),
      color: Colors.white,
      itemBuilder: (BuildContext context) {
        return [
          PopupMenuItem(
            padding: EdgeInsets.only(left: 21),
            height: 0,
            child: Text("保存到本地"),
            onTap: () {
              _viewModel.saveImageToLocal();
            },
          ),
        ];
      },
    );
  }
}
