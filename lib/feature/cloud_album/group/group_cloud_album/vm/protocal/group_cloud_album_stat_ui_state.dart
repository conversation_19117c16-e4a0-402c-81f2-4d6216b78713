import 'package:gdjg_pure_flutter/data/group_data/group_cloud_album/repo/model/album_stat_biz_model.dart';
import 'package:get/get.dart';

class GroupCloudAlbumUiState {
  final id = 0.obs;
  final statCount = 0.obs;
  final statTitle = ''.obs;
  final statIcon = ''.obs;

  final startTime = '2020-01-01'.obs;
  final endTime = ''.obs;

  final workNotes = '781356'.obs;
  final workIds = ''.obs;
  final wechatToken = 'rn'.obs;

  var statList = <AlbumStatABizModel>[].obs;
}