import 'package:gdjg_pure_flutter/data/group_data/group_cloud_album/repo/model/album_net_model_biz_model.dart';
import 'package:get/get.dart';

class GroupCloudAlbumListUiState {
  var cardList = AlbumNetModelABizModel().obs;
}

class GroupCloudAlbumListCardEntity {
  String? date;
  int? num;
  List<String> urls = [];

  GroupCloudAlbumListCardEntity({
    required this.date,
    required this.num,
    required this.urls,
  });
}