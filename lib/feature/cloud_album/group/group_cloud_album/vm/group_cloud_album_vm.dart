import 'package:gdjg_pure_flutter/data/group_data/group_cloud_album/ds/model/param/album_attendance_list_param_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_cloud_album/ds/model/param/album_net_model_param_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_cloud_album/ds/model/param/album_stat_param_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_cloud_album/repo/group_cloud_album_repo.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_cloud_album/repo/model/album_net_model_biz_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_cloud_album/repo/model/album_stat_biz_model.dart';
import 'package:gdjg_pure_flutter/feature/cloud_album/group/group_cloud_album/vm/protocal/clock_service_list_ui_state.dart';
import 'package:gdjg_pure_flutter/feature/cloud_album/group/group_cloud_album/vm/protocal/group_cloud_album_list_ui_state.dart';
import 'package:gdjg_pure_flutter/feature/cloud_album/group/group_cloud_album/vm/protocal/group_cloud_album_stat_ui_state.dart';
import 'package:gdjg_pure_flutter/utils/system_util/yprint.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/toast_util.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

class GroupCloudAlbumVM extends GetxController {
  var currentCardIndex = 0.obs;
  final _groupCloudAlbumRepo = GroupCloudAlbumRepo();

  final albumStatUiState = GroupCloudAlbumUiState();
  final albumListUiState = GroupCloudAlbumListUiState();
  final albumClockListUiState = ClockServiceListUiState();

  /// 获取班组云相册统计数据
  Future<AlbumStatBizModel?> getAlbumStatData(
      AlbumStatParamModel params) async {
    final result = await _groupCloudAlbumRepo.getGroupStatData(params);
    if (result.isOK()) {
      return result.getSucData();
    } else {
      yprint(result.fail?.errorMsg ?? '');
      yprint('----------获取相册统计数据失败-----------');
      return null;
    }
  }

  /// 获取当前日期
  String getCurrentData() {
    final now = DateTime.now();
    final formatter = DateFormat('yyyy-MM-dd');
    return formatter.format(now);
  }

  /// 初始化数据
  initData() {
    albumStatUiState.endTime.value = getCurrentData();
    var statParams = AlbumStatParamModel(
      start_date: albumStatUiState.startTime.value,
      end_date: albumStatUiState.endTime.value,
      work_notes: albumStatUiState.workNotes.value,
      worker_ids: albumStatUiState.workIds.value,
      wechat_token: albumStatUiState.wechatToken.value,
    );
    getAlbumStatData(statParams).then((res) {
      if (res != null) {
        albumStatUiState.statList.value = res.data;

        // 请求相册列表数据
        fetchAlbumListData().then((res) {
          if (res != null) {
            albumListUiState.cardList.value = res;
          }
        });
      } else {
        // 模拟数据
        // albumStatUiState.statList.value = [
        //   AlbumStatABizModel(
        //       key: '1',
        //       name: '记工照片',
        //       icon: '1',
        //       iconChosen: '1',
        //       num: 1,
        //       isUpload: 0,
        //       isPublic: 0,
        //       id: 1,
        //       tags: ['1', '2', '3']),
        //   AlbumStatABizModel(
        //       key: '1',
        //       name: '记账照片',
        //       icon: '1',
        //       iconChosen: '1',
        //       num: 1,
        //       isUpload: 0,
        //       isPublic: 0,
        //       id: 1,
        //       tags: ['1', '2', '3']),
        //   AlbumStatABizModel(
        //       key: '1',
        //       name: '工程照片',
        //       icon: '1',
        //       iconChosen: '1',
        //       num: 1,
        //       isUpload: 0,
        //       isPublic: 0,
        //       id: 1,
        //       tags: ['1', '2', '3']),
        //   AlbumStatABizModel(
        //       key: '1',
        //       name: '打卡照片',
        //       icon: '1',
        //       iconChosen: '1',
        //       num: 1,
        //       isUpload: 0,
        //       isPublic: 0,
        //       id: 1,
        //       tags: ['1', '2', '3'])
        // ];
      }
    });
  }

  /// 刷新数据
  Future<void> onRefresh() async {
    // await queryProjectList();
  }

  Future<AlbumNetModelABizModel?> fetchAlbumListData() async {
    var listParams = AlbumNetModelParamModel(
      start_date: albumStatUiState.startTime.value,
      end_date: albumStatUiState.endTime.value,
      worker_ids: albumStatUiState.workIds.value,
      work_notes: albumStatUiState.workNotes.value,
      key: albumStatUiState.statList[currentCardIndex.value].key,
      limit: 20.toString(),
    );
    final result = await _groupCloudAlbumRepo.getGroupListData(listParams);
    if (result.isOK()) {
      return result.getSucData();
    } else {
      ToastUtil.showToast('网络处理异常，请稍后再试');
      return null;
    }
  }

  fetchAlbumClockData() async {
    var listParams = AlbumAttendanceListParamModel(
      work_notes: albumStatUiState.workNotes.value,
      worker_ids: albumStatUiState.workIds.value,
      start_date: albumStatUiState.startTime.value,
      end_date: albumStatUiState.endTime.value,
      limit: 20.toString(),
    );
    final result =
        await _groupCloudAlbumRepo.getAlbumAttendanceListData(listParams);
    if (result.isOK()) {
      albumClockListUiState.list.value = result.getSucData()!.list;
    } else {
      ToastUtil.showToast('网络处理异常，请稍后再试');
    }
  }
}
