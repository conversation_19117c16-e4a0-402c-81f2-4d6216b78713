import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_cloud_album/repo/model/album_attendance_list_biz_model.dart';
import 'package:gdjg_pure_flutter/feature/cloud_album/group/image_viewer/vm/protocal/image_viewer_props.dart';
import 'package:gdjg_pure_flutter/generated/assets.dart';
import 'package:gdjg_pure_flutter/init_module/init_route.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/utils/system_util/date_util.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/button_util.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';

class AttendancePhotoView extends StatelessWidget {
  final List<AlbumAttendanceListABizModel> uiState;

  const AttendancePhotoView({super.key, required this.uiState});

  @override
  Widget build(BuildContext context) {
    return Expanded(
        child: Stack(
      children: [
        ListView.builder(
            itemCount: uiState.length,
            itemBuilder: (BuildContext context, int index) {
              var isx5 = (index + 1) % 5 == 0;
              return Column(
                children: [
                  AttendancePhotoCard(uiState: uiState[index]),
                  if (isx5)
                    Container(
                      color: Colors.white,
                      height: 1,
                    ),
                  if (index == uiState.length - 1)
                    Container(
                      margin: EdgeInsets.only(bottom: 66, top: 8),
                      child: Text(
                        '我是有底线的~',
                        style:
                            TextStyle(color: ColorsUtil.black45, fontSize: 12),
                      ),
                    ),
                ],
              );
            }),
        Container(
            alignment: Alignment.bottomCenter,
            child: Container(
              padding: EdgeInsets.only(left: 16, right: 16, bottom: 8, top: 8),
              color: Colors.white,
              child: ButtonUtil.buildCommonButton(
                  text: '导出打卡记录',
                  onPressed: () {
                    YPRoute.openPage(RouteNameCollection.exportRecord);
                  },
                  textStyle: TextStyle(fontSize: 16.sp, color: Colors.white)),
            )),
      ],
    ));
  }
}

class AttendancePhotoCard extends StatelessWidget {
  AlbumAttendanceListABizModel uiState;

  AttendancePhotoCard({Key? key, required this.uiState}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // 日期
        Container(
          color: ColorsUtil.inputBgColor,
          height: 40,
          padding: EdgeInsets.symmetric(horizontal: 16, vertical: 10),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                uiState.date,
                style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w500,
                    color: Colors.black),
              ),
              SizedBox(
                width: 2,
              ),
              Text(
                uiState.atteText ??
                    '${uiState.atteNum}人已打卡/${uiState.notAtteNum}人未打卡',
                style: TextStyle(fontSize: 12.sp, color: ColorsUtil.greyColor),
              ),
            ],
          ),
        ),
        if (uiState.attendance.isNotEmpty)
          ListView.builder(
              // 禁止内部滚动
              physics: NeverScrollableScrollPhysics(),
              shrinkWrap: true,
              itemCount: uiState.attendance.length,
              itemBuilder: (BuildContext context, int index) {
                return AttendanceWorkerCard(uiState: uiState.attendance[index]);
              })
      ],
    );
  }
}

class AttendanceWorkerCard extends StatelessWidget {
  AttendanceBizModel uiState;

  AttendanceWorkerCard({Key? key, required this.uiState}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return // 工人卡片
        Container(
      color: Colors.white,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 15),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Name
          Text(
            uiState.name,
            style: TextStyle(
              color: Color(0xFF323232),
              fontSize: 18,
            ),
          ),
          SizedBox(
            height: 13,
          ),
          LayoutBuilder(builder: (context, constraints) {
            // 在布局完成后获取高度
            WidgetsBinding.instance.addPostFrameCallback((_) {
              _currentContext = context;
              updateHeight();
            });
            return Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Column(
                  children: [
                    Image.asset(
                      Assets.commonWaaIcClockOn,
                      width: 16,
                      height: 16,
                    ),
                    Container(
                      height: _currentHeight.value,
                      width: 1,
                      color: Color(0xFFCCCCCC),
                    )
                  ],
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Container(
                            margin: const EdgeInsets.only(left: 8),
                            // color: Colors.green,
                            child: Text(
                              '上班${DateUtil.timestampToTime(uiState.record[0].createdTime.toInt())}',
                              style: TextStyle(
                                color: Color(0xFF5290FD),
                                fontSize: 16.sp,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                          Container(
                            width: 52,
                            height: 18,
                            margin: const EdgeInsets.only(left: 6),
                            alignment: Alignment.center,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(4),
                              color: Colors.blue, // Replace with your shape
                            ),
                            child: const Text(
                              "已打卡",
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 12,
                              ),
                            ),
                          ),
                        ],
                      ),
                      Container(
                        margin: const EdgeInsets.only(top: 10),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Container(
                              margin: const EdgeInsets.only(
                                  left: 8, top: 2, right: 4),
                              child: Image.asset(
                                Assets.commonWaaIcClockLocation,
                                width: 12,
                                height: 12,
                              ),
                            ),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    uiState.record[0].areaName,
                                    maxLines: 2,
                                    overflow: TextOverflow.ellipsis,
                                    style: TextStyle(
                                      color: Color(0xFF656565),
                                      fontSize: 12,
                                    ),
                                  ),
                                  Text(
                                    uiState.record[0].note,
                                    maxLines: 2,
                                    overflow: TextOverflow.ellipsis,
                                    style: TextStyle(
                                      color: Color(0xFF8A8A99),
                                      fontSize: 12,
                                    ),
                                  ),
                                  SizedBox(height: 16),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                if (uiState.record.isNotEmpty)
                  GestureDetector(
                    onTap: () {
                      final album = [uiState.record[0].img];
                      final props = ImageViewerProps(
                          workNoteId: '123456',
                          workNoteName: '项目1',
                          typeName: '打卡照片',
                          index: 0,
                          album: album);
                      YPRoute.openPage(
                          params: props, RouteNameCollection.imageViewer);
                    },
                    child: Image.network(
                      fit: BoxFit.cover,
                      uiState.record[0].img ?? '',
                      width: 67,
                      height: 67,
                    ),
                  )
              ],
            );
          }),
          if (uiState.record.length > 1) ...[
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Image.asset(
                  Assets.commonWaaIcClockOn,
                  height: 16,
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Container(
                            margin: const EdgeInsets.only(left: 8),
                            child: Text(
                              '下班${DateUtil.timestampToTime(uiState.record[1].createdTime.toInt())}',
                              style: TextStyle(
                                color: Color(0xFF5290FD),
                                fontSize: 16.sp,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            // color: Colors.green,
                          ),
                          Container(
                            width: 52,
                            height: 18,
                            margin: const EdgeInsets.only(left: 6),
                            alignment: Alignment.center,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(4),
                              color: Colors.blue, // Replace with your shape
                            ),
                            child: const Text(
                              "已打卡",
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 12,
                              ),
                            ),
                          ),
                        ],
                      ),
                      Container(
                        // color: Colors.red,
                        margin: const EdgeInsets.only(top: 10),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Container(
                              margin: const EdgeInsets.only(
                                  left: 8, top: 2, right: 4),
                              child: Image.asset(
                                Assets.commonWaaIcClockLocation,
                                width: 12,
                                height: 12,
                              ),
                            ),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    uiState.record[1].areaName,
                                    maxLines: 2,
                                    overflow: TextOverflow.ellipsis,
                                    style: TextStyle(
                                      color: Color(0xFF656565),
                                      fontSize: 12,
                                    ),
                                  ),
                                  Text(
                                    uiState.record[1].note,
                                    maxLines: 2,
                                    overflow: TextOverflow.ellipsis,
                                    style: TextStyle(
                                      color: Color(0xFF8A8A99),
                                      fontSize: 12,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                if (uiState.record.isNotEmpty)
                  GestureDetector(
                    onTap: () {
                      final album = [uiState.record[1].img];
                      final props = ImageViewerProps(
                          workNoteId: '123456',
                          workNoteName: '项目1',
                          typeName: '打卡照片',
                          index: 0,
                          album: album,
                      );
                      YPRoute.openPage(
                          params: props, RouteNameCollection.imageViewer);
                    },
                    child: Image.network(
                      fit: BoxFit.cover,
                      uiState.record[1].img ?? '',
                      width: 67,
                      height: 67,
                    ),
                  )
              ],
            ),
          ] else
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Image.asset(
                  Assets.commonWaaIcClockUn,
                  height: 16,
                ),
                Container(
                  margin: const EdgeInsets.only(left: 8),
                  child: Text(
                    '下班',
                    style: TextStyle(
                      color: Color(0xFF606066),
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                Container(
                  width: 52,
                  height: 18,
                  margin: const EdgeInsets.only(left: 6),
                  alignment: Alignment.center,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(4),
                    color: Color(0xFF8A8A99), // Replace with your shape
                  ),
                  child: const Text(
                    "未打卡",
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                    ),
                  ),
                ),
              ],
            ),
        ],
      ),
    );
  }

  BuildContext? _currentContext;
  final ValueNotifier<double> _currentHeight = ValueNotifier<double>(64);

  // 关键方法：更新当前高度
  void updateHeight() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_currentContext != null && _currentContext!.mounted) {
        final renderBox = _currentContext!.findRenderObject() as RenderBox?;
        if (renderBox != null) {
          renderBox.markNeedsLayout();
          final newHeight = renderBox.size.height;
          if (_currentHeight.value != newHeight) {
            _currentHeight.value = newHeight;
          }
        }
      }
    });
  }
}
