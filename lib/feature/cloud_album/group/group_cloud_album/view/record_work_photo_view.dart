import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_cloud_album/repo/model/album_net_model_biz_model.dart';
import 'package:gdjg_pure_flutter/feature/cloud_album/group/image_viewer/vm/protocal/image_viewer_props.dart';
import 'package:gdjg_pure_flutter/generated/assets.dart';
import 'package:gdjg_pure_flutter/init_module/init_route.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/utils/system_util/date_util.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/button_util.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/dialog_util.dart';

class RecordWorkPhotoView extends StatelessWidget {
  AlbumNetModelABizModel uiState;
  String title;
  bool isShowUpload;

  RecordWorkPhotoView({
    Key? key,
    required this.uiState,
    required this.title,
    this.isShowUpload = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Expanded(
        child: Stack(
      children: [
        ListView.builder(
            // shrinkWrap: true,
            itemCount: uiState.list.length,
            itemBuilder: (BuildContext context, int index) {
              return RecordWorkPhotoCard(
                uiState: uiState.list[index],
                statTitle: title,
                isLast: index == uiState.list.length - 1,
              );
            }),
        if (isShowUpload)
          Container(
              alignment: Alignment.bottomCenter,
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.white,
                  boxShadow: [
                    BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        blurRadius: 10,
                        offset: Offset(0, 0))
                  ],
                ),
                padding:
                    EdgeInsets.only(left: 16, right: 16, bottom: 8, top: 8),
                // color: Colors.white,
                child: ButtonUtil.buildCommonButton(
                    text: '上传照片',
                    onPressed: () {
                      DialogUtil.showUploadImageBottomSheet(context,
                          onTap: (urls) {});
                    },
                    textStyle: TextStyle(fontSize: 16.sp, color: Colors.white)),
              )),
      ],
    ));
  }
}

class RecordWorkPhotoCard extends StatelessWidget {
  AlbumNetModelBBizModel uiState;
  String statTitle;
  bool isLast;

  RecordWorkPhotoCard(
      {Key? key,
      required this.uiState,
      required this.statTitle,
      this.isLast = false})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Container(
          height: 40,
          color: ColorsUtil.inputBgColor,
          padding: EdgeInsets.symmetric(horizontal: 16, vertical: 10),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                DateUtil.formatChineseDate(DateTime.parse(uiState.currentDate),
                    showYear: true),
                style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w500,
                    color: Colors.black),
              ),
              SizedBox(
                width: 2,
              ),
              Text(
                '/${uiState.photoNum}张照片',
                style: TextStyle(fontSize: 12.sp, color: ColorsUtil.greyColor),
              ),
            ],
          ),
        ),
        Container(
          color: Colors.white,
          padding: EdgeInsets.all(16),
          child: GridView.builder(
              shrinkWrap: true,
              physics: NeverScrollableScrollPhysics(),
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 3,
                  mainAxisSpacing: 3,
                  crossAxisSpacing: 3,
                  childAspectRatio: 1),
              itemCount: uiState.photoNum.toInt(),
              itemBuilder: (BuildContext context, int index) {
                final List<String> album =
                    uiState.photoDetailList.map((e) => e.url).toList();
                final buttonName = switch (statTitle) {
                  '记工照片' => '查看记工',
                  '记账照片' => '查看记账',
                  String() => '',
                };
                final props = ImageViewerProps(
                    workNoteId: '123456',
                    workNoteName: '项目1',
                    typeName: statTitle,
                    index: index,
                    buttonName: buttonName,
                    album: album);
                return GestureDetector(
                  onTap: () {
                    YPRoute.openPage(
                        params: props, RouteNameCollection.imageViewer);
                  },
                  child: Image.network(
                    fit: BoxFit.cover,
                    uiState.photoDetailList[index].url,
                  ),
                );
              }),
        ),
        if (isLast)
          Container(
            margin: EdgeInsets.only(bottom: 66),
            child: Text(
              '我是有底线的~',
              style: TextStyle(color: ColorsUtil.black45, fontSize: 12),
            ),
          ),
      ],
    );
  }
}
