import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/feature/cloud_album/group/vm/protocal/group_cloud_album_list_ui_state.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/button_util.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/dialog_util.dart';

class RecordWorkPhotoView extends StatelessWidget {
  GroupCloudAlbumListUiState uiState;
  bool isShowUpload;

  RecordWorkPhotoView(
      {Key? key, required this.uiState, this.isShowUpload = false})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Expanded(
        child: Stack(
      children: [
        ListView.builder(
            itemCount: uiState.cardList.length,
            itemBuilder: (BuildContext context, int index) {
              return RecordWorkPhotoCard(uiState: uiState.cardList[index]);
            }),
        if (isShowUpload)
          Container(
              alignment: Alignment.bottomCenter,
              child: Container(
                padding:
                    EdgeInsets.only(left: 16, right: 16, bottom: 8, top: 8),
                color: Colors.white,
                child: ButtonUtil.buildCommonButton(
                    text: '上传照片',
                    onPressed: () {
                      DialogUtil.showUploadImageBottomSheet();
                    },
                    textStyle: TextStyle(fontSize: 16.sp, color: Colors.white)),
              )),
      ],
    ));
  }
}

class RecordWorkPhotoCard extends StatelessWidget {
  GroupCloudAlbumListCardEntity uiState;

  RecordWorkPhotoCard({Key? key, required this.uiState}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Container(
          height: 40,
          padding: EdgeInsets.symmetric(horizontal: 16, vertical: 10),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                uiState.data ?? '',
                style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.bold,
                    color: Colors.black),
              ),
              SizedBox(
                width: 2,
              ),
              Text(
                '/${uiState.urls.length.toString()}张照片',
                style: TextStyle(fontSize: 12.sp, color: ColorsUtil.greyColor),
              ),
            ],
          ),
        ),
        Container(
          color: Colors.white,
          padding: EdgeInsets.all(16),
          child: GridView.builder(
              shrinkWrap: true,
              physics: NeverScrollableScrollPhysics(),
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 3,
                  mainAxisSpacing: 3,
                  crossAxisSpacing: 3,
                  childAspectRatio: 1),
              itemCount: uiState.urls.length,
              itemBuilder: (BuildContext context, int index) {
                return Image.network(
                  fit: BoxFit.cover,
                  uiState.urls[index],
                );
              }),
        ),
      ],
    );
  }
}
