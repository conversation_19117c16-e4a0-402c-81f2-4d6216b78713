import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:gdjg_pure_flutter/feature/media_viewer/video_viewer.dart';
import 'package:gdjg_pure_flutter/feature/worker/worker_flow/vm/protocol/worker_flow_us.dart';
import 'package:gdjg_pure_flutter/utils/route_util/base_page/base_ful_page.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:get/get.dart';

import 'media_viewer_viewmodel.dart';

/// @date 2025/06/19
/// @param props 页面路由参数
/// @returns
/// @description MediaViewer页面入口
class MediaViewerPage extends BaseFulPage {
  const MediaViewerPage({super.key}) : super(appBar: null);

  @override
  State<StatefulWidget> createState() {
    return _MediaViewerPageState();
  }
}

class _MediaViewerPageState extends BaseFulPageState<MediaViewerPage> {
  final MediaViewerViewModel viewModel = MediaViewerViewModel();
  PageController? _mPageViewController;

  @override
  Widget yBuild(BuildContext context) {
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
    SystemChrome.setSystemUIOverlayStyle(const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      systemNavigationBarColor: Colors.transparent,
      systemNavigationBarContrastEnforced: false,
      statusBarIconBrightness: Brightness.light,
      systemNavigationBarIconBrightness: Brightness.light,
    ));
    return Container(
      color: Colors.black,
      child: Obx(() {
        return PageView.builder(
          onPageChanged: _onPageChanged,
          controller: _mPageViewController,
          itemBuilder: (BuildContext context, int index) {
            var mItem = viewModel.mediaUIStateList[index];
            var itemView = mItem.isVideo ? VideoViewerWidget(mItem) : Image.network(mItem.url);
            return buildItemView(mItem.url, itemView);
          },
          itemCount: viewModel.mediaUIStateList.length,
        );
      }),
    );
  }

  _onPageChanged(int currentIndex) {
    viewModel.currentPageIndex.value = currentIndex;
  }

  @override
  void onPageRoute(Object? routeParams, bool fromLaunchTask) {
    if (routeParams != null) {
      var mParams = routeParams as Map<String, dynamic>;
      var mMediaList = mParams["mediaList"] as List<ResourceEntity>;
      int mIndex = mParams["index"];
      _mPageViewController = PageController(initialPage: mIndex);
      viewModel.currentPageIndex.value = mIndex;

      /// 增加视频控制器
      var urlList = mMediaList.indexed.map((entry) {
        final (index, media) = entry;
        return MediaViewerUIState(media.url, media.isVideo, index);
      }).toList();
      viewModel.mediaUIStateList.value = urlList;
    }
  }

  @override
  void dispose() {
    _mPageViewController?.dispose();
    super.dispose();
  }

  Widget buildItemView(String url, Widget mItemView) {
    var statusBarHeight = MediaQuery.of(context).padding.top;
    return Stack(children: [
      mItemView,
      Container(
        color: Colors.black.withAlpha(60),
        margin: EdgeInsets.only(top: statusBarHeight),
        height: 44,
        child: Row(
          children: [
            InkWell(
              onTap: () => YPRoute.closePage(),
              child: Container(
                padding: EdgeInsets.all(12),
                child: Icon(Icons.arrow_back, color: Colors.white),
              ),
            ),
            Expanded(
              child: Center(
                child: Obx(() {
                  return Text(
                    "${viewModel.currentPageIndex.value + 1}/${viewModel.mediaUIStateList.length}",
                    style: TextStyle(color: Colors.white, fontSize: 14),
                  );
                }),
              ),
            ),
            SizedBox(width: 48),
          ],
        ),
      ),
    ]);
  }
}
