import "package:get/get.dart";

/// @date 2025/06/19
/// @description MediaViewer页ViewModel
/// 1. 处理数据的加载状态，如loading和错误视图等
/// 2. 观测UIRep中的业务实体转为UIState
/// 3. 接受来自view的意图并分发事件进行处理（暂无）
/// 4. 对View层暴露事件（操作视图和处理一些与系统权限交互等场景）
class MediaViewerViewModel {
  var currentPageIndex = 0.obs;
  var mediaUIStateList = <MediaViewerUIState>[].obs;
  MediaViewerViewModel();
}

class MediaViewerUIState {
  final String url;
  final bool isVideo;
  final int pageIndex;
  final bool isPlaying;
  final int progress;
  final bool isInitialized;

  MediaViewerUIState(
    this.url,
    this.isVideo,
    this.pageIndex, {
    this.isPlaying = false,
    this.progress = 0,
    this.isInitialized = false,
  });
}
