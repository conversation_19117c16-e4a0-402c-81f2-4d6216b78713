import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:video_player/video_player.dart';

import 'media_viewer_viewmodel.dart';

class VideoViewerWidget extends StatefulWidget {
  final MediaViewerUIState mediaItem;
  const VideoViewerWidget(this.mediaItem, {super.key});

  @override
  State<VideoViewerWidget> createState() => _VideoViewerWidgetState();
}

class _VideoViewerWidgetState extends State<VideoViewerWidget> {
  VideoPlayerController? controller;
  MediaViewerUIState get itemData => widget.mediaItem;
  var isInitialized = false.obs;
  var isPlaying = false.obs;
  var isBuffering = false.obs;
  var progress = 0.obs;
  var lastProgress = 0;

  @override
  void initState() {
    controller = VideoPlayerController.networkUrl(Uri.parse(itemData.url))
      ..initialize().then((_) {
        isInitialized.value = true;
        isPlaying.value = true;
        controller?.play();
      })
      ..addListener(() async {
        if (controller!.value.position >= controller!.value.duration) {
          isPlaying.value = false;
          isBuffering.value = false;
        } else {
          isPlaying.value = true;
          progress.value = controller!.value.position.inMilliseconds;
          if (progress.value != lastProgress) {
            isBuffering.value = false;
          } else {
            isBuffering.value = true;
          }
          lastProgress = progress.value;
        }
      });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return _buildVideoView(itemData);
  }

  Widget _buildVideoView(MediaViewerUIState item) {
    if (controller == null) return const SizedBox();
    return Obx(() {
      return isInitialized.value
          ? Stack(
              children: [
                AspectRatio(
                  aspectRatio: controller!.value.aspectRatio,
                  child: InkWell(
                    onTap: _onTapVideo,
                    child: VideoPlayer(controller!),
                  ),
                ),
                Obx(() {
                  return isPlaying.value
                      ? const SizedBox()
                      : InkWell(
                          onTap: _onTapVideo,
                          child: Center(
                            child: Image.asset(
                              "assets/images/common/icon_role_worker.webp",
                              width: 60,
                              height: 60,
                            ),
                          ),
                        );
                }),
                Obx(() {
                  return isBuffering.value
                      ? Center(
                          child: Container(
                            width: 80,
                            height: 80,
                            alignment: Alignment.center,
                            decoration: BoxDecoration(
                              color: Colors.black.withAlpha(88),
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(width: 0.5, color: Colors.white),
                            ),
                            child: SizedBox(
                              width: 32,
                              height: 32,
                              child: CircularProgressIndicator(color: Colors.white),
                            ),
                          ),
                        )
                      : const SizedBox();
                }),
                Positioned(
                  left: 0,
                  right: 0,
                  bottom: 0,
                  child: Obx(() {
                    var valueProgress = 0.0;
                    if (!controller!.value.isInitialized ||
                        controller!.value.duration.inMilliseconds == 0) {
                      valueProgress = 0;
                    } else {
                      valueProgress = progress.value / controller!.value.duration.inMilliseconds;
                    }
                    return Container(
                      padding: const EdgeInsets.symmetric(vertical: 8),
                      color: Colors.black.withAlpha(88),
                      child: Row(
                        children: [
                          Expanded(
                            child: Slider(
                              activeColor: Colors.white,
                              value: valueProgress.clamp(0.0, 1.0),
                              onChanged: (value) {
                                final position = controller!.value.duration * value;
                                controller?.seekTo(position);
                              },
                            ),
                          ),
                          Text(
                            _formatDuration(controller!.value.position),
                            style: TextStyle(color: Colors.white),
                          ),
                          Text(
                            "/${_formatDuration(controller!.value.duration)}",
                            style: TextStyle(color: Colors.white),
                          ),
                          SizedBox(width: 20),
                        ],
                      ),
                    );
                  }),
                ),
              ],
            )
          : const Center(child: CircularProgressIndicator(color: Colors.white));
    });
  }

  _onTapVideo() {
    if (controller!.value.isPlaying) {
      controller!.pause();
      isPlaying.value = false;
    } else {
      controller!.play();
      isPlaying.value = true;
    }
  }

  /// 辅助函数：格式化时间
  String _formatDuration(Duration duration) {
    final minutes = duration.inMinutes.remainder(60).toString().padLeft(2, '0');
    final seconds = duration.inSeconds.remainder(60).toString().padLeft(2, '0');
    return '$minutes:$seconds';
  }

  @override
  void dispose() {
    print("viedeo_viewer_dispose=======");
    controller?.dispose();
    super.dispose();
  }
}
