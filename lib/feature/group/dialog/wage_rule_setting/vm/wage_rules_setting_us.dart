
import 'package:get/get.dart';

class WageRuleSettingUS{
  /// 是否按小时计算
  final _isHour = true.obs;

  /// 上班时间-小时
  final _workHour = ''.obs;

  /// 上班工资
  final workMoney = ''.obs;

  /// 加班时间-小时
  final _overWorkHour = ''.obs;

  /// 加班工资--按小时算时的工资
  final overWorkMoney = ''.obs;

  ///工友名称
  final _workerName = ''.obs;

  get isHour => _isHour.value;

  get workHour => _workHour.value;

  get overWorkHour => _overWorkHour.value;

  get workerName => _workerName.value;


  setIsHour(bool isHour) {
    _isHour.value = isHour;
    _isHour.refresh();
  }

  setWorkHour(String workHour) {
    _workHour.value = workHour;
    _workHour.refresh();
  }

  setWorkMoney(String workMoney) {
    this.workMoney.value = workMoney;
    this.workMoney.refresh();
  }

  setOverWorkHour(String overWorkHour) {
    _overWorkHour.value = overWorkHour;
    _overWorkHour.refresh();
  }

  setOverWorkMoney(String overWorkMoney) {
    this.overWorkMoney.value = overWorkMoney;
    this.overWorkMoney.refresh();
  }

  setWorkerName(String workerName) {
    _workerName.value = workerName;
    _workerName.refresh();
  }

}