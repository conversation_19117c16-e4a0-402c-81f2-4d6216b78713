import 'package:get/get.dart';

/// @date 2025/06/24
/// @description WageRulesSetting页ViewModel
/// 工价设置
class WageRulesSettingViewModel {
  /// 是否按小时计算
  var isHour = true.obs;

  /// 上班时间-小时
  var workHour = "8".obs;

  /// 上班工资
  var workMoney = 301.00.obs;

  /// 加班时间-小时
  var overWorkHour = "6".obs;

  /// 加班工资--按小时算时的工资
  var overWorkMoney = 51.00.obs;

  WageRulesSettingViewModel();

  void onSwitchChanged() {
    isHour.value = !isHour.value;
  }
}
