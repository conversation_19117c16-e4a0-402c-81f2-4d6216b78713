import 'package:flutter/widgets.dart';
import 'package:gdjg_pure_flutter/data/fee_standard_data/ds/model/params/delete_fee_param_model.dart';
import 'package:gdjg_pure_flutter/data/fee_standard_data/ds/model/params/fee_standard_param_model.dart';
import 'package:gdjg_pure_flutter/data/fee_standard_data/ds/model/params/update_fee_param_model.dart';
import 'package:gdjg_pure_flutter/data/fee_standard_data/repo/fee_standard_repo.dart';
import 'package:gdjg_pure_flutter/data/fee_standard_data/repo/model/fee_standard_biz_model.dart';
import 'package:gdjg_pure_flutter/feature/group/dialog/wage_rule_setting/entity/wage_rules_props.dart';
import 'package:gdjg_pure_flutter/feature/group/dialog/wage_rule_setting/vm/wage_rules_setting_us.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/utils/system_util/string_util.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/toast_util.dart';
import 'package:gdjg_pure_flutter/widget/hour_selector_dialog.dart';

/// @date 2025/06/24
/// @description WageRulesSetting页ViewModel
/// 工价设置
class WageRulesSettingViewModel {
  final _feeStandardRepo = FeeStandardRepo();
  final us = WageRuleSettingUS();
  WageRulesProps? props;
  FeeStandardBizModel? _feeStandard;

  get feeStandard => _feeStandard;

  init(WageRulesProps? props) {
    this.props = props;
    if (props?.feeStandard != null) {
      _feeStandard = props?.feeStandard;
      convertFeeEntityToUIState(props?.feeStandard);
    } else {
      fetchData();
    }
  }

  /// 切换加班类型
  void onSwitchChanged() {
    us.setIsHour(!us.isHour);
    if (us.isHour) {
      _feeStandard?.overtimeType = 1;
    } else {
      _feeStandard?.overtimeType = 2;
    }
  }

  /// 修改上班单价
  void changeWorkPrice(String value) {
    if (value == us.workMoney.value) return;
    us.setWorkMoney(value);
    _feeStandard?.workingHoursPrice = value;
  }

  /// 修改加班单价
  void changeOverPrice(String value) {
    if (value == us.overWorkMoney.value) return;
    us.setOverWorkMoney(value);
    _feeStandard?.overtimeHoursPrice = value;
  }

  void onConfirm() {
    ToastUtil.showToast('点击了确定');
  }

  void onDelete() {
    ToastUtil.showToast('点击了删除');
  }

  fetchData() async {
    var workerIds =
        props?.workers?.map((item) => item.workerId.toString()).join(',') ?? '';

    var feeParams = FeeStandardParamModel()
      ..workNoteId = props?.workNoteId ?? ''
      ..businessType = props?.businessType?.code.value.toString() ?? ''
      ..workerId = workerIds
      ..type = props?.recordNoteType?.value.toString() ?? "1";
    var feeResult = await _feeStandardRepo.getNewFeeStandard(feeParams);
    if (feeResult.isOK()) {
      var data = feeResult.getSucData();
      _feeStandard = data;
      convertFeeEntityToUIState(data);
    }
  }

  void convertFeeEntityToUIState(FeeStandardBizModel? fee) {
    if (fee == null) return;
    us.setWorkHour(fee.workingHoursStandard.trimTrailingZeros());
    us.setWorkMoney(fee.workingHoursPrice);
    us.setOverWorkHour(fee.overtimeHoursStandard.trimTrailingZeros());
    us.setOverWorkMoney(fee.overtimeHoursPrice);
  }

  /// 显示加班选择弹窗
  void showWorkTimeDialog() {
    var hour =
        double.tryParse(_feeStandard?.workingHoursStandard ?? "0.5") ?? 0.5;
    YPRoute.openDialog(
      builder: (context) => HourSelectorDialog(
        initHour: hour,
        initWorkDay: 0,
        enableKeyboard: false,
        onConfirm: (result) {
          if (result.type == HourSelectionType.hour) {
            var hour = result.value.trimTrailingZeros();
            _feeStandard?.workingHoursStandard = hour;
            us.setWorkHour(hour);
          }
        },
      ),
      alignment: Alignment.bottomCenter,
    );
  }

  /// 显示加班选择弹窗
  void showOvertimeDialog() {
    var hour =
        double.tryParse(_feeStandard?.overtimeHoursStandard ?? "0.5") ?? 0.5;
    YPRoute.openDialog(
      builder: (context) => HourSelectorDialog(
        initHour: hour,
        initWorkDay: 0,
        enableKeyboard: false,
        onConfirm: (result) {
          if (result.type == HourSelectionType.hour) {
            var hour = result.value.trimTrailingZeros();
            _feeStandard?.overtimeHoursStandard = hour;
            us.setOverWorkHour(hour);
          }
        },
      ),
      alignment: Alignment.bottomCenter,
    );
  }

  Future<bool> deleteFeeStandard() async {
    var feeParams = DeleteFeeStandardParamModel(
      work_note: props?.workNoteId,
      worker_ids:
          props?.workers?.map((item) => item.workerId.toString()).join(','),
      businessType: props?.businessType?.code.value.toString(),
      type: props?.recordNoteType?.value.toString(),
    );
    var feeResult = await _feeStandardRepo.deleteFeeStandard(feeParams);
    return feeResult.isOK();
  }

  Future<bool> updateFeeStandard() async {
    var feeParams = FeeStandardUpdateNewParamModel(
      workNoteId: props?.workNoteId,
      workerId:
      props?.workers?.map((item) => item.workerId.toString()).join(','),
      businessType: props?.businessType?.code.value.toString(),
      workingHoursPrice: _feeStandard?.workingHoursPrice,
      workingHoursStandard: _feeStandard?.workingHoursStandard,
      overtimeHoursPrice: _feeStandard?.overtimeHoursPrice,
      overtimeHoursStandard: _feeStandard?.workingHoursStandard,
      overtimeType: _feeStandard?.overtimeType,
      type: props?.requestType ?? 1,
    );

    var feeResult = await _feeStandardRepo.updateFeeStandard(feeParams);
    if (feeResult.isOK()) {
      var feeStandardId = feeResult.getSucData()?.feeStandardId;
      if (feeStandardId != null && feeStandardId.isNotEmpty == true) {
        _feeStandard?.feeStandardId = feeStandardId;
      }
      return true;
    }
    return false;
  }
}
