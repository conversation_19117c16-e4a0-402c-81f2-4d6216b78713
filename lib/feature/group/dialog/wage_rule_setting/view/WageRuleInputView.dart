import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:gdjg_pure_flutter/feature/group/dialog/wage_rule_setting/vm/WageRulesSettingViewModel.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';

/// 工价设置输入框
class WageRuleInputView extends StatelessWidget {
  final WageRulesSettingViewModel viewModel;
  final TextEditingController controller;

  const WageRuleInputView(
      {super.key, required this.viewModel, required this.controller});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 30,
      width: 80,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(6),
        border: Border.all(color: Colors.grey, width: 0.5),
      ),
      child: TextField(
        keyboardType: TextInputType.number,
        controller: controller,
        textAlign: TextAlign.center,
        // 文本居中显示
        style: TextStyle(
          color: ColorsUtil.rgb(255, 160, 17), // 输入文本颜色为橙色
          fontSize: 16,
        ),
        inputFormatters: [
          // 只允许输入数字和小数点，并限制小数点后最多2位
          _DecimalInputFormatter(maxValue: 9999, decimalPlaces: 2),
        ],
        decoration: InputDecoration(
          border: InputBorder.none,
          hintText: '0.00',
          hintStyle: TextStyle(
            fontSize: 16,
            color: ColorsUtil.rgb(255, 160, 17),
          ),
        ),
      ),
    );
  }
}

/// 自定义输入格式化器，限制最大值和小数点位数
class _DecimalInputFormatter extends TextInputFormatter {
  final double maxValue;
  final int decimalPlaces;

  _DecimalInputFormatter({required this.maxValue, required this.decimalPlaces});

  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    // 允许空输入
    if (newValue.text.isEmpty) {
      return newValue;
    }

    // 只允许数字和小数点
    if (!RegExp(r'^[0-9]*\.?[0-9]*$').hasMatch(newValue.text)) {
      return oldValue;
    }
    // 检查小数点数量（最多只能有一个小数点）
    if (newValue.text.split('.').length > 2) {
      return oldValue;
    }

    // 检查小数位数
    if (newValue.text.contains('.')) {
      List<String> parts = newValue.text.split('.');
      if (parts.length == 2 && parts[1].length > decimalPlaces) {
        return oldValue;
      }
    }

    // 尝试解析输入的数字
    final double? value = double.tryParse(newValue.text);

    // 如果解析成功，检查是否超过最大值
    if (value != null && value > maxValue) {
      return oldValue;
    }

    return newValue;
  }
}
