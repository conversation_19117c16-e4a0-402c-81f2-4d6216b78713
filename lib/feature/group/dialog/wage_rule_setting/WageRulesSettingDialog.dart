import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/feature/group/dialog/wage_rule_setting/view/WageRuleInputView.dart';
import 'package:gdjg_pure_flutter/feature/group/dialog/wage_rule_setting/vm/WageRulesSettingViewModel.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/toast_util.dart';
import 'package:get/get.dart';

class WageRulesSettingDialog extends Dialog {
  /// 弹窗标题
  final String title;
  final WageRulesSettingViewModel viewModel = WageRulesSettingViewModel();
  final TextEditingController _workMoneyController = TextEditingController();
  final TextEditingController _overWorkMoneyController =
      TextEditingController();

  WageRulesSettingDialog({super.key, required this.title});

  @override
  Widget build(BuildContext context) {
    _workMoneyController.text = viewModel.workMoney.value.toString();
    _overWorkMoneyController.text = viewModel.overWorkMoney.value.toString();
    return Material(
      type: MaterialType.transparency,
      child: Center(
        child: Container(
          height: 224.h,
          margin: const EdgeInsets.fromLTRB(24, 0, 24, 0),
          padding: const EdgeInsets.fromLTRB(16, 16, 16, 0),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            spacing: 8,
            children: [
              // 标题左对齐
              Align(
                alignment: Alignment.centerLeft,
                child: SizedBox(
                  height: 24.h,
                  child: Row(
                    children: [
                      Text(title),
                      Spacer(),
                      GestureDetector(
                        onTap: () {},
                        child: Icon(
                          Icons.delete,
                          color: ColorsUtil.primaryColor,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              // "工价："文本左对齐
              Align(
                alignment: Alignment.centerLeft,
                child: const Text("工价："),
              ),
              _buildWorkItem(),
              _buildOverWorkItem(),
              _buildBottomView(context),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildWorkItem() {
    return SizedBox(
      height: 50,
      child: Row(
        spacing: 2,
        children: [
          const Text("上班"),
          SizedBox(width: 2),
          _buildTimeView(viewModel.workHour.value),
          const Text("小时="),
          WageRuleInputView(
              viewModel: viewModel, controller: _workMoneyController),
          const Text("元=1个工"),
        ],
      ),
    );
  }

  Widget _buildOverWorkItem() {
    return Obx(() {
      return SizedBox(
        height: 50,
        child: Row(
          spacing: 2,
          children: [
            const Text("加班"),
            SizedBox(width: 2),
            if (viewModel.isHour.value) _buildHourType() else _buildDayType(),
            Spacer(), // 添加弹性空间，将切换按钮推到右侧
            _buildWageTypeSwitch(viewModel.isHour.value),
          ],
        ),
      );
    });
  }

  /// 按小时计算
  Widget _buildHourType() {
    return Row(
      spacing: 2,
      children: [
        _buildTimeView(viewModel.overWorkHour.value),
        const Text("小时=1个工"),
      ],
    );
  }

  /// 按工天计算
  Widget _buildDayType() {
    return Row(
      spacing: 2,
      children: [
        const Text("1小时="),
        WageRuleInputView(
          viewModel: viewModel,
          controller: _overWorkMoneyController,
        ),
        const Text("元"),
      ],
    );
  }

  Widget _buildBottomView(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        border: Border(top: BorderSide(color: Color(0xFFE6E6E6), width: 1.w)),
      ),
      child: SizedBox(
        height: 38.h,
        child: Row(
          children: [
            Expanded(
              child: TextButton(
                onPressed: () {
                  ToastUtil.showToast("点击了取消");
                  Navigator.pop(context);
                },
                style: TextButton.styleFrom(
                  // 去掉默认的椭圆形点击效果
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.zero,
                  ),
                  overlayColor:
                      Color.fromARGB((255 * 0.1).toInt(), 238, 238, 238), // 自定义点击效果
                ),
                child: Text(
                  '取消',
                  style: TextStyle(
                    color: ColorsUtil.black65,
                  ),
                ),
              ),
            ),
            VerticalDivider(width: 1.w, color: Color(0xFFE6E6E6)),
            Expanded(
              child: TextButton(
                onPressed: () {
                  ToastUtil.showToast("点击了确定");
                },
                style: TextButton.styleFrom(
                  // 去掉默认的椭圆形点击效果
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.zero,
                  ),
                  overlayColor: Colors.grey.withOpacity(0.1), // 自定义点击效果
                ),
                child: Text('确定',
                    style: TextStyle(
                      color: ColorsUtil.primaryColor,
                      fontWeight: FontWeight.bold,
                    )),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建时间选择view
  Widget _buildTimeView(String value) {
    return Container(
      height: 30,
      width: 40,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(6),
        border: Border.all(color: Colors.grey, width: 0.5),
      ),
      child: Align(
        alignment: Alignment.center,
        child: Text(
          value,
          style: TextStyle(color: Colors.blue),
        ),
      ),
    );
  }

  /// 工价输入框: value <= 1000
  Widget _buildMoneyInputView(double value, TextEditingController controller) {
    return Container(
      height: 30,
      width: 80,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(6),
        border: Border.all(color: Colors.grey, width: 0.5),
      ),
      child: TextField(
        keyboardType: TextInputType.number,
        controller: controller,
        textAlign: TextAlign.center,
        // 文本居中显示
        style: TextStyle(
          color: ColorsUtil.rgb(255, 160, 17), // 输入文本颜色为橙色
          fontSize: 16,
        ),
        inputFormatters: [
          // 只允许输入数字和小数点，并限制小数点后最多2位
          _DecimalInputFormatter(maxValue: 9999, decimalPlaces: 2),
        ],
        decoration: InputDecoration(
          border: InputBorder.none,
          hintText: '0.00',
          hintStyle: TextStyle(
            fontSize: 16,
            color: ColorsUtil.rgb(255, 160, 17),
          ),
        ),
      ),
    );
  }

  /// 工价类型切换
  Widget _buildWageTypeSwitch(bool isHour) {
    return GestureDetector(
      onTap: onSwitchChanged,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 4),
        decoration: BoxDecoration(
          color: Color(0xFFedf4ff),
          borderRadius: BorderRadius.circular(4),
          border: Border.all(color: Colors.blue, width: 1),
        ),
        child: Row(
          spacing: 4,
          children: [
            Icon(Icons.loop, color: Colors.blue, size: 16),
            Text(
              "按${isHour ? '工天' : '小时'}算",
              style: TextStyle(color: Colors.blue),
            ),
          ],
        ),
      ),
    );
  }

  /// 切换事件处理
  void onSwitchChanged() {
    ToastUtil.showToast('切换工价类型');
    viewModel.onSwitchChanged();
  }
}

/// 自定义输入格式化器，限制最大值和小数位数
class _DecimalInputFormatter extends TextInputFormatter {
  final double maxValue;
  final int decimalPlaces;

  _DecimalInputFormatter({required this.maxValue, required this.decimalPlaces});

  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    // 允许空输入
    if (newValue.text.isEmpty) {
      return newValue;
    }

    // 只允许数字和小数点
    if (!RegExp(r'^[0-9]*\.?[0-9]*$').hasMatch(newValue.text)) {
      return oldValue;
    }

    // 检查小数点数量（最多只能有一个小数点）
    if (newValue.text.split('.').length > 2) {
      return oldValue;
    }

    // 检查小数位数
    if (newValue.text.contains('.')) {
      List<String> parts = newValue.text.split('.');
      if (parts.length == 2 && parts[1].length > decimalPlaces) {
        return oldValue;
      }
    }

    // 尝试解析输入的数字
    final double? value = double.tryParse(newValue.text);

    // 如果解析成功，检查是否超过最大值
    if (value != null && value > maxValue) {
      return oldValue;
    }

    return newValue;
  }
}
