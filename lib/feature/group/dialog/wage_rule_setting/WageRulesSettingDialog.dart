import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/feature/group/dialog/wage_rule_setting/vm/WageRulesSettingViewModel.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/toast_util.dart';
import 'package:get/get.dart';

class WageRulesSettingDialog extends Dialog {
  /// 弹窗标题
  final String title;
  final WageRulesSettingViewModel viewModel = WageRulesSettingViewModel();
  final TextEditingController _workMoneyController = TextEditingController();
  final TextEditingController _overWorkMoneyController =
      TextEditingController();

  WageRulesSettingDialog({super.key, required this.title});

  @override
  Widget build(BuildContext context) {
    _workMoneyController.text = viewModel.workMoney.value.toString();
    _overWorkMoneyController.text = viewModel.overWorkMoney.value.toString();
    return Material(
      type: MaterialType.transparency,
      child: Center(
        child: Container(
          height: 212.h,
          margin: const EdgeInsets.fromLTRB(24, 0, 24, 0),
          padding: const EdgeInsets.fromLTRB(16, 16, 16, 0),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            spacing: 8,
            children: [
              // 标题左对齐
              Align(
                alignment: Alignment.centerLeft,
                child: Text(title),
              ),
              // "工价："文本左对齐
              Align(
                alignment: Alignment.centerLeft,
                child: const Text("工价："),
              ),
              _buildWorkItem(),
              _buildOverWorkItem(),
              _buildBottomView(context),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildWorkItem() {
    return SizedBox(
      height: 50,
      child: Row(
        spacing: 2,
        children: [
          const Text("上班"),
          SizedBox(width: 2),
          _buildTimeView(viewModel.workHour.value),
          const Text("小时="),
          _buildMoneyInputView(viewModel.workMoney.value, _workMoneyController),
          const Text("元=1个工"),
        ],
      ),
    );
  }

  Widget _buildOverWorkItem() {
    return Obx(() {
      return SizedBox(
        height: 50,
        child: Row(
          spacing: 2,
          children: [
            const Text("加班"),
            SizedBox(width: 2),
            if (viewModel.isHour.value) _buildHourType() else _buildDayType(),
            Spacer(), // 添加弹性空间，将切换按钮推到右侧
            _buildWageTypeSwitch(viewModel.isHour.value),
          ],
        ),
      );
    });
  }

  /// 按小时计算
  Widget _buildHourType() {
    return Row(
      spacing: 2,
      children: [
        _buildTimeView(viewModel.overWorkHour.value),
        const Text("小时=1个工"),
      ],
    );
  }

  /// 按工天计算
  Widget _buildDayType() {
    return Row(
      spacing: 2,
      children: [
        const Text("1小时="),
        _buildMoneyInputView(
            viewModel.overWorkMoney.value, _overWorkMoneyController),
        const Text("元"),
      ],
    );
  }

  Widget _buildBottomView(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        border: Border(top: BorderSide(color: Color(0xFFE6E6E6), width: 1.w)),
      ),
      child: SizedBox(
        height: 32,
        child: Row(
          children: [
            Expanded(
              child: TextButton(
                onPressed: () {
                  ToastUtil.showToast("点击了取消");
                  Navigator.pop(context);
                },
                style: TextButton.styleFrom(
                  // 去掉默认的椭圆形点击效果
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.zero,
                  ),
                ),
                child: const Text('取消'),
              ),
            ),
            VerticalDivider(width: 1.w, color: Color(0xFFE6E6E6)),
            Expanded(
              child: TextButton(
                onPressed: () {
                  ToastUtil.showToast("点击了确定");
                },
                style: TextButton.styleFrom(
                  // 去掉默认的椭圆形点击效果
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.zero,
                  ),
                  overlayColor: Colors.grey.withOpacity(0.1), // 自定义点击效果
                ),
                child: const Text('确定'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建时间选择view
  Widget _buildTimeView(String value) {
    return Container(
      height: 30,
      width: 40,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(6),
        border: Border.all(color: Colors.grey, width: 0.5),
      ),
      child: Align(
        alignment: Alignment.center,
        child: Text(
          value,
          style: TextStyle(color: Colors.blue),
        ),
      ),
    );
  }

  /// 工价输入框: value <= 1000
  Widget _buildMoneyInputView(double value, TextEditingController controller) {
    return Container(
      height: 30,
      width: 80,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(6),
        border: Border.all(color: Colors.grey, width: 0.5),
      ),
      child: TextField(
        keyboardType: TextInputType.number,
        controller: controller,
        textAlign: TextAlign.center, // 文本居中显示
        style: TextStyle(
          color: Colors.blue, // 输入文本颜色为蓝色
          fontSize: 16,
        ),
        decoration: InputDecoration(
          border: InputBorder.none,
          hintText: '0.00',
          hintStyle: TextStyle(
            fontSize: 16,
            color: ColorsUtil.hintFontColor,
          ),
        ),
      ),
    );
  }

  /// 工价类型切换
  Widget _buildWageTypeSwitch(bool isHour) {
    return GestureDetector(
      onTap: onSwitchChanged,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 4),
        decoration: BoxDecoration(
          color: Color(0xFFedf4ff),
          borderRadius: BorderRadius.circular(4),
          border: Border.all(color: Colors.blue, width: 1),
        ),
        child: Row(
          spacing: 4,
          children: [
            Icon(Icons.loop, color: Colors.blue, size: 16),
            Text(
              "按${isHour ? '工天' : '小时'}算",
              style: TextStyle(color: Colors.blue),
            ),
          ],
        ),
      ),
    );
  }

  /// 切换事件处理
  void onSwitchChanged() {
    ToastUtil.showToast('切换工价类型');
    viewModel.onSwitchChanged();
  }
}
