import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/data/fee_standard_data/repo/model/fee_standard_biz_model.dart';
import 'package:gdjg_pure_flutter/feature/group/dialog/wage_rule_setting/entity/wage_rules_props.dart';
import 'package:gdjg_pure_flutter/feature/group/dialog/wage_rule_setting/view/WageRuleInputView.dart';
import 'package:gdjg_pure_flutter/feature/group/dialog/wage_rule_setting/vm/WageRulesSettingViewModel.dart';
import 'package:gdjg_pure_flutter/iconfont/iconfont.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/toast_util.dart';
import 'package:get/get.dart';

class WageRulesSettingDialog {
  /// 工资规则弹窗
  /// [context] 上下文
  /// [title] 弹窗标题
  /// [workNoteId] 项目id
  /// [workerIds] 工友id
  /// [businessType] 记工类型
  /// [recordNoteType] 账本类型
  /// [onSelected] 选中回调
  static void show({
    required WageRulesProps props,
    Function(FeeStandardBizModel?)? onSelected,
  }) {
    YPRoute.openDialog(
        builder: (context) => WageRulesSettingView(
              props: props,
              onSelected: onSelected,
            ),
        alignment: Alignment.center,
        maskColor: Colors.black.withValues(alpha: 0.5));
  }
}

/// 月份选择器组件
class WageRulesSettingView extends StatefulWidget {
  const WageRulesSettingView({super.key, required this.props, this.onSelected});

  final WageRulesProps props;
  final Function(FeeStandardBizModel?)? onSelected;

  @override
  State<WageRulesSettingView> createState() => _WageRulesSettingView();
}

class _WageRulesSettingView extends State<WageRulesSettingView> {
  final WageRulesSettingViewModel _viewModel = WageRulesSettingViewModel();
  final TextEditingController _workMoneyController = TextEditingController();
  final TextEditingController _overWorkMoneyController =
      TextEditingController();

  // 用于存储ever监听器的引用，以便在dispose时取消
  final List<Worker> _workers = [];

  _onConfirm() async {
    if (widget.props.confirmRequestApi == true) {
      var feeStandard = await _viewModel.updateFeeStandard();
      if (feeStandard) {
        widget.onSelected?.call(_viewModel.feeStandard);
      } else {
        widget.onSelected?.call(null);
      }
    } else {
      widget.onSelected?.call(_viewModel.feeStandard);
    }
    YPRoute.closeDialog();
  }

  _onDelete() async {
    if (widget.props.deleteRequestApi == true) {
      await _viewModel.deleteFeeStandard();
    }
    widget.onSelected?.call(null);
    YPRoute.closeDialog();
  }

  _onCancel() {
    YPRoute.closeDialog();
  }

  @override
  void initState() {
    super.initState();
    _workMoneyController.addListener(() {
      if (mounted) {
        _viewModel.changeWorkPrice(_workMoneyController.text);
      }
    });

    _overWorkMoneyController.addListener(() {
      if (mounted) {
        _viewModel.changeOverPrice(_overWorkMoneyController.text);
      }
    });

    // 存储监听器引用以便后续取消
    _workers.add(ever(_viewModel.us.workMoney, (value) {
      if (mounted && _workMoneyController.text != value) {
        _workMoneyController.text = value;
      }
    }));

    _workers.add(ever(_viewModel.us.overWorkMoney, (value) {
      if (mounted && _overWorkMoneyController.text != value) {
        _overWorkMoneyController.text = value;
      }
    }));

    //注意这里不能放最上边不然上边监听会监听不到
    _viewModel.init(widget.props);
  }

  @override
  void dispose() {
    super.dispose();
    // 取消所有ever监听器
    for (var worker in _workers) {
      worker.dispose();
    }
    // 销毁控制器
    _workMoneyController.dispose();
    _overWorkMoneyController.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      type: MaterialType.transparency,
      child: Center(
        child: Container(
          margin: const EdgeInsets.fromLTRB(24, 0, 24, 0),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: IntrinsicHeight(
            child: Column(
              children: [
                // 标题左对齐
                Container(
                  margin: const EdgeInsets.fromLTRB(16, 16, 16, 0),
                  child: SizedBox(
                    height: 24.h,
                    child: Row(
                      children: [
                        Text(widget.props.title ?? "工价设置",
                            style: TextStyle(
                                fontSize: 16,
                                color: ColorsUtil.black32,
                                fontWeight: FontWeight.w600)),
                        Spacer(),
                        if (widget.props.isShowDelete)
                          GestureDetector(
                            onTap: _onDelete,
                            child: IconFont(
                              IconNames.saasDel,
                              size: 18.h,
                            ),
                          ),
                      ],
                    ),
                  ),
                ),
                // "工价："文本左对齐
                if (widget.props.workers?.isNotEmpty == true)
                  Padding(
                    padding: const EdgeInsets.only(top: 12,left: 16,right: 16),
                    child: Row(
                      children: [
                        Text(
                          widget.props.getWorkerName(),
                          style: TextStyle(
                              fontSize: 16, color: ColorsUtil.primaryColor),
                        ),
                        Text("工价：",
                            style: TextStyle(
                                fontSize: 16, color: ColorsUtil.black32))
                      ],
                    ),
                  ),
                SizedBox(height: 12.h),
                _buildWorkItem(),
                _buildOverWorkItem(),
                SizedBox(height: 12.h),
                _buildBottomView(context),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildWorkItem() {
    return Container(
      height: 50.h,
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      child: Row(
        spacing: 2,
        children: [
          const Text("上班"),
          SizedBox(width: 2),
          GestureDetector(
              onTap: () {
                FocusScope.of(context).unfocus();
                _viewModel.showWorkTimeDialog();
              },
              child: Obx(() => _buildTimeView(_viewModel.us.workHour))),
          const Text("小时="),
          WageRuleInputView(
              viewModel: _viewModel, controller: _workMoneyController),
          const Text("元=1个工"),
        ],
      ),
    );
  }

  Widget _buildOverWorkItem() {
    return Obx(() {
      return Container(
        height: 50.h,
        padding: EdgeInsets.symmetric(horizontal: 16.w),
        child: Row(
          spacing: 2,
          children: [
            const Text("加班"),
            SizedBox(width: 2),
            if (_viewModel.us.isHour) _buildHourType() else _buildDayType(),
            Spacer(), // 添加弹性空间，将切换按钮推到右侧
            Obx(() => _buildWageTypeSwitch(_viewModel.us.isHour)),
          ],
        ),
      );
    });
  }

  /// 按小时计算
  Widget _buildHourType() {
    return Row(
      spacing: 2,
      children: [
        GestureDetector(
            onTap: () {
              FocusScope.of(context).unfocus();
              _viewModel.showOvertimeDialog();
            },
            child: Obx(() => _buildTimeView(_viewModel.us.overWorkHour))),
        const Text("小时=1个工"),
      ],
    );
  }

  /// 按工天计算
  Widget _buildDayType() {
    return Row(
      spacing: 2,
      children: [
        const Text("1小时="),
        WageRuleInputView(
          viewModel: _viewModel,
          controller: _overWorkMoneyController,
        ),
        const Text("元"),
      ],
    );
  }

  Widget _buildBottomView(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        border: Border(top: BorderSide(color: Color(0xFFE6E6E6), width: 0.5.w)),
      ),
      child: SizedBox(
        height: 48.h,
        child: Row(
          children: [
            Expanded(
              child: TextButton(
                onPressed: _onCancel,
                style: TextButton.styleFrom(
                  // 去掉默认的椭圆形点击效果
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.zero,
                  ),
                  overlayColor: Colors.grey.withOpacity(0), // 自定义点击效果
                ),
                child: Text(
                  '取消',
                  style: TextStyle(
                    color: ColorsUtil.black65,
                  ),
                ),
              ),
            ),
            VerticalDivider(width: 1.w, color: Color(0xFFE6E6E6)),
            Expanded(
              child: TextButton(
                onPressed: _onConfirm,
                style: TextButton.styleFrom(
                  // 去掉默认的椭圆形点击效果
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.zero,
                  ),
                  overlayColor: Colors.grey.withOpacity(0), // 自定义点击效果
                ),
                child: Text('确定',
                    style: TextStyle(
                      color: ColorsUtil.primaryColor,
                      fontWeight: FontWeight.bold,
                    )),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建时间选择view
  Widget _buildTimeView(String value) {
    return Container(
      height: 30,
      width: 40,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(6),
        border: Border.all(color: Colors.grey, width: 0.5),
      ),
      child: Align(
        alignment: Alignment.center,
        child: Text(
          value,
          style: TextStyle(color: Colors.blue),
        ),
      ),
    );
  }

  /// 工价类型切换
  Widget _buildWageTypeSwitch(bool isHour) {
    return GestureDetector(
      onTap: onSwitchChanged,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 4),
        decoration: BoxDecoration(
          color: Color(0xFFedf4ff),
          borderRadius: BorderRadius.circular(4),
          border: Border.all(color: Colors.blue, width: 1),
        ),
        child: Row(
          spacing: 4,
          children: [
            Icon(Icons.loop, color: Colors.blue, size: 16),
            Text(
              "按${isHour ? '工天' : '小时'}算",
              style: TextStyle(color: Colors.blue),
            ),
          ],
        ),
      ),
    );
  }

  /// 切换事件处理
  void onSwitchChanged() {
    ToastUtil.showToast('切换工价类型');
    _viewModel.onSwitchChanged();
  }
}
