import 'package:gdjg_pure_flutter/data/fee_standard_data/repo/model/fee_standard_biz_model.dart';
import 'package:gdjg_pure_flutter/feature/group/common_params_model/worker_model.dart';
import 'package:gdjg_pure_flutter/model/RecordNoteType.dart';
import 'package:gdjg_pure_flutter/model/RecordType.dart';

class WageRulesProps {
  /// 工友所在项目名称
  String? title;

  ///是否需要请求工资规则 默认请求
  bool confirmRequestApi;
  ///点击 x 会不会请求接口。默认根据 confirmBtnType 参数来, 1 会请求接口，0: 不处理, -1 默认
  bool deleteRequestApi;
  ///是否显示删除按钮
  bool isShowDelete;
  FeeStandardBizModel? feeStandard;
  String? workNoteId;
  List<WorkerModel>? workers;
  RwaRecordType? businessType;
  RecordNoteType? recordNoteType;
  int requestType;

  WageRulesProps({
    this.title,
    this.isShowDelete = false,
    this.confirmRequestApi = false,
    this.deleteRequestApi = false,
    this.feeStandard,
    this.workNoteId,
    this.workers,
    this.businessType,
    this.recordNoteType,
    this.requestType = 1,
  });

  getWorkerName() {
    return workers?.map((e) => e.workerName).join(",") ?? '';
  }
}
