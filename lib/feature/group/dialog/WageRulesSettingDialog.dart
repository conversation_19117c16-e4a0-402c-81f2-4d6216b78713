import 'package:flutter/material.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';

class WageRulesSettingDialog extends Dialog {
  /// 弹窗标题
  final String title;

  const WageRulesSettingDialog({super.key, required this.title});

  @override
  Widget build(BuildContext context) {
    return Material(
      type: MaterialType.transparency,
      child: Container(
        width: 300,
        height: 200,
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          spacing: 16,
          children: [
            Text(title),
            const Text("工价："),
            _buildWorkItem(),
            _buildOverWorkItem(),
            _buildBottomView(context),
          ],
        ),
      ),
    );
  }

  Widget _buildWorkItem() {
    return SizedBox(
      height: 50,
      child: Row(
        children: [
          const Text("上班"),
          _buildTimeView("10"),
          const Text("小时="),
          _buildMoneyInputView(300.00),
          const Text("元=1个工"),
        ],
      ),
    );
  }

  Widget _buildOverWorkItem() {
    return SizedBox(
      height: 50,
      child: Row(
        children: [
          const Text("加班"),
          _buildTimeView("6"),
          const Text("小时=1个工"),
          _buildMoneyInputView(300.00),
          const Text("元=1个工"),
        ],
      ),
    );
  }

  Widget _buildBottomView(BuildContext context) {
    return SizedBox(
      height: 40,
      child: Row(
        children: [
          Expanded(
            child: TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: const Text('取消'),
            ),
          ),
          Expanded(
            child: TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: const Text('确定'),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建时间选择view
  Widget _buildTimeView(String value) {
    return Container(
      height: 40,
      width: 60,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey, width: 0.5),
      ),
      child: Text(value),
    );
  }

  /// 工价输入框: value <= 1000
  Widget _buildMoneyInputView(double value) {
    return Container(
      height: 40,
      width: 120,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey, width: 0.5),
      ),
      child: TextField(
        keyboardType: TextInputType.number,
        decoration: InputDecoration(
          border: InputBorder.none,
          hintText: '0.00',
          hintStyle: TextStyle(
            fontSize: 16,
            color: ColorsUtil.hintFontColor,
          ),
          contentPadding:
              const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
        ),
      ),
    );
  }
}
