import 'package:gdjg_pure_flutter/feature/group/common_params_model/pro_model.dart';
import 'package:gdjg_pure_flutter/feature/group/common_params_model/worker_model.dart';
import 'package:gdjg_pure_flutter/model/RecordNoteType.dart';
import 'package:gdjg_pure_flutter/model/RecordType.dart';

/// @date 2025/06/30
/// @description GroupEditWage页入参
class GroupEditWageProps {
  String? workNoteName;

  ///账本类型
  RecordNoteType? recordNoteType;

  ///
  String? startTime;

  ///
  String? endTime;

  ///带班id
  double? deptId;

  ///分项id(只有工量记工时有用)
  String? unitWorkType;

  ///记工类型
  List<RwaRecordType>? businessType;

  ///工友
  List<WorkerModel>? workers;

  ///多个项目
  List<GroupProjectModel>? projects;

  GroupEditWageProps({
    this.workNoteName,
    this.recordNoteType,
    this.startTime,
    this.endTime,
    this.deptId,
    this.unitWorkType,
    this.businessType,
    this.workers,
    this.projects,
  });
}
