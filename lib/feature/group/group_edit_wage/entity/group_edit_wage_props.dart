import 'package:gdjg_pure_flutter/feature/group/common_params_model/pro_model.dart';
import 'package:gdjg_pure_flutter/feature/group/common_params_model/worker_model.dart';
import 'package:gdjg_pure_flutter/model/RecordNoteType.dart';
import 'package:gdjg_pure_flutter/model/RecordType.dart';

/// @date 2025/06/30
/// @description GroupEditWage页入参
class GroupEditWageProps {
  String? workNoteName;

  ///账本类型
  RecordNoteType? recordNoteType;

  ///
  DateTime? startTime;

  ///
  DateTime? endTime;

  ///带班id
  double? deptId;

  ///分项id(只有工量记工时有用)
  String? unitWorkType;

  ///记工类型
  RwaRecordType? businessType;

  ///工友
  List<WorkerModel>? workers;

  ///多个项目
  List<GroupProjectModel>? projects;

  GroupEditWageProps({
    this.workNoteName,
    this.recordNoteType,
    this.startTime,
    this.endTime,
    this.deptId,
    this.unitWorkType,
    this.businessType,
    this.workers,
    this.projects,
  });

  ///只有点工和包工才回去请求工资规则
  isRequestWageRule() {
    return businessType == RwaRecordType.workDays ||
        businessType == RwaRecordType.packageWork;
  }

  isWorkDays(){
    return businessType == RwaRecordType.workDays;
  }

  ///修改工资规则需要根据记工类型去显示标题
  getTitle() {
    if (workNoteName?.isNotEmpty == true) {
      return workNoteName;
    }
    if (businessType == RwaRecordType.workDays) {
      return '修改点工工资';
    } else if (businessType == RwaRecordType.packageWork) {
      return '修改包工工资';
    } else if (businessType == RwaRecordType.workLoad) {
      return '修改工量工资';
    }
    return '修改工资';
  }
}
