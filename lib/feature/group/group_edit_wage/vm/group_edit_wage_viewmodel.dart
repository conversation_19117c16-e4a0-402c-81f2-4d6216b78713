import "package:gdjg_pure_flutter/common_uistate/statistice_helper.dart";
import "package:gdjg_pure_flutter/common_uistate/statistics_ui_state.dart";
import "package:gdjg_pure_flutter/data/group_data/group_pro_bill/ds/model/param/group_business_get_group_business_count_param_model.dart";
import "package:gdjg_pure_flutter/feature/group/group_edit_wage/entity/group_edit_wage_props.dart";
import "package:gdjg_pure_flutter/model/RecordType.dart";
import "package:get/get.dart";
import "../ui_rep/group_edit_wage_ui_rep.dart";
import "protocol/group_edit_wage_ui_state.dart";

/// @date 2025/06/30
/// @description GroupEditWage页ViewModel
/// 1. 处理数据的加载状态，如loading和错误视图等
/// 2. 观测UIRep中的业务实体转为UIState
/// 3. 接受来自view的意图并分发事件进行处理（暂无）
/// 4. 对View层暴露事件（操作视图和处理一些与系统权限交互等场景）
class GroupEditWageViewModel {
  var isLoading = false.obs;
  var isShowError = false.obs;
  var uiState = GroupEditWageUIState().obs;
  var uiRep = GroupEditWageUIRep();
  GroupEditWageProps? props;

  GroupEditWageViewModel() {
    ever(uiRep.entity, (value) {
      convertEntityToUIState(value);
    });
  }

  init(GroupEditWageProps? props) {
    this.props = props;
    fetchData();
  }

  ///更新开始和结束时间
  void updateDateRange(
      DateTime startTime, DateTime endTime, List<RwaRecordType>? businessType) {
    props?.businessType = businessType;
    props?.startTime = _formatDate(startTime);
    props?.endTime = _formatDate(endTime);
    fetchData(); // 请求新数据
  }

  ///格式化日期
  String _formatDate(DateTime date) {
    return "${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}";
  }

  /// 初始化数据同步，如果不需要loading，则在这里去掉
  void fetchData() async {
    isLoading.value = true;
    try {
      var workNoteId = props?.projects
              ?.map((worker) => worker.workNoteId.toString())
              .join(',') ??
          '';
      var businessTypeStr = props?.businessType
              ?.map((type) => type.code.value.toString())
              .join(',') ??
          '';
      var workerIds = props?.workers
              ?.map((worker) => worker.workerId.toString())
              .join(',') ??
          '';
      var businessCountParams = GroupBusinessGetGroupBusinessCountParamModel()
        ..work_note = workNoteId ?? ''
        ..start_time = props?.startTime ?? ''
        ..end_time = props?.endTime ?? ''
        ..business_type = businessTypeStr
        ..worker_ids = workerIds;
      await uiRep.getGroupBusinessCount(businessCountParams);
      // 这里的result仅做弹窗之类的处理，不在此处转为UI实体
    } catch (e) {
      uiState.value.isShowError = true;
    } finally {
      isLoading.value = false;
    }
  }

  /// 将实体数据转换为UIState
  /// @param entity
  /// @returns
  void convertEntityToUIState(GroupEditWageUIRepEntity entity) {
    var count = entity.count;
    List<StatisticsItemUIState> statisticsList =
        StatisticsUIStateHelper.buildStatisticsItem(count);
    uiState.value.statisticsList = statisticsList;
    uiState.refresh();
  }
}
