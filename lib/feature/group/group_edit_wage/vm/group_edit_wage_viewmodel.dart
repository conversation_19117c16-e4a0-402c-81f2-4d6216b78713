import "package:collection/collection.dart";
import "package:flutter/widgets.dart";
import "package:gdjg_pure_flutter/data/common_data/biz/count_biz_model.dart";
import "package:gdjg_pure_flutter/data/fee_standard_data/ds/model/params/fee_standard_param_model.dart";
import "package:gdjg_pure_flutter/data/fee_standard_data/ds/model/params/fee_standard_recalculate_team_param_model.dart";
import "package:gdjg_pure_flutter/data/fee_standard_data/ds/model/params/fee_unit_recalculate_team_param_model.dart";
import "package:gdjg_pure_flutter/data/fee_standard_data/repo/fee_standard_repo.dart";
import "package:gdjg_pure_flutter/data/fee_standard_data/repo/model/fee_standard_biz_model.dart";
import "package:gdjg_pure_flutter/data/group_data/group_pro_bill/ds/model/param/group_business_get_group_business_count_param_model.dart";
import "package:gdjg_pure_flutter/data/group_data/group_pro_bill/repo/group_pro_bill_repo.dart";
import "package:gdjg_pure_flutter/data/note_time_data/note_time_repo.dart";
import "package:gdjg_pure_flutter/feature/group/common_uistate/statistice_helper.dart";
import "package:gdjg_pure_flutter/feature/group/common_uistate/statistics_ui_state.dart";
import "package:gdjg_pure_flutter/feature/group/group_edit_wage/entity/group_edit_wage_props.dart";
import "package:gdjg_pure_flutter/feature/group/group_edit_wage/vm/group_edit_wage_us.dart";
import "package:gdjg_pure_flutter/feature/group/group_event_bus/group_edit_wage_event_bus_model.dart";
import "package:gdjg_pure_flutter/model/RecordType.dart";
import "package:gdjg_pure_flutter/utils/event_bus_util/event_bus_util.dart";
import "package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart";
import "package:gdjg_pure_flutter/utils/system_util/date_util.dart";
import "package:gdjg_pure_flutter/utils/system_util/string_util.dart";
import "package:gdjg_pure_flutter/widget/hour_selector_dialog.dart";

/// @date 2025/06/30
/// @description GroupEditWage页ViewModel
/// 1. 处理数据的加载状态，如loading和错误视图等
/// 2. 观测UIRep中的业务实体转为UIState
/// 3. 接受来自view的意图并分发事件进行处理（暂无）
/// 4. 对View层暴露事件（操作视图和处理一些与系统权限交互等场景）
class GroupEditWageViewModel {
  /// 统计相关
  final _groupProBillRepo = GroupProBillRepo();

  /// 工资规则相关
  final _feeStandardRepo = FeeStandardRepo();
  final _noteTimeRepo = NoteTimeRepo();
  final us = GroupEditWageUs();
  DateTime? _netStartTime;
  DateTime? _startTime = DateTime.now();
  DateTime _endTime = DateTime.now();
  CountBizModel? _countBizModel;
  FeeStandardBizModel? _feeStandardBizModel;
  GroupEditWageProps? _props;
  var _unitPrice = '';

  /// 获取标题
  String get title => _props?.getTitle();

  /// 是否是点工或者包工
  bool get _isWorkDaysAndPackage => _props?.isRequestWageRule() == true;

  init(GroupEditWageProps? props) {
    _props = props;
    _startTime = props?.startTime ?? _startTime;
    _endTime = props?.endTime ?? _endTime;
    us.setStartTime(_startTime);
    us.setEndTime(_endTime);
    _initWageView();
    fetchData();
  }

  void _initWageView() {
    //设置点工包工需要显示的布局
    if (_isWorkDaysAndPackage) {
      us.setShowWorkPackageView(true);
      final isWorkDays = _props?.businessType == RwaRecordType.workDays;
      //设置加班类型
      us.setOverType(isWorkDays);
      //是否显示切换按钮
      us.setShowSwitchBtn(isWorkDays);
    } else if (_props?.businessType == RwaRecordType.workLoad) {
      us.setShowWorkLoadView(true);
    }
  }

  ///更新开始和结束时间
  void updateDateRange(DateTime startTime, DateTime endTime) {
    _startTime = startTime;
    _endTime = endTime;
    fetchData(); // 请求新数据
  }

  /// 初始化数据同步，返回同步是否成功的状态给UI层
  fetchData() async {
    if (_startTime == null) {
      await fetchFirstTime();
    }
    var workNoteId = _props?.projects
            ?.map((worker) => worker.workNoteId.toString())
            .join(',') ??
        '';
    var workerIds = _props?.workers
            ?.map((e) => e.workerId.toString())
            .join(',')
            .trimTrailingZeros() ??
        '';
    var countParams = GroupBusinessGetGroupBusinessCountParamModel()
      ..work_note = workNoteId
      ..start_time = DateUtil.formatStartDate(_startTime)
      ..end_time = DateUtil.formatEndDate(_endTime)
      ..business_type = _props?.businessType?.code.value.toString() ?? ''
      ..worker_ids = workerIds;
    var result = await _groupProBillRepo.getGroupBusinessCount(countParams);
    if (result.isOK()) {
      CountBizModel? data = result.getSucData();
      _countBizModel = data;
      convertEntityToUIState(data);
    }

    //点工包工
    if (_isWorkDaysAndPackage) {
      var feeParams = FeeStandardParamModel()
        ..workNoteId = workNoteId
        ..businessType = _props?.businessType?.code.value.toString() ?? ''
        ..workerId = workerIds
        ..type = _props?.recordNoteType?.value.toString() ?? "1";
      var feeResult = await _feeStandardRepo.getNewFeeStandard(feeParams);
      if (feeResult.isOK()) {
        var data = feeResult.getSucData();
        _feeStandardBizModel = data;
        convertFeeEntityToUIState(data);
        _calculateTotalWage(_countBizModel, _feeStandardBizModel);
      }
    } else {
      final unit = _getCurUnitEntity();
      final price = unit?.lastUnitPrice ?? "0.00";
      _unitPrice = price;
      us.setUnitPrice(price);
      us.setUnitWorkTypeName(unit?.unitWorkTypeName ?? "");
      _calculateAmountWorkTotalWage(price, _countBizModel);
    }
  }

  ///获取开始时间和结束时间
  Future<void> fetchFirstTime() async {
    DateTime endTime = DateTime.now();
    var workNoteId =
        _props?.projects?.map((e) => e.workNoteId.toString()).join(',') ?? '';
    var resultTime = await _noteTimeRepo.getNoteFirstTime(workNoteId);
    if (resultTime.isOK()) {
      var time = resultTime.getSucData()?.date ?? "2020-01-01";
      var startTime = DateTime.parse(time);
      _startTime = startTime;
      _netStartTime = startTime;
      _endTime = endTime;
      us.setStartTime(startTime);
      us.setEndTime(endTime);
    }
  }

  CountUnitEntity? _getCurUnitEntity() {
    final unit = _countBizModel?.unit?.countUnit;
    if (unit != null) {
      return unit.firstWhereOrNull(
          (element) => element.unitWorkType == _props?.unitWorkType);
    }
    return null;
  }

  /// 将实体数据转换为UIState
  /// @param entity
  /// @returns
  void convertEntityToUIState(CountBizModel? count) {
    List<StatisticsItemUIState> statisticsList =
        StatisticsUIStateHelper.buildStatisticsItem(
      count,
      unitWorkType: _props?.unitWorkType,
      isShowUnsettle: false,
    );
    us.setStatisticsList(statisticsList);
  }

  void convertFeeEntityToUIState(FeeStandardBizModel? fee) {
    if (fee == null) return;
    us.setWorkHour(fee.workingHoursStandard.trimTrailingZeros());
    us.setWorkPrice(fee.workingHoursPrice);
    us.setOverHour(fee.overtimeHoursStandard.trimTrailingZeros());
    us.setOverPrice(fee.overtimeHoursPrice);
  }

  /// 修改班组点工包工工资规则
  Future<void> _recalculateTeamWage() async {
    final params = FeeStandardRecalculateTeamParamModel(
      workNoteId:
          _props?.projects?.map((e) => e.workNoteId.toString()).join(',') ?? '',
      startTime: DateUtil.formatStartDate(_startTime),
      endTime: DateUtil.formatEndDate(_endTime),
      businessType: _props?.businessType?.code.value.toString() ?? '',
      workerId: _props?.workers
              ?.map((e) => e.workerId.toString())
              .join(',')
              .trimTrailingZeros() ??
          '',
      workingHoursStandard: _feeStandardBizModel?.workingHoursStandard ?? '',
      workingHoursPrice: _feeStandardBizModel?.workingHoursPrice ?? '0',
      overtimeType:
          _feeStandardBizModel?.overtimeType.trimTrailingZeros() ?? "2",
      overtimeHoursStandard: _feeStandardBizModel?.overtimeHoursStandard ?? '',
      overtimeHoursPrice: _feeStandardBizModel?.overtimeHoursPrice ?? '0',
    );
    final result = await _feeStandardRepo.recalculateTeam(params);
    if (result.isOK()) {
      EventBusUtil.emit(GroupEditWageEventBusModel());
      YPRoute.closePage();
    }
  }

  /// 修改班组工量工资规则
  Future<void> _recalculateUnitTeamWage() async {
    final params = FeeUnitStandardRecalculateTeamParamModel(
      work_note:
          _props?.projects?.map((e) => e.workNoteId.toString()).join(',') ?? '',
      start_time: DateUtil.formatStartDate(_startTime),
      end_time: DateUtil.formatEndDate(_endTime),
      worker_id: _props?.workers
              ?.map((e) => e.workerId.toString())
              .join(',')
              .trimTrailingZeros() ??
          '',
      unit_price: _unitPrice,
      unit_work_type: _props?.unitWorkType ?? '',
    );
    final result = await _feeStandardRepo.recalculateUnitTeam(params);
    if (result.isOK()) {
      EventBusUtil.emit(GroupEditWageEventBusModel());
      YPRoute.closePage();
    }
  }

  /// 切换加班类型
  void setOvertimeTap() {
    us.setOverType(!us.overType);
    if (us.overType) {
      _feeStandardBizModel?.overtimeType = 1;
    } else {
      _feeStandardBizModel?.overtimeType = 2;
    }
  }

  /// 修改上班单价
  void changeWorkPrice(String value) {
    if (value == us.workPrice.value) return;
    us.setWorkPrice(value);
    _feeStandardBizModel?.workingHoursPrice = value;
    _calculateTotalWage(_countBizModel, _feeStandardBizModel);
  }

  /// 修改加班单价
  void changeOverPrice(String value) {
    if (value == us.overPrice.value) return;
    us.setOverPrice(value);
    _feeStandardBizModel?.overtimeHoursPrice = value;
    _calculateTotalWage(_countBizModel, _feeStandardBizModel);
  }

  /// 修改工量单价
  void changeUnitPrice(String value) {
    if (value == us.unitPrice.value) return;
    _unitPrice = value;
    us.setUnitPrice(value);
    _calculateAmountWorkTotalWage(value, _countBizModel);
  }

  ///提交按钮
  void setCommitTap() {
    //点工包工工资规则修改
    if (_isWorkDaysAndPackage) {
      _recalculateTeamWage();
    } else {
      _recalculateUnitTeamWage();
    }
  }


  ///计算工资
  void _calculateTotalWage(CountBizModel? count, FeeStandardBizModel? rule) {
    if (count == null || rule == null) {
      us.setTotalMoney("");
      return;
    }
    final isWorkDays = _props?.businessType == RwaRecordType.workDays;

    ///计算点工工资
    //上班工天
    var workTime = isWorkDays
        ? (double.tryParse(count.spotWork?.workTime ?? "0") ?? 0)
        : double.tryParse(count.contractor?.contractorWorkTime ?? "0") ?? 0;
    //加班工天
    var overTime = isWorkDays
        ? (double.tryParse(count.spotWork?.overTimeWork ?? "0") ?? 0)
        : 0;

    // 上班小时
    var workTimeHour = isWorkDays
        ? (double.tryParse(count.spotWork?.workTimeHour ?? "0") ?? 0)
        : double.tryParse(count.contractor?.contractorWorkTimeHour ?? "0") ?? 0;
    // 加班小时
    var overTimeHour = isWorkDays
        ? (double.tryParse(count.spotWork?.overTime ?? "0") ?? 0)
        : double.tryParse(count.contractor?.contractorOverTime ?? "0") ?? 0;
    // 总工天(上班+加班)
    final allTime = workTime + overTime;

    // 计算工钱
    // 上班1个工多少钱
    final workingHoursPrice = double.tryParse(rule.workingHoursPrice) ?? 0;
    // 上班多少小时1个工
    final workingHoursStandard =
        double.tryParse(rule.workingHoursStandard) ?? 0;
    // 加班1个小时多少钱(默认加班按小时)
    var overtimeHoursPrice = double.tryParse(rule.overtimeHoursPrice) ?? 0;

    if (rule.overtimeType == 1) {
      // 如果是加班按工天
      // 加班多少小时1个工
      final overtimeHoursStandard =
          double.tryParse(rule.overtimeHoursStandard) ?? 0;
      // 加班1个小时多少钱
      overtimeHoursPrice = workingHoursPrice / overtimeHoursStandard;
    }
    // 上班工钱+加班工天的工钱
    final ntAmount = allTime * workingHoursPrice +
        workTimeHour * workingHoursPrice / workingHoursStandard;
    // 加班工钱
    final otAmount = overTimeHour * overtimeHoursPrice;
    // 总工钱
    final amount = ntAmount + otAmount;
    // 更新总工钱
    us.setTotalMoney(amount.formatDoubleToMoney());
  }

  /// 计算工量记工工资总计
  void _calculateAmountWorkTotalWage(
      String? unitPrice, CountBizModel? countBizModel) {
    if (unitPrice == null || countBizModel == null) {
      us.setTotalMoney("");
      return;
    }
    final unit = _getCurUnitEntity();
    final price = double.tryParse(unitPrice) ?? 0;
    final count = double.tryParse(unit?.count.toString() ?? "0") ?? 0;
    // 计算总金额并保留两位小数
    final totalMoney = ((price * count) * 100).round() / 100;
    us.setTotalMoney(totalMoney.trimTrailingZeros());
  }

  onSelectAllTap() async {
    if (_netStartTime == null) {
      await fetchFirstTime();
    } else {
      DateTime endTime = DateTime.now();
      _startTime = _netStartTime;
      _endTime = endTime;
      us.setStartTime(_netStartTime);
      us.setEndTime(endTime);
    }
    fetchData();
  }

  _setWorkTime(double workTime) {
    us.setWorkHour(workTime.toStringAsFixed(1));
    _feeStandardBizModel?.workingHoursStandard = workTime.toStringAsFixed(1);
    _calculateTotalWage(_countBizModel, _feeStandardBizModel);
  }

  _setOverTime(double overTime) {
    us.setOverHour(overTime.toStringAsFixed(1));
    _feeStandardBizModel?.overtimeHoursStandard = overTime.toStringAsFixed(1);
    _calculateTotalWage(_countBizModel, _feeStandardBizModel);
  }

  /// 显示上班选择小时弹窗
  void showWorkTimeDialog(BuildContext context) {
    YPRoute.openDialog(
      builder: (context) => HourSelectorDialog(
        initHour: double.tryParse(us.workHour),
        onConfirm: (result) {
          _setWorkTime(result.value);
        },
      ),
      alignment: Alignment.bottomCenter,
    );
  }

  /// 显示加班选择小时弹窗
  void showOverTimeDialog(BuildContext context) {
    YPRoute.openDialog(
      builder: (context) => HourSelectorDialog(
        initHour: double.tryParse(us.workHour),
        onConfirm: (result) {
          _setOverTime(result.value);
        },
      ),
      alignment: Alignment.bottomCenter,
    );
  }
}
