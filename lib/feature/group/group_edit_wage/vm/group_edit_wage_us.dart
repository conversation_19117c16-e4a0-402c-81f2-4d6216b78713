import 'package:gdjg_pure_flutter/feature/group/common_uistate/statistics_ui_state.dart';
import 'package:get/get.dart';

class GroupEditWageUs {

  final _startTime = DateTime.now().obs;

  final _endTime = DateTime.now().obs;

  /// 显示点工包工修改工资布局
  final _showWorkPackageView = false.obs;

  /// 显示工量修改工资布局
  final _showWorkLoadView = false.obs;

  /// 加班类型，true按工天算，false按小时算
  final _overType = true.obs;

  final _workHour = "".obs;

  final workPrice = "".obs;

  final _overHour = "".obs;

  final overPrice = "".obs;

  final unitPrice = "".obs;

  final _unitWorkTypeName = "".obs;

  final _totalMoney = "".obs;

  ///显示点工切换按钮布局
  final _showWorkSwitchBtn = false.obs;

  final _statisticsList = <StatisticsItemUIState>[].obs;

  DateTime get startTime => _startTime.value;

  DateTime get endTime => _endTime.value;

  bool get showWorkPackageView => _showWorkPackageView.value;

  bool get showWorkLoadView => _showWorkLoadView.value;

  bool get overType => _overType.value;

  String get workHour => _workHour.value.toString();

  String get overHour => _overHour.value.toString();

  String get totalMoney => _totalMoney.value.toString();

  bool get showSwitchBtn => _showWorkSwitchBtn.value;

  String get unitWorkTypeName => _unitWorkTypeName.value;

  List<StatisticsItemUIState> get statisticsList => _statisticsList.value;

  setStartTime(DateTime? date) {
    if (date == null) {
      return;
    }
    _startTime.value = date;
    _startTime.refresh();
  }

  setEndTime(DateTime? date) {
    if (date == null) {
      return;
    }
    _endTime.value = date;
    _endTime.refresh();
  }

  setShowWorkPackageView(bool showWorkPackageView) {
    _showWorkPackageView.value = showWorkPackageView;
    _showWorkPackageView.refresh();
  }

  setShowWorkLoadView(bool showWorkLoadView) {
    _showWorkLoadView.value = showWorkLoadView;
    _showWorkLoadView.refresh();
  }

  setWorkHour(String workHour) {
    _workHour.value = workHour;
    _workHour.refresh();
  }

  setWorkPrice(String workPrice) {
    this.workPrice.value = workPrice;
    this.workPrice.refresh();
  }

  setOverHour(String overHour) {
    _overHour.value = overHour;
    _overHour.refresh();
  }

  setOverPrice(String overPrice) {
    this.overPrice.value = overPrice;
    this.overPrice.refresh();
  }

  setOverType(bool overtime) {
    _overType.value = overtime;
    _overType.refresh();
  }

  setUnitPrice(String unitPrice) {
    this.unitPrice.value = unitPrice;
    this.unitPrice.refresh();
  }

  void setTotalMoney(String amount) {
    _totalMoney.value = amount;
    _totalMoney.refresh();
  }

  void setShowSwitchBtn(bool showSwitchBtn) {
    _showWorkSwitchBtn.value = showSwitchBtn;
    _showWorkSwitchBtn.refresh();
  }

  setUnitWorkTypeName(String unitWorkTypeName) {
    _unitWorkTypeName.value = unitWorkTypeName;
    _unitWorkTypeName.refresh();
  }

  setStatisticsList(List<StatisticsItemUIState> statisticsList) {
    _statisticsList.clear();
    _statisticsList.addAll(statisticsList);
    _statisticsList.refresh();
  }
}
