import 'package:get/get.dart';

/// @date 2025/06/30
/// @description GroupEditWage页UI仓库
/// 1.为vm提供业务数据
/// 2.解耦视图层与业务仓库之间的耦合
class GroupEditWageUIRep {
  /// 实体数据
  var entity = GroupEditWageUIRepEntity().obs;

  /// 初始化数据同步，返回同步是否成功的状态给UI层
  /// 如果成功，则将业务数据存储在entity中
  Future<GroupEditWageUIRepEntity> fetchData() async {
    // 调用网络的方法获取数据
    await Future.delayed(const Duration(milliseconds: 2000));
    // 返回成功的情况
    entity.value = GroupEditWageUIRepEntity(data: "success");
    // 模拟异常情况
    // throw Exception("error");
    return entity.value;
  }
}

class GroupEditWageUIRepEntity {
  String? data;
  GroupEditWageUIRepEntity({this.data});

  @override
  String toString() {
    return 'GroupEditWageUIRepEntity{data: $data}';
  }
}
