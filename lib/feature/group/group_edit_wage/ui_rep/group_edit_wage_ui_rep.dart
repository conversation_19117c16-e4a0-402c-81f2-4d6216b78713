import 'package:gdjg_pure_flutter/data/group_data/group_calendar/repo/model/group_calendar_biz_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_pro_bill/ds/model/param/group_business_get_group_business_count_param_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_pro_bill/repo/group_pro_bill_repo.dart';
import 'package:get/get.dart';

/// @date 2025/06/30
/// @description GroupEditWage页UI仓库
/// 1.为vm提供业务数据
/// 2.解耦视图层与业务仓库之间的耦合
class GroupEditWageUIRep {
  final _groupProBillRepo = GroupProBillRepo();

  /// 实体数据
  var entity = GroupEditWageUIRepEntity().obs;

  /// 初始化数据同步，返回同步是否成功的状态给UI层
  /// 如果成功，则将业务数据存储在entity中
  Future<GroupEditWageUIRepEntity> getGroupBusinessCount(
      GroupBusinessGetGroupBusinessCountParamModel params) async {
    // 调用网络的方法获取数据
    var result = await _groupProBillRepo.getGroupBusinessCount(params);
    if (result.isOK()) {
      CountBizModel? data = result.getSucData();
      if (data != null) {
        entity.value.count = data;
        entity.refresh();
      }
    } else {
      // 模拟异常情况
      // throw Exception("error");
    }
    return entity.value;
  }
}

class GroupEditWageUIRepEntity {
  CountBizModel? count;

  GroupEditWageUIRepEntity({this.count});
}
