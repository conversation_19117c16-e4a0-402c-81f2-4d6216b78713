import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/feature/group/group_pro_bill/view/group_bill_statistics_view.dart';
import 'package:gdjg_pure_flutter/utils/route_util/base_page/base_ful_page.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/appbar_util.dart';
import 'package:gdjg_pure_flutter/widget/combined_filter_widget.dart';
import 'entity/group_edit_wage_props.dart';
import 'vm/group_edit_wage_viewmodel.dart';

/// @date 2025/06/30
/// @param props 页面路由参数
/// @returns
/// @description GroupEditWage页面入口 班组项目修改工资
class GroupEditWagePage extends BaseFulPage {
  const GroupEditWagePage({super.key}) : super(appBar: null);

  @override
  createState() => _GroupProBillPage();
}

class _GroupProBillPage<GroupEditWagePage> extends BaseFulPageState {
  final GroupEditWageViewModel viewModel = GroupEditWageViewModel();

  @override
  onPageRoute(Object? routeParams, bool fromLaunchTask) {
    var props = routeParams as GroupEditWageProps?;
  }

  @override
  Widget yBuild(BuildContext context) {
    return Scaffold(
      appBar: AppBarUtil.buildCommonAppBar(
        title: "",
        // title: viewModel.props?.workNoteName ?? "",
        onBackTap: () => YPRoute.closePage(),
      ),
      body: contentView(),
    );
  }

  /// 实际展示的视图，需要处理：
  /// 1. 画出UI
  /// 2. 绑定UIState
  /// 3. 处理事件监听
  /// 4. 向VM传递来自View层的事件
  Widget contentView() {
    // 进行事件处理
    // handleGroupEditWageVMEvent(props.vm)
    return Column(children: [
      Divider(height: 8.h, color: Color(0xFFF0F0F0), thickness: 8.h),
      CombinedFilterWidget(
        onFilterChanged: (filter) {},
      ),
      Divider(height: 8.h, color: Color(0xFFF0F0F0), thickness: 8.h),
      Expanded(
        child: Column(
          children: [
            _buildStatisticsView(),
          ],
        ),
      )
    ]);
  }

  Widget _buildStatisticsView() {
    return Container(
      color: Colors.white,
      child: GroupBillStatisticsView(
        list: viewModel.uiState.value.statisticsList,
      ),
    );
  }
}
