import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/feature/group/group_pro_bill/view/group_bill_statistics_view.dart';
import 'package:gdjg_pure_flutter/utils/route_util/base_page/base_ful_page.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/utils/system_util/font_util.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/appbar_util.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';
import 'package:gdjg_pure_flutter/widget/combined_filter_widget.dart';
import 'package:get/get.dart';

import 'entity/group_edit_wage_props.dart';
import 'vm/group_edit_wage_viewmodel.dart';

/// @date 2025/06/30
/// @param props 页面路由参数
/// @returns
/// @description GroupEditWage页面入口 班组项目修改工资
class GroupEditWagePage extends BaseFulPage {
  const GroupEditWagePage({super.key}) : super(appBar: null);

  @override
  createState() => _GroupProBillPage();
}

class _GroupProBillPage<GroupEditWagePage> extends BaseFulPageState {
  final GroupEditWageViewModel _viewModel = GroupEditWageViewModel();
  final TextEditingController _normalRateController = TextEditingController();
  final TextEditingController _overtimeRateController = TextEditingController();
  final TextEditingController _unitRateController = TextEditingController();

  // 用于存储ever监听器的引用，以便在dispose时取消
  final List<Worker> _workers = [];

  @override
  onPageRoute(Object? routeParams, bool fromLaunchTask) {
    var props = routeParams as GroupEditWageProps?;
    _viewModel.init(props);
  }

  @override
  void onPageCreate() {
    super.onPageCreate();
    _normalRateController.addListener(() {
      if (mounted) {
        _viewModel.changeWorkPrice(_normalRateController.text);
      }
    });

    _overtimeRateController.addListener(() {
      if (mounted) {
        _viewModel.changeOverPrice(_overtimeRateController.text);
      }
    });

    // 存储监听器引用以便后续取消
    _workers.add(ever(_viewModel.us.workPrice, (value) {
      if (mounted && _normalRateController.text != value) {
        _normalRateController.text = value;
      }
    }));

    _workers.add(ever(_viewModel.us.overPrice, (value) {
      if (mounted && _overtimeRateController.text != value) {
        _overtimeRateController.text = value;
      }
    }));

    _unitRateController.addListener(() {
      if (mounted) {
        _viewModel.changeUnitPrice(_unitRateController.text);
      }
    });
    _workers.add(ever(_viewModel.us.unitPrice, (value) {
      if (mounted && _unitRateController.text != value) {
        _unitRateController.text = value;
      }
    }));
  }

  @override
  void onPageDestroy() {
    super.onPageDestroy();
    // 取消所有ever监听器
    for (var worker in _workers) {
      worker.dispose();
    }

    // 销毁控制器
    _normalRateController.dispose();
    _overtimeRateController.dispose();
    _unitRateController.dispose();
  }

  @override
  Widget yBuild(BuildContext context) {
    return Scaffold(
      appBar: AppBarUtil.buildCommonAppBar(
        title: _viewModel.title,
        onBackTap: () => YPRoute.closePage(),
      ),
      resizeToAvoidBottomInset: false,
      backgroundColor: Color(0xFFFFFFFF),
      body: contentView(context),
    );
  }

  /// 实际展示的视图，需要处理：
  /// 1. 画出UI
  /// 2. 绑定UIState
  /// 3. 处理事件监听
  /// 4. 向VM传递来自View层的事件
  Widget contentView(BuildContext context) {
    // 进行事件处理
    // handleGroupEditWageVMEvent(props.vm)
    return Column(children: [
      Divider(height: 10.h, color: Color(0xFFF0F0F0), thickness: 10.h),
      Obx(
        () => CombinedFilterWidget(
          showTypeFilter: false,
          initialStartDate: _viewModel.us.startTime,
          initialEndDate: _viewModel.us.endTime,
          onSelectAll: () {
            _viewModel.onSelectAllTap();
          },
          onFilterChanged: (filter) {
            _viewModel.updateDateRange(filter.startDate, filter.endDate);
          },
        ),
      ),
      Divider(height: 10.h, color: Color(0xFFF0F0F0), thickness: 10.h),
      Expanded(
        child: Column(
          children: [
            _buildStatisticsView(),
            _buildWageView(context),
          ],
        ),
      ),
      _buildBottomView()
    ]);
  }

  Widget _buildStatisticsView() {
    return Obx(
      () => GroupBillStatisticsView(
        list: _viewModel.us.statisticsList,
        isShowEditWage: false,
      ),
    );
  }

  /// 构建工资视图
  Widget _buildWageView(BuildContext context) {
    return Obx(() {
      if (_viewModel.us.showWorkPackageView) {
        return Padding(
          padding: EdgeInsets.all(16.w),
          child: Column(
            children: [
              SizedBox(height: 16.h),
              _buildNormalWorkSection(context),
              SizedBox(height: 16.h),
              _buildOvertimeWorkSection(context),
              SizedBox(height: 16.h),
              Obx(() => _buildTotalWageSection()),
            ],
          ),
        );
      } else if (_viewModel.us.showWorkLoadView) {
        return Padding(
          padding: EdgeInsets.all(16.w),
          child: Column(
            children: [
              _buildUnitWorkSection(),
              SizedBox(height: 16.h),
              Obx(() => _buildTotalWageSection()),
            ],
          ),
        );
      } else {
        return SizedBox.shrink();
      }
    });
  }

  ///点工、包工 上班View
  Widget _buildNormalWorkSection(BuildContext context) {
    return Row(
      children: [
        Text('上班：', style: _textStyle(FontWeight.w500)),
        Obx(() => _buildTextView(_viewModel.us.workHour, onTap: () {
              _viewModel.showWorkTimeDialog(context);
            })),
        SizedBox(width: 2.w),
        Text('小时=', style: _textStyle(FontWeight.normal)),
        SizedBox(width: 2.w),
        _buildInputField(
          _normalRateController,
          width: 90.w,
          textColor: Color(0xFFFFA011),
        ),
        SizedBox(width: 2.w),
        Text('元=1个工', style: _textStyle(FontWeight.normal)),
      ],
    );
  }

  ///点工、包工 加班View
  Widget _buildOvertimeWorkSection(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Row(
          children: [
            Text('加班：', style: _textStyle(FontWeight.w500)),
            Obx(() => _buildOvertimeWorkView(context)),
          ],
        ),
        Obx(() => _buildSwitchView()),
      ],
    );
  }

  ///工量View
  Widget _buildUnitWorkSection() {
    return Row(children: [
      Obx(
        () => Text(
          _viewModel.us.unitWorkTypeName,
          style: _textStyle(FontWeight.normal),
        ),
      ),
      Text('=', style: _textStyle(FontWeight.normal)),
      SizedBox(width: 2.w),
      _buildInputField(
        _unitRateController,
        width: 90.w,
        textColor: Color(0xFFFFA011),
      ),
      Text('元', style: _textStyle(FontWeight.normal)),
    ]);
  }

  ///点工切换按钮
  Widget _buildSwitchView() {
    if (_viewModel.us.showSwitchBtn) {
      return GestureDetector(
        onTap: () {
          _viewModel.setOvertimeTap();
        },
        behavior: HitTestBehavior.opaque,
        child: Container(
          width: 84.w,
          height: 28.h,
          alignment: Alignment.center,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(4.r),
            border: Border.all(color: ColorsUtil.primaryColor, width: 1.w),
          ),
          child: Obx(
            () => Text(
              _viewModel.us.overType ? '按小时算' : '按工天算',
              style: TextStyle(fontSize: 14.sp, color: ColorsUtil.primaryColor),
            ),
          ),
        ),
      );
    }
    return SizedBox.shrink();
  }

  /// 加班会按照 按小时算和按工天算
  Widget _buildOvertimeWorkView(BuildContext context) {
    if (_viewModel.us.overType) {
      /// 按小时算
      return Row(
        children: [
          Obx(() => _buildTextView(_viewModel.us.overHour, onTap: () {
                _viewModel.showOverTimeDialog(context);
              })),
          SizedBox(width: 2.w),
          Text('小时 = 1个工', style: _textStyle(FontWeight.normal)),
        ],
      );
    } else {
      return Row(
        children: [
          Text('1小时 =', style: TextStyle(fontSize: 16.sp)),
          SizedBox(width: 2.w),
          _buildInputField(
            _overtimeRateController,
            width: 90.w,
            textColor: Color(0xFFFFA011),
          ),
          SizedBox(width: 2.w),
          Text('元', style: _textStyle(FontWeight.normal))
        ],
      );
    }
  }

  /// 文本框布局
  Widget _buildTextView(String hour, {required void Function() onTap}) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 60.h,
        height: 32.h,
        alignment: Alignment.center,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(4.r),
          border: Border.all(color: Color(0xFFF0F0F0), width: 1.w),
          color: Colors.transparent,
        ),
        child: Text(
          hour,
          style: _textFieldStyle(ColorsUtil.primaryColor),
        ),
      ),
    );
  }

  ///输入框布局
  Widget _buildInputField(
    TextEditingController? controller, {
    double width = 80,
    Color textColor = Colors.black,
  }) {
    return Container(
      width: width,
      alignment: Alignment.center,
      child: TextField(
        maxLength: 7,
        controller: controller,
        textAlign: TextAlign.center,
        inputFormatters: <TextInputFormatter>[
          LengthLimitingTextInputFormatter(7),
          // FilteringTextInputFormatter.allow(RegExp(r'^(\d+)\.(\d{0,2}).*$')),
        ],
        // 设置光标颜色
        cursorColor: ColorsUtil.primaryColor,
        keyboardType: TextInputType.text,
        decoration: InputDecoration(
          counterText: '',
          hintText: '',
          hintStyle: TextStyle(
            color: ColorsUtil.hintFontColor,
            fontSize: 16,
          ),
          // 关键代码：添加 hint
          isDense: true,
          contentPadding:
              const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(4),
            borderSide: const BorderSide(
              color: Color(0XFFE6E6E6), // 边框颜色
              width: 1, // 👈 设置边框宽度（单位是 dp）
            ),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(4),
            borderSide: const BorderSide(
              color: Color(0XFFe6e6e6), // 边框颜色
              width: 1, // 👈 设置边框宽度（单位是 dp）
            ),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(4),
            borderSide: const BorderSide(
              color: Color(0XFFe6e6e6), // 边框颜色
              width: 1, // 👈 设置边框宽度（单位是 dp）
            ),
          ),
        ),
        style: _textFieldStyle(textColor),
      ),
    );
  }

  /// 总工资
  Widget _buildTotalWageSection() {
    return Row(
      children: [
        Text('工资：', style: _textStyle(FontWeight.w500)),
        Text(
          _viewModel.us.totalMoney,
          style: TextStyle(
            fontSize: 18.sp,
            color: Color(0xFFFFA011),
            fontFamily: FontUtil.fontCondMedium,
          ),
        ),
      ],
    );
  }

  ///底部取消按钮和确认按钮
  Widget _buildBottomView() {
    return Container(
      padding: EdgeInsets.only(left: 16.w, right: 16.w, bottom: 16.h),
      child: Row(
        children: [
          // 取消按钮
          Expanded(
            child: GestureDetector(
              onTap: () {
                YPRoute.closePage();
              },
              behavior: HitTestBehavior.opaque,
              child: Container(
                height: 44,
                alignment: Alignment.center,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(4.r),
                  border:
                      Border.all(color: ColorsUtil.primaryColor, width: 1.w),
                ),
                child: Text('取消',
                    style: TextStyle(
                        fontSize: 16.sp, color: ColorsUtil.primaryColor)),
              ),
            ),
          ),
          SizedBox(width: 10.w), // 按钮间距
          // 确认修改按钮
          // 取消按钮
          Expanded(
            child: GestureDetector(
              onTap: () {
                _viewModel.setCommitTap();
              },
              behavior: HitTestBehavior.opaque,
              child: Container(
                height: 44,
                alignment: Alignment.center,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(4.r),
                  border:
                      Border.all(color: ColorsUtil.primaryColor, width: 1.w),
                  color: ColorsUtil.primaryColor,
                ),
                child: Text('确认',
                    style: TextStyle(fontSize: 16.sp, color: Colors.white)),
              ),
            ),
          ),
        ],
      ),
    );
  }

  TextStyle _textStyle(FontWeight fontWeight) {
    return TextStyle(
      fontSize: 16.sp,
      fontWeight: fontWeight,
      color: Color(0xFF323232),
    );
  }

  TextStyle _textFieldStyle(Color textColor) {
    return TextStyle(
      fontSize: 18.sp,
      color: textColor,
      fontFamily: FontUtil.fontCondMedium,
    );
  }
}
