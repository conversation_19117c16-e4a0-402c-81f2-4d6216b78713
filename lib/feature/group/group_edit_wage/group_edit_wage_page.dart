import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/feature/group/group_pro_bill/view/group_bill_statistics_view.dart';
import 'package:gdjg_pure_flutter/utils/route_util/base_page/base_ful_page.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/appbar_util.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';
import 'package:gdjg_pure_flutter/widget/combined_filter_widget.dart';
import 'package:get/get.dart';
import 'entity/group_edit_wage_props.dart';
import 'vm/group_edit_wage_viewmodel.dart';

/// @date 2025/06/30
/// @param props 页面路由参数
/// @returns
/// @description GroupEditWage页面入口 班组项目修改工资
class GroupEditWagePage extends BaseFulPage {
  const GroupEditWagePage({super.key}) : super(appBar: null);

  @override
  createState() => _GroupProBillPage();
}

class _GroupProBillPage<GroupEditWagePage> extends BaseFulPageState {
  final TextEditingController normalHoursController = TextEditingController();
  final TextEditingController normalRateController = TextEditingController();
  final TextEditingController overtimeHoursController = TextEditingController();
  final TextEditingController overtimeRateController = TextEditingController();

  final GroupEditWageViewModel viewModel = GroupEditWageViewModel();

  @override
  onPageRoute(Object? routeParams, bool fromLaunchTask) {
    var props = routeParams as GroupEditWageProps?;
    viewModel.init(props);
  }

  @override
  Widget yBuild(BuildContext context) {
    return Scaffold(
      appBar: AppBarUtil.buildCommonAppBar(
        title: viewModel.props?.workNoteName ?? "",
        onBackTap: () => YPRoute.closePage(),
      ),
      backgroundColor: Color(0xFFFFFFFF),
      body: contentView(),
    );
  }

  /// 实际展示的视图，需要处理：
  /// 1. 画出UI
  /// 2. 绑定UIState
  /// 3. 处理事件监听
  /// 4. 向VM传递来自View层的事件
  Widget contentView() {
    // 进行事件处理
    // handleGroupEditWageVMEvent(props.vm)
    return Column(children: [
      Divider(height: 10.h, color: Color(0xFFF0F0F0), thickness: 10.h),
      CombinedFilterWidget(
        onFilterChanged: (filter) {},
      ),
      Divider(height: 10.h, color: Color(0xFFF0F0F0), thickness: 10.h),
      Expanded(
        child: Column(
          children: [
            _buildStatisticsView(),
            _buildWageView(),
          ],
        ),
      )
    ]);
  }

  Widget _buildStatisticsView() {
    return Obx(
      () => GroupBillStatisticsView(
        list: viewModel.uiState.value.statisticsList,
        isShowEditWage: false,
      ),
    );
  }

  /// 构建工资视图
  Widget _buildWageView() {
    return Padding(
      padding: EdgeInsets.all(16.w),
      child: Column(
        children: [
          _buildNormalWorkSection(),
          SizedBox(height: 16.h),
          _buildOvertimeWorkSection(),
          SizedBox(height: 16.h),
          _buildTotalWageSection(),
          SizedBox(height: 16.h),
          _buildSaveButton(),
        ],
      ),
    );
  }

  _textStyle() {
    return TextStyle(
        fontSize: 16.sp, fontWeight: FontWeight.bold, color: Color(0xFF323232));
  }

  Widget _buildNormalWorkSection() {
    return Row(
      children: [
        Text('上班：', style: _textStyle()),
        SizedBox(width: 8.w),
        _buildInputField(normalHoursController, width: 60.w,isEnabled: false,textColor: ColorsUtil.primaryColor),
        SizedBox(width: 8.w),
        Text('小时=', style: TextStyle(fontSize: 16.sp)),
        SizedBox(width: 8.w),
        _buildInputField(normalRateController, width: 60.w),
        SizedBox(width: 8.w),
        Text('元=1个工', style: TextStyle(fontSize: 16.sp)),
      ],
    );
  }

  Widget _buildOvertimeWorkSection() {
    return Row(
      children: [
        Text('加班：', style: _textStyle()),
        SizedBox(width: 8.w),
        _buildInputField(overtimeHoursController, width: 40.w),
        SizedBox(width: 8.w),
        Text('小时=', style: TextStyle(fontSize: 16.sp)),
        SizedBox(width: 8.w),
        _buildInputField(overtimeRateController, width: 60.w),
        SizedBox(width: 8.w),
        Text('元', style: TextStyle(fontSize: 16.sp)),
      ],
    );
  }

  Widget _buildTotalWageSection() {
    return Row(
      children: [
        Text('工资：', style: _textStyle()),
        SizedBox(width: 8.w),
        Text(
          '2795.00',
          style: TextStyle(fontSize: 16.sp, color: Colors.orange),
        ),
      ],
    );
  }

  Widget _buildSaveButton() {
    return ElevatedButton(
      onPressed: () {
        // 保存逻辑
      },
      child: Text('保存工资'),
    );
  }

  Widget _buildInputField(TextEditingController controller,
      {double width = 80,Color textColor = Colors.black,bool isEnabled = true,}) {
    return Container(
      width: width,
      alignment: Alignment.center,
      child:TextField(
        maxLength: 7,
        enabled: isEnabled,
        controller: controller,
        textAlign: TextAlign.center,
        inputFormatters: <TextInputFormatter>[
          FilteringTextInputFormatter.digitsOnly,
          LengthLimitingTextInputFormatter(7),
        ],
        // 设置光标颜色
        cursorColor: ColorsUtil.primaryColor,
        keyboardType: TextInputType.number,
        decoration: InputDecoration(
          counterText: '',
          hintText: '',
          hintStyle: TextStyle(
            color: ColorsUtil.hintFontColor,
            fontSize: 16,
          ),
          // 关键代码：添加 hint
          isDense: true,
          contentPadding: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(4),
            borderSide: const BorderSide(
              color: Color(0XFFE6E6E6), // 边框颜色
              width: 1, // 👈 设置边框宽度（单位是 dp）
            ),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(4),
            borderSide: const BorderSide(
              color: Color(0XFFe6e6e6), // 边框颜色
              width: 1, // 👈 设置边框宽度（单位是 dp）
            ),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(4),
            borderSide: const BorderSide(
              color: Color(0XFFe6e6e6), // 边框颜色
              width: 1, // 👈 设置边框宽度（单位是 dp）
            ),
          ),
        ),
        style: TextStyle(fontSize: 16.sp,color: textColor,fontWeight: FontWeight.bold),
      ),
    );
  }
}
