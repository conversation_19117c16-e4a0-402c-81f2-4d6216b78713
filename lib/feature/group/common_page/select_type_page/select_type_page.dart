import 'package:flutter/material.dart';
import 'package:gdjg_pure_flutter/model/RecordType.dart';
import 'package:gdjg_pure_flutter/utils/route_util/base_page/base_ful_page.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';
import 'package:get/get.dart';

import 'entity/select_type_props.dart';
import 'vm/select_type_viewmodel.dart';

/// @date 2025/06/13
/// @param props 页面路由参数
/// @returns
/// @description SelectType 类型选择页面入口
class SelectTypePage extends BaseFulPage {
  const SelectTypePage({super.key})
      : super(appBar: const YPAppBar(title: "选择类型"));

  @override
  createState() => _SelectTypePage();
}

class _SelectTypePage<GroupProStatisticsPage> extends BaseFulPageState {
  final SelectTypeViewModel viewModel = SelectTypeViewModel();

  // 记工所有类型
  final List<RwaRecordType> recordTypeList = [
    RwaRecordType.workDays,
    RwaRecordType.packageWork,
    RwaRecordType.dailyWages,
    RwaRecordType.workLoad,
  ];

  // 记工所有类型（包含其他费用）
  final List<RwaRecordType> recordTypeListAll = [
    RwaRecordType.workDays,
    RwaRecordType.packageWork,
    RwaRecordType.dailyWages,
    RwaRecordType.workLoad,
    RwaRecordType.expense,
  ];

  // 记账所有类型
  final List<RwaRecordType> accountRecordTypeList = [
    RwaRecordType.debt,
    RwaRecordType.wageLast,
  ];

  late List<RwaRecordType> workRecordTypeList;

  @override
  onPageRoute(Object? routeParams, bool fromLaunchTask) {
    var props = routeParams as SelectTypeProps?;
    workRecordTypeList =
        props?.isShowExpense == true ? recordTypeListAll : recordTypeList;
    viewModel.setInitialSelection(props?.recordTypeList);
  }

  @override
  Widget yBuild(BuildContext context) {
    return Scaffold(
      body: contentView(workRecordTypeList),
    );
  }

  @override
  onSend2Previous() {
    return viewModel.selectedTypes.toList();
  }

  /// 实际展示的视图
  Widget contentView(List<RwaRecordType> list) {
    return Column(
      children: [
        Expanded(
          child: Container(
            color: Colors.white,
            child: ListView(
              children: [
                const Divider(
                    height: 8, color: Color(0xFFF5F5F5), thickness: 8),
                _buildSectionHeader("记工"),
                ...workRecordTypeList.map((type) => _buildSelectionItem(type)),
                _buildSectionHeader("记账"),
                ...accountRecordTypeList
                    .map((type) => _buildSelectionItem(type)),
              ],
            ),
          ),
        ),
        _buildBottomButtons(),
      ],
    );
  }

  /// 构建分组标题
  Widget _buildSectionHeader(String title) {
    return Container(
      padding: const EdgeInsets.only(left: 16, top: 16, bottom: 8),
      alignment: Alignment.centerLeft,
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.bold,
          color: Colors.black,
        ),
      ),
    );
  }

  /// 构建选择项
  Widget _buildSelectionItem(RwaRecordType type) {
    return Obx(() {
      final isSelected = viewModel.selectedTypes.contains(type);

      return InkWell(
        onTap: () => viewModel.toggleSelection(type),
        child: Container(
          padding: EdgeInsets.symmetric(vertical: 16),
          margin: EdgeInsets.symmetric(horizontal: 16),
          decoration: BoxDecoration(
            border: Border(
              bottom: BorderSide(color: Color(0xFFF5F5F5), width: 1),
            ),
          ),
          child: Row(
            children: [
              Container(
                width: 20,
                height: 20,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: isSelected
                        ? ColorsUtil.primaryColor
                        : ColorsUtil.primaryColor,
                    width: 1.5,
                  ),
                  color:
                      isSelected ? ColorsUtil.primaryColor : Colors.transparent,
                ),
                child: isSelected
                    ? Icon(Icons.check, size: 16, color: Colors.white)
                    : null,
              ),
              SizedBox(width: 8),
              Text(
                type.label,
                style: TextStyle(fontSize: 19, color: Color(0xFF323232)),
              ),
            ],
          ),
        ),
      );
    });
  }

  /// 构建底部按钮
  Widget _buildBottomButtons() {
    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 5,
            offset: Offset(0, -1),
          ),
        ],
      ),
      child: Row(
        children: [
          TextButton(
            onPressed: () => viewModel.resetSelection(),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(Icons.refresh, color: ColorsUtil.primaryColor),
                Text("重置",
                    style: TextStyle(
                        color: ColorsUtil.primaryColor, fontSize: 16)),
              ],
            ),
          ),
          SizedBox(width: 16),
          Expanded(
            child: ElevatedButton(
              onPressed: () {
                YPRoute.closePage(onSend2Previous());
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: ColorsUtil.primaryColor,
                padding: EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(6.0), // 设置圆角大小
                ),
              ),
              child: Text("确定",
                  style: TextStyle(color: Colors.white, fontSize: 16)),
            ),
          ),
        ],
      ),
    );
  }
}
