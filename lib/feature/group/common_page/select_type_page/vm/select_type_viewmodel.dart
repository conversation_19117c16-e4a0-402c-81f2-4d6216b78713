import "package:gdjg_pure_flutter/model/RecordType.dart";
import "package:get/get.dart";

/// @date 2025/06/13
/// @description SelectType页ViewModel
/// 1. 处理数据的加载状态，如loading和错误视图等
/// 2. 观测UIRep中的业务实体转为UIState
/// 3. 接受来自view的意图并分发事件进行处理（暂无）
/// 4. 对View层暴露事件（操作视图和处理一些与系统权限交互等场景）
class SelectTypeViewModel {
  // 已选择的类型
  final selectedTypes = <RwaRecordType>{}.obs;

  void setInitialSelection(List<RwaRecordType>? initialTypes) {
    if (initialTypes != null) {
      selectedTypes.assignAll(initialTypes);
    }
  }

  /// 切换选择状态
  void toggleSelection(RwaRecordType type) {
    if (selectedTypes.contains(type)) {
      selectedTypes.remove(type);
    } else {
      selectedTypes.add(type);
    }
  }

  /// 重置选择
  void resetSelection() {
    selectedTypes.clear();
  }
}
