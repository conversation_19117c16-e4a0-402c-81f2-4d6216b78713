import 'package:flutter/material.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/appbar_util.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/button_util.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';
import 'package:gdjg_pure_flutter/feature/widget/info_item_widget.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/toast_util.dart';

class WorkerResumePage extends StatelessWidget {
  const WorkerResumePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      appBar: AppBarUtil.buildCommonAppBar(
        title: '工友名片',
        backgroundColor: Colors.white,
        iconColor: Colors.black,
      ),
      body: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                children: [
                  // 顶部提示信息
                  _buildNoticeSection(),
                  const SizedBox(height: 24),
                  // 基本信息
                  const SectionTitleWidget(title: '基本信息'),
                  _buildBasicInfoSection(),
                  const SizedBox(height: 24),
                  // 隐私信息
                  const SectionTitleWidget(title: '隐私信息(微信分享给工友填写)'),
                  _buildPrivacyInfoSection(),
                ],
              ),
            ),
          ),
          // 底部按钮
          _buildBottomButton(),
        ],
      ),
    );
  }

  Widget _buildNoticeSection() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        color: Color(0xFFE3F2FD),
      ),
      child: Text(
        '处于对用户隐私信息的保护，依照相关法律法规，工友的隐私信息需工友自行上传、填写，并授权给班组长使用。',
        style: TextStyle(
          fontSize: 14,
          color: ColorsUtil.primaryColor,
          height: 1.5,
        ),
      ),
    );
  }

  Widget _buildBasicInfoSection() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          InfoItemWidget(
            label: '头像',
            value: '',
            customValue: Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: ColorsUtil.primaryColor,
                borderRadius: BorderRadius.circular(6),
              ),
              child: const Center(
                child: Text(
                  '老板',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),
            onTap: () => _onAvatarTap(),
          ),
          InfoItemWidget(
            label: '姓名',
            value: '姚老板',
            onTap: () => _onNameTap(),
          ),
          InfoItemWidget(
            label: '电话',
            value: '18202422933',
            onTap: () => _onPhoneTap(),
          ),
          InfoItemWidget(
            label: '工种',
            value: '请选择工种',
            onTap: () => _onJobTypeTap(),
          ),
        ],
      ),
    );
  }

  Widget _buildPrivacyInfoSection() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          InfoItemWidget(
            label: '身份证',
            value: '未上传',
            isRequired: true,
            onTap: () => _onIdCardTap(),
          ),
          InfoItemWidget(
            label: '银行卡',
            value: '未上传',
            isRequired: true,
            onTap: () => _onBankCardTap(),
          ),
          InfoItemWidget(
            label: '截止时间',
            value: '未填写时间',
            isRequired: true,
            onTap: () => _onDeadlineTap(),
          ),
        ],
      ),
    );
  }

  Widget _buildBottomButton() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: ButtonUtil.buildCommonButton(
        text: '邀请工友填写名片',
        onPressed: () => _onInviteWorker(),
        backgroundColor: ColorsUtil.primaryColor,
      ),
    );
  }

  void _onAvatarTap() {
    ToastUtil.showToast('选择头像');
  }

  void _onNameTap() {
    ToastUtil.showToast('编辑姓名');
  }

  void _onPhoneTap() {
    ToastUtil.showToast('编辑电话');
  }

  void _onJobTypeTap() {
    ToastUtil.showToast('选择工种');
  }

  void _onIdCardTap() {
    ToastUtil.showToast('上传身份证');
  }

  void _onBankCardTap() {
    ToastUtil.showToast('上传银行卡');
  }

  void _onDeadlineTap() {
    ToastUtil.showToast('设置截止时间');
  }

  void _onInviteWorker() {
    ToastUtil.showToast('邀请工友填写名片');
  }
}
