import 'package:flutter/material.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/appbar_util.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';

class WorkerSettingPage extends StatefulWidget {
  const WorkerSettingPage({super.key});

  @override
  State<WorkerSettingPage> createState() => _WorkerSettingPageState();
}

class _WorkerSettingPageState extends State<WorkerSettingPage> {
  bool allowViewWorkData = true;
  bool allowViewSalary = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      appBar: AppBarUtil.buildCommonAppBar(
        title: '工友设置',
        backgroundColor: Colors.white,
        iconColor: Colors.black,
      ),
      body: Column(
        children: [
          const SizedBox(height: 20),
          // 工友信息卡片
          _buildWorkerInfoCard(),
          const SizedBox(height: 20),
          // 工友对工权限标题
          _buildSectionTitle('工友对工权限'),
          const SizedBox(height: 12),
          // 权限设置项
          _buildPermissionItem(
            title: '允许在线查看记工数据',
            value: allowViewWorkData,
            onChanged: (value) {
              setState(() {
                allowViewWorkData = value;
              });
            },
          ),
          _buildPermissionItem(
            title: '允许工人查看记工工资',
            value: allowViewSalary,
            onChanged: (value) {
              setState(() {
                allowViewSalary = value;
              });
            },
          ),
        ],
      ),
    );
  }

  Widget _buildWorkerInfoCard() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // 头像
          Container(
            width: 50,
            height: 50,
            decoration: BoxDecoration(
              color: ColorsUtil.primaryColor,
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Center(
              child: Text(
                '老板',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
          const SizedBox(width: 12),
          // 姓名和电话
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  '姚老板',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: Colors.black,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '18202422933',
                  style: TextStyle(
                    fontSize: 14,
                    color: ColorsUtil.black65,
                  ),
                ),
              ],
            ),
          ),
          // 编辑按钮
          GestureDetector(
            onTap: () {
              // 编辑功能
            },
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  '编辑',
                  style: TextStyle(
                    fontSize: 14,
                    color: ColorsUtil.black65,
                  ),
                ),
                const SizedBox(width: 4),
                Icon(
                  Icons.arrow_forward_ios,
                  size: 14,
                  color: ColorsUtil.black45,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      color: const Color(0xFFF5F5F5),
      child: Text(
        title,
        style: TextStyle(
          fontSize: 14,
          color: ColorsUtil.black65,
        ),
      ),
    );
  }

  Widget _buildPermissionItem({
    required String title,
    required bool value,
    required ValueChanged<bool> onChanged,
  }) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
      decoration: const BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: BorderSide(
            color: Color(0xFFF0F0F0),
            width: 0.5,
          ),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: Text(
              title,
              style: const TextStyle(
                fontSize: 16,
                color: Colors.black,
              ),
            ),
          ),
          Switch(
            value: value,
            onChanged: onChanged,
            activeColor: const Color(0xFF4CD964),
            inactiveThumbColor: Colors.white,
            inactiveTrackColor: const Color(0xFFE5E5EA),
            materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
          ),
        ],
      ),
    );
  }
}
