import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';
import 'package:gdjg_pure_flutter/feature/tabbar/view/role_switch_bar_view.dart';
import 'package:get/get.dart';
import '../group_mine/group_mine_page.dart';
import '../group_not_settled/group_not_settled_page.dart';
import '../group_project/group_project_page.dart';
import '../group_statistics/group_statistics_page.dart';
import '../us/group_us.dart';

class GroupPage extends GetView<GroupUS> {
  const GroupPage({super.key});

  @override
  Widget build(BuildContext context) {
    final List<Widget> pages = [
      const GroupProjectPage(),
      GroupStatisticsPage(),
      const GroupNotSettledPage(),
      const GroupMinePage(),
    ];

    return Scaffold(
      appBar: AppBar(
        titleSpacing: 0,  // 移除title的左右边距
        title: RoleSwitchBar(
          rightText: '班组长顶部资源位',
          rightIconAsset: 'assets/images/tabbar/icon_tabbar_calendar_normal.webp',
          backgroundColor: Colors.white,
          onRightTap: () {},
        ),
        centerTitle: false,
        backgroundColor: Colors.white,
        elevation: 0,
      ),
      body: Obx(() => IndexedStack(
            index: controller.currentIndex.value,
            children: pages,
          )),
      bottomNavigationBar: Container(
        decoration: BoxDecoration(
            color: Colors.white,
            border: Border(
              top: BorderSide(
                color: Color(0x80CCCCCC),
                width: 0.5,
              ),
            )
        ),
        child: Obx(
          () => BottomNavigationBar(
            backgroundColor: Colors.white,
            type: BottomNavigationBarType.fixed,
            currentIndex: controller.currentIndex.value,
            onTap: controller.changeTabIndex,
            selectedItemColor: ColorsUtil.primaryColor,
            unselectedItemColor: Color(0xFF5A5A66),
            selectedFontSize: 14,
            unselectedFontSize: 14,
            items: [
              BottomNavigationBarItem(
                label: '项目',
                icon: Padding(
                    padding: const EdgeInsets.only(bottom: 4),
                    child: Image(
                      fit: BoxFit.fill,
                      width: 22,
                      height: 22,
                      image: controller.currentIndex.value == 0
                          ? const AssetImage('assets/images/tabbar/icon_tabbar_project_selected.webp')
                          : const AssetImage('assets/images/tabbar/icon_tabbar_project_nomal.webp'),
                    )),
              ),
               BottomNavigationBarItem(
                label: '统计',
                 icon: Padding(
                     padding: const EdgeInsets.only(bottom: 4),
                     child: Image(
                       fit: BoxFit.fill,
                       width: 22,
                       height: 22,
                       image: controller.currentIndex.value == 1
                           ? const AssetImage('assets/images/tabbar/icon_tabbar_statistic_selected.webp')
                           : const AssetImage('assets/images/tabbar/icon_tabbar_statistic_normal.webp'),
                     )),
              ),
               BottomNavigationBarItem(
                label: '未结',
                icon: Padding(
                    padding: const EdgeInsets.only(bottom: 4),
                    child: Image(
                      fit: BoxFit.fill,
                      width: 22,
                      height: 22,
                      image: controller.currentIndex.value == 2
                          ? const AssetImage('assets/images/tabbar/icon_tabbar_not_settled_selected.webp')
                          : const AssetImage('assets/images/tabbar/icon_tabbar_not_settled_normal.webp'),
                    )),
              ),
               BottomNavigationBarItem(
                label: '我的',
                icon: Padding(
                    padding: const EdgeInsets.only(bottom: 4),
                    child: Image(
                      fit: BoxFit.fill,
                      width: 22,
                      height: 22,
                      image: controller.currentIndex.value == 3
                          ? const AssetImage('assets/images/tabbar/icon_tabbar_mine_selected.webp')
                          : const AssetImage('assets/images/tabbar/icon_tabbar_mine_normal.webp'),
                    )),
              ),
            ],
          ),
        ),
      ),
    );
  }
} 