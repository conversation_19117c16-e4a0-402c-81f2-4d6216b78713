import 'package:flutter/material.dart';

import '../../../utils/ui_util/appbar_util.dart';
import '../../../utils/ui_util/toast_util.dart';

class InviteWorkerPage extends StatelessWidget {
  const InviteWorkerPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBarUtil.buildCommonAppBar(
        title: '微信邀请',
        backgroundColor: Colors.transparent,
        iconColor: Colors.black,
      ),
      extendBodyBehindAppBar: true,
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF6BA6FF),
              Color(0xFF4A90E2),
            ],
          ),
        ),
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 40.0, vertical: 20.0),
            child: Column(
              children: [
                const SizedBox(height: 40),
                // 白色卡片容器
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(24),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.1),
                        blurRadius: 10,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Column(
                    children: [
                      // 项目标题
                      const Text(
                        '[荷花池二期水电]',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: Colors.black,
                        ),
                      ),
                      const SizedBox(height: 12),
                      // 副标题
                      RichText(
                        text: const TextSpan(
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey,
                          ),
                          children: [
                            TextSpan(text: '工友可使用'),
                            TextSpan(
                              text: '【微信】',
                              style: TextStyle(
                                color: Color(0xFF07C160),
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            TextSpan(text: '扫码加入'),
                          ],
                        ),
                      ),
                      const SizedBox(height: 24),
                      // 二维码
                      Container(
                        width: 200,
                        height: 200,
                        decoration: BoxDecoration(
                          border: Border.all(
                            color: Colors.grey.withValues(alpha: 0.3),
                            width: 1,
                          ),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(8),
                          child: Image.asset(
                            'assets/images/common/image_take_photo.png',
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) {
                              return Container(
                                color: Colors.grey[100],
                                child: const Icon(
                                  Icons.qr_code,
                                  size: 80,
                                  color: Colors.grey,
                                ),
                              );
                            },
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 24),
                // 功能按钮行
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    _buildActionButton(
                      icon: Icons.wechat,
                      label: '微信邀请工友',
                      onTap: _onWechatInvite,
                    ),
                    _buildActionButton(
                      icon: Icons.qr_code_scanner,
                      label: '分享二维码',
                      onTap: _onShareQRCode,
                    ),
                    _buildActionButton(
                      icon: Icons.download,
                      label: '保存二维码',
                      onTap: _onSaveQRCode,
                    ),
                  ],
                ),
                const SizedBox(height: 40),
                // 底部说明文字
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: const Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '1、通过微信，将邀请链接或二维码分享给工友。',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.white,
                          height: 1.5,
                        ),
                      ),
                      SizedBox(height: 8),
                      Text(
                        '2、工友点击链接后，填写自己的姓名与电话后，自动加入项目。班组长无需再手动填写工友信息。',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.white,
                          height: 1.5,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 40),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        children: [
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(30),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Icon(
              icon,
              size: 28,
              color: const Color(0xFF4A90E2),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            label,
            style: const TextStyle(
              fontSize: 12,
              color: Colors.white,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  void _onWechatInvite() {
    ToastUtil.showToast('微信邀请功能');
  }

  void _onShareQRCode() {
    ToastUtil.showToast('分享二维码功能');
  }

  void _onSaveQRCode() {
    ToastUtil.showToast('保存二维码功能');
  }
}
