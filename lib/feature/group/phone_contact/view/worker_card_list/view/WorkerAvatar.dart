import 'package:flutter/material.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';

/// 工友列表-工友头像组件：没有头像就展示纯蓝色背景
class WorkerAvatar extends StatelessWidget {
  final String? avatar;
  final String? shortName;
  /// 工友列表-工友头像组件：没有头像就展示纯蓝色背景
  const WorkerAvatar({super.key, this.avatar, this.shortName});

  @override
  Widget build(BuildContext context) {
    return _buildAvatar();
  }

  Widget _buildAvatar() {
    if (avatar?.isNotEmpty == true) {
      return ClipRRect(
        borderRadius: BorderRadius.circular(6),
        child: Image.network(
          avatar!,
          width: 47,
          height: 47,
          fit: BoxFit.cover,
          errorBuilder: (context, error, stackTrace) {
            return _buildDefaultAvatar();
          },
        ),
      );
    }
    return _buildDefaultAvatar();
  }

  Widget _buildDefaultAvatar() {
    return Container(
      width: 47,
      height: 47,
      decoration: BoxDecoration(
        color: ColorsUtil.primaryColor,
        borderRadius: BorderRadius.circular(6),
      ),
      child: Center(
        child: Center(
          child: Text(
            shortName ?? "",
            style: const TextStyle(
              color: Colors.white,
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ),
    );
  }
}
