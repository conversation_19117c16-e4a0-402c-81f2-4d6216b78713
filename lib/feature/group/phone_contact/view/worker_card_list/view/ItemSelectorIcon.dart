import 'package:flutter/cupertino.dart';

class ItemSelectorIcon extends StatelessWidget {
  const ItemSelectorIcon({super.key, required this.isSelected});

  final bool isSelected;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: 20,
      height: 20,
      child: Image.asset(
        isSelected
            ? 'assets/images/common/waa_ic_find_pro_check.webp'
            : 'assets/images/common/waa_ic_find_pro_uncheck.webp',
        width: 20,
        height: 20,
      ),
    );
  }
}
