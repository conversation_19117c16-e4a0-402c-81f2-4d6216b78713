import 'package:gdjg_pure_flutter/data/demo_setting_data/entity/worker_info/worker/workers_get_worker_info_entity.dart';
import 'package:gdjg_pure_flutter/feature/group/phone_contact/view/worker_card_list/vm/protocol/worker_card_list_ui_state.dart';

/// @date 2025/06/25
/// item选中回调
typedef WorkerSelectCallBack = void Function(List<double> selectIds);

/// @date 2025/06/25
/// @description WorkerCardList页入参
/// @param canSelect 列表项是否可以选择
/// @param data 工友列表数据
/// @param canSearch 是否可以搜索
class WorkerCardListProps {
  bool? canSelect;
  List<double>? selectIds;
  List<WorkersGetWorkerInfoAEntity>? data;

  WorkerCardListProps({this.data, this.canSelect, this.selectIds});

  @override
  String toString() {
    return 'WorkerCardListProps{data: ${data.toString()}';
  }

  /// 将数据转换为WorkerCardSections
  /// isIdentify：取isAgent
  /// title：取namePy
  List<WorkerCardItemUIState> toWorkerUIState() {
    return data
            ?.map((item) => WorkerCardItemUIState(
                  workerId: item.id.toString(),
                  name: item.name,
                  shortName: item.getPortraitName(),
                  tel: item.tel,
                  avatar: item.avatar,
                  isIdentify: item.isAgent == 1,
                  namePy: item.namePy,
                  isSelected: false,
                ))
            .toList() ??
        [];
  }
}
