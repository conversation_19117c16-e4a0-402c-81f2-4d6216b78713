import 'package:gdjg_pure_flutter/data/demo_setting_data/entity/worker_info/worker/workers_get_worker_info_entity.dart';
import 'package:gdjg_pure_flutter/feature/group/phone_contact/view/worker_card_list/vm/protocol/worker_card_list_ui_state.dart';

class WorkerCardListProps {
  List<WorkersGetWorkerInfoAEntity>? data;

  WorkerCardListProps({this.data});

  @override
  String toString() {
    return 'WorkerCardListProps{data: ${data.toString()}';
  }

  /// 将数据转换为WorkerCardSections
  /// isIdentify：取isAgent
  /// title：取namePy
  List<WorkerCardItemUIState> toWorkerUIState() {
    return data
            ?.map((item) => WorkerCardItemUIState(
                  workerId: item.id.toString(),
                  name: item.name,
                  shortName: item.getPortraitName(),
                  tel: item.tel,
                  avatar: item.avatar,
                  isIdentify: item.isAgent == 1,
                  namePy: item.namePy,
                  isSelected: false,
                ))
            .toList() ??
        [];
  }
}
