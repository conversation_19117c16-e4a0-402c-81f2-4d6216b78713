import "package:gdjg_pure_flutter/feature/group/phone_contact/view/worker_card_list/entity/worker_card_list_props.dart";
import "package:get/get.dart";
import "protocol/worker_card_list_ui_state.dart";

/// @date 2025/06/24
/// @description WorkerCardList页VMCell
/// 1. 处理数据的加载状态，如loading和错误视图等
/// 2. 观测UIRep中的业务实体转为UIState
/// 3. 接受来自view的意图并分发事件进行处理
/// 4. 对View层暴露事件（操作视图和处理一些与系统权限交互等场景）
class WorkerCardListVMCell {
  var uiState = WorkerCardListUIState(list: []).obs;

  WorkerCardListVMCell();

  /// 数据转换
  void transformWorkerList(WorkerCardListProps props) async {
    uiState.value.list = props.toWorkerUIState();
  }
}
