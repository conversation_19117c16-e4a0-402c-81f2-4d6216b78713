import "package:gdjg_pure_flutter/feature/group/phone_contact/view/worker_card_list/entity/worker_card_list_props.dart";
import "package:gdjg_pure_flutter/init_module/init_route.dart";
import "package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart";
import "package:gdjg_pure_flutter/utils/ui_util/toast_util.dart";
import "package:get/get.dart";
import "protocol/worker_card_list_ui_state.dart";

/// @date 2025/06/24
/// @description WorkerCardList页VMCell
/// 1. 处理数据的加载状态，如loading和错误视图等
/// 2. 观测UIRep中的业务实体转为UIState
/// 3. 接受来自view的意图并分发事件进行处理
/// 4. 对View层暴露事件（操作视图和处理一些与系统权限交互等场景）
class WorkerCardListVMCell {
  var uiState = WorkerCardListUIState(list: []).obs;
  /// 是否可以选择
  var listCanSelect = false.obs;

  WorkerCardListVMCell();

  /// 数据转换
  void transformWorkerList(WorkerCardListProps props) async {
    uiState.value.list = props.toWorkerUIState();
    listCanSelect.value = props.canSelect ?? false;
  }

  /// 处理列表项点击事件
  void onItemTap(WorkerCardItemUIState element) {
    // 这里可以处理点击事件，比如跳转到详情页
    ToastUtil.showToast(element.name);
  }

  /// 处理列表项设置按钮点击事件
  void onItemSettingTap(WorkerCardItemUIState element) {
    // 这里可以处理点击事件，比如跳转到详情页
    YPRoute.openPage(RouteNameCollection.workerSetting);
  }
}
