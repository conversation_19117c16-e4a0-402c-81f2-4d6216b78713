import 'package:flutter/material.dart';
import 'package:gdjg_pure_flutter/feature/group/phone_contact/view/worker_card_list/view/WorkerAvatar.dart';
import 'package:gdjg_pure_flutter/feature/group/phone_contact/view/worker_card_list/vm/protocol/worker_card_list_ui_state.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/toast_util.dart';
import 'package:get/get.dart';
import 'package:grouped_list/grouped_list.dart';

import 'entity/worker_card_list_props.dart';
import 'vm/worker_card_list_vmcell.dart';

/// @date 2025/06/24
/// @param props 页面路由参数
/// @returns
/// @description WorkerCardList页面入口
class WorkerCardListView extends StatelessWidget {
  WorkerCardListView({super.key, this.props});

  final WorkerCardListProps? props;
  final WorkerCardListVMCell viewmodel = WorkerCardListVMCell();

  /// 有的组件不需要加载转态和错误处理，去掉即可
  @override
  Widget build(BuildContext context) {
    viewmodel.transformWorkerList(props!);
    return Obx(() {
      return contentView();
    });
  }

  /// 实际展示的视图，需要处理：
  /// 1. 画出UI
  /// 2. 绑定UIState
  /// 3. 处理事件监听
  /// 4. 向VM传递来自View层的事件
  Widget contentView() {
    return Flexible(
      child: GroupedListView<WorkerCardItemUIState, String>(
        elements: viewmodel.uiState.value.list,
        groupBy: (element) => element.namePy,
        groupSeparatorBuilder: (String groupValue) =>
            _buildGroupHeader(groupValue),
        itemBuilder: (context, WorkerCardItemUIState element) =>
            _buildListItem(element),
        order: GroupedListOrder.ASC,
        useStickyGroupSeparators: true,
        floatingHeader: true,
        physics: const BouncingScrollPhysics(),
      ),
    );
  }

  /// 构建分组头部
  Widget _buildGroupHeader(String groupValue) {
    return Container(
      height: 40,
      color: Colors.grey[100],
      child: Row(
        children: [
          const SizedBox(width: 12),
          Text(
            groupValue,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建列表项
  Widget _buildListItem(WorkerCardItemUIState element) {
    return Container(
      color: Colors.white,
      height: 80,
      child: ListTile(
        leading: _buildLeading(element),
        contentPadding: const EdgeInsets.only(left: 6),
        title: Text(element.name),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          spacing: 4,
          children: [
            Text(element.tel),
            Row(
              spacing: 4,
              children: [
                Icon(Icons.info, size: 12),
                Text(element.isIdentify ? '名片已授权' : '名片未授权',
                    style: TextStyle(fontSize: 12)),
              ],
            ),
          ],
        ),
        isThreeLine: true,
        onTap: () => _onItemTap(element),
      ),
    );
  }

  /// 处理列表项点击事件
  void _onItemTap(WorkerCardItemUIState element) {
    // 这里可以处理点击事件，比如跳转到详情页
    ToastUtil.showToast(element.name);
  }

  Widget _buildLeading(WorkerCardItemUIState element) {
    return ColoredBox(
      color: Colors.red,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        spacing: 6,
        children: [
          SizedBox(
            width: 20,
            height: 20,
            child: Image.asset(
              element.isSelected
                  ? 'assets/images/common/waa_ic_find_pro_check.webp'
                  : 'assets/images/common/waa_ic_find_pro_uncheck.webp',
              width: 20,
              height: 20,
            ),
          ),
          WorkerAvatar(avatar: element.avatar, shortName: element.shortName),
        ],
      ),
    );
  }
}
