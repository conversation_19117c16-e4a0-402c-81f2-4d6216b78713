import 'package:flutter/material.dart';
import 'package:gdjg_pure_flutter/widget/page_error_view.dart';
import 'package:gdjg_pure_flutter/widget/page_loading_view.dart';
import 'package:get/get.dart';
import 'package:grouped_list/grouped_list.dart';
import 'entity/worker_card_list_props.dart';
import 'vm/worker_card_list_vmcell.dart';

/// @date 2025/06/24
/// @param props 页面路由参数
/// @returns
/// @description WorkerCardList页面入口
class WorkerCardListView extends StatelessWidget {
  WorkerCardListView({super.key, this.props});

  final WorkerCardListProps? props;
  final WorkerCardListVMCell viewmodel = WorkerCardListVMCell();
  final List<Map<String, dynamic>> _elements = [
    {'group': '2022年8月26日', 'name': 'Item 1'},
    {'group': '2022年8月26日', 'name': 'Item 2'},
    {'group': '2022年8月26日', 'name': 'Item 3'},
    {'group': '2022年8月25日', 'name': 'Item 4'},
    {'group': '2022年8月25日', 'name': 'Item 5'},
    {'group': '2022年8月24日', 'name': 'Item 6'},
    {'group': '2022年8月24日', 'name': 'Item 7'},
  ];

  /// 有的组件不需要加载转态和错误处理，去掉即可
  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (viewmodel.isLoading.value) {
        return const PageLoadingView();
      }
      if (viewmodel.uiState.value.isShowError == true) {
        return PageErrorView(onReload: () => viewmodel.fetchData());
      }
      return contentView();
    });
  }

  /// 实际展示的视图，需要处理：
  /// 1. 画出UI
  /// 2. 绑定UIState
  /// 3. 处理事件监听
  /// 4. 向VM传递来自View层的事件
  Widget contentView() {
    // 进行事件处理
    // handleWorkerCardListVMEvent(props.vm)
    // return Center(
    //   child: Text(viewmodel.uiState.value.data.toString()),
    // );

    return GroupedListView<Map<String,String>, String>(
      elements: _elements,
      groupBy: (element) => element['group'],
      groupSeparatorBuilder: (String groupByValue) => Container(
        width: double.infinity,
        color: Colors.white,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
        child: Text(
          groupByValue,
          style: TextStyle(
            fontSize: 16,
            color: Colors.black,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
      itemBuilder: (context, Map<String, dynamic> element) {
        return ListTile(
          title: Text(element['name']),
        );
      },
      order: GroupedListOrder.DESC,
    );
  }
}
