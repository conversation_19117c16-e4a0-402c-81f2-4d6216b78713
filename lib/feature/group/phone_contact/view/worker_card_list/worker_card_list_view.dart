import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/feature/group/phone_contact/view/worker_card_list/view/WorkerAvatar.dart';
import 'package:gdjg_pure_flutter/feature/group/phone_contact/view/worker_card_list/vm/protocol/worker_card_list_ui_state.dart';
import 'package:gdjg_pure_flutter/generated/assets.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';
import 'package:get/get.dart';
import 'package:grouped_list/grouped_list.dart';

import 'entity/worker_card_list_props.dart';
import 'vm/worker_card_list_vmcell.dart';

/// @date 2025/06/24
/// @param props 页面路由参数
/// @returns
/// @description WorkerCardList页面入口
class WorkerCardListView extends StatelessWidget {
  WorkerCardListView({super.key, this.props});

  final WorkerCardListProps? props;
  final WorkerCardListVMCell viewModel = WorkerCardListVMCell();

  /// 有的组件不需要加载转态和错误处理，去掉即可
  @override
  Widget build(BuildContext context) {
    viewModel.transformWorkerList(props!);
    return Obx(() {
      return contentView();
    });
  }

  /// 实际展示的视图，需要处理：
  /// 1. 画出UI
  /// 2. 绑定UIState
  /// 3. 处理事件监听
  /// 4. 向VM传递来自View层的事件
  Widget contentView() {
    return Flexible(
      child: GroupedListView<WorkerCardItemUIState, String>(
        elements: viewModel.uiState.value.list,
        groupBy: (element) => element.namePy,
        groupSeparatorBuilder: (String groupValue) =>
            _buildGroupHeader(groupValue),
        itemBuilder: (context, WorkerCardItemUIState element) =>
            _buildListItem(element),
        order: GroupedListOrder.ASC,
        useStickyGroupSeparators: false,
        floatingHeader: true,
        physics: const BouncingScrollPhysics(),
      ),
    );
  }

  /// 构建分组头部
  Widget _buildGroupHeader(String groupValue) {
    return Container(
      height: 40.h,
      color: ColorsUtil.ypGreyColor,
      child: Row(
        children: [
          const SizedBox(width: 12),
          Text(
            groupValue,
            style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.w600,
              color: ColorsUtil.black85,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建列表项
  Widget _buildListItem(WorkerCardItemUIState element) {
    return GestureDetector(
      onTap: () {
        viewModel.onItemTap(element);
      },
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          border:
              Border(bottom: BorderSide(color: Color(0xfff5f6fa), width: 1.w)),
        ),
        height: 107.h,
        padding: EdgeInsets.only(left: 16.w, right: 8.w),
        child: Row(
          spacing: 8,
          crossAxisAlignment: CrossAxisAlignment.center, // 确保整行垂直居中
          children: [
            // Leading部分 - 现在会垂直居中
            _buildLeading(element),
            // 内容部分
            Expanded(
              child: _buildContentView(element),
            ),
            // 可以在这里添加trailing部分，比如箭头图标
            _buildTrailing(element),
          ],
        ),
      ),
    );
  }

  /// 构建列表项Leading部分
  Widget _buildLeading(WorkerCardItemUIState element) {
    return Obx(() {
      return Row(
        spacing: 6,
        children: [
          if (viewModel.listCanSelect.value)
            SizedBox(
              width: 18.w,
              height: 18.h,
              child: Image.asset(
                viewModel.selectedIds.contains(element.workerId)
                    ? Assets.commonWaaIcFindProCheck
                    : Assets.commonWaaIcFindProUncheck,
                width: 20,
                height: 20,
              ),
            ),
          WorkerAvatar(avatar: element.avatar, shortName: element.shortName),
        ],
      );
    });
  }

  Widget _buildContentView(WorkerCardItemUIState element) {
    return Column(
      spacing: 4,
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.center, // 垂直居中
      children: [
        Text(
          element.name,
          style: TextStyle(
            fontSize: 16.sp,
            fontWeight: FontWeight.bold,
            color: ColorsUtil.black85,
          ),
        ),
        Text(
          element.tel,
          style: TextStyle(
            fontSize: 14.sp,
            color: ColorsUtil.black45,
          ),
        ),
        _buildResumeStatus(element),
      ],
    );
  }

  /// 列表项右侧图标
  Widget _buildTrailing(WorkerCardItemUIState element) {
    return GestureDetector(
      onTap: () {
        viewModel.onItemSettingTap(element);
      },
      child: SizedBox(
        width: 40.w,
        height: 40.h,
        child: Icon(
          Icons.settings,
          color: ColorsUtil.black45,
          size: 20,
        ),
      ),
    );
  }

  /// 名片授权状态
  Widget _buildResumeStatus(WorkerCardItemUIState element) {
    final text = element.isIdentify ? '名片已授权' : '名片未授权';
    final color =
        element.isIdentify ? ColorsUtil.ypPrimaryColor : ColorsUtil.black45;
    final bgColor =
        element.isIdentify ? ColorsUtil.primaryColor15 : ColorsUtil.ypGreyColor;
    return Container(
      padding: EdgeInsets.symmetric(vertical: 2.h, horizontal: 4.w),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(2.r),
        color: bgColor,
      ),
      child: IntrinsicWidth(
        child: Row(
          children: [
            Icon(Icons.info, size: 12, color: color),
            const SizedBox(width: 4),
            Text(
              text,
              style: TextStyle(
                fontSize: 12.sp,
                color: color,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
