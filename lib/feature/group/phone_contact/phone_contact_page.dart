import 'package:flutter/material.dart';
import 'package:gdjg_pure_flutter/data/worker_data/worker_info/repo/model/workers_get_worker_info_biz_model.dart';
import 'package:gdjg_pure_flutter/feature/group/widget/worker_card_list/entity/worker_card_list_props.dart';
import 'package:gdjg_pure_flutter/feature/group/widget/worker_card_list/worker_card_list_view.dart';
import 'package:gdjg_pure_flutter/utils/route_util/base_page/base_ful_page.dart';
import 'package:gdjg_pure_flutter/utils/system_util/yprint.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/toast_util.dart';
import 'package:get/get.dart';
import 'package:gdjg_pure_flutter/widget/page_error_view.dart';
import 'package:gdjg_pure_flutter/widget/page_loading_view.dart';
import 'entity/phone_contact_props.dart';
import 'vm/phone_contact_viewmodel.dart';

/// 手机联系人
/// @date 2025/06/24
/// @param props 页面路由参数
/// @returns
/// @description PhoneContact页面入口
class PhoneContactPage extends BaseFulPage {
  const PhoneContactPage({super.key})
      : super(appBar: const YPAppBar(title: '手机联系人'));

  @override
  State createState() => _PhoneContactState();
}

class _PhoneContactState extends BaseFulPageState<PhoneContactPage> {
  late PhoneContactProps? props;
  final PhoneContactViewModel viewModel = PhoneContactViewModel();
  final WorkerCardListProps mockProps = WorkerCardListProps(
    canSelect: true,
    isStickyHeader: true,
    selectIds: [3174274, 3174273],
    data: [
      WorkersGetWorkerInfoBizModel(
        id: 3174274,
        name: "王五",
        tel: "137****7777",
        namePy: "W",
        avatar: "",
        isAgent: 0,
      ),
      WorkersGetWorkerInfoBizModel(
        id: 3174273,
        name: "赵六",
        tel: "136****6666",
        namePy: "Z",
        avatar: "",
        isAgent: 1,
      ),
      WorkersGetWorkerInfoBizModel(
        id: 3174294,
        name: "孙七",
        tel: "135****5555",
        namePy: "S",
        avatar: "",
        isAgent: 0,
      ),
      WorkersGetWorkerInfoBizModel(
        id: 3174290,
        name: "阿娇",
        tel: "139****6534",
        namePy: "A",
        avatar: "",
        isAgent: 0,
      ),
      WorkersGetWorkerInfoBizModel(
        id: 3174291,
        name: "阿莲",
        tel: "139****4312",
        namePy: "A",
        avatar: "",
        isAgent: 0,
        occ: OccBizModel(
          industryId: 2,
          industryName: "修/房屋翻新",
          occupationId: 301,
          occupationName: "电工/消防/弱电",
        ),
      ),
      WorkersGetWorkerInfoBizModel(
        id: 3174292,
        name: "阿尔法",
        tel: "139****1234",
        namePy: "A",
        avatar: "",
        isAgent: 0,
      ),
      WorkersGetWorkerInfoBizModel(
        id: 3174272,
        name: "张三",
        tel: "139****8888",
        namePy: "Z",
        avatar: "",
        isAgent: 0,
      ),
      WorkersGetWorkerInfoBizModel(
        id: 3174271,
        name: "李四",
        tel: "138****9999",
        namePy: "L",
        avatar: "",
        isAgent: 1,
      ),
      WorkersGetWorkerInfoBizModel(
        id: 3174275,
        name: "周八",
        tel: "134****4444",
        namePy: "Z",
        avatar: "",
        isAgent: 1,
      ),
      WorkersGetWorkerInfoBizModel(
        id: 3174287,
        name: "吴九",
        tel: "133****3333",
        namePy: "W",
        avatar: "",
        isAgent: 0,
      ),
      WorkersGetWorkerInfoBizModel(
        id: 3174276,
        name: "郑十",
        tel: "132****2222",
        namePy: "Z",
        avatar: "",
        isAgent: 1,
      ),
    ],
    onSelectCallBack: (selectIds) {
      yprint('页面接收数据-$selectIds');
      ToastUtil.showToast('$selectIds');
    },
  );

  @override
  void onPageRoute(Object? routeParams, bool fromLaunchTask) {
    super.onPageRoute(routeParams, fromLaunchTask);
    props = routeParams as PhoneContactProps?;
  }

  @override
  Widget yBuild(BuildContext context) {
    return Scaffold(
      body: Obx(() {
        if (viewModel.isLoading.value) {
          return const PageLoadingView();
        }
        if (viewModel.uiState.value.isShowError == true) {
          return PageErrorView(onReload: () => viewModel.fetchData());
        }
        return contentView();
      }),
    );
  }

  /// 实际展示的视图
  Widget contentView() {
    return Center(
      child: Column(
        children: [
          Text(viewModel.uiState.value.data.toString()),
          WorkerCardListView(props: mockProps),
        ],
      ),
    );
  }
}
