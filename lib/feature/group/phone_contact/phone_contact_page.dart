import 'package:flutter/material.dart';
import 'package:gdjg_pure_flutter/data/demo_setting_data/entity/worker_info/worker/workers_get_worker_info_entity.dart';
import 'package:gdjg_pure_flutter/feature/group/phone_contact/view/worker_card_list/entity/worker_card_list_props.dart';
import 'package:gdjg_pure_flutter/feature/group/phone_contact/view/worker_card_list/worker_card_list_view.dart';
import 'package:gdjg_pure_flutter/utils/route_util/base_page/base_ful_page.dart';
import 'package:get/get.dart';
import 'package:gdjg_pure_flutter/widget/page_error_view.dart';
import 'package:gdjg_pure_flutter/widget/page_loading_view.dart';
import 'entity/phone_contact_props.dart';
import 'vm/phone_contact_viewmodel.dart';

/// 手机联系人
/// @date 2025/06/24
/// @param props 页面路由参数
/// @returns
/// @description PhoneContact页面入口
class PhoneContactPage extends BaseFulPage {
  const PhoneContactPage({super.key})
      : super(appBar: const YPAppBar(title: '手机联系人'));

  @override
  State createState() => _PhoneContactState();
}

class _PhoneContactState extends BaseFulPageState<PhoneContactPage> {
  late PhoneContactProps? props;
  final PhoneContactViewModel viewModel = PhoneContactViewModel();
  final WorkerCardListProps mockProps = WorkerCardListProps(
    canSelect: true,
    data: [
      WorkersGetWorkerInfoAEntity(
        id: 53,
        name: "王五",
        tel: "137****7777",
        namePy: "W",
        avatar: "",
        isAgent: 0,
      ),
      WorkersGetWorkerInfoAEntity(
        id: 44,
        name: "赵六",
        tel: "136****6666",
        namePy: "Z",
        avatar: "",
        isAgent: 1,
      ),
      WorkersGetWorkerInfoAEntity(
        id: 35,
        name: "孙七",
        tel: "135****5555",
        namePy: "S",
        avatar: "",
        isAgent: 0,
      ),
      WorkersGetWorkerInfoAEntity(
        id: 2,
        name: "阿娇",
        tel: "139****6534",
        namePy: "A",
        avatar: "",
        isAgent: 0,
      ),
      WorkersGetWorkerInfoAEntity(
        id: 1,
        name: "阿莲",
        tel: "139****4312",
        namePy: "A",
        avatar: "",
        isAgent: 0,
      ),
      WorkersGetWorkerInfoAEntity(
        id: 10,
        name: "阿尔法",
        tel: "139****1234",
        namePy: "A",
        avatar: "",
        isAgent: 0,
      ),
      WorkersGetWorkerInfoAEntity(
        id: 11,
        name: "张三",
        tel: "139****8888",
        namePy: "Z",
        avatar: "",
        isAgent: 0,
      ),
      WorkersGetWorkerInfoAEntity(
        id: 12,
        name: "李四",
        tel: "138****9999",
        namePy: "L",
        avatar: "",
        isAgent: 1,
      ),
      WorkersGetWorkerInfoAEntity(
        id: 16,
        name: "周八",
        tel: "134****4444",
        namePy: "Z",
        avatar: "",
        isAgent: 1,
      ),
      WorkersGetWorkerInfoAEntity(
        id: 17,
        name: "吴九",
        tel: "133****3333",
        namePy: "W",
        avatar: "",
        isAgent: 0,
      ),
      WorkersGetWorkerInfoAEntity(
        id: 18,
        name: "郑十",
        tel: "132****2222",
        namePy: "Z",
        avatar: "",
        isAgent: 1,
      ),
    ],
  );

  @override
  void onPageRoute(Object? routeParams, bool fromLaunchTask) {
    super.onPageRoute(routeParams, fromLaunchTask);
    props = routeParams as PhoneContactProps?;
  }

  @override
  Widget yBuild(BuildContext context) {
    return Scaffold(
      body: Obx(() {
        if (viewModel.isLoading.value) {
          return const PageLoadingView();
        }
        if (viewModel.uiState.value.isShowError == true) {
          return PageErrorView(onReload: () => viewModel.fetchData());
        }
        return contentView();
      }),
    );
  }

  /// 实际展示的视图
  Widget contentView() {
    return Center(
      child: Column(
        children: [
          Text(viewModel.uiState.value.data.toString()),
          WorkerCardListView(props: mockProps),
        ],
      ),
    );
  }
}
