import 'package:gdjg_pure_flutter/model/RecordType.dart';

/// @date 2025/06/21
/// @description GroupFlowDetail页入参
class GroupProStatisticsProps {
  ///
  String workNoteId;

  ///
  String workNoteName;

  ///
  String? startTime;

  ///
  String? endTime;

  ///
  double? deptId;

  ///
  List<RwaRecordType>? businessType;

  ///
  String? workerIds;

  GroupProStatisticsProps({
    this.workNoteId = '',
    this.workNoteName = '',
    this.startTime,
    this.endTime,
    this.businessType,
    this.workerIds,
  });

  @override
  String toString() {
    return 'GroupProStatisticsProps{data: $workNoteId,$workNoteName,$startTime,$endTime,$businessType,$workerIds}';
  }
}
