import 'package:gdjg_pure_flutter/feature/group/common_params_model/worker_model.dart';
import 'package:gdjg_pure_flutter/model/RecordType.dart';

/// @date 2025/06/21
/// @description GroupFlowDetail页入参
class GroupProStatisticsProps {
  ///
  String workNoteId;

  ///
  String workNoteName;

  ///
  DateTime? startTime;

  ///
  DateTime? endTime;

  ///
  double? deptId;

  ///
  List<RwaRecordType>? businessType;

  ///
  List<WorkerModel>? workerIds;

  GroupProStatisticsProps({
    this.workNoteId = '',
    this.workNoteName = '',
    this.startTime,
    this.endTime,
    this.businessType,
    this.workerIds,
  });

  @override
  String toString() {
    return 'GroupProStatisticsProps{data: $workNoteId,$workNoteName,$startTime,$endTime,$businessType,$workerIds}';
  }
}
