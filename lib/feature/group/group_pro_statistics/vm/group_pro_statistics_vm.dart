import "package:gdjg_pure_flutter/common_uistate/statistice_helper.dart";
import "package:gdjg_pure_flutter/common_uistate/statistics_ui_state.dart";
import "package:gdjg_pure_flutter/data/group_data/group_pro_statistics/ds/model/param/group_project_get_group_worker_count_param_model.dart";
import "package:gdjg_pure_flutter/feature/group/group_com_uistate/group_worker_statistics_ui_state.dart";
import "package:gdjg_pure_flutter/feature/group/group_pro_statistics/entity/group_pro_statistics_props.dart";
import "package:gdjg_pure_flutter/feature/group/group_pro_statistics/ui_rep/group_pro_statistics_ui_rep.dart";
import "package:gdjg_pure_flutter/feature/group/group_pro_statistics/vm/protocol/group_pro_total_statistics_ui_state.dart";
import "package:gdjg_pure_flutter/feature/group/group_pro_statistics/vm/protocol/group_pro_worker_statistics_ui_state.dart";
import "package:gdjg_pure_flutter/model/RecordType.dart";
import "package:get/get.dart";

/// @date 2025/06/21
/// @description GroupFlowDetail页ViewModel
/// 1. 处理数据的加载状态，如loading和错误视图等
/// 2. 观测UIRep中的业务实体转为UIState
/// 3. 接受来自view的意图并分发事件进行处理（暂无）
/// 4. 对View层暴露事件（操作视图和处理一些与系统权限交互等场景）
class GroupProStatisticsViewModel {
  var isLoading = false.obs;
  var isShowError = false.obs;
  //用于没有传递开始时间时从后台获取账本第一次记工时间，并通知UI刷新开始时间
  var netStartTime = "2020-01-02".obs;

  //工人统计列表
  var workerStatisticsUIState =
      GroupProWorkerStatisticsListUIState(isEmpty: false).obs;
  var totalStatisticsUIState =
      GroupProTotalStatisticsListUIState(isEmpty: false).obs;
  var uiRep = GroupProStatisticsUIRep();

  GroupProStatisticsProps? props;

  GroupProStatisticsViewModel() {
    ever(uiRep.entity, (value) {
      convertEntityToUIState(value);
    });

    ever(uiRep.startTime, (value) {
      props?.startTime = value;
      netStartTime.value = value;
    });
  }

  init(GroupProStatisticsProps? props) {
    this.props = props;
    fetchData();
  }

  ///更新开始和结束时间
  void updateDateRange(DateTime startTime, DateTime endTime, List<RwaRecordType>? businessType) {
    props?.businessType = businessType;
    props?.startTime = _formatDate(startTime);
    props?.endTime = _formatDate(endTime);
    fetchData(); // 请求新数据
  }

  ///更新月份
  void updateMonthRange(int year, int month) {
    var now = DateTime.now();
    var startTime = "$year-${month.toString().padLeft(2, '0')}-01";
    DateTime endOfMonth = DateTime(year, month + 1, 0); // 获取该月最后一天
    DateTime endDateValue =
        endOfMonth.isAfter(now) ? now : endOfMonth; // 如果是未来月份，则限制到今天
    var endTime = _formatDate(endDateValue);
    props?.startTime = startTime;
    props?.endTime = endTime;
    fetchData(); // 请求新数据
  }

  ///格式化日期
  String _formatDate(DateTime date) {
    return "${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}";
  }

  /// 初始化数据同步，如果不需要loading，则在这里去掉
  void fetchData() async {
    isLoading.value = true;
    try {
      var businessTypeStr = props?.businessType
              ?.map((type) => type.code.value.toString())
              .join(',') ??
          '';
      var params = GroupProjectGetGroupWorkerCountParamModel()
        ..work_note = props?.workNoteId ?? ''
        ..start_time = props?.startTime ?? ''
        ..end_time = props?.endTime ?? ''
        ..business_type = businessTypeStr;
      await uiRep.getGroupWorkerCount(params);
      // 这里的result仅做弹窗之类的处理，不在此处转为UI实体
    } catch (e) {
      isShowError.value = true;
    } finally {
      isLoading.value = false;
    }
  }

  /// 将实体数据转换为UIState
  /// @param entity
  /// @returns
  void convertEntityToUIState(GroupProStatisticsUIRepEntity entity) {
    if (entity.data == null) return;
    // 1.工人统计
    List<GroupWorkerStatisticsUIState> workerStatisticsList = [];
    entity.data?.worker?.forEach((element) {
      workerStatisticsList.add(GroupWorkerStatisticsUIState(
          workerName: element.name,
          workerId: element.id,
          statisticsItemList: StatisticsUIStateHelper.buildStatisticsItem(
              element,
              isShowUnsettle: false,
              isShowDailyWages: false)));
    });
    workerStatisticsUIState.value.isEmpty = workerStatisticsList.isEmpty;
    workerStatisticsUIState.value.list = workerStatisticsList;
    workerStatisticsUIState.refresh();

    // 2. 总计
    List<StatisticsItemUIState> totalStatisticsList = [];
    var all = entity.data?.all;
    if (all != null) {
      totalStatisticsList = StatisticsUIStateHelper.buildStatisticsItem(all,
          isShowDailyWages: false);
    }
    totalStatisticsUIState.value.isEmpty = workerStatisticsList.isEmpty;
    totalStatisticsUIState.value.list = totalStatisticsList;
    totalStatisticsUIState.refresh();
  }
}
