import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/feature/group/common_params_model/worker_model.dart';
import 'package:gdjg_pure_flutter/feature/group/group_pro_bill/entity/group_pro_bill_props.dart';
import 'package:gdjg_pure_flutter/feature/group/group_pro_statistics/entity/group_pro_statistics_props.dart';
import 'package:gdjg_pure_flutter/feature/group/group_pro_statistics/view/total_statistics_list_view.dart';
import 'package:gdjg_pure_flutter/feature/group/group_pro_statistics/view/worker_statistics_list_view.dart';
import 'package:gdjg_pure_flutter/feature/group/group_pro_statistics/vm/group_pro_statistics_vm.dart';
import 'package:gdjg_pure_flutter/feature/group/group_statistics/view/empty_view.dart';
import 'package:gdjg_pure_flutter/init_module/init_route.dart';
import 'package:gdjg_pure_flutter/utils/route_util/base_page/base_ful_page.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/appbar_util.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';
import 'package:gdjg_pure_flutter/widget/combined_filter_widget.dart';
import 'package:gdjg_pure_flutter/widget/page_error_view.dart';
import 'package:gdjg_pure_flutter/widget/round_tab_indicator_widget.dart';
import 'package:get/get.dart';

/// @date 2025/06/21
/// @param props 页面路由参数
/// @returns
/// @description GroupFlowDetail 班组单个项目统计页面 工人和总计页面
class GroupProStatisticsPage extends BaseFulPage {
  const GroupProStatisticsPage({super.key}) : super(appBar: null);

  @override
  createState() => _GroupFlowDetailPage();
}

class _GroupFlowDetailPage<GroupProStatisticsPage> extends BaseFulPageState
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  late GroupProStatisticsViewModel viewModel = GroupProStatisticsViewModel();

  @override
  onPageRoute(Object? routeParams, bool fromLaunchTask) {
    var props = routeParams as GroupProStatisticsProps?;
    viewModel.init(props);
  }

  @override
  initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _tabController.addListener(() {
      setState(() {});
    });
  }

  @override
  void dispose() {
    _tabController.removeListener(() {});
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget yBuild(BuildContext context) {
    return Scaffold(
      appBar: AppBarUtil.buildCommonAppBar(
          title: viewModel.props?.workNoteName ?? "",
          onBackTap: () => YPRoute.closePage()),
      body: Obx(() {
        if (viewModel.isShowError.value) {
          return PageErrorView(onReload: () => viewModel.fetchData());
        }
        return contentView();
      }),
    );
  }

  /// 实际展示的视图，需要处理：
  /// 1. 画出UI
  /// 2. 绑定UIState
  /// 3. 处理事件监听
  /// 4. 向VM传递来自View层的事件
  Widget contentView() {
    // 进行事件处理
    // handleGroupFlowDetailVMEvent(props.vm)
    return Column(
      children: [
        Divider(height: 8.h, color: Color(0xFFF0F0F0), thickness: 8.h),
        CombinedFilterWidget(
          onFilterChanged: (filter) {
            viewModel.updateDateRange(
                filter.startDate, filter.endDate, filter.selectedTypes);
          },
        ),
        Divider(height: 8.h, color: Color(0xFFF0F0F0), thickness: 8.h),
        _builderTabView()
      ],
    );
  }

  Widget _builderTabView() {
    return Flexible(
      child: Column(
        children: [
          Container(
            color: Colors.white,
            height: 44,
            child: TabBar(
                controller: _tabController,
                labelColor: Color(0xFF8A8A99),
                unselectedLabelColor: Color(0xFF8A8A99),
                labelStyle: const TextStyle(
                  fontSize: 18,
                ),
                // unselectedLabelStyle: const TextStyle(fontSize: 16),
                indicator: RoundUnderlineTabIndicator(
                  borderSide:
                      BorderSide(width: 3, color: ColorsUtil.primaryColor),
                ),
                dividerHeight: 1,
                dividerColor: Color(0xFFE6E6E6),
                tabs: [
                  Tab(
                    child: Text('工人'),
                  ),
                  Tab(
                    child: Text('总计'),
                  ),
                ]),
          ),
          Flexible(
            flex: 1,
            child: Container(
              color: Colors.white,
              child: TabBarView(
                  controller: _tabController,
                  // physics: NeverScrollableScrollPhysics(),
                  children: [
                    _buildWorkerStatisticsList(),
                    _buildWorkerStatisticsTotal(),
                  ]),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建工友统计列表
  Widget _buildWorkerStatisticsList() {
    return Obx(() {
      if (viewModel.workerStatisticsUIState.value.isEmpty) {
        return const EmptyView(
          subtitle: '暂无数据',
          imagePath: 'https://cdn.yupaowang.com/jgjz/empty-note.png',
        );
      }
      return WorkerStatisticsListView(
        list: viewModel.workerStatisticsUIState.value.list ?? [],
        onItemTap: (item) {
          GroupProBillProps params = GroupProBillProps()
            ..workNoteId = viewModel.props?.workNoteId ?? ""
            ..workNoteName = viewModel.props?.workNoteName ?? ""
            ..startTime = viewModel.props?.startTime
            ..endTime = viewModel.props?.endTime
            ..deptId = viewModel.props?.deptId
            ..workers = [WorkerModel(workerId: item.workerId, workerName: item.workerName)];
          YPRoute.openPage(RouteNameCollection.groupProBill, params: params);
        },
      );
    });
  }

  /// 构建工友总计
  Widget _buildWorkerStatisticsTotal() {
    return Obx(() {
      if (viewModel.totalStatisticsUIState.value.isEmpty) {
        return EmptyView(
          subtitle: '暂无数据',
          imagePath: 'https://cdn.yupaowang.com/jgjz/empty-note.png',
        );
      }
      return TotalStatisticsListView(
        list: viewModel.totalStatisticsUIState.value.list ?? [],
        onItemTap: (item) {
          GroupProBillProps params = GroupProBillProps()
            ..workNoteId = viewModel.props?.workNoteId ?? ""
            ..workNoteName = viewModel.props?.workNoteName ?? ""
            ..startTime = viewModel.props?.startTime
            ..endTime = viewModel.props?.endTime
            ..deptId = viewModel.props?.deptId;
          YPRoute.openPage(RouteNameCollection.groupProBill, params: params);
        },
      );
    });
  }
}
