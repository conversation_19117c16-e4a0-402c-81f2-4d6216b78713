// 文件路径: lib/widget/worker_statistics_list_view.dart
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/common_uistate/statistics_ui_state.dart';
import 'package:gdjg_pure_flutter/feature/group/group_com_uistate/group_worker_statistics_ui_state.dart';
import 'package:gdjg_pure_flutter/utils/system_util/font_util.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';

// 独立组件
class WorkerStatisticsListView extends StatelessWidget {
  final List<GroupWorkerStatisticsUIState> list;
  final Function(GroupWorkerStatisticsUIState)? onItemTap;

  const WorkerStatisticsListView(
      {super.key, required this.list, this.onItemTap});

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      padding: EdgeInsets.zero,
      itemCount: list.length ?? 0,
      itemBuilder: (context, index) {
        final item = list[index];
        return _buildWorkerItem(context, item);
      },
    );
  }

  Widget _buildWorkerItem(
      BuildContext context, GroupWorkerStatisticsUIState uiState) {
    return Container(
      color: const Color(0xFFF0F0F0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 工友姓名
          Container(
            width: double.infinity,
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 10.h),
            child: Text(
              uiState.workerName ?? "",
              style: TextStyle(
                fontSize: 16.sp,
                color: const Color(0xFF323233),
              ),
            ),
          ),
          // 工友统计项目

          ...?uiState.statisticsItemList?.map((item) => GestureDetector(
              onTap: () {
                onItemTap?.call(uiState);
              },
              child: _buildStatisticsItem(item))),
        ],
      ),
    );
  }

  Widget _buildStatisticsItem(StatisticsItemUIState item) {
    return Container(
      color: Colors.white,
      child: Column(
        children: [
          Container(
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 10.h),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                // 左侧颜色指示条
                Container(
                  width: 3.w,
                  height: 16.h,
                  margin: EdgeInsets.only(right: 8.w),
                  decoration: BoxDecoration(
                    color: item.isRecordWorkType == true
                        ? ColorsUtil.primaryColor
                        : const Color(0xFFFFA011),
                    borderRadius: BorderRadius.circular(1.5.w),
                  ),
                ),
                // 类型和笔数
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      item.typeName ?? "",
                      style: TextStyle(
                        fontSize: 16.sp,
                        color: const Color(0xFF323233),
                      ),
                    ),
                    if (item.unitWorkNum != null)
                      Text(
                        item.unitWorkNum ?? '',
                        style: TextStyle(
                          fontSize: 15.sp,
                          color: const Color(0xFF9D9DB3),
                        ),
                      ),
                  ],
                ),
                // 详情
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // 分项
                      if (item.unitWorkTypeName != null)
                        Container(
                          margin: EdgeInsets.only(left: 4.w),
                          child: Text(
                            item.unitWorkTypeName ?? '',
                            style: TextStyle(
                              fontSize: 16.sp,
                              color: const Color(0xFF323233),
                            ),
                          ),
                        ),
                      // 上班和加班详情
                      Container(
                        margin: EdgeInsets.only(left: 8.w),
                        child: Text(
                          item.detail ?? "",
                          style: TextStyle(
                            fontSize: 15.sp,
                            color: const Color(0xFF323233),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                // 右侧金额
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Text(
                          item.feeMoney ?? '',
                          style: TextStyle(
                            fontFamily: FontUtil.fontCondMedium,
                            fontSize: 22.sp,
                            fontWeight: FontWeight.w600,
                            color: item.isRecordWorkType == true
                                ? ColorsUtil.primaryColor
                                : const Color(0xFFFFB84D),
                          ),
                        ),
                        // 总计信息
                        if (item.total != null) ...[
                          Text(
                            item.total!,
                            style: TextStyle(
                              fontSize: 15.sp,
                              color: const Color(0xFF323233),
                            ),
                          ),
                        ],
                      ],
                    ),
                  ],
                ),
              ],
            ),
          ),
          // 分割线
          Container(
            height: 0.5.h,
            color: const Color(0xFFE6E6E6),
          ),
        ],
      ),
    );
  }
}
