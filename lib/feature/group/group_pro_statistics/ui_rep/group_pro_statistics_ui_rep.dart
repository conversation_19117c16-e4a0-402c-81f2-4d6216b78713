import 'package:gdjg_pure_flutter/data/group_data/group_pro_statistics/ds/model/param/group_project_get_group_worker_count_param_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_pro_statistics/repo/group_pro_repo.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_pro_statistics/repo/model/group_project_get_group_worker_count_biz_model.dart';
import 'package:gdjg_pure_flutter/data/note_time_data/ds/param/business_get_note_first_time_param_model.dart';
import 'package:gdjg_pure_flutter/data/note_time_data/note_time_repo.dart';
import 'package:get/get.dart';

/// @date 2025/06/21
/// @description GroupFlowDetail页UI仓库
/// 1.为vm提供业务数据
/// 2.解耦视图层与业务仓库之间的耦合
class GroupProStatisticsUIRep {
  final _groupProRepo = GroupProRepo();

  final _noteTimeRepo = NoteTimeRepo();

  /// 实体数据
  var entity = GroupProStatisticsUIRepEntity().obs;

  var startTime = "2020-01-01".obs;

  /// 初始化数据同步，返回同步是否成功的状态给UI层
  /// 如果成功，则将业务数据存储在entity中
  Future<GroupProStatisticsUIRepEntity> getGroupWorkerCount(
      GroupProjectGetGroupWorkerCountParamModel params) async {
    if(params.start_time.isEmpty) {
      var startTime = await getNoteFirstTime(params.work_note);
      params.start_time = startTime ?? "2020-01-01";
      // 调用网络的方法获取数据
      var result = await _groupProRepo.getGroupWorkerCount(params);
      // 返回成功的情况
      entity.value = GroupProStatisticsUIRepEntity(data: result.getSucData());
    }
    // 调用网络的方法获取数据
    var result = await _groupProRepo.getGroupWorkerCount(params);
    // 返回成功的情况
    entity.value = GroupProStatisticsUIRepEntity(data: result.getSucData());
    return entity.value;
  }

  Future<String> getNoteFirstTime(String workNoteId) async {
    var params = BusinessGetNoteFirstTimeParamModel(work_note: workNoteId);
    var result = await _noteTimeRepo.getNoteFirstTime(params);
    startTime.value = result.getSucData()?.date ?? "2020-01-01";
    return startTime.value;
  }
}

class GroupProStatisticsUIRepEntity {
  GroupProjectGetGroupWorkerCountBizModel? data;

  GroupProStatisticsUIRepEntity({this.data});

  @override
  String toString() {
    return 'GroupFlowDetailUIRepEntity{data: $data}';
  }
}
