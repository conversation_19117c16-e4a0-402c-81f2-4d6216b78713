import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/feature/group/group_edit_record_work/view/vm/edit_daily_work_vm.dart';
import 'package:gdjg_pure_flutter/utils/system_util/font_util.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';
import 'package:get/get.dart';

/// 班组短工修改
class EditDailyWorkView extends StatefulWidget {
  const EditDailyWorkView({super.key});

  @override
  State<EditDailyWorkView> createState() => _EditDailyWorkView();
}

class _EditDailyWorkView extends State<EditDailyWorkView>
    with AutomaticKeepAliveClientMixin {
  final EditDailyWorkVM _vvm = Get.find<EditDailyWorkVM>();
  final TextEditingController _normalRateController = TextEditingController();

  // 用于存储ever监听器的引用，以便在dispose时取消
  final List<Worker> _workers = [];

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    _normalRateController.addListener(() {
      if (mounted) {
        _vvm.changeDailyPrice(_normalRateController.text);
      }
    });

    // 存储监听器引用以便后续取消
    _workers.add(ever(_vvm.dailyUS.money, (value) {
      if (mounted && _normalRateController.text != value) {
        _normalRateController.text = value;
      }
    }));
  }

  @override
  void dispose() {
    super.dispose();
    for (var element in _workers) {
      element.dispose();
    }
    _normalRateController.dispose();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // 必须调用以保持页面状态
    return Column(
      children: [
        _buildDiaryMoneyView(),
        Divider(
            height: 1,
            color: Color(0xFFF5F5F5),
            thickness: 1,
            indent: 16.h,
            endIndent: 16.h),
        // 照片上传区域
        _buildPhotoSection(),
        // 备注区域
        _buildRemarkSection(),
      ],
    );
  }

  /// 金额区域
  Widget _buildDiaryMoneyView() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.only(left: 16.w, right: 16.w, top: 16.h),
      color: Colors.white,
      child: Row(
        children: [
          Text(
            '金额：',
            style: TextStyle(
              fontSize: 16.sp,
              color: const Color(0xFF000000),
            ),
          ),
          Expanded(child: _buildInputField(_normalRateController)),
        ],
      ),
    );
  }

  ///输入框布局
  Widget _buildInputField(TextEditingController? controller) {
    return SizedBox(
      width: double.infinity,
      height: 50.h,
      child: TextField(
        maxLength: 7,
        controller: controller,
        textAlign: TextAlign.left,
        inputFormatters: <TextInputFormatter>[
          LengthLimitingTextInputFormatter(7),
        ],
        // 设置光标颜色
        cursorColor: ColorsUtil.primaryColor,
        keyboardType: TextInputType.text,
        decoration: InputDecoration(
          counterText: '',
          hintText: '0.00',
          hintStyle: TextStyle(
            fontSize: 36.sp,
            color: ColorsUtil.hintFontColor,
            fontFamily: FontUtil.fontCondMedium,
          ),
          // 关键代码：添加 hint
          isDense: true,
          border: OutlineInputBorder(
            borderSide: BorderSide(width: 0, color: Colors.transparent),
          ),
          enabledBorder: OutlineInputBorder(
            borderSide: const BorderSide(width: 0, color: Colors.transparent),
          ),
          focusedBorder: OutlineInputBorder(
            borderSide: const BorderSide(width: 0, color: Colors.transparent),
          ),
        ),
        style: TextStyle(
          fontSize: 36.sp,
          color: Color(0xFF323232),
          fontFamily: FontUtil.fontCondMedium,
        ),
      ),
    );
  }

  /// 照片上传区域
  Widget _buildPhotoSection() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(16.w),
      color: Colors.white,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '照片：',
            style: TextStyle(
              fontSize: 16.sp,
              color: const Color(0xFF000000),
            ),
          ),
          GestureDetector(
            onTap: () {
              _vvm.baseVm.showPhotoSelectionDialog();
            },
            child: Container(
              width: 80.w,
              height: 80.w,
              decoration: BoxDecoration(
                color: const Color(0xFFF5F5F5),
                borderRadius: BorderRadius.circular(8.r),
                border: Border.all(
                  color: const Color(0xFFE0E0E0),
                  width: 1.w,
                ),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.file_upload_outlined,
                    size: 24.sp,
                    color: const Color(0xFF999999),
                  ),
                  SizedBox(height: 4.h),
                  Text(
                    '添加照片/视频',
                    style: TextStyle(
                      fontSize: 10.sp,
                      color: const Color(0xFF999999),
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建备注区域
  Widget _buildRemarkSection() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(16.w),
      color: Colors.white,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '备注：',
            style: TextStyle(
              fontSize: 16.sp,
              color: const Color(0xFF000000),
            ),
          ),
          Expanded(
            child: GestureDetector(
              onTap: () {
                _vvm.baseVm.setOnJumpToNotePage();
              },
              child: Container(
                height: 90.h,
                decoration: BoxDecoration(
                  color: const Color(0xFFF5F5F5),
                  borderRadius: BorderRadius.circular(2.r),
                ),
                child: Padding(
                  padding: EdgeInsets.only(left: 12.w, top: 6.h),
                  child: Obx(
                        () => Text(
                      _vvm.baseVm.baseUs.remark.length<=0
                          ? '请输入备注...'
                          : _vvm.baseVm.baseUs.remark,
                      style: TextStyle(
                        fontSize: 14.sp,
                        color:_vvm.baseVm.baseUs.remark.length<=0
                            ? const Color(0xFF999999)
                            : const Color(0xFF000000),
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
