import 'package:get/get.dart';

class WorkTimeUIState {
  // UI状态变量
  /// 当前选中的工时类型索引 (0:1个工, 1:半个工, 2:选小时, 3:休息, 4:上下午)
  var index = 0;

  /// "1个工"按钮显示的文本 (如"1个工"、"2个工"等)
  var workTimeText = "1个工";

  /// 半个工
  var workHalfWorkText = "半个工";

  /// "选小时"显示的文本 (如"选小时"、"8小时"等)
  var workHoursText = '选小时';

  var restText = '休息';

  var morningAndAfternoonText = '上下午';

  WorkTimeUIState({
    this.index = 0,
    this.workTimeText = "1个工",
    this.workHalfWorkText = "半个工",
    this.workHoursText = '选小时',
    this.restText = '休息',
    this.morningAndAfternoonText = '上下午',
  });
}

/// 点工UI状态管理 - 纯状态管理层
class EditPointWorkUS {

  /// 上班UI状态
  final _workTimeUIState = WorkTimeUIState().obs;
  get workTimeUIState => _workTimeUIState.value;

  setWorkTimeUIState(WorkTimeUIState workTimeUIState) {
    _workTimeUIState.value = workTimeUIState;
  }

  /// 加班文案
  final _overtimeText = "无加班".obs;

  get overtimeText => _overtimeText.value;

  setOvertimeText(String text) {
    _overtimeText.value = text;
    _overtimeText.refresh();
  }

  final _hasOvertime = false.obs;

  get hasOvertime => _hasOvertime.value;

  setHasOvertime(bool hasOvertime) {
    _hasOvertime.value = hasOvertime;
    _hasOvertime.refresh();
  }

  final _feeStandardId = false.obs;

  get hasFeeStandardId => _feeStandardId.value;

  setFeeStandardId(bool hasFeeStandardId) {
    _feeStandardId.value = hasFeeStandardId;
    _feeStandardId.refresh();
  }

  /// 计算得出的工资金额
  final _money = "设置工价".obs;

  get money => _money.value;

  setMoney(String money) {
    _money.value = money;
    _money.refresh();
  }

  /// 工资规则文案
  final _wageText = "".obs;

  get wageText => _wageText.value;

  setWageText(String text) {
    _wageText.value = text;
    _wageText.refresh();
  }

  /// 当前选中的时间段索引 (0:上午, 1:下午) - 用于"上下午"工时类型
  final _selectedTimeSlotIndex = 0.obs;

  get selectedTimeSlotIndex => _selectedTimeSlotIndex.value;

  setSelectedTimeSlotIndex(int index) {
    _selectedTimeSlotIndex.value = index;
    _selectedTimeSlotIndex.refresh();
  }
}
