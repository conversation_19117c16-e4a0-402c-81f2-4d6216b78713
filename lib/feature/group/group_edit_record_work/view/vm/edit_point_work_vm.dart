import 'package:flutter/material.dart';
import 'package:gdjg_pure_flutter/data/fee_standard_data/repo/model/fee_standard_biz_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_pro_update/ds/model/param/group_pro_update_param_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_pro_update/repo/model/update_pro_info_biz_models.dart';
import 'package:gdjg_pure_flutter/feature/group/common_params_model/worker_model.dart';
import 'package:gdjg_pure_flutter/feature/group/dialog/wage_rule_setting/WageRulesSettingDialog.dart';
import 'package:gdjg_pure_flutter/feature/group/dialog/wage_rule_setting/entity/wage_rules_props.dart';
import 'package:gdjg_pure_flutter/feature/group/group_edit_record_work/view/vm/edit_base_vm.dart';
import 'package:gdjg_pure_flutter/feature/group/group_edit_record_work/view/vm/edit_point_work_us.dart';
import 'package:gdjg_pure_flutter/model/RecordNoteType.dart';
import 'package:gdjg_pure_flutter/model/RecordType.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/utils/system_util/string_util.dart';
import 'package:gdjg_pure_flutter/widget/hour_keyboard_dialog.dart';
import 'package:gdjg_pure_flutter/widget/hour_selector_dialog.dart';
import 'package:gdjg_pure_flutter/widget/work_hours_keyboard_dialog.dart';

/// 点工ViewModel
class EditPointWorkVM {
  final baseVm = EditBaseVM();
  final pointUS = EditPointWorkUS();

  List<WorkerModel>? _workers;
  String? _workNoteName;

  //上班工天
  var _workTime = 0.0;

  // 上班小时
  var _workTimeHour = 0.0;

  //加班小时
  var _overtime = 0.0;

  //加班工天
  var _overtimeWork = 0.0;

  //上午工天
  var _morningWorkTime = "";

  // 上午小时
  var _morningWorkTimeHour = "";

  // 下午工天
  var _afternoonWorkTime = "";

  // 下午小时
  var _afternoonWorkTimeHour = "";

  // 选择工位
  var _userChooseSpotwork = 1;

  // 工资规则
  FeeStandardBizModel? _feeStandard;

  ///初始化需要获取的数据
  @override
  void init(
      {RwaRecordType? businessType,
      String? workNoteId,
      double? billId,
      List<WorkerModel>? workers}) {
    baseVm.init(
        businessType: businessType, workNoteId: workNoteId, billId: billId);
    _workers = workers;
  }

  ///对外保留参数用于提交
  @override
  GroupProUpdateParamModel getUpdateParams() {
    return GroupProUpdateParamModel(
      business_type: baseVm.businessType?.code.value ?? 0,
      identity: 1,
      note: baseVm.note,
      morning_work_time: _morningWorkTime,
      morning_work_time_hour: _morningWorkTimeHour,
      afternoon_work_time: _afternoonWorkTime,
      afternoon_work_time_hour: _afternoonWorkTimeHour,
      work_time: _workTime,
      work_time_hour: _workTimeHour,
      overtime: _overtime,
      overtime_work: _overtimeWork,
      user_choose_spotwork: _userChooseSpotwork,
      fee_standard_id: _feeStandard?.feeStandardId ?? '',
    );
  }

  ///获取一个账本的数据
  @override
  Future<UpdateProInfoBizModel?> fetchUpdateProInfo() async {
    var result = await baseVm.fetchUpdateProInfo();
    if (result != null) {
      _coverEntityUIState(result);
      return result;
    }
    return null;
  }

  ///获取工资规则
  getFeedStandard() async {}

  setWorkerIds(List<WorkerModel> workers) {
    _workers = workers;
  }

  setWorkNoteName(String? name) {
    _workNoteName = name;
  }

  /// 选择工作时长按钮
  void _setWorkTime(double work) {
    _workTime = work;
    _userChooseSpotwork = 1;
    pointUS.setWorkTimeUIState(WorkTimeUIState(
        index: 0, workTimeText: "${work.trimTrailingZeros()}个工"));
    // 重新计算工资
    _calculateSalary();
  }

  /// 点击半个工
  void onHardTimeTap() {
    _workTime = 0.5;
    _workTimeHour = 0;
    _userChooseSpotwork = 1;
    pointUS.setWorkTimeUIState(WorkTimeUIState(index: 1));
    // 重新计算工资
    _calculateSalary();
  }

  /// 设置上班小时
  void _setWorkHours(double hours) {
    _workTimeHour = hours;
    _workTime = 0;
    _userChooseSpotwork = 2;
    pointUS.setWorkTimeUIState(WorkTimeUIState(
        index: 2, workHoursText: '${hours.trimTrailingZeros()}小时'));
    _calculateSalary();
  }

  /// 休息
  void onRestTap() {
    _workTime = 0;
    _workTimeHour = 0;
    _morningWorkTime = "";
    _morningWorkTimeHour = "";
    _afternoonWorkTime = "";
    _afternoonWorkTimeHour = "";
    _userChooseSpotwork = 1;
    pointUS.setWorkTimeUIState(WorkTimeUIState(index: 3));
    _calculateSalary();
  }

  /// 上下午
  void _setMorningAndAfternoon(String text) {
    _userChooseSpotwork = 3;
    pointUS.setWorkTimeUIState(
        WorkTimeUIState(index: 4, morningAndAfternoonText: text));
    _calculateSalary();
  }

  /// 加班工天
  void _setOverTimeWork(double work) {
    _overtimeWork = work;
    pointUS.setOvertimeText("${work.trimTrailingZeros()}工");
    pointUS.setHasOvertime(work > 0);
    _calculateSalary();
  }

  /// 加班小时
  void _setOverTime(double hours) {
    _overtime = hours;
    pointUS.setOvertimeText("${hours.trimTrailingZeros()}小时");
    pointUS.setHasOvertime(hours > 0);
    _calculateSalary();
  }

  ///清除加班
  void onDeleteOverTimeTap(bool bool) {
    _overtime = 0;
    _overtimeWork = 0;
    pointUS.setHasOvertime(false);
    _calculateSalary();
  }

  /// 计算工资
  void _calculateSalary() {
    var rule = _feeStandard;
    if (rule == null) {
      pointUS.setMoney('设置工价');
      return;
    }
    //上班工天
    var workTime = _workTime;
    //加班工天
    var overTime = _overtimeWork;

    // 上班小时
    var workTimeHour = _workTimeHour;
    // 加班小时
    var overTimeHour = _overtime;
    // 总工天(上班+加班)
    final allTime = workTime + overTime;

    // 计算工钱
    // 上班1个工多少钱
    final workingHoursPrice = double.tryParse(rule.workingHoursPrice) ?? 0;
    // 上班多少小时1个工
    final workingHoursStandard =
        double.tryParse(rule.workingHoursStandard) ?? 0;
    // 加班1个小时多少钱(默认加班按小时)
    var overtimeHoursPrice = double.tryParse(rule.overtimeHoursPrice) ?? 0;

    if (rule.overtimeType == 1) {
      // 如果是加班按工天
      // 加班多少小时1个工
      final overtimeHoursStandard =
          double.tryParse(rule.overtimeHoursStandard) ?? 0;
      // 加班1个小时多少钱
      overtimeHoursPrice = workingHoursPrice / overtimeHoursStandard;
    }
    // 上班工钱+加班工天的工钱
    final ntAmount = allTime * workingHoursPrice +
        workTimeHour * workingHoursPrice / workingHoursStandard;
    // 加班工钱
    final otAmount = overTimeHour * overtimeHoursPrice;
    // 总工钱
    final amount = ntAmount + otAmount;
    pointUS.setMoney(amount.formatDoubleToMoney());
  }

  /// 显示工时时长键盘（1个工按钮点击）
  void showWorkHoursKeyboard(BuildContext context) {
    YPRoute.openDialog(
      builder: (context) => WorkHoursKeyboardDialog(
        onConfirm: (value) {
          _setWorkTime(value);
        },
      ),
      alignment: Alignment.bottomCenter,
    );
  }

  /// 显示小时选择弹窗（选小时按钮点击）
  void showHourSelectionDialog(BuildContext context) {
    YPRoute.openDialog(
      builder: (context) => HourKeyboardDialog(
        onConfirm: (value) {
          _setWorkHours(value);
        },
      ),
      alignment: Alignment.bottomCenter,
    );
  }

  /// 显示上下午选择弹窗
  void showTimeSlotDialog(BuildContext context) {
    // TODO: 实现上下午选择弹窗
    // _setMorningAndAfternoon();
  }

  /// 显示加班选择弹窗
  void showOvertimeDialog(BuildContext context) {
    YPRoute.openDialog(
      builder: (context) => HourSelectorDialog(
        initHour: _overtime,
        initWorkDay: _overtimeWork,
        enableKeyboard: true,
        onConfirm: (result) {
          if (result.type == HourSelectionType.workDay) {
            _setOverTimeWork(result.value);
          } else {
            _setOverTime(result.value);
          }
        },
      ),
      alignment: Alignment.bottomCenter,
    );
  }

  /// 显示工资计算说明弹窗
  void showSalaryCalculationDialog() {
    var props = WageRulesProps(
      title: _workNoteName ?? '工资规则',
      feeStandard: _feeStandard,
      workers: _workers,
      workNoteId: baseVm.workNoteId,
      businessType: baseVm.businessType,
      recordNoteType: RecordNoteType.group,
      deleteRequestApi: false,
      confirmRequestApi: true,
      isShowDelete: _feeStandard?.hasFeeStandard() == true,
    );
    WageRulesSettingDialog.show(
        props: props,
        onSelected: (feeInfo) {
          _feeStandard = feeInfo;
          pointUS.setFeeStandardId(feeInfo?.hasFeeStandard() == true);
          pointUS.setWageText(feeInfo?.getWageText() ?? '');
          _calculateSalary();
        });
  }

  void _coverEntityUIState(UpdateProInfoBizModel data) {
    //是否上班休息
    if (data.hasRest()) {
      onRestTap();
    } else {
      if (data.hasWorkTime()) {
        //是否有上班工天
        if (data.isHardTime()) {
          onHardTimeTap();
        } else {
          _setWorkTime(data.workTime);
        }
      } else if (data.hasWorkHour()) {
        //是否有上班小时
        _setWorkHours(data.workTimeHour);
      } else if (data.hasMorningAndAfternoon()) {
        _morningWorkTime = data.morningWorkTime;
        _morningWorkTimeHour = data.morningWorkTimeHour;
        _afternoonWorkTime = data.afternoonWorkTime;
        _afternoonWorkTimeHour = data.afternoonWorkTimeHour;
        _setMorningAndAfternoon(_getUpOrDownText());
      }
    }

    //设置加班
    if (data.hasOverTime()) {
      if (data.overtimeWork != 0) {
        _setOverTimeWork(data.overtimeWork);
      } else {
        _setOverTime(data.overtime);
      }
    }
    //设置工资规则和工价
    _feeStandard = data.feeInfo;
    var hasFeeStandard = data.hasFeeStandard();
    pointUS.setFeeStandardId(hasFeeStandard);
    if (hasFeeStandard) {
      pointUS.setWageText(data.feeInfo?.getWageText() ?? '');
      _calculateSalary();
    } else {
      pointUS.setMoney('');
      pointUS.setWageText('');
    }
    baseVm.setRemark(data.note);
  }

  String _formatWorkTime(String? workTimeStr, String? workTimeHourStr) {
    double? workTime = double.tryParse(workTimeStr ?? '');
    double? workTimeHour = double.tryParse(workTimeHourStr ?? '');

    if (workTime != null && workTime > 0) {
      return '$workTime个工';
    } else if (workTimeHour != null && workTimeHour > 0) {
      return '$workTimeHour小时';
    } else {
      return '休息';
    }
  }

  String _getUpOrDownText() {
    bool hasMorning =
        _morningWorkTime.isNotEmpty || _morningWorkTimeHour.isNotEmpty;
    bool hasAfternoon =
        _afternoonWorkTime.isNotEmpty || _afternoonWorkTimeHour.isNotEmpty;

    List<String> result = [];

    if (hasMorning) {
      String morningText =
          _formatWorkTime(_morningWorkTime, _morningWorkTimeHour);
      result.add('上午$morningText');
    }

    if (hasAfternoon) {
      String afternoonText =
          _formatWorkTime(_afternoonWorkTime, _afternoonWorkTimeHour);
      result.add('下午$afternoonText');
    }

    return result.join('-');
  }

  void onDeleteFeeStandardIdTap() {
    _feeStandard = null;
    pointUS.setFeeStandardId(false);
    pointUS.setWageText('');
    _calculateSalary();
  }
}
