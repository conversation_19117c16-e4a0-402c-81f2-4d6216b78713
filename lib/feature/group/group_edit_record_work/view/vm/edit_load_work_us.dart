import 'package:get/get.dart';

/// 点工UI状态管理 - 纯状态管理层
class EditLoadWorkUS {
  final _unitWorkTypeName = "".obs;
  final unitNum = "".obs;
  final unitPrice = "".obs;
  final _unitWorkTypeUnit = "".obs;
  final money = "".obs;

  get unitWorkTypeName => _unitWorkTypeName.value;

  get unitWorkTypeUnit => _unitWorkTypeUnit.value;

  setUnitWorkTypeName(String value) {
    _unitWorkTypeName.value = value;
    _unitWorkTypeName.refresh();
  }

  setUnitNum(String value) {
    unitNum.value = value;
    unitNum.refresh();
  }

  setUnitPrice(String value) {
    unitPrice.value = value;
    unitPrice.refresh();
  }

  setUnitWorkTypeUnit(String value) {
    _unitWorkTypeUnit.value = value;
    _unitWorkTypeUnit.refresh();
  }

  setMoney(String value) {
    money.value = value;
    money.refresh();
  }
}
