import 'package:gdjg_pure_flutter/data/group_data/group_pro_update/ds/model/param/group_pro_update_param_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_pro_update/repo/model/update_pro_info_biz_models.dart';
import 'package:gdjg_pure_flutter/feature/group/group_edit_record_work/view/vm/edit_base_vm.dart';
import 'package:gdjg_pure_flutter/feature/group/group_edit_record_work/view/vm/edit_daily_work_us.dart';
import 'package:gdjg_pure_flutter/model/RecordType.dart';

/// 点工ViewModel
class EditDailyWorkVM {
  final baseVm = EditBaseVM();
  final dailyUS = EditDailyWorkUS();

  String? _money;

  ///初始化需要获取的数据
  @override
  void init({RwaRecordType? businessType, String? workNoteId, double? billId}) {
    baseVm.init(
        businessType: businessType, workNoteId: workNoteId, billId: billId);
  }

  ///对外保留参数用于提交
  @override
  GroupProUpdateParamModel getUpdateParams() {
    return GroupProUpdateParamModel(
      business_type: baseVm.businessType?.code.value ?? 0,
      identity: 1,
      note: baseVm.note,
      user_choose_spotwork: 1,
      money: _money,
    );
  }

  ///获取一个账本的数据
  @override
  Future<UpdateProInfoBizModel?> fetchUpdateProInfo() async {
    var result = await baseVm.fetchUpdateProInfo();
    if (result != null) {
      changeDailyPrice(result.money);
      return result;
    }
    return null;
  }

  /// 修改短工工资
  void changeDailyPrice(String text) {
    _money = text;
    dailyUS.setMoney(text);
  }
}
