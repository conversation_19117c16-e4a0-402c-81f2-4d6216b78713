import 'package:get/get.dart';

/// 点工UI状态管理 - 纯状态管理层
class EditBaseUS {
  /// 备注信息
  final _remark = ''.obs;

  get remark => _remark.value;

  setRemark(String text) {
    _remark.value = text;
    _remark.refresh();
  }

  /// 照片URL列表
  final _photoUrls = <String>[].obs;

  get photoUrls => _photoUrls.value;

  setPhotoUrls(List<String> urls) {
    _photoUrls.value = urls;
    _photoUrls.refresh();
  }

  void addPhoto(String photoUrl) {
    _photoUrls.add(photoUrl);
  }

  void removePhoto(int index) {
    if (index >= 0 && index < _photoUrls.length) {
      _photoUrls.removeAt(index);
    }
  }
}
