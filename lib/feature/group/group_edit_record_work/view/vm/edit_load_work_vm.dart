import 'package:gdjg_pure_flutter/data/group_data/group_pro_update/ds/model/param/group_pro_update_param_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_pro_update/repo/model/update_pro_info_biz_models.dart';
import 'package:gdjg_pure_flutter/feature/group/group_edit_record_work/view/vm/edit_base_vm.dart';
import 'package:gdjg_pure_flutter/feature/group/group_edit_record_work/view/vm/edit_load_work_us.dart';
import 'package:gdjg_pure_flutter/model/RecordType.dart';

/// 工量ViewModel
class EditLoadWorkVM {
  final baseVm = EditBaseVM();
  final loadUs = EditLoadWorkUS();

  String? _unitWorkTypeName;
  String? _unitNum;
  String? _unitPrice;
  String? _unitWorkTypeUnit;
  String? _money;
  double? _unitWorkType;

  ///获取数据

  ///初始化需要获取的数据
  @override
  void init({RwaRecordType? businessType, String? workNoteId, double? billId}) {
    baseVm.init(
        businessType: businessType, workNoteId: workNoteId, billId: billId);
  }

  ///对外保留参数用于提交
  @override
  GroupProUpdateParamModel getUpdateParams() {
    return GroupProUpdateParamModel(
      business_type: baseVm.businessType?.code.value ?? 0,
      identity: 1,
      unit_work_type_name: _unitWorkTypeName,
      unit_num: _unitNum,
      unit_price: _unitPrice,
      unit_work_type_unit: _unitWorkTypeUnit,
      unit_work_type: _unitWorkType,
      note: baseVm.note,
      user_choose_spotwork: 1,
      money: _money,
    );
  }

  ///获取一个账本的数据
  @override
  Future<UpdateProInfoBizModel?> fetchUpdateProInfo() async {
    var result = await baseVm.fetchUpdateProInfo();
    if (result != null) {
      setUnitWorkTypeName(result.unitWorkTypeName);
      setUnitWorkType(result.unitWorkType);
      setUnitNum(result.unitNum);
      setUnitPrice(result.unitPrice);
      setUnitWorkTypeUnit(result.unitWorkTypeUnit);
      setMoney(result.money);
      return result;
    }
    return null;
  }

  ///修改分项的id
  void setUnitWorkType(double text) {
    _unitWorkType = text;
  }

  /// 修改分项
  void setUnitWorkTypeName(String text) {
    _unitWorkTypeName = text;
    loadUs.setUnitWorkTypeName(text);
  }

  /// 修改数量
  void setUnitNum(String text) {
    _unitNum = text;
    loadUs.setUnitNum(text);
  }

  /// 修改单价
  void setUnitPrice(String text) {
    _unitPrice = text;
    loadUs.setUnitPrice(text);
  }

  ///修改单价单位
  void setUnitWorkTypeUnit(String text) {
    _unitWorkTypeUnit = text;
    loadUs.setUnitWorkTypeUnit(text);
  }

  /// 修改工钱
  void setMoney(String text) {
    _money = text;
    loadUs.setMoney(text);
  }
}
