import 'package:gdjg_pure_flutter/data/group_data/group_pro_update/ds/model/param/group_pro_update_param_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_pro_update/repo/group_pro_update.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_pro_update/repo/model/update_pro_info_biz_models.dart';
import 'package:gdjg_pure_flutter/feature/common_page/notes/entity/notes_props.dart';
import 'package:gdjg_pure_flutter/feature/group/common_params_model/worker_model.dart';
import 'package:gdjg_pure_flutter/feature/group/group_edit_record_work/view/vm/edit_base_us.dart';
import 'package:gdjg_pure_flutter/feature/group/group_event_bus/group_edit_wage_event_bus_model.dart';
import 'package:gdjg_pure_flutter/init_module/init_route.dart';
import 'package:gdjg_pure_flutter/model/RecordType.dart';
import 'package:gdjg_pure_flutter/utils/event_bus_util/event_bus_util.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/toast_util.dart';

class EditBaseVM {
  final _groupProUpdateRepo = GroupProUpdateRepo();
  final baseUs = EditBaseUS();

  RwaRecordType? businessType;

  double? billId;

  String? workNoteId;

  var note = "";

  ///初始化需要获取的数据
  void init({RwaRecordType? businessType, String? workNoteId, double? billId}) {
    this.businessType = businessType;
    this.workNoteId = workNoteId;
    this.billId = billId;
  }

  ///获取一个账本的数据
  Future<UpdateProInfoBizModel?> fetchUpdateProInfo() async {
    var result = await _groupProUpdateRepo.getUpdateProInfo(billId, workNoteId);
    if (result.isOK()) {
      var data = result.getSucData();
      return data;
    } else {
      ToastUtil.showToast(result.fail?.errorMsg ?? "");
    }
    return null;
  }

  /// 确认提交
  Future<void> onSubmitTap(GroupProUpdateParamModel params) async {
    var result =
        await _groupProUpdateRepo.updateGroupPro(billId, workNoteId, params);
    if (result.isOK()) {
      ToastUtil.showToast("修改成功");
      // 刷新流水，日历
      EventBusUtil.emit(GroupEditWageEventBusModel());
      YPRoute.closePage();
    } else {
      ToastUtil.showToast(result.fail?.errorMsg ?? "");
    }
  }

  ///  删除记工
  Future<void> onDeleteTap() async {
    var result = await _groupProUpdateRepo.deleteGroupPro(billId, workNoteId);
    if (result.isOK()) {
      ToastUtil.showToast("删除成功");
      // 刷新流水，日历
      EventBusUtil.emit(GroupEditWageEventBusModel());
      YPRoute.closePage();
    } else {
      ToastUtil.showToast(result.fail?.errorMsg ?? "");
    }
  }

  setRemark(String remark) {
    baseUs.setRemark(remark);
  }

  /// 删除照片
  void deletePhoto(int index) {
    baseUs.removePhoto(index);
  }

  /// 输入备注
  void setInputRemark(String text) {
    baseUs.setRemark(text);
  }

  /// 显示照片选择弹窗
  void showPhotoSelectionDialog() {
    // TODO: 实现照片选择弹窗
  }

  /// 拍照
  void takePhoto() {
    // TODO: 实现拍照逻辑
  }

  /// 从相册选择
  void pickFromGallery() {
    // TODO: 实现从相册选择逻辑
  }

  Future<void> setOnJumpToNotePage() async {
    var result = await YPRoute.openPage(
      RouteNameCollection.notes,
      params: NotesProps(pageSource: NotesPageSource.groupEditRecordWork),
    );
    if (result != null) {
      note = result as String;
      baseUs.setRemark(result.toString());
    }
  }
}
