import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/feature/group/group_edit_record_work/view/vm/edit_load_work_vm.dart';
import 'package:gdjg_pure_flutter/utils/system_util/font_util.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';
import 'package:get/get.dart';

/// 班组工量修改
class EditLoadWorkView extends StatefulWidget {
  const EditLoadWorkView({super.key});

  @override
  State<EditLoadWorkView> createState() => _EditLoadWorkView();
}

class _EditLoadWorkView extends State<EditLoadWorkView>
    with AutomaticKeepAliveClientMixin {
  final EditLoadWorkVM _vvm = Get.find<EditLoadWorkVM>();
  final TextEditingController _unitNumController = TextEditingController();
  final TextEditingController _unitPriceController = TextEditingController();
  final TextEditingController _unitMoneyController = TextEditingController();

  // 用于存储ever监听器的引用，以便在dispose时取消
  final List<Worker> _workers = [];

  @override
  void initState() {
    super.initState();
    _unitNumController.addListener(() {
      if (mounted) {
        _vvm.setUnitNum(_unitNumController.text);
      }
    });
    _unitPriceController.addListener(() {
      if (mounted) {
        _vvm.setUnitPrice(_unitPriceController.text);
      }
    });
    _unitMoneyController.addListener(() {
      if (mounted) {
        _vvm.setMoney(_unitMoneyController.text);
      }
    });

    // 存储监听器引用以便后续取消
    _workers.add(ever(_vvm.loadUs.unitNum, (value) {
      if (mounted && _unitNumController.text != value) {
        _unitNumController.text = value;
      }
    }));

    _workers.add(ever(_vvm.loadUs.unitPrice, (value) {
      if (mounted && _unitPriceController.text != value) {
        _unitPriceController.text = value;
      }
    }));

    _workers.add(ever(_vvm.loadUs.money, (value) {
      if (mounted && _unitMoneyController.text != value) {
        _unitMoneyController.text = value;
      }
    }));
  }

  @override
  void dispose() {
    super.dispose();
    for (var element in _workers) {
      element.dispose();
    }
    _unitNumController.dispose();
    _unitPriceController.dispose();
    _unitMoneyController.dispose();
  }

  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context); // 必须调用以保持页面状态
    return SingleChildScrollView(
      child: Column(
        children: [
          //分项区域
          _buildUnitWorkTypeNameView(),
          //工程量
          _buildUnitWorkNum(),
          //单价
          _buildUnitPriceView(),
          // //工钱
          _buildUnitMoneyView(),
          // 照片上传区域
          _buildPhotoSection(),
          // 备注区域
          _buildRemarkSection(),
        ],
      ),
    );
  }

  Widget _buildUnitWorkTypeNameView() {
    return Container(
      width: double.infinity,
      height: 50.h,
      margin: EdgeInsets.only(top: 16.h),
      padding: EdgeInsets.only(left: 16.w, right: 16.w),
      decoration: BoxDecoration(
          color: Colors.white,
          border:
              Border(bottom: BorderSide(color: Color(0xFFF5F5F5), width: 1.w))),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            '分项：',
            style: TextStyle(
              fontSize: 16.sp,
              color: const Color(0xFF000000),
            ),
          ),
          Obx(
            () => Text(
              _vvm.loadUs.unitWorkTypeName,
              style: TextStyle(
                fontSize: 17.sp,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 工程量
  Widget _buildUnitWorkNum() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.only(left: 16.w, right: 16.w, top: 16.h),
      decoration: BoxDecoration(
          color: Colors.white,
          border:
              Border(bottom: BorderSide(color: Color(0xFFF5F5F5), width: 1.w))),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            '工程量：',
            style: TextStyle(
              fontSize: 16.sp,
              color: const Color(0xFF000000),
            ),
          ),
          Expanded(child: _buildInputField(_unitNumController)),
        ],
      ),
    );
  }

  /// 工程量
  Widget _buildUnitPriceView() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.only(left: 16.w, right: 16.w, top: 16.h),
      decoration: BoxDecoration(
          color: Colors.white,
          border:
              Border(bottom: BorderSide(color: Color(0xFFF5F5F5), width: 1.w))),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            '单价：',
            style: TextStyle(
              fontSize: 16.sp,
              color: const Color(0xFF000000),
            ),
          ),
          Expanded(child: _buildInputField(_unitPriceController)),
          Obx(() {
            return Column(
              children: [
                Text(
                  "元/${_vvm.loadUs.unitWorkTypeUnit}",
                  style: TextStyle(
                    fontSize: 16.sp,
                    color: const Color(0xFF000000),
                  ),
                ),
              ],
            );
          })
        ],
      ),
    );
  }

  /// 工钱
  Widget _buildUnitMoneyView() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.only(left: 16.w, right: 16.w, top: 16.h),
      decoration: BoxDecoration(
          color: Colors.white,
          border:
              Border(bottom: BorderSide(color: Color(0xFFF5F5F5), width: 1.w))),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            '工钱：',
            style: TextStyle(
              fontSize: 16.sp,
              color: const Color(0xFF000000),
            ),
          ),
          Expanded(child: _buildInputField(_unitMoneyController)),
          Text(
            "元",
            style: TextStyle(
              fontSize: 16.sp,
              color: const Color(0xFF000000),
            ),
          ),
        ],
      ),
    );
  }

  ///输入框布局
  Widget _buildInputField(TextEditingController? controller) {
    return SizedBox(
      width: double.infinity,
      height: 40.h,
      child: TextField(
        maxLength: 7,
        controller: controller,
        textAlign: TextAlign.right,
        inputFormatters: <TextInputFormatter>[
          LengthLimitingTextInputFormatter(7),
        ],
        // 设置光标颜色
        cursorColor: ColorsUtil.primaryColor,
        cursorHeight: 32.h,
        keyboardType: TextInputType.text,
        decoration: InputDecoration(
          counterText: '',
          hintText: '0.00',
          hintStyle: TextStyle(
            fontSize: 15.sp,
            color: ColorsUtil.hintFontColor,
            fontFamily: FontUtil.fontCondMedium,
          ),
          // 关键代码：添加 hint
          isDense: true,
          border: OutlineInputBorder(
            borderSide: BorderSide(width: 0, color: Colors.transparent),
          ),
          enabledBorder: OutlineInputBorder(
            borderSide: const BorderSide(width: 0, color: Colors.transparent),
          ),
          focusedBorder: OutlineInputBorder(
            borderSide: const BorderSide(width: 0, color: Colors.transparent),
          ),
        ),
        style: TextStyle(
          fontSize: 15.sp,
          color: Color(0xFF181818),
        ),
      ),
    );
  }

  /// 照片上传区域
  Widget _buildPhotoSection() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(16.w),
      color: Colors.white,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '照片：',
            style: TextStyle(
              fontSize: 16.sp,
              color: const Color(0xFF000000),
            ),
          ),
          GestureDetector(
            onTap: () {
              _vvm.baseVm.showPhotoSelectionDialog();
            },
            child: Container(
              width: 80.w,
              height: 80.w,
              decoration: BoxDecoration(
                color: const Color(0xFFF5F5F5),
                borderRadius: BorderRadius.circular(8.r),
                border: Border.all(
                  color: const Color(0xFFE0E0E0),
                  width: 1.w,
                ),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.file_upload_outlined,
                    size: 24.sp,
                    color: const Color(0xFF999999),
                  ),
                  SizedBox(height: 4.h),
                  Text(
                    '添加照片/视频',
                    style: TextStyle(
                      fontSize: 10.sp,
                      color: const Color(0xFF999999),
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建备注区域
  Widget _buildRemarkSection() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(16.w),
      color: Colors.white,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '备注：',
            style: TextStyle(
              fontSize: 16.sp,
              color: const Color(0xFF000000),
            ),
          ),
          Expanded(
            child: GestureDetector(
              onTap: () {
                _vvm.baseVm.setOnJumpToNotePage();
              },
              child: Container(
                height: 90.h,
                decoration: BoxDecoration(
                  color: const Color(0xFFF5F5F5),
                  borderRadius: BorderRadius.circular(2.r),
                ),
                child: Padding(
                  padding: EdgeInsets.only(left: 12.w, top: 6.h),
                  child: Obx(
                        () => Text(
                      _vvm.baseVm.baseUs.remark.length<=0
                          ? '请输入备注...'
                          : _vvm.baseVm.baseUs.remark,
                      style: TextStyle(
                        fontSize: 14.sp,
                        color:_vvm.baseVm.baseUs.remark.length<=0
                            ? const Color(0xFF999999)
                            : const Color(0xFF000000),
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
