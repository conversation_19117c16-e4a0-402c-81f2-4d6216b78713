import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/feature/group/group_edit_record_work/view/vm/edit_point_work_vm.dart';
import 'package:gdjg_pure_flutter/iconfont/iconfont.dart';
import 'package:get/get.dart';

/// 班组点工修改
class EditPointWorkView extends StatefulWidget {
  const EditPointWorkView({super.key});

  @override
  State<EditPointWorkView> createState() => _EditPointWorkView();
}

class _EditPointWorkView extends State<EditPointWorkView>
    with AutomaticKeepAliveClientMixin {
  final EditPointWorkVM _vvm = Get.find<EditPointWorkVM>();

  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context); // 必须调用以保持页面状态
    return SingleChildScrollView(
      child: Column(
        children: [
          // 上班状态选择区域
          _buildWorkStatusSection(),
          // 加班选择区域
          _buildOvertimeSection(),
          // 工资显示区域
          _buildSalarySection(),
          // 照片上传区域
          _buildPhotoSection(),
          // 备注区域
          _buildRemarkSection(),
        ],
      ),
    );
  }

  /// 上班状态选择区域
  Widget _buildWorkStatusSection() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.only(left: 16.w, right: 16.w, top: 16.h),
      color: Colors.white,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                '上班：',
                style: TextStyle(
                  fontSize: 16.sp,
                  color: const Color(0xFF000000),
                ),
              ),
              Obx(
                () => Expanded(
                  child: Row(
                    spacing: 5.w,
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Container(
                        constraints: BoxConstraints(minWidth: 88.w),
                        child: _buildWorkTimeButton(
                            0, _vvm.pointUS.workTimeUIState.workTimeText),
                      ),
                      Expanded(
                        child: _buildWorkTimeButton(
                            1, _vvm.pointUS.workTimeUIState.workHalfWorkText),
                      ),
                      Expanded(
                        child: _buildWorkTimeButton(
                            2, _vvm.pointUS.workTimeUIState.workHoursText),
                      ),
                      _buildWorkTimeButton(
                          3, _vvm.pointUS.workTimeUIState.restText,
                          padding: EdgeInsets.symmetric(horizontal: 12.w)),
                    ],
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: 8.h),
          Row(
            children: [
              SizedBox(width: 48.w),
              Obx(() => Container(
                    constraints: BoxConstraints(minWidth: 88.w),
                    child: _buildWorkTimeButton(4,
                        _vvm.pointUS.workTimeUIState.morningAndAfternoonText),
                  )),
              const Spacer(flex: 6),
            ],
          ),
        ],
      ),
    );
  }

  /// 工作时长按钮
  Widget _buildWorkTimeButton(int index, String text,
      {EdgeInsets padding = const EdgeInsets.all(0)}) {
    final isSelected = _vvm.pointUS.workTimeUIState.index == index;
    return GestureDetector(
      onTap: () {
        if (index == 0) {
          _vvm.showWorkHoursKeyboard(context);
        } else if (index == 1) {
          _vvm.onHardTimeTap();
        } else if (index == 2) {
          _vvm.showHourSelectionDialog(context);
        } else if (index == 3) {
          _vvm.onRestTap();
        } else if (index == 4) {
          _vvm.showTimeSlotDialog(context);
        }
      },
      child: Container(
        height: 34.h,
        decoration: BoxDecoration(
          color: isSelected ? const Color(0xFF5290FD) : const Color(0xFFF5F5F5),
          borderRadius: BorderRadius.circular(3.r),
        ),
        child: Center(
          child: index == 0
              ? Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      text,
                      style: TextStyle(
                        fontSize: 14.sp,
                        color:
                            isSelected ? Colors.white : const Color(0xFF000000),
                      ),
                      maxLines: 1,
                    ),
                    SizedBox(width: 2.w),
                    IconFont(
                      IconNames.saasEditPen,
                      size: 14,
                      color: isSelected ? "#FFFFFF" : "#000000",
                    ),
                  ],
                )
              : Padding(
                  padding: padding,
                  child: Text(
                    text,
                    style: TextStyle(
                      fontSize: 14.sp,
                      color:
                          isSelected ? Colors.white : const Color(0xFF000000),
                    ),
                    maxLines: 1,
                  ),
                ),
        ),
      ),
    );
  }

  /// 加班选择区域
  Widget _buildOvertimeSection() {
    return GestureDetector(
      onTap: () {
        _vvm.showOvertimeDialog(context);
      },
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.only(left: 16.w, right: 16.w, top: 15.h),
        color: Colors.white,
        child: Row(
          children: [
            Text(
              '加班：',
              style: TextStyle(
                fontSize: 16.sp,
                color: const Color(0xFF000000),
              ),
            ),
            _buildOvertimeButton(),
          ],
        ),
      ),
    );
  }

  ///加班按钮
  Widget _buildOvertimeButton() {
    return Obx(() {
      return GestureDetector(
        onTap: () {
          _vvm.showOvertimeDialog(context);
        },
        child: Container(
          height: 34.h,
          constraints: BoxConstraints(minWidth: 88.w),
          decoration: BoxDecoration(
            color: _vvm.pointUS.hasOvertime
                ? const Color(0xFF5290FD)
                : const Color(0xFFF5F5F5),
            borderRadius: BorderRadius.circular(3.r),
          ),
          child: Center(
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  _vvm.pointUS.hasOvertime ? _vvm.pointUS.overtimeText : '无加班',
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: _vvm.pointUS.hasOvertime
                        ? Colors.white
                        : const Color(0xFF000000),
                  ),
                ),
                SizedBox(width: 4.w),
                _buildOvertimeIcon(),
              ],
            ),
          ),
        ),
      );
    });
  }

  Widget _buildOvertimeIcon() {
    if (_vvm.pointUS.hasOvertime) {
      return GestureDetector(
        onTap: () {
          _vvm.onDeleteOverTimeTap(false);
        },
        child: IconFont(
          IconNames.saasClose,
          size: 15,
          color: "#FFFFFF",
        ),
      );
    }
    return IconFont(
      IconNames.saasEditPen,
      size: 14,
    );
  }

  /// 工资设置区域
  Widget _buildSalarySection() {
    return Container(
      width: double.infinity,
      margin: EdgeInsets.only(top: 16.h),
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      alignment: Alignment.center,
      constraints: BoxConstraints(minHeight: 44.h),
      color: Colors.white,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                '工资：',
                style: TextStyle(
                  fontSize: 16.sp,
                  color: const Color(0xFF000000),
                ),
              ),
              Obx(() => Text(
                    '${_vvm.pointUS.hasFeeStandardId ? _vvm.pointUS.money : '设置工价'}',
                    style: TextStyle(
                      fontSize: _vvm.pointUS.hasFeeStandardId ? 28.sp : 16.sp,
                      color: _vvm.pointUS.hasFeeStandardId
                          ? const Color.fromRGBO(255, 160, 17, 1)
                          : const Color(0xFFCCCCCC),
                      fontWeight: FontWeight.bold,
                    ),
                  )),
              const Spacer(),
              GestureDetector(
                onTap: () {
                  _vvm.showSalaryCalculationDialog();
                },
                behavior: HitTestBehavior.opaque,
                child: Obx(
                  () => Row(
                    children: [
                      Text(
                        _vvm.pointUS.hasFeeStandardId
                            ? _vvm.pointUS.wageText
                            : '',
                        style: TextStyle(
                          fontSize: 14.sp,
                          color: const Color(0xFF999999),
                        ),
                      ),
                      SizedBox(width: 4),
                      if (_vvm.pointUS.hasFeeStandardId) ...[
                        GestureDetector(
                            onTap: () {
                              _vvm.onDeleteFeeStandardIdTap();
                            },
                            child: IconFont(IconNames.saasClose, size: 18.w)),
                      ] else ...[
                        IconFont(IconNames.saasArrowRight, size: 18.w),
                      ],
                    ],
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 照片上传区域
  Widget _buildPhotoSection() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(16.w),
      color: Colors.white,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '照片：',
            style: TextStyle(
              fontSize: 16.sp,
              color: const Color(0xFF000000),
            ),
          ),
          GestureDetector(
            onTap: () {
              _vvm.baseVm.showPhotoSelectionDialog();
            },
            child: Container(
              width: 80.w,
              height: 80.w,
              decoration: BoxDecoration(
                color: const Color(0xFFF5F5F5),
                borderRadius: BorderRadius.circular(8.r),
                border: Border.all(
                  color: const Color(0xFFE0E0E0),
                  width: 1.w,
                ),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.file_upload_outlined,
                    size: 24.sp,
                    color: const Color(0xFF999999),
                  ),
                  SizedBox(height: 4.h),
                  Text(
                    '添加照片/视频',
                    style: TextStyle(
                      fontSize: 10.sp,
                      color: const Color(0xFF999999),
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建备注区域
  Widget _buildRemarkSection() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(16.w),
      color: Colors.white,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '备注：',
            style: TextStyle(
              fontSize: 16.sp,
              color: const Color(0xFF000000),
            ),
          ),
          Expanded(
            child: GestureDetector(
              onTap: () {
                _vvm.baseVm.setOnJumpToNotePage();
              },
              child: Container(
                height: 90.h,
                decoration: BoxDecoration(
                  color: const Color(0xFFF5F5F5),
                  borderRadius: BorderRadius.circular(2.r),
                ),
                child: Padding(
                  padding: EdgeInsets.only(left: 12.w, top: 6.h),
                  child: Obx(
                    () => Text(
                      _vvm.baseVm.baseUs.remark.length <= 0
                          ? '请输入备注...'
                          : _vvm.baseVm.baseUs.remark,
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: _vvm.baseVm.baseUs.remark.length <= 0
                            ? const Color(0xFF999999)
                            : const Color(0xFF000000),
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
