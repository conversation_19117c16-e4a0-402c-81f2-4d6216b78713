import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/feature/group/group_edit_record_work/entity/group_edit_record_work_props.dart';
import 'package:gdjg_pure_flutter/feature/group/group_edit_record_work/view/edit_daily_work_view.dart';
import 'package:gdjg_pure_flutter/feature/group/group_edit_record_work/view/edit_load_work_view.dart';
import 'package:gdjg_pure_flutter/feature/group/group_edit_record_work/view/edit_point_work_view.dart';
import 'package:gdjg_pure_flutter/feature/group/group_edit_record_work/vm/group_edit_record_work_viewmodel.dart';
import 'package:gdjg_pure_flutter/generated/assets.dart';
import 'package:gdjg_pure_flutter/iconfont/iconfont.dart';
import 'package:gdjg_pure_flutter/utils/route_util/base_page/base_ful_page.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/utils/system_util/date_util.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/appbar_util.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';
import 'package:get/get.dart';

/// @date 2025/07/03
/// @param props 页面路由参数
/// @returns
/// @description GroupEditRecordWork页面入口 班组修改记工页面
class GroupEditRecordWorkPage extends BaseFulPage {
  const GroupEditRecordWorkPage({super.key}) : super(appBar: null);

  @override
  createState() => _GroupEditRecordWorkPage();
}

class _GroupEditRecordWorkPage<GroupEditWagePage> extends BaseFulPageState {
  late final GroupEditRecordWorkViewModel _viewModel;

  @override
  void onPageRoute(Object? routeParams, bool fromLaunchTask) {
    super.onPageRoute(routeParams, fromLaunchTask);
    var props = routeParams as GroupEditRecordWorkProps?;
    _viewModel = GroupEditRecordWorkViewModel(props);
  }

  @override
  Widget yBuild(BuildContext context) {
    return Scaffold(
      appBar: AppBarUtil.buildCommonAppBar(
        title: _viewModel.title,
        onBackTap: () => YPRoute.closePage(),
      ),
      resizeToAvoidBottomInset: false,
      backgroundColor: Color(0xFFFFFFFF),
      body: contentView(),
      bottomNavigationBar: _buildBottomView(),
    );
  }

  /// 实际展示的视图，需要处理：
  /// 1. 画出UI
  /// 2. 绑定UIState
  /// 3. 处理事件监听
  /// 4. 向VM传递来自View层的事件
  Widget contentView() {
    return Column(
      children: [
        _buildTipsView(),
        _buildProjectView(),
        Divider(thickness: 8, color: Color(0xFFF0F0F0)),
        Expanded(child: _buildContentView()),
      ],
    );
  }

  Widget _buildContentView() {
    if (_viewModel.us.showWorkView) {
      return EditPointWorkView();
    } else if (_viewModel.us.showDailyView) {
      return EditDailyWorkView();
    } else if (_viewModel.us.showWorkLoadView) {
      return EditLoadWorkView();
    } else {
      return Container();
    }
  }

  ///顶部提示，班组才会显示
  Widget _buildTipsView() {
    return Obx(
      () => Container(
        color: _viewModel.us.tips.bgColor,
        padding: EdgeInsets.symmetric(vertical: 8, horizontal: 16),
        child: Row(
          children: [
            IconFont(IconNames.saasInfo, color: _viewModel.us.tips.iconColor),
            SizedBox(width: 9),
            Text(_viewModel.us.tips.text,
                style: TextStyle(
                    color: _viewModel.us.tips.textColor, fontSize: 14)),
          ],
        ),
      ),
    );
  }

  /// 构建日期项目和工友
  Widget _buildProjectView() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16),
      width: double.infinity,
      child: Column(
        children: [
          SizedBox(
            width: double.infinity,
            height: 45,
            child: Center(
              child: Row(
                children: [
                  Text('日期：',
                      style: TextStyle(fontSize: 17, color: Color(0xFF323233))),
                  Obx(
                    () => Text(
                      DateUtil.formatDate(_viewModel.us.dateTime),
                      style: TextStyle(
                          fontSize: 17,
                          color: Color(0xFF323233),
                          fontWeight: FontWeight.w500),
                    ),
                  ),
                  Spacer(),
                  SizedBox(width: 4),
                  Image.asset(Assets.commonIconArrowRightGrey,
                      width: 18, height: 18)
                ],
              ),
            ),
          ),
          Divider(
            thickness: 1,
            color: Color(0xFFF5F5F5),
          ),
          SizedBox(
            width: double.infinity,
            height: 45,
            child: Center(
              child: Row(
                children: [
                  Text('项目：',
                      style: TextStyle(fontSize: 17, color: Color(0xFF323233))),
                  Obx(
                    () => Text(_viewModel.us.workNoteName,
                        style: TextStyle(
                            fontSize: 17,
                            color: Color(0xFF323233),
                            fontWeight: FontWeight.w500)),
                  ),
                ],
              ),
            ),
          ),
          Divider(
            thickness: 1,
            color: Color(0xFFF5F5F5),
          ),
          SizedBox(
            width: double.infinity,
            height: 45,
            child: Center(
              child: Row(
                children: [
                  Text('工友：',
                      style: TextStyle(fontSize: 17, color: Color(0xFF323233))),
                  Obx(
                    () => Text(_viewModel.us.workerName,
                        style: TextStyle(
                            fontSize: 17,
                            color: Color(0xFF323233),
                            fontWeight: FontWeight.w500)),
                  ),
                  Spacer(),
                  SizedBox(width: 4),
                  Image.asset(Assets.commonIconArrowRightGrey,
                      width: 18, height: 18)
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  ///底部取消按钮和确认按钮
  Widget _buildBottomView() {
    return Container(
      padding: EdgeInsets.only(left: 16, right: 16, top: 10, bottom: 10),
      decoration: BoxDecoration(
        border: Border(
          top: BorderSide(color: Color(0xFFF5F5F5), width: 0.5.w),
        ),
      ),
      child: Row(
        children: [
          // 取消按钮
          GestureDetector(
            onTap: _viewModel.onConfirmTap,
            behavior: HitTestBehavior.opaque,
            child: Container(
              height: 44,
              width: 100.w,
              alignment: Alignment.center,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(4.r),
                border: Border.all(color: Color(0xFFF54A45), width: 1.w),
              ),
              child: Text('删除',
                  style: TextStyle(fontSize: 16.sp, color: Color(0xFFF54A45))),
            ),
          ),
          SizedBox(width: 10.w), // 按钮间距
          Expanded(
            child: GestureDetector(
              onTap: _viewModel.onSubmitTap,
              behavior: HitTestBehavior.opaque,
              child: Container(
                height: 44,
                alignment: Alignment.center,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(4.r),
                  border:
                      Border.all(color: ColorsUtil.primaryColor, width: 1.w),
                  color: ColorsUtil.primaryColor,
                ),
                child: Text('保存修改',
                    style: TextStyle(fontSize: 16.sp, color: Colors.white)),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
