import 'package:gdjg_pure_flutter/feature/group/common_params_model/worker_model.dart';
import 'package:gdjg_pure_flutter/model/RecordType.dart';

/// @date 2025/07/03
/// @description GroupEditRecordWork页入参
class GroupEditRecordWorkProps {
  ///记工类型
  RwaRecordType? businessType;

  String? workNoteId;

  String? workNoteName;

  DateTime? date;

  double? billId;

  ///带班id
  double? deptId;

  List<WorkerModel>? workers;

  GroupEditRecordWorkProps({
    this.date,
    this.workNoteId,
    this.workNoteName,
    this.billId,
    this.businessType,
    this.deptId,
    this.workers,
  });

  //记工类型
  isRecordWorkType() {
    return (businessType == RwaRecordType.workDays ||
        businessType == RwaRecordType.workLoad ||
        businessType == RwaRecordType.dailyWages ||
        businessType == RwaRecordType.packageWork);
  }

  getTitle() {
    switch (businessType) {
      case RwaRecordType.workDays:
        return '点工';
      case RwaRecordType.packageWork:
        return '包工';
      case RwaRecordType.workLoad:
        return '工量';
      case RwaRecordType.dailyWages:
        return '短工';
      case RwaRecordType.debt:
        return '借支';
      case RwaRecordType.wageLast:
        return '结算';
      case RwaRecordType.expense:
        return '其他费用';
      default:
        return '记工';
    }
  }

  isWorkDaysOrPackageWork() {
    return businessType == RwaRecordType.workDays || businessType == RwaRecordType.packageWork;
  }

  @override
  String toString() {
    return 'GroupEditRecordWorkProps{workNoteId: $workNoteId, id: $billId, businessType: $businessType, deptId: $deptId}';
  }
}
