import "package:flutter/material.dart";
import "package:gdjg_pure_flutter/data/group_data/group_pro_update/repo/model/update_pro_info_biz_models.dart";
import "package:gdjg_pure_flutter/feature/group/common_params_model/worker_model.dart";
import "package:gdjg_pure_flutter/feature/group/group_edit_record_work/entity/group_edit_record_work_props.dart";
import "package:gdjg_pure_flutter/feature/group/group_edit_record_work/view/vm/edit_daily_work_vm.dart";
import "package:gdjg_pure_flutter/feature/group/group_edit_record_work/view/vm/edit_load_work_vm.dart";
import "package:gdjg_pure_flutter/feature/group/group_edit_record_work/view/vm/edit_point_work_vm.dart";
import "package:gdjg_pure_flutter/iconfont/iconfont.dart";
import "package:gdjg_pure_flutter/model/RecordType.dart";
import "package:gdjg_pure_flutter/utils/system_util/date_util.dart";
import "package:gdjg_pure_flutter/utils/ui_util/common_dialog.dart";
import "package:get/get.dart";

import "group_edit_record_work_ui_state.dart";

/// @date 2025/07/03
/// @description GroupEditRecordWork页ViewModel
/// 1. 处理数据的加载状态，如loading和错误视图等
/// 2. 观测UIRep中的业务实体转为UIState
/// 3. 接受来自view的意图并分发事件进行处理（暂无）
/// 4. 对View层暴露事件（操作视图和处理一些与系统权限交互等场景）
class GroupEditRecordWorkViewModel {
  late final EditPointWorkVM _pointVVM;
  late final EditDailyWorkVM _dailyVVM;
  late final EditLoadWorkVM _loadVVM;
  GroupEditRecordWorkProps? _props;
  var us = GroupEditRecordWorkUS();
  var _dateTime = DateTime.now();
  var _workers = <WorkerModel>[];

  GroupEditRecordWorkViewModel(GroupEditRecordWorkProps? props) {
    _props = props;
    if (props == null) return;
    us.setBusinessType(_props?.businessType);
    setDateTime(props.date ?? DateTime.now());
    if (props.isWorkDaysOrPackageWork()) {
      // 页面加载时注册一个局部 ViewModel（避免与其它页面冲突）
      if (!Get.isRegistered<EditPointWorkVM>()) {
        Get.put<EditPointWorkVM>(EditPointWorkVM(), permanent: false);
      }
      _pointVVM = Get.find<EditPointWorkVM>();
      setWorkerList(props.workers ?? []);
      _pointVVM.setWorkNoteName(props.workNoteName);
      _pointVVM.init(
        businessType: props.businessType,
        workNoteId: props.workNoteId,
        billId: props.billId,
        workers: props.workers ?? [],
      );
      _pointVVM.fetchUpdateProInfo().then((result) => {
            if (result != null) {convertEntityToUIState(result)}
          });
    } else if (props.businessType == RwaRecordType.dailyWages ||
        props.businessType == RwaRecordType.wageLast) {
      // 页面加载时注册一个局部 ViewModel（避免与其它页面冲突）
      if (!Get.isRegistered<EditDailyWorkVM>()) {
        Get.put<EditDailyWorkVM>(EditDailyWorkVM(), permanent: false);
      }
      _dailyVVM = Get.find<EditDailyWorkVM>();
      _dailyVVM.init(
        businessType: props.businessType,
        workNoteId: props.workNoteId,
        billId: props.billId,
      );
      _dailyVVM.fetchUpdateProInfo().then((result) => {
            if (result != null) {convertEntityToUIState(result)}
          });
    } else if (props.businessType == RwaRecordType.workLoad) {
      // 页面加载时注册一个局部 ViewModel（避免与其它页面冲突）
      if (!Get.isRegistered<EditLoadWorkVM>()) {
        Get.put<EditLoadWorkVM>(EditLoadWorkVM(), permanent: false);
      }
      _loadVVM = Get.find<EditLoadWorkVM>();
      _loadVVM.init(
        businessType: props.businessType,
        workNoteId: props.workNoteId,
        billId: props.billId,
      );
      _loadVVM.fetchUpdateProInfo().then((result) => {
            if (result != null) {convertEntityToUIState(result)}
          });
    }
  }

  String get title => "修改${_props?.getTitle()}";

  /// 初始化数据同步，如果不需要loading，则在这里去掉
  void fetchData() async {}

  /// 将实体数据转换为UIState
  /// @param entity
  /// @returns
  void convertEntityToUIState(UpdateProInfoBizModel result) {
    if (result.confirm == 1) {
      us.setTipsUIState(TipsUIState(
        icon: IconNames.saasXuanzhong,
        iconColor: "#46DB7A",
        textColor: const Color.fromRGBO(82, 144, 253, 1),
        text:
            '${result.getWorkerName()}已确认该笔${(_props?.isRecordWorkType() == true) ? '记工' : '记账'}数据',
        bgColor: Colors.white,
      ));
    } else if (result.confirm == 2) {
      us.setTipsUIState(TipsUIState(
        icon: IconNames.saasClean,
        iconColor: "#FF4A45F5",
        textColor: const Color.fromRGBO(245, 74, 69, 1),
        text: '${result.getWorkerName()}已确认该笔有误',
        bgColor: const Color.fromRGBO(245, 74, 69, 0.1),
      ));
    } else {
      us.setTipsUIState(TipsUIState());
    }
  }

  void setWorkerList(List<WorkerModel> workers) {
    _workers = workers;
    //点工包工才回去设置工友
    if (_props?.isWorkDaysOrPackageWork()) {
      _pointVVM.setWorkerIds(workers);
    }
    var workerName =
        workers.map((worker) => worker.workerName.toString()).join(',');
    us.setWorkerName(workerName);
  }

  void setDateTime(DateTime date) {
    _dateTime = date;
    us.setDateTime(date);
  }

  void onSubmitTap() async {
    var date = DateUtil.formatDate(_dateTime);
    var workerId = _workers.map((e) => e.workerId).join(",") ?? "";
    if (_props?.isWorkDaysOrPackageWork()) {
      //点工包工修改
      //点工、包工
      var params = _pointVVM.getUpdateParams();
      params.business_time = date;
      params.worker_id = workerId;
      _pointVVM.baseVm.onSubmitTap(params);
    } else if (_props?.businessType == RwaRecordType.dailyWages) {
      //短工修改
      var params = _dailyVVM.getUpdateParams();
      params.business_time = date;
      params.worker_id = workerId;
      _dailyVVM.baseVm.onSubmitTap(params);
    } else if (_props?.businessType == RwaRecordType.wageLast) {
      //结算修改
      showCommonDialog(CommonDialogConfig(
        title: '提示',
        content: '当前结算金额为${_dailyVVM.dailyUS.money}元，确定要记为结算吗？',
        negative: '取消',
        positive: '确定',
        onPositive: () {
          var params = _dailyVVM.getUpdateParams();
          params.business_time = date;
          params.worker_id = workerId;
          _dailyVVM.baseVm.onSubmitTap(params);
        },
      ));
    } else if (_props?.businessType == RwaRecordType.workLoad) {
      //工量修改
      var params = _loadVVM.getUpdateParams();
      params.business_time = date;
      params.worker_id = workerId;
      _loadVVM.baseVm.onSubmitTap(params);
    }
  }

  void onConfirmTap() async {
    showCommonDialog(CommonDialogConfig(
      title: '温馨提示',
      content: '您确定要删除这笔${_props?.getTitle()}吗？',
      negative: '取消',
      positive: '确定',
      onPositive: _onDeletePro,
    ));
  }

  void _onDeletePro() {
    if (_props?.isRecordWorkType()) {
      _pointVVM.baseVm.onDeleteTap();
    } else if (_props?.businessType == RwaRecordType.dailyWages) {
      _dailyVVM.baseVm.onDeleteTap();
    } else if (_props?.businessType == RwaRecordType.wageLast) {
      _dailyVVM.baseVm.onDeleteTap();
    } else if (_props?.businessType == RwaRecordType.workLoad) {
      _loadVVM.baseVm.onDeleteTap();
    }
  }
}
