import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:gdjg_pure_flutter/iconfont/iconfont.dart';
import 'package:gdjg_pure_flutter/model/RecordType.dart';
import 'package:get/get.dart';

/// @date 2025/07/03
/// @description JDetail页UI状态
class GroupEditRecordWorkUS {
  /// 顶部提示，班组才会显示
  final _tips = TipsUIState().obs;

  /// 时间
  final _dateTime = DateTime.now().obs;

  ///项目名称
  final _workNoteName = '项目名称'.obs;

  ///工友名称
  final _workerName = '工友名称'.obs;

  final _showWorkView = false.obs;

  final _showDailyView = false.obs;

  final _showWorkLoadView = false.obs;

  TipsUIState get tips => _tips.value;

  DateTime get dateTime => _dateTime.value;

  String get workNoteName => _workNoteName.value;

  String get workerName => _workerName.value;

  bool get showWorkView => _showWorkView.value;

  bool get showDailyView => _showDailyView.value;

  bool get showWorkLoadView => _showWorkLoadView.value;

  setBusinessType(RwaRecordType? businessType) {
    if(businessType == RwaRecordType.workDays || businessType == RwaRecordType.packageWork){
      _showWorkView.value = true;
      _showWorkView.refresh();
    }else if(businessType == RwaRecordType.dailyWages || businessType == RwaRecordType.wageLast){
      _showDailyView.value = true;
      _showDailyView.refresh();
    }else if(businessType == RwaRecordType.workLoad){
      _showWorkLoadView.value = true;
      _showWorkLoadView.refresh();
    }
  }

  setTipsUIState(TipsUIState tipsUIState) {
    _tips.value = tipsUIState;
    _tips.refresh();
  }

  setDateTime(DateTime dateTime) {
    _dateTime.value = dateTime;
    _dateTime.refresh();
  }

  setWorkNoteName(String workNoteName) {
    _workNoteName.value = workNoteName;
    _workNoteName.refresh();
  }

  setWorkerName(String workerName) {
    _workerName.value = workerName;
    _workerName.refresh();
  }
}

class TipsUIState {
  final IconNames icon;
  final String iconColor;
  final Color textColor;
  final String text;
  final Color bgColor;

  TipsUIState({
    this.icon = IconNames.saasInfo,
    this.iconColor=  "#FFA011FF",
    this.textColor = const Color.fromRGBO(255, 160, 17, 1),
    this.text = '为避免劳资纠纷，请尽快与工人确认',
    this.bgColor = const Color.fromRGBO(255, 160, 17, 0.10),
  });
}
