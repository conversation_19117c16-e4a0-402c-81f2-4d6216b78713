import 'package:gdjg_pure_flutter/model/RecordType.dart';

/// 统计项
class StatisticsItemUIState {
  /// 类型 主要是为了点击的时候需要传值使用
  RwaRecordType recordType;

  /// 类型
  String? typeName;

  /// 详情
  String? detail;

  /// 工资
  String? feeMoney;

  /// 分项名称
  String? unitWorkTypeName;

  /// 笔数,用于显示到标题下边
  String? workNum;

  /// 总计
  String? total;

  /// 是否为记账项目
  bool isRecordWorkType;

  String? unitWorkType;

  StatisticsItemUIState({
    required this.recordType,
    this.typeName,
    this.detail,
    this.feeMoney,
    this.unitWorkTypeName,
    this.workNum,
    this.total,
    this.isRecordWorkType = true,
    this.unitWorkType,
  });

  isShowWage() {
    return recordType == RwaRecordType.workDays ||
        recordType == RwaRecordType.packageWork ||
        recordType == RwaRecordType.workLoad;
  }

  /// 是否为记账
  isAccounting() {
    return recordType == RwaRecordType.debt || recordType == RwaRecordType.wageLast;
  }
}
