import 'package:collection/collection.dart';
import 'package:gdjg_pure_flutter/feature/group/common_uistate/statistics_ui_state.dart';
import 'package:gdjg_pure_flutter/data/common_data/biz/count_biz_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_pro_bill/repo/model/group_business_get_group_business_list_biz_model.dart';
import 'package:gdjg_pure_flutter/model/RecordType.dart';
import 'package:gdjg_pure_flutter/utils/system_util/string_util.dart';

class StatisticsUIStateHelper {
  ///构建统计项
  /// ```dart
  ///bizModel 统计数据
  ///isShowUnsettle 是否显示未结
  ///isShowDailyWages 是否显示短工笔数
  ///unitWorkType 工量对应的类型，在修改工资的时候用于筛选出当前传入的一个工量
  /// ```
  static List<StatisticsItemUIState> buildStatisticsItem(
    CountBizModel? bizModel, {
    String? unitWorkType,
    bool isShowUnsettle = true,
    bool isShowDailyWages = true,
  }) {
    final List<StatisticsItemUIState> statisticsItemList = [];
    if (bizModel == null) return statisticsItemList;
    // 1. 点工
    var spotWork = bizModel.spotWork;
    if (spotWork != null && spotWork.num > 0) {
      final detail = _buildWorkDetailString(
        workTime: spotWork.workTime,
        workTimeHour: double.tryParse(spotWork.workTimeHour) ?? 0.0,
        overtimeWork: spotWork.overTimeWork,
        overtime: double.tryParse(spotWork.overTime) ?? 0.0,
      );
      statisticsItemList.add(StatisticsItemUIState(
        recordType: RwaRecordType.workDays,
        typeName: "点工",
        detail: detail,
        feeMoney: spotWork.spotWorkFeeMoney.formatDoubleToMoney(),
      ));
    }

    // 2. 包工
    var contractor = bizModel.contractor;
    if (contractor != null && contractor.num > 0) {
      final detail = _buildWorkDetailString(
          workTime: contractor.contractorWorkTime,
          workTimeHour:
              double.tryParse(contractor.contractorWorkTimeHour) ?? 0.0,
          overtimeWork: "",
          overtime: double.tryParse(contractor.contractorOverTime) ?? 0.0);
      statisticsItemList.add(StatisticsItemUIState(
        recordType: RwaRecordType.packageWork,
        typeName: "包工",
        detail: detail,
        feeMoney: contractor.contractorMoney.formatDoubleToMoney(),
      ));
    }
    // 3. 工量
    var unit = bizModel.unit;
    if (unit != null && unit.num > 0) {
      if (unitWorkType.isNullOrEmpty()) {
        for (var entity in unit.countUnit) {
          statisticsItemList.add(StatisticsItemUIState(
            recordType: RwaRecordType.workLoad,
            typeName: "工量",
            unitWorkTypeName: entity.unitWorkTypeName,
            workNum:
                entity.num > 0 ? "${entity.num.toStringAsFixed(0)}笔" : "",
            feeMoney: entity.unitMoney.formatStringToMoney(),
            total: (int.parse(entity.count) > 0)
                ? "总计:${entity.count}${entity.unitWorkTypeUnit}"
                : "",
            unitWorkType: entity.unitWorkType,
          ));
        }
      } else {
        final entity = unit.countUnit.firstWhereOrNull(
            (element) => element.unitWorkType == unitWorkType);
        if (entity != null) {
          statisticsItemList.add(StatisticsItemUIState(
            recordType: RwaRecordType.workLoad,
            typeName: "工量",
            unitWorkTypeName: entity.unitWorkTypeName,
            workNum:
                entity.num > 0 ? "${entity.num.toStringAsFixed(0)}笔" : "",
            feeMoney: entity.unitMoney.formatStringToMoney(),
            total: (int.parse(entity.count) > 0)
                ? "总计:${entity.count}${entity.unitWorkTypeUnit}"
                : "",
            unitWorkType: entity.unitWorkType,
          ));
        }
      }
    }
    // 4. 短工
    var workMoney = bizModel.workMoney;
    if (workMoney != null && workMoney.num > 0) {
      statisticsItemList.add(StatisticsItemUIState(
        recordType: RwaRecordType.dailyWages,
        typeName: "短工",
        feeMoney: workMoney.workMoney.formatStringToMoney(),
        detail: isShowDailyWages
            ? "工资:${workMoney.workMoney.formatStringToMoney()}"
            : "",
      ));
    }

    // 5. 借支
    var borrow = bizModel.borrow;
    if (borrow != null && borrow.num > 0) {
      statisticsItemList.add(StatisticsItemUIState(
        recordType: RwaRecordType.debt,
        typeName: "借支",
        feeMoney: borrow.borrowCount.formatStringToMoney(),
        workNum: "${borrow.num.trimTrailingZeros()}笔",
        isRecordWorkType: false,
      ));
    }

    // 9. 结算
    var wage = bizModel.wage;
    if (wage != null && wage.num > 0) {
      statisticsItemList.add(StatisticsItemUIState(
        recordType: RwaRecordType.wageLast,
        typeName: "结算",
        feeMoney: wage.wageCount.formatStringToMoney(),
        workNum: "${wage.num.trimTrailingZeros()}笔",
        isRecordWorkType: false,
      ));
    }

    //其他费用
    var otherExpenses = bizModel.otherExpenses;
    if(otherExpenses.isNotEmpty){
      for(var item in otherExpenses){
        statisticsItemList.add(StatisticsItemUIState(
          recordType: RwaRecordType.expense,
          typeName: "其他费用 ${item.name}",
          feeMoney: item.money.formatStringToMoney(),
          workNum : "${item.num.trimTrailingZeros()}笔",
          isRecordWorkType: true,
        ));
      }
    }

    // 6. 未结
    if (isShowUnsettle) {
      var unsettled = bizModel.unsettled;
      if (unsettled != null && unsettled > 0) {
        statisticsItemList.add(StatisticsItemUIState(
          recordType: RwaRecordType.wageLast,
          typeName: "未结",
          feeMoney: unsettled.formatDoubleToMoney(),
          isRecordWorkType: false,
        ));
      }
    }

    return statisticsItemList;
  }

  /// 构建点工和包工详情
  static String _buildWorkDetailString({
    required String workTime,
    required double workTimeHour,
    required String overtimeWork,
    required double overtime,
  }) {
    final List<String> workParts = [];
    if (double.tryParse(workTime) != null && double.parse(workTime) > 0) {
      workParts.add("${workTime.trimTrailingZeros()}个工");
    }
    if (workTimeHour > 0) {
      workParts.add("${workTimeHour.trimTrailingZeros()}小时");
    }
    final workDetail = workParts.join(' + ');

    final List<String> overtimeParts = [];
    if (double.tryParse(overtimeWork) != null &&
        double.parse(overtimeWork) > 0) {
      overtimeParts.add("${overtimeWork.trimTrailingZeros()}个工");
    }
    if (overtime > 0) {
      overtimeParts.add("${overtime.trimTrailingZeros()}小时");
    }
    final overtimeDetail = overtimeParts.join(' + ');

    final List<String> finalParts = [];
    if (workDetail.isNotEmpty) {
      finalParts.add("上班: $workDetail");
    }
    if (overtimeDetail.isNotEmpty) {
      finalParts.add("加班: $overtimeDetail");
    }

    return finalParts.join('\n');
  }

  static RwaRecordType getRwaRecordType(double businessType) {
    if (businessType == RecordType.workDays.value) {
      return RwaRecordType.workDays;
    }
    if (businessType == RecordType.workLoad.value) {
      return RwaRecordType.workLoad;
    }
    if (businessType == RecordType.dailyWages.value) {
      return RwaRecordType.dailyWages;
    }
    if (businessType == RecordType.packageWork.value) {
      return RwaRecordType.packageWork;
    }
    if (businessType == RecordType.debt.value) {
      return RwaRecordType.debt;
    }
    if (businessType == RecordType.wageLast.value) {
      return RwaRecordType.wageLast;
    }
    if (businessType == RecordType.incomeLast.value) {
      return RwaRecordType.incomeLast;
    }
    if (businessType == RecordType.expenditure.value) {
      return RwaRecordType.expenditure;
    }
    return RwaRecordType.expense;
  }

  static String getBusinessTypeName(double businessType) {
    if (businessType == RecordType.workDays.value) {
      return "点工";
    }
    if (businessType == RecordType.workLoad.value) {
      return "工量";
    }
    if (businessType == RecordType.dailyWages.value) {
      return "短工";
    }
    if (businessType == RecordType.packageWork.value) {
      return "包工";
    }
    if (businessType == RecordType.debt.value) {
      return "借支";
    }
    if (businessType == RecordType.wageLast.value) {
      return "结算";
    }
    if (businessType == RecordType.incomeLast.value) {
      return "收入";
    }
    if (businessType == RecordType.expenditure.value) {
      return "支出";
    }
    return "记录";
  }

  static String getShowContentText(
      GroupBusinessGetGroupBusinessListBBizModel entity) {
    final businessType = entity.businessType;
    String contentText = '';
    if (businessType == RecordType.workDays.value) {
      if (entity.workTime > 0) {
        contentText = '上班:${entity.workTime.trimTrailingZeros()}个工';
      }
      if (entity.workTimeHour > 0) {
        contentText = '上班:${entity.workTimeHour.trimTrailingZeros()}小时';
      }
      if (contentText.isEmpty) {
        contentText = '上班:休息';
      }

      if (entity.morningWorkTime != null ||
          entity.morningWorkTimeHour != null ||
          entity.afternoonWorkTime != null ||
          entity.afternoonWorkTimeHour != null) {
        contentText = '';
        if ((entity.morningWorkTime ?? 0) > 0) {
          contentText = '上午:${entity.morningWorkTime?.trimTrailingZeros()}个工';
        } else {
          contentText = '上午:休息';
        }
        if ((int.tryParse(entity.morningWorkTimeHour ?? '0') ?? 0) > 0) {
          contentText =
              '上午:${entity.morningWorkTimeHour?.trimTrailingZeros()}小时';
        } else if ((entity.morningWorkTime ?? 0) < 0) {
          contentText = '上午:休息';
        }
        if ((entity.afternoonWorkTime ?? 0) > 0) {
          contentText +=
              '\n下午:${entity.afternoonWorkTime?.trimTrailingZeros()}个工';
        } else if (entity.afternoonWorkTime != null) {
          contentText += '\n下午:休息';
        }
        if ((int.tryParse(entity.afternoonWorkTimeHour ?? '0') ?? 0) > 0) {
          contentText +=
              '\n下午:${entity.afternoonWorkTimeHour?.trimTrailingZeros()}小时';
        } else if (entity.afternoonWorkTimeHour != null) {
          contentText += '\n下午:休息';
        }
      }

      if (entity.overtime > 0) {
        contentText += '\n加班:${entity.overtime.trimTrailingZeros()}小时';
      }
      if (entity.overtimeWork > 0) {
        contentText += '\n加班:${entity.overtimeWork.trimTrailingZeros()}个工';
      }
      return contentText;
    }

    //包工
    if (businessType == RecordType.packageWork.value) {
      if (entity.workTime > 0) {
        contentText = '上班:${entity.workTime.trimTrailingZeros()}个工';
      }
      if (entity.workTimeHour > 0) {
        contentText = '上班:${entity.workTimeHour.trimTrailingZeros()}小时';
      }
      if (contentText.isEmpty) {
        contentText = '上班:休息';
      }

      if (entity.morningWorkTime != null ||
          entity.morningWorkTimeHour != null ||
          entity.afternoonWorkTime != null ||
          entity.afternoonWorkTimeHour != null) {
        contentText = '';
        if ((entity.morningWorkTime ?? 0) > 0) {
          contentText = '上午:${entity.morningWorkTime?.trimTrailingZeros()}个工';
        } else {
          contentText = '上午:休息';
        }
        if ((int.tryParse(entity.morningWorkTimeHour ?? '0') ?? 0) > 0) {
          contentText =
              '上午:${entity.morningWorkTimeHour?.trimTrailingZeros()}小时';
        } else if ((entity.morningWorkTime ?? 0) < 0) {
          contentText = '上午:休息';
        }
        if ((entity.afternoonWorkTime ?? 0) > 0) {
          contentText +=
              '\n下午:${entity.afternoonWorkTime?.trimTrailingZeros()}个工';
        } else if (entity.afternoonWorkTime != null) {
          contentText += '\n下午:休息';
        }
        if ((int.tryParse(entity.afternoonWorkTimeHour ?? '0') ?? 0) > 0) {
          contentText +=
              '\n下午:${entity.afternoonWorkTimeHour?.trimTrailingZeros()}小时';
        } else if (entity.afternoonWorkTimeHour != null) {
          contentText += '\n下午:休息';
        }
      }

      if (entity.overtime > 0) {
        contentText += '\n加班:${entity.overtime.trimTrailingZeros()}小时';
      }
      return contentText;
    }

    if (businessType == RecordType.workLoad.value) {
      contentText = '工程量:${entity.unitNum}${entity.unitWorkTypeUnit}';
    }

    return contentText;
  }
}
