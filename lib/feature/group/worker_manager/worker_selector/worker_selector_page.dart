import 'package:flutter/material.dart';
import 'package:gdjg_pure_flutter/feature/group/worker_manager/worker_selector/vm/protocol/worker_selector_ui_state.dart';
import 'package:gdjg_pure_flutter/generated/assets.dart';
import 'package:gdjg_pure_flutter/utils/route_util/base_page/base_ful_page.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/button_util.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/toast_util.dart';
import 'package:get/get.dart';

import 'vm/worker_selector_viewmodel.dart';

/// @date 2025/06/13
/// @param props 页面路由参数
/// @returns
/// @description WorkerSelector页面入口
class WorkerSelectorPage extends BaseFulPage {
  const WorkerSelectorPage({super.key})
      : super(appBar: const YPAppBar(title: "选择工友"));

  @override
  State createState() => _WorkerSelectorPageState();
// const WorkerSelectorPage({super.key, this.props});
//
// final WorkerSelectorProps? props;
//
// @override
// State createState() => _WorkerSelectorPageState();
}

class _WorkerSelectorPageState<WorkerSelectorPage> extends BaseFulPageState {
  final WorkerSelectorViewModel viewModel = WorkerSelectorViewModel();
  final TextEditingController _searchController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  final FocusNode _searchFocusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _searchController.dispose();
    _scrollController.dispose();
    _searchFocusNode.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      viewModel.loadMore();
    }
  }

  /// 实际展示的视图
  Widget contentView() {
    return Obx(() {
      return Column(
        children: [
          // 搜索框区域
          _buildSearchSection(),
          // 工友列表区域
          Expanded(child: _buildWorkerList()),
          // 底部按钮区域 - 搜索状态时隐藏
          if (!viewModel.showSearchView.value) _buildBottomSection(),
        ],
      );
    });
  }

  /// 搜索框区域
  Widget _buildSearchSection() {
    return Container(
      color: Colors.white,
      padding: const EdgeInsets.all(16),
      child: Obx(() {
        return Row(
          children: [
            // 搜索框
            Expanded(
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 12),
                decoration: BoxDecoration(
                  color: const Color(0xFFF5F5F5),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: _buildTextField(),
              ),
            ),
            // 取消按钮 - 搜索状态时显示
            if (viewModel.showSearchView.value) ...[
              const SizedBox(width: 20),
              GestureDetector(
                onTap: _onCancelSearch,
                child: Text(
                  '取消',
                  style: TextStyle(
                    fontSize: 16,
                    color: ColorsUtil.primaryColor,
                  ),
                ),
              ),
            ],
          ],
        );
      }),
    );
  }

  /// 输入框组件
  Widget _buildTextField() {
    return TextField(
      controller: _searchController,
      focusNode: _searchFocusNode,
      onTap: _onSearchTap,
      onChanged: (value) => viewModel.searchWorkers(value),
      decoration: InputDecoration(
        border: InputBorder.none,
        hintText: '请输入工友姓名/手机号码',
        hintStyle: TextStyle(
          fontSize: 16,
          color: ColorsUtil.hintFontColor,
        ),
        prefixIcon: Icon(
          Icons.search,
          color: ColorsUtil.hintFontColor,
          size: 20,
        ),
      ),
    );
  }

  /// 搜索框点击事件
  void _onSearchTap() {
    viewModel.showSearchView.value = true;
    _searchFocusNode.requestFocus();
  }

  /// 取消搜索事件
  void _onCancelSearch() {
    viewModel.showSearchView.value = false;
    _searchController.clear();
    _searchFocusNode.unfocus();
    viewModel.searchWorkers('');
  }

  /// 工友列表
  Widget _buildWorkerList() {
    return Obx(() {
      List<WorkerContactUIState> workers;

      if (viewModel.showSearchView.value) {
        // 搜索状态：显示搜索结果
        workers = viewModel.getFilteredWorkers();
      } else {
        // 正常状态：显示全部工友
        workers = viewModel.uiState.value.data ?? [];
      }

      if (workers.isEmpty && !viewModel.isLoading.value) {
        return const Center(child: Text('暂无工友数据'));
      }

      return RefreshIndicator(
        onRefresh: () => viewModel.refreshData(),
        child: ListView.builder(
          controller: _scrollController,
          itemCount: workers.length +
              (viewModel.hasMore.value && !viewModel.showSearchView.value
                  ? 1
                  : 0),
          itemBuilder: (context, index) {
            if (index == workers.length) {
              // 加载更多指示器 - 只在非搜索状态显示
              return _buildLoadMoreIndicator();
            }
            final worker = workers[index];
            return _buildWorkerItem(worker, index);
          },
        ),
      );
    });
  }

  /// 加载更多指示器
  Widget _buildLoadMoreIndicator() {
    return Obx(() {
      if (viewModel.isLoadingMore.value) {
        return const Padding(
          padding: EdgeInsets.all(16),
          child: Center(child: CircularProgressIndicator()),
        );
      }
      return const SizedBox.shrink();
    });
  }

  /// 工友项 - 使用独立的Obx优化性能
  Widget _buildWorkerItem(WorkerContactUIState worker, int index) {
    return Obx(() {
      final isSelected = viewModel.selectedWorkerIds.contains(worker.id);

      return GestureDetector(
        onTap: () => listItemClick(worker.id ?? ''),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          decoration: const BoxDecoration(
            color: Colors.white,
            border: Border(
              bottom: BorderSide(
                color: Color(0xFFF0F0F0),
                width: 0.5,
              ),
            ),
          ),
          child: Row(
            children: [
              // 选择状态图标 - 使用指定的图片资源
              SizedBox(
                width: 20,
                height: 20,
                child: Image.asset(
                  isSelected
                      ? Assets.commonWaaIcFindProCheck
                      : Assets.commonWaaIcFindProUncheck,
                  width: 20,
                  height: 20,
                  errorBuilder: (context, error, stackTrace) {
                    // 如果图片加载失败，使用默认的圆形选择器
                    return Container(
                      width: 20,
                      height: 20,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: isSelected
                              ? ColorsUtil.primaryColor
                              : const Color(0xFFD1D1D6),
                          width: 2,
                        ),
                        color: isSelected
                            ? ColorsUtil.primaryColor
                            : Colors.transparent,
                      ),
                      child: isSelected
                          ? const Icon(
                              Icons.check,
                              size: 12,
                              color: Colors.white,
                            )
                          : null,
                    );
                  },
                ),
              ),
              const SizedBox(width: 12),
              // 头像
              _buildAvatar(worker),
              const SizedBox(width: 12),
              // 姓名和电话
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      worker.name ?? '',
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                        color: Colors.black,
                      ),
                    ),
                    if (worker.tel?.isNotEmpty == true) ...[
                      const SizedBox(height: 2),
                      Text(
                        worker.tel ?? '',
                        style: TextStyle(
                          fontSize: 14,
                          color: ColorsUtil.black65,
                        ),
                      ),
                    ],
                  ],
                ),
              ),
              // 状态标签
              if (worker.status?.isNotEmpty == true) ...[
                const SizedBox(width: 8),
                Text(
                  worker.status ?? '',
                  style: const TextStyle(
                    fontSize: 13,
                    color: Color.fromRGBO(0xF7, 0x47, 0x42, 0.8),
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ],
          ),
        ),
      );
    });
  }

  /// 头像组件
  Widget _buildAvatar(WorkerContactUIState worker) {
    return Container(
      width: 47,
      height: 47,
      decoration: BoxDecoration(
        color: ColorsUtil.primaryColor,
        borderRadius: BorderRadius.circular(6),
      ),
      child: Center(
        child: Text(
          worker.shortName ?? '',
          style: const TextStyle(
            color: Colors.white,
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }

  /// 底部按钮区域
  Widget _buildBottomSection() {
    return Container(
      color: Colors.white,
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          // 全选/取消全选
          Expanded(
            child: Obx(() {
              return GestureDetector(
                onTap: () => viewModel.toggleSelectAll(),
                child: Row(
                  children: [
                    SizedBox(
                      width: 20,
                      height: 20,
                      child: Image.asset(
                        viewModel.isAllSelected
                            ? 'assets/images/common/waa_ic_find_pro_check.webp'
                            : 'assets/images/common/waa_ic_find_pro_uncheck.webp',
                        width: 20,
                        height: 20,
                        errorBuilder: (context, error, stackTrace) {
                          // 如果图片加载失败，使用默认的圆形选择器
                          return Container(
                            width: 20,
                            height: 20,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              border: Border.all(
                                color: viewModel.isAllSelected
                                    ? ColorsUtil.primaryColor
                                    : const Color(0xFFD1D1D6),
                                width: 2,
                              ),
                              color: viewModel.isAllSelected
                                  ? ColorsUtil.primaryColor
                                  : Colors.transparent,
                            ),
                            child: viewModel.isAllSelected
                                ? const Icon(
                                    Icons.check,
                                    size: 12,
                                    color: Colors.white,
                                  )
                                : null,
                          );
                        },
                      ),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      '全选',
                      style: TextStyle(
                        fontSize: 16,
                        color: ColorsUtil.black85,
                      ),
                    ),
                  ],
                ),
              );
            }),
          ),
          const SizedBox(width: 16),
          // 确定按钮
          Expanded(
            flex: 2,
            child: ButtonUtil.buildCommonButton(
              text: '确定',
              onPressed: _onConfirm,
              backgroundColor: ColorsUtil.primaryColor,
            ),
          ),
        ],
      ),
    );
  }

  /// 确定按钮点击事件
  void _onConfirm() {
    final selectedWorkers = viewModel.getSelectedWorkers();
    if (selectedWorkers.isEmpty) {
      ToastUtil.showToast('请选择工友');
      return;
    }

    final names = selectedWorkers.map((w) => w.name ?? '').join('、');
    ToastUtil.showToast('已选择：$names');
    // 返回选择的工友列表
    YPRoute.closePage(onSend2Previous);
  }

  void listItemClick(String id) {
    _onCancelSearch();
    viewModel.toggleWorkerSelection(id);
  }

  @override
  onSend2Previous() {
    return viewModel.getSelectedWorkers();
  }

  @override
  Widget yBuild(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      resizeToAvoidBottomInset: false,
      body: Obx(() {
        if (viewModel.isLoading.value) {
          return const Center(child: CircularProgressIndicator());
        }
        if (viewModel.uiState.value.isShowError == true) {
          return const Center(child: Text('页面加载失败...'));
        }
        return contentView();
      }),
    );
  }
}
