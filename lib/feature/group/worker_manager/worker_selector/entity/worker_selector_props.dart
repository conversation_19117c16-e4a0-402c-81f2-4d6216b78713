/// @date 2025/06/13
/// @description WorkerSelector页入参
class WorkerSelectorProps {
  WorkerSelectorPropsEntity? data;

  WorkerSelectorProps({this.data});

  @override
  String toString() {
    return 'WorkerSelectorProps{propsJson: ${data.toString()}';
  }
}

/// 跳转工友选择页传参
class WorkerSelectorPropsEntity {
  String? deptId;
  int? type;
  bool? inNote;
  int? isDelete;
  bool? isSingleSelect;
  ContactInfoEntity? entity;

  WorkerSelectorPropsEntity({
    this.deptId,
    this.type,
    this.inNote,
    this.isDelete,
    this.isSingleSelect,
    this.entity,
  });

  @override
  String toString() {
    return 'WorkerSelectorPropsEntity{deptId: $deptId, type: $type, inNote: $inNote, isDelete: $isDelete, isSingleSelect: $isSingleSelect, entity: $entity}';
  }
}

/// 联系人信息实体
class ContactInfoEntity {
  String? id;
  String? name;
  String? tel;
  String? isSelf;
  int? isDeleted;
  String? isSelfCreated;
  String? quitTime;
  String? isAgent;
  String? isBind;
  String? avatar;

  ContactInfoEntity({
    this.id,
    this.name,
    this.tel,
    this.isSelf,
    this.isDeleted,
    this.isSelfCreated,
    this.quitTime,
    this.isAgent,
    this.isBind,
    this.avatar,
  });

  /// 是否已退场
  bool isExit() {
    return quitTime != null && quitTime != '0';
  }

  /// 是否已删除
  bool isContactDeleted() {
    return isDeleted == 1;
  }

  /// 联系人状态
  String getStateText() {
    if (isContactDeleted()) {
      return '已删除';
    }
    if (isExit() && isDeleted != null && isDeleted != 0) {
      return '已退场';
    }
    return '';
  }

  /// 是否是带班
  bool isContactAgent() {
    return isAgent == '1';
  }
}
