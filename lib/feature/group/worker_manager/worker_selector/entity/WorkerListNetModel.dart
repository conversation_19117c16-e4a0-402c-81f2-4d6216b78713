import 'dart:convert';

import 'package:gdjg_pure_flutter/feature/group/worker_selector/entity/worker_selector_props.dart';
import 'package:json_annotation/json_annotation.dart';

part 'WorkerListNetModel.g.dart';

@JsonSerializable()
class ApiV3WorkerDeptNetModel {
  double? code;
  ApiV3WorkerDeptModel? data;
  String? msg;

  ApiV3WorkerDeptNetModel();

  factory ApiV3WorkerDeptNetModel.fromJson(Map<String, dynamic> json) =>
      _$ApiV3WorkerDeptNetModelFromJson(json);

  Map<String, dynamic> toJson() => _$ApiV3WorkerDeptNetModelToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class ApiV3WorkerDeptModel {
  List<NoteWorker>? note_worker;
  String? share_token;
  double? del_num;

  ApiV3WorkerDeptModel();

  factory ApiV3WorkerDeptModel.fromJson(Map<String, dynamic> json) =>
      _$ApiV3WorkerDeptModelFromJson(json);

  Map<String, dynamic> toJson() => _$ApiV3WorkerDeptModelToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }

  /// 转业务实体
  List<ContactInfoEntity> toContactInfoEntity() =>
      note_worker
          ?.map((item) => ContactInfoEntity(
              id: item.worker_id.toString(),
              name: item.name,
              tel: item.tel,
              isSelf: item.is_self?.toString(),
              isDeleted: 0,
              isSelfCreated: item.is_self_created?.toString(),
              quitTime: item.quit_time?.toString(),
              isAgent: item.is_agent?.toString(),
              isBind: item.is_bind?.toString(),
              avatar: item.avatar))
          .toList() ??
      [];
}

@JsonSerializable()
class NoteWorker {
  double? worker_id;
  String? name;
  String? tel;
  String? name_py;
  String? name_color;
  String? avatar;
  double? quit_time;
  double? is_bind;
  double? member_id;
  double? user_id;
  double? is_rest;
  double? is_self;
  double? is_self_created;
  double? is_agent;
  double? contract_employee_status;
  double? is_grant;

  NoteWorker();

  factory NoteWorker.fromJson(Map<String, dynamic> json) =>
      _$NoteWorkerFromJson(json);

  Map<String, dynamic> toJson() => _$NoteWorkerToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
