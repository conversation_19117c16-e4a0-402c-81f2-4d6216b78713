import 'package:get/get.dart';

import '../../entity/worker_selector_props.dart';

/// @date 2025/06/13
/// @description JDetail页UI状态
class WorkerSelectorUIState {
  bool? isShowError = false;

  // 联系人大列表数据
  List<WorkerContactUIState>? data;

  WorkerSelectorUIState({
    this.isShowError,
    this.data,
  });
}

/// 工友信息UI状态
class WorkerContactUIState {
  // 工友id
  String? id;

  // 工友名称
  String? name;

  // 工友名字简称
  String? shortName;

  // 工友手机
  String? tel;

  // 工友是否是自己
  String? isSelf;

  // 工友头像
  String? avatar;

  // 工友状态
  String? status;

  WorkerContactUIState({
    this.id,
    this.name,
    this.shortName,
    this.tel,
    this.isSelf,
    this.avatar,
    this.status,
  });

  @override
  String toString() {
    // TODO: implement toString
    return 'WorkerContactUIState{id: $id, name: $name, shortName: $shortName, tel: $tel, isSelf: $isSelf, avatar: $avatar, status: $status';
  }
}
