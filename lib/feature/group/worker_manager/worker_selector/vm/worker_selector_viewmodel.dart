import "package:gdjg_pure_flutter/utils/system_util/string_util.dart";
import "package:get/get.dart";
import "../ui_rep/worker_selector_ui_rep.dart";
import "protocol/worker_selector_ui_state.dart";

/// @date 2025/06/13
/// @description WorkerSelector页ViewModel
/// 1. 处理数据的加载状态，如loading和错误视图等
/// 2. 观测UIRep中的业务实体转为UIState
/// 3. 接受来自view的意图并分发事件进行处理（暂无）
/// 4. 对View层暴露事件（操作视图和处理一些与系统权限交互等场景）
class WorkerSelectorViewModel {
  var isLoading = false.obs;
  var uiState = WorkerSelectorUIState().obs;
  var uiRep = WorkerSelectorUIRep();

  // 选择状态管理
  var selectedWorkerIds = <String>{}.obs;
  var searchText = ''.obs;
  var filteredWorkers = <WorkerContactUIState>[].obs;

  /// 是否显示搜索视图
  var showSearchView = false.obs;

  // 分页相关
  var currentPage = 1.obs;
  var hasMore = true.obs;
  var isLoadingMore = false.obs;

  WorkerSelectorViewModel() {
    fetchData();
    ever(uiRep.entity, (value) {
      convertEntityToUIState(value);
    });
  }

  /// 初始化数据同步，如果不需要loading，则在这里去掉
  void fetchData() async {
    isLoading.value = true;
    try {
      // 同步获取数据
      await uiRep.fetchData();
      // 这里的result仅做弹窗之类的处理，不在此处转为UI实体
    } catch (e) {
      uiState.value.data = null;
      uiState.value.isShowError = true;
    } finally {
      isLoading.value = false;
    }
  }

  /// 将实体数据转换为UIState
  /// @param entity
  /// @returns
  void convertEntityToUIState(WorkerSelectorUIRepEntity entity) {
    uiState.value.data = entity.data
            ?.map((item) => WorkerContactUIState(
                  id: item.id,
                  name: item.name,
                  shortName: item.name.lastTwoChars,
                  tel: item.tel,
                  isSelf: item.isSelf,
                  avatar: item.avatar,
                  status: item.getStateText(),
                ))
            .toList() ??
        [];
  }

  /// 搜索工友
  void searchWorkers(String query) {
    searchText.value = query;
  }

  /// 获取过滤后的工友列表
  List<WorkerContactUIState> getFilteredWorkers() {
    final allWorkers = uiState.value.data ?? [];
    final query = searchText.value;

    if (query.isEmpty) {
      return [];
    } else {
      return allWorkers
          .where((worker) =>
              (worker.name?.toLowerCase().contains(query.toLowerCase()) ??
                  false) ||
              (worker.tel?.contains(query) ?? false))
          .toList();
    }
  }

  /// 切换工友选择状态
  void toggleWorkerSelection(String workerId) {
    if (selectedWorkerIds.contains(workerId)) {
      selectedWorkerIds.remove(workerId);
    } else {
      selectedWorkerIds.add(workerId);
    }
    if (showSearchView.value == true) {
      showSearchView.value = false;
    }
  }

  /// 全选/取消全选
  void toggleSelectAll() {
    final currentWorkers = showSearchView.value
        ? getFilteredWorkers()
        : (uiState.value.data ?? []);

    if (selectedWorkerIds.length == currentWorkers.length) {
      // 当前是全选状态，取消全选
      selectedWorkerIds.clear();
    } else {
      // 全选
      selectedWorkerIds.clear();
      selectedWorkerIds.addAll(currentWorkers
          .map((worker) => worker.id ?? '')
          .where((id) => id.isNotEmpty));
    }
  }

  /// 是否全选状态
  bool get isAllSelected {
    final currentWorkers = showSearchView.value
        ? getFilteredWorkers()
        : (uiState.value.data ?? []);
    return currentWorkers.isNotEmpty &&
        selectedWorkerIds.length == currentWorkers.length;
  }

  /// 获取选中的工友列表
  List<WorkerContactUIState> getSelectedWorkers() {
    final allWorkers = uiState.value.data ?? [];
    return allWorkers
        .where((worker) => selectedWorkerIds.contains(worker.id))
        .toList();
  }

  /// 刷新数据
  Future<void> refreshData() async {
    currentPage.value = 1;
    hasMore.value = true;
    selectedWorkerIds.clear();
    fetchData();
  }

  /// 加载更多数据
  Future<void> loadMore() async {
    if (isLoadingMore.value || !hasMore.value) return;

    isLoadingMore.value = true;
    try {
      currentPage.value++;
      // 这里应该调用分页接口
      uiRep.fetchMoreData(currentPage.value);
    } catch (e) {
      currentPage.value--;
    } finally {
      isLoadingMore.value = false;
    }
  }
}
