import 'dart:convert';

import 'package:get/get.dart';

import '../entity/WorkerListNetModel.dart';
import '../entity/worker_selector_props.dart';

/// @date 2025/06/13
/// @description WorkerSelector页UI仓库
/// 1.为vm提供业务数据
/// 2.解耦视图层与业务仓库之间的耦合
class WorkerSelectorUIRep {
  /// 实体数据
  var entity = WorkerSelectorUIRepEntity().obs;

  bool getStatus() {
    return true;
  }

  /// 初始化数据同步，返回同步是否成功的状态给UI层
  /// 如果成功，则将业务数据存储在entity中
  Future<WorkerSelectorUIRepEntity> fetchData() async {
    // 调用网络的方法获取数据-GET/api/v3/worker/list/dept
    // param = {
    //       dept_id, is_deleted,
    //     }

    await Future.delayed(const Duration(milliseconds: 500));
    final jsonStr = jsonEncode({
      "note_worker": [
        {
          "worker_id": 3174224,
          "name": "Mock工人",
          "tel": "185****9898",
          "name_py": "M",
          "name_color": "#be92e8",
          "avatar":
              "https:\/\/static-test-public.cdqlkj.cn\/r\/8375\/108\/pb\/p\/20241011\/db48869d7f684d3d98063219792425ef.jpeg",
          "quit_time": 0,
          "is_bind": 1,
          "member_id": 24847,
          "user_id": 23505,
          "is_rest": 0,
          "is_self": 1,
          "is_self_created": 1,
          "is_agent": 0,
          "contract_employee_status": 0,
          "is_grant": 0,
          "occ": null
        },
        {
          "worker_id": 3174251,
          "name": "邹强",
          "tel": "159****2774",
          "name_py": "Z",
          "name_color": "#A4BFFF",
          "avatar": "",
          "quit_time": 0,
          "is_bind": 0,
          "member_id": null,
          "user_id": null,
          "is_rest": 0,
          "is_self": 0,
          "is_self_created": 0,
          "is_agent": 0,
          "contract_employee_status": 0,
          "is_grant": 0,
          "occ": null
        },
        {
          "worker_id": 3174250,
          "name": "赵丹",
          "tel": "150****8082",
          "name_py": "Z",
          "name_color": "#A4BFFF",
          "avatar": "",
          "quit_time": 0,
          "is_bind": 0,
          "member_id": null,
          "user_id": null,
          "is_rest": 0,
          "is_self": 0,
          "is_self_created": 0,
          "is_agent": 0,
          "contract_employee_status": 0,
          "is_grant": 0,
          "occ": null
        },
        {
          "worker_id": 3174249,
          "name": "赵川",
          "tel": "173****3606",
          "name_py": "Z",
          "name_color": "#58C7FF",
          "avatar": "",
          "quit_time": 0,
          "is_bind": 0,
          "member_id": null,
          "user_id": null,
          "is_rest": 0,
          "is_self": 0,
          "is_self_created": 0,
          "is_agent": 0,
          "contract_employee_status": 0,
          "is_grant": 0,
          "occ": null
        },
        {
          "worker_id": 3174248,
          "name": "张学成",
          "tel": "186****5503",
          "name_py": "Z",
          "name_color": "#58C7FF",
          "avatar": "",
          "quit_time": 0,
          "is_bind": 0,
          "member_id": null,
          "user_id": null,
          "is_rest": 0,
          "is_self": 0,
          "is_self_created": 0,
          "is_agent": 0,
          "contract_employee_status": 0,
          "is_grant": 0,
          "occ": null
        },
        {
          "worker_id": 3174247,
          "name": "张伟",
          "tel": "157****5861",
          "name_py": "Z",
          "name_color": "#A4BFFF",
          "avatar": "",
          "quit_time": 0,
          "is_bind": 0,
          "member_id": null,
          "user_id": null,
          "is_rest": 0,
          "is_self": 0,
          "is_self_created": 0,
          "is_agent": 0,
          "contract_employee_status": 0,
          "is_grant": 0,
          "occ": null
        },
        {
          "worker_id": 3174246,
          "name": "张琼之",
          "tel": "183****7494",
          "name_py": "Z",
          "name_color": "#74E8D5",
          "avatar": "",
          "quit_time": 0,
          "is_bind": 0,
          "member_id": null,
          "user_id": null,
          "is_rest": 0,
          "is_self": 0,
          "is_self_created": 0,
          "is_agent": 0,
          "contract_employee_status": 0,
          "is_grant": 0,
          "occ": null
        },
        {
          "worker_id": 3174245,
          "name": "张庆广",
          "tel": "151****5609",
          "name_py": "Z",
          "name_color": "#A4BFFF",
          "avatar": "",
          "quit_time": 0,
          "is_bind": 0,
          "member_id": null,
          "user_id": null,
          "is_rest": 0,
          "is_self": 0,
          "is_self_created": 0,
          "is_agent": 0,
          "contract_employee_status": 0,
          "is_grant": 0,
          "occ": null
        },
        {
          "worker_id": 3174244,
          "name": "张雷",
          "tel": "186****1625",
          "name_py": "Z",
          "name_color": "#74E8D5",
          "avatar": "",
          "quit_time": 0,
          "is_bind": 0,
          "member_id": null,
          "user_id": null,
          "is_rest": 0,
          "is_self": 0,
          "is_self_created": 0,
          "is_agent": 0,
          "contract_employee_status": 0,
          "is_grant": 0,
          "occ": null
        },
        {
          "worker_id": 3174243,
          "name": "张璐璐",
          "tel": "182****1907",
          "name_py": "Z",
          "name_color": "#74E8D5",
          "avatar": "",
          "quit_time": 0,
          "is_bind": 0,
          "member_id": null,
          "user_id": null,
          "is_rest": 0,
          "is_self": 0,
          "is_self_created": 0,
          "is_agent": 0,
          "contract_employee_status": 0,
          "is_grant": 0,
          "occ": null
        },
        {
          "worker_id": 3174242,
          "name": "张春梅",
          "tel": "182****0283",
          "name_py": "Z",
          "name_color": "#74E8D5",
          "avatar": "",
          "quit_time": 0,
          "is_bind": 0,
          "member_id": null,
          "user_id": null,
          "is_rest": 0,
          "is_self": 0,
          "is_self_created": 0,
          "is_agent": 0,
          "contract_employee_status": 0,
          "is_grant": 0,
          "occ": null
        },
        {
          "worker_id": 3174241,
          "name": "勇哥哥",
          "tel": "186****9586",
          "name_py": "Y",
          "name_color": "#4ECBF4",
          "avatar": "",
          "quit_time": 0,
          "is_bind": 0,
          "member_id": null,
          "user_id": null,
          "is_rest": 0,
          "is_self": 0,
          "is_self_created": 0,
          "is_agent": 0,
          "contract_employee_status": 0,
          "is_grant": 0,
          "occ": null
        },
        {
          "worker_id": 3174234,
          "name": "宇文嘉嘉",
          "tel": "155****5587",
          "name_py": "Y",
          "name_color": "#74E8D5",
          "avatar": "",
          "quit_time": 0,
          "is_bind": 0,
          "member_id": null,
          "user_id": null,
          "is_rest": 0,
          "is_self": 0,
          "is_self_created": 0,
          "is_agent": 0,
          "contract_employee_status": 0,
          "is_grant": 0,
          "occ": null
        },
        {
          "worker_id": 3174233,
          "name": "宇文",
          "tel": "158****7487",
          "name_py": "Y",
          "name_color": "#74E8D5",
          "avatar": "",
          "quit_time": 0,
          "is_bind": 0,
          "member_id": null,
          "user_id": null,
          "is_rest": 0,
          "is_self": 0,
          "is_self_created": 0,
          "is_agent": 0,
          "contract_employee_status": 0,
          "is_grant": 0,
          "occ": null
        },
        {
          "worker_id": 3174232,
          "name": "姚老板",
          "tel": "182****2933",
          "name_py": "Y",
          "name_color": "#4ECBF4",
          "avatar": "",
          "quit_time": 0,
          "is_bind": 0,
          "member_id": null,
          "user_id": null,
          "is_rest": 0,
          "is_self": 0,
          "is_self_created": 0,
          "is_agent": 0,
          "contract_employee_status": 0,
          "is_grant": 0,
          "occ": null
        },
        {
          "worker_id": 3174231,
          "name": "叶龙",
          "tel": "158****4013",
          "name_py": "Y",
          "name_color": "#74E8D5",
          "avatar": "",
          "quit_time": 0,
          "is_bind": 0,
          "member_id": null,
          "user_id": null,
          "is_rest": 0,
          "is_self": 0,
          "is_self_created": 0,
          "is_agent": 0,
          "contract_employee_status": 0,
          "is_grant": 0,
          "occ": null
        }
      ],
      "share_token": "3b3912944456cc46f63c6678b6469fde",
      "del_num": 0
    });
    Map<String, dynamic> jsonMap = json.decode(jsonStr);
    final mockData = ApiV3WorkerDeptModel.fromJson(jsonMap);
    final newData = mockData.toContactInfoEntity();
    // 返回成功的情况
    entity.value = WorkerSelectorUIRepEntity(data: newData);
    // 模拟异常情况
    // throw Exception("error");
    return entity.value;
  }

  /// 加载更多数据
  Future<WorkerSelectorUIRepEntity> fetchMoreData(int page) async {
    // 模拟分页加载
    await Future.delayed(const Duration(milliseconds: 1000));

    // 模拟没有更多数据
    if (page > 2) {
      return WorkerSelectorUIRepEntity(data: []);
    }

    // 模拟第二页数据
    final moreJsonStr = jsonEncode({
      "note_worker": [
        {
          "worker_id": 3174225,
          "name": "张三",
          "tel": "139****8888",
          "name_py": "Z",
          "name_color": "#be92e8",
          "avatar": "",
          "quit_time": 0,
          "is_bind": 0,
          "member_id": null,
          "user_id": null,
          "is_rest": 0,
          "is_self": 0,
          "is_self_created": 0,
          "is_agent": 0,
          "contract_employee_status": 0,
          "is_grant": 0,
          "occ": null
        },
        {
          "worker_id": 3174226,
          "name": "李四",
          "tel": "138****9999",
          "name_py": "L",
          "name_color": "#74E8D5",
          "avatar": "",
          "quit_time": 0,
          "is_bind": 0,
          "member_id": null,
          "user_id": null,
          "is_rest": 0,
          "is_self": 0,
          "is_self_created": 0,
          "is_agent": 0,
          "contract_employee_status": 0,
          "is_grant": 0,
          "occ": null
        }
      ]
    });

    Map<String, dynamic> moreJsonMap = json.decode(moreJsonStr);
    final moreResult = ApiV3WorkerDeptModel.fromJson(moreJsonMap);

    // 追加到现有数据
    final currentData = entity.value.data ?? [];
    final newData = moreResult.toContactInfoEntity();
    entity.value =
        WorkerSelectorUIRepEntity(data: [...currentData, ...newData]);

    return entity.value;
  }
}

class WorkerSelectorUIRepEntity {
  List<ContactInfoEntity>? data;

  WorkerSelectorUIRepEntity({this.data});

  @override
  String toString() {
    return 'WorkerSelectorUIRepEntity{data: $data}';
  }
}
