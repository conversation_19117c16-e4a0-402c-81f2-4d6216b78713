import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/feature/group/worker_manager/worker_tel_modify/entity/modify_worker_tel_props.dart';
import 'package:gdjg_pure_flutter/feature/group/worker_manager/worker_tel_modify/vm/modify_worker_tel_vm.dart';
import 'package:gdjg_pure_flutter/utils/route_util/base_page/base_ful_page.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';

/// 工友电话修改页面
/// @date 2025/06/20
/// @param props 页面路由参数
/// @description ModifyWorkerTel页面入口
class ModifyWorkerTelPage extends BaseFulPage {
  ModifyWorkerTelPage({super.key}) : super(appBar: YPAppBar(title: "修改工友电话"));

  @override
  State createState() => ModifyWorkerTelPageState();
}

class ModifyWorkerTelPageState extends BaseFulPageState {
  late ModifyWorkerTelProps props;
  late ModifyWorkerTelVM viewModel;
  late TextEditingController _telController;
  final FocusNode _telFocusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    viewModel = ModifyWorkerTelVM(props);
    _telController = TextEditingController();
    _telController.text = props.tel ?? '';

    // 页面加载完成后自动获取焦点
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _telFocusNode.requestFocus();
    });
  }

  @override
  void onPageRoute(Object? routeParams, bool fromLaunchTask) {
    super.onPageRoute(routeParams, fromLaunchTask);
    dynamicRightWidget = _buildCustomAppBarRight();
    props = routeParams as ModifyWorkerTelProps;
  }

  @override
  void dispose() {
    _telController.dispose();
    _telFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget yBuild(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      body: Container(
        margin: EdgeInsets.only(top: 8.h),
        color: Colors.white,
        child: Column(
          children: [
            _buildTelField(),
            _buildBottomView(),
          ],
        ),
      ),
    );
  }

  Widget _buildCustomAppBarRight() {
    return Container(
      margin: EdgeInsets.only(right: 16.w),
      padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 8.h),
      decoration: BoxDecoration(
        color: ColorsUtil.ypGreyColor,
        borderRadius: BorderRadius.circular(4.0),
      ),
      child: Text(
        "保存",
        style: TextStyle(
          color: ColorsUtil.black25,
          fontSize: 16.sp,
        ),
      ),
    );
  }

  /// 输入电话号码视图
  Widget _buildTelField() {
    return Container(
      margin: EdgeInsets.only(top: 16.h),
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      child: Container(
        decoration: BoxDecoration(
            border: Border(
                bottom: BorderSide(
          color: ColorsUtil.ypGreyColor,
          width: 0.5.w,
        ))),
        child: Row(
          children: [
            Text(
              '电话号码',
              style: TextStyle(fontSize: 15.sp, color: ColorsUtil.black85),
            ),
            SizedBox(width: 20.w), // 添加20像素的间距
            Expanded(
                child: TextField(
              controller: _telController,
              focusNode: _telFocusNode,
              keyboardType: TextInputType.number,
              decoration: InputDecoration(
                hintText: "请输入真实手机号码",
                hintStyle: TextStyle(
                  color: ColorsUtil.black25,
                  fontSize: 16.sp,
                ),
                border: InputBorder.none,
              ),
            )),
          ],
        ),
      ),
    );
  }

  /// 构建底部按钮区域
  Widget _buildBottomView() {
    return Container(
      width: 310.w,
      margin: EdgeInsets.only(top: 36.h),
      child: Column(
        spacing: 6.h,
        children: [
          GestureDetector(
            onTap: viewModel.onShareTap,
            child: Container(
              width: 310.w,
              padding: EdgeInsets.symmetric(vertical: 10.h),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(4.w),
                border: Border.all(
                  color: ColorsUtil.ypPrimaryColor,
                  width: 1.w,
                ),
              ),
              child: Center(
                child: Text(
                  '分享给工友',
                  style: TextStyle(
                    fontSize: 16.sp,
                    color: ColorsUtil.ypPrimaryColor,
                  ),
                ),
              ),
            ),
          ),
          Text(
            '不知道工人电话？分享给工人自己填写工友填写完手机号后，将自动修改现有手机号',
            style: TextStyle(fontSize: 14.sp, color: ColorsUtil.ypPrimaryColor),
          ),
        ],
      ),
    );
  }
}
