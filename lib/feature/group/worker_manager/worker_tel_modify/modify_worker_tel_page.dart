import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/feature/group/worker_manager/worker_tel_modify/entity/modify_worker_tel_props.dart';
import 'package:gdjg_pure_flutter/feature/group/worker_manager/worker_tel_modify/vm/modify_worker_tel_vm.dart';
import 'package:gdjg_pure_flutter/utils/route_util/base_page/base_ful_page.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';
import 'package:get/get.dart';

/// 工友电话修改页面
/// @date 2025/06/20
/// @param props 页面路由参数
/// @description ModifyWorkerTel页面入口
class ModifyWorkerTelPage extends BaseFulPage {
  ModifyWorkerTelPage({super.key}) : super(appBar: YPAppBar(title: "修改工友电话"));

  @override
  State createState() => ModifyWorkerTelPageState();
}

class ModifyWorkerTelPageState extends BaseFulPageState {
  late ModifyWorkerTelProps props;
  late ModifyWorkerTelVM viewModel;
  late TextEditingController _telController;
  late FocusNode _telFocusNode;

  @override
  void initState() {
    super.initState();
    viewModel = ModifyWorkerTelVM(props);
    _telController = TextEditingController();
    _telController.text = props.tel ?? '';
    _telFocusNode = FocusNode();
  }

  @override
  void onPageRoute(Object? routeParams, bool fromLaunchTask) {
    super.onPageRoute(routeParams, fromLaunchTask);
    dynamicRightWidget = _buildCustomAppBarRight();
    props = routeParams as ModifyWorkerTelProps;
  }

  @override
  Widget yBuild(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      body: Container(
        margin: EdgeInsets.only(top: 12),
        color: Colors.white,
        child: Column(
          children: [
            _buildTelField(),
          ],
        ),
      ),
    );
  }

  Widget _buildCustomAppBarRight() {
    return Container(
      margin: const EdgeInsets.only(right: 16.0),
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 8),
      decoration: BoxDecoration(
        color: ColorsUtil.ypGreyColor,
        borderRadius: BorderRadius.circular(4.0),
      ),
      child: Text(
        "保存",
        style: TextStyle(
          color: ColorsUtil.black25,
          fontSize: 16.sp,
        ),
      ),
    );
  }

  Widget _buildTelField() {
    return Container(
      margin: const EdgeInsets.only(top: 16.0),
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        children: [
          Text(
            '电话号码',
            style: TextStyle(fontSize: 15.sp, color: ColorsUtil.black85),
          ),
          Expanded(
              child: ColoredBox(
            color: Colors.lightGreen,
            child: TextField(
              controller: _telController,
              focusNode: _telFocusNode,
              keyboardType: TextInputType.number,
              decoration: InputDecoration(
                hintText: "请输入真实手机号码",
                hintStyle: TextStyle(
                  color: ColorsUtil.black25,
                  fontSize: 16.sp,
                ),
                border: InputBorder.none,
              ),
            ),
          )),
        ],
      ),
    );
  }
}
