class ModifyWorkerTelProps {
  /// 工友id
  int? workerId;

  /// 记工id
  String? workNoteId;

  /// 工友名称
  String? name;

  /// 工友电话
  String? tel;

  /// 部门id
  String? deptId;

  ModifyWorkerTelProps({
    this.workerId,
    this.workNoteId,
    this.name,
    this.tel,
    this.deptId,
  });

  @override
  toString() {
    return 'ModifyWorkerTelProps{workerId: $workerId, workNoteId: $workNoteId, name: $name, tel: $tel, deptId: $deptId}';
  }
}
