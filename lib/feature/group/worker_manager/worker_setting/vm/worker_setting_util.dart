import "package:gdjg_pure_flutter/data/fee_standard_data/repo/model/fee_standard_biz_model.dart";
import "package:gdjg_pure_flutter/feature/group/common_params_model/worker_model.dart";
import "package:gdjg_pure_flutter/feature/group/dialog/wage_rule_setting/entity/wage_rules_props.dart";
import "package:gdjg_pure_flutter/model/RecordNoteType.dart";
import "package:gdjg_pure_flutter/model/RecordType.dart";

class WorkerSettingUtil {
  /// 构造工资规则弹窗参数
  static WageRulesProps wageRulesPropsBuilder(
      String workNoteName,
      String workNoteId,
      int? workerId,
      RwaRecordType recordType,
      String name,
      bool isShowDelete,
      FeeStandardBizModel? feeStandard) {
    return WageRulesProps(
      title: workNoteName,
      confirmRequestApi: true,
      deleteRequestApi: true,
      businessType: recordType,
      workNoteId: workNoteId,
      workers: [
        WorkerModel(
          workerId: workerId?.toDouble(),
          workerName: name,
        )
      ],
      recordNoteType: RecordNoteType.group,
      isShowDelete: isShowDelete,
      feeStandard: feeStandardBizModelCopy(feeStandard),
    );
  }

  static feeStandardBizModelCopy(FeeStandardBizModel? feeStandard) {
    return FeeStandardBizModel(
      feeStandardId: feeStandard?.feeStandardId ?? '',
      workingHoursPrice: feeStandard?.workingHoursPrice ?? "0",
      workingHoursStandard: feeStandard?.workingHoursStandard ?? "0",
      overtimeHoursPrice: feeStandard?.overtimeHoursPrice ?? "0",
      overtimeHoursStandard: feeStandard?.overtimeHoursStandard ?? "0",
      overtimeType: feeStandard?.overtimeType ?? 0.0,
      businessType: feeStandard?.businessType ?? "",
    );
  }
}
