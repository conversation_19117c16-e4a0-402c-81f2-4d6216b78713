/// @date 2025/06/20
/// @description 工友设置页基础信息UI状态
/// @param workerName 工友姓名
/// @param avatar 头像
/// @param phone 手机号
/// @param occNames 工友的职位名称
class WorkerSettingUIState {
  String? name;
  String? avatar;
  String? phone;
  String? occNames;

  WorkerSettingUIState({
    this.name,
    this.avatar,
    this.phone,
    this.occNames,
  });

  @override
  String toString() {
    return 'WorkerSettingUIState{name: $name, avatar: $avatar, phone: $phone, occNames: $occNames}';
  }
}

/// @date 2025/06/20
/// @description 工友设置页权限UI状态
/// @param hasBookPermission 是否有记工数据查看权限
/// @param hasSalaryPermission 是否有工资查看权限：需要前置权限-记工数据查看权限
class WSPermissionUIState {
  bool? hasBookPermission;
  bool? hasSalaryPermission;

  WSPermissionUIState({
    this.hasBookPermission,
    this.hasSalaryPermission,
  });

  @override
  String toString() {
    return 'WSPermissionUIState{hasBookPermission: $hasBookPermission, hasSalaryPermission: $hasSalaryPermission}';
  }
}

/// @date 2025/06/20
/// @description 工友设置页工价UI状态
/// @param partTimeSalary 点工工资
/// @param allTimeSalary 包工工资
/// @param partOverTime 点工加班
/// @param allOverTime 包工加班
class WSSalaryUIState {
  String? partTimeSalary;
  String? partOverTime;
  String? allTimeSalary;
  String? allOverTime;

  WSSalaryUIState({
    this.partTimeSalary,
    this.allTimeSalary,
    this.partOverTime,
    this.allOverTime,
  });

  @override
  String toString() {
    return 'WSSalaryUIState{partTimeSalary: $partTimeSalary, allTimeSalary: $allTimeSalary, partOverTime: $partOverTime, allOverTime: $allOverTime}';
  }
}
