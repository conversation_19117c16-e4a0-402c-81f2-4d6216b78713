import 'package:flutter/material.dart';
import 'package:gdjg_pure_flutter/iconfont/iconfont.dart';
import 'package:gdjg_pure_flutter/utils/route_util/base_page/base_ful_page.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';
import 'package:gdjg_pure_flutter/widget/page_error_view.dart';
import 'package:gdjg_pure_flutter/widget/page_loading_view.dart';
import 'package:get/get.dart';

import 'entity/worker_setting_props.dart';
import 'view/basic_info_section.dart';
import 'view/permission_section.dart';
import 'view/salary_section.dart';
import 'view/statistics_section.dart';
import 'vm/worker_setting_viewmodel.dart';

/// @date 2025/06/20 工友设置页面
/// @param props 页面路由参数
/// @returns
/// @description WorkerSetting页面入口
class WorkerSettingPage extends BaseFulPage {
  WorkerSettingPage({super.key})
      : super(
            appBar: YPAppBar(
          title: "工友设置",
          rightResTextColor: Color(0xfff54a45),
          rightBackgroundColor: Color(0xfffee5e4),
        ));

  @override
  State createState() => WorkerSettingPageState();
}

class WorkerSettingPageState<WorkSettingPage> extends BaseFulPageState {
  late WorkerSettingProps? props;
  late WorkerSettingViewModel viewModel;

  @override
  void initState() {
    super.initState();
    viewModel = WorkerSettingViewModel(props?.workerId, '', '');
    viewModel.init();
  }

  @override
  void onPageRoute(Object? routeParams, bool fromLaunchTask) {
    super.onPageRoute(routeParams, fromLaunchTask);
    props = routeParams as WorkerSettingProps?;
    dynamicTitle = props?.workNoteName ?? '工友设置';
    dynamicRightResText = getDynamicRightResText(props?.source);
    dynamicRightResIconFont = getDynamicRightResIconFont(props?.source);
    dynamicRightResTap = () {
      viewModel.onRightResTap(props?.source);
    };
  }

  @override
  Widget yBuild(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      body: Obx(() {
        if (viewModel.isLoading.value) {
          return const PageLoadingView();
        }
        if (viewModel.isShowError.value == true) {
          return PageErrorView(onReload: () => viewModel.fetchData());
        }
        return contentView();
      }),
    );
  }

  /// 根据来源获取右侧按钮文案-没有不展示文案
  String? getDynamicRightResText(WorkerSettingSource? source) {
    if (source == WorkerSettingSource.projectWorker) {
      return '将工友从项目中退场';
    }
    if (source == WorkerSettingSource.moreBook) {
      return '从通讯录中删除';
    }
    return null;
  }

  /// 根据来源获取右侧按钮图标
  IconFont? getDynamicRightResIconFont(WorkerSettingSource? source) {
    // 工友退场
    if (source == WorkerSettingSource.projectWorker) {
      return IconFont(
        IconNames.saasTuichang,
        size: 16,
        color: '#f54a45',
      );
    }
    // 工友删除
    if (source == WorkerSettingSource.moreBook) {
      return IconFont(
        IconNames.saasDelete,
        size: 16,
        color: '#f54a45',
      );
    }
    return null;
  }

  /// 实际展示的视图
  Widget contentView() {
    return SingleChildScrollView(
      child: ColoredBox(
        color: ColorsUtil.ypBgColor,
        child: Column(
          children: [
            // 基础信息区域
            BasicInfoSection(viewModel: viewModel),
            // 工价设置区域 - 仅在项目工友时显示
            if (props?.source == WorkerSettingSource.projectWorker)
              SalarySection(viewModel: viewModel),
            // 记工统计区域 - 仅在项目工友时显示
            if (props?.source == WorkerSettingSource.projectWorker)
              StatisticsSection(viewModel: viewModel),
            // 工友对工权限区域
            PermissionSection(viewModel: viewModel),
          ],
        ),
      ),
    );
  }
}
