/// @date 2025/06/20
/// @description WorkerSetting页入参
class WorkerSettingProps {
  WorkerSettingSource? source;
  String? workNoteName;
  bool? isSelf;
  int? workerId;
  String? workNoteId;

  WorkerSettingProps(
      {this.source, this.isSelf, this.workerId, this.workNoteName});

  @override
  String toString() {
    return 'WorkerSettingPropsEntity{source: $source, isSelf: $isSelf, workerId: $workerId, workNoteName: $workNoteName}';
  }
}

/// 工友设置页来源
/// @description 从哪些页面进入
/// @date 2025/06/20
/// @param moreBook 更多-工友通讯录
/// @param projectWorker 项目在场工友
/// @param bookList 添加工友-工友通讯录
/// @param defaultPage 默认页
enum WorkerSettingSource {
  moreBook,
  projectWorker,
  bookList,
  defaultPage,
}
