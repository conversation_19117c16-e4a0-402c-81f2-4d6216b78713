import 'package:flutter/material.dart';
import 'package:gdjg_pure_flutter/utils/system_util/string_util.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';
import 'package:get/get.dart';

import '../vm/worker_setting_viewmodel.dart';

/// 工友头像组件
class WorkerAvatar extends StatelessWidget {
  final WorkerSettingViewModel viewModel;
  final double size;

  const WorkerAvatar({
    super.key,
    required this.viewModel,
    this.size = 47,
  });

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final avatar = viewModel.baseUiState.value.avatar;
      return Container(
        width: size,
        height: size,
        decoration: BoxDecoration(
          color: ColorsUtil.primaryColor,
          borderRadius: BorderRadius.circular(8),
        ),
        child: avatar?.isNotEmpty == true
            ? ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: Image.network(
                  avatar!,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    return _buildDefaultAvatar();
                  },
                ),
              )
            : _buildDefaultAvatar(),
      );
    });
  }

  /// 默认头像
  Widget _buildDefaultAvatar() {
    return Obx(() {
      final name = viewModel.baseUiState.value.name ?? '';
      return Container(
        width: size,
        height: size,
        decoration: BoxDecoration(
          color: ColorsUtil.primaryColor,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Center(
          child: Text(
            name.isNotEmpty ? name.lastTwoChars ?? '' : '',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      );
    });
  }
}
