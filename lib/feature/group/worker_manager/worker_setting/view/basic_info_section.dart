import 'package:flutter/material.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';
import 'package:get/get.dart';

import '../vm/worker_setting_viewmodel.dart';
import 'worker_avatar.dart';

/// 基础信息区域组件
class BasicInfoSection extends StatelessWidget {
  final WorkerSettingViewModel viewModel;

  const BasicInfoSection({
    super.key,
    required this.viewModel,
  });

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      return Container(
        color: Colors.white,
        padding: const EdgeInsets.all(16),
        margin: const EdgeInsets.only(top: 8),
        child: GestureDetector(
          onTap: viewModel.onEditWorker,
          behavior: HitTestBehavior.opaque, // 让整个区域都能响应点击
          child: SizedBox(
            width: double.infinity, // 确保占满整个宽度
            child: Row(
              children: [
                // 头像
                WorkerAvatar(viewModel: viewModel),
                const SizedBox(width: 12),
                // 姓名和电话
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        viewModel.baseUiState.value.name ?? '',
                        style: const TextStyle(
                          fontSize: 17,
                          fontWeight: FontWeight.w500,
                          color: Colors.black,
                          height: 1.2,
                        ),
                      ),
                      Text(
                        viewModel.baseUiState.value.phone ?? '',
                        style: TextStyle(
                          fontSize: 14,
                          color: ColorsUtil.black65,
                          height: 1.2,
                        ),
                      ),
                      if(viewModel.baseUiState.value.occNames?.isNotEmpty == true)
                      Text(
                        viewModel.baseUiState.value.occNames ?? '',
                        style: TextStyle(
                          fontSize: 14,
                          color: ColorsUtil.black65,
                          height: 1.2,
                        ),
                      ),
                    ],
                  ),
                ),
                // 编辑按钮
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      '编辑',
                      style: TextStyle(
                        fontSize: 16,
                        color: ColorsUtil.black65,
                      ),
                    ),
                    const SizedBox(width: 4),
                    Icon(
                      Icons.chevron_right,
                      size: 20,
                      color: ColorsUtil.black65,
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      );
    });
  }
}
