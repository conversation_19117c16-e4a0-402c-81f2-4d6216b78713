import 'package:flutter/material.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';

class ItemTitleView extends StatelessWidget {
  final String title;

  const ItemTitleView({
    super.key,
    required this.title,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      height: 50,
      child: Text(
        title,
        style: TextStyle(
          fontSize: 14,
          color: ColorsUtil.black65,
        ),
      ),
    );
  }
}
