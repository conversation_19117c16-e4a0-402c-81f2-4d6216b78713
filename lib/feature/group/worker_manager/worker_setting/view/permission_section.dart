import 'package:flutter/material.dart';
import 'package:gdjg_pure_flutter/feature/group/worker_manager/worker_setting/view/item_title_view.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';
import 'package:get/get.dart';

import '../vm/worker_setting_viewmodel.dart';

/// 工友对工权限区域组件
class PermissionSection extends StatelessWidget {
  final WorkerSettingViewModel viewModel;

  const PermissionSection({
    super.key,
    required this.viewModel,
  });

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      return Container(
        color: Color(0xfff5f6fa),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 标题
            ItemTitleView(title: '工友对工权限'),
            // 查看记工数据权限
            _PermissionItem(
              title: '允许在线查看记工数据',
              value:
                  viewModel.permissionUiState.value.hasBookPermission ?? false,
              onChanged: viewModel.onBookPermissionChanged,
            ),
            // 查看记工工资权限
            _PermissionItem(
              title: '允许工人查看记工工资',
              value: (viewModel.permissionUiState.value.hasBookPermission ??
                      false) &&
                  (viewModel.permissionUiState.value.hasSalaryPermission ??
                      false),
              onChanged: viewModel.onSalaryPermissionChanged,
              showDivider: false,
            ),
          ],
        ),
      );
    });
  }
}

/// 权限项组件
class _PermissionItem extends StatelessWidget {
  final String title;
  final bool value;
  final ValueChanged<bool>? onChanged;
  final bool showDivider;

  const _PermissionItem({
    required this.title,
    required this.value,
    required this.onChanged,
    this.showDivider = true,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        border: showDivider
            ? const Border(
                bottom: BorderSide(
                  color: Color(0xFFF0F0F0),
                  width: 0.5,
                ),
              )
            : null,
      ),
      child: Row(
        children: [
          Expanded(
            child: Text(
              title,
              style: TextStyle(
                fontSize: 16,
                color: onChanged != null ? Colors.black : ColorsUtil.black45,
              ),
            ),
          ),
          Switch(
            value: value,
            onChanged: onChanged,
            activeColor: Colors.white,
            // 选中时圆球颜色（白色）
            activeTrackColor: Color(0xff02be02),
            // 选中时轨道颜色（绿色）
            inactiveThumbColor: Colors.white,
            // 未选中时圆球颜色（白色）
            inactiveTrackColor: Color(0xff767577),
            // 未选中时轨道颜色（灰色）
            materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
            splashRadius: 20, // 控制点击波纹半径
          ),
        ],
      ),
    );
  }
}
