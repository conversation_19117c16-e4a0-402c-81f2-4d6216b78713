import 'package:flutter/material.dart';
import 'package:gdjg_pure_flutter/utils/route_util/base_page/base_ful_page.dart';
import 'vm/worker_resume_viewmodel.dart';

/// @date 2025/07/25
/// @param props 页面路由参数
/// @returns
/// @description WorkerResume页面入口
class WorkerResumePage extends BaseFulPage {
  WorkerResumePage({super.key}) : super(appBar: YPAppBar(title: "标题"));

  @override
  State<WorkerResumePage> createState() => _WorkerResumePageState();
}

class _WorkerResumePageState extends BaseFulPageState<WorkerResumePage> {
  final WorkerResumeViewModel viewModel = WorkerResumeViewModel();

  @override
  Widget yBuild(BuildContext context) {
    return Center(
        child: Text(viewModel.uiState.value.data.toString()),
      );
  }
}
