import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/generated/assets.dart';
import 'package:gdjg_pure_flutter/utils/route_util/base_page/base_ful_page.dart';
import 'package:gdjg_pure_flutter/utils/system_util/date_util.dart';
import 'package:gdjg_pure_flutter/utils/system_util/font_util.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';
import 'package:get/get.dart';
import 'entity/group_settlement_props.dart';
import 'vm/group_settlement_viewmodel.dart';

/// @date 2025/07/09
/// @param props 页面路由参数
/// @returns
/// @description GroupSettlement未结 跳转 记结算
class GroupSettlementPage extends BaseFulPage {
  const GroupSettlementPage({super.key})
      : super(appBar: const YPAppBar(title: '结算'));

  @override
  createState() => _GroupSettlementPage();
}

class _GroupSettlementPage<GroupSettlementPage> extends BaseFulPageState {
  final GroupSettlementViewModel _viewModel = GroupSettlementViewModel();
  final TextEditingController _normalRateController = TextEditingController();

  // 用于存储ever监听器的引用，以便在dispose时取消
  final List<Worker> _workers = [];

  @override
  void onPageCreate() {
    super.onPageCreate();
    _normalRateController.addListener(() {
      if (mounted) {
        _viewModel.setMoney(_normalRateController.text);
      }
    });

    // 存储监听器引用以便后续取消
    _workers.add(ever(_viewModel.us.money, (value) {
      if (mounted && _normalRateController.text != value) {
        _normalRateController.text = value;
      }
    }));
  }

  @override
  void onPageDestroy() {
    super.onPageDestroy();
    for (var element in _workers) {
      element.dispose();
    }
    _normalRateController.dispose();
  }

  @override
  void onPageRoute(Object? routeParams, bool fromLaunchTask) {
    super.onPageRoute(routeParams, fromLaunchTask);
    var props = routeParams as GroupSettlementProps?;
    _viewModel.init(props);
  }

  @override
  Widget yBuild(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      backgroundColor: Colors.white,
      body: SingleChildScrollView(
        child: contentView(),
      ),
      bottomNavigationBar: _buildBottomView(), // 固定在底部
    );
  }

  /// 实际展示的视图，需要处理：
  /// 1. 画出UI
  /// 2. 绑定UIState
  /// 3. 处理事件监听
  /// 4. 向VM传递来自View层的事件
  Widget contentView() {
    return Column(children: [
      _buildProjectView(),
      // 金额区域
      _buildDiaryMoneyView(),
      // 照片上传区域
      _buildPhotoSection(),
      // 备注区域
      _buildRemarkSection(),
    ]);
  }

  final TextStyle textStyle = TextStyle(
    fontSize: 15,
    color: ColorsUtil.black85,
  );

  /// 构建日期项目和工友
  Widget _buildProjectView() {
    return Column(
      children: [
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 16),
          child: Container(
            padding: EdgeInsets.symmetric(vertical: 15),
            width: double.infinity,
            child: Center(
              child: Row(
                children: [
                  Text('日期：', style: textStyle),
                  Obx(
                    () => Text(
                      DateUtil.formatDate(_viewModel.us.dateTime),
                      style: TextStyle(
                          fontSize: 15,
                          color: ColorsUtil.black85,
                          fontWeight: FontWeight.w500),
                    ),
                  ),
                  Spacer(),
                  SizedBox(width: 4),
                  Image.asset(Assets.commonIconArrowRightGrey,
                      width: 18, height: 18)
                ],
              ),
            ),
          ),
        ),
        Divider(
          thickness: 8,
          color: Color(0xFFF5F5F5),
          height: 8,
        ),
        Container(
          padding: EdgeInsets.symmetric(horizontal: 16),
          width: double.infinity,
          child: Column(
            children: [
              Container(
                width: double.infinity,
                padding: EdgeInsets.symmetric(vertical: 15),
                child: Center(
                  child: Row(
                    children: [
                      Text('项目：', style: textStyle),
                      Obx(
                        () => Text(_viewModel.us.workNoteName,
                            style: TextStyle(
                                fontSize: 15,
                                color: ColorsUtil.black85,
                                fontWeight: FontWeight.w500)),
                      ),
                    ],
                  ),
                ),
              ),
              Divider(
                thickness: 1,
                color: Color(0xFFF5F5F5),
                height: 1,
              ),
              Container(
                width: double.infinity,
                padding: EdgeInsets.symmetric(vertical: 15),
                child: Center(
                  child: Row(
                    children: [
                      Text('工友：', style: textStyle),
                      Obx(
                        () => Text(_viewModel.us.workerName,
                            style: TextStyle(
                                fontSize: 15,
                                color: ColorsUtil.black85,
                                fontWeight: FontWeight.w500)),
                      ),
                      Spacer(),
                      SizedBox(width: 4),
                      Image.asset(Assets.commonIconArrowRightGrey,
                          width: 18, height: 18)
                    ],
                  ),
                ),
              ),
              Divider(
                thickness: 1,
                color: Color(0xFFF5F5F5),
                height: 1,
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// 金额区域
  Widget _buildDiaryMoneyView() {
    return Container(
      width: double.infinity,
      margin: EdgeInsets.symmetric(horizontal: 16),
      padding: EdgeInsets.symmetric(vertical: 20),
      decoration: BoxDecoration(
          color: Colors.white,
          border:
              Border(bottom: BorderSide(color: Color(0xFFF5F5F5), width: 1))),
      child: Row(
        children: [
          Text('金额：', style: textStyle),
          Expanded(child: _buildInputField(_normalRateController)),
        ],
      ),
    );
  }

  ///输入框布局
  Widget _buildInputField(TextEditingController? controller) {
    return SizedBox(
      width: double.infinity,
      height: 50.h,
      child: TextField(
        maxLength: 7,
        controller: controller,
        textAlign: TextAlign.left,
        inputFormatters: <TextInputFormatter>[
          LengthLimitingTextInputFormatter(7),
        ],
        // 设置光标颜色
        cursorColor: ColorsUtil.primaryColor,
        keyboardType: TextInputType.text,
        decoration: InputDecoration(
          counterText: '',
          hintText: '0.00',
          hintStyle: TextStyle(
            fontSize: 36.sp,
            color: ColorsUtil.hintFontColor,
            fontFamily: FontUtil.fontCondMedium,
          ),
          // 关键代码：添加 hint
          isDense: true,
          border: OutlineInputBorder(
            borderSide: BorderSide(width: 0, color: Colors.transparent),
          ),
          enabledBorder: OutlineInputBorder(
            borderSide: const BorderSide(width: 0, color: Colors.transparent),
          ),
          focusedBorder: OutlineInputBorder(
            borderSide: const BorderSide(width: 0, color: Colors.transparent),
          ),
        ),
        style: TextStyle(
          fontSize: 36.sp,
          color: Color(0xFF323232),
          fontFamily: FontUtil.fontCondMedium,
        ),
      ),
    );
  }

  /// 照片上传区域
  Widget _buildPhotoSection() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(16.w),
      color: Colors.white,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('照片：', style: textStyle),
          GestureDetector(
            onTap: () {
              _viewModel.baseVm.showPhotoSelectionDialog();
            },
            child: Container(
              width: 80.w,
              height: 80.w,
              decoration: BoxDecoration(
                color: const Color(0xFFF5F5F5),
                borderRadius: BorderRadius.circular(8.r),
                border: Border.all(
                  color: const Color(0xFFE0E0E0),
                  width: 1.w,
                ),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.file_upload_outlined,
                    size: 24.sp,
                    color: const Color(0xFF999999),
                  ),
                  SizedBox(height: 4.h),
                  Text(
                    '添加照片/视频',
                    style: TextStyle(
                      fontSize: 10.sp,
                      color: const Color(0xFF999999),
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建备注区域
  Widget _buildRemarkSection() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(15.w),
      color: Colors.white,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('备注：', style: textStyle),
          Expanded(
            child: GestureDetector(
              onTap: () {
                _viewModel.baseVm.setOnJumpToNotePage();
              },
              child: Container(
                height: 90.h,
                decoration: BoxDecoration(
                  color: const Color(0xFFF5F5F5),
                  borderRadius: BorderRadius.circular(2.r),
                ),
                child: Padding(
                  padding: EdgeInsets.only(left: 12.w, top: 6.h),
                  child: Obx(
                    () => Text(
                      _viewModel.baseVm.baseUs.remark.length <= 0
                          ? '请输入备注...'
                          : _viewModel.baseVm.baseUs.remark,
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: _viewModel.baseVm.baseUs.remark.length <= 0
                            ? const Color(0xFF999999)
                            : const Color(0xFF000000),
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  ///底部取消按钮和确认按钮
  Widget _buildBottomView() {
    return Container(
      padding: EdgeInsets.only(left: 16, right: 16, top: 10, bottom: 10),
      decoration: BoxDecoration(
        border: Border(
          top: BorderSide(color: Color(0xFFF5F5F5), width: 0.5.w),
        ),
      ),
      child: GestureDetector(
        onTap: _viewModel.onConfirmTap,
        behavior: HitTestBehavior.opaque,
        child: Container(
          height: 44,
          alignment: Alignment.center,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(4.r),
            border:
                Border.all(color: ColorsUtil.primaryColor, width: 1.w),
            color: ColorsUtil.primaryColor,
          ),
          child: Text('确认记账',
              style: TextStyle(fontSize: 16.sp, color: Colors.white)),
        ),
      ),
    );
  }
}
