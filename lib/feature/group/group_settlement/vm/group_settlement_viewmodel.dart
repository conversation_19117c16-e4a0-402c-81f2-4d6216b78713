import "package:gdjg_pure_flutter/data/group_data/group_settlement/ds/model/param/group_settlement_param_model.dart";
import "package:gdjg_pure_flutter/data/group_data/group_settlement/repo/project_settle_repo.dart";
import "package:gdjg_pure_flutter/feature/group/common_params_model/worker_model.dart";
import "package:gdjg_pure_flutter/feature/group/group_edit_record_work/view/vm/edit_base_vm.dart";
import "package:gdjg_pure_flutter/feature/group/group_event_bus/group_edit_wage_event_bus_model.dart";
import "package:gdjg_pure_flutter/feature/group/group_settlement/entity/group_settlement_props.dart";
import "package:gdjg_pure_flutter/utils/event_bus_util/event_bus_util.dart";
import "package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart";
import "package:gdjg_pure_flutter/utils/system_util/date_util.dart";
import "package:gdjg_pure_flutter/utils/system_util/string_util.dart";
import "package:gdjg_pure_flutter/utils/ui_util/common_dialog.dart";
import "package:gdjg_pure_flutter/utils/ui_util/toast_util.dart";

import "group_settlement_us.dart";

/// @date 2025/07/09
/// @description GroupSettlement页ViewModel
/// 1. 处理数据的加载状态，如loading和错误视图等
/// 2. 观测UIRep中的业务实体转为UIState
/// 3. 接受来自view的意图并分发事件进行处理（暂无）
/// 4. 对View层暴露事件（操作视图和处理一些与系统权限交互等场景）
class GroupSettlementViewModel {
  final baseVm = EditBaseVM();
  final _repo = ProjectSettleRepo();
  var us = GroupSettlementUS();
  var _dateTime = DateTime.now();
  var _workers = <WorkerModel>[];
  var _workNoteId = '';
  var _money = '';
  var _bookkeepingSource = 0.0;
  var _sourceFixId = 0.0;

  void init(GroupSettlementProps? props) {
    if (props != null) {
      _workNoteId = props.workNoteId;
      us.setWorkNoteName(props.workNoteName ?? '');
      setMoney(props.money.formatStringToMoney());
      setWorkerList(props.workers ?? []);
      setDateTime(props.endTime ?? DateTime.now());
      fetchData();
    }
  }

  /// 初始化数据同步，如果不需要loading，则在这里去掉
  void fetchData() async {
    var result = await _repo.getBkWageIndex(_workNoteId);
    if (result.isOK()) {
      var data = result.getSucData();
      if (data != null) {
        _bookkeepingSource = data.bookkeepingSource;
        _sourceFixId = data.sourceFixId;
      }
    }
  }

  void setMoney(String value) {
    if (value == _money) return;
    var moneyValue = double.tryParse(value) ?? 0;
    if(moneyValue<=0){
      _money = '0.00';
      us.setMoney('');
    return;
    }
    _money = value;
    us.setMoney(value);
  }

  void setWorkerList(List<WorkerModel> workers) {
    _workers = workers;
    var workerName =
        workers.map((worker) => worker.workerName.toString()).join(',');
    us.setWorkerName(workerName);
  }

  void setDateTime(DateTime date) {
    _dateTime = date;
    us.setDateTime(date);
  }

  void onConfirmTap() {
    if (_money.isEmpty || (double.tryParse(_money) ?? 0) <= 0) {
      showCommonDialog(CommonDialogConfig(
        title: '提示?',
        content: '当前结算金额为0.00元，确定要记为结算吗？',
        negative: '取消',
        positive: '确认',
        onPositive: () {
          _commit();
        },
      ));
    } else {
      _commit();
    }
  }

  Future<void> _commit() async {
    var workerIds =
        _workers.map((worker) => worker.workerId.toString()).join(',');
    var params = GroupSettlementParamModel(
      work_note: _workNoteId,
      worker_id: workerIds,
      bookkeeping_source: _bookkeepingSource,
      img_url: baseVm.baseUs.photoUrls.join(','),
      money: _money,
      source_fix_id: _sourceFixId,
      business_time: DateUtil.formatDate(_dateTime),
    );
    var result = await _repo.addMyself(params);
    if (result.isOK()) {
      // 刷新流水，日历
      EventBusUtil.emit(GroupEditWageEventBusModel());
      YPRoute.closePage();
    } else {
      ToastUtil.showToast(result.fail?.errorMsg ?? "");
    }
  }
}
