import 'package:get/get.dart';

/// @date 2025/07/09
/// @description JDetail页UI状态
class GroupSettlementUS {

  /// 时间
  final _dateTime = DateTime.now().obs;

  ///项目名称
  final _workNoteName = ''.obs;

  ///工友名称
  final _workerName = ''.obs;

  Rx<String> money = '0.00'.obs;

  DateTime get dateTime => _dateTime.value;

  String get workNoteName => _workNoteName.value;

  String get workerName => _workerName.value;


  setDateTime(DateTime dateTime) {
    _dateTime.value = dateTime;
    _dateTime.refresh();
  }

  setWorkNoteName(String workNoteName) {
    _workNoteName.value = workNoteName;
    _workNoteName.refresh();
  }

  setWorkerName(String workerName) {
    _workerName.value = workerName;
    _workerName.refresh();
  }

  setMoney(String value) {
    money.value = value;
    money.refresh();
  }

}
