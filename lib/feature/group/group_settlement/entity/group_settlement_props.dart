import 'package:gdjg_pure_flutter/feature/group/common_params_model/worker_model.dart';

/// @date 2025/07/09
/// @description 未结记工结算参数
class GroupSettlementProps {
  ///
  String workNoteId;

  ///
  String workNoteName;

  ///
  DateTime? endTime;

  ///
  String? money;

  ///工友
  List<WorkerModel>? workers;

  GroupSettlementProps({
    this.workNoteId = '',
    this.workNoteName = '',
    this.endTime,
    this.money,
    this.workers,
  });

  @override
  String toString() {
    return 'GroupSettlementProps{data: $workNoteId,$workNoteName,$endTime,$money,$workers}'
        .toString();
  }
}
