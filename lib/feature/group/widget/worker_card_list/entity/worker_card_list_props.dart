import 'package:gdjg_pure_flutter/data/worker_data/worker_info/repo/model/workers_get_worker_info_biz_model.dart';
import 'package:gdjg_pure_flutter/feature/group/widget/worker_card_list/vm/protocol/worker_card_list_ui_state.dart';
import 'package:gdjg_pure_flutter/utils/system_util/string_util.dart';

/// 列表选中回调---如果列表允许选中的话
typedef WorkerSelectCallBack = void Function(List<int> selectIds);

/// 工友列表传参
/// @date 2025/06/25
/// @param canSelect 列表项是否可以选择
/// @param isStickyHeader 是否让分组标题吸顶
/// @param selectIds 已经选中的工友id
/// @param data 工友数据
/// @param onSelectCallBack 选中回调
/// @param source 来源
class WorkerCardListProps {
  bool? canSelect;
  bool? isStickyHeader;
  String? source;
  List<int>? selectIds;
  List<WorkersGetWorkerInfoBizModel>? data;
  WorkerSelectCallBack? onSelectCallBack;

  WorkerCardListProps(
      {this.data,
      this.canSelect,
      this.selectIds,
      this.onSelectCallBack,
      this.isStickyHeader,
      this.source});

  @override
  String toString() {
    return 'WorkerCardListProps{data: ${data.toString()}';
  }

  /// 将数据转换为WorkerCardSections
  /// isIdentify：取isAgent
  /// title：取namePy
  List<WorkerCardItemUIState> toWorkerUIState() {
    return data
            ?.map((item) => WorkerCardItemUIState(
                  workerId: item.id,
                  name: item.name,
                  shortName: item.name.lastTwoChars ?? "",
                  tel: item.tel,
                  avatar: item.avatar,
                  isIdentify: item.isAgent == 1,
                  namePy: item.namePy,
                  occupationName: item.occ?.occupationName ?? "",
                ))
            .toList() ??
        [];
  }
}
