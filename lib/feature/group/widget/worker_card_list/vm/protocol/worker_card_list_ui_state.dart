/// @date 2025/06/24
/// @description JDetail页UI状态
class WorkerCardListUIState {
  List<WorkerCardItemUIState> list;

  WorkerCardListUIState({
    required this.list,
  });

  @override
  String toString() {
    return 'WorkerCardListUIState{list: $list}';
  }
}

/// @date 2025/06/24
/// @description 手机联系人列表UI数据
/// @param workerId 工友id
/// @param name 工友姓名
/// @param tel 工友手机号
/// @param avatar 工友头像
/// @param identity 工友名片是否授权
/// @param namePy 工友姓名拼音首字母
class WorkerCardItemUIState {
  int workerId;
  String name;
  String shortName;
  String tel;
  String namePy;
  String? avatar;
  bool? isIdentify;
  String? occupationName;

  WorkerCardItemUIState({
    required this.workerId,
    required this.name,
    required this.tel,
    required this.namePy,
    required this.shortName,
    this.avatar,
    this.isIdentify,
    this.occupationName,
  });

  @override
  String toString() {
    return 'WorkerCardItemUIState{workerId: $workerId, name: $name, tel: $tel, avatar: $avatar, isIdentify: $isIdentify, namePy: $namePy, occupationName: $occupationName}';
  }
}

/// 分组列表数据
class WorkerCardSections {
  String? title;
  WorkerCardItemUIState? item;

  WorkerCardSections({
    this.title,
    this.item,
  });

  @override
  String toString() {
    return 'WorkerCardSections{title: $title, items: ${item.toString()}';
  }
}
