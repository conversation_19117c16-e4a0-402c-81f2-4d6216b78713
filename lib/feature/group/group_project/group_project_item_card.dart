import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_project/repo/model/net_model_group_biz_model.dart';
import 'package:gdjg_pure_flutter/feature/group/common_params_model/worker_model.dart';
import 'package:gdjg_pure_flutter/feature/group/group_pro_calendar/entity/group_pro_calendar_props.dart';
import 'package:gdjg_pure_flutter/init_module/init_route.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/utils/system_util/date_util.dart';

class GroupProjectItemCard extends StatelessWidget {
  final NetModelGroupABizModel item;

  const GroupProjectItemCard({
    super.key,
    required this.item,
  });

  void _onTap() {
    var params = GroupProCalendarProps(
        workNoteId: item.workNoteId.toString(),
        workNoteName: item.name,
        deptId: item.deptId,
        isSelfCreated: item.isSelfCreated,
        isAgent: item.isAgent,
        indentity: item.identity,
        workers: [WorkerModel(workerId: item.workerId, workerName: '')]);
    YPRoute.openPage(RouteNameCollection.groupProCalendar, params: params);
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: _onTap,
        borderRadius: BorderRadius.circular(8.r),
        child: Container(
          margin: EdgeInsets.only(bottom: 12.h),
          padding:
              EdgeInsets.only(left: 12.w, right: 12.w, top: 10.h, bottom: 8.h),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8.r),
          ),
          child: Column(
            children: [
              // 项目名称和日期
              Row(
                children: [
                  Expanded(
                    child: Text(
                      item.name,
                      style: TextStyle(
                        fontSize: 22.sp,
                        fontWeight: FontWeight.w600,
                        color: const Color(0xFF333333),
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  Text(
                    item.lastOperationDate.isNotEmpty
                        ? DateUtil.formatRelativeDate(item.lastOperationDate)
                        : '未知',
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: const Color(0xFF999999),
                    ),
                  ),
                ],
              ),

              SizedBox(height: 7.h),

              Row(
                children: [
                  Text(
                    '班组人数：',
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: const Color(0xD9000000),
                    ),
                  ),
                  Text(
                    '${item.workerNum.toInt()}人',
                    style: TextStyle(
                        fontSize: 20.sp,
                        color: const Color(0xFF8A8A99),
                        letterSpacing: -1.5),
                  ),
                  const Spacer(),
                  Icon(
                    Icons.arrow_forward_ios,
                    size: 16.w,
                    color: const Color(0xFF999999),
                  ),
                ],
              ),

              SizedBox(height: 8.h),

              // 分割线
              Container(
                height: 0.5.h,
                color: Colors.grey[300],
              ),

              SizedBox(height: 6.h),

              // 设置
              Row(
                children: [
                  GestureDetector(
                    onTap: null,
                    child: Icon(
                      Icons.settings,
                      size: 21.w,
                      color: Colors.grey[600],
                    ),
                  ),

                  const Spacer(),

                  GestureDetector(
                    onTap: null,
                    child: Container(
                      padding:
                          EdgeInsets.symmetric(horizontal: 10.w, vertical: 4.h),
                      decoration: BoxDecoration(
                        border: Border.all(color: const Color(0xFFFF9500)),
                        borderRadius: BorderRadius.circular(4.r),
                      ),
                      child: Text(
                        '借支/结算',
                        style: TextStyle(
                          fontSize: 14.sp,
                          color: const Color(0xFFFF9500),
                        ),
                      ),
                    ),
                  ),

                  SizedBox(width: 8.w),

                  // 记工按钮
                  GestureDetector(
                    onTap: null,
                    child: Container(
                      padding:
                          EdgeInsets.symmetric(horizontal: 18.w, vertical: 4.h),
                      decoration: BoxDecoration(
                        color: const Color(0xFF007AFF),
                        borderRadius: BorderRadius.circular(4.r),
                      ),
                      child: Text(
                        '记工',
                        style: TextStyle(
                          fontSize: 14.sp,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
