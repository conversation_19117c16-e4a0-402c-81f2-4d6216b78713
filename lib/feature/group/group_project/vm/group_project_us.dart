import 'package:gdjg_pure_flutter/data/group_data/group_project/repo/model/net_model_group_biz_model.dart';
import 'package:get/get.dart';

/// 班组项目页面UI状态管理
class GroupProjectUS {
  final _showProList = <NetModelGroupABizModel>[].obs;

  /// 已结清项目数量
  final _ignoredNum = 0.0.obs;

  // Getters
  RxList<NetModelGroupABizModel> get showProList => _showProList;
  RxDouble get ignoredNum => _ignoredNum;

  /// 设置项目列表
  void setShowProList(List<NetModelGroupABizModel> list) {
    _showProList.value = list;
  }

  /// 设置已结清项目数量
  void setIgnoredNum(double num) {
    _ignoredNum.value = num;
  }
}
