import 'package:flutter/material.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_project/repo/group_project_repo.dart';
import 'package:gdjg_pure_flutter/data/worker_data/worker_project_data/ds/param/dept_create_dept_req_entity.dart';
import 'package:gdjg_pure_flutter/data/worker_data/worker_project_data/repo/worker_project_repo.dart';
import 'package:gdjg_pure_flutter/feature/group/group_project/vm/group_project_us.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/toast_util.dart';

/// 班组项目页面ViewModel
class GroupProjectVM {
  GroupProjectUS us = GroupProjectUS();
  final _groupProjectRepo = GroupProjectRepo();
  final _workerProjectRepo = WorkerProjectRepo();

  /// 查询项目列表
  Future<void> queryProjectList() async {
    final result = await _groupProjectRepo.getDeptListGroup();

    if (result.isOK()) {
      final bizModel = result.getSucData();
      if (bizModel != null) {
        us.setShowProList(bizModel.list);
        // 设置已结清项目数量
        us.setIgnoredNum(bizModel.ignoredNum);
      }
    } else {
      ToastUtil.showToast(result.fail?.errorMsg ?? '获取项目列表失败');
      us.setShowProList([]);
      us.setIgnoredNum(0.0);
    }
  }

  /// 创建新项目
  Future<void> createNewProject(String projectName, {VoidCallback? onSuccess}) async {
    final result = await _workerProjectRepo.createProject(DeptCreateDeptParamModel(
      name: projectName,
      identity: '1',
    ));

    if (result.isOK()) {
      // 刷新项目列表
      await queryProjectList();
      onSuccess?.call();
      ToastUtil.showToast('项目创建成功');
    } else {
      ToastUtil.showToast(result.fail?.errorMsg ?? '创建失败');
    }
  }

  /// 刷新数据
  Future<void> onRefresh() async {
    await queryProjectList();
  }


}
