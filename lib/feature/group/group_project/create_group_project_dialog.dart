import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/toast_util.dart';
import 'package:gdjg_pure_flutter/feature/group/group_project/vm/group_project_vm.dart';

/// 新建班组项目弹窗
class CreateGroupProjectDialog extends StatefulWidget {
  final GroupProjectVM viewModel;

  const CreateGroupProjectDialog({
    super.key,
    required this.viewModel,
  });

  @override
  State<CreateGroupProjectDialog> createState() => _CreateGroupProjectDialogState();
}

class _CreateGroupProjectDialogState extends State<CreateGroupProjectDialog> {
  late TextEditingController _projectNameController;
  late FocusNode _focusNode;

  GroupProjectVM get _viewModel => widget.viewModel;

  @override
  void initState() {
    super.initState();
    _projectNameController = TextEditingController();
    _focusNode = FocusNode();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _focusNode.requestFocus();
    });
  }

  @override
  void dispose() {
    _projectNameController.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 320.w,
      margin: EdgeInsets.symmetric(horizontal: 20.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildTitle(),

          _buildInputSection(),

          _buildButtonSection(),
        ],
      ),
    );
  }

  Widget _buildTitle() {
    return Container(
      padding: EdgeInsets.only(top: 24.h, bottom: 16.h),
      child: Text(
        '新建班组项目',
        style: TextStyle(
          fontSize: 18.sp,
          color: ColorsUtil.black85,
        ),
      ),
    );
  }

  /// 构建输入框区域
  Widget _buildInputSection() {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 20.w),
      padding: EdgeInsets.only(bottom: 24.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '项目名称',
            style: TextStyle(
              fontSize: 14.sp,
              color: ColorsUtil.black85,
              fontWeight: FontWeight.w500,
            ),
          ),

          SizedBox(height: 8.h),

          _buildTextField(),

          SizedBox(height: 24.h),
        ],
      ),
    );
  }

  /// 构建输入框
  Widget _buildTextField() {
    return Container(
      height: 40.h,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(6.r),
        border: Border.all(
          color: Colors.grey[300]!,
          width: 1,
        ),
      ),
      child: TextField(
        controller: _projectNameController,
        focusNode: _focusNode,
        style: TextStyle(
          fontSize: 14.sp,
          color: ColorsUtil.black85,
        ),
        decoration: InputDecoration(
          hintText: '请输入项目名称',
          hintStyle: TextStyle(
            fontSize: 14.sp,
            color: ColorsUtil.hintFontColor,
          ),
          border: InputBorder.none,
          contentPadding: EdgeInsets.symmetric(
            horizontal: 12.w,
            vertical: 10.h,
          ),
        ),
        maxLength: 50,
        buildCounter: (context, {required currentLength, required isFocused, maxLength}) {
          return null;
        },
        onSubmitted: (_) => _handleCreateProject(),
      ),
    );
  }

  /// 构建按钮区域
  Widget _buildButtonSection() {
    return SizedBox(
      height: 44.h,
      child: Row(
        children: [
          // 取消按钮
          Expanded(
            child: _buildCancelButton(),
          ),

          // 创建按钮
          Expanded(
            child: _buildCreateButton(),
          ),
        ],
      ),
    );
  }

  /// 取消按钮
  Widget _buildCancelButton() {
    return GestureDetector(
      onTap: () {
        YPRoute.closeDialog();
      },
      child: Container(
        height:44.h,
        decoration: BoxDecoration(
          color: Colors.white,
          border: Border(
            top: BorderSide(
              color: Colors.grey[300]!,
              width: 1,
            ),
            right: BorderSide(
              color: Colors.grey[300]!,
              width: 0.5,
            ),
          ),
          borderRadius: BorderRadius.only(
            bottomLeft: Radius.circular(12.r),
          ),
        ),
        child: Center(
          child: Text(
            '取消',
            style: TextStyle(
              fontSize: 14.sp,
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ),
    );
  }

  /// 创建按钮
  Widget _buildCreateButton() {
    return GestureDetector(
      onTap: _handleCreateProject,
      child: Container(
        height: 44.h,
        decoration: BoxDecoration(
          color: Colors.white,
          border: Border(
            top: BorderSide(
              color: Colors.grey[300]!,
              width: 1,
            ),
            left: BorderSide(
              color: Colors.grey[300]!,
              width: 0.5,
            ),
          ),
          borderRadius: BorderRadius.only(
            bottomRight: Radius.circular(12.r),
          ),
        ),
        child: Center(
          child: Text(
            '创建',
            style: TextStyle(
              fontSize: 14.sp,
              color: ColorsUtil.primaryColor,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ),
    );
  }

  /// 创建项目
  void _handleCreateProject() {
    final projectName = _projectNameController.text.trim();

    if (projectName.isEmpty) {
      ToastUtil.showToast('请输入项目名称');
      return;
    }

    if (projectName.length < 2) {
      ToastUtil.showToast('项目名称至少需要2个字符');
      return;
    }

    _viewModel.createNewProject(projectName, onSuccess: () {
      YPRoute.closeDialog();
    });
  }
}
