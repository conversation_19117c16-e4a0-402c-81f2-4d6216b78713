import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/feature/worker/worker_setting/entity/worker_setting_props.dart';
import 'package:gdjg_pure_flutter/init_module/init_route.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/feature/group/common_page/select_type_page/entity/select_type_props.dart';
import 'package:gdjg_pure_flutter/feature/group/group_pro_calendar/entity/group_pro_calendar_props.dart';
import 'package:gdjg_pure_flutter/init_module/init_route.dart';
import 'package:gdjg_pure_flutter/model/RecordType.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/widget/label/text_label.dart';

class GroupProjectPage extends StatelessWidget {
  const GroupProjectPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      color: const Color(0xFFF5F5F5),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Text(
              '项目',
              style: TextStyle(
                fontSize: 16.sp,
                color: const Color(0xFF222222),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                // 可选：构建页面参数
                final SelectTypeProps props = SelectTypeProps(
                    isShowExpense: true,
                    recordTypeList: [RwaRecordType.workDays]);
                YPRoute.openPage(RouteNameCollection.selectRecordType,
                        params: props)
                    ?.then((res) {
                  if (res != null) {}
                });
              },
              child: Text(
                '类型选择',
                style: TextStyle(
                  fontSize: 16.sp,
                  color: const Color(0xFF222222),
                ),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                var params = GroupProCalendarProps(
                  workNoteId: "781193",
                  workNoteName: '账本名称',
                );
                YPRoute.openPage(RouteNameCollection.groupProCalendar,
                        params: params)
                    ?.then((res) {
                  if (res != null) {}
                });
              },
              child: Text(
                '班组大日历',
                style: TextStyle(
                  fontSize: 16.sp,
                  color: const Color(0xFF222222),
                ),
              ),
            ),
            ElevatedButton(
                onPressed: () {
                  YPRoute.openPage(RouteNameCollection.inviteWorker);
                },
                child: Text('跳转到微信邀请页')),

            // 测试不同source的工友设置页跳转
            ElevatedButton(
                onPressed: () {
                  _navigateToWorkerSetting(WorkerSettingSource.projectWorker);
                },
                child: Text('项目在场工友：跳转到工友设置页')),
            ElevatedButton(
                onPressed: () {
                  _navigateToWorkerSetting(WorkerSettingSource.defaultPage);
                },
                child: Text('默认页面：跳转到工友设置页')),
            ElevatedButton(
                onPressed: () {
                  YPRoute.openPage(RouteNameCollection.workerResume);
                },
                child: Text('跳转到工友名片页')),
            ElevatedButton(
                onPressed: () {
                  YPRoute.openPage(RouteNameCollection.workerSelector);
                },
                child: Text('跳转到选择工友页')),
            ElevatedButton(
                onPressed: () {
                  YPRoute.openPage(RouteNameCollection.phoneContact);
                },
                child: Text('跳转到手机联系人页')),
            TextLabel(
              label: '全选',
            ),
          ],
        ),
      ),
    );
  }

  /// 跳转到工友设置页，传递不同的source参数
  void _navigateToWorkerSetting(WorkerSettingSource source) {
    // 创建不同的参数配置
    WorkerSettingProps props;

    switch (source) {
      case WorkerSettingSource.projectWorker:
        props = WorkerSettingProps(
          data: WorkerSettingPropsEntity(
            source: source,
            projectName: '恒大天府文化旅游城2期', // 项目名称
            workerId: 3174249,
            isSelf: false,
          ),
        );
        break;
      default:
        props = WorkerSettingProps(
          data: WorkerSettingPropsEntity(
            source: source,
            workerId: 3174249,
            isSelf: false,
          ),
        );
        break;
    }

    // 跳转到工友设置页，传递参数
    YPRoute.openPage(RouteNameCollection.workerSetting, params: props);
  }
}
