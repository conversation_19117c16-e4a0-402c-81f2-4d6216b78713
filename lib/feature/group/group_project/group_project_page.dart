import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:gdjg_pure_flutter/feature/group/group_project/vm/group_project_vm.dart';
import 'package:gdjg_pure_flutter/feature/group/group_project/create_group_project_dialog.dart';
import 'package:gdjg_pure_flutter/feature/group/group_project/group_project_item_card.dart';
import 'package:gdjg_pure_flutter/feature/group/group_record_work/entity/group_record_work_props.dart';
import 'package:gdjg_pure_flutter/feature/group/worker_manager/worker_setting/entity/worker_setting_props.dart';
import 'package:gdjg_pure_flutter/feature/group/worker_manager/worker_tel_modify/entity/modify_worker_tel_props.dart';
import 'package:gdjg_pure_flutter/generated/assets.dart';
import 'package:gdjg_pure_flutter/feature/group/common_page/select_type_page/entity/select_type_props.dart';
import 'package:gdjg_pure_flutter/init_module/init_route.dart';
import 'package:gdjg_pure_flutter/model/RecordType.dart';
import 'package:gdjg_pure_flutter/utils/route_util/base_page/base_ful_page.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/toast_util.dart';
import 'package:gdjg_pure_flutter/widget/label/text_label.dart';
import 'package:gdjg_pure_flutter/widget/network_carousel_widget.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

/// 班组项目页面
class GroupProjectPage extends BaseFulPage {
  const GroupProjectPage({super.key}) : super(appBar: null);

  @override
  State<GroupProjectPage> createState() => _GroupProjectPageState();
}

class _GroupProjectPageState extends BaseFulPageState<GroupProjectPage> {
  late GroupProjectVM _viewModel;
  late RefreshController _refreshController;

  @override
  void onPageCreate() {
    super.onPageCreate();
    _viewModel = GroupProjectVM();
    _refreshController = RefreshController(initialRefresh: false);
    _viewModel.queryProjectList();
  }

  @override
  void onPageDestroy() {
    _refreshController.dispose();
    super.onPageDestroy();
  }

  @override
  Widget yBuild(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      body: SafeArea(
        child: Column(
          children: [
            // 轮播图
            _buildBannerSection(),

            // 可滚动内容区域
            Expanded(
              child: SmartRefresher(
                controller: _refreshController,
                enablePullDown: true,
                enablePullUp: false,
                onRefresh: _onRefresh,
                child: CustomScrollView(
                  physics: const AlwaysScrollableScrollPhysics(),
                  slivers: [
                    // 新建项目按钮
                    SliverToBoxAdapter(
                      child: _buildCreateProjectSection(),
                    ),

                    // 项目列表
                    _buildProjectListSliver(),

                    // 测试按钮
                    SliverToBoxAdapter(
                      child: _buildTestButtonsSection(),
                    ),

                    // 底部预留空间
                    SliverToBoxAdapter(
                      child: SizedBox(height: 20.h),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 刷新回调
  Future<void> _onRefresh() async {
    await _viewModel.onRefresh();
    _refreshController.refreshCompleted();
  }

  /// 轮播图
  Widget _buildBannerSection() {
    return Container(
      color: Colors.white,
      padding: EdgeInsets.only(top: 14.h, left: 16.w, right: 16.w, bottom: 2.h),
      child: NetworkCarouselWidget(
        code: 'JGJZ_HOME_GROUP_BANER',
        height: 48.h,
      ),
    );
  }

  /// 新建项目按钮行
  Widget _buildCreateProjectSection() {
    return Container(
      padding:
          EdgeInsets.only(left: 16.w, top: 10.h, bottom: 10.h, right: 16.w),
      child: Row(
        children: [
          _buildNewProjectButton(),
          const Spacer(),
          Obx(() => _buildIgnoredProjectsEntry()),
        ],
      ),
    );
  }

  /// 新建项目按钮
  Widget _buildNewProjectButton() {
    return GestureDetector(
      onTap: _showCreateProjectDialog,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(5.w),
          border: Border.all(
            color: ColorsUtil.primaryColor,
            width: 1,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.add,
              color: ColorsUtil.primaryColor,
              size: 20.w,
            ),
            SizedBox(width: 4.w),
            Text(
              '新建项目',
              style: TextStyle(
                color: ColorsUtil.primaryColor,
                fontSize: 14.w,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 创建项目弹窗
  void _showCreateProjectDialog() {
    SmartDialog.show(
      builder: (context) => CreateGroupProjectDialog(
        viewModel: _viewModel,
      ),
    );
  }

  /// 已结清入口
  Widget _buildIgnoredProjectsEntry() {
    final ignoredNum = _viewModel.us.ignoredNum.value;

    return GestureDetector(
      onTap: null,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            '已结清（${ignoredNum.toInt()}）',
            style: TextStyle(
              fontSize: 14.sp,
              color: const Color(0xFF7B7D81),
              fontWeight: FontWeight.w500,
              height: 1.0,
            ),
          ),
          SizedBox(width: 4.w),
          Icon(
            Icons.arrow_forward_ios,
            size: 16.w,
            color: const Color(0xFF7B7D81),
          ),
        ],
      ),
    );
  }

  /// 项目列表
  Widget _buildProjectListSliver() {
    return Obx(() {
      // 无数据时显示空状态
      if (_viewModel.us.showProList.isEmpty) {
        return SliverToBoxAdapter(
          child: _buildEmptyState(),
        );
      }

      // 有数据时显示项目列表
      return SliverPadding(
        padding: EdgeInsets.symmetric(horizontal: 16.w),
        sliver: SliverList(
          delegate: SliverChildBuilderDelegate(
            (context, index) {
              final project = _viewModel.us.showProList[index];
              return GroupProjectItemCard(item: project);
            },
            childCount: _viewModel.us.showProList.length,
          ),
        ),
      );
    });
  }

  /// 构建空状态
  Widget _buildEmptyState() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.only(top: 70.h, bottom: 50.h),
      child: Column(
        children: [
          Image.asset(
            Assets.commonIconEmptyTeamProject,
            width: 120.w,
            height: 120.w,
            fit: BoxFit.contain,
          ),
          Text(
            '暂未创建项目',
            style: TextStyle(
              fontSize: 14.sp,
              color: const Color(0xFF8A8A99),
            ),
          ),
        ],
      ),
    );
  }

  /// 测试按钮
  Widget _buildTestButtonsSection() {
    return Container(
      color: const Color(0xFFFFFFFF),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 24),
      child: Center(
        child: Column(
          spacing: 16,
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Text(
              '项目',
              style: TextStyle(
                fontSize: 16.sp,
                color: const Color(0xFF222222),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                // 可选：构建页面参数
                final SelectTypeProps props = SelectTypeProps(
                    isShowExpense: true,
                    recordTypeList: [RwaRecordType.workDays]);
                YPRoute.openPage(RouteNameCollection.selectRecordType,
                        params: props)
                    ?.then((res) {
                  if (res != null) {}
                });
              },
              child: Text(
                '类型选择',
                style: TextStyle(
                  fontSize: 16.sp,
                  color: const Color(0xFF222222),
                ),
              ),
            ),
            ElevatedButton(
                onPressed: () {
                  YPRoute.openPage(RouteNameCollection.inviteWorker);
                },
                child: Text('跳转到微信邀请页')),

            // 测试不同source的工友设置页跳转
            ElevatedButton(
                onPressed: () {
                  _navigateToWorkerSetting(WorkerSettingSource.projectWorker);
                },
                child: Text('项目在场工友：跳转到工友设置页')),
            ElevatedButton(
                onPressed: () {
                  _navigateToWorkerSetting(WorkerSettingSource.defaultPage);
                },
                child: Text('默认页面：跳转到工友设置页')),
            ElevatedButton(
                onPressed: () {
                  YPRoute.openPage(RouteNameCollection.workerResume);
                },
                child: Text('跳转到工友名片页')),
            ElevatedButton(
                onPressed: () {
                  YPRoute.openPage(RouteNameCollection.workerSelector);
                },
                child: Text('跳转到选择工友页')),
            ElevatedButton(
                onPressed: () {
                  YPRoute.openPage(RouteNameCollection.modifyWorkerTel,
                      params: ModifyWorkerTelProps(
                        workerId: 1,
                        workNoteId: '1',
                        name: '张三',
                        tel: '13998184246',
                        deptId: '1',
                      ));
                },
                child: Text('跳转到修改手机号')),
            ElevatedButton(
                onPressed: () {
                  YPRoute.openPage(RouteNameCollection.phoneContact);
                },
                child: Text('跳转到手机联系人页')),
            ElevatedButton(
                onPressed: () {
                  YPRoute.openPage(RouteNameCollection.groupRecordWork,
                      params: GroupRecordWorkProps(
                          from: GroupRecordWorkFrom.projectList,
                          deptId: 41679));
                },
                child: Text('跳转到班组记工页')),
            TextLabel(
              id: '001',
              label: '在建项目',
              labelType: TextLabelType.blue,
              isBold: true,
              onCloseTap: (id) => {ToastUtil.showToast('关闭标签$id')},
              onLabelTap: (id) => {ToastUtil.showToast('点击标签$id')},
            ),
          ],
        ),
      ),
    );
  }

  /// 跳转到工友设置页，传递不同的source参数
  void _navigateToWorkerSetting(WorkerSettingSource source) {
    // 创建不同的参数配置
    WorkerSettingProps props;

    switch (source) {
      case WorkerSettingSource.projectWorker:
        props = WorkerSettingProps(
          source: source,
          workNoteName: '恒大天府文化旅游城2期', // 项目名称
          workerId: 3174276,
          isSelf: false,
        );
        break;
      default:
        props = WorkerSettingProps(
          source: source,
          workerId: 3174276,
          isSelf: false,
        );
        break;
    }

    // 跳转到工友设置页，传递参数
    YPRoute.openPage(RouteNameCollection.workerSetting, params: props);
  }
}
