import "package:gdjg_pure_flutter/data/group_data/group_un_liauidated/ds/model/param/group_business_get_group_business_count_param_model.dart";
import "package:gdjg_pure_flutter/data/group_data/group_un_liauidated/ds/model/param/group_project_get_group_worker_settle_param_model.dart";
import "package:gdjg_pure_flutter/data/group_data/group_un_liauidated/ds/model/param/group_project_update_worker_settle_param_model.dart";
import "package:gdjg_pure_flutter/data/group_data/group_un_liauidated/repo/model/group_business_get_group_business_count_biz_model.dart";
import "package:gdjg_pure_flutter/data/group_data/group_un_liauidated/repo/model/group_project_get_group_worker_settle_biz_model.dart";
import "package:gdjg_pure_flutter/data/group_data/group_un_liauidated/repo/project_settle_repo.dart";
import "package:gdjg_pure_flutter/feature/group/common_params_model/worker_model.dart";
import "package:gdjg_pure_flutter/feature/group/dialog/wage_rule_setting/WageRulesSettingDialog.dart";
import "package:gdjg_pure_flutter/feature/group/dialog/wage_rule_setting/entity/wage_rules_props.dart";
import "package:gdjg_pure_flutter/feature/group/group_liquidated_detail/entity/group_liquidated_detail_props.dart";
import "package:gdjg_pure_flutter/feature/group/group_liquidated_detail/vm/protocol/group_liquidated_detail_ui_state.dart";
import "package:gdjg_pure_flutter/feature/group/group_liquidated_detail/vm/protocol/group_liquidated_group_business_count_ui_state.dart";
import "package:gdjg_pure_flutter/feature/group/group_pro_bill/entity/group_pro_bill_props.dart";
import "package:gdjg_pure_flutter/feature/group/group_settlement/entity/group_settlement_props.dart";
import "package:gdjg_pure_flutter/init_module/init_route.dart";
import "package:gdjg_pure_flutter/model/RecordNoteType.dart";
import "package:gdjg_pure_flutter/model/RecordType.dart";
import "package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart";
import "package:gdjg_pure_flutter/utils/system_util/string_util.dart";
import "package:gdjg_pure_flutter/utils/system_util/yprint.dart";
import "package:gdjg_pure_flutter/utils/ui_util/common_dialog.dart";
import "package:gdjg_pure_flutter/utils/ui_util/toast_util.dart";
import "package:intl/intl.dart";

/// @date 2025/07/01
/// @description GroupUnLiquidated页ViewModel
/// 1. 处理数据的加载状态，如loading和错误视图等
/// 2. 观测UIRep中的业务实体转为UIState
/// 3. 接受来自view的意图并分发事件进行处理（暂无）
/// 4. 对View层暴露事件（操作视图和处理一些与系统权限交互等场景）
class GroupLiquidatedDetailViewmodel {
  final uiState = GroupLiquidatedDetailUiState();
  final _repo = ProjectSettleRepo();

  GroupLiquidatedDetailProps? props;

  GroupProjectGetGroupWorkerSettleBizModel? settleBizModel;

  initParam(GroupLiquidatedDetailProps? props) {
    this.props = props;
  }

  init() {
    fetchData();
  }

  ///是否显示未结底部ui
  bool isShowUnSettledFooterUI() {
    var identity = 2;
    return isUnLiquidated() && identity == 2 && props?.isAgent == 0;
  }

  ///是否是未结 true是未结，false是已结
  bool isUnLiquidated() {
    return props?.isUnLiquidated ?? false;
  }

  /// 初始化数据同步，如果不需要loading，则在这里去掉
  void fetchData() async {
    try {
      final param = GroupProjectGetGroupWorkerSettleParamModel(
          workNote: getWorkNote(), workerId: getWorkerId());
      // 同步获取数据
      var result = await _repo.getGroupProjectWorkerSettle(param);
      if (result.isOK()) {
        final netModel = result.getSucData();
        settleBizModel = netModel;
        convertStatisticsEntityToUIState(netModel);
        getGroupBusinessCount(netModel);
      }
    } catch (e) {
      yprint(e.toString());
    }
  }

  ///修改班组 工人 结清状态
  ///[settle]  0 未结清，1已结清
  void updateWorkerSettle(String settle) async {
    try {
      final param = GroupProjectUpdateWorkerSettleParamModel(
          is_settle: settle,
          note_id: getWorkNote(),
          work_note: getWorkNote(),
          worker_id: getWorkerId());
      // 同步获取数据
      var result = await _repo.updateWorkerSettle(param);
      if (result.isOK()) {
        ///设为已结了
        YPRoute.closePage('do_settle');
        if (settle == "0") {
          ToastUtil.showToast('恢复成功');
        }
      }
    } catch (e) {
      yprint(e.toString());
    }
  }

  /// 获取班组统计数据
  void getGroupBusinessCount(
      GroupProjectGetGroupWorkerSettleBizModel? statisticsNetModel) async {
    try {
      final param = GroupBusinessGetGroupBusinessCountParamModel(
          start_time: statisticsNetModel?.startTime,
          end_time: statisticsNetModel?.endTime,
          worker_ids: getWorkerId(),
          work_note: getWorkNote());
      var result = await _repo.getGroupBusinessCount(param);
      if (result.isOK()) {
        final netModel = result.getSucData();
        List<GroupLiquidatedGroupBusinessCountListUiState> list = [];
        // 点工 spotWork
        addList(list, handlerSpotWorkUIState(statisticsNetModel, netModel));
        // 包工 contractor
        addList(list, handlerContractorUIState(statisticsNetModel, netModel));
        // 工量 unit
        addList(list, handlerUnitUIState(netModel));
        // 短工 work_money
        addList(list, handlerWorkMoneyUIState(netModel));
        // 借支 borrow
        addList(list, handlerBorrowUIState(netModel));
        // 结算 wage
        addList(list, handlerWageUIState(netModel));
        // 未结总计
        addList(list, handlerTotalUIState(netModel));
        // 将 list 更新到 UI 层或状态管理中
        uiState.setBillList(list);
      }
    } catch (e) {
      yprint(e.toString());
    }
  }

  ///处理点工ui实体
  GroupLiquidatedGroupBusinessCountListUiState? handlerSpotWorkUIState(
      GroupProjectGetGroupWorkerSettleBizModel? statisticsNetModel,
      GroupBusinessGetGroupBusinessCountBizModel? netModel) {
    if ((netModel?.spotWork?.num ?? 0) > 0) {
      final spotWork = netModel?.spotWork;
      ;
      final workTime = double.tryParse(spotWork?.workTime ?? '0') ?? 0;
      final workTimeHour = double.tryParse(spotWork?.workTimeHour ?? '0') ?? 0;
      String content = '';
      if (workTime > 0 || workTimeHour > 0) {
        content += '上班:';
        if (workTime > 0) {
          content += '${spotWork?.workTime.trimTrailingZeros()}个工';
        }
        if (workTimeHour > 0) {
          if (workTime > 0) content += '+';
          content += '${spotWork?.workTimeHour.trimTrailingZeros()}小时';
        }
      }

      final overtimeWork = double.tryParse(spotWork?.overtimeWork ?? '0') ?? 0;
      final overtime = double.tryParse(spotWork?.overtime ?? '0') ?? 0;
      if (overtimeWork > 0 || overtime > 0) {
        String tempText = '加班:';
        if (overtimeWork > 0) {
          tempText += '${spotWork?.overtimeWork.trimTrailingZeros()}个工';
        }
        if (overtime > 0) {
          if (overtimeWork > 0) tempText += '+';
          tempText += '${spotWork?.overtime.trimTrailingZeros()}小时';
        }

        if (content.isNotEmpty) {
          content += '\n$tempText';
        } else {
          content = tempText;
        }
      }
      String foot = statisticsNetModel?.feeStandardId == "0" ? '未设点工工价' : '';
      return GroupLiquidatedGroupBusinessCountListUiState(
          rwaRecordType: RwaRecordType.workDays,
          name: '点工',
          content: content,
          money: spotWork?.spotWorkFeeMoney.formatDoubleToMoney().toString(),
          foot: foot,
          type: UnliquidatedDetailType.report);
    }
    return null;
  }

  ///处理包工ui实体
  GroupLiquidatedGroupBusinessCountListUiState? handlerContractorUIState(
      GroupProjectGetGroupWorkerSettleBizModel? statisticsNetModel,
      GroupBusinessGetGroupBusinessCountBizModel? netModel) {
    if ((netModel?.contractor?.num ?? 0) > 0) {
      final contractor = netModel?.contractor;

      final contractorWorkTime = contractor?.contractorWorkTime ?? 0.0;
      final contractorWorkTimeHour = contractor?.contractorWorkTimeHour ?? 0.0;

      String content = '';
      if (contractorWorkTime > 0 || contractorWorkTimeHour > 0) {
        content += '上班:';
        if (contractorWorkTime > 0) {
          content += '$contractorWorkTime个工';
        }
        if (contractorWorkTimeHour > 0) {
          if (contractorWorkTime > 0) content += '+';
          content += '$contractorWorkTimeHour小时';
        }
      }

      final contractorOvertime =
          double.tryParse(contractor?.contractorOvertime ?? '0') ?? 0.0;

      if (contractorOvertime > 0) {
        if (content.isNotEmpty) {
          content += '\n加班:$contractorOvertime小时';
        } else {
          content += '加班:$contractorOvertime小时';
        }
      }

      String foot =
          statisticsNetModel?.feeStandardIdContractor == '0' ? '未设包工工价' : '';

      return GroupLiquidatedGroupBusinessCountListUiState(
          rwaRecordType: RwaRecordType.packageWork,
          name: '包工',
          content: content,
          money: contractor?.contractorMoney.formatDoubleToMoney().toString(),
          foot: foot,
          type: UnliquidatedDetailType.report);
    }
    return null;
  }

  /// 处理工量 unit
  GroupLiquidatedGroupBusinessCountListUiState? handlerUnitUIState(
      GroupBusinessGetGroupBusinessCountBizModel? netModel) {
    if ((netModel?.unit?.num ?? 0) > 0 &&
        netModel?.unit?.countUnit.isNotEmpty == true) {
      for (int i = 0; i < (netModel?.unit?.countUnit.length ?? 0);) {
        final countUnit = netModel?.unit?.countUnit[i];
        final name = '工量 ${countUnit?.unitWorkTypeName}';
        final nameSub = '${countUnit?.num.trimTrailingZeros()}笔';
        final moneySub = '总计:${countUnit?.count}${countUnit?.unitWorkTypeUnit}';
        return GroupLiquidatedGroupBusinessCountListUiState(
            rwaRecordType: RwaRecordType.workLoad,
            name: name,
            nameSub: nameSub,
            money: countUnit?.unitMoney.formatStringToMoney(),
            moneySub: moneySub,
            type: UnliquidatedDetailType.report);
      }
    }
    return null;
  }

  ///处理短工ui实体
  GroupLiquidatedGroupBusinessCountListUiState? handlerWorkMoneyUIState(
      GroupBusinessGetGroupBusinessCountBizModel? netModel) {
    if ((netModel?.workMoney?.num ?? 0) > 0) {
      final workMoney = netModel?.workMoney;
      final content = '${workMoney?.num.trimTrailingZeros()}笔';
      return GroupLiquidatedGroupBusinessCountListUiState(
          rwaRecordType: RwaRecordType.dailyWages,
          name: '短工',
          content: content,
          money: workMoney?.workMoney.formatDoubleToMoney().toString(),
          type: UnliquidatedDetailType.report);
    }
    return null;
  }

  ///处理借支ui实体
  GroupLiquidatedGroupBusinessCountListUiState? handlerBorrowUIState(
      GroupBusinessGetGroupBusinessCountBizModel? netModel) {
    if ((netModel?.borrow?.num ?? 0) > 0) {
      final borrow = netModel?.borrow;
      return GroupLiquidatedGroupBusinessCountListUiState(
          rwaRecordType: RwaRecordType.debt,
          name: '借支',
          content: '${borrow?.num.trimTrailingZeros()}笔',
          money: borrow?.borrowCount ?? '',
          type: UnliquidatedDetailType.borrow);
    }
    return null;
  }

  ///处理结算ui实体
  GroupLiquidatedGroupBusinessCountListUiState? handlerWageUIState(
      GroupBusinessGetGroupBusinessCountBizModel? netModel) {
    if ((netModel?.wage?.num ?? 0) > 0) {
      final wage = netModel?.wage;
      return GroupLiquidatedGroupBusinessCountListUiState(
          rwaRecordType: RwaRecordType.wageLast,
          name: '结算',
          content: '${wage?.num.trimTrailingZeros()}笔',
          money: wage?.wageCount ?? '',
          type: UnliquidatedDetailType.borrow);
    }
    return null;
  }

  GroupLiquidatedGroupBusinessCountListUiState? handlerTotalUIState(
      GroupBusinessGetGroupBusinessCountBizModel? netModel) {
    return GroupLiquidatedGroupBusinessCountListUiState(
        money: netModel?.unsettled.formatDoubleToMoney().toString() ?? "");
  }

  void addList(List<GroupLiquidatedGroupBusinessCountListUiState> list,
      GroupLiquidatedGroupBusinessCountListUiState? uiState) {
    if (uiState != null) list.add(uiState);
  }

  /// 转换实体数据转换为UIState
  void convertStatisticsEntityToUIState(
      GroupProjectGetGroupWorkerSettleBizModel? netModel) {
    final startTime = formatDateString(netModel?.startTime);
    final endTime = formatDateString(netModel?.endTime);
    uiState.setWorkerName(netModel?.workerName ?? "");
    uiState.setShowDate('$startTime-$endTime');
    uiState.setIsDeleted(netModel?.isDeleted == "1");
  }

  ///转换日历为yyyy年mm月dd日
  String formatDateString(String? dateStr, {String defaultIfInvalid = ''}) {
    if (dateStr == null || dateStr.trim().isEmpty) {
      return defaultIfInvalid;
    }
    try {
      DateTime date = DateTime.parse(dateStr);
      return DateFormat('yyyy年MM月dd日').format(date);
    } catch (e) {
      // 可以根据需要记录日志
      yprint("Invalid date format: $dateStr");
      return defaultIfInvalid;
    }
  }

  String getWorkerId() {
    WorkerModel? worker = props?.workers?.firstOrNull;
    return worker?.workerId?.toString() ?? "";
  }

  String getWorkNoteName() {
    return props?.workNoteName ?? "";
  }

  String getWorkNote() {
    return props?.workNote ?? "";
  }

  bool isJoin() {
    return props?.isJoin ?? false;
  }

  double deptId() {
    return props?.deptId ?? 0;
  }

  ///设置点击事件
  void onSetClickTap(GroupLiquidatedGroupBusinessCountListUiState uiState) {
    var wageProps = WageRulesProps(
      title: '点工工价设置',
      workers: props?.workers,
      workNoteId: getWorkNote(),
      businessType: uiState.rwaRecordType,
      recordNoteType: RecordNoteType.group,
    );
    WageRulesSettingDialog.show(props: wageProps, onSelected: (feeInfo) {
      fetchData();
    });
  }

  ///设为未结点击事件
  void onMarkerNotTap() {
    updateWorkerSettle("0");
  }

  ///标记已结清点击事件
  void onMarkerRendTap() {
    showCommonDialog(CommonDialogConfig(
      title: '确认设为已结清吗',
      content: '设为已结清后，此工友将从项目退场',
      positive: '确认',
      negative: '取消',
      onPositive: () {
        updateWorkerSettle("1");
      },
    ));
  }

  ///列表点击事件
  void onItemClickTap(GroupLiquidatedGroupBusinessCountListUiState uiState) {
    _toGroupProBillClick(uiState.rwaRecordType);
  }

  ///对工点击事件
  void onJumpConfrontationTap() {
    _toGroupProBillClick(null);
  }

  ///去对账页面
  void _toGroupProBillClick(RwaRecordType? rwaRecordType) {
    GroupProBillProps params = GroupProBillProps();
    params.workNoteId = getWorkNote();
    params.workNoteName = getWorkNoteName();
    params.deptId = deptId();
    params.startTime = DateTime.parse(settleBizModel?.startTime ?? "");
    params.endTime = DateTime.parse(settleBizModel?.endTime ?? "");
    params.isJoin = isJoin();
    if (rwaRecordType != null) {
      params.businessType = [rwaRecordType];
    }
    params.workers = [
      WorkerModel(
          workerId: getWorkerId().toDouble,
          workerName: settleBizModel?.workerName ?? "")
    ];
    yprint(params.toString());
    YPRoute.openPage(RouteNameCollection.groupProBill, params: params);
  }

  /// 记结算点击事件
  void onRecordEndTap() {
    var params = GroupSettlementProps()
      ..workNoteId = getWorkNote()
      ..workNoteName = props?.workNoteName ?? ""
      ..endTime = DateTime.parse(settleBizModel?.endTime ?? "")
      ..money = settleBizModel?.notSettleMoney.toString() ?? ""
      ..workers = [
        WorkerModel(
            workerId: getWorkerId().toDouble,
            workerName: settleBizModel?.workerName ?? "")
      ];
    YPRoute.openPage(RouteNameCollection.groupSettlement, params: params);
  }
}
