import 'package:gdjg_pure_flutter/model/RecordType.dart';

/// @date 2025/07/01
/// @description 班组统计
class GroupLiquidatedGroupBusinessCountListUiState {
  /// 记账id
  RwaRecordType? rwaRecordType;

  /// 类型
  String? name;

  /// 名称
  String? nameSub;

  /// 中间内容
  String? content;

  /// 价格
  String? money;

  /// 价格下面的描述
  String? moneySub;

  /// 底部提示
  String? foot;

  /// 结算类型
  UnliquidatedDetailType? type;

  GroupLiquidatedGroupBusinessCountListUiState(
      {this.rwaRecordType,
      this.name,
      this.nameSub,
      this.content,
      this.money,
      this.moneySub,
      this.foot,
      this.type});
}
