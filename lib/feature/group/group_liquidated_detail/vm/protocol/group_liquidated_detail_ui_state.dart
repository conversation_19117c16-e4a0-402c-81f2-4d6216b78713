import 'package:gdjg_pure_flutter/feature/group/group_liquidated_detail/vm/protocol/group_liquidated_group_business_count_ui_state.dart';
import 'package:get/get.dart';

/// @date 2025/07/01
/// @description 班组长未结UI状态
class GroupLiquidatedDetailUiState {
  /// 工人姓名
  final _workerName = ''.obs;

  /// 工人id
  final _workerId = ''.obs;

  /// 日期
  final _showDate = ''.obs;

  /// 是否退场
  final _isDeleted = false.obs;


  /// 记账数据
  final _billList = <GroupLiquidatedGroupBusinessCountListUiState>[].obs;

  /// 设置工人姓名
  void setWorkerName(String workerName) {
    _workerName.value = workerName;
  }

  /// 设置工人id
  void setWorkerId(String workerId) {
    _workerId.value = workerId;
  }

  /// 设置工人id
  void setShowDate(String showDate) {
    _showDate.value = showDate;
  }

  /// 是否退场
  void setIsDeleted(bool isDeleted) {
    _isDeleted.value = isDeleted;
  }


  /// 设置记账数据
  void setBillList(
      List<GroupLiquidatedGroupBusinessCountListUiState>? billList) {
    if (billList != null) {
      _billList.value = billList;
    }
  }

  String get workerName => _workerName.value;

  String get workerId => _workerId.value;

  String get showDate => _showDate.value;

  bool get isDeleted => _isDeleted.value;

  List<GroupLiquidatedGroupBusinessCountListUiState> get billList =>
      _billList.value;
}
