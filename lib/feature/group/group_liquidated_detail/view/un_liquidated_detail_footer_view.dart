import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';
import 'package:gdjg_pure_flutter/widget/divider_view.dart';

import '../vm/group_liquidated_detail_viewmodel.dart';

///未结清底部组件
class UnLiquidatedDetailFooter extends StatelessWidget {
  ///viewModel
  final GroupLiquidatedDetailViewmodel viewModel;

  const UnLiquidatedDetailFooter({super.key, required this.viewModel});

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.white,
      child: Column(
        children: [
          // 上边线
          DividerView(color: ColorsUtil.divideLineColor),
          // 内容区域
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 10),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                // 左侧按钮
                leftWidget(context),
                // 右侧按钮
                rightWidget(context)
              ],
            ),
          ),
        ],
      ),
    );
  }

  ///设为已结清按钮
  Widget leftWidget(BuildContext context) {
    return GestureDetector(
      onTap: viewModel.onMarkerRendTap,
      child: Container(
        width: MediaQuery.of(context).size.width * 0.4,
        padding: const EdgeInsets.symmetric(vertical: 10),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(4),
          border: Border.all(color: Colors.red), // 替换为 c.red
        ),
        alignment: Alignment.center,
        child: Text(
          '设为已结清',
          style: TextStyle(
            fontSize: 15.sp,
            color: Colors.red, // 替换为 c.red
          ),
        ),
      ),
    );
  }

  ///记结算按钮
  Widget rightWidget(BuildContext context) {
    return GestureDetector(
      onTap: viewModel.onRecordEndTap,
      child: Container(
        width: MediaQuery.of(context).size.width * 0.4,
        padding: const EdgeInsets.symmetric(vertical: 10),
        decoration: BoxDecoration(
          color: Colors.blue, // 替换为 c.primary
          borderRadius: BorderRadius.circular(4),
        ),
        alignment: Alignment.center,
        child: Text(
          '记结算',
          style: TextStyle(
            fontSize: 15.sp,
            color: Colors.white, // 替换为 c.white
          ),
        ),
      ),
    );
  }
}
