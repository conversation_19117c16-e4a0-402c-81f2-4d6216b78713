import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/feature/group/group_statistics/view/empty_view.dart';
import 'package:gdjg_pure_flutter/utils/system_util/font_util.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';
import '../vm/protocol/group_liquidated_group_business_count_ui_state.dart';

///未结工资组件
class ListItemUnLiquidatedDetailWages extends StatelessWidget {
  ///是否有账单数据
  final bool haveBillList;
  final GroupLiquidatedGroupBusinessCountListUiState? uiState;

  const ListItemUnLiquidatedDetailWages(
      {super.key, required this.haveBillList, required this.uiState});

  @override
  Widget build(BuildContext context) {
    if (haveBillList) {
      return const EmptyView(subtitle: '暂无数据'); // 假设你已经定义了 Empty 组件
    }
    return Container(
      width: double.infinity, // 让 Column 占满整行
      margin: const EdgeInsets.only(right: 8, top: 25),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.end, // 子项靠右
        children: [
          // 顶部内容区域
          Row(
            mainAxisSize: MainAxisSize.min, // 让 Row 尺寸不要占满整行
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Text(
                '未结工资：',
                style: TextStyle(
                  fontSize: 16.sp,
                  color: const Color(0xFF323233),
                ),
              ),
              Text(
                uiState?.money ?? "",
                style: TextStyle(
                  fontSize: 40.sp,
                  color: ColorsUtil.primaryColor,
                  fontFamily: FontUtil.fontCondMedium,
                ),
              )
            ],
          ),
          // 提示文本
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 10),
            child: Text(
              '未结工资=工资-借支-结算',
              style: TextStyle(
                fontSize: 13.sp,
                color: const Color(0x59000000), // 不透明一点点的黑色
              ),
            ),
          ),
        ],
      ),
    );
  }
}
