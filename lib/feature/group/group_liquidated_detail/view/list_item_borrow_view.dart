import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/feature/group/group_liquidated_detail/vm/protocol/group_liquidated_group_business_count_ui_state.dart';
import 'package:gdjg_pure_flutter/iconfont/iconfont.dart';
import 'package:gdjg_pure_flutter/utils/system_util/font_util.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';
import '../vm/group_liquidated_detail_viewmodel.dart';

///借支List组件
class ListItemBorrow extends StatelessWidget {
  final GroupLiquidatedGroupBusinessCountListUiState uiState;

  ///viewModel
  final GroupLiquidatedDetailViewmodel viewModel;

  const ListItemBorrow(
      {super.key, required this.uiState, required this.viewModel});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        viewModel.onItemClickTap(uiState);
      },
      child: Container(
        margin: const EdgeInsets.only(bottom: 5, left: 8, right: 8),
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
        decoration: BoxDecoration(
          color: Color(0XFFFFF4E5),
          borderRadius: BorderRadius.circular(4),
        ),
        child: Row(
          children: [leftViewWidget(), rightViewWidget()],
        ),
      ),
    );
  }

  ///左边组件
  Widget leftViewWidget() {
    return Expanded(
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Text(
            uiState.name ?? "",
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Color(0xFF323233),
            ),
          ),
          const SizedBox(width: 8),
          Text(
            uiState.content ?? '',
            style: TextStyle(
              fontSize: 13.sp,
              color: ColorsUtil.black85,
            ),
          ),
        ],
      ),
    );
  }

  ///右边组件
  Widget rightViewWidget() {
    return Row(
      children: [
        Text(
          uiState.money ?? '',
          style: const TextStyle(
            fontSize: 22,
            color: Color(0XFFFF9800),
            fontFamily: FontUtil.fontCondMedium,
          ),
        ),
        IconFont(
          IconNames.saasArrowRight,
          size: 15,
          color: '#333333',
        ),
      ],
    );
  }
}
