import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/feature/group/group_liquidated_detail/vm/group_liquidated_detail_viewmodel.dart';
import 'package:gdjg_pure_flutter/iconfont/iconfont.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_obx_widget.dart';

///头部信息
class HeaderView extends StatelessWidget {
  ///viewModel
  final GroupLiquidatedDetailViewmodel viewModel;

  const HeaderView({super.key, required this.viewModel});

  @override
  Widget build(BuildContext context) {
    return headerWidget();
  }

  ///头部信息
  Widget headerWidget() {
    return GestureDetector(
      onTap: () {},
      child: Container(
          //设置这个是为了点击事件
          color: Colors.white,
          child: headerItemWidget()),
    );
  }

  ///头部item信息
  Widget headerItemWidget() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Row(children: [
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Obx(() => Text(viewModel.uiState.workerName,
                    style:
                        TextStyle(color: ColorsUtil.black85, fontSize: 18.sp))),
                SizedBox(width: 5),
                Obx(() => Visibility(
                      visible: viewModel.uiState.isDeleted,
                      child: Container(
                        height: 22.5,
                        padding: const EdgeInsets.symmetric(horizontal: 5),
                        decoration: BoxDecoration(
                            color: ColorsUtil.f5f5f5,
                            borderRadius: BorderRadius.circular(11)),
                        child: Center(
                          child: Text("已退场",
                              style: TextStyle(
                                  color: ColorsUtil.black45, fontSize: 15.sp)),
                        ),
                      ),
                    )),
              ],
            ),
            SizedBox(height: 5),
            Obx(() => Text(viewModel.uiState.showDate,
                style: TextStyle(color: Color(0XFF323233), fontSize: 16.sp))),
          ],
        ),
        SizedBox(width: 5),
        Spacer(),
        GestureDetector(
          onTap: () {
            viewModel.onJumpConfrontationTap();
          },
          child: Text("对工",
              style: TextStyle(color: ColorsUtil.black65, fontSize: 17.sp)),
        ),
        IconFont(
          IconNames.saasArrowRight,
          size: 15,
          color: '#8A8A99',
        ),
      ]),
    );
  }
}
