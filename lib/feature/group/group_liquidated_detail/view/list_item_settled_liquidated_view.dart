import 'package:flutter/material.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';
import '../vm/group_liquidated_detail_viewmodel.dart';

///已结清底部组件
class SettledLiquidatedView extends StatelessWidget {
  ///viewModel
  final GroupLiquidatedDetailViewmodel viewModel;

  const SettledLiquidatedView({super.key, required this.viewModel});

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        GestureDetector(
          onTap: viewModel.onMarkerNotTap,
          child: Container(
            margin: const EdgeInsets.only(top: 50),
            padding: const EdgeInsets.symmetric(horizontal: 25, vertical: 10),
            width: MediaQuery.of(context).size.width * 0.4,
            // flex: 0.4
            decoration: BoxDecoration(
              border: Border.all(color: ColorsUtil.primaryColor),
              borderRadius: BorderRadius.circular(5),
            ),
            alignment: Alignment.center,
            child: Text(
              '设为未结',
              style: TextStyle(
                fontSize: 15,
                color: ColorsUtil.primaryColor, // 替换 c.primary
              ),
            ),
          ),
        ),
      ],
    );
  }
}
