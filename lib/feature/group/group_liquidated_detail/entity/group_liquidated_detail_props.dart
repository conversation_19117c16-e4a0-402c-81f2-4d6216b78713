import 'package:gdjg_pure_flutter/feature/group/common_params_model/worker_model.dart';

/// @date 2025/07/01
/// @description GroupUnLiquidated页入参
class GroupLiquidatedDetailProps {
  ///工友id
  final List<WorkerModel>? workers;

  ///项目名称
  final String? workNoteName;

  ///项目id
  final String workNote;

  ///部门id
  final double? deptId;

  ///是否加入不知道啥意思原来也没有注释
  final bool? isJoin;

  ///是否是带班 0-否 1-是
  final double? isAgent;

  ///是否是未结这个参数会影响页面ui样式
  final bool? isUnLiquidated;

  GroupLiquidatedDetailProps({
    required this.workNoteName,
    required this.workNote,
    required this.deptId,
    required this.isUnLiquidated,
    required this.isAgent,
    required this.isJoin,
    this.workers,
  });
}
