import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gdjg_pure_flutter/widget/page_error_view.dart';
import 'package:gdjg_pure_flutter/widget/page_loading_view.dart';
import 'entity/group_pro_recycle_props.dart';
import 'vm/group_pro_recycle_viewmodel.dart';

/// @date 2025/07/07
/// @param props 页面路由参数
/// @returns
/// @description GroupProRecycle页面入口 回收站
class GroupProRecyclePage extends StatelessWidget {
  GroupProRecyclePage({super.key, this.props});

  final GroupProRecycleProps? props;
  final GroupProRecycleViewModel viewModel = GroupProRecycleViewModel();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text("GroupProRecycle"), centerTitle: true),
      body: Obx(() {
        if (viewModel.isLoading.value) {
          return const PageLoadingView();
        }
        if (viewModel.uiState.value.isShowError == true) {
          return PageErrorView(onReload: () => viewModel.fetchData());
        }
        return contentView();
      }),
    );
  }

  /// 实际展示的视图，需要处理：
  /// 1. 画出UI
  /// 2. 绑定UIState
  /// 3. 处理事件监听
  /// 4. 向VM传递来自View层的事件
  Widget contentView() {
    // 进行事件处理
    // handleGroupProRecycleVMEvent(props.vm)
    return Center(
      child: Text(viewModel.uiState.value.data.toString()),
    );
  }
}
