import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/common_uistate/statistics_ui_state.dart';
import 'package:gdjg_pure_flutter/model/RecordType.dart';
import 'package:gdjg_pure_flutter/utils/system_util/font_util.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';
import 'package:gdjg_pure_flutter/widget/dashe_line_view.dart';

class GroupBillStatisticsView extends StatelessWidget {
  final List<StatisticsItemUIState> list;
  final bool isShowEditWage;
  final Function(int)? onItemTap;
  final Function(int)? onEditWageTap;

  const GroupBillStatisticsView({
    super.key,
    this.list = const [],
    this.isShowEditWage = true,
    this.onItemTap,
    this.onEditWageTap,
  });

  @override
  Widget build(BuildContext context) {
    final skippedItems = list.toList();
    return Padding(
      padding: const EdgeInsets.fromLTRB(12, 10, 12, 0),
      child: ListView.separated(
        shrinkWrap: true,
        // 添加此属性
        physics: const NeverScrollableScrollPhysics(),
        // 添加此属性
        padding: EdgeInsets.zero,
        itemCount: skippedItems.length,
        separatorBuilder: (context, index) => const SizedBox(height: 8),
        // 8像素间隔
        itemBuilder: (context, index) {
          return _buildItem(skippedItems[index], index);
        },
      ),
    );
  }

  Widget _buildItem(StatisticsItemUIState item, int index) {
    var bgColor = (item.recordType == RwaRecordType.debt ||
            item.recordType == RwaRecordType.wageLast)
        ? Color(0x1AFFA011)
        : ColorsUtil.primaryColor15;
    var amountColor = (item.recordType == RwaRecordType.debt ||
            item.recordType == RwaRecordType.wageLast)
        ? Color(0xFFFF9F31)
        : ColorsUtil.primaryColor;
    return Container(
      padding: EdgeInsets.symmetric(vertical: 10, horizontal: 8),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(4),
        color: bgColor,
      ),
      constraints: BoxConstraints(minHeight: 86),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => onItemTap?.call(index),
          child:  Row(
            children: [
              //最左边的标题和笔数
              Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
                Row(
                  children: [
                    //标题
                    Text(
                      item.typeName ?? "",
                      style: TextStyle(
                        fontSize: 16.sp,
                        color: Color(0xFF323232),
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    if (item.unitWorkTypeName != null) ...[
                      SizedBox(width: 8),
                      Text(
                        item.unitWorkTypeName ?? "",
                        style: TextStyle(
                          fontSize: 16.sp,
                          color: Color(0xFF323232),
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ],
                ),
                // 笔数
                if (item.unitWorkNum != null)
                  Text(
                    item.unitWorkNum ?? "",
                    style: TextStyle(fontSize: 15, color: Color(0xFF9D9DB3)),
                    overflow: TextOverflow.ellipsis,
                  ),
              ]),
              SizedBox(width: 12),
              //中间上班和加班
              Expanded(
                flex: 1,
                child: Center(
                  child: Text(
                    item.detail ?? "",
                    style: TextStyle(fontSize: 15, color: Color(0xFF323232)),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ),
              Row(
                children: [
                  Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                        item.feeMoney ?? "0.00",
                        style: TextStyle(
                          fontSize: 22,
                          fontFamily: FontUtil.fontCondMedium,
                          color: amountColor,
                        ),
                      ),
                      if (item.total != null)
                        Text(
                          item.total ?? "",
                          style: TextStyle(
                            fontSize: 14,
                            color: ColorsUtil.black65,
                          ),
                        ),
                    ],
                  ),
                  if (isShowEditWage && item.isRecordWorkType == true) ...[
                    SizedBox(width: 8),
                    GestureDetector(
                      onTap: () {
                        onEditWageTap?.call(index);
                      },
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          // 虚线部分
                          CustomPaint(
                            painter: DashedLinePainter(
                                color: ColorsUtil.primaryColor),
                            size: Size(1, 48), // 设置虚线高度为24像素，可根据需求调整
                          ),
                          SizedBox(width: 5, height: 5), // 虚线和文字之间的间距
                          // 文字部分
                          SizedBox(
                            width: 16,
                            child: Text(
                              "改工资",
                              style: TextStyle(
                                  fontSize: 14,
                                  color: ColorsUtil.primaryColor),
                            ),
                          ),
                        ],
                      ),
                    )
                  ]
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
