import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/feature/group/group_pro_bill/view/image_and_share_pop_view.dart';
import 'package:gdjg_pure_flutter/feature/group/group_pro_bill/vm/group_pro_bill_vm.dart';
import 'package:gdjg_pure_flutter/iconfont/iconfont.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/debounce_util.dart';

/// 生成图片点击-前置确认弹窗
class GenerateImageConfirmDialog extends StatefulWidget {
  final String buttonText;
  final GroupProBillViewModel viewModel;

  const GenerateImageConfirmDialog({
    super.key,
    this.buttonText = '生成图片',
    required this.viewModel,
  });

  /// 显示弹窗
  static void show(
    BuildContext context, {
    required GroupProBillViewModel viewModel,
    bool showSalary = true,
    String buttonText = '生成图片',
    VoidCallback? onShare,
    VoidCallback? onClose,
  }) {
    YPRoute.openDialog(
      alignment: Alignment.center,
      clickMaskDismiss: false,
      builder: (context) => GenerateImageConfirmDialog(
        viewModel: viewModel,
        buttonText: buttonText,
      ),
    );
  }

  @override
  State<GenerateImageConfirmDialog> createState() =>
      _GenerateImageConfirmDialogState();
}

class _GenerateImageConfirmDialogState
    extends State<GenerateImageConfirmDialog> {
  bool isShow = false;
  final _debounce = DebounceUtil();

  @override
  Widget build(BuildContext context) {
    return Material(
      type: MaterialType.transparency,
      child: Center(
        child: Container(
          width: 320.w,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8.r),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 标题和关闭按钮
              _buildHeader(),

              // 内容区域
              _buildContent(),

              // 底部按钮
              _buildFooter(),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建头部（标题 + 关闭按钮）
  Widget _buildHeader() {
    return Stack(
      children: [
        // 标题
        Container(
          width: double.infinity,
          padding: EdgeInsets.only(top: 15.h, bottom: 12.h),
          child: Text(
            '分享确认',
            style: TextStyle(
              color: const Color(0xFF323233),
              fontSize: 16.sp,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
        ),

        // 关闭按钮
        Positioned(
          top: 8.h,
          right: 8.w,
          child: GestureDetector(
            behavior: HitTestBehavior.opaque,
            onTap: () {
              YPRoute.closeDialog();
            },
            child: Container(
              padding: EdgeInsets.all(8.w),
              child: IconFont(IconNames.saasClose, size: 20),
            ),
          ),
        ),
      ],
    );
  }

  /// 构建内容区域
  Widget _buildContent() {
    return Container(
      padding: EdgeInsets.only(left: 32.w, right: 16.w, bottom: 16.h),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            width: 1.w,
            color: const Color(0xFFE6E6E6),
          ),
        ),
      ),
      child: Row(
        children: [
          Text(
            '是否展示工资：',
            style: TextStyle(
              fontSize: 17.sp,
              color: const Color(0xFF333333),
            ),
          ),
          _buildYesNoButtons(),
        ],
      ),
    );
  }

  /// 构建是否按钮组（带滑动动画）
  Widget _buildYesNoButtons() {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      width: 136.w,
      height: 40.h,
      padding: EdgeInsets.all(2.w),
      decoration: BoxDecoration(
        border: Border.all(
          width: 1.w,
          color: Colors.black.withOpacity(0.4),
        ),
        borderRadius: BorderRadius.circular(4.r),
      ),
      child: Stack(
        children: [
          // 滑动的背景
          AnimatedPositioned(
            duration: const Duration(milliseconds: 200),
            curve: Curves.easeInOut,
            left: isShow ? 0 : 64.w,
            top: 0,
            child: Container(
              width: 64.w,
              height: 32.h,
              decoration: BoxDecoration(
                color: ColorsUtil.primaryColor,
                borderRadius: BorderRadius.circular(4.r),
              ),
            ),
          ),

          // 按钮文字层
          Row(
            children: [
              // "是" 按钮
              GestureDetector(
                behavior: HitTestBehavior.opaque,
                onTap: () {
                  setState(() {
                    isShow = true;
                  });
                },
                child: Container(
                  width: 64.w, // 128 / 2 = 64
                  height: 32.h, // 64 / 2 = 32
                  alignment: Alignment.center,
                  child: Text(
                    '是',
                    style: TextStyle(
                      fontSize: 16.sp,
                      color:
                          isShow ? Colors.white : Colors.black.withOpacity(0.4),
                      fontWeight: FontWeight.w500,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),

              // "否" 按钮
              GestureDetector(
                behavior: HitTestBehavior.opaque,
                onTap: () {
                  setState(() {
                    isShow = false;
                  });
                },
                child: Container(
                  width: 64.w,
                  height: 32.h,
                  alignment: Alignment.center,
                  child: Text(
                    '否',
                    style: TextStyle(
                      fontSize: 16.sp,
                      color: !isShow
                          ? Colors.white
                          : Colors.black.withOpacity(0.4),
                      fontWeight: FontWeight.w500,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 构建底部按钮
  Widget _buildFooter() {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () {
        _debounce.run(context, () async {
          // 参数常量定义
          const String shareChannel = "1"; // 班组流水统计页
          const String shareType = "2"; // 图片
          const String identity = "1"; // 班组长
          
         await widget.viewModel.getGenerateShareImageParam(
              isShowSalary: isShow,
              shareChannel: shareChannel,
              shareType: shareType,
              identity: identity);
          YPRoute.closeDialog();
          if(widget.viewModel.imageAndShareUS.isShowPopView==true){
            if (mounted && !YPRoute.isDialogShowing()) {
              ImageAndSharePopView.show(
                context,
                viewModel: widget.viewModel,
                isShowSalary: isShow,
              );
            }
          }
        });
      },
      child: Container(
        height: 53.h,
        width: double.infinity,
        alignment: Alignment.center,
        child: Text(
          widget.buttonText,
          style: TextStyle(
            fontSize: 16.sp,
            color: ColorsUtil.primaryColor,
            fontWeight: FontWeight.bold,
          ),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }
}
