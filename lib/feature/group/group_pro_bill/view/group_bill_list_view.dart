import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/feature/group/group_pro_bill/vm/protocol/group_pro_bill_ui_state.dart';
import 'package:gdjg_pure_flutter/utils/system_util/font_util.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';

class GroupBillListView extends StatelessWidget {
  final List<GroupBillListUIState> list;
  final Function(BillItemUIState)? onItemTap;

  const GroupBillListView({
    super.key,
    this.list = const [],
    this.onItemTap,
  });

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      shrinkWrap: true,
      // 添加此属性
      physics: const NeverScrollableScrollPhysics(),
      padding: EdgeInsets.zero,
      itemCount: list.length ?? 0,
      itemBuilder: (context, index) {
        final worker = list[index];
        return _buildWorkerItem(context, worker);
      },
    );
  }

  Widget _buildWorkerItem(BuildContext context, GroupBillListUIState uiState) {
    return Container(
      color: const Color(0xFFF0F0F0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 日期
          Container(
            width: double.infinity,
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 10.h),
            child: Text(
              uiState.date ?? "",
              style: TextStyle(
                fontSize: 16.sp,
                color: const Color(0xFF323233),
              ),
            ),
          ),
          // 工友统计项目

          ...uiState.billList.map((item) => _buildBillItem(item)),
        ],
      ),
    );
  }

  Widget _buildBillItem(BillItemUIState item) {
    return Container(
      color: Colors.white,
      child: Column(
        children: [
          Container(
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 10.h),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,

              children: [
                // 工人名字和类型
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      item.workerName ?? "",
                      style: TextStyle(
                        fontSize: 16.sp,
                        color: const Color(0xFF323233),
                      ),
                    ),
                    SizedBox(height: 6.h),
                    Row(
                      children: [
                        Text(
                          item.typeName ?? '',
                          style: TextStyle(
                            fontSize: 15.sp,
                            color: const Color(0xFF8A8A99),
                          ),
                        ),
                        if (item.unitWorkTypeName?.isNotEmpty == true)
                          SizedBox(width: 8.w),

                        if (item.unitWorkTypeName?.isNotEmpty == true)
                          Text(
                            item.unitWorkTypeName ?? '',
                            style: TextStyle(
                              fontSize: 15.sp,
                              color: const Color(0xFF8A8A99),
                            ),
                          ),
                      ],
                    )
                  ],
                ),

                Row(
                  children: [
                    if(item.hasConfirm)
                    //对工状态
                      Container(
                        padding: EdgeInsets.symmetric(
                            vertical: 2.h, horizontal: 4.w),
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(6),
                            color: item.confirm == 0 ? Color(0xFFF0F0F0) : item
                                .confirm == 1 ? ColorsUtil.primaryColor : Color(
                                0xFFFFA011)),
                        child: Text(
                          item.getConfirm(),
                          style: TextStyle(
                            fontSize: 12.sp,
                            color: item.confirm == 0
                                ? const Color(0xFF808080)
                                : const Color(0xFFFFFFFF),
                          ),
                        ),
                      ),
                    // 工资和详情
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Text(
                          item.feeMoney,
                          style: TextStyle(
                            fontFamily: FontUtil.fontCondMedium,
                            fontSize: 22.sp,
                            fontWeight: FontWeight.w600,
                            color: ColorsUtil.primaryColor,
                          ),
                        ),
                        // 上班和加班详情
                        Container(
                          margin: EdgeInsets.only(left: 8.w),
                          child: Text(
                            item.detail,
                            style: TextStyle(
                              fontSize: 15.sp,
                              color: const Color(0xFF323233),
                            ),
                          ),
                        ),
                      ],
                    ),
                    SizedBox(width: 4.w),
                    Image.asset(
                        "assets/images/common/icon_arrow_right_grey.png",
                        width: 18,
                        height: 18),
                  ],
                )
              ],
            ),
          ),
          // 分割线
          Container(
            height: 0.5.h,
            color: const Color(0xFFE6E6E6),
          ),
        ],
      ),
    );
  }
}
