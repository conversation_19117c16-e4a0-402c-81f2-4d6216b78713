import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/feature/group/common_uistate/statistics_ui_state.dart';
import 'package:gdjg_pure_flutter/feature/group/group_pro_bill/vm/group_pro_bill_vm.dart';
import 'package:gdjg_pure_flutter/iconfont/iconfont.dart';
import 'package:gdjg_pure_flutter/model/RecordType.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/utils/system_util/font_util.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';
import 'package:qr_flutter/qr_flutter.dart';

/// 分享图片和底部分享弹窗
class ImageAndSharePopView extends StatefulWidget {
  final String buttonText;
  final GroupProBillViewModel vm;
  final bool isShowSalary;

  const ImageAndSharePopView({
    super.key,
    this.buttonText = '分享',
    required this.vm,
    this.isShowSalary = true,
  });

  /// 显示弹窗
  static void show(
    BuildContext context, {
    GroupProBillViewModel? viewModel,
    String buttonText = '分享',
    bool isShowSalary = true,
  }) {
    YPRoute.openDialog(
      alignment: Alignment.center,
      clickMaskDismiss: false,
      maskColor: Colors.transparent, // 设置为透明，我们自己处理遮罩
      builder: (context) => ImageAndSharePopView(
        vm: viewModel!,
        buttonText: buttonText,
        isShowSalary: isShowSalary,
      ),
    );
  }

  @override
  State<ImageAndSharePopView> createState() => _ImageAndSharePopViewState();
}

class _ImageAndSharePopViewState extends State<ImageAndSharePopView>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  final GlobalKey _contentKey = GlobalKey();

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutQuad,
    ));

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // 计算 AppBar + 状态栏的高度
    final statusBarHeight = MediaQuery.of(context).padding.top;
    final appBarHeight = kToolbarHeight;
    final topOffset = statusBarHeight + appBarHeight;

    // 计算可用的 body 区域高度
    final availableHeight = MediaQuery.of(context).size.height;
    final maxHeight = availableHeight;

    return Stack(
      children: [
        // AppBar点击区域
        Positioned(
          top: 0,
          left: 0,
          right: 0,
          height: topOffset,
          child: GestureDetector(
            behavior: HitTestBehavior.opaque,
            onTap: () {
              // 先关闭弹窗
              YPRoute.closeDialog();
              // 然后返回上一页
              YPRoute.closePage();
            },
            child: Container(
              color: Colors.transparent,
            ),
          ),
        ),
        // 遮罩层，只覆盖 body 区域
        Positioned(
          top: topOffset,
          left: 0,
          right: 0,
          bottom: 0,
          child: Container(
            color: Colors.black.withOpacity(0.5),
          ),
        ),
        // 统计内容区域 - 居中显示
        Positioned(
          top: topOffset,
          left: 0,
          right: 0,
          bottom: 150.h,
          // 为底部分享区域留出空间
          child: Align(
            alignment: Alignment.center,
            child: AnimatedBuilder(
              animation: _animationController,
              builder: (context, child) {
                return SlideTransition(
                  position: _slideAnimation,
                  child: child,
                );
              },
              child: GestureDetector(
                onTap: () {}, // 阻止点击内容区域关闭弹窗
                child: Material(
                  type: MaterialType.transparency,
                  child: SizedBox(
                    width: MediaQuery.of(context).size.width * 0.80,
                    child: _buildMainContent(),
                  ),
                ),
              ),
            ),
          ),
        ),
        // 分享操作区域 - 底部显示
        Positioned(
          left: 0,
          right: 0,
          bottom: 0,
          child: AnimatedBuilder(
            animation: _animationController,
            builder: (context, child) {
              return SlideTransition(
                position: _slideAnimation,
                child: child,
              );
            },
            child: _buildBottomActions(),
          ),
        ),
      ],
    );
  }

  /// 构建主要内容
  Widget _buildMainContent() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(10.r),
        child: RepaintBoundary(
          key: _contentKey,
          child: Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(10.r),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(10.r),
              child: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // 蓝色顶部标题栏
                    Container(
                      height: 40.h,
                      width: double.infinity,
                      color: ColorsUtil.primaryColor,
                      alignment: Alignment.center,
                      child: Text(
                        widget.vm.imageAndShareUS.title,
                        style: TextStyle(
                          fontSize: 15.sp,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                    ),
                    SizedBox(height: 10.h),

                    // 项目名称
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: 16.w),
                      child: Text(
                        widget.vm.imageAndShareUS.projectName,
                        style: TextStyle(
                          fontSize: 14.sp,
                          fontWeight: FontWeight.w500,
                          color: const Color(0xFF333333),
                        ),
                        textAlign: TextAlign.center,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),

                    // 工人信息（如果有）
                    SizedBox(height: 5.h),
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: 16.w),
                      child: Text(
                        widget.vm.imageAndShareUS.workerName, // 可以根据实际数据调整
                        style: TextStyle(
                          fontSize: 14.sp,
                          fontWeight: FontWeight.w500,
                          color: const Color(0xFF333333),
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),

                    SizedBox(height: 5.h),

                    // 时间范围
                    Text(
                      widget.vm.imageAndShareUS.timeRangeText,
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: const Color(0xFF666666),
                      ),
                      textAlign: TextAlign.center,
                    ),

                    SizedBox(height: 10.h),

                    // 记工部分（包含标题，只有有数据时才显示）
                    _buildWorkTypeItems(),

                    // 借支/结算部分（包含标题，只有有数据时才显示）
                    _buildAccountItems(),

                    // 二维码区域
                    SizedBox(
                      height: 100.h,
                      width: 100.w,
                      child: QrImageView(
                        data: widget.vm.imageAndShareUS.qrCodeUrl, // 实际分享链接
                        version: QrVersions.auto,
                        backgroundColor: Colors.white,
                      ),
                    ),

                    // 微信扫描提示
                    Text(
                      '微信扫描 查看流水',
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: Colors.black.withOpacity(0.85),
                      ),
                      textAlign: TextAlign.center,
                    ),

                    SizedBox(height: 10.h),

                    // 蓝色底部宣传栏
                    Container(
                      height: 40.h,
                      width: double.infinity,
                      color: ColorsUtil.primaryColor,
                      alignment: Alignment.center,
                      child: Text(
                        '记工简单、对工方便、用不丢失',
                        style: TextStyle(
                          fontSize: 12.sp,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// 构建记工类型项目
  Widget _buildWorkTypeItems() {
    final statisticsList = widget.vm.us.statisticsList;

    // 筛选记工类型的数据
    final workTypeList = statisticsList.where((item) {
      return item.recordType == RwaRecordType.workDays ||
          item.recordType == RwaRecordType.packageWork ||
          item.recordType == RwaRecordType.workLoad ||
          item.recordType == RwaRecordType.dailyWages;
    }).toList();

    // 如果没有记工数据，返回空Widget
    if (workTypeList.isEmpty) {
      return const SizedBox.shrink();
    }

    // 有数据时，返回包含标题的完整部分
    return Column(
      children: [
        _buildSectionTitle('- 记工 -'),
        SizedBox(height: 15.h),
        ...workTypeList.map((item) {
          return _buildRecordItem(
            item,
            _buildDetailText(item),
            widget.isShowSalary ? (item.feeMoney ?? '') : "",
          );
        }),
        SizedBox(height: 10.h),
      ],
    );
  }

  /// 构建借支/结算类型项目
  Widget _buildAccountItems() {
    final statisticsList = widget.vm.us.statisticsList;

    // 筛选借支/结算类型的数据
    final accountTypeList = statisticsList.where((item) {
      return item.recordType == RwaRecordType.debt ||
          item.recordType == RwaRecordType.wageLast;
    }).toList();

    // 如果没有借支/结算数据，返回空Widget
    if (accountTypeList.isEmpty) {
      return const SizedBox.shrink();
    }

    // 有数据时，返回包含标题的完整部分
    return Column(
      children: [
        _buildSectionTitle('- 借支/结算 -'),
        SizedBox(height: 15.h),
        ...accountTypeList.map((item) {
          return _buildRecordItem(item, _buildDetailText(item),
              widget.isShowSalary ? (item.feeMoney ?? '') : '');
        }),
        SizedBox(height: 10.h),
      ],
    );
  }

  /// 构建详情文本
  String _buildDetailText(StatisticsItemUIState item) {
    String detail = '';

    if (item.detail != null && item.detail!.isNotEmpty) {
      detail = item.detail!;
    }

    if (item.workNum != null &&
        (item.typeName == '短工' || item.isRecordWorkType == false)) {
      detail += ' ${item.workNum}笔';
    }
    return detail;
  }

  /// 构建记录条目
  /// todo:LINer笔数未添加，等待上一个页面拿笔数数据
  Widget _buildRecordItem(
      StatisticsItemUIState item, String detail, String amount) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 8.w, vertical: 2.h),
      padding: EdgeInsets.all(8.h),
      decoration: BoxDecoration(
        color: item.isRecordWorkType == true
            ? Color.fromRGBO(82, 144, 253, 0.1)
            : Color.fromRGBO(255, 160, 17, 0.15),
        borderRadius: BorderRadius.circular(4.r),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Text(
                    item.typeName ?? '',
                    style: TextStyle(
                      fontSize: 16.sp,
                      fontWeight: item.isRecordWorkType == true
                          ? FontWeight.normal
                          : FontWeight.bold,
                      color: const Color(0xFF323233),
                    ),
                  ),
                  if (item.workNum != null)
                    Text(
                      item.workNum ?? '',
                      style: TextStyle(
                        fontSize: 15.sp,
                        color: const Color(0xFF9D9DB3),
                      ),
                    ),
                ],
              ),
              if (item.unitWorkTypeName != null) SizedBox(width: 8.w),
              Text(
                item.unitWorkTypeName ?? '',
                style: TextStyle(
                  fontSize: 16.sp,
                  color: const Color(0xFF323233),
                ),
              ),
            ],
          ),
          SizedBox(width: 8.w),
          Expanded(
            child: Text(
              detail,
              style: TextStyle(
                fontSize: 15.sp,
                color: const Color(0xFF323233),
              ),
            ),
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              if (widget.isShowSalary)
                Text(
                  amount,
                  style: TextStyle(
                    fontSize: 22.sp,
                    color: item.isRecordWorkType == true
                        ? ColorsUtil.primaryColor
                        : Color(0xFFFFA011),
                    fontFamily: FontUtil.fontCondMedium,
                  ),
                ),
              if (item.total != null)
                Text(
                  item.total ?? '',
                  style: TextStyle(
                    fontSize: 15.sp,
                    color: const Color(0xFF323233),
                  ),
                ),
            ],
          ),
        ],
      ),
    );
  }

  /// 构建章节标题
  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: TextStyle(
        fontSize: 13.sp,
        fontWeight: FontWeight.bold,
        color: ColorsUtil.primaryColor,
      ),
      textAlign: TextAlign.center,
    );
  }

  /// 构建分享操作区域
  Widget _buildBottomActions() {
    return Container(
      height: 150.h,
      width: double.infinity,
      color: Colors.white,
      child: Column(
        children: [
          // 关闭按钮栏
          Container(
            height: 40.h,
            decoration: BoxDecoration(
              border: Border(
                bottom: BorderSide(
                  width: 1.w,
                  color: Color(0xFFe6e6e6),
                ),
              ),
            ),
            child: Row(
              children: [
                GestureDetector(
                  onTap: () {
                    // 点击关闭按钮关闭弹窗
                    YPRoute.closeDialog();
                  },
                  child: Container(
                    padding: EdgeInsets.symmetric(horizontal: 16.w),
                    child: IconFont(
                      IconNames.saasClose,
                      size: 20,
                      color: "#8A8A99",
                    ),
                  ),
                ),
                Expanded(
                  child: Text(
                    '分享',
                    style: TextStyle(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.bold,
                      color: const Color(0xFF8A8A99),
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
                SizedBox(width: 52.w), // 平衡右侧空间
              ],
            ),
          ),

          // 操作按钮
          Expanded(
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 50.w, vertical: 15.h),
              child: Row(
                children: [
                  // 分享到微信
                  Expanded(
                    child: GestureDetector(
                      onTap: _handleShareToWechat,
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Image.network(
                            'https://cdn.yupaowang.com/jgjz/ic_personal_bill_detail_wechat.png',
                            width: 40.w,
                            height: 40.w,
                          ),
                          SizedBox(height: 2.h),
                          Text(
                            '分享到微信',
                            style: TextStyle(
                              fontSize: 12.sp,
                              color: Colors.black.withOpacity(0.75),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  // 保存至手机
                  Expanded(
                    child: GestureDetector(
                      onTap: _handleSaveToPhone,
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Image.network(
                            'https://cdn.yupaowang.com/jgjz/ic_personal_bill_detail_download.png',
                            width: 40.w,
                            height: 40.w,
                          ),
                          SizedBox(height: 2.h),
                          Text(
                            '保存至手机',
                            style: TextStyle(
                              fontSize: 12.sp,
                              color: Colors.black.withOpacity(0.75),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 分享到微信
  Future<void> _handleShareToWechat() async {
    await widget.vm.onShareToWechatTap(_contentKey);
    YPRoute.closeDialog();
  }

  /// 保存至手机
  void _handleSaveToPhone() async {
    await widget.vm.onSaveToPhoneTap(_contentKey);
    YPRoute.closeDialog();
  }
}
