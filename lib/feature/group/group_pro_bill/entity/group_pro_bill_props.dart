import 'package:gdjg_pure_flutter/feature/group/common_params_model/worker_model.dart';
import 'package:gdjg_pure_flutter/model/RecordType.dart';

/// @date 2025/06/26
/// @description GroupProBill页入参
class GroupProBillProps {
  ///
  String workNoteId;

  ///
  String workNoteName;

  ///
  String? startTime;

  ///
  String? endTime;

  ///带班id
  double? deptId;

  ///是否加入
  bool? isJoin;

  ///类型
  List<RwaRecordType>? businessType;

  ///工友
  List<WorkerModel>? workers;

  /// 分页字段，一页多少条，不传为不分页
  int limit;

  /// 页数，1页，2页。配合limit 使用
  int page;

  GroupProBillProps({
    this.workNoteId = '',
    this.workNoteName = '',
    this.startTime,
    this.endTime,
    this.deptId,
    this.isJoin = false,
    this.businessType,
    this.workers,
    this.limit = 20,
    this.page = 1,
  });
}
