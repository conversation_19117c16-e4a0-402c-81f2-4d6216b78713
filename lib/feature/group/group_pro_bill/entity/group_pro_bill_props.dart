import 'package:gdjg_pure_flutter/feature/group/common_params_model/worker_model.dart';
import 'package:gdjg_pure_flutter/model/RecordType.dart';

/// @date 2025/06/26
/// @description GroupProBill页入参
class GroupProBillProps {
  ///
  String workNoteId;

  ///
  String workNoteName;

  ///
  DateTime? startTime;

  ///
  DateTime? endTime;

  ///带班id
  double? deptId;

  ///是否加入
  bool? isJoin;

  ///类型
  List<RwaRecordType>? businessType;

  ///工友
  List<WorkerModel>? workers;

  GroupProBillProps({
    this.workNoteId = '',
    this.workNoteName = '',
    this.startTime,
    this.endTime,
    this.deptId,
    this.isJoin = false,
    this.businessType,
    this.workers,
  });

  getTitle(){
    return workNoteName;
  }

  @override
  String toString() {
    return 'GroupProBillProps{data: $workNoteId,$workNoteName,$startTime,$endTime,$deptId,$isJoin,$businessType,$workers}';
  }
}
