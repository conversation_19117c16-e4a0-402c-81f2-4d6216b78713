import 'package:gdjg_pure_flutter/feature/group/common_uistate/statistics_ui_state.dart';
import 'package:gdjg_pure_flutter/feature/group/group_pro_bill/vm/group_pro_bill_list_ui_state.dart';
import 'package:gdjg_pure_flutter/model/RecordType.dart';
import 'package:get/get.dart';

/// @date 2025/06/26
/// @description JDetail页UI状态
class GroupProBillUS {

  /// 是否有更多数据
  final _hasMore = true.obs;

  get hasMore => _hasMore.value;

  set hasMore(value) => _hasMore.value = value;

  /// 统计项
  final _statisticsList = <StatisticsItemUIState>[].obs;
  final _isStatisticsEmpty = false.obs;

  /// 流水列表
  final _billList = <GroupBillListUIState>[].obs;

  final _startTime = DateTime.now().obs;

  final _endTime = DateTime.now().obs;

  final _rwaRecordTypeList = <RwaRecordType>[].obs;

  List<StatisticsItemUIState> get statisticsList => _statisticsList.value;

  List<GroupBillListUIState> get billList => _billList.value;

  bool get isStatisticsEmpty => _isStatisticsEmpty.value;

  DateTime get startTime => _startTime.value;

  DateTime get endTime => _endTime.value;

  List<RwaRecordType> get selectedTypes => _rwaRecordTypeList.value;

  void setStatisticsList(List<StatisticsItemUIState> list) {
    _statisticsList.assignAll(list);
    _isStatisticsEmpty.value = list.isEmpty;
    _statisticsList.refresh();
    _isStatisticsEmpty.refresh();
  }

  setBillList(List<GroupBillListUIState> list) {
    _billList.assignAll(list);
    _billList.refresh();
  }

  addBillList(List<GroupBillListUIState> list) {
    _billList.addAll(list);
    _billList.refresh();
  }

  setStartTime(DateTime? date) {
    if (date == null) {
      return;
    }
    _startTime.value = date;
    _startTime.refresh();
  }

  setEndTime(DateTime? date) {
    if (date == null) {
      return;
    }
    _endTime.value = date;
    _endTime.refresh();
  }

  setRwaRecordTypeList(List<RwaRecordType> list) {
    _rwaRecordTypeList.value = list;
    _rwaRecordTypeList.refresh();
  }
}
