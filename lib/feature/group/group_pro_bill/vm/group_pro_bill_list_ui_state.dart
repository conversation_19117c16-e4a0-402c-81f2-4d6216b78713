import 'package:gdjg_pure_flutter/model/RecordType.dart';

class GroupBillListUIState {
  String date;

  List<BillItemUIState> billList;

  GroupBillListUIState({
    this.date = "",
    this.billList = const [],
  });
}

/// 统计项
class BillItemUIState {
  /// 类型 主要是为了点击的时候需要传值使用
  RwaRecordType businessType;
  /// 唯一id
  double id;
  /// 类型
  String typeName;

  /// 详情
  String detail;

  /// 工资
  String feeMoney;

  /// 分项名称
  String? unitWorkTypeName;

  ///动对工状态 0-未对工 1-对工无成 2-对工有误
  double? confirm;

  /// 是否存在对工状态
  bool hasConfirm = false;

  String? workerName;

  double? workerId;

  BillItemUIState({
    required this.businessType,
    this.id = 0,
    this.typeName = '',
    this.detail = '',
    this.feeMoney = '-',
    this.unitWorkTypeName,
    this.confirm,
    this.hasConfirm = false,
    this.workerName,
    this.workerId,
  });

  //记工类型
  isRecordWorkType() {
    return (businessType == RwaRecordType.workDays ||
        businessType == RwaRecordType.workLoad ||
        businessType == RwaRecordType.dailyWages ||
        businessType == RwaRecordType.packageWork);
  }

  getConfirm() {
    if (confirm == 0) {
      return isRecordWorkType() ? "未对工" : '未对账';
    } else if (confirm == 1) {
      return isRecordWorkType() ? "已对工" : '已对账';
    } else if (confirm == 2) {
      return isRecordWorkType() ? "对工有误" : '对账有误';
    }
  }
}
