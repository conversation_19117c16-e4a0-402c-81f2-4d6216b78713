import "package:gdjg_pure_flutter/common_uistate/statistice_helper.dart";
import "package:gdjg_pure_flutter/common_uistate/statistics_ui_state.dart";
import "package:gdjg_pure_flutter/data/group_data/group_pro_bill/ds/model/param/group_business_get_group_business_count_param_model.dart";
import "package:gdjg_pure_flutter/data/group_data/group_pro_bill/ds/model/param/group_business_get_group_business_list_param_model.dart";
import "package:gdjg_pure_flutter/data/group_data/group_pro_bill/repo/model/group_business_get_group_business_list_biz_model.dart";
import "package:gdjg_pure_flutter/feature/group/common_params_model/pro_model.dart";
import "package:gdjg_pure_flutter/feature/group/group_edit_wage/entity/group_edit_wage_props.dart";
import "package:gdjg_pure_flutter/feature/group/group_pro_bill/entity/group_pro_bill_props.dart";
import "package:gdjg_pure_flutter/feature/group/group_pro_bill/ui_rep/group_pro_bill_ui_rep.dart";
import "package:gdjg_pure_flutter/feature/group/group_pro_bill/vm/protocol/group_pro_bill_ui_state.dart";
import "package:gdjg_pure_flutter/init_module/init_route.dart";
import "package:gdjg_pure_flutter/model/RecordNoteType.dart";
import "package:gdjg_pure_flutter/model/RecordType.dart";
import "package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart";
import "package:gdjg_pure_flutter/utils/system_util/string_util.dart";
import "package:gdjg_pure_flutter/utils/system_util/yprint.dart";
import "package:get/get.dart";

/// @date 2025/06/26
/// @description GroupProBill页ViewModel
/// 1. 处理数据的加载状态，如loading和错误视图等
/// 2. 观测UIRep中的业务实体转为UIState
/// 3. 接受来自view的意图并分发事件进行处理（暂无）
/// 4. 对View层暴露事件（操作视图和处理一些与系统权限交互等场景）
class GroupProBillViewModel {
  var isLoading = false.obs;
  var isShowError = false.obs;
  var uiState = GroupProBillUIState().obs;
  var uiRep = GroupProBillUIRep();

  GroupProBillProps? props;

  GroupProBillViewModel() {
    ever(uiRep.entity, (value) {
      convertEntityToUIState(value);
    });
  }

  init(GroupProBillProps? props) {
    this.props = props;
    fetchData();
  }

  ///更新开始和结束时间
  void updateDateRange(DateTime startTime, DateTime endTime, List<RwaRecordType>? businessType) {
    props?.businessType = businessType;
    props?.startTime = _formatDate(startTime);
    props?.endTime = _formatDate(endTime);
    fetchData(); // 请求新数据
  }

  ///格式化日期
  String _formatDate(DateTime date) {
    return "${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}";
  }

  /// 初始化数据同步，如果不需要loading，则在这里去掉
  void fetchData() async {
    isLoading.value = true;
    try {
      var businessTypeStr = props?.businessType
              ?.map((type) => type.code.value.toString())
              .join(',') ??
          '';
      var workerIds = props?.workers
              ?.map((worker) => worker.workerId.toString())
              .join(',') ??
          '';
      var businessCountParams = GroupBusinessGetGroupBusinessCountParamModel()
        ..work_note = props?.workNoteId ?? ''
        ..start_time = props?.startTime ?? ''
        ..end_time = props?.endTime ?? ''
        ..business_type = businessTypeStr
        ..worker_ids = workerIds;
      var businessListParams = GroupBusinessGetGroupBusinessListParamModel()
        ..page = props?.page ?? 1
        ..work_note = props?.workNoteId ?? ''
        ..start_time = props?.startTime ?? ''
        ..end_time = props?.endTime ?? ''
        ..business_type = businessTypeStr
        ..worker_ids = workerIds;
      // 同步获取数据
      await uiRep.getCombinedData(businessCountParams, businessListParams);
      // 这里的result仅做弹窗之类的处理，不在此处转为UI实体
    } catch (e) {
      uiState.value.isShowError = true;
    } finally {
      isLoading.value = false;
    }
  }

  /// 用于上拉加载更多使用
  void onLoadBusinessList() async {
    try {
      props?.page = (props?.page ?? 0) + 1;
      var businessTypeStr = props?.businessType
              ?.map((type) => type.code.value.toString())
              .join(',') ??
          '';
      var workerIds = props?.workers
              ?.map((worker) => worker.workerId.toString())
              .join(',') ??
          '';
      var businessListParams = GroupBusinessGetGroupBusinessListParamModel()
        ..page = props?.page ?? 1
        ..work_note = props?.workNoteId ?? ''
        ..start_time = props?.startTime ?? ''
        ..end_time = props?.endTime ?? ''
        ..business_type = businessTypeStr
        ..worker_ids = workerIds;
      // 同步获取数据
      await uiRep.getGroupBusinessList(businessListParams);
      // 这里的result仅做弹窗之类的处理，不在此处转为UI实体
    } catch (e) {
      yprint(e.toString());
    } finally {}
  }

  /// 将实体数据转换为UIState
  /// @param entity
  /// @returns
  void convertEntityToUIState(GroupProBillUIRepEntity entity) {
    var count = entity.count;
    List<StatisticsItemUIState> statisticsList =
        StatisticsUIStateHelper.buildStatisticsItem(count);
    uiState.value.statisticsList = statisticsList;
    uiState.value.isEmpty = statisticsList.isEmpty;
    if (statisticsList.isEmpty) {
      uiState.value.billList = [];
      uiState.refresh();
      return;
    }

    var list = entity.list;
    if (list != null && list.isNotEmpty) {
      List<GroupBillListUIState> listBill = [];
      for (var element in list) {
        listBill.add(GroupBillListUIState(
            date: element.date,
            billList: covertEntityToBillItemUIState(element.list)));
      }
      uiState.value.billList = listBill;
    }
    uiState.refresh();
  }

  List<BillItemUIState> covertEntityToBillItemUIState(
      List<GroupBusinessGetGroupBusinessListBBizModel> list) {
    List<BillItemUIState> listCount = [];
    for (var element in list) {
      var recordType = RwaRecordType.values[element.businessType.toInt()];
      BillItemUIState uiState = BillItemUIState(recordType: recordType);
      uiState.workerName = element.workerName;
      uiState.typeName =
          StatisticsUIStateHelper.getBusinessTypeName(element.businessType);
      uiState.detail = StatisticsUIStateHelper.getShowContentText(element);
      uiState.feeMoney = getShowMoney(element);
      //分项
      if (element.businessType == RecordType.workLoad.value) {
        uiState.unitWorkTypeName = element.unitWorkTypeName;
      }
      uiState.confirm = element.confirm;
      uiState.hasConfirm = element.confirm != null;
      listCount.add(uiState);
    }
    return listCount;
  }

  getShowMoney(GroupBusinessGetGroupBusinessListBBizModel model) {
    var businessType = model.businessType;
    if (businessType == RecordType.workDays.value ||
        businessType == RecordType.packageWork.value) {
      if ((model.feeStandardId ?? 0) > 0) {
        return model.feeMoney.formatDoubleToMoney();
      }
      return "-";
    }
    if (businessType == RecordType.workLoad.value ||
        businessType == RecordType.dailyWages.value ||
        businessType == RecordType.debt.value ||
        businessType == RecordType.wageLast.value ||
        businessType == RecordType.incomeLast.value ||
        businessType == RecordType.expenditure.value) {
      return model.money.formatStringToMoney();
    }
    return "0.00";
  }

  ///跳转修改工资规则页面 todo 后边提取出去
  void jumpToEditWage(int index) {
    GroupEditWageProps params = GroupEditWageProps()
      ..recordNoteType = RecordNoteType.group
      ..workNoteName = props?.workNoteName ?? ""
      ..startTime = props?.startTime
      ..endTime = props?.endTime
      ..deptId = props?.deptId
      ..businessType = [uiState.value.statisticsList[index].recordType]
      ..unitWorkType = uiState.value.statisticsList[index].unitWorkType
      ..projects = [
        GroupProjectModel(
            workNoteId: props?.workNoteId, workNoteName: props?.workNoteName)
      ]
      ..workers = props?.workers;
    YPRoute.openPage(RouteNameCollection.groupProEditWage, params: params);
  }
}
