import "dart:typed_data";
import "dart:ui" as ui;

import "package:flutter/rendering.dart";
import "package:flutter/widgets.dart";
import "package:gdjg_pure_flutter/data/common_data/biz/count_biz_model.dart";
import "package:gdjg_pure_flutter/data/group_data/group_pro_bill/ds/model/param/business_get_new_share_business_count_url_param_model.dart";
import "package:gdjg_pure_flutter/data/group_data/group_pro_bill/ds/model/param/group_business_get_group_business_count_param_model.dart";
import "package:gdjg_pure_flutter/data/group_data/group_pro_bill/ds/model/param/group_business_get_group_business_list_param_model.dart";
import "package:gdjg_pure_flutter/data/group_data/group_pro_bill/repo/group_pro_bill_repo.dart";
import "package:gdjg_pure_flutter/data/group_data/group_pro_bill/repo/model/business_get_new_share_business_count_url_biz_model.dart";
import "package:gdjg_pure_flutter/data/group_data/group_pro_bill/repo/model/group_business_get_group_business_list_biz_model.dart";
import "package:gdjg_pure_flutter/data/note_time_data/note_time_repo.dart";
import "package:gdjg_pure_flutter/feature/group/common_params_model/pro_model.dart";
import "package:gdjg_pure_flutter/feature/group/common_params_model/worker_model.dart";
import "package:gdjg_pure_flutter/feature/group/common_uistate/statistice_helper.dart";
import "package:gdjg_pure_flutter/feature/group/common_uistate/statistics_ui_state.dart";
import "package:gdjg_pure_flutter/feature/group/group_edit_record_work/entity/group_edit_record_work_props.dart";
import "package:gdjg_pure_flutter/feature/group/group_edit_wage/entity/group_edit_wage_props.dart";
import "package:gdjg_pure_flutter/feature/group/group_pro_bill/entity/group_pro_bill_props.dart";
import "package:gdjg_pure_flutter/feature/group/group_pro_bill/vm/group_pro_bill_list_ui_state.dart";
import "package:gdjg_pure_flutter/feature/group/group_pro_bill/vm/group_pro_bill_us.dart";
import "package:gdjg_pure_flutter/feature/group/group_pro_bill/vm/image_and_share_us.dart";
import "package:gdjg_pure_flutter/init_module/init_route.dart";
import "package:gdjg_pure_flutter/model/RecordNoteType.dart";
import "package:gdjg_pure_flutter/model/RecordType.dart";
import "package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart";
import "package:gdjg_pure_flutter/utils/system_util/date_util.dart";
import "package:gdjg_pure_flutter/utils/system_util/string_util.dart";
import "package:gdjg_pure_flutter/utils/system_util/yprint.dart";
import "package:gdjg_pure_flutter/utils/ui_util/photo_picker_util.dart";
import "package:gdjg_pure_flutter/utils/ui_util/toast_util.dart";
import "package:gdjg_pure_flutter/utils/wechat_util/wechat_util.dart";
import "package:intl/intl.dart";
import "package:pull_to_refresh/pull_to_refresh.dart";

/// @date 2025/06/26
/// @description GroupProBill页ViewModel
/// 1. 处理数据的加载状态，如loading和错误视图等
/// 2. 观测UIRep中的业务实体转为UIState
/// 3. 接受来自view的意图并分发事件进行处理（暂无）
/// 4. 对View层暴露事件（操作视图和处理一些与系统权限交互等场景）
class GroupProBillViewModel {
  final _groupProBillRepo = GroupProBillRepo();
  final _noteTimeRepo = NoteTimeRepo();
  final RefreshController refreshController =
      RefreshController(initialRefresh: false);
  var us = GroupProBillUS();
  var imageAndShareUS = ImageAndShareUS();
  List<RwaRecordType> _businessType = [];
  DateTime? _netStartTime;
  DateTime? _startTime;
  DateTime _endTime = DateTime.now();
  int _page = 1;
  int pageSize = 10;
  GroupProBillProps? _props;

  /// 获取标题
  String get title => _props?.getTitle();

  init(GroupProBillProps? props) {
    _props = props;
    var businessType = props?.businessType ?? [];
    _businessType = businessType;
    _startTime = props?.startTime;
    _endTime = props?.endTime ?? _endTime;
    us.setStartTime(_startTime);
    us.setEndTime(_endTime);
    us.setRwaRecordTypeList(businessType);
    fetchData();
  }

  ///更新开始和结束时间
  void updateDateRange(
      DateTime startTime, DateTime endTime, List<RwaRecordType>? businessType) {
    _startTime = startTime;
    _endTime = endTime;
    _businessType = businessType ?? [];
    fetchData();
  }

  /// 初始化数据同步，如果不需要loading，则在这里去掉
  void fetchData() async {
    try {
      _page = 1;
      if (_startTime == null) {
        await fetchFirstTime();
      }
      var businessType =
          _businessType.map((type) => type.code.value.toString()).join(',');
      var workerIds = _props?.workers
              ?.map((worker) => worker.workerId.toString())
              .join(',') ??
          '';
      var startTime = DateUtil.formatStartDate(_startTime);
      var endTime = DateUtil.formatEndDate(_endTime);
      var businessCountParams = GroupBusinessGetGroupBusinessCountParamModel()
        ..work_note = _props?.workNoteId ?? ''
        ..start_time = startTime
        ..end_time = endTime
        ..business_type = businessType
        ..worker_ids = workerIds;
      var businessListParams = GroupBusinessGetGroupBusinessListParamModel()
        ..page = _page
        ..work_note = _props?.workNoteId ?? ''
        ..start_time = startTime
        ..end_time = endTime
        ..business_type = businessType
        ..worker_ids = workerIds;
      // 同步获取数据
      await _getCombinedData(businessCountParams, businessListParams);
      // 这里的result仅做弹窗之类的处理，不在此处转为UI实体
    } finally {}
  }

  ///获取开始时间和结束时间
  Future<void> fetchFirstTime() async {
    DateTime endTime = DateTime.now();
    var resultTime =
        await _noteTimeRepo.getNoteFirstTime(_props?.workNoteId ?? "");
    if (resultTime.isOK()) {
      var time = resultTime.getSucData()?.date ?? "2020-01-01";
      var startTime = DateTime.parse(time);
      _startTime = startTime;
      _netStartTime = startTime;
      _endTime = endTime;
      us.setStartTime(startTime);
      us.setEndTime(endTime);
    }
  }

  /// 用于上拉加载更多使用
  void onLoadBusinessList() async {
    try {
      _page++;
      var businessType =
          _businessType.map((type) => type.code.value.toString()).join(',');
      var workerIds = _props?.workers
              ?.map((worker) => worker.workerId.toString())
              .join(',') ??
          '';
      var businessListParams = GroupBusinessGetGroupBusinessListParamModel()
        ..page = _page
        ..work_note = _props?.workNoteId ?? ''
        ..start_time = DateUtil.formatStartDate(_startTime)
        ..end_time = DateUtil.formatEndDate(_endTime)
        ..business_type = businessType
        ..worker_ids = workerIds;
      // 同步获取数据
      getGroupBusinessList(businessListParams);
      // 这里的result仅做弹窗之类的处理，不在此处转为UI实体
    } catch (e) {
      _page--;
      refreshController.loadFailed();
    } finally {}
  }

  Future<void> _getCombinedData(
      GroupBusinessGetGroupBusinessCountParamModel countParams,
      GroupBusinessGetGroupBusinessListParamModel listParams) async {
    try {
      final results = await Future.wait([
        _groupProBillRepo.getGroupBusinessCount(countParams),
        _groupProBillRepo.getGroupBusinessList(listParams),
      ]);

      final countResult = results[0];
      if (countResult.isOK()) {
        CountBizModel? data = countResult.getSucData() as CountBizModel?;
        _convertEntityToStatisticsUIState(data);
      }
      final listResult = results[1];
      if (listResult.isOK()) {
        GroupBusinessGetGroupBusinessListBizModel? data = listResult
            .getSucData() as GroupBusinessGetGroupBusinessListBizModel?;
        _convertEntityToBillListUIState(data);
      }
    } catch (e) {
      refreshController.refreshFailed();
    }
  }

  Future<void> getGroupBusinessList(
      GroupBusinessGetGroupBusinessListParamModel params) async {
    // 调用网络的方法获取数据
    var result = await _groupProBillRepo.getGroupBusinessList(params);
    if (result.isOK()) {
      GroupBusinessGetGroupBusinessListBizModel? data = result.getSucData();
      if (data != null) {
        _convertEntityToBillListUIState(data, isLoadMore: true);
      }
    } else {
      // 模拟异常情况
      // throw Exception("error");
    }
  }

  /// 获取生成图片所需参数
  /// 分享途径 0 默认，1班组流水统计页，2班组个人流水统计页，3个人流水统计页
  /// 分享形式 1下载，2图片 3微信对账
  /// 身份 1班组 2工人
  /// 是否显示钱：1展示 2不展示
  Future getGenerateShareImageParam({
    required bool isShowSalary,
    required String shareChannel,
    required String shareType,
    required String identity,
  }) async {
    var params = BusinessGetNewShareBusinessCountUrlParamModel(
      start_business_time: _props?.startTime?.toString() ?? "",
      end_business_time: _props?.endTime?.toString() ?? "",
      work_note: _props?.workNoteId,
      business_type:
          _props?.businessType?.map((type) => type.code.value).join(','),
      worker_id: _props?.workers?.map((worker) => worker.workerId).join(','),
      share_channel: shareChannel,
      share_type: shareType,
      identity: identity,
      is_show: isShowSalary ? "1" : "2",
    );
    var res = await _groupProBillRepo.generateShareImage(params);
    if (res.isOK()) {
      BusinessGetNewShareBusinessCountUrlBizModel? data = res.getSucData();
      if (data != null) {
        imageAndShareUS.setIsShowPopView(true);
        _convertEntityToImageAndShareUIState(data);
      }
    } else {
      imageAndShareUS.setIsShowPopView(false);
    }
  }

  /// 将数据转换成图片和分享UIState
  void _convertEntityToImageAndShareUIState(
      BusinessGetNewShareBusinessCountUrlBizModel? data) {
    if (data == null) return;
    imageAndShareUS.setTitle("工地记工项目协议");
    imageAndShareUS.setProjectName(title);
    // todo:LINer 工友名字
    imageAndShareUS.setWorkerName("等待筛选接入数据");
    imageAndShareUS.setTimeRangeText(
        "${DateFormat('yyyy-MM-dd').format(us.startTime)} 至 ${DateFormat('yyyy-MM-dd').format(us.endTime)}");
    imageAndShareUS.setQrCodeUrl(data.webUrl + data.path);
  }

  /// 将实体数据转换为UIState
  /// @param entity
  /// @returns
  void _convertEntityToStatisticsUIState(CountBizModel? data) {
    if (data == null) return;
    var count = data;
    List<StatisticsItemUIState> statisticsList =
        StatisticsUIStateHelper.buildStatisticsItem(count);
    us.setStatisticsList(statisticsList);
  }

  void _convertEntityToBillListUIState(
      GroupBusinessGetGroupBusinessListBizModel? data,
      {bool isLoadMore = false}) {
    var list = data?.list;
    if (list != null && list.isNotEmpty) {
      List<GroupBillListUIState> listBill = [];
      for (var element in list) {
        listBill.add(GroupBillListUIState(
            date: element.date,
            billList: _covertEntityToBillItemUIState(element.list)));
      }
      if (isLoadMore) {
        us.addBillList(listBill);
        refreshController.loadComplete();
      } else {
        us.setBillList(listBill);
        refreshController.refreshCompleted();
      }
      //不管刷新还是上拉加载，都要设置hasMore
      var hasMore = list.length >= pageSize;
      us.hasMore = hasMore;
      if (!hasMore) {
        refreshController.loadNoData();
      }
    } else {
      us.hasMore = false;
    }
  }

  List<BillItemUIState> _covertEntityToBillItemUIState(
      List<GroupBusinessGetGroupBusinessListBBizModel> list) {
    List<BillItemUIState> listCount = [];
    for (var element in list) {
      var businessType = RwaRecordType.fromCode(element.businessType.toInt());
      BillItemUIState uiState = BillItemUIState(businessType: businessType);
      uiState.id = element.id;
      uiState.workerName = element.workerName;
      uiState.workerId = element.workerId;
      uiState.typeName =
          StatisticsUIStateHelper.getBusinessTypeName(element.businessType);
      uiState.detail = StatisticsUIStateHelper.getShowContentText(element);
      uiState.feeMoney = getShowMoney(element);
      //分项
      if (element.businessType == RecordType.workLoad.value) {
        uiState.unitWorkTypeName = element.unitWorkTypeName;
      }
      uiState.confirm = element.confirm;
      uiState.hasConfirm = element.confirm != null;
      listCount.add(uiState);
    }
    return listCount;
  }

  getShowMoney(GroupBusinessGetGroupBusinessListBBizModel model) {
    var businessType = model.businessType;
    if (businessType == RecordType.workDays.value ||
        businessType == RecordType.packageWork.value) {
      if ((model.feeStandardId ?? 0) > 0) {
        return model.feeMoney.formatDoubleToMoney();
      }
      return "-";
    }
    if (businessType == RecordType.workLoad.value ||
        businessType == RecordType.dailyWages.value ||
        businessType == RecordType.debt.value ||
        businessType == RecordType.wageLast.value ||
        businessType == RecordType.incomeLast.value ||
        businessType == RecordType.expenditure.value) {
      return model.money.formatStringToMoney();
    }
    return "0.00";
  }

  onSelectAllTap() async {
    if (_netStartTime == null) {
      await fetchFirstTime();
    } else {
      DateTime endTime = DateTime.now();
      _startTime = _netStartTime;
      _endTime = endTime;
      us.setStartTime(_netStartTime);
      us.setEndTime(endTime);
    }
    fetchData();
  }

  ///跳转修改工资规则页面
  void onJumpToEditWageTap(int index) {
    GroupEditWageProps params = GroupEditWageProps()
      ..recordNoteType = RecordNoteType.group
      ..workNoteName = _props?.workNoteName ?? ""
      ..startTime = _startTime
      ..endTime = _endTime
      ..deptId = _props?.deptId
      ..businessType = us.statisticsList[index].recordType
      ..unitWorkType = us.statisticsList[index].unitWorkType
      ..projects = [
        GroupProjectModel(
            workNoteId: _props?.workNoteId, workNoteName: _props?.workNoteName)
      ]
      ..workers = _props?.workers;
    YPRoute.openPage(RouteNameCollection.groupProEditWage, params: params);
  }

  ///跳转修改记工
  void onJumpToEditRecordTap(BillItemUIState item, String data) {
    var params = GroupEditRecordWorkProps()
      ..billId = item.id
      ..date = DateTime.parse(data)
      ..workNoteId = _props?.workNoteId
      ..workNoteName = _props?.workNoteName
      ..businessType = item.businessType
      ..workers = [
        WorkerModel(workerId: item.workerId, workerName: item.workerName)
      ]
      ..deptId = _props?.deptId;

    YPRoute.openPage(RouteNameCollection.groupProEditRecordWork,
        params: params);
  }

  /// 保存图片到本地相册
  /// [imageBytes] 截图的字节数据，由UI层传入
  Future<void> onSaveImageTap(Uint8List imageBytes) async {
    try {
      // 检查存储权限
      bool hasPermission = await PhotoPickerUtil.checkStoragePermission();
      if (!hasPermission) {
        ToastUtil.showToast('需要存储权限才能保存图片');
        return;
      }
      // 保存到相册
      bool success = await PhotoPickerUtil.saveImageToGallery(
        imageBytes,
        fileName: '班组流水统计_${DateTime.now().millisecondsSinceEpoch}.png',
      );
      if (success) {
        ToastUtil.showToast('图片保存成功！');
      }
    } catch (e) {
      yprint('保存图片失败: $e');
    }
  }

  /// 截图功能
  /// [contentKey] RepaintBoundary的GlobalKey
  Future<Uint8List?> captureWidget(GlobalKey contentKey) async {
    try {
      RenderRepaintBoundary? boundary = contentKey.currentContext
          ?.findRenderObject() as RenderRepaintBoundary?;
      if (boundary == null) {
        yprint('找不到RepaintBoundary');
        return null;
      }
      ui.Image image = await boundary.toImage(pixelRatio: 3.0);
      ByteData? byteData =
          await image.toByteData(format: ui.ImageByteFormat.png);
      return byteData?.buffer.asUint8List();
    } catch (e) {
      yprint('截图失败: $e');
      return null;
    }
  }

  /// 分享到微信
  /// [contentKey] RepaintBoundary的GlobalKey
  Future<void> onShareToWechatTap(GlobalKey contentKey) async {
    try {
      Uint8List? imageBytes = await captureWidget(contentKey);
      if (imageBytes == null) {
        return;
      }

      // 使用微信工具类分享图片
      bool success = await WeChatUtil.shareImage(imageData: imageBytes);
      if (!success) {
        ToastUtil.showToast('分享失败');
      }
    } catch (e) {
      yprint('分享到微信失败: $e');
    }
  }

  /// 保存到手机
  /// [contentKey] RepaintBoundary的GlobalKey
  Future<void> onSaveToPhoneTap(GlobalKey contentKey) async {
    try {
      // 截图
      Uint8List? imageBytes = await captureWidget(contentKey);
      if (imageBytes == null) {
        return;
      }
      await onSaveImageTap(imageBytes);
    } catch (e) {
      yprint('保存到手机失败: $e');
    }
  }
}
