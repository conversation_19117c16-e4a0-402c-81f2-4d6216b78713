import 'package:get/get.dart';

/// 生成图片和分享弹窗
class ImageAndShareUS {
  final _isShowPopView = true.obs;
  final _title = ''.obs;
  final _projectName = ''.obs;
  final _workerName = ''.obs;
  final _timeRangeText = ''.obs;
  final _qrCodeUrl = ''.obs;

  bool get isShowPopView => _isShowPopView.value;

  String get title => _title.value;

  String get projectName => _projectName.value;

  String get workerName => _workerName.value;

  String get timeRangeText => _timeRangeText.value;

  String get qrCodeUrl => _qrCodeUrl.value;

  void setIsShowPopView(bool isShow) {
    _isShowPopView.value = isShow;
  }

  void setTitle(String title) {
    _title.value = title;
  }

  void setProjectName(String projectName) {
    _projectName.value = projectName;
  }

  void setWorkerName(String workerName) {
    _workerName.value = workerName;
  }

  void setTimeRangeText(String timeRangeText) {
    _timeRangeText.value = timeRangeText;
  }

  void setQrCodeUrl(String qrCodeUrl) {
    _qrCodeUrl.value = qrCodeUrl;
  }
}
