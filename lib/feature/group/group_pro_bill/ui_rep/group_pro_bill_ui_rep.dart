import 'package:gdjg_pure_flutter/data/group_data/group_calendar/repo/model/group_calendar_biz_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_pro_bill/ds/model/param/group_business_get_group_business_count_param_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_pro_bill/ds/model/param/group_business_get_group_business_list_param_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_pro_bill/repo/group_pro_bill_repo.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_pro_bill/repo/model/group_business_get_group_business_list_biz_model.dart';
import 'package:get/get.dart';

/// @date 2025/06/26
/// @description GroupProBill页UI仓库
/// 1.为vm提供业务数据
/// 2.解耦视图层与业务仓库之间的耦合
class GroupProBillUIRep {
  final _groupProBillRepo = GroupProBillRepo();

  /// 实体数据
  var entity = GroupProBillUIRepEntity().obs;

  Future<GroupProBillUIRepEntity> getCombinedData(
      GroupBusinessGetGroupBusinessCountParamModel countParams,
      GroupBusinessGetGroupBusinessListParamModel listParams) async {
    final results = await Future.wait([
      _groupProBillRepo.getGroupBusinessCount(countParams),
      _groupProBillRepo.getGroupBusinessList(listParams),
    ]);

    final countResult = results[0];
    final listResult = results[1];

    if (countResult.isOK()) {
      CountBizModel? data = countResult.getSucData() as CountBizModel?;
      if (data != null) {
        entity.value.count = data;
      }
    }

    if (listResult.isOK()) {
      GroupBusinessGetGroupBusinessListBizModel? data = listResult.getSucData() as GroupBusinessGetGroupBusinessListBizModel?;
      if (data != null) {
        entity.value.list = data.list;
      }
    }
    entity.refresh();
    return entity.value;
  }

  Future<GroupProBillUIRepEntity> getGroupBusinessList(
      GroupBusinessGetGroupBusinessListParamModel params) async {
    // 调用网络的方法获取数据
    var result = await _groupProBillRepo.getGroupBusinessList(params);
    if (result.isOK()) {
      GroupBusinessGetGroupBusinessListBizModel? data = result.getSucData();
      if (data != null) {
        entity.value.list = data.list;
        entity.refresh();
      }
    } else {
      // 模拟异常情况
      // throw Exception("error");
    }
    return entity.value;
  }
}


class GroupProBillUIRepEntity {
  CountBizModel? count;

  List<GroupBusinessGetGroupBusinessListABizModel>? list;

  GroupProBillUIRepEntity({this.count, this.list});

  @override
  String toString() {
    return 'GroupProBillUIRepEntity{data: $count}';
  }
}
