import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/feature/group/common_params_model/pro_model.dart';
import 'package:gdjg_pure_flutter/feature/group/group_edit_wage/entity/group_edit_wage_props.dart';
import 'package:gdjg_pure_flutter/feature/group/group_pro_bill/view/group_bill_list_view.dart';
import 'package:gdjg_pure_flutter/feature/group/group_pro_bill/view/group_bill_statistics_view.dart';
import 'package:gdjg_pure_flutter/feature/group/group_statistics/view/empty_view.dart';
import 'package:gdjg_pure_flutter/init_module/init_route.dart';
import 'package:gdjg_pure_flutter/model/RecordNoteType.dart';
import 'package:gdjg_pure_flutter/utils/route_util/base_page/base_ful_page.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/appbar_util.dart';
import 'package:gdjg_pure_flutter/widget/combined_filter_widget.dart';
import 'package:get/get.dart';

import 'entity/group_pro_bill_props.dart';
import 'vm/group_pro_bill_vm.dart';

class GroupProBillPage extends BaseFulPage {
  const GroupProBillPage({super.key}) : super(appBar: null);

  @override
  createState() => _GroupProBillPage();
}

class _GroupProBillPage<GroupProBillPage> extends BaseFulPageState {
  final GroupProBillViewModel viewModel = GroupProBillViewModel();

  @override
  onPageRoute(Object? routeParams, bool fromLaunchTask) {
    var props = routeParams as GroupProBillProps?;
    viewModel.init(props);
  }

  @override
  Widget yBuild(BuildContext context) {
    return Scaffold(
      appBar: AppBarUtil.buildCommonAppBar(
        title: viewModel.props?.workNoteName ?? "",
        onBackTap: () => YPRoute.closePage(),
      ),
      body: contentView(),
    );
  }

  Widget contentView() {
    return Column(
      children: [
        CombinedFilterWidget(
          onFilterChanged: (filter) {
            viewModel.updateDateRange(
                filter.startDate, filter.endDate, filter.selectedTypes);
          },
        ),
        Divider(height: 1.h, color: Color(0xFFF0F0F0), thickness: 1.h),
        Divider(height: 10.h, color: Color(0xFFFFFFFF), thickness: 10.h),
        Expanded(
          child: Obx(() {
            if (viewModel.uiState.value.isEmpty) {
              return Container(
                color: Colors.white,
                child: Center(
                  child: EmptyView(
                    subtitle: '当月无记工',
                    imagePath: 'https://cdn.yupaowang.com/jgjz/empty-note.png',
                  ),
                ),
              );
            }
            return SingleChildScrollView(
              child: Column(children: [
                _buildStatisticsView(),
                Divider(
                    height: 10.h, color: Color(0xFFFFFFFF), thickness: 10.h),
                _buildBillListView()
              ]),
            );
          }),
        ),
        _buildBottomView(),
      ],
    );
  }

  Widget _buildStatisticsView() {
    return Container(
      color: Colors.white,
      child: GroupBillStatisticsView(
        list: viewModel.uiState.value.statisticsList,
        onEditWageTap: (index) {
          viewModel.jumpToEditWage(index);
        },
      ),
    );
  }

  Widget _buildBillListView() {
    return GroupBillListView(
      list: viewModel.uiState.value.billList,
      onItemTap: (index) {},
    );
  }

  Widget _buildBottomView() {
    return Column(
      children: [
        Divider(height: 2.h, color: Color(0xFFF0F0F0), thickness: 2.h),
        Container(
          color: Colors.white,
          padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
          child: Row(
            children: [
              Expanded(
                child: Text(
                  "分享时可选择\n是否展示工资",
                  style: TextStyle(
                    fontSize: 16.sp,
                    color: Color(0xFF323232),
                  ),
                ),
              ),
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  _buildFunctionButton(
                    '下载',
                    'https://cdn.yupaowang.com/jgjz/ic_personal_bill_detail_download.png',
                    () {},
                  ),
                  SizedBox(width: 16.w),
                  _buildFunctionButton(
                    '生成图片',
                    'https://cdn.yupaowang.com/jgjz/ic_personal_bill_detail_create_img.png',
                    () {},
                  ),
                  SizedBox(width: 16.w),
                  _buildFunctionButton(
                    '微信对工',
                    'https://cdn.yupaowang.com/jgjz/ic_personal_bill_detail_wechat.png',
                    () {},
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildFunctionButton(
      String name, String imageUrl, VoidCallback onPressed) {
    return GestureDetector(
      onTap: onPressed,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Image.network(
            imageUrl,
            width: 24.w,
            height: 24.w,
          ),
          SizedBox(height: 4.h),
          Text(
            name,
            style: TextStyle(
              fontSize: 12.sp,
              color: Color(0xFF666666),
            ),
          ),
        ],
      ),
    );
  }
}
