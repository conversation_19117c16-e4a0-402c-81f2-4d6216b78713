import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/data/group/group_statistics/ds/model/param/cors_note_group_record_work_statistics_v2_param_model.dart';
import 'package:gdjg_pure_flutter/feature/group/group_statistics/view/empty_view.dart';
import 'package:gdjg_pure_flutter/utils/system_util/font_util.dart';
import 'package:get/get.dart';
import 'package:gdjg_pure_flutter/widget/page_error_view.dart';
import 'package:gdjg_pure_flutter/widget/page_loading_view.dart';
import '../../../utils/ui_util/colors_util.dart';
import 'entity/group_statistics_props.dart';
import 'vm/group_statistics_viewmodel.dart';

/// 班组-统计
class GroupStatisticsPage extends StatelessWidget {
  GroupStatisticsPage({super.key, this.props});

  final GroupStatisticsProps? props;
  final GroupStatisticsViewModel viewModel =
      GroupStatisticsViewModel(CorsNoteGroupRecordWorkStatisticsV2ParamModel(
    start_business_time: "2025-05-06",
    end_business_time: "2025-06-24",
    business_type: "",
    worker_id: "",
  ));
  String selectedDateRange = '2025-05-06 至 2025-06-18';
  String selectedWorker = '全部工友';
  String selectedType = '类型';
  bool hasWorkerFilter = false;
  bool hasTypeFilter = false;
  bool isEmptyData = false; // 用于测试空数据状态

  // 模拟数据
  final List<WorkerStatistics> workerStatistics = [
    WorkerStatistics(
      name: '默认工人',
      items: [
        StatisticsItem(
          type: '点工',
          detail: '上班:0.5个工\n加班:5小时',
          amount: '0.00',
          isWork: true,
        ),
        StatisticsItem(
            type: '工量',
            detail: '',
            amount: '16.00',
            totalInfo: '总计:8',
            isWork: true,
            num: '2笔',
            unitWorkTypeName: '分项2'),
        StatisticsItem(
          type: '未结',
          detail: '',
          amount: '16.00',
          isWork: false,
        ),
      ],
    ),
    WorkerStatistics(
      name: '工人1',
      items: [
        StatisticsItem(
          type: '点工',
          detail: '上班:1个工',
          amount: '0.00',
          isWork: true,
        ),
        StatisticsItem(
          type: '未结',
          detail: '',
          amount: '0.00',
          isWork: false,
        ),
      ],
    ),
    WorkerStatistics(
      name: '工友2',
      items: [
        StatisticsItem(
          type: '点工',
          detail: '上班:1个工',
          amount: '0.00',
          isWork: true,
        ),
        StatisticsItem(
          type: '短工',
          detail: '',
          amount: '288.00',
          isWork: false,
        ),
        StatisticsItem(
          type: '未结',
          detail: '',
          amount: '288.00',
          isWork: false,
        ),
      ],
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF0F0F0),
      body: Obx(() {
        if (viewModel.isLoading.value) {
          return const PageLoadingView();
        }
        if (viewModel.uiState.value.isShowError == true) {
          return PageErrorView(onReload: () => viewModel.fetchData(CorsNoteGroupRecordWorkStatisticsV2ParamModel(
            start_business_time: "2025-05-06",
            end_business_time: "2025-06-24",
            business_type: "",
            worker_id: "",
          )));
        }
        return contentView();
      }),
    );
  }

  /// 实际展示的视图，需要处理：
  /// 1. 画出UI
  /// 2. 绑定UIState
  /// 3. 处理事件监听
  /// 4. 向VM传递来自View层的事件
  Widget contentView() {
    // 进行事件处理
    // handleGroupStatisticsVMEvent(props.vm)
    return Column(
      children: [
        /// 时间筛选组件
        _buildDateRangeFilter(),

        /// 工友总计标题和内容
        Expanded(
          child: _buildMainContent(),
        ),
      ],
    );
  }

  /// 顶部时间筛选组件
  Widget _buildDateRangeFilter() {
    return Container(
        margin: EdgeInsets.only(top: 8.h),
        width: double.infinity,
        height: 86.h,
        color: Colors.blue,
        child: Text(
          '统计时间组件',
        ));
  }

  /// 构建主要内容区域
  Widget _buildMainContent() {
    // 判断是否显示空数据状态
    final hasData = !isEmptyData && workerStatistics.isNotEmpty;

    return Container(
      margin: EdgeInsets.only(top: 8.h),
      color: Colors.white,
      child: Column(
        children: [
          // 工友总计标题
          _buildTotalTitle(),

          // 内容区域
          Expanded(
            child: hasData ? _buildWorkerStatisticsList() : _buildEmptyState(),
          ),
        ],
      ),
    );
  }

  /// 构建空数据状态
  Widget _buildEmptyState() {
    return const EmptyView(
      subtitle: '暂无数据',
      imagePath: 'https://cdn.yupaowang.com/jgjz/empty-note.png',
    );
  }

  /// 构建工友总计标题
  Widget _buildTotalTitle() {
    return SizedBox(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            margin: EdgeInsets.only(top: 11.h, bottom: 9.h),
            child: Text(
              '工友总计',
              style: TextStyle(
                fontSize: 16.sp,
                fontWeight: FontWeight.w600,
                color: ColorsUtil.primaryColor,
              ),
            ),
          ),
          Container(
            width: 20.w,
            height: 4.h,
            decoration: BoxDecoration(
              color: ColorsUtil.primaryColor,
              borderRadius: BorderRadius.circular(2.r),
            ),
          )
        ],
      ),
    );
  }

  /// 构建工友统计列表
  Widget _buildWorkerStatisticsList() {
    return Column(
      children: [
        // 列表内容
        Expanded(
          child: ListView.builder(
            padding: EdgeInsets.zero,
            itemCount: workerStatistics.length,
            itemBuilder: (context, index) {
              final worker = workerStatistics[index];
              return _buildWorkerItem(worker);
            },
          ),
        ),
      ],
    );
  }

  /// 构建工友项目
  Widget _buildWorkerItem(WorkerStatistics worker) {
    return Container(
      color: const Color(0xFFF0F0F0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 工友姓名
          Container(
            width: double.infinity,
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 10.h),
            child: Text(
              worker.name,
              style: TextStyle(
                fontSize: 16.sp,
                color: const Color(0xFF323233),
              ),
            ),
          ),
          // 工友统计项目
          ...worker.items.map((item) => _buildStatisticsItem(item)).toList(),
        ],
      ),
    );
  }

  /// 构建每项item
  Widget _buildStatisticsItem(StatisticsItem item) {
    return Container(
      color: Colors.white,
      child: Column(
        children: [
          Container(
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 10.h),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                // 左侧颜色指示条
                Container(
                  width: 3.w,
                  height: 16.h,
                  margin: EdgeInsets.only(right: 8.w),
                  decoration: BoxDecoration(
                    color: item.isWork // todo:根据记工类型修改
                        ? ColorsUtil.primaryColor
                        : const Color(0xFFFFA011),
                    borderRadius: BorderRadius.circular(1.5.w),
                  ),
                ),
                // 类型和笔数
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      item.type,
                      style: TextStyle(
                        fontSize: 16.sp,
                        color: const Color(0xFF323233),
                      ),
                    ),
                    if (item.num != null)
                      Text(
                        item.num ?? '',
                        style: TextStyle(
                          fontSize: 15.sp,
                          color: const Color(0xFF9D9DB3),
                        ),
                      ),
                  ],
                ),
                // 详情
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // 分项
                      if (item.unitWorkTypeName != null)
                        Container(
                          margin: EdgeInsets.only(left: 4.w),
                          child: Text(
                            item.unitWorkTypeName ?? '',
                            style: TextStyle(
                              fontSize: 16.sp,
                              color: const Color(0xFF323233),
                            ),
                          ),
                        ),
                      // 上班和加班详情
                      Container(
                        margin: EdgeInsets.only(left: 8.w),
                        child: Text(
                          item.detail,
                          style: TextStyle(
                            fontSize: 15.sp,
                            color: const Color(0xFF323233),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                // 右侧金额
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Text(
                          item.amount,
                          style: TextStyle(
                            fontFamily: FontUtil.fontCondMedium,
                            fontSize: 22.sp,
                            fontWeight: FontWeight.w600,
                            color: item.isWork
                                ? ColorsUtil.primaryColor
                                : const Color(0xFFFFB84D),
                          ),
                        ),
                        // 总计信息
                        if (item.totalInfo != null) ...[
                          Text(
                            item.totalInfo!,
                            style: TextStyle(
                              fontSize: 15.sp,
                              color: const Color(0xFF323233),
                            ),
                          ),
                        ],
                      ],
                    ),
                  ],
                ),
              ],
            ),
          ),
          // 分割线
          Container(
            height: 0.5.h,
            color: const Color(0xFFE6E6E6),
          ),
        ],
      ),
    );
  }
}

/// 工友统计数据模型
class WorkerStatistics {
  final String name;
  final List<StatisticsItem> items;

  WorkerStatistics({
    required this.name,
    required this.items,
  });
}

/// 统计项目数据模型
class StatisticsItem {
  final String type;
  final String detail;
  final String amount;
  final String? totalInfo;
  final bool isWork;
  final String? num;
  final String? unitWorkTypeName;

  StatisticsItem({
    required this.type,
    required this.detail,
    required this.amount,
    this.totalInfo,
    required this.isWork,
    this.num,
    this.unitWorkTypeName,
  });
}
