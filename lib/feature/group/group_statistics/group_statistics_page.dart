import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_statistics/ds/model/param/cors_note_group_record_work_statistics_v2_param_model.dart';
import 'package:gdjg_pure_flutter/feature/group/group_statistics/view/empty_view.dart';
import 'package:gdjg_pure_flutter/utils/route_util/base_page/base_ful_page.dart';
import 'package:gdjg_pure_flutter/utils/system_util/font_util.dart';
import 'package:gdjg_pure_flutter/widget/combined_filter_widget.dart';
import 'package:get/get.dart';
import 'package:gdjg_pure_flutter/widget/page_error_view.dart';
import '../../../common_uistate/statistics_ui_state.dart';
import '../../../utils/ui_util/colors_util.dart';
import '../group_com_uistate/group_worker_statistics_ui_state.dart';
import 'view/group_project_select_dialog.dart';
import 'vm/group_statistics_vm.dart';

/// 班组-统计
class GroupStatisticsPage extends BaseFulPage {
  const GroupStatisticsPage({super.key}) : super(appBar: null);

  @override
  State<StatefulWidget> createState() {
    return _GroupStatisticPageState();
  }
}

class _GroupStatisticPageState extends BaseFulPageState {
  final GroupStatisticsVM viewModel =
      GroupStatisticsVM();
  final List<ProjectSelectItem> sampleData = [
    ProjectSelectItem(tag: '住宅', name: 'XX小区项目', idx: 1),
    ProjectSelectItem(tag: '公建', name: '市图书馆项目', idx: 2),
    ProjectSelectItem(tag: '商业', name: '万达广场', idx: 3),
    ProjectSelectItem(tag: '住宅', name: 'XX小区项目', idx: 4),
    ProjectSelectItem(tag: '公建', name: '市图书馆项目', idx: 5),
    ProjectSelectItem(tag: '商业', name: '万达广场', idx: 6),
    ProjectSelectItem(tag: '住宅', name: 'XX小区项目', idx: 7),
    ProjectSelectItem(tag: '公建', name: '市图书馆项目', idx: 8),
    ProjectSelectItem(tag: '商业', name: '万达广场', idx: 9),
  ];
  @override
  void initState() {
    super.initState();
    // 延迟触发初始化回调，确保 CombinedFilterWidget 已经构建完成
    WidgetsBinding.instance.addPostFrameCallback((_) {
      viewModel.triggerInitialCallback((startDate, endDate) {
      });
    });
  }

  @override
  Widget yBuild(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF0F0F0),
      body: Obx(() {
        if (viewModel.us.value.isShowError == true) {
          return PageErrorView(
              onReload: () => viewModel
                      .fetchData(CorsNoteGroupRecordWorkStatisticsV2ParamModel(
                    start_business_time: "2025-05-06",
                    end_business_time: "2025-06-24",
                    business_type: "",
                    worker_id: "",
                  )));
        }
        return _contentView();
      }),
    );
  }

  /// 构建页面内容
  Widget _contentView() {
    return Column(
      children: [
        /// 时间筛选组件
        _buildDateRangeFilter(),

        /// 工友总计标题和内容
        Expanded(
          child: _buildMainContent(),
        ),
      ],
    );
  }

  /// 顶部时间筛选组件
  Widget _buildDateRangeFilter() {
    return  CombinedFilterWidget(
      onFilterChanged: (filter) {
        viewModel.updateDateRange(filter.startDate, filter.endDate);
        viewModel.updateSelectType(filter.selectedTypes);
      },
    );
  }

  /// 构建主要内容区域
  Widget _buildMainContent() {
    // 判断是否显示空数据状态
    final hasData = viewModel.us.value.data?.isNotEmpty;
    return Container(
      margin: EdgeInsets.only(top: 2.h),
      color: Colors.white,
      child: Column(
        children: [
          // 工友总计标题
          _buildTotalTitle(),

          // 内容区域
          Expanded(
            child: hasData == true
                ? _buildWorkerStatisticsList()
                : _buildEmptyState(),
          ),
        ],
      ),
    );
  }

  /// 构建空数据状态
  Widget _buildEmptyState() {
    return const EmptyView(
      subtitle: '暂无数据',
      imagePath: 'https://cdn.yupaowang.com/jgjz/empty-note.png',
    );
  }

  /// 构建工友总计标题
  Widget _buildTotalTitle() {
    return GestureDetector(
      onTap: () {
        GroupProjectSelectDialog.show(
            context,
            data: sampleData,
            onSelected: (selectedItem) {
          // 处理用户选择的项
          print('用户选择了: ${selectedItem.name}');
          // 例如，可以使用 SmartDialog.showToast 来显示提示
          // SmartDialog.showToast('选择了: ${selectedItem.name}');
        });
      },
      child: SizedBox(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              margin: EdgeInsets.only(top: 11.h, bottom: 9.h),
              child: Text(
                '工友总计',
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w600,
                  color: ColorsUtil.primaryColor,
                ),
              ),
            ),
            Container(
              width: 20.w,
              height: 4.h,
              decoration: BoxDecoration(
                color: ColorsUtil.primaryColor,
                borderRadius: BorderRadius.circular(2.r),
              ),
            )
          ],
        ),
      ),
    );
  }

  /// 构建工友统计列表
  Widget _buildWorkerStatisticsList() {
    return Column(
      children: [
        // 列表内容
        Expanded(
          child: ListView.builder(
            padding: EdgeInsets.zero,
            itemCount: viewModel.us.value.data?.length ?? 0,
            itemBuilder: (context, index) {
              final worker = viewModel.us.value.data?[index];
              return _buildWorkerItem(worker);
            },
          ),
        ),
      ],
    );
  }

  /// 构建工友项目
  Widget _buildWorkerItem(GroupWorkerStatisticsUIState? uiState) {
    return Container(
      color: const Color(0xFFF0F0F0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 工友姓名
          Container(
            width: double.infinity,
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 10.h),
            child: Text(
              uiState?.workerName ?? '',
              style: TextStyle(
                fontSize: 16.sp,
                color: const Color(0xFF323233),
              ),
            ),
          ),
          // 工友统计项目
          ...?uiState?.statisticsItemList
              ?.map((item) => _buildStatisticsItem(item)),
        ],
      ),
    );
  }

  /// 构建每项item
  Widget _buildStatisticsItem(StatisticsItemUIState itemUIState) {
    return Container(
      color: Colors.white,
      child: Column(
        children: [
          Container(
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 10.h),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                // 左侧颜色指示条
                Container(
                  width: 3.w,
                  height: 16.h,
                  margin: EdgeInsets.only(right: 8.w),
                  decoration: BoxDecoration(
                    color: itemUIState.isRecordWorkType == true // todo:根据记工类型修改
                        ? ColorsUtil.primaryColor
                        : const Color(0xFFFFA011),
                    borderRadius: BorderRadius.circular(1.5.w),
                  ),
                ),
                // 类型和笔数
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      itemUIState.typeName ?? '',
                      style: TextStyle(
                        fontSize: 16.sp,
                        color: const Color(0xFF323233),
                      ),
                    ),
                    if (itemUIState.unitWorkNum != null)
                      Text(
                        itemUIState.unitWorkNum ?? '',
                        style: TextStyle(
                          fontSize: 15.sp,
                          color: const Color(0xFF9D9DB3),
                        ),
                      ),
                  ],
                ),
                // 详情
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // 分项
                      if (itemUIState.unitWorkTypeName != null)
                        Container(
                          margin: EdgeInsets.only(left: 4.w),
                          child: Text(
                            itemUIState.unitWorkTypeName ?? '',
                            style: TextStyle(
                              fontSize: 16.sp,
                              color: const Color(0xFF323233),
                            ),
                          ),
                        ),
                      // 上班和加班详情
                      Container(
                        margin: EdgeInsets.only(left: 8.w),
                        child: Text(
                          itemUIState.detail ?? '',
                          style: TextStyle(
                            fontSize: 15.sp,
                            color: const Color(0xFF323233),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                // 右侧金额
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Text(
                          itemUIState.feeMoney ?? '',
                          style: TextStyle(
                            fontFamily: FontUtil.fontCondMedium,
                            fontSize: 22.sp,
                            fontWeight: FontWeight.w600,
                            color: itemUIState.isRecordWorkType == true
                                ? ColorsUtil.primaryColor
                                : const Color(0xFFFFB84D),
                          ),
                        ),
                        // 总计信息
                        if (itemUIState.total != null) ...[
                          Text(
                            itemUIState.total!,
                            style: TextStyle(
                              fontSize: 15.sp,
                              color: const Color(0xFF323233),
                            ),
                          ),
                        ],
                      ],
                    ),
                  ],
                ),
              ],
            ),
          ),
          // 分割线
          Container(
            height: 0.5.h,
            color: const Color(0xFFE6E6E6),
          ),
        ],
      ),
    );
  }
}
