import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/feature/group/common_uistate/statistics_ui_state.dart';
import 'package:gdjg_pure_flutter/feature/group/group_com_uistate/group_worker_statistics_ui_state.dart';
import 'package:gdjg_pure_flutter/feature/group/group_statistics/view/empty_view.dart';
import 'package:gdjg_pure_flutter/utils/route_util/base_page/base_ful_page.dart';
import 'package:gdjg_pure_flutter/utils/system_util/font_util.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';
import 'package:gdjg_pure_flutter/widget/combined_filter_widget.dart';
import 'package:get/get.dart';
import 'package:gdjg_pure_flutter/widget/page_error_view.dart';
import 'vm/group_statistics_vm.dart';

/// 班组-统计
class GroupStatisticsPage extends BaseFulPage {
  const GroupStatisticsPage({super.key}) : super(appBar: null);

  @override
  State<StatefulWidget> createState() {
    return _GroupStatisticPageState();
  }
}

class _GroupStatisticPageState extends BaseFulPageState {
  final GroupStatisticsVM vm = GroupStatisticsVM();
  @override
  void initState() {
    super.initState();
    // 延迟触发初始化回调，确保 CombinedFilterWidget 已经构建完成
    WidgetsBinding.instance.addPostFrameCallback((_) {
      vm.triggerInitialCallback((startDate, endDate) {
      });
    });
  }

  @override
  Widget yBuild(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF0F0F0),
      body: Obx(() {
        if (vm.us.isShowError) {
          return PageErrorView(onReload: () => vm.fetchGroupStatisticsData());
        }
        return _contentView();
      }),
    );
  }

  /// 构建页面内容
  Widget _contentView() {
    return Column(
      children: [
        /// 时间筛选组件
        SizedBox(height: 8.h),
        _buildDateRangeFilter(),

        /// 工友总计标题和内容
        Expanded(
          child: _buildMainContent(),
        ),
      ],
    );
  }

  /// 顶部时间筛选组件
  Widget _buildDateRangeFilter() {
    return  CombinedFilterWidget(
      onFilterChanged: (filter) {
        vm.updateDateRange(filter.startDate, filter.endDate);
        vm.updateSelectType(filter.selectedTypes);
      },
    );
  }

  /// 构建主要内容区域
  Widget _buildMainContent() {
    // 判断是否显示空数据状态
    final hasData = vm.us.groupStatisticsData.isNotEmpty;
    return Container(
      margin: EdgeInsets.only(top: 2.h),
      color: Colors.white,
      child: Column(
        children: [
          // 工友总计标题
          _buildTotalTitle(),

          // 内容区域
          Expanded(
            child: hasData
                ? _buildWorkerStatisticsList()
                : _buildEmptyState(),
          ),
        ],
      ),
    );
  }

  /// 构建空数据状态
  Widget _buildEmptyState() {
    return const EmptyView(
      subtitle: '暂无数据',
      imagePath: 'https://cdn.yupaowang.com/jgjz/empty-note.png',
    );
  }

  /// 构建工友总计标题
  Widget _buildTotalTitle() {
    return SizedBox(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            margin: EdgeInsets.only(top: 11.h, bottom: 9.h),
            child: Text(
              '工友总计',
              style: TextStyle(
                fontSize: 16.sp,
                fontWeight: FontWeight.w600,
                color: ColorsUtil.primaryColor,
              ),
            ),
          ),
          Container(
            width: 20.w,
            height: 4.h,
            decoration: BoxDecoration(
              color: ColorsUtil.primaryColor,
              borderRadius: BorderRadius.circular(2.r),
            ),
          )
        ],
      ),
    );
  }

  /// 构建工友统计列表
  Widget _buildWorkerStatisticsList() {
    return Column(
      children: [
        // 列表内容
        Expanded(
          child: ListView.builder(
            padding: EdgeInsets.zero,
            itemCount: vm.us.groupStatisticsData.length,
            itemBuilder: (context, index) {
              final worker = vm.us.groupStatisticsData[index];
              return _buildWorkerItem(worker);
            },
          ),
        ),
      ],
    );
  }

  /// 构建工友项目
  Widget _buildWorkerItem(GroupWorkerStatisticsUIState uiState) {
    return Container(
      color: const Color(0xFFF0F0F0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 工友姓名
          Container(
            width: double.infinity,
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 10.h),
            child: Text(
              uiState.workerName ?? '',
              style: TextStyle(
                fontSize: 16.sp,
                color: const Color(0xFF323233),
              ),
            ),
          ),
          // 工友统计项目
          ...uiState.statisticsItemList
              ?.map((item) => _buildStatisticsItem(item,uiState.workerName,uiState.workerId)) ?? [],
        ],
      ),
    );
  }

  /// 构建每项item
  Widget _buildStatisticsItem(StatisticsItemUIState uiState,String? workerName,double? workerId) {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () {
        if(workerName!=null&&workerName!=''&&workerId!=null){
          vm.onStatisticsItemTap(context, uiState,workerName,workerId);
        }
      },
      child: Container(
        color: Colors.white,
        child: Column(
          children: [
            Container(
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 10.h),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  // 左侧颜色指示条
                  Container(
                    width: 3.w,
                    height: 16.h,
                    margin: EdgeInsets.only(right: 8.w),
                    decoration: BoxDecoration(
                      color: uiState.isRecordWorkType == true
                          ? ColorsUtil.primaryColor
                          : const Color(0xFFFFA011),
                      borderRadius: BorderRadius.circular(1.5.w),
                    ),
                  ),
                  // 类型和笔数
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        uiState.typeName ?? '',
                        style: TextStyle(
                          fontSize: 16.sp,
                          color: const Color(0xFF323233),
                        ),
                      ),
                      if (uiState.workNum != null)
                        Text(
                          uiState.workNum ?? '',
                          style: TextStyle(
                            fontSize: 15.sp,
                            color: const Color(0xFF9D9DB3),
                          ),
                        ),
                    ],
                  ),
                  // 详情
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // 分项
                        if (uiState.unitWorkTypeName != null)
                          Container(
                            margin: EdgeInsets.only(left: 4.w),
                            child: Text(
                              uiState.unitWorkTypeName ?? '',
                              style: TextStyle(
                                fontSize: 16.sp,
                                color: const Color(0xFF323233),
                              ),
                            ),
                          ),
                        // 上班和加班详情
                        Container(
                          margin: EdgeInsets.only(left: 8.w),
                          child: Text(
                            uiState.detail ?? '',
                            style: TextStyle(
                              fontSize: 15.sp,
                              color: const Color(0xFF323233),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  // 右侧金额
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          Text(
                            uiState.feeMoney ?? '',
                            style: TextStyle(
                              fontFamily: FontUtil.fontCondMedium,
                              fontSize: 22.sp,
                              fontWeight: FontWeight.w600,
                              color: uiState.isRecordWorkType == true
                                  ? ColorsUtil.primaryColor
                                  : const Color(0xFFFFB84D),
                            ),
                          ),
                          // 总计信息
                          if (uiState.total != null) ...[
                            Text(
                              uiState.total!,
                              style: TextStyle(
                                fontSize: 15.sp,
                                color: const Color(0xFF323233),
                              ),
                            ),
                          ],
                        ],
                      ),
                    ],
                  ),
                ],
              ),
            ),
            // 分割线
            Container(
              height: 0.5.h,
              color: const Color(0xFFE6E6E6),
            ),
          ],
        ),
      ),
    );
  }
}
