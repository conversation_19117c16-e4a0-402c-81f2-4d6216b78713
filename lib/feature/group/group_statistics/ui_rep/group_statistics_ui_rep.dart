
import 'dart:convert';
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:dio/io.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:gdjg_pure_flutter/data/group/group_statistics/ds/model/net/cors_note_group_record_work_statistics_v2_biz_model.dart';
import 'package:gdjg_pure_flutter/data/group/group_statistics/ds/model/net/cors_note_group_record_work_statistics_v2_net_model.dart';
import 'package:gdjg_pure_flutter/data/group/group_statistics/ds/model/param/cors_note_group_record_work_statistics_v2_param_model.dart';
import 'package:get/get.dart'as getx;

/// @date 2025/06/21
/// @description GroupStatistics页UI仓库
class GroupStatisticsUIRep {
  /// 实体数据
  var entity = GroupStatisticsUIRepEntity().obs;

  // Future<GroupStatisticsUIRepEntity> fetchGroupStatisticsListData(
  //     CorsNoteGroupRecordWorkStatisticsV2ReqEntity req) async {
  //   var result = await GroupStatisticsListRepo().queryGroupStatisticsList(req);
  //   // 返回成功的情况
  //   entity.value = GroupStatisticsUIRepEntity(data: result.success?.data?.data);
  //   return entity.value;
  // }
  Future<CorsNoteGroupRecordWorkStatisticsV2BizModel?> fetchGroupStatisticsListData(CorsNoteGroupRecordWorkStatisticsV2ParamModel req) async {
    try {
      var token =
          "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpYXQiOjE3NTA0NzY1NTEsImV4cCI6MTc2MDg0NDU1MSwiZGF0YSI6eyJzaW5nbGUiOiI5UVE4M0ZITTNLS1JVS0FaIiwidWlkIjoxODE4OTI0ODUsImJ1c2luZXNzIjoiMiIsInN5c3RlbV90eXBlIjoiYW5kcm9pZCIsIm1pbmlfdG9rZW4iOiI5UVE4M0ZITTNLS1JVS0FaIiwiaWQiOjE4MTg5MjQ4NSwidXVpZCI6MTgxODkyNDg1fSwidG9rZW4iOnsicmVnUnQiOiJhbmRyb2lkIiwidGVuYW50S2V5IjoiSkdKWiIsInRlbmFudElkIjoxODE4OTI0ODUsInBhY2thZ2VOYW1lIjoiY29tLnl1cGFvLmdvbmdkaWppZ29uZyIsInVzZXJJZCI6MTgxODkyNDg1LCJ0b2tlbiI6IjlRUTgzRkhNM0tLUlVLQVoifX0.KTF6jTtv7Kxi9WARb5Wkba4MSHLMkaDseSXZsK8aUXs";
      var jgjztoken =
      "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJwcm9kLmxvZ2luIiwiaWF0IjoxNzUwNDc2NTUxLCJ1aWQiOjE4MTg5MjQ4NX0.0vs8F6gW1gkz7pE7SPjvMiujDIh5S2wd67CNU8GeGdA";
      Dio dio = Dio(BaseOptions(
        baseUrl: "https://app.cdmgkj.cn",
        connectTimeout: const Duration(seconds: 10),
        receiveTimeout: const Duration(seconds: 10),
        headers: {
          'Content-Type': 'application/json',
          'jgjztoken': jgjztoken,
          'singletoken': token,
          'uuid': '181892485',
          'uid': '181892485',
          'deviceuuid': 'f6b33dc1a5569cd2',
          'imei': 'f6b33dc1a5569cd2',
          'env': 'PRO',
          'channel': 'authority',
          'user-agent': 'YP JGJZ HUAWEI JEF-AN00 12 6.5.2 f6b33dc1a5569cd2 1750728158444',
          'business': '2',
          'package_name': 'gdjg',
          'version': '6.5.2',
          'device': 'HUAWEI,JEF-AN00',
          'versioncode': '652',
          'system_type': 'android',
          'system': 'android',
          'systemversion': '12',
          'apiversion': '3.0',
          'token': token,
          'source': 'agd',
          'oaid': '298cc3d4-c532-4203-841a-6861d5eb9143',
        },
      ));
      if (kDebugMode) {
        dio.httpClientAdapter = IOHttpClientAdapter()
          ..createHttpClient = () {
            var httpClient = HttpClient();
            // httpClient.findProxy = (uri) => "PROXY ************:8889";
            httpClient.badCertificateCallback =
                (X509Certificate cert, String host, int port) => true;
            return httpClient;
          };
      }
      var mYear = DateTime.now().year;
      var mMonth = DateTime.now().month;
      var mDay = DateTime.now().day;
      Map<String, dynamic> requestData = {
        "start_business_time": req.start_business_time,
        "end_business_time": req.end_business_time,
        "business_type": req.business_type,
        "worker_id": req.worker_id,
      };

      Response response = await dio.get(
        "/cors_note/group-record-work-statistics-v2",
        data: requestData,
      );
      if (response.statusCode == 200) {
        Future<Map<String, dynamic>> loadTestJson() async {
          final jsonString = await rootBundle.loadString('lib/data/group/group_statistics/test_json.json');
          return json.decode(jsonString);
        }

        final testJson = await loadTestJson();
        final netModel = CorsNoteGroupRecordWorkStatisticsV2NetModel.fromJson(testJson["data"]);
        var respData = netModel.transform();
        print('成功: ${respData}');
        return respData;
      } else {
        print('失败: code:${response.statusCode} msg:${response.statusMessage}');
      }
    } catch (e) {
      print('请求异常: $e');
      throw Exception("error");
    }
    return null;
  }

}

class GroupStatisticsUIRepEntity {
  CorsNoteGroupRecordWorkStatisticsV2BizModel? data;

  GroupStatisticsUIRepEntity({this.data});

  @override
  String toString() {
    return 'GroupStatisticsUIRepEntity{data: $data}';
  }
}
