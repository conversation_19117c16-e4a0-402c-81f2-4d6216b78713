// import 'package:gdjg_pure_flutter/data/group_data/group_statistics/ds/model/net/cors_note_group_record_work_statistics_v2_biz_model.dart';
// import 'package:gdjg_pure_flutter/data/group_data/group_statistics/ds/model/param/cors_note_group_record_work_statistics_v2_param_model.dart';
// import 'package:gdjg_pure_flutter/data/group_data/group_statistics/repo/group_statistics_list_repo.dart';
// import 'package:get/get.dart' as getx;
//
// /// @date 2025/06/21
// /// @description GroupStatistics页UI仓库
// class GroupStatisticsUIRep {
//   /// 实体数据
//   var entity = GroupStatisticsUIRepEntity().obs;
//
//   Future<GroupStatisticsUIRepEntity> fetchGroupStatisticsListData(
//       CorsNoteGroupRecordWorkStatisticsV2ParamModel req) async {
//     var result = await GroupStatisticsListRepo().queryGroupStatisticsList(req);
//     // 返回成功的情况
//     entity.value = GroupStatisticsUIRepEntity(data: result.getSucData());
//     return entity.value;
//   }
// }
//
// class GroupStatisticsUIRepEntity {
//   CorsNoteGroupRecordWorkStatisticsV2BizModel? data;
//
//   GroupStatisticsUIRepEntity({this.data});
//
//   @override
//   String toString() {
//     return 'GroupStatisticsUIRepEntity{data: $data}';
//   }
// }
