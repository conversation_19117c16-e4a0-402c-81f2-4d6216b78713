import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 班组统计空数据状态组件
class EmptyView extends StatelessWidget {
  final String? imagePath;
  final String subtitle;
  final VoidCallback? onRetry;
  final String? retryText;

  const EmptyView({
    super.key,
    this.imagePath,
    required this.subtitle,
    this.onRetry,
    this.retryText,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Container(
        margin: EdgeInsets.only(top: 80.h),
        child: Column(
          children: [
            // 空状态图片
              Image.network(
                imagePath!,
                width: 120.w,
                height: 120.w,
                fit: BoxFit.contain,
              ),

            SizedBox(height: 8.h),

            // 提示文字
            Text(
              subtitle,
              style: TextStyle(
                fontSize: 16.sp,
                color: const Color(0xFF8A8A99),
              ),
            ),
          ],
        ),
      ),
    );
  }
} 