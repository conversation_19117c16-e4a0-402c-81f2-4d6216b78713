import 'dart:ffi';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';

/// 列表项数据模型
class ProjectSelectItem {
  final String tag;
  final String name;
  final double deptId;
  final String workNoteId;

  ProjectSelectItem({required this.tag, required this.name, required this.deptId,required this.workNoteId});
}

/// 显示项目选择弹窗
class GroupProjectSelectDialog {
  static void show(
    BuildContext context, {
    required List<ProjectSelectItem> data,
    required Function(ProjectSelectItem) onSelected,
    VoidCallback? onDismiss,
  }) {
    YPRoute.openDialog(
      alignment: Alignment.bottomCenter,
      clickMaskDismiss: true,
      onDismiss: onDismiss,
      builder: (context) {
        return _GroupProjectSelectDialog(
          data: data,
          onSelected: onSelected,
        );
      },
    );
  }
}

/// 项目选择弹窗的主体UI
class _GroupProjectSelectDialog extends StatelessWidget {
  final List<ProjectSelectItem> data;
  final Function(ProjectSelectItem) onSelected;

  const _GroupProjectSelectDialog({
    super.key,
    required this.data,
    required this.onSelected,
  });

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Material(
        type: MaterialType.transparency,
        child: Container(
          height: 300.h,
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(15),
              topRight: Radius.circular(15),
            ),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildHeader(context),
              Divider(height: 1.h,color: Color(0xFFF5F5F5)),
              Flexible(
                child: ListView.separated(
                  shrinkWrap: true,
                  itemCount: data.length,
                  itemBuilder: (context, index) {
                    final item = data[index];
                    return GestureDetector(
                      behavior: HitTestBehavior.opaque,
                      onTap: () {
                        onSelected(item);
                        YPRoute.closeDialog();
                      },
                      child: _ProjectItem(item: item),
                    );
                  },
                  separatorBuilder: (context, index) => const Divider(
                    height: 1,
                    thickness: 1,
                    color: Color(0xFFF5F5F5),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建弹窗头部
  Widget _buildHeader(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 8),
      child: Row(
        children: [
          GestureDetector(
            onTap: () => YPRoute.closeDialog(),
            child: Image.network(
              "https://cdn.yupaowang.com/yupao_app/resumes_close.png",
              width: 20,
              height: 20,
            ),
          ),
          Expanded(
            child: Center(
              child: Text(
                "请选择项目",
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 15,
                  color: ColorsUtil.black85,
                ),
              ),
            ),
          ),
          SizedBox(width: 20),
        ],
      ),
    );
  }}

/// 列表项UI
class _ProjectItem extends StatelessWidget {
  final ProjectSelectItem item;

  const _ProjectItem({Key? key, required this.item}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 10),
      child: Row(
        children: [
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 8),
            padding: const EdgeInsets.symmetric(horizontal: 5,vertical: 2.5),
            decoration: BoxDecoration(
              color: const Color(0xFFF2F9FF),
              borderRadius: BorderRadius.circular(3),
            ),
            child: Text(
              "班组",
              style:  TextStyle(
                fontSize: 15,
                color: ColorsUtil.primaryColor,
              ),
            ),
          ),
          Expanded(
            child: Text(
              item.name,
              style:  TextStyle(
                fontSize: 15,
                color: ColorsUtil.black85,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
