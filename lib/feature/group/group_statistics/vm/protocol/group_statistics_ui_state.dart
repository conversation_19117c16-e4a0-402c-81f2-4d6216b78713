/// @date 2025/06/21
/// @description 班组-统计页UI状态
class GroupStatisticsUIState {
  GroupStatisticsUIState({this.isShowError, this.data});

  bool? isShowError = false;
  GroupStatisticsDetailUIState? data;
}

/// 班组统计详情UI状态
class GroupStatisticsDetailUIState {
  /// workerId
  String? workerId;

  /// 数据列表
  List<GroupStatisticsListUIState>? list;
}

/// 班组统计列表UI状态
class GroupStatisticsListUIState {
  /// 工友名字
  String? workerName;

  /// 点工
  SpotWorkInfo? spotWorkerInfo;

  /// 包工
  ContractorWorkInfo? contractorWorkInfo;

  /// 工量
  List<UnitCountWorkInfo>? unitCountWorkInfoList;

  /// 短工
  ShortWorkInfo? shortWorkInfo;

  /// 借支
  BorrowInfo? borrowInfo;

  /// 未结算
  NofeeMoneyInfo? nofeeMoneyInfo;
}

/// 班组记工item by worker
class WorkerStatisticsUIState {
  /// 工友名字
  String? workerName;

  /// 统计项
  List<StatisticsItem>? statisticsItemList;
}

/// 统计项
class StatisticsItem {
  /// 类型
  String? typeName;

  /// 详情
  String? detail;

  /// 工资
  String? feeMoney;

  /// 分项名称
  String? unitWorkTypeName;

  /// 分项笔数
  String? unitWorkNum;

  /// 总计
  String? total;

  /// 是否为记账项目
  bool? isRecordWorkType;
}

/// 点工信息
class SpotWorkInfo {
  String? detail;
  String? feeMoney;
}

/// 包工信息
class ContractorWorkInfo {
  String? detail;
  String? feeMoney;
}

/// 工量信息
class UnitCountWorkInfo {
  /// 工量名称
  String? unitWorkTypeName;

  /// 工量笔数
  String? num;

  /// 工量工钱
  String? unitMoney;

  /// 工量单位类型
  String? unitType;
}

/// 短工
class ShortWorkInfo {
  String? feeMoney;
}

/// 借支
class BorrowInfo {
  String? feeMoney;
}

/// 未结
class NofeeMoneyInfo {
  String? feeMoney;
}
