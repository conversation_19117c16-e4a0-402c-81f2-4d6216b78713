import 'package:gdjg_pure_flutter/feature/group/group_com_uistate/group_worker_statistics_ui_state.dart';
import 'package:gdjg_pure_flutter/feature/group/group_statistics/vm/protocol/worker_in_project_us.dart';
import 'package:get/get.dart';

/// @date 2025/06/21
/// @description 班组-统计页UI状态
class GroupStatisticsUS {
  // UI状态管理
  final _isShowError = false.obs;
  final _groupStatisticData = <GroupWorkerStatisticsUIState>[].obs;
  final _workerProjects = <WorkerInProjectUS>[].obs;

  // Getters
  bool get isShowError => _isShowError.value;
  List<GroupWorkerStatisticsUIState> get groupStatisticsData => _groupStatisticData;
  List<WorkerInProjectUS> get workerProjects => _workerProjects;


  void setShowError(bool showError) {
    _isShowError.value = showError;
  }

  void setGroupStatisticsData(List<GroupWorkerStatisticsUIState> data) {
    _groupStatisticData.value = data;
  }

  void setWorkerProjects(List<WorkerInProjectUS> data) {
    _workerProjects.value = data;
  }
}
