import "package:gdjg_pure_flutter/feature/group/group_com_uistate/group_worker_statistics_ui_state.dart";
import "package:gdjg_pure_flutter/feature/group/group_statistics/vm/protocol/group_statistics_us.dart";
import "package:gdjg_pure_flutter/model/RecordType.dart";
import "package:get/get.dart";

import "../../../../common_uistate/statistics_ui_state.dart";
import "../../../../data/group_data/group_statistics/ds/model/param/cors_note_group_record_work_statistics_v2_param_model.dart";
import "../ui_rep/group_statistics_ui_rep.dart";

/// @date 2025/06/21
/// @description GroupStatistics页ViewModel
/// 1. 处理数据的加载状态，如loading和错误视图等
/// 2. 观测UIRep中的业务实体转为UIState
/// 3. 接受来自view的意图并分发事件进行处理（暂无）
/// 4. 对View层暴露事件（操作视图和处理一些与系统权限交互等场景）
class GroupStatisticsVM {
  var isLoading = false.obs;
  var isShowError = false.obs;
  var us = GroupStatisticsUS().obs;
  var uiRep = GroupStatisticsUIRep();
  var params = CorsNoteGroupRecordWorkStatisticsV2ParamModel();
  bool _isInitialized = false;

  GroupStatisticsVM() {
    ///  设置默认时间参数
    params
      ..start_business_time = "${DateTime.now().year - 1}-01-01"
      ..end_business_time = _formatDate(DateTime.now());

    fetchData(params);
    ever(uiRep.entity, (value) {
      convertEntityToUIState(value);
    });
  }

  /// 触发初始化回调（仅在页面首次加载时调用）
  void triggerInitialCallback(Function(DateTime, DateTime) callback) {
    if (!_isInitialized) {
      _isInitialized = true;
      final startDate = DateTime(DateTime.now().year - 1, 1, 1);
      final endDate = DateTime.now();
      callback(startDate, endDate);
    }
  }

  ///更新开始和结束时间
  void updateDateRange(DateTime startTime, DateTime endTime) {
    String startDate = _formatDate(startTime);
    String endDate = _formatDate(endTime);
    params
      ..start_business_time = startDate
      ..end_business_time = endDate;
    fetchData(params);
  }

  /// 更新选择类型
  void updateSelectType(List<RwaRecordType> type) {
    params.business_type = type.map((type) => type.code.value.toString()).join(',') ?? '';;
    fetchData(params);
  }

  ///格式化日期
  String _formatDate(DateTime date) {
    return "${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}";
  }

  /// 初始化数据同步，如果不需要loading，则在这里去掉
  void fetchData(CorsNoteGroupRecordWorkStatisticsV2ParamModel req) async {
    isLoading.value = true;
    try {
      // 同步获取数据
      // var result = uiRep.getStatus();
      await uiRep.fetchGroupStatisticsListData(req);
    } catch (e) {
      us.value.data = null;
      us.value.isShowError = true;
    } finally {
      isLoading.value = false;
    }
  }

  /// 列表点击选择工种弹窗

  /// 将实体数据转换为UIState
  /// @param entity
  /// @returns
  void convertEntityToUIState(GroupStatisticsUIRepEntity entity) {
    if (entity.data == null) return;
    print("LINer========从ui仓库接收到的数据${entity}");
    final List<GroupWorkerStatisticsUIState> workerStatisticsList = [];
    for (var workerData in entity.data!.list) {
      final List<StatisticsItemUIState> statisticsItemList = [];

      // 1. 点工
      if (workerData.spotWork != null && workerData.spotWorkNum > 0) {
        final detail = _buildWorkDetailString(
          workTime: _formatWorkTime(workerData.spotWork?.workTime ?? ""),
          workTimeHour: workerData.spotWork?.workTimeHour ?? 0.0,
          overtimeWork:
              _formatWorkTime(workerData.spotWork?.overtimeWork ?? ""),
          overtime: workerData.spotWork?.overtime ?? 0.0,
        );
        statisticsItemList.add(StatisticsItemUIState(
          recordType: RwaRecordType.workDays,
          typeName: "点工",
          detail: detail,
          feeMoney: workerData.spotWork?.feeMoney,
          isRecordWorkType: true,
        ));
      }

      // 2. 包工
      if (workerData.contractorWork != null &&
          workerData.contractorWorkNum > 0) {
        final detail = _buildWorkDetailString(
            workTime:
                _formatWorkTime(workerData.contractorWork?.workTime ?? ""),
            workTimeHour: workerData.contractorWork?.workTimeHour ?? 0.0,
            overtimeWork: _formatWorkTime(
                workerData.contractorWork?.overtimeWork.toString() ?? ""),
            overtime: workerData.contractorWork?.overtime ?? 0.0);
        statisticsItemList.add(StatisticsItemUIState(
          recordType: RwaRecordType.packageWork,
          typeName: "包工",
          detail: detail,
          feeMoney: workerData.contractorWork?.feeMoney,
          isRecordWorkType: true,
        ));
      }

      // 3. 工量
      if (workerData.unitCount.isNotEmpty) {
        for (var unit in workerData.unitCount) {
          statisticsItemList.add(StatisticsItemUIState(
            recordType: RwaRecordType.workLoad,
            typeName: "工量",
            unitWorkTypeName: unit.unitWorkTypeName ?? "",
            detail: "",
            unitWorkNum: unit.num > 0 ? "${unit.num.toInt()}笔" : "",
            feeMoney: unit.unitMoney ?? "",
            total: (int.parse(unit.count) > 0)
                ? "总计:${unit.count}${unit.unitWorkTypeUnit}"
                : "",
            isRecordWorkType: true,
          ));
        }
      }

      // 4. 短工
      if (workerData.money != null && workerData.moneyNum > 0) {
        statisticsItemList.add(StatisticsItemUIState(
          recordType: RwaRecordType.dailyWages,
          typeName: "短工",
          feeMoney: workerData.money?.money,
          isRecordWorkType: true,
        ));
      }

      // 5. 借支
      if (workerData.borrowMoneyNum > 0) {
        statisticsItemList.add(StatisticsItemUIState(
          recordType: RwaRecordType.debt,
          typeName: "借支",
          detail: "",
          feeMoney: workerData.borrowMoney,
          isRecordWorkType: false,
        ));
      }

      // 6. 未结
      if (workerData.spotWorkNoFeeNum > 0) {
        statisticsItemList.add(StatisticsItemUIState(
          recordType: RwaRecordType.wageLast,
          typeName: "未结",
          detail: "",
          feeMoney: workerData.spotWorkNoFeeMoney,
          isRecordWorkType: false,
        ));
      }

      // 7. 结算
      if (workerData.wageNum > 0) {
        statisticsItemList.add(StatisticsItemUIState(
          recordType: RwaRecordType.wageLast,
          typeName: "结算",
          detail: "",
          feeMoney: workerData.wage,
          isRecordWorkType: false,
        ));
      }

      workerStatisticsList.add(GroupWorkerStatisticsUIState(
        workerName: workerData.name,
        statisticsItemList: statisticsItemList,
      ));
    }

    us.value.data = workerStatisticsList;
    us.refresh();
  }

  /// 构建点工和包工详情
  String _buildWorkDetailString({
    required String workTime,
    required double workTimeHour,
    required String overtimeWork,
    required double overtime,
  }) {
    final List<String> workParts = [];
    if (double.tryParse(workTime) != null && double.parse(workTime) > 0) {
      workParts.add("$workTime个工");
    }
    if (workTimeHour > 0) {
      workParts.add("${workTimeHour.toInt()}小时");
    }
    final workDetail = workParts.join('+');

    final List<String> overtimeParts = [];
    if (double.tryParse(overtimeWork) != null &&
        double.parse(overtimeWork) > 0) {
      overtimeParts.add("$overtimeWork个工");
    }
    if (overtime > 0) {
      overtimeParts.add("${overtime.toInt()}小时");
    }
    final overtimeDetail = overtimeParts.join('+');

    final List<String> finalParts = [];
    if (workDetail.isNotEmpty) {
      finalParts.add("上班:$workDetail");
    }
    if (overtimeDetail.isNotEmpty) {
      finalParts.add("加班:$overtimeDetail");
    }

    return finalParts.join('\n');
  }

  // 格式化工时
  String _formatWorkTime(String workTime) {
    if (workTime.isEmpty) return "";

    final numValue = num.tryParse(workTime);
    if (numValue == null) return workTime; // return original if not a number

    if (numValue % 1 == 0) {
      return numValue.toInt().toString(); // integer - no decimal
    } else {
      return numValue.toStringAsFixed(1); // decimal - keep 1 digit
    }
  }
}
