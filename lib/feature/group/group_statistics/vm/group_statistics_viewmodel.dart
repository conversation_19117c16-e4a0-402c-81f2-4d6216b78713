import "package:gdjg_pure_flutter/data/group/group_statistics/ds/model/param/cors_note_group_record_work_statistics_v2_param_model.dart";
import "package:get/get.dart";
import "../ui_rep/group_statistics_ui_rep.dart";
import "protocol/group_statistics_ui_state.dart";

/// @date 2025/06/21
/// @description GroupStatistics页ViewModel
/// 1. 处理数据的加载状态，如loading和错误视图等
/// 2. 观测UIRep中的业务实体转为UIState
/// 3. 接受来自view的意图并分发事件进行处理（暂无）
/// 4. 对View层暴露事件（操作视图和处理一些与系统权限交互等场景）
class GroupStatisticsViewModel {
  var isLoading = false.obs;
  var isShowError = false.obs;
  var uiState = GroupStatisticsUIState().obs;
  var uiRep = GroupStatisticsUIRep();

  GroupStatisticsViewModel(CorsNoteGroupRecordWorkStatisticsV2ParamModel req) {
    fetchData(req);
    ever(uiRep.entity, (value) {
      convertEntityToUIState(value);
    });
  }

  /// 初始化数据同步，如果不需要loading，则在这里去掉
  void fetchData(CorsNoteGroupRecordWorkStatisticsV2ParamModel req) async {
    isLoading.value = true;
    try {
      // 同步获取数据
      // var result = uiRep.getStatus();
      await uiRep.fetchGroupStatisticsListData(req);
    } catch (e) {
      uiState.value.data = null;
      uiState.value.isShowError = true;
    } finally {
      isLoading.value = false;
    }
  }

  /// 将实体数据转换为UIState
  /// @param entity
  /// @returns
  void convertEntityToUIState(GroupStatisticsUIRepEntity entity) {
    uiState.value.data = entity.data as GroupStatisticsDetailUIState?;
  }
}
