import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/feature/group/group_pro_calendar/entity/group_pro_calendar_props.dart';
import 'package:gdjg_pure_flutter/feature/group/group_pro_calendar/view/group_pro_bench_view.dart';
import 'package:gdjg_pure_flutter/feature/group/group_pro_calendar/view/group_pro_calendar_bottom_view.dart';
import 'package:gdjg_pure_flutter/feature/group/group_pro_calendar/view/group_statistics_list_view.dart';
import 'package:gdjg_pure_flutter/feature/group/group_pro_calendar/view/month_switcher_view.dart';
import 'package:gdjg_pure_flutter/feature/group/group_pro_calendar/vm/group_pro_calendar_viewmodel.dart';
import 'package:gdjg_pure_flutter/feature/group/group_pro_statistics/entity/group_pro_statistics_props.dart';
import 'package:gdjg_pure_flutter/init_module/init_route.dart';
import 'package:gdjg_pure_flutter/model/RecordType.dart';
import 'package:gdjg_pure_flutter/utils/route_util/base_page/base_ful_page.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/appbar_util.dart';
import 'package:gdjg_pure_flutter/widget/network_carousel_widget.dart';
import 'package:gdjg_pure_flutter/widget/page_error_view.dart';
import 'package:gdjg_pure_flutter/widget/page_loading_view.dart';
import 'package:get/get.dart';

/// @date 2025/06/19
/// @param props 页面路由参数
/// @returns
/// @description GroupProCalendar页面入口 班组项目日历页
class GroupProCalendarPage extends BaseFulPage {
  const GroupProCalendarPage({super.key}) : super(appBar: null);

  @override
  createState() => _GroupProCalendarPageState();
}

class _GroupProCalendarPageState<GroupProCalendarPage>
    extends BaseFulPageState {
  final GroupProCalendarViewModel viewModel = GroupProCalendarViewModel();

  @override
  onPageRoute(Object? routeParams, bool fromLaunchTask) {
    GroupProCalendarProps? props = routeParams as GroupProCalendarProps?;
    viewModel.init(props);
  }

  @override
  Widget yBuild(BuildContext context) {
    return Scaffold(
        appBar: AppBarUtil.buildWithResourceWidget(
            title: viewModel.props?.workNoteName ?? "",
            resourceText: '拍证据工资有保障',
            resourceIcon: "assets/images/common/ic_take_phone_small.webp",
            onBackTap: () => YPRoute.closePage()),
        body: contentView());
  }

  /// 实际展示的视图，需要处理：
  /// 1. 画出UI
  /// 2. 绑定UIState
  /// 3. 处理事件监听
  /// 4. 向VM传递来自View层的事件
  Widget contentView() {
    // 进行事件处理
    // handleGroupProCalendarVMEvent(props.vm)
    return Container(
        color: Colors.white,
        child: Stack(
          children: [
            Column(
              children: [
                _buildBannerView(),
                Divider(height: 6.h, color: Color(0xFFF0F0F0), thickness: 6.h),
                _buildMonthSwitchView(),
                Expanded(
                    child: SingleChildScrollView(
                  child: Column(
                    children: [
                      _buildStatisticsView(), // 统计视图
                      _buildCalenderView(), //大日历
                      _buildProBenchView(), // 功能列表视图
                    ],
                  ),
                )),
                _buildBottomView()
              ],
            ),
            Obx(() {
              if (viewModel.isLoading.value) {
                return const PageLoadingView();
              }
              if (viewModel.uiState.value.isShowError == true) {
                return PageErrorView(onReload: () => {viewModel.refresh()});
              }
              return Container();
            }),
          ],
        ));
  }

  ///统计布局
  Widget _buildStatisticsView() {
    return Obx(() {
      if (viewModel.statisticsUIState.value.isEmpty) {
        return SizedBox(
          width: double.infinity,
          height: 56.h,
          child: Center(
            child: Text(
              "当月无记工",
              style: TextStyle(color: Color(0xFF323232), fontSize: 16.sp),
            ),
          ),
        );
      }
      return GroupStatisticsView(
        items: viewModel.statisticsUIState.value.list,
        initialVisibleCount: 1,
        onItemTap: (index) {
          if (viewModel.props == null) return;
          var uiState = viewModel.statisticsUIState.value.list[index];
          List<RwaRecordType> businessType = [];
          if (uiState.recordType != RwaRecordType.wageLast) {
            businessType.add(uiState.recordType);
          }

          ///跳转统计页面
          if (viewModel.props?.isSelfCreatedOrAgent) {
            GroupProStatisticsProps params = GroupProStatisticsProps()
              ..workNoteId = viewModel.props?.workNoteId ?? ""
              ..workNoteName = viewModel.props?.workNoteName ?? ""
              ..deptId = viewModel.props?.deptId
              ..startTime = viewModel.params.start_time
              ..endTime = viewModel.params.end_time
              ..businessType = businessType;

            YPRoute.openPage(RouteNameCollection.groupProStatistics,
                params: params);
          } else {
            ///跳转流水页面

          }
        },
      );
    });
  }

  ///顶部banner
  Widget _buildBannerView() {
    return Container(
      color: Color(0xFFF0F0F0),
      padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 6.h),
      child: NetworkCarouselWidget(
        code: 'JGJZ_HOME_GROUP_BANER',
      ),
    );
  }

  ///月份切换控件
  Widget _buildMonthSwitchView() {
    return MonthSwitcherView(
        key: Key("MonthSwitcherView"),
        initialYear: viewModel.now.year,
        initialMonth: viewModel.now.month,
        onYearMonthSelected: (startDate, endDate) {
          viewModel.updateDateRange(startDate, endDate);
        });
  }

  ///大日历
  Widget _buildCalenderView() {
    return Container(
      height: 250,
      color: Colors.grey,
      child: Center(
        child: Text("日历"),
      ),
    );
  }

  ///底部静态功能区域
  Widget _buildProBenchView() {
    return GroupProBenchView(viewModel: viewModel);
  }

  ///底部布局
  Widget _buildBottomView() {
    return GroupProCalendarBottomView(
      onLeftCallback: () {},
      onRightCallback: () {},
      isCreateByMySelfOrAgent: viewModel.props?.isSelfCreatedOrAgent ?? true,
    );
  }
}
