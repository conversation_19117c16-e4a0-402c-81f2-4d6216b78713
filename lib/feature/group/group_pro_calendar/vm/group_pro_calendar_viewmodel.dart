import "package:gdjg_pure_flutter/common_uistate/statistice_helper.dart";
import "package:gdjg_pure_flutter/common_uistate/statistics_ui_state.dart";
import "package:gdjg_pure_flutter/data/group_data/group_calendar/ds/model/param/group_calendar_param_model.dart";
import "package:gdjg_pure_flutter/feature/group/group_pro_calendar/entity/group_pro_calendar_props.dart";
import "package:gdjg_pure_flutter/feature/group/group_pro_calendar/vm/protocol/group_pro_calendar_statistics_ui_state.dart";
import "package:gdjg_pure_flutter/model/RecordNoteType.dart";
import "package:get/get.dart";

import "../ui_rep/group_pro_calendar_ui_rep.dart";
import "protocol/group_pro_calendar_ui_state.dart";

/// @date 2025/06/19
/// @description GroupProCalendar页ViewModel
/// 1. 处理数据的加载状态，如loading和错误视图等
/// 2. 观测UIRep中的业务实体转为UIState
/// 3. 接受来自view的意图并分发事件进行处理（暂无）
/// 4. 对View层暴露事件（操作视图和处理一些与系统权限交互等场景）
class GroupProCalendarViewModel {
  GroupProCalendarProps? props;
  var isLoading = false.obs;
  var isShowError = false.obs;
  var uiState = GroupProCalendarUIState().obs;
  var now = DateTime.now();
  var statisticsUIState = GroupProCalendarStatisticsUIState(list: []).obs;
  var uiRep = GroupProCalendarUIRep();
  final _identity = RecordNoteType.group;
  var params = GroupCalendarParamModel();

  GroupProCalendarViewModel() {
    ever(uiRep.entity, (value) {
      convertEntityToUIState(value);
    });
  }

  init(GroupProCalendarProps? props) {
    this.props = props;
    var startTime = "${now.year}-${now.month.toString().padLeft(2, '0')}-01";
    DateTime endOfMonth = DateTime(now.year, now.month + 1, 0); // 获取该月最后一天
    DateTime endDateValue =
        endOfMonth.isAfter(now) ? now : endOfMonth; // 如果是未来月份，则限制到今天
    var endTime =
        "${endDateValue.year}-${endDateValue.month.toString().padLeft(2, '0')}-${endDateValue.day.toString().padLeft(2, '0')}";
    params
      ..work_note = props?.workNoteId ?? ''
      ..start_time = startTime
      ..end_time = endTime;
    fetchData();
  }

  void refresh() {
    fetchData();
  }

  void updateDateRange(DateTime dateTime) {
    var now = DateTime.now();
    bool isCurrentMonth = dateTime.year == now.year && dateTime.month == now.month;
    var startTime = "${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-01";
    DateTime endOfMonth = DateTime(dateTime.year, dateTime.month + 1, 0); // 获取该月最后一天
    DateTime endDateValue = isCurrentMonth ? now : endOfMonth; // 如果是当月则限制到今天，否则取该月最后一天
    var endTime = "${endDateValue.year}-${endDateValue.month.toString().padLeft(2, '0')}-${endDateValue.day.toString().padLeft(2, '0')}";
    params
      ..start_time = startTime
      ..end_time = endTime;
    fetchData(); // 请求新数据
  }

  /// 初始化数据同步，如果不需要loading，则在这里去掉
  void fetchData() async {
    isLoading.value = true;
    try {
      await uiRep.fetchData(params);
    } catch (e) {
      uiState.value.isShowError = true;
    } finally {
      isLoading.value = false;
    }
  }

  /// 将实体数据转换为UIState
  /// @param entity
  /// @returns
  void convertEntityToUIState(GroupProCalendarUIRepEntity entity) {
    var count = entity.data?.count;
    List<StatisticsItemUIState> list =
        StatisticsUIStateHelper.buildStatisticsItem(count);
    statisticsUIState.value =
        GroupProCalendarStatisticsUIState(list: list, isEmpty: list.isEmpty);
  }
}
