import 'package:gdjg_pure_flutter/feature/group/common_uistate/statistics_ui_state.dart';
import 'package:gdjg_pure_flutter/widget/calendar/dayevent/DayEvent.dart';
import 'package:get/get.dart';

/// @date 2025/06/19
/// @description JDetail页UI状态
class GroupProCalendarUS {
  final _dataTime = DateTime.now().obs;
  final _statisticsList = <StatisticsItemUIState>[].obs;
  final _isStatisticsEmpty = false.obs;

  final RxMap<DateTime, List<DayEvent>> _events = <DateTime, List<DayEvent>>{}.obs;

  DateTime get dataTime => _dataTime.value;

  List<StatisticsItemUIState> get statisticsList => _statisticsList.value;

  bool get isStatisticsEmpty => _isStatisticsEmpty.value;

  Map<DateTime, List<DayEvent>> get events => _events.value;

  void setCurrentDate(DateTime date) {
    _dataTime.value = date;
    _dataTime.refresh();
  }

  void setStatisticsList(List<StatisticsItemUIState> list) {
    _statisticsList.value = list;
    _isStatisticsEmpty.value = list.isEmpty;
    _statisticsList.refresh();
    _isStatisticsEmpty.refresh();
  }

  void setEvents(Map<DateTime, List<DayEvent>> events) {
    _events.value = events;
  }

}
