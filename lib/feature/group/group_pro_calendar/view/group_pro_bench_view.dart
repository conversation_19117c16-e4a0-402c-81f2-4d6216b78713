import 'package:flutter/material.dart';
import 'package:gdjg_pure_flutter/feature/group/construction_log/entity/construction_log_props.dart';
import 'package:gdjg_pure_flutter/feature/group/group_pro_calendar/vm/group_pro_calendar_viewmodel.dart';
import 'package:gdjg_pure_flutter/init_module/init_route.dart';
import 'package:gdjg_pure_flutter/model/RecordNoteType.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/widget/quick_link_view/entity/quick_link_props.dart';
import 'package:gdjg_pure_flutter/widget/quick_link_view/quick_link_view.dart';

class GroupProBenchView extends StatelessWidget {
  const GroupProBenchView({super.key, required this.viewModel});

  final GroupProCalendarViewModel viewModel;

  @override
  Widget build(BuildContext context) {
    return _buildProBenchView();
  }

  ///底部功能管理区
  Widget _buildProBenchView() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildDynamicProBenchView(),
        Divider(height: 4, color: Color(0xFFF0F0F0), thickness: 4),
        _buildSectionTitle("记工管理"),
        _buildFunctionGrid([
          _buildFunctionItem(
              "统计", "assets/images/group/ic_calendar_menu_statistics.webp", () {
                viewModel.onJumpToProStatisticsTap();

          }),
          _buildFunctionItem("未结",
              "assets/images/group/ic_calendar_menu_not_checkout.webp", () {
                viewModel.onGroupUnLiquidatedTap();
              }),
          _buildFunctionItem(
              "记工表",
              "assets/images/group/ic_calendar_menu_work_schedules.webp",
              () {}),
          _buildFunctionItem("回收站",
              "assets/images/group/ic_calendar_menu_recycle_bin.webp", () {}),
        ]),
        Divider(height: 4, color: Color(0xFFF0F0F0), thickness: 4),
        _buildSectionTitle("日常管理"),
        _buildFunctionGrid([
          _buildFunctionItem(
              "打卡", "assets/images/group/ic_calendar_menu_clock.webp", () {}),
          _buildFunctionItem(
              "打卡统计",
              "assets/images/group/ic_calendar_menu_clock_statistic.webp",
              () {}),
          _buildFunctionItem(
              "施工日志",
              "assets/images/group/ic_calendar_menu_construction_logs.webp",
              () {
                YPRoute.openPage(
                  RouteNameCollection.constructionLog,
                  params: ConstructionLogProps(
                    deptId: viewModel.deptId?.toInt().toString(),
                  ),
                );
              }),
          _buildFunctionItem("水印相机",
              "assets/images/group/ic_calendar_menu_statistics.webp", () {}),
        ]),
        Divider(height: 4, color: Color(0xFFF0F0F0), thickness: 4),
        _buildSectionTitle("项目信息"),
        _buildFunctionGrid([
          _buildFunctionItem("在场工友",
              "assets/images/group/ic_calendar_menu_scene_worker.webp", () {}),
          _buildFunctionItem("点工工价",
              "assets/images/group/ic_calendar_menu_point_wages.webp", () {}),
          _buildFunctionItem("带班设置",
              "assets/images/group/ic_calendar_menu_shift_setting.webp", () {}),
          _buildFunctionItem(
              "项目设置",
              "assets/images/group/ic_calendar_menu_project_setting.webp",
              () {}),
          _buildFunctionItem(
              "班组云相册",
              "assets/images/group/ic_calendar_menu_team_cloud_album.webp",
              () {}),
          _buildFunctionItem("我的授权",
              "assets/images/group/ic_calendar_menu_mandate.webp", () {}),
        ]),
      ],
    );
  }

  ///动态部分
  Widget _buildDynamicProBenchView() {
    return QuickLinkView(props: QuickLinkProps(noteType: RecordNoteType.group));
  }

// 标题部分
  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: EdgeInsets.fromLTRB(16, 16, 0, 16),
      child: Text(
        title,
        style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Color(0xFF323232)),
      ),
    );
  }

// 功能按钮网格
  Widget _buildFunctionGrid(List<Widget> items) {
    return Padding(
      padding: EdgeInsets.fromLTRB(0, 0, 0, 16),
      child: Wrap(
        runSpacing: 16,
        children: items,
      ),
    );
  }

// 单个功能项
  Widget _buildFunctionItem(String label, String icon, VoidCallback onTap) {
    return FractionallySizedBox(
      widthFactor: 0.25,
      child: InkWell(
        onTap: onTap,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Image.asset(icon, width: 48, height: 48),
            const SizedBox(height: 4),
            Text(
              label,
              textAlign: TextAlign.center,
              style: const TextStyle(fontSize: 14, color: Color(0xFF323232)),
            ),
          ],
        ),
      ),
    );
  }
}
