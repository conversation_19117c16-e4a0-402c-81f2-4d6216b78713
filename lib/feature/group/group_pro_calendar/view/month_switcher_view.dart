import 'package:flutter/material.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/month_select_util.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/toast_util.dart';

typedef OnMonthRangeSelected = void Function(String startDate, String endDate);

/// 月份切换控件
class MonthSwitcherView extends StatefulWidget {
  final int? initialYear;
  final int? initialMonth;
  final OnMonthRangeSelected onYearMonthSelected;

  const MonthSwitcherView({
    super.key,
    this.initialYear,
    this.initialMonth,
    required this.onYearMonthSelected,
  });

  @override
  State<MonthSwitcherView> createState() => _MonthSwitcherViewState();
}

class _MonthSwitcherViewState extends State<MonthSwitcherView> {
  late int _year;
  late int _month;
  final DateTime now = DateTime.now();

  @override
  void initState() {
    super.initState();
    _year = widget.initialYear ?? now.year;
    _month = widget.initialMonth ?? now.month;
  }

  void _notifyParent(int year, int month) {
    String startDate = "$year-${month.toString().padLeft(2, '0')}-01";
    DateTime endOfMonth = DateTime(year, month + 1, 0); // 获取该月最后一天
    DateTime endDateValue =
        endOfMonth.isAfter(now) ? now : endOfMonth; // 如果是未来月份，则限制到今天
    String endDate =
        "${endDateValue.year}-${endDateValue.month.toString().padLeft(2, '0')}-${endDateValue.day.toString().padLeft(2, '0')}";
    widget.onYearMonthSelected(startDate, endDate);
  }

  /// 显示月份选择器
  void _showMonthPicker() {
    MonthSelectUtil.show(
      context: context,
      initialYear: _year,
      initialMonth: _month,
      onSelected: (year, month) {
        setState(() {
          _year = year;
          _month = month;
        });
        _notifyParent(year, month);
      },
    );
  }

  void _changeMonth(int delta) {
    DateTime newDate = DateTime(_year, _month + delta, 1);
    if (delta == 1 && !_isSameOrBeforeCurrentMonth(newDate)) {
      ToastUtil.showToast("下个月还没有到哟");
      return;
    }
    setState(() {
      _year = newDate.year;
      _month = newDate.month;
    });
    _notifyParent(_year, _month);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 40,
      width: double.infinity,
      color: const Color(0xFFF0F0F0),
      child: Row(
        children: [
          // 左侧“上一月”按钮
          SizedBox(
            width: 98,
            child: Center(
              child: GestureDetector(
                onTap: () {
                  _changeMonth(-1);
                },
                child: Text('上一月', style: _monthButtonTextStyle),
              ),
            ),
          ),

          // 中间区域（带背景图）
          Expanded(
            flex: 1,
            child: GestureDetector(
              onTap: () {
                _showMonthPicker();
              },
              child: Container(
                decoration: BoxDecoration(
                  image: DecorationImage(
                    image: AssetImage(
                        "assets/images/group/waa_ic_psca_date_bg.webp"),
                    fit: BoxFit.fill,
                  ),
                ),
                child: Center(
                  child: _monthDisplayRow("$_year年$_month月"),
                ),
              ),
            ),
          ),

          // 右侧按钮
          SizedBox(
            width: 98,
            child: Center(
              child: GestureDetector(
                onTap: () {
                  _changeMonth(1);
                },
                child: Text('下一月', style: _monthButtonTextStyle),
              ),
            ),
          ),
        ],
      ),
    );
  }

  ///判断是否到下个月
  bool _isSameOrBeforeCurrentMonth(DateTime date) {
    final now = DateTime.now();
    return date.year < now.year ||
        (date.year == now.year && date.month <= now.month);
  }

  Widget _monthDisplayRow(String monthText) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          monthText,
          style: _monthTextStyle,
        ),
        SizedBox(width: 4), // 更好的间距控制
        Image.asset(
          "assets/images/group/waa_svg_select_time.webp",
          width: 12,
          height: 12,
        ),
      ],
    );
  }

  // --- UI 样式和辅助方法 ---
  TextStyle get _monthTextStyle => const TextStyle(
        fontSize: 16,
        color: Color(0xFF323233),
        fontWeight: FontWeight.bold,
      );

  TextStyle get _monthButtonTextStyle => const TextStyle(
        fontSize: 14,
        color: Color(0xFF323233),
      );
}
