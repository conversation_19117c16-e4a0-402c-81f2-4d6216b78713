import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/common_uistate/statistics_ui_state.dart';
import 'package:gdjg_pure_flutter/model/RecordType.dart';
import 'package:gdjg_pure_flutter/utils/system_util/font_util.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';

class GroupStatisticsView extends StatefulWidget {
  final List<StatisticsItemUIState> items;
  final Function(int)? onItemTap;
  final int initialVisibleCount;

  const GroupStatisticsView({
    super.key,
    this.items = const [],
    this.onItemTap,
    this.initialVisibleCount = 1,
  });

  @override
  State<GroupStatisticsView> createState() => _GroupStatisticsViewState();
}

class _GroupStatisticsViewState extends State<GroupStatisticsView>
    with SingleTickerProviderStateMixin {
  late bool _expanded = false;
  bool _hideAmount = false;
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _animation = CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    );

    if (_expanded) {
      _controller.value = 1.0;
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _toggleExpanded() {
    setState(() {
      _expanded = !_expanded;
      if (_expanded) {
        _controller.forward();
      } else {
        _controller.reverse();
      }
    });
  }

  void _toggleHideAmount() {
    setState(() {
      _hideAmount = !_hideAmount;
    });
  }

  @override
  Widget build(BuildContext context) {
    final skippedItems = widget.items.skip(widget.initialVisibleCount).toList();
    return Padding(
      padding: const EdgeInsets.fromLTRB(12, 4, 12, 0),
      child: Column(
        children: [
          // 始终显示的项目
          ...widget.items
              .take(widget.initialVisibleCount)
              .map((item) => _buildItem(item, 0, showEyeIcon: true)),
          Divider(height: 8, color: Colors.transparent, thickness: 8),

          // 可折叠的项目
          if (widget.items.length > widget.initialVisibleCount)
            SizeTransition(
              sizeFactor: _animation,
              child: Column(
                spacing: 8,
                children: List.generate(skippedItems.length, (index) {
                  return _buildItem(
                      skippedItems[index], index + widget.initialVisibleCount);
                }),
              ),
            ),

          // 展开/收起按钮
          if (widget.items.length > widget.initialVisibleCount)
            _buildToggleButton(),
        ],
      ),
    );
  }

  Widget _buildItem(StatisticsItemUIState item, int index,
      {bool showEyeIcon = false}) {
    bool showEyeIcon = index == 0;
    var bgColor = (item.recordType == RwaRecordType.debt ||
            item.recordType == RwaRecordType.wageLast)
        ? Color(0x1AFFA011)
        : ColorsUtil.primaryColor15;
    var amountColor = (item.recordType == RwaRecordType.debt ||
            item.recordType == RwaRecordType.wageLast)
        ? Color(0xFFFF9F31)
        : ColorsUtil.primaryColor;
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(4),
        color: bgColor,
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => widget.onItemTap?.call(index),
          child: Padding(
            padding: const EdgeInsets.all(8),
            child: Row(
              children: [
                //最左边的标题和笔数
                Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
                  Row(
                    children: [
                      //标题
                      Text(
                        item.typeName ?? "",
                        style: TextStyle(
                          fontSize: 16.sp,
                          color: Color(0xFF323232),
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      if (item.unitWorkTypeName != null) SizedBox(width: 8),
                      if (item.unitWorkTypeName != null)
                        Text(
                          item.unitWorkTypeName ?? "",
                          style: TextStyle(
                            fontSize: 16.sp,
                            color: Color(0xFF323232),
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                    ],
                  ),
                  // 笔数
                  if (item.unitWorkNum != null)
                    Text(
                      item.unitWorkNum ?? "",
                      style: TextStyle(fontSize: 15, color: Color(0xFF9D9DB3)),
                      overflow: TextOverflow.ellipsis,
                    ),
                ]),
                SizedBox(width: 12),
                //中间上班和加班
                Expanded(
                  flex: 1,
                  child: Center(
                    child: Text(
                      item.detail ?? "",
                      style: TextStyle(fontSize: 15, color: Color(0xFF323232)),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ),
                Row(
                  children: [
                    Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Text(
                          _hideAmount ? "****" : item.feeMoney ?? "0.00",
                          style: TextStyle(
                            fontSize: 22,
                            fontFamily: FontUtil.fontCondMedium,
                            color: amountColor,
                          ),
                        ),
                        if (item.total != null)
                          Text(
                            item.total ?? "",
                            style: TextStyle(
                              fontSize: 14,
                              color: ColorsUtil.black65,
                            ),
                          ),
                      ],
                    ),
                    SizedBox(width: 8),
                    if (showEyeIcon)
                      GestureDetector(
                        onTap: _toggleHideAmount,
                        child: Image.asset(
                            _hideAmount
                                ? "assets/images/group/waa_svg_pro_eye_close.webp"
                                : "assets/images/group/waa_svg_pro_eye.webp",
                            width: 18,
                            height: 18),
                      ),
                    //右边箭头
                    if (!showEyeIcon)
                      Image.asset(
                          "assets/images/common/icon_arrow_right_grey.png",
                          width: 18,
                          height: 18),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildToggleButton() {
    return GestureDetector(
      onTap: _toggleExpanded,
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(vertical: 8),
        alignment: Alignment.center,
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              _expanded ? "收起" : "更多",
              style: TextStyle(fontSize: 14, color: ColorsUtil.primaryColor),
            ),
            SizedBox(width: 4),
            Image.asset(
              _expanded
                  ? "assets/images/group/waa_ic_big_calendar_more_up.webp"
                  : "assets/images/group/waa_ic_big_calendar_more.webp",
              width: 18,
              height: 18,
            ),
          ],
        ),
      ),
    );
  }
}
