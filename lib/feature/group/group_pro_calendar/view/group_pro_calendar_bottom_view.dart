import 'package:flutter/material.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';

class GroupProCalendarBottomView extends StatelessWidget {
  VoidCallback? onLeftCallback;
  VoidCallback? onRightCallback;
  VoidCallback? onClockCallback;
  bool isCreateByMySelfOrAgent;

   GroupProCalendarBottomView(
      {super.key,
      this.onLeftCallback,
      this.onRightCallback,
      this.onClockCallback,
      required this.isCreateByMySelfOrAgent});

  @override
  Widget build(BuildContext context) {
    if (!isCreateByMySelfOrAgent) {
      return _buildClockInView();
    }

    return _buildRecodedView();
  }

  Widget _buildRecodedView() {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 16),
      decoration: BoxDecoration(
        border: Border(
          top: BorderSide(color: Color(0xFFE6E6E6), width: 1),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // 左侧按钮
          Expanded(
            flex: 1,
            child: SizedBox(
              height: 44,
              child: TextButton(
                style: TextButton.styleFrom(
                  foregroundColor: const Color(0xFFF69500),
                  backgroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(6),
                    side: const BorderSide(color: Color(0xFFF69500), width: 1),
                  ),
                  textStyle: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    height: 20 / 16,
                  ),
                ),
                onPressed: () {
                  // 根据 isWorker 决定跳转页面
                },
                child: Text(
                  "借支/结算",
                  style: const TextStyle(
                      color: Color(0xFFF69500),
                      fontSize: 16,
                      fontWeight: FontWeight.w500),
                ),
              ),
            ),
          ),

          const SizedBox(width: 16),

          // 右侧“记工”按钮
          Expanded(
            flex: 1,
            child: SizedBox(
              height: 44,
              child: TextButton(
                style: TextButton.styleFrom(
                  foregroundColor: ColorsUtil.primaryColor,
                  backgroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(6),
                    side: BorderSide(color: ColorsUtil.primaryColor, width: 1),
                  ),
                  textStyle: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    height: 20 / 16,
                  ),
                ),
                onPressed: () {},
                child: Text('记工',
                    style: TextStyle(
                        color: ColorsUtil.primaryColor,
                        fontSize: 16,
                        fontWeight: FontWeight.w500)),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildClockInView() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 16),
      decoration: BoxDecoration(
        border: Border(
          top: BorderSide(color: Color(0xFFE6E6E6), width: 1),
        ),
      ),
      child: SizedBox(
        height: 44,
        child: TextButton(
          style: TextButton.styleFrom(
            backgroundColor: ColorsUtil.primaryColor,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(6),
              side: BorderSide(color: ColorsUtil.primaryColor, width: 1),
            ),
            textStyle: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              height: 20 / 16,
            ),
          ),
          onPressed: () {},
          child: Text('打卡',
              style: TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.w500)),
        ),
      ),
    );
  }
}
