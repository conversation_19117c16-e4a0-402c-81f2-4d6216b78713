
import 'package:gdjg_pure_flutter/data/group_data/group_calendar/ds/model/param/group_calendar_param_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_calendar/repo/group_repo.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_calendar/repo/model/group_calendar_biz_model.dart';
import 'package:get/get.dart';

/// @date 2025/06/19
/// @description GroupProCalendar页UI仓库
/// 1.为vm提供业务数据
/// 2.解耦视图层与业务仓库之间的耦合
class GroupProCalendarUIRep {
  final _groupRepo = GroupRepo();

  /// 实体数据
  var entity = GroupProCalendarUIRepEntity().obs;

  bool getStatus() {
    return true;
  }

  /// 初始化数据同步，返回同步是否成功的状态给UI层
  /// 如果成功，则将业务数据存储在entity中
  Future<GroupProCalendarUIRepEntity> fetchData(GroupCalendarParamModel params) async {
    // 调用网络的方法获取数据
    var result = await _groupRepo.getGroupCalendar(params);
    entity.value = GroupProCalendarUIRepEntity(data: result.getSucData());
    return entity.value;
  }
}

class GroupProCalendarUIRepEntity {
  GroupCalendarBizModel? data;

  GroupProCalendarUIRepEntity({this.data});

  @override
  String toString() {
    return 'WorkerFlowUIRepEntity{data: $data}';
  }
}
