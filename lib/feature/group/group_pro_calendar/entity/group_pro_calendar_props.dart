/// @date 2025/06/19
/// @description GroupProCalendar页入参
class GroupProCalendarProps {
  String workNoteId;
  String workNoteName;
  double? deptId;

  /// 是否是自己创建的账本，0：不是，1：是
  int isSelfCreated = 1;

  /// 是否是带班，0：不是，1：是
  int isAgent = 0;

  ///是否是班组长
  get isCreateByMySelf => isSelfCreated == 1;

  ///是否是班组长或者带班
  get isSelfCreatedOrAgent => isSelfCreated == 1 || isAgent == 1;

  /// 是否是带班
  get hasAgent => isAgent == 1;

  GroupProCalendarProps(
      {this.workNoteId = '',
      this.workNoteName = '',
      this.isSelfCreated = 1,
      this.isAgent = 0,
      this.deptId});

  @override
  String toString() {
    return 'GroupProCalendarProps{data: $workNoteId}';
  }
}
