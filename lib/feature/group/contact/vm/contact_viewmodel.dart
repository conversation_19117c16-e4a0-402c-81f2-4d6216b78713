import "package:gdjg_pure_flutter/data/contact_data/ds/param/worker_create_new_param_model.dart";
import "package:gdjg_pure_flutter/utils/ui_util/toast_util.dart";
import "package:get/get.dart";
import "../ui_rep/contact_ui_rep.dart";
import "protocol/contact_ui_state.dart";

/// @date 2025/06/25
/// @description Contact页ViewModel
/// 1. 处理数据的加载状态，如loading和错误视图等
/// 2. 观测UIRep中的业务实体转为UIState
/// 3. 接受来自view的意图并分发事件进行处理（暂无）
/// 4. 对View层暴露事件（操作视图和处理一些与系统权限交互等场景）
class ContactViewModel {
  var isLoading = false.obs;
  var isShowError = false.obs;
  var uiState = ContactUIState().obs;
  var uiRep = ContactUIRep();

  ContactViewModel() {
    // fetchData();
    ever(uiRep.entity, (value) {
      convertEntityToUIState(value);
    });
  }

  /// 手动添加工友
  void createWorker(String name, String tel) async {
    isLoading.value = true;
    try {
      // 同步获取数据
      var result = await uiRep
          .createWorker(WorkerCreateNewParamModel(name: name, tel: tel));
      ToastUtil.showToast("添加成功");
      //当调用成功重新拉取数据
      // fetchData();
      // 这里的result仅做弹窗之类的处理，不在此处转为UI实体
    } catch (e) {
      uiState.value.isShowError = true;
    } finally {
      isLoading.value = false;
    }
  }

  ///初始化数据同步，如果不需要loading，则在这里去掉
  void fetchData() async {
    isLoading.value = true;
    try {
      // 同步获取数据
      var result = await uiRep.fetchData();
      // 这里的result仅做弹窗之类的处理，不在此处转为UI实体
    } catch (e) {
      uiState.value.isShowError = true;
    } finally {
      isLoading.value = false;
    }
  }

  ///批量删除按钮点击
  void onDeleteTab() {

  }

  /// 将实体数据转换为UIState
  /// @param entity
  /// @returns
  void convertEntityToUIState(ContactUIRepEntity entity) {
    uiState.value.data = entity.data;
  }
}
