import 'package:gdjg_pure_flutter/data/contact_data/ds/param/worker_create_new_param_model.dart';
import 'package:gdjg_pure_flutter/data/contact_data/repo/contact_repo.dart';
import 'package:gdjg_pure_flutter/data/contact_data/repo/model/worker_create_new_biz_model.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/common_dialog.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/toast_util.dart';
import 'package:get/get.dart';

/// @date 2025/06/25
/// @description Contact页UI仓库
/// 1.为vm提供业务数据
/// 2.解耦视图层与业务仓库之间的耦合
class ContactUIRep {
  final _contactRepo = ContactRepo();

  /// 实体数据
  var entity = ContactUIRepEntity().obs;

  /// 手动添加工友
  Future<ContactUIRepEntity> createWorker(
      WorkerCreateNewParamModel param) async {
    final result = await _contactRepo.contactCreateWorker(param);
    if (!result.isOK()) {
      if (result.fail?.code == 2) {
        showCommonDialog(CommonDialogConfig(
          content: '所填写手机号已被删除，是否直接恢复？',
          positive: '恢复',
          negative: '取消',
          onPositive: () => {recoverWorker(param)}, //恢复选项
        ));
      }
    }
    // 返回成功的情况
    entity.value = ContactUIRepEntity(data: result.getSucData());
    // 模拟异常情况
    // throw Exception("error");
    return entity.value;
  }

  /// 如果成功，则将业务数据存储在entity中
  Future<ContactUIRepEntity> fetchData() async {
    // 调用网络的方法获取数据
    // await Future.delayed(const Duration(milliseconds: 2000));
    // 返回成功的情况
    entity.value = ContactUIRepEntity(data: null);
    // 模拟异常情况
    // throw Exception("error");
    return entity.value;
  }

  /// 恢复删除工友
  Future<ContactUIRepEntity> recoverWorker(
      WorkerCreateNewParamModel param) async {
    final result = await _contactRepo.recoverWorker(param);
    if (result.isOK()) {
      ToastUtil.showToast("恢复成功");
    }
    // 返回成功的情况
    entity.value = ContactUIRepEntity(data: null);
    return entity.value;
  }
}

///通讯录页面数据
class ContactUIRepEntity {
  //手动添加工友数据
  WorkerCreateNewBizModel? data;

  ContactUIRepEntity({this.data});

  @override
  String toString() {
    return 'ContactUIRepEntity{data: $data}';
  }
}
