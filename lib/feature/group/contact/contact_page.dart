import 'package:flutter/material.dart';
import 'package:gdjg_pure_flutter/utils/route_util/base_page/base_ful_page.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:get/get.dart';
import 'package:gdjg_pure_flutter/widget/page_error_view.dart';
import 'package:gdjg_pure_flutter/widget/page_loading_view.dart';
import '../../../widget/SearchInput.dart';
import '../../tabbar/view/add_worker_dialog.dart';
import 'vm/contact_viewmodel.dart';

/// @date 2025/06/25
/// @param props 页面路由参数
/// @returns
/// @description 通讯录页面入口
class ContactPage extends BaseFulPage {
  ContactPage({super.key})
      : super(
            appBar: YPAppBar(
                title: "工友通讯录", rightResText: '批量删除'));

  @override
  createState() => _ContactPageState();
}

class _ContactPageState extends BaseFulPageState {
  final ContactViewModel viewModel = ContactViewModel();
  var name = "ddd".obs;

  @override
  void onPageCreate() {
    super.onPageCreate();
    // 确保 widget.appBar 不为空
    dynamicRightResTap = () {
      viewModel.onDeleteTab();
    };
  }

  @override
  Widget yBuild(BuildContext context) {
    return Scaffold(
      body: Obx(() {
        if (viewModel.isLoading.value) {
          return const PageLoadingView();
        }
        if (viewModel.uiState.value.isShowError == true) {
          return PageErrorView(onReload: () => viewModel.fetchData());
        }
        return contentView();
      }),
    );
  }

  /// 实际展示的视图，需要处理：
  /// 1. 画出UI
  /// 2. 绑定UIState
  /// 3. 处理事件监听
  /// 4. 向VM传递来自View层的事件
  Widget contentView() {
    // 进行事件处理
    // handleContactVMEvent(props.vm)
    return Container(
      color: Colors.white, // 整体背景白色
      child: titleContentWidget(),
    );
  }

  /// 标题内容
  Widget titleContentWidget() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
        _buildWorkerSearchInput(),
        _buildAddWorkerTitle(),
        _buildNavigationFunc()
      ]),
    );
  }

  /// title组件
  Widget _buildAddWorkerTitle() {
    return Padding(
        padding: const EdgeInsets.only(top: 16),
        child: Text(
          '添加工友',
          style: TextStyle(
              fontSize: 19,
              fontWeight: FontWeight.bold,
              color: Colors.black), // 设置字体颜色为 #000000),
        ));
  }

  ///搜索框组件
  Widget _buildWorkerSearchInput() {
    return SearchInput(
      hintText: '请输入姓名或手机号查找',
      onChanged: (value) {
        print('当前输入值: $value');
      },
    );
  }

  ///生成导航栏功能
  Widget _buildIconText(IconData iconData, String label,
      {VoidCallback? onTap}) {
    return GestureDetector(
      onTap: onTap,
      behavior: HitTestBehavior.opaque, // 增大点击区域，防止点击空白无响应
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(iconData, size: 54, color: Colors.blue),
          const SizedBox(height: 8),
          Text(label,
              style: const TextStyle(fontSize: 14, color: Colors.black)),
        ],
      ),
    );
  }

  ///功能模块点击事件
  Widget _buildNavigationFunc() {
    return Builder(builder: (context) {
      return Padding(
        padding: const EdgeInsets.symmetric(horizontal: 34, vertical: 16),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            _buildIconText(Icons.favorite, "手动添加", onTap: () {
              //弹窗
              YPRoute.openDialog(
                  builder: (context) => AddWorkerDialog(
                        onConfirm: (name, phone) {
                          viewModel.createWorker(name, phone);
                        },
                      ));
            }),
            _buildIconText(Icons.star, "手机联系人", onTap: () {
              print('手机联系人点击了');
              // 你可以调用你要的逻辑
            }),
          ],
        ),
      );
    });
  }

  //通讯录大列表
  Widget listWidget() {
    //TODO 等富聪弄完
    return Text("wait");
  }

  @override
  Object? onSend2Previous() {
    return null;
  }
}
