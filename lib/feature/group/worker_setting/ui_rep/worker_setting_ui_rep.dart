import 'package:gdjg_pure_flutter/data/fee_standard_data/repo/model/fee_standard_biz_model.dart';
import 'package:gdjg_pure_flutter/data/note_time_data/ds/param/group_project_get_group_worker_settle_param_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/worker_info/ds/param/workers_get_worker_info_param_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/worker_info/repo/model/workers_get_worker_info_biz_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/worker_info/repo/worker_info_repo.dart';
import 'package:gdjg_pure_flutter/data/worker_data/worker_manager/ds/param/update_show_param_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/worker_manager/ds/param/worker_quit_param_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/worker_manager/repo/worker_manager_repo.dart';
import 'package:gdjg_pure_flutter/utils/system_util/yprint.dart';
import 'package:get/get.dart';

/// @date 2025/06/20
/// @description WorkerSetting页UI仓库
/// 1.为vm提供业务数据
/// 2.解耦视图层与业务仓库之间的耦合
class WorkerSettingUIRep {
  /// 实体数据
  var entity = WorkersGetWorkerInfoBizModel().obs;

  var noSettleMoney = ''.obs;

  final workerRepo = WorkerInfoRepo();

  final managerRepo = WorkerManagerRepo();

  bool getStatus() {
    return true;
  }

  /// 初始化数据同步，返回同步是否成功的状态给UI层
  /// 如果成功，则将业务数据存储在entity中
  Future<WorkersGetWorkerInfoBizModel> fetchWorkerInfo(int workerId) async {
    // 调用网络的方法获取数据
    var result =
        await workerRepo.fetchWorkerInfo(WorkersGetWorkerInfoParamModel(
      worker_id: workerId.toString(),
      dept_id: "41679",
    ));
    // var data = result.getSucData();
    // if (!(result.isOK() && data != null)) {
    //   throw Exception("data error");
    // }
    // entity.value = data;
    var mockData = WorkersGetWorkerInfoBizModel(
        id: 3174276,
        name: "武威",
        tel: "***********",
        namePy: "W",
        nameColor: "#74E8D5",
        memberId: 0.0,
        isDeleted: 0.0,
        corpId: 17596,
        isBind: 0.0,
        username: "",
        isShow: 2.0,
        isSelfCreated: 0.0,
        isAgent: 0.0,
        feeStandardInfo: FeeStandardBizModel(
          workingHoursStandard: "8.0",
          workingHoursPrice: "300.00",
          overtimeHoursStandard: "6.0",
          overtimeHoursPrice: "30.00",
          overtimeType: 1.0,
          businessType: "1",
          feeStandardId: "777773895951319040",
        ),
        contractorFeeStandardInfo: FeeStandardBizModel(
          workingHoursStandard: "0",
          workingHoursPrice: "0",
          overtimeHoursStandard: "0",
          overtimeHoursPrice: "0",
          overtimeType: 2.0,
          businessType: "6",
          feeStandardId: "0",
        ),
        grant: null,
        avatar: "",
        occ: OccBizModel(
          industryId: 2,
          industryName: "修/房屋翻新",
          occupationId: 301,
          occupationName: "电工/消防/弱电",
        ));

    entity.value = mockData;
    // 模拟异常情况
    return entity.value;
  }

  /// 获取工友所在项目中未结工资信息
  Future<String> fetchSettleInfo(
      GroupProjectGetGroupWorkerSettleParamModel param) async {
    // 调用网络的方法获取数据
    var result = await workerRepo.fetchWorkersSettle(param);
    final settleInfo = result.getSucData()?.notSettleMoney.toString() ?? '0.0';
    noSettleMoney.value = settleInfo;

    return noSettleMoney.value;
  }

  /// 更新失败返回负值
  Future<int> updatePermission(UpdateShowParamModel params) async {
    var result = await managerRepo.updateWorkerPermission(params);
    if (result.isOK()) {
      return params.isShow.value;
    }
    return -1;
  }

  /// 删除工友
  Future<bool> deleteWorker(int? workerId) async {
    if (workerId == null) {
      yprint('参数异常，无法删除');
      return false;
    }
    var result = await managerRepo.deleteWorker(workerId.toString());
    return result.isOK();
  }

  /// 工友退场
  Future<bool> workerQuit(int? workerId, String? workNoteId) async {
    if (workerId == null || workNoteId == null) {
      yprint('参数异常，无法退场');
      return false;
    }
    var result = await managerRepo.workerQuit(WorkerQuitParamModel(
      worker_id: workerId.toString(),
      dept_id: workNoteId,
    ));
    return result.isOK();
  }
}
