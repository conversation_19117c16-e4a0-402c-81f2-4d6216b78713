import 'package:flutter/material.dart';
import 'package:gdjg_pure_flutter/utils/route_util/base_page/base_ful_page.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';
import 'package:get/get.dart';
import 'package:gdjg_pure_flutter/widget/page_error_view.dart';
import 'package:gdjg_pure_flutter/widget/page_loading_view.dart';
import 'vm/group_un_liquidated_viewmodel.dart';

/// @date 2025/07/01
/// @param props 页面路由参数
/// @returns
/// @description 班组长未结页面
class GroupUnLiquidatedPage extends BaseFulPage {
  final String title; // 标题字段

  GroupUnLiquidatedPage({super.key, required this.title})
      : super(appBar: YPAppBar(title: title));

  @override
  createState() => _GroupUnLiquidatedPageState();
}

class _GroupUnLiquidatedPageState extends BaseFulPageState
    with SingleTickerProviderStateMixin {
  final GroupUnLiquidatedViewModel viewModel = GroupUnLiquidatedViewModel();
  late TabController _tabController;
  int unSettledCount = 4; // 未结工友数量
  int settledCount = 0; // 已结清数量

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _tabController.addListener(() {
      setState(() {}); // 强制重建 UI 来反映当前 tab 的选中状态
    });
  }

  @override
  void dispose() {
    _tabController.removeListener(() {});
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget yBuild(BuildContext context) {
    return Scaffold(
      body: Obx(() {
        if (viewModel.isLoading.value) {
          return const PageLoadingView();
        }
        if (viewModel.uiState.value.isShowError == true) {
          return PageErrorView(onReload: () => viewModel.fetchData());
        }
        return contentView();
      }),
    );
  }

  /// 实际展示的视图，需要处理：
  /// 1. 画出UI
  /// 2. 绑定UIState
  /// 3. 处理事件监听
  /// 4. 向VM传递来自View层的事件
  Widget contentView() {
    // 进行事件处理
    return Container(
      color: Colors.white,
      child: Column(children: [
        Container(
          color: Color(0XFFF5F5F5),
          height: 5,
        ),
        tabBarTitleView(),
        Expanded(child: tabBarContentView())
      ]),
    );
  }

  ///tabBar标题
  Widget tabBarTitleView() {
    return CustomTabBar(tabController: _tabController);
  }

  ///barBar内容
  Widget tabBarContentView() {
    return TabBarView(
      controller: _tabController,
      children: [
        UnSettledListWidget(), // 未结工友列表
        SettledListWidget(), // 已结清列表
      ],
    );
  }
}

//未结工友列表
class UnSettledListWidget extends StatelessWidget {
  const UnSettledListWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      itemCount: 4, // 示例数据
      itemBuilder: (context, index) {
        return ListTile(
          leading: CircleAvatar(child: Icon(Icons.person)),
          title: Text(['投得土地', '虐了', '木头父母肚兜头懂', '授权'][index]),
          trailing: Text(['555.00', '300.00', '127.00', '0.00'][index],
              style: TextStyle(color: Colors.blue)),
          onTap: () {
            // 点击事件
          },
        );
      },
    );
  }
}

///已结清列表
class SettledListWidget extends StatelessWidget {
  const SettledListWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      itemCount: 0, // 示例数据
      itemBuilder: (context, index) {
        return ListTile(
          leading: CircleAvatar(child: Icon(Icons.person)),
          title: Text('已结清项 $index'),
          trailing: Text('0.00', style: TextStyle(color: Colors.grey)),
        );
      },
    );
  }
}

///自定义tabBar
class CustomTabBar extends StatelessWidget {
  final TabController tabController;

  const CustomTabBar({super.key, required this.tabController});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // 自定义标签行，设置高度为 30
        SizedBox(
          height: 45,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              SizedBox(width: 16),
              CustomTabLabel(
                text: '未结工友(4)',
                isSelected: tabController.index == 0,
                onTap: () => tabController.animateTo(0),
              ),
              SizedBox(width: 40), //
              CustomTabLabel(
                text: '已结清(0)',
                isSelected: tabController.index == 1,
                onTap: () => tabController.animateTo(1),
              ),
            ],
          ),
        ),
        // 分割线，保持默认高度
        Divider(height: 1, thickness: 0.5),
      ],
    );
  }
}

class CustomTabLabel extends StatelessWidget {
  final String text;
  final bool isSelected;
  final VoidCallback onTap;

  const CustomTabLabel({
    super.key,
    required this.text,
    required this.isSelected,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Text(
        text,
        style: TextStyle(
          fontSize: 18,
          color: isSelected ? ColorsUtil.primaryColor : ColorsUtil.black85,
        ),
      ),
    );
  }
}
