import 'package:flutter/material.dart';
import 'package:gdjg_pure_flutter/utils/route_util/base_page/base_ful_page.dart';
import 'package:get/get.dart';
import 'package:gdjg_pure_flutter/widget/page_error_view.dart';
import 'package:gdjg_pure_flutter/widget/page_loading_view.dart';
import 'vm/group_un_liquidated_viewmodel.dart';

/// @date 2025/07/01
/// @param props 页面路由参数
/// @returns
/// @description 班组长未结页面
class GroupUnLiquidatedPage extends BaseFulPage {
  GroupUnLiquidatedPage({super.key}) : super(appBar: YPAppBar(title: "未结项目1"));

  @override
  createState() => _GroupUnLiquidatedPageState();
}

class _GroupUnLiquidatedPageState extends BaseFulPageState {
  final GroupUnLiquidatedViewModel viewModel = GroupUnLiquidatedViewModel();

  @override
  Widget yBuild(BuildContext context) {
    return Scaffold(
      body: Obx(() {
        if (viewModel.isLoading.value) {
          return const PageLoadingView();
        }
        if (viewModel.uiState.value.isShowError == true) {
          return PageErrorView(onReload: () => viewModel.fetchData());
        }
        return contentView();
      }),
    );
  }

  /// 实际展示的视图，需要处理：
  /// 1. 画出UI
  /// 2. 绑定UIState
  /// 3. 处理事件监听
  /// 4. 向VM传递来自View层的事件
  Widget contentView() {
    // 进行事件处理
    // handleContactVMEvent(props.vm)
    return Container(
      color: Colors.white, // 整体背景白色
      child: listWidget(),
    );
  }

  Widget listWidget() {
    return Text("wait");
  }
}
