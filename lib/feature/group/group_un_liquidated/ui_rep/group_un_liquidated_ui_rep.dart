import 'package:get/get.dart';

/// @date 2025/07/01
/// @description GroupUnLiquidated页UI仓库
/// 1.为vm提供业务数据
/// 2.解耦视图层与业务仓库之间的耦合
class GroupUnLiquidatedUIRep {
  /// 实体数据
  var entity = GroupUnLiquidatedUIRepEntity().obs;

  /// 初始化数据同步，返回同步是否成功的状态给UI层
  /// 如果成功，则将业务数据存储在entity中
  Future<GroupUnLiquidatedUIRepEntity> fetchData() async {
    // 返回成功的情况
    entity.value = GroupUnLiquidatedUIRepEntity(data: "success");
    // 模拟异常情况
    // throw Exception("error");
    return entity.value;
  }
}

class GroupUnLiquidatedUIRepEntity {
  String? data;

  GroupUnLiquidatedUIRepEntity({this.data});

  @override
  String toString() {
    return 'GroupUnLiquidatedUIRepEntity{data: $data}';
  }
}
