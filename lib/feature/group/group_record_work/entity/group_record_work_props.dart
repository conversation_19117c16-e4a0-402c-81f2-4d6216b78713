import 'dart:convert';

import 'package:gdjg_pure_flutter/data/worker_data/worker_info/repo/model/attendance_worker_biz_model.dart';
import 'package:gdjg_pure_flutter/feature/worker/worker_flow/vm/protocol/worker_flow_us.dart';

/// @date 2025/07/29
/// @description GroupRecordWork页入参
class GroupRecordWorkProps {
  /// 页面来源
  /// projectList : 项目列表
  /// workSheet : 考勤表
  GroupRecordWorkFrom? from;

  /// 记工的时间
  String? date;

  /// 记工类型
  BusinessType? businessType;

  /// 工人ID
  String? workerId;

  /// 项目 dept_id，用于切企业接口，如果有就请求接口，切换企业
  int deptId;

  GroupRecordWorkProps(
      {this.from,
      this.date,
      this.businessType,
      this.workerId,
      required this.deptId});

  @override
  String toString() {
    return jsonEncode(this);
  }
}

/// 页面来源
enum GroupRecordWorkFrom {
  /// 项目列表
  projectList('projectList'),

  /// 考勤表
  workSheet('workSheet');

  final String value;

  const GroupRecordWorkFrom(this.value);
}

class WorkerRecordProps {
  /// 工友集
  final List<WorkerBizModel> workers;

  /// 已经记工过的工友id集
  final List<String>? recordedIds;

  /// 未记工的工友id集
  final List<String>? noRecordedIds;

  WorkerRecordProps(
      {required this.workers, this.recordedIds, this.noRecordedIds});
}
