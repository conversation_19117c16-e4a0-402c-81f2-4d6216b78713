import 'package:flutter/material.dart';
import 'package:gdjg_pure_flutter/utils/route_util/base_page/base_ful_page.dart';
import 'package:gdjg_pure_flutter/utils/system_util/yprint.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';
import 'package:gdjg_pure_flutter/widget/round_tab_indicator_widget.dart';
import 'package:gdjg_pure_flutter/widget/tab_view/tab_view.dart';
import 'package:get/get.dart';
import 'vm/group_record_work_viewmodel.dart';

/// 班组记工页
/// @date 2025/07/29
/// @param props 页面路由参数
/// @returns
/// @description GroupRecordWork页面入口
class GroupRecordWorkPage extends BaseFulPage {
  GroupRecordWorkPage({super.key}) : super(appBar: YPAppBar(title: "标题"));

  @override
  State<GroupRecordWorkPage> createState() => _GroupRecordWorkPageState();
}

class _GroupRecordWorkPageState extends BaseFulPageState<GroupRecordWorkPage>
    with SingleTickerProviderStateMixin {
  final GroupRecordWorkViewModel viewModel = GroupRecordWorkViewModel();
  TabController? _tabController;

  @override
  void initState() {
    super.initState();
    viewModel.init();
    _tabController = TabController(
        length: viewModel.us.recordWorkBarItems.length, vsync: this);
  }

  @override
  dispose() {
    super.dispose();
    _tabController?.dispose();
  }

  @override
  void onPageRoute(Object? routeParams, bool fromLaunchTask) {
    super.onPageRoute(routeParams, fromLaunchTask);
    dynamicTitle = "班组记工页";
  }

  @override
  Widget yBuild(BuildContext context) {
    return _buildContentView();
  }

  Widget _buildContentView() {
    return Column(
      children: [
        Container(
          padding: EdgeInsets.all(16),
          height: 60,
          width: double.infinity,
          color: ColorsUtil.white65,
          child: Text(
            "日期：2025年07月29日",
            style: TextStyle(fontSize: 20, color: Colors.black),
          ),
        ),
        _buildPagerView(),
      ],
    );
  }

  Widget _buildPagerView() {
    return Expanded(
      child: Column(
        children: [
          TabBar(
              controller: _tabController,
              labelColor: Color(0xFF8A8A99),
              unselectedLabelColor: Color(0xFF8A8A99),
              labelStyle: const TextStyle(
                fontSize: 18,
              ),
              indicator: RoundUnderlineTabIndicator(
                borderSide:
                    BorderSide(width: 3, color: ColorsUtil.primaryColor),
              ),
              dividerHeight: 1,
              dividerColor: Color(0xFFE6E6E6),
              tabs: [
                Tab(
                  child: Text('工人'),
                ),
                Tab(
                  child: Text('总计'),
                ),
                Tab(
                  child: Text('工人3'),
                ),
                Tab(
                  child: Text('总计4'),
                ),
              ]),
          Expanded(
            child: Container(
              color: Colors.white,
              child: TabBarView(controller: _tabController, children: [
                _buildPageA(),
                _buildPageB(),
                _buildPageC(),
                _buildPageD(),
              ]),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPageA() {
    return Container(
        color: ColorsUtil.white65,
        child: Text("页面-Page A", style: TextStyle()));
  }

  Widget _buildPageB() {
    return Container(
        color: ColorsUtil.white65,
        child: Text("页面-Page B", style: TextStyle()));
  }

  Widget _buildPageC() {
    return Container(
        color: ColorsUtil.white65,
        child: Text("页面-Page C", style: TextStyle()));
  }

  Widget _buildPageD() {
    return Container(
        color: ColorsUtil.white65,
        child: Text("页面-Page D", style: TextStyle()));
  }
}
