import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/feature/group/group_record_work/entity/group_record_work_props.dart';
import 'package:gdjg_pure_flutter/feature/group/group_record_work/view/amount_worker_record_view.dart';
import 'package:gdjg_pure_flutter/feature/group/group_record_work/view/day_worker_record_view/day_worker_record_view.dart';
import 'package:gdjg_pure_flutter/feature/group/group_record_work/view/package_worker_record_view/package_worker_record_view.dart';
import 'package:gdjg_pure_flutter/feature/group/group_record_work/view/short_worker_record_view.dart';
import 'package:gdjg_pure_flutter/feature/worker/worker_flow/vm/protocol/worker_flow_us.dart';
import 'package:gdjg_pure_flutter/utils/route_util/base_page/base_ful_page.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';
import 'package:gdjg_pure_flutter/widget/round_tab_indicator_widget.dart';
import 'package:get/get.dart';

import 'vm/group_record_work_viewmodel.dart';

/// 班组记工页
/// @date 2025/07/29
/// @param props 页面路由参数
/// @returns
/// @description GroupRecordWork页面入口
class GroupRecordWorkPage extends BaseFulPage {
  GroupRecordWorkPage({super.key}) : super(appBar: YPAppBar(title: "标题"));

  @override
  State<GroupRecordWorkPage> createState() => _GroupRecordWorkPageState();
}

class _GroupRecordWorkPageState extends BaseFulPageState<GroupRecordWorkPage>
    with SingleTickerProviderStateMixin {
  late GroupRecordWorkProps props;
  late GroupRecordWorkViewModel viewModel;
  TabController? _tabController;

  @override
  onPageRoute(Object? routeParams, bool fromLaunchTask) {
    super.onPageRoute(routeParams, fromLaunchTask);
    dynamicTitle = "班组记工页";
    props = routeParams as GroupRecordWorkProps;
    viewModel = GroupRecordWorkViewModel(props);
    viewModel.init();
  }

  @override
  void initState() {
    super.initState();
    _tabController = TabController(
        length: viewModel.us.recordWorkBarItems.length, vsync: this);
  }

  @override
  dispose() {
    super.dispose();
    _tabController?.dispose();
  }

  @override
  Widget yBuild(BuildContext context) {
    return _buildContentView();
  }

  Widget _buildContentView() {
    return Column(
      children: [
        Container(
          padding: EdgeInsets.all(16),
          height: 60,
          width: double.infinity,
          color: ColorsUtil.white65,
          child: Text(
            "日期：2025年07月29日",
            style: TextStyle(fontSize: 20, color: Colors.black),
          ),
        ),
        _buildPagerView(),
      ],
    );
  }

  Widget _buildPagerView() {
    return Expanded(
      child: Column(
        children: [
          _buildTitleBar(),
          _buildPageContent(),
          _buildBottom(),
        ],
      ),
    );
  }

  Widget _buildTitleBar() {
    return Container(
      color: Colors.white,
      child: TabBar(
          controller: _tabController,
          labelColor: ColorsUtil.ypPrimaryColor,
          unselectedLabelColor: ColorsUtil.black85,
          labelStyle: const TextStyle(
            fontSize: 18,
          ),
          indicator: RoundUnderlineTabIndicator(
            borderSide: BorderSide(width: 3, color: ColorsUtil.ypPrimaryColor),
          ),
          dividerHeight: 1,
          dividerColor: ColorsUtil.ypBgColor,
          tabs: [
            Tab(
              child: Text(BusinessType.workAndAccountTypeDays.desc),
            ),
            Tab(
              child: Text(BusinessType.workAndAccountTypePackage.desc),
            ),
            Tab(
              child: Text(BusinessType.workAndAccountTypeWages.desc),
            ),
            Tab(
              child: Text(BusinessType.workAndAccountTypeLoad.desc),
            ),
          ]),
    );
  }

  Widget _buildPageContent() {
    return Expanded(
      child: Container(
        color: Colors.white,
        child: TabBarView(controller: _tabController, children: [
          _buildPageA(),
          _buildPageB(),
          _buildPageC(),
          _buildPageD(),
        ]),
      ),
    );
  }

  Widget _buildBottom() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.symmetric(horizontal: 16, vertical: 10),
      decoration: BoxDecoration(
        border: Border(
          top: BorderSide(width: 1, color: ColorsUtil.ypBgColor),
        ),
        color: Colors.white,
      ),
      child: Container(
          height: 48,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            color: ColorsUtil.ypPrimaryColor,
          ),
          child: TextButton(
            onPressed: () {},
            child: Obx(() {
              return Text(
                "确认记工(${viewModel.us.confirmWorkerCount})",
                style: TextStyle(
                    color: Colors.white,
                    fontSize: 17.sp,
                    fontWeight: FontWeight.bold),
              );
            }),
          )),
    );
  }

  Widget _buildPageA() {
    return Container(
      color: Colors.white,
      child: DayWorkerRecordView(
          props: WorkerRecordProps(
            workers: viewModel.us.dayWorkers,
            recordedIds: [3,4],
          )),
    );
  }

  Widget _buildPageB() {
    return Container(
        color: Colors.white,
        child: PackageWorkerRecordView(
          props: WorkerRecordProps(
            workers: viewModel.us.packageWorkers,
            recordedIds: [],
          ),
        ));
  }

  Widget _buildPageC() {
    return Container(color: Colors.white, child: ShortWorkerRecordView());
  }

  Widget _buildPageD() {
    return Container(color: Colors.white, child: AmountWorkerRecordView());
  }
}
