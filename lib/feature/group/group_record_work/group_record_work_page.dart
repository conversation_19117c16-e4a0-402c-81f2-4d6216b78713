import 'package:flutter/material.dart';
import 'package:gdjg_pure_flutter/utils/route_util/base_page/base_ful_page.dart';
import 'package:gdjg_pure_flutter/utils/system_util/yprint.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';
import 'package:get/get.dart';
import 'vm/group_record_work_viewmodel.dart';

/// 班组记工页
/// @date 2025/07/29
/// @param props 页面路由参数
/// @returns
/// @description GroupRecordWork页面入口
class GroupRecordWorkPage extends BaseFulPage {
  GroupRecordWorkPage({super.key}) : super(appBar: YPAppBar(title: "标题"));

  @override
  State<GroupRecordWorkPage> createState() => _GroupRecordWorkPageState();
}

class _GroupRecordWorkPageState extends BaseFulPageState<GroupRecordWorkPage> {
  final GroupRecordWorkViewModel viewModel = GroupRecordWorkViewModel();

  @override
  void initState() {
    super.initState();
    viewModel.init();
  }

  @override
  void onPageRoute(Object? routeParams, bool fromLaunchTask) {
    super.onPageRoute(routeParams, fromLaunchTask);
    dynamicTitle = "班组记工页";
  }

  @override
  Widget yBuild(BuildContext context) {
    return _buildContentView();
  }

  Widget _buildContentView() {
    return Column(
      children: [
        Container(
          padding: EdgeInsets.all(16),
          height: 60,
          width: double.infinity,
          color: ColorsUtil.white65,
          child: Text(
            "日期：2025年07月29日",
            style: TextStyle(fontSize: 20, color: Colors.black),
          ),
        ),
        _buildTabBar(),
        _buildPagerView(),
      ],
    );
  }

  Widget _buildTabBar() {
    return Obx(() {
      return Container(
        height: 48,
        child: ListView.builder(
          scrollDirection: Axis.horizontal,
          itemCount: viewModel.recordUs.recordWorkBarItems.length,
          itemBuilder: (context, index) {
            return Container(
              width: 72,
              color: ColorsUtil.white65,
              child: Text(
                viewModel.recordUs.recordWorkBarItems[index].type.desc,
                style: TextStyle(fontSize: 20, color: Colors.black),
              ),
            );
          },
        ),
      );
    });
  }

  Widget _buildPagerView() {
    return Obx(() {
      return Expanded(
          child: PageView.builder(
        itemCount: viewModel.recordUs.recordWorkBarItems.length,
        itemBuilder: (context, index) {
          return Text(
            "Page $index",
            style: TextStyle(fontSize: 20, color: Colors.black),
          );
        },
      ));
    });
  }
}
