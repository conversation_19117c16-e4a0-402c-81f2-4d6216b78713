import 'package:flutter/cupertino.dart';
import 'package:gdjg_pure_flutter/feature/group/group_record_work/entity/group_record_work_props.dart';
import 'package:gdjg_pure_flutter/feature/group/group_record_work/view/worker_add_select_view/entity/worker_add_select_props.dart';
import 'package:gdjg_pure_flutter/feature/group/group_record_work/view/worker_add_select_view/worker_add_select_view.dart';
import 'package:gdjg_pure_flutter/utils/system_util/yprint.dart';

/// 点工记工子页面
class DayWorkerRecordView extends StatefulWidget {
  final WorkerRecordProps props;

  const DayWorkerRecordView({super.key, required this.props});

  @override
  State<DayWorkerRecordView> createState() => _DayWorkerRecordViewState();
}

class _DayWorkerRecordViewState extends State<DayWorkerRecordView> {
  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        children: [
          Text('点工记工子页面'),
          WorkerAddSelectView(
              props: WorkerAddSelectProps(
            workers: widget.props.workers,
            recordedIds: widget.props.recordedIds,
            noRecordedIds: widget.props.noRecordedIds,
            onSelected: onSelected,
          )),
        ],
      ),
    );
  }

  void onSelected(List<String> workerIds) {
    yprint('点工选择记工的工友----$workerIds');
  }
}
