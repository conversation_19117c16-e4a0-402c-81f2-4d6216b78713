import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/feature/group/group_record_work/entity/group_record_work_props.dart';
import 'package:gdjg_pure_flutter/feature/group/group_record_work/view/worker_add_select_view/entity/worker_add_select_props.dart';
import 'package:gdjg_pure_flutter/feature/group/group_record_work/view/worker_add_select_view/worker_edit_list_view.dart';
import 'package:gdjg_pure_flutter/utils/system_util/yprint.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';

/// 点工记工子页面
class DayWorkerRecordView extends StatefulWidget {
  final WorkerRecordProps props;

  const DayWorkerRecordView({super.key, required this.props});

  @override
  State<DayWorkerRecordView> createState() => _DayWorkerRecordViewState();
}

class _DayWorkerRecordViewState extends State<DayWorkerRecordView>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        children: [
          Text('点工记工子页面'),
          WorkerAddSelectView(
              props: WorkerAddSelectProps(
            workers: widget.props.workers,
            recordedIds: widget.props.recordedIds,
            noRecordedIds: widget.props.noRecordedIds,
            onSelected: onSelected,
          )),
          _buildRecordAndAccounts(),
        ],
      ),
    );
  }

  void onSelected(List<double> workerIds) {
    yprint('点工选择记工的工友----$workerIds');
  }

  /// 记工和记工流水视图
  Widget _buildRecordAndAccounts() {
    return Expanded(
        child: Container(
      color: ColorsUtil.ypBgColor,
      child: TabBarView(controller: _tabController, children: [
        _buildRecordView(),
        _buildAccountView(),
      ]),
    ));
  }

  Widget _buildRecordView() {
    return Container(
      color: Colors.lightBlueAccent,
      height: 300.h,
      child: Column(),
    );
  }

  Widget _buildAccountView() {
    return Container(
      color: Colors.lightGreenAccent,
      height: 300.h,
      child: Column(),
    );
  }
}
