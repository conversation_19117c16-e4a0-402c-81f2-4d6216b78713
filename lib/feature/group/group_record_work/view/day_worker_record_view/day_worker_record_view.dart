import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/feature/group/group_record_work/entity/group_record_work_props.dart';
import 'package:gdjg_pure_flutter/feature/group/group_record_work/view/record_account_view/entity/record_account_list_pops.dart';
import 'package:gdjg_pure_flutter/feature/group/group_record_work/view/record_account_view/record_account_view.dart';
import 'package:gdjg_pure_flutter/feature/group/group_record_work/view/worker_add_select_view/entity/worker_add_select_props.dart';
import 'package:gdjg_pure_flutter/feature/group/group_record_work/view/worker_add_select_view/worker_edit_list_view.dart';
import 'package:gdjg_pure_flutter/utils/system_util/yprint.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';

/// 点工记工子页面
class DayWorkerRecordView extends StatefulWidget {
  final WorkerRecordProps props;

  const DayWorkerRecordView({super.key, required this.props});

  @override
  State<DayWorkerRecordView> createState() => _DayWorkerRecordViewState();
}

class _DayWorkerRecordViewState extends State<DayWorkerRecordView>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
        child: Container(
      color: ColorsUtil.ypBgColor,
      child: Column(
        children: [
          Text('点工记工子页面'),
          WorkerAddSelectView(
              props: WorkerAddSelectProps(
            workers: widget.props.workers,
            recordedIds: widget.props.recordedIds,
            noRecordedIds: widget.props.noRecordedIds,
            onSelected: onSelected,
          )),
          const SizedBox(
            height: 10,
          ),
          _buildTitleBar(),
          _buildRecordAndAccounts(),
        ],
      ),
    ));
  }

  void onSelected(List<double> workerIds) {
    yprint('点工选择记工的工友----$workerIds');
  }

  /// 构建记工-记工流水 titleBar
  Widget _buildTitleBar() {
    return Container(
        height: 50.h,
        color: ColorsUtil.ypBgColor,
        child: TabBar(
            controller: _tabController,
            labelColor: ColorsUtil.ypPrimaryColor,
            unselectedLabelColor: Colors.grey,
            // 未选中标签的颜色
            indicator: BoxDecoration(
              color: Colors.white, // 选中后的背景色为白色
            ),
            indicatorSize: TabBarIndicatorSize.tab,
            // 指示器大小与tab一致
            indicatorPadding: EdgeInsets.zero,
            // 去掉指示器的内边距，让指示器与tab完全重合
            dividerHeight: 0,
            // 去掉TabBar下方的分割线
            tabs: [
              Tab(
                child: Text(
                  '记工',
                  style: TextStyle(
                    fontSize: 15.sp,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              Tab(
                child: Text(
                  '记工流水',
                  style: TextStyle(
                    fontSize: 15.sp,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              )
            ]));
  }

  /// 记工和记工流水视图
  Widget _buildRecordAndAccounts() {
    return SizedBox(
      height: MediaQuery.of(context).size.height * 0.6, // 给TabBarView一个固定高度
      child: Container(
        color: ColorsUtil.ypBgColor,
        child: TabBarView(
          controller: _tabController,
          physics: const NeverScrollableScrollPhysics(), // 禁止左右滑动切换
          children: [
            _buildRecordView(),
            _buildAccountView(),
          ],
        ),
      ),
    );
  }

  Widget _buildRecordView() {
    return Container(
      color: Colors.lightBlueAccent,
      child: Column(),
    );
  }

  Widget _buildAccountView() {
    return RecordAccountView(props: RecordAccountListProps());
  }
}
