import 'package:flutter/material.dart';
import 'package:gdjg_pure_flutter/feature/group/group_record_work/entity/group_record_work_props.dart';
import 'package:gdjg_pure_flutter/feature/group/group_record_work/view/worker_add_select_view/entity/worker_add_select_props.dart';
import 'package:gdjg_pure_flutter/feature/group/group_record_work/view/worker_add_select_view/worker_edit_list_view.dart';
import 'package:gdjg_pure_flutter/utils/system_util/yprint.dart';

/// 包工记工子页面
class PackageWorkerRecordView extends StatefulWidget {
  final WorkerRecordProps props;

  const PackageWorkerRecordView({super.key, required this.props});

  @override
  State<PackageWorkerRecordView> createState() =>
      _PackageWorkerRecordViewState();
}

class _PackageWorkerRecordViewState extends State<PackageWorkerRecordView> {
  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        children: [
          Text('包工记工子页面'),
          WorkerAddSelectView(
              props: WorkerAddSelectProps(
            workers: widget.props.workers,
            recordedIds: widget.props.recordedIds,
            noRecordedIds: widget.props.noRecordedIds,
            onSelected: onSelected,
          )),
        ],
      ),
    );
  }

  void onSelected(List<double> workerIds) {
    yprint('包工选择记工的工友----${workerIds}');
  }
}
