import 'package:gdjg_pure_flutter/data/group_data/group_pro_bill/repo/model/group_business_get_group_business_list_biz_model.dart';
import 'package:gdjg_pure_flutter/feature/group/group_record_work/view/record_account_view/entity/record_account_list_us.dart';

/// 更新记工流水数据
typedef UpdateAccountListProps = void Function();

class RecordAccountListProps {
  /// 工友流水统计数据
  final GroupBusinessGetGroupBusinessListBizModel? business;

  /// 身份，没有传，取store 里面的 1进入班组本，2进入个人本
  final RecordIdentityType? identity;

  /// 是否是代班
  final bool? isAgent;

  RecordAccountListProps({this.business, this.identity, this.isAgent});
}
