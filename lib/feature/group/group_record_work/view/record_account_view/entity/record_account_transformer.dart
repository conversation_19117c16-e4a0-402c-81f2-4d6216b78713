import 'dart:ui';

import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_pro_bill/repo/model/group_business_get_group_business_list_biz_model.dart';
import 'package:gdjg_pure_flutter/feature/group/group_record_work/view/record_account_view/entity/record_account_list_pops.dart';
import 'package:gdjg_pure_flutter/feature/group/group_record_work/view/record_account_view/entity/record_account_list_us.dart';
import 'package:gdjg_pure_flutter/feature/worker/worker_flow/vm/protocol/worker_flow_us.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';

class RecordAccountTransformer {
  /// 转换记工流水数据：展平itemList集合，取出每个item中的数据:
  /// item.date 赋值给RecordAccountListUS.date;
  /// item.list 展平后，取出每个item中的数据: 构造RecordAccountListUS对象
  static List<RecordAccountListUS> transformListUS(
      RecordAccountListProps props) {
    final itemList = props.business?.list ?? [];
    final list = itemList.expand((element) => element.list.map((e) =>
        transformAccountItemUS(
            e,
            element.date,
            element.list.any((item) => item.confirm == 0),
            props.identity,
            props.isAgent)));

    return list.toList();
  }

  static RecordAccountListUS transformAccountItemUS(
    GroupBusinessGetGroupBusinessListBBizModel subItem,
    String date,
    bool hasNoConfirm,
    RecordIdentityType? identity,
    bool? isAgent,
  ) {
    return RecordAccountListUS(
      header: RecordAccountHeader(date: date, hasNoConfirm: hasNoConfirm),
      // 使用父级item的date
      id: subItem.id,
      // 转换为String
      note: subItem.note,
      // 备注
      hasVideo: subItem.hasVideo > 0,
      // 根据业务需求设置，这里默认false
      hasImg: subItem.hasImg > 0,
      // 转换为bool
      businessType: subItem.businessType,
      // 记工类型
      money: subItem.money,
      // 记的钱
      feeMoney: subItem.feeMoney,
      // 费用金额
      feeStandardId: subItem.feeStandardId,
      // 工资规则ID
      workNote: subItem.workNote,
      // 账本
      workerId: subItem.workerId,
      // 工人id
      workerName: subItem.workerName,
      // 工人名字
      unitNum: subItem.unitNum,
      // 工量数量
      unitWorkTypeName: subItem.unitWorkTypeName,
      // 工量类型名称
      unitWorkTypeUnit: subItem.unitWorkTypeUnit,
    );
  }

  static Map<double, TextStyle> getTextStyleMap() {
    return {
      BusinessType.workAndAccountTypeDays.code.toDouble(): TextStyle(
        fontSize: 12.sp,
        color: ColorsUtil.ypPrimaryColor,
      ),
      BusinessType.workAndAccountTypePackage.code.toDouble(): TextStyle(
        fontSize: 12.sp,
        color: ColorsUtil.rgb(145, 204, 155),
      ),
      BusinessType.workAndAccountTypeWages.code.toDouble(): TextStyle(
        fontSize: 12.sp,
        color: ColorsUtil.rgb(129, 153, 226),
      ),
      BusinessType.workAndAccountTypeLoad.code.toDouble(): TextStyle(
        fontSize: 12.sp,
        color: ColorsUtil.rgb(225, 170, 80),
      ),
      BusinessType.workAndAccountTypeDebt.code.toDouble(): TextStyle(
        fontSize: 12.sp,
        color: ColorsUtil.rgb(224, 127, 129),
      ),
      BusinessType.workAndAccountTypeWageLast.code.toDouble(): TextStyle(
        fontSize: 16,
        color: ColorsUtil.rgb(142, 186, 166),
      ),
    };
  }
}
