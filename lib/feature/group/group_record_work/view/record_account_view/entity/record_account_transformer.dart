import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_pro_bill/repo/model/group_business_get_group_business_list_biz_model.dart';
import 'package:gdjg_pure_flutter/feature/group/group_record_work/view/record_account_view/entity/record_account_list_pops.dart';
import 'package:gdjg_pure_flutter/feature/group/group_record_work/view/record_account_view/entity/record_account_list_us.dart';
import 'package:gdjg_pure_flutter/feature/worker/worker_flow/vm/protocol/worker_flow_us.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';

class RecordAccountTransformer {
  /// 转换记工流水数据：展平itemList集合，取出每个item中的数据:
  /// item.date 赋值给RecordAccountListUS.date;
  /// item.list 展平后，取出每个item中的数据: 构造RecordAccountListUS对象
  static List<RecordAccountListUS> transformListUS(
      RecordAccountListProps props) {
    final itemList = props.business?.list ?? [];
    final list = itemList.expand((element) => element.list.map((e) =>
        transformAccountItemUS(
            e,
            element.date,
            element.list.any((item) => item.confirm == 0),
            props.identity,
            props.isAgent)));

    return list.toList();
  }

  static RecordAccountListUS transformAccountItemUS(
    GroupBusinessGetGroupBusinessListBBizModel subItem,
    String date,
    bool hasNoConfirm,
    RecordIdentityType? identity,
    bool? isAgent,
  ) {
    return RecordAccountListUS(
      date: date,
      header: RecordAccountHeader(date: date, hasNoConfirm: hasNoConfirm),
      // 使用父级item的date
      id: subItem.id,
      // 转换为String
      note: subItem.note,
      // 备注
      hasVideo: subItem.hasVideo > 0,
      // 根据业务需求设置，这里默认false
      hasImg: subItem.hasImg > 0,
      // 转换为bool
      businessType: subItem.businessType,
      // 记工类型
      money: subItem.money,
      // 记的钱
      feeMoney: subItem.feeMoney,
      // 费用金额
      feeStandardId: subItem.feeStandardId,
      // 工资规则ID
      workNote: subItem.workNote,
      // 账本
      workerId: subItem.workerId,
      // 工人id
      workerName: subItem.workerName,
      // 工人名字
      unitNum: subItem.unitNum,
      // 工量数量
      unitWorkTypeName: subItem.unitWorkTypeName,
      // 工量类型名称
      unitWorkTypeUnit: subItem.unitWorkTypeUnit,
      confirm: subItem.confirm,
    );
  }

  /// 根据记工类型获取标签样式
  static AccountTypeTagStyle? getAccountTagStyleByType(double? type) {
    final map = getTypeStyleMap();
    return map[type];
  }

  static Map<double, AccountTypeTagStyle> getTypeStyleMap() {
    return {
      BusinessType.workAndAccountTypeDays.code.toDouble(): AccountTypeTagStyle(
        tag: '点',
        textColor: ColorsUtil.ypPrimaryColor,
        bgColor: ColorsUtil.rgb(241, 243, 253),
      ),
      BusinessType.workAndAccountTypePackage.code.toDouble():
          AccountTypeTagStyle(
        tag: '包',
        textColor: ColorsUtil.rgb(145, 204, 155),
        bgColor: ColorsUtil.rgb(238, 250, 236),
      ),
      BusinessType.workAndAccountTypeWages.code.toDouble(): AccountTypeTagStyle(
        tag: '短',
        textColor: ColorsUtil.rgb(129, 153, 226),
        bgColor: ColorsUtil.rgb(240, 245, 255),
      ),
      BusinessType.workAndAccountTypeLoad.code.toDouble(): AccountTypeTagStyle(
        tag: '量',
        textColor: ColorsUtil.rgb(225, 170, 80),
        bgColor: ColorsUtil.rgb(250, 243, 225),
      ),
      BusinessType.workAndAccountTypeDebt.code.toDouble(): AccountTypeTagStyle(
        tag: '借',
        bgColor: ColorsUtil.rgb(254, 242, 244),
        textColor: ColorsUtil.rgb(224, 127, 129),
      ),
      BusinessType.workAndAccountTypeWageLast.code.toDouble():
          AccountTypeTagStyle(
        tag: '结',
        textColor: ColorsUtil.rgb(142, 186, 166),
        bgColor: ColorsUtil.rgb(235, 252, 244),
      ),
    };
  }

  /// 根据记工状态获取标签样式
  /// @param hasJieZhi 是否包含借支结清数据
  static Map<double, AccountStateTagStyle> getStateStyleMap(bool hasJieZhi) {
    return {
      RecordConfirmType.unconfirmed.code: AccountStateTagStyle(
        tag: hasJieZhi ? '已对账' : '未对工',
        textColor: ColorsUtil.rgb(128, 128, 128),
        bgColor: Color(0xffF5F6FA),
      ),
      RecordConfirmType.confirmedYes.code: AccountStateTagStyle(
        tag: hasJieZhi ? '已对账' : '已对工',
        textColor: Colors.white,
        bgColor: Color(0xff5290fd),
      ),
      RecordConfirmType.confirmedNo.code: AccountStateTagStyle(
        tag: hasJieZhi ? '对账有误' : '对工有误',
        textColor: Colors.white,
        bgColor: Color(0xffffa011),
      ),
    };
  }

  static AccountStateTagStyle? getStateStyleByConfirm(
      double? confirm, bool hasJieZhi) {
    final realConfirm = confirm ?? RecordConfirmType.unconfirmed.code;
    final map = getStateStyleMap(hasJieZhi);
    return map[realConfirm];
  }
}

/// 记工流水类型标签样式
class AccountTypeTagStyle {
  /// 背景色
  final Color bgColor;

  /// 文字颜色
  final Color textColor;

  /// 标签文字
  final String tag;

  AccountTypeTagStyle({
    required this.tag,
    required this.bgColor,
    required this.textColor,
  });
}

/// 记工流水状态标签样式
class AccountStateTagStyle {
  /// 背景色
  final Color bgColor;

  /// 文字颜色
  final Color textColor;

  /// 标签文字
  final String tag;

  AccountStateTagStyle({
    required this.tag,
    required this.bgColor,
    required this.textColor,
  });
}
