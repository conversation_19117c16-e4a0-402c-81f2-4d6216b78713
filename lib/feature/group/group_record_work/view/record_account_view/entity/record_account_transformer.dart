import 'dart:ui';

import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_pro_bill/repo/model/group_business_get_group_business_list_biz_model.dart';
import 'package:gdjg_pure_flutter/feature/group/group_record_work/view/record_account_view/entity/record_account_list_pops.dart';
import 'package:gdjg_pure_flutter/feature/group/group_record_work/view/record_account_view/entity/record_account_list_us.dart';
import 'package:gdjg_pure_flutter/feature/worker/worker_flow/vm/protocol/worker_flow_us.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';

class RecordAccountTransformer {
  /// 转换记工流水数据：展平itemList集合，取出每个item中的数据:
  /// item.date 赋值给RecordAccountListUS.date;
  /// item.list 展平后，取出每个item中的数据: 构造RecordAccountListUS对象
  static List<RecordAccountListUS> transformListUS(
      RecordAccountListProps props) {
    final itemList = props.business?.list ?? [];
    final list = itemList.expand((element) => element.list.map((e) =>
        transformAccountItemUS(
            e,
            element.date,
            element.list.any((item) => item.confirm == 0),
            props.identity,
            props.isAgent)));

    return list.toList();
  }

  static RecordAccountListUS transformAccountItemUS(
    GroupBusinessGetGroupBusinessListBBizModel subItem,
    String date,
    bool hasNoConfirm,
    RecordIdentityType? identity,
    bool? isAgent,
  ) {
    return RecordAccountListUS(
      header: RecordAccountHeader(date: date, hasNoConfirm: hasNoConfirm),
      // 使用父级item的date
      id: subItem.id,
      // 转换为String
      note: subItem.note,
      // 备注
      hasVideo: subItem.hasVideo > 0,
      // 根据业务需求设置，这里默认false
      hasImg: subItem.hasImg > 0,
      // 转换为bool
      businessType: subItem.businessType,
      // 记工类型
      money: subItem.money,
      // 记的钱
      feeMoney: subItem.feeMoney,
      // 费用金额
      feeStandardId: subItem.feeStandardId,
      // 工资规则ID
      workNote: subItem.workNote,
      // 账本
      workerId: subItem.workerId,
      // 工人id
      workerName: subItem.workerName,
      // 工人名字
      unitNum: subItem.unitNum,
      // 工量数量
      unitWorkTypeName: subItem.unitWorkTypeName,
      // 工量类型名称
      unitWorkTypeUnit: subItem.unitWorkTypeUnit,
    );
  }

  /// 根据记工类型获取标签样式
  static AccountTagStyle? getAccountTagStyleByType(double? type) {
    final map = getTextStyleMap();
    return map[type];
  }

  static Map<double, AccountTagStyle> getTextStyleMap() {
    return {
      BusinessType.workAndAccountTypeDays.code.toDouble(): AccountTagStyle(
        tag: '点',
        textColor: ColorsUtil.ypPrimaryColor,
        bgColor: ColorsUtil.rgb(241, 243, 253),
      ),
      BusinessType.workAndAccountTypePackage.code.toDouble(): AccountTagStyle(
        tag: '包',
        textColor: ColorsUtil.rgb(145, 204, 155),
        bgColor: ColorsUtil.rgb(238, 250, 236),
      ),
      BusinessType.workAndAccountTypeWages.code.toDouble(): AccountTagStyle(
        tag: '短',
        textColor: ColorsUtil.rgb(129, 153, 226),
        bgColor: ColorsUtil.rgb(240, 245, 255),
      ),
      BusinessType.workAndAccountTypeLoad.code.toDouble(): AccountTagStyle(
        tag: '量',
        textColor: ColorsUtil.rgb(225, 170, 80),
        bgColor: ColorsUtil.rgb(250, 243, 225),
      ),
      BusinessType.workAndAccountTypeDebt.code.toDouble(): AccountTagStyle(
        tag: '借',
        bgColor: ColorsUtil.rgb(254, 242, 244),
        textColor: ColorsUtil.rgb(224, 127, 129),
      ),
      BusinessType.workAndAccountTypeWageLast.code.toDouble(): AccountTagStyle(
        tag: '结',
        textColor: ColorsUtil.rgb(142, 186, 166),
        bgColor: ColorsUtil.rgb(235, 252, 244),
      ),
    };
  }
}

/// 记工流水标签样式
class AccountTagStyle {
  /// 背景色
  final Color bgColor;

  /// 背景色
  final Color textColor;

  /// 标签文字
  final String tag;

  AccountTagStyle({
    required this.tag,
    required this.bgColor,
    required this.textColor,
  });
}
