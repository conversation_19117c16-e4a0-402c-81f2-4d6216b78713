import 'dart:convert';

class RecordAccountListUS {
  double id;

  String? note;
  bool? hasVideo;
  bool? hasImg;
  RecordAccountHeader header;

  /// 记工类型 [BusinessType]
  double? businessType;

  /// 记的钱
  String? money;
  double? feeMoney;
  double? feeStandardId;
  double? workNote;

  /// 工人id
  double? workerId;

  /// 工人名字
  String? workerName;
  String? unitNum;
  String? unitWorkTypeName;
  String? unitWorkTypeUnit;

  /// 身份，没有传，取store 里面的 1进入班组本，2进入个人本
  RecordIdentityType? identity;

  /// 是否是代班
  bool? isAgent;

  RecordAccountListUS({
    required this.id,
    required this.header,
    this.note,
    this.hasVideo,
    this.hasImg,
    this.businessType,
    this.money,
    this.feeMoney,
    this.feeStandardId,
    this.workNote,
    this.workerId,
    this.workerName,
    this.unitNum,
    this.unitWorkTypeName,
    this.unitWorkTypeUnit,
    this.identity,
    this.isAgent,
  });

  @override
  String toString() {
    return "RecordAccountListUS{" "id: $id, " "note: $note, " "hasVideo: $hasVideo, " "hasImg: $hasImg, " "header: $header, " +
        "businessType: $businessType, " +
        "money: $money, " +
        "feeMoney: $feeMoney, " +
        "feeStandardId: $feeStandardId, " +
        "workNote: $workNote, " +
        "workerId: $workerId, " +
        "workerName: $workerName, " +
        "unitNum: $unitNum, " +
        "unitWorkTypeName: $unitWorkTypeName, " +
        "unitWorkTypeUnit: $unitWorkTypeUnit, " +
        "identity: $identity, " +
        "isAgent: $isAgent, " +
        "}";
  }
}

enum RecordIdentityType {
  /// 进入班组本
  group(1),

  /// 进入个人本
  personal(2);

  final int value;

  const RecordIdentityType(this.value);
}

class RecordAccountHeader {
  /// 日期
  String date;

  /// 是否有为对工的记工流水
  bool? hasNoConfirm;

  RecordAccountHeader({
    required this.date,
    this.hasNoConfirm,
  });

  @override
  String toString() {
    return "RecordAccountHeader {date: $date, hasNoConfirm: $hasNoConfirm}";
  }
}
