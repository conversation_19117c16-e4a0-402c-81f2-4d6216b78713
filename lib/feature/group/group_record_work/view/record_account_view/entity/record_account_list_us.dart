class RecordAccountListUS {
  String? id;
  String? note;
  bool? hasVideo;
  bool? hasImg;

  /// 记工类型 [BusinessType]
  double? businessType;

  /// 记的钱
  String? money;
  double? feeMoney;
  double? feeStandardId;
  double? workNote;

  /// 工人id
  double? workerId;

  /// 工人名字
  String? workerName;
  String? unitNum;
  String? unitWorkTypeName;
  String? unitWorkTypeUnit;

  /// [4.5]新增 手动对工状态 0-未对工 1-对工无成 2-对工有误
  double? confirm;

  RecordAccountListUS({
    this.id,
    this.note,
    this.hasVideo,
    this.hasImg,
    this.businessType,
    this.money,
    this.feeMoney,
    this.feeStandardId,
    this.workNote,
    this.workerId,
    this.workerName,
    this.unitNum,
    this.unitWorkTypeName,
    this.unitWorkTypeUnit,
    this.confirm,
  });
}
