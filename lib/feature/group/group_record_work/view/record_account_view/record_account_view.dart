import 'package:flutter/cupertino.dart';
import 'package:gdjg_pure_flutter/feature/group/group_record_work/view/record_account_view/entity/record_account_list_pops.dart';
import 'package:gdjg_pure_flutter/feature/group/group_record_work/view/record_account_view/entity/record_account_list_us.dart';
import 'package:gdjg_pure_flutter/feature/group/group_record_work/view/record_account_view/entity/record_account_transformer.dart';
import 'package:grouped_list/grouped_list.dart';

/// 记工流水页
class RecordAccountView extends StatefulWidget {
  final RecordAccountListProps props;

  const RecordAccountView({super.key, required this.props});

  @override
  State<RecordAccountView> createState() => _RecordAccountViewState();
}

class _RecordAccountViewState extends State<RecordAccountView> {
  late List<RecordAccountListUS> list = [];

  @override
  void initState() {
    super.initState();
    list = RecordAccountTransformer.transformListUS(
        widget.props);
  }

  @override
  Widget build(BuildContext context) {
    return Center(
      child: GroupedListView<RecordAccountListUS, RecordAccountHeader>(
        elements: list,
        groupBy: (element) => element.header,
        groupSeparatorBuilder: (RecordAccountHeader groupValue) =>
            _buildHeader(groupValue),
        itemBuilder: (context, RecordAccountListUS element) =>
            _buildListItem(element),
        order: GroupedListOrder.ASC,
        useStickyGroupSeparators: true,
        floatingHeader: true,
        physics: const BouncingScrollPhysics(),
      ),
    );
  }

  Widget _buildListItem(RecordAccountListUS item) {
    return Container(
      child: Column(
        children: [
          _buildItemTop(item),
          _buildItemBottom(item),
        ],
      ),
    );
  }

  Widget _buildItemTop(RecordAccountListUS item) {
    return Container(
      child: Column(
        children: [
          _buildAccountInfo(item),
        ],
      ),
    );
  }

  Widget _buildItemBottom(RecordAccountListUS item) {

  }

  Widget _buildHeader(RecordAccountHeader groupValue) {
    return Container();
  }

  Widget _buildAccountInfo(RecordAccountListUS item) {
    return Container(
      child: Row(
        children: [
          _buildAccountLeft(item),
          _buildAccountRight(item),
        ],
      ),
    );
  }

  Widget _buildAccountLeft(RecordAccountListUS item) {
    return Container(
      child: Column(
        children: [
          Row(
            children: [
              Text('${item.workerName}', style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold)),
            ],
          ),
          Text('分项'),
          Text('工程量：${item.workerName}'),
        ],
      ),
    );
  }

  Widget _buildAccountRight(RecordAccountListUS item) {
    return Container(
      child: Row(
        children: [
          Text(item.workerName ?? ''),
        ],
      ),
    );
  }

  Widget _buildItemBottom(RecordAccountListUS item) {

  }
}
