import 'package:flutter/cupertino.dart';
import 'package:gdjg_pure_flutter/feature/group/group_record_work/view/record_account_view/entity/record_account_list_pops.dart';

/// 记工流水页
class RecordAccountView extends StatefulWidget {
  final RecordAccountListProps props;

  const RecordAccountView({super.key, required this.props});

  @override
  State<RecordAccountView> createState() => _RecordAccountViewState();
}

class _RecordAccountViewState extends State<RecordAccountView> {

  @override
  Widget build(BuildContext context) {
    return Center(
      child: ListView.builder(
          itemCount: 100,
          itemBuilder: (context, index) {
            final item = widget.props.businessList[index];
            return _buildListItem(item);
          }),
    );
  }

  Widget _buildListItem() {
    return Container();
  }
}
