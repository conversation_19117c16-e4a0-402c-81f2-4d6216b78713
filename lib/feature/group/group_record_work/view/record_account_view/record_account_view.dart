import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/feature/group/group_record_work/view/record_account_view/entity/record_account_list_pops.dart';
import 'package:gdjg_pure_flutter/feature/group/group_record_work/view/record_account_view/entity/record_account_list_us.dart';
import 'package:gdjg_pure_flutter/feature/group/group_record_work/view/record_account_view/entity/record_account_transformer.dart';
import 'package:gdjg_pure_flutter/utils/system_util/yprint.dart';
import 'package:gdjg_pure_flutter/widget/label/text_label.dart';
import 'package:grouped_list/grouped_list.dart';

/// 记工流水页
class RecordAccountView extends StatefulWidget {
  final RecordAccountListProps props;

  const RecordAccountView({super.key, required this.props});

  @override
  State<RecordAccountView> createState() => _RecordAccountViewState();
}

class _RecordAccountViewState extends State<RecordAccountView> {
  late List<RecordAccountListUS> list = [];

  @override
  void initState() {
    super.initState();
    list = RecordAccountTransformer.transformListUS(widget.props);
  }

  @override
  Widget build(BuildContext context) {
    yprint('list ------- ${list.toString()}');
    return Center(
      child: GroupedListView<RecordAccountListUS, String>(
        elements: list,
        groupBy: (element) => element.date,
        groupSeparatorBuilder: (String groupValue) => _buildHeader(groupValue),
        itemBuilder: (context, RecordAccountListUS element) =>
            _buildListItem(element),
        order: GroupedListOrder.ASC,
        useStickyGroupSeparators: true,
        floatingHeader: true,
        physics: const BouncingScrollPhysics(),
      ),
    );
  }

  Widget _buildListItem(RecordAccountListUS item) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
      color: Colors.white,
      child: Column(
        children: [
          _buildItemTop(item),
          _buildItemBottom(item),
        ],
      ),
    );
  }

  Widget _buildItemTop(RecordAccountListUS item) {
    return Container(
      child: Column(
        children: [
          _buildAccountInfo(item),
        ],
      ),
    );
  }

  Widget _buildItemBottom(RecordAccountListUS item) {
    return Container();
  }

  Widget _buildHeader(String groupValue) {
    return Container();
  }

  Widget _buildAccountInfo(RecordAccountListUS item) {
    return Container(
      child: Row(
        children: [
          _buildAccountLeft(item),
          _buildAccountRight(item),
        ],
      ),
    );
  }

  Widget _buildAccountLeft(RecordAccountListUS item) {
    return Container(
      child: Column(
        children: [
          Row(
            spacing: 8.w,
            children: [
              Text('${item.workerName}',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold)),
              _buildTag(item.businessType),
              TextLabel(label: '为对工'),
            ],
          ),
          Text(item.unitWorkTypeName ?? '默认'),
          Text('工程量：${item.workerName}'),
        ],
      ),
    );
  }

  Widget _buildAccountRight(RecordAccountListUS item) {
    return Container(
      child: Row(
        children: [Text(item.workerName ?? '')],
      ),
    );
  }

  Widget _buildTag(double? type) {
    final style = RecordAccountTransformer.getAccountTagStyleByType(type);
    if (style == null) {
      return Container();
    }

    return Container(
      margin: EdgeInsets.only(left: 5.w),
      padding: EdgeInsets.symmetric(horizontal: 4.w),
      decoration: BoxDecoration(
        color: style.bgColor,
        borderRadius: BorderRadius.circular(4.w),
      ),
      child: Text(
        style.tag,
        style: TextStyle(
          color: style.textColor,
          fontSize: 12.sp,
        ),
      ),
    );
  }
}
