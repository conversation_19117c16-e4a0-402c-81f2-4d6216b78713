import 'package:gdjg_pure_flutter/feature/group/group_record_work/view/worker_add_select_view/entity/worker_add_select_props.dart';
import 'package:gdjg_pure_flutter/feature/group/group_record_work/view/worker_add_select_view/vm/worker_edit_list_us.dart';
import 'package:gdjg_pure_flutter/utils/system_util/yprint.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/toast_util.dart';

class WorkerAddSelectViewModel {
  final us = WorkerEditListUS();

  final WorkerAddSelectProps props;

  WorkerAddSelectViewModel({required this.props});

  void initPage() {
    if (props.workers.isEmpty) {
      return;
    }
    us.setSelectedIds([2, 3]);
    us.setRecordedIds(props.recordedIds ?? []);
    us.setNoRecordedIds(props.noRecordedIds ?? []);
    us.setWorkers(props.workers
        .map((e) => WorkerEditItemUIState(
              id: e.id,
              name: e.name,
              avatar: e.avatar,
              isRecorded: us.recordedIds.contains(e.id),
            ))
        .toList());
  }

  /// 点击工友列表项
  void onItemSelectClick(double workerId) {
    if (us.selectedIds.contains(workerId)) {
      yprint('取消选择工友');

      /// 取消选择工友
      us.setSelectedIds(
          us.selectedIds.where((element) => element != workerId).toList());
      props.onSelected(us.selectedIds);
      return;
    }

    yprint('选择工友');

    /// 选择工友
    us.setSelectedIds([...us.selectedIds, workerId]);
    props.onSelected(us.selectedIds);
  }

  void onWorkerAddClick() {
    ToastUtil.showToast('跳转到工友通讯录页面');
  }

  void onWorkerReduceClick() {
    ToastUtil.showToast('跳转到工友退场页面');
  }

  /// 跳转到工价设置页面
  void routeToSalarySetting() {
    ToastUtil.showToast('跳转到工价设置页面');
  }

  /// 全选/取消全选按钮点击
  void onAllSelectClick() {
    if (us.selectedIds.length == us.workers.length) {
      /// 取消全选
      us.setSelectedIds([]);
      props.onSelected(us.selectedIds);
      return;
    }

    /// 全选
    us.setSelectedIds(us.workers.map((e) => e.id).toList());
    props.onSelected(us.selectedIds);
  }
}
