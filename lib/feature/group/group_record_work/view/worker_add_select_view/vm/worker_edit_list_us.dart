import 'package:get/get.dart';

class WorkerEditListUS {
  /// 工友列表
  final _workers = <WorkerEditItemUIState>[].obs;

  /// 列表选中的工友id集
  final _selectedIds = <double>[].obs;

  /// 已经记工过的工友id集
  final _recordedIds = <double>[].obs;

  /// 未记工的工友id集
  final _noRecordedIds = <double>[].obs;

  /// 工友是否全部被选中
  final _isAllSelected = false.obs;

  /// 是否显示添加和删除按钮
  final _isShowAddReduceBtn = true.obs;

  /// 是否显示全部工友
  final _isShowAllWorker = false.obs;

  List<double> get selectedIds => _selectedIds.value;

  List<double> get recordedIds => _recordedIds.value;

  List<double> get noRecordedIds => _noRecordedIds.value;

  List<WorkerEditItemUIState> get workers => _workers.value;

  bool get isAllSelected => _isAllSelected.value;

  bool get isShowAddReduceBtn => _isShowAddReduceBtn.value;

  bool get isShowAllWorker => _isShowAllWorker.value;

  void setIsShowAllWorker(bool value) {
    _isShowAllWorker.value = value;
  }

  void setIsShowAddReduceBtn(bool value) {
    _isShowAddReduceBtn.value = value;
  }

  void setIsAllSelected(bool value) {
    _isAllSelected.value = value;
  }

  void setWorkers(List<WorkerEditItemUIState> value) {
    /// 对工友数据进行排序，已记工的工友排在后面
    value.sort((a, b) {
      if (a.isRecorded == b.isRecorded) {
        return 0;
      }
      if (a.isRecorded == true) {
        return 1;
      }
      return -1;
    });
    _workers.value = value;
  }

  setSelectedIds(List<double> value) {
    _selectedIds.value = value;
    setIsAllSelected(value.length == _workers.length);
  }

  setRecordedIds(List<double> value) {
    _recordedIds.value = value;
  }

  setNoRecordedIds(List<double> value) {
    _noRecordedIds.value = value;
  }
}

/// 工友列表项UI状态
class WorkerEditItemUIState {
  final double id;
  final String name;
  final String? avatar;
  final bool? isRecorded;

  WorkerEditItemUIState({
    required this.id,
    required this.name,
    this.avatar,
    this.isRecorded,
  });
}
