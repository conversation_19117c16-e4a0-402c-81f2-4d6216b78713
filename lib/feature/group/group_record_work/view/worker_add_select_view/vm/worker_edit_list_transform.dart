import 'package:gdjg_pure_flutter/data/worker_data/worker_info/repo/model/attendance_worker_biz_model.dart';
import 'package:gdjg_pure_flutter/feature/group/group_record_work/view/worker_add_select_view/vm/worker_edit_list_us.dart';

class WorkerEditListTransform {
  static WorkerEditItemUIState bizModelToUIState(WorkerBizModel bizModel, bool hasRecorded) {
    return WorkerEditItemUIState(
      id: bizModel.id,
      name: bizModel.name,
      avatar: bizModel.avatar,
      isRecorded: hasRecorded,
    );
  }
}
