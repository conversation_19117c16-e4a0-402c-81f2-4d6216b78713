import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/feature/group/group_record_work/view/worker_add_select_view/entity/worker_add_select_props.dart';
import 'package:gdjg_pure_flutter/feature/group/group_record_work/view/worker_add_select_view/vm/worker_edit_list_us.dart';
import 'package:gdjg_pure_flutter/feature/group/group_record_work/view/worker_add_select_view/vm/worker_edit_list_vm.dart';
import 'package:gdjg_pure_flutter/iconfont/iconfont.dart';
import 'package:gdjg_pure_flutter/utils/system_util/string_util.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';
import 'package:gdjg_pure_flutter/widget/label/text_label.dart';
import 'package:get/get.dart';

/// 工友添加，删除，选择组件,回调选中的工友
typedef OnWorkerSelected = void Function(List<double> workerIds);

/// 工友添加，删除，选择组件
class WorkerAddSelectView extends StatefulWidget {
  final WorkerAddSelectProps props;

  const WorkerAddSelectView({super.key, required this.props});

  @override
  State<WorkerAddSelectView> createState() => _WorkerAddSelectViewState();
}

class _WorkerAddSelectViewState extends State<WorkerAddSelectView> {
  late WorkerAddSelectViewModel viewModel;

  @override
  Widget build(BuildContext context) {
    return Container(
      child: Wrap(
        children: [
          _buildHeader(),
          _buildWorkers(),
        ],
      ),
    );
  }

  @override
  void initState() {
    super.initState();
    viewModel = WorkerAddSelectViewModel(props: widget.props);
    viewModel.initPage();
  }

  /// 顶部试图
  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        children: [
          Obx(() {
            return TextLabel(
              label: viewModel.us.isAllSelected ? '取消全选' : '全选',
              labelType: TextLabelType.white,
              hasBorder: true,
              onLabelTap: (id) {
                viewModel.onAllSelectClick();
              },
            );
          }),
          const Spacer(),
          Row(
            children: [
              Obx(() {
                return Text(
                  viewModel.us.noRecordedIds.isNotEmpty
                      ? '${viewModel.us.noRecordedIds.length}人未设置工价'
                      : '设置工价',
                  style: TextStyle(
                      color: viewModel.us.noRecordedIds.isNotEmpty
                          ? Colors.red
                          : ColorsUtil.primaryColor),
                );
              }),
              IconFont(
                IconNames.saasEdit,
                color: '#0092ff',
                size: 18,
              )
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildWorkers() {
    return Container(
        margin: const EdgeInsets.symmetric(horizontal: 16),
        child: Wrap(
          spacing: 20.w,
          runSpacing: 12.h,
          alignment: WrapAlignment.start,
          children: _buildWorkerList(),
        ));
  }

  List<Widget> _buildWorkerList() {
    var list = viewModel.us.workers.map((e) => _buildWorkerItem(e)).toList();
    if (viewModel.us.isShowAddReduceBtn) {
      list.add(_buildAddView());
      list.add(_buildReduceView());
    }
    return list;
  }

  Widget _buildWorkerItem(WorkerEditItemUIState item) {
    /// 取姓名的前三个字符
    final showName =
        item.name.substring(0, item.name.length > 3 ? 3 : item.name.length);
    return GestureDetector(
        onTap: () {
          viewModel.onItemSelectClick(item.id);
        },
        child: Column(spacing: 4, children: [
          Stack(children: [
            _buildItemContent(item),
            if (item.isSelected == true) _buildSelectView(item),
          ]),
          Text(showName,
              style:
                  TextStyle(color: ColorsUtil.primaryColor, fontSize: 11.sp)),
        ]));
  }

  Widget _buildItemContent(WorkerEditItemUIState item) {
    Color borderColor = ColorsUtil.primaryColor;
    Color bgColor = ColorsUtil.primaryColor;

    if (item.isRecorded == true) {
      bgColor = ColorsUtil.yellowMedium;
      borderColor = ColorsUtil.yellowMedium;
    }
    if (item.isSelected == true) {
      bgColor = item.isRecorded == true
          ? Color.fromRGBO(255, 160, 17, 0.6)
          : Color.fromRGBO(82, 144, 253, 0.6);
      borderColor = ColorsUtil.yellowMedium;
    }

    return Container(
        alignment: Alignment.center,
        width: 42,
        height: 42,
        decoration: BoxDecoration(
          color: bgColor,
          borderRadius: BorderRadius.circular(4),
          border: Border.all(color: borderColor, width: 2),
        ),
        child: Stack(
          children: [
            // 名字文本居中显示
            Center(
              child: Text(item.name.lastTwoChars ?? "",
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 14.sp,
                  )),
            ),
            // 记录标识定位到底部
            if (item.isRecorded == true)
              Positioned(
                bottom: 0,
                left: 0,
                right: 0,
                child: _buildRecordedView(),
              ),
          ],
        ));
  }

  Widget _buildRecordedView() {
    return Container(
      alignment: Alignment.center,
      width: 42,
      height: 15,
      decoration: BoxDecoration(
        color: Color(0xffd5e4ff),
        borderRadius: BorderRadius.only(
            bottomRight: Radius.circular(4), bottomLeft: Radius.circular(4)),
      ),
      child: Text(
        '已记',
        style: TextStyle(color: ColorsUtil.primaryColor, fontSize: 10.sp),
      ),
    );
  }

  Widget _buildSelectView(WorkerEditItemUIState element) {
    return Container(
        alignment: Alignment.center,
        width: 42,
        height: 42,
        decoration: BoxDecoration(
          color: Color.fromRGBO(255, 160, 17, 0.2),
          borderRadius: BorderRadius.circular(4),
          border: Border.all(color: ColorsUtil.yellowMedium, width: 2),
        ),
        child: IconFont(
          IconNames.saasGou,
          color: '#ffa011',
          size: 22,
        ));
  }

  /// 添加工友按钮
  Widget _buildAddView() {
    return GestureDetector(
        onTap: viewModel.onWorkerAddClick,
        child: Column(spacing: 4, children: [
          Container(
              alignment: Alignment.center,
              width: 42,
              height: 42,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(4),
                border: Border.all(color: ColorsUtil.primaryColor, width: 1),
              ),
              child: IconFont(
                IconNames.saasPlus,
                size: 30,
                color: '#0092ff',
              )),
          Text('添加',
              style:
                  TextStyle(color: ColorsUtil.primaryColor, fontSize: 11.sp)),
        ]));
  }

  /// 删除工友按钮
  Widget _buildReduceView() {
    return GestureDetector(
      onTap: viewModel.onWorkerReduceClick,
      child: Column(spacing: 4, children: [
        Container(
            alignment: Alignment.center,
            width: 42,
            height: 42,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(4),
              border: Border.all(color: ColorsUtil.primaryColor, width: 1),
            ),
            child: IconFont(
              IconNames.saasJian,
              size: 30,
              color: '#0092ff',
            )),
        Text('删除',
            style: TextStyle(color: ColorsUtil.primaryColor, fontSize: 11.sp)),
      ]),
    );
  }
}
