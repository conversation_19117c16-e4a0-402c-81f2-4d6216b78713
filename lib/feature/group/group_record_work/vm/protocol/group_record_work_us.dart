import 'package:gdjg_pure_flutter/data/worker_data/worker_info/repo/model/attendance_worker_biz_model.dart';
import 'package:gdjg_pure_flutter/feature/worker/worker_flow/vm/protocol/worker_flow_us.dart';
import 'package:get/get.dart';

/// 班组记工页UI状态管理
class GroupRecordWorkUS {
  /// 记工项
  final _recordWorkBarItems = <RecordWorkBarItem>[
    RecordWorkBarItem(type: BusinessType.workAndAccountTypeDays),
    RecordWorkBarItem(type: BusinessType.workAndAccountTypePackage),
    RecordWorkBarItem(type: BusinessType.workAndAccountTypeWages),
    RecordWorkBarItem(type: BusinessType.workAndAccountTypeLoad),
  ].obs;

  /// 用户是否记过工
  final _hasBusiness = false.obs;

  /// 确认记工的工友人数
  final _confirmWorkerCount = 10.obs;

  /// 点工出勤工友
  var _dayWorkers = <WorkerBizModel>[];

  /// 包工工友
  var _packageWorkers = <WorkerBizModel>[];

  /// 短工工友
  var _shortWorkers = <WorkerBizModel>[];

  /// 工量工友
  var _amountWorkers = <WorkerBizModel>[];

  List<RecordWorkBarItem> get recordWorkBarItems => _recordWorkBarItems.value;

  bool get hasBusiness => _hasBusiness.value;

  int get confirmWorkerCount => _confirmWorkerCount.value;

  List<WorkerBizModel> get packageWorkers => _packageWorkers;

  List<WorkerBizModel> get dayWorkers => _dayWorkers;

  List<WorkerBizModel> get shortWorkers => _shortWorkers;

  List<WorkerBizModel> get amountWorkers => _amountWorkers;

  void setDayWorkers(List<WorkerBizModel> value) => _dayWorkers = value;

  void setPackageWorkers(List<WorkerBizModel> value) => _packageWorkers = value;

  void setShortWorkers(List<WorkerBizModel> value) => _shortWorkers = value;

  void setAmountWorkers(List<WorkerBizModel> value) => _amountWorkers = value;

  void setConfirmWorkerCount(int value) => _confirmWorkerCount.value = value;

  void setHasBusiness(bool value) => _hasBusiness.value = value;
}

/// 记工项
class RecordWorkBarItem {
  /// 记工项类型
  final BusinessType type;

  RecordWorkBarItem({
    required this.type,
  });
}
