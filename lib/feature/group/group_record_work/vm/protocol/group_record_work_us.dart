import 'package:gdjg_pure_flutter/feature/worker/worker_flow/vm/protocol/worker_flow_us.dart';
import 'package:get/get.dart';

/// 班组记工页UI状态管理
class GroupRecordWorkUS {
  /// 记工项
  final _recordWorkBarItems = <RecordWorkBarItem>[].obs;

  List<RecordWorkBarItem> get recordWorkBarItems => _recordWorkBarItems.value;

  set recordWorkBarItems(List<RecordWorkBarItem> list) {
    _recordWorkBarItems.value = list;
  }
}

/// 记工项
class RecordWorkBarItem {
  /// 记工项类型
  final BusinessType type;

  RecordWorkBarItem({
    required this.type,
  });
}
