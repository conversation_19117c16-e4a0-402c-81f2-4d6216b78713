import "package:gdjg_pure_flutter/data/worker_data/corp_select/repo/model/corp_select_biz_model.dart";
import "package:gdjg_pure_flutter/data/worker_data/worker_info/repo/model/attendance_worker_biz_model.dart";
import "package:gdjg_pure_flutter/data/worker_data/worker_project_data/repo/model/dept_detail_biz_model.dart";
import "package:gdjg_pure_flutter/feature/group/group_record_work/entity/group_record_work_props.dart";
import "package:gdjg_pure_flutter/feature/group/group_record_work/vm/protocol/group_record_work_us.dart";
import "package:gdjg_pure_flutter/feature/worker/worker_flow/vm/protocol/worker_flow_us.dart";
import "package:gdjg_pure_flutter/utils/system_util/date_util.dart";
import "package:get/get.dart";

import "../ui_rep/group_record_work_ui_rep.dart";

/// @date 2025/07/29
/// @description GroupRecordWork页ViewModel
/// 1. 处理数据的加载状态，如loading和错误视图等
/// 2. 观测UIRep中的业务实体转为UIState
/// 3. 接受来自view的意图并分发事件进行处理（暂无）
/// 4. 对View层暴露事件（操作视图和处理一些与系统权限交互等场景）
class GroupRecordWorkViewModel {
  var isLoading = false.obs;
  var isShowError = false.obs;
  var us = GroupRecordWorkUS();
  var uiRep = GroupRecordWorkUIRep();

  /// 当前日期 'YYYY-MM-DD', 可多选
  var dates = [DateUtil.formatDate(DateTime.now())];

  /// 出勤工友原始数据
  var originalWorkers = <WorkerBizModel>[];

  final GroupRecordWorkProps _props;

  GroupRecordWorkViewModel(this._props);

  /// 页面初始化
  /// 1.1. 先获取项目数据: api/dept/detail?dept_id=41679
  /// 1.2. 同时获取用户是否记过工：api/member/has_business，没记工退出页面是弹出挽留弹窗
  /// 2.1 再使用项目数据中的corp_id--切换企业: api/corp/select
  /// 2.2 然后获取项目下的工友：api/workers/get-workers-at-work
  void init() {
    pageInit();
  }

  /// 初始化数据同步，如果不需要loading，则在这里去掉
  void fetchWorkerData(List<String> date, String workerNoteId) async {
    isLoading.value = true;
    try {
      // 获取工友数据
      var result = await uiRep.getWorkerData(date, workerNoteId);
      originalWorkers = result?.workers ?? [];
    } catch (e) {
      isShowError.value = true;
    } finally {
      isLoading.value = false;
    }
  }

  void pageInit() async {
    /// mock数据
    final mockDays = [
      WorkerBizModel(
        id: 1,
        name: "张三",
        isSelf: 1,
      ),
      WorkerBizModel(
        id: 2,
        name: "宇文成都",
        isSelf: 1,
      ),
      WorkerBizModel(
        id: 3,
        name: "滚",
        isSelf: 1,
      ),
      WorkerBizModel(
        id: 4,
        name: "阿坝滋",
        isSelf: 1,
      ),
      WorkerBizModel(
        id: 5,
        name: "张三",
        isSelf: 1,
      ),
      WorkerBizModel(
        id: 6,
        name: "宇文成都",
        isSelf: 1,
      ),
      WorkerBizModel(
        id: 7,
        name: "滚",
        isSelf: 1,
      ),
      WorkerBizModel(
        id: 8,
        name: "阿坝滋",
        isSelf: 1,
      ),
      WorkerBizModel(
        id: 9,
        name: "张三",
        isSelf: 1,
      ),
      WorkerBizModel(
        id: 10,
        name: "宇文成都",
        isSelf: 1,
      ),
      WorkerBizModel(
        id: 11,
        name: "滚",
        isSelf: 1,
      ),
      WorkerBizModel(
        id: 12,
        name: "阿坝滋",
        isSelf: 1,
      ),
    ];
    us.setDayWorkers(mockDays);

    /// 1.1 获取用户是否记过工
    hasBusiness();

    /// 1.2 获取项目详情
    var deptDetail = await fetchDeptDetail(_props.deptId);
    if (deptDetail != null) {
      /// 2.1 切换企业
      await corpSelect(deptDetail.corpId.toString());

      /// 2.2 如果有项目详情，则获取项目下的工友数据
      fetchWorkerData(dates, deptDetail.workNoteId.toString());
    }
  }

  /// 获取项目详情
  Future<DeptDetailBizModel?> fetchDeptDetail(int deptId) async {
    isLoading.value = true;
    try {
      // 获取班组详情
      var result = await uiRep.getDeptDetail(deptId);
      if (result == null) {
        return null;
      }
    } catch (e) {
      isShowError.value = true;
    } finally {
      isLoading.value = false;
    }
    return null;
  }

  /// 切换企业
  Future<CorpSelectBizModel?> corpSelect(String corpId) async {
    return uiRep.getCorpSelect(corpId);
  }

  /// 获取用户是否记过工
  void hasBusiness() async {
    final result = await uiRep.hasBusiness();
    us.setHasBusiness(result);
  }
}
