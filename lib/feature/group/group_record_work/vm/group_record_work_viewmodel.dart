import "package:gdjg_pure_flutter/feature/group/group_record_work/vm/protocol/group_record_work_us.dart";
import "package:gdjg_pure_flutter/feature/worker/worker_flow/vm/protocol/worker_flow_us.dart";
import "package:get/get.dart";

import "../ui_rep/group_record_work_ui_rep.dart";

/// @date 2025/07/29
/// @description GroupRecordWork页ViewModel
/// 1. 处理数据的加载状态，如loading和错误视图等
/// 2. 观测UIRep中的业务实体转为UIState
/// 3. 接受来自view的意图并分发事件进行处理（暂无）
/// 4. 对View层暴露事件（操作视图和处理一些与系统权限交互等场景）
class GroupRecordWorkViewModel {
  var isLoading = false.obs;
  var isShowError = false.obs;
  var recordUs = GroupRecordWorkUS();
  var uiRep = GroupRecordWorkUIRep();

  GroupRecordWorkViewModel() {
    ever(uiRep.entity, (value) {
      convertEntityToUIState(value);
    });
  }

  /// 页面初始化
  /// 1. 获取项目数据: api/dept/detail?dept_id=41679
  /// 2. 使用项目数据中的corp_id--切换企业？？？？: api/corp/select
  /// 3. 获取用户是否记过工：api/member/has_business，没记工退出页面是弹出挽留弹窗
  /// 4. 获取项目下的工友：api/workers/get-workers-at-work
  void init() {
    initRecordItem();
    fetchData();
  }

  void initRecordItem() {
    var list = <RecordWorkBarItem>[
      RecordWorkBarItem(type: BusinessType.workAndAccountTypeDays),
      RecordWorkBarItem(type: BusinessType.workAndAccountTypePackage),
      RecordWorkBarItem(type: BusinessType.workAndAccountTypeWages),
      RecordWorkBarItem(type: BusinessType.workAndAccountTypeLoad),
    ];
    recordUs.setRecordWorkBarItems(list);
  }

  /// 初始化数据同步，如果不需要loading，则在这里去掉
  void fetchData() async {
    isLoading.value = true;
    try {
      // 同步获取数据
      var result = await uiRep.fetchData();
      // 这里的result仅做弹窗之类的处理，不在此处转为UI实体
    } catch (e) {
      isShowError.value = true;
    } finally {
      isLoading.value = false;
    }
  }

  /// 将实体数据转换为UIState
  /// @param entity
  /// @returns
  void convertEntityToUIState(GroupRecordWorkUIRepEntity entity) {
    //
  }
}
