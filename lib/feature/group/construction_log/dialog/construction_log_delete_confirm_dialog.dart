import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';

/// 显示施工日志删除确认弹窗
void showConstructionLogDeleteConfirmDialog({
  required VoidCallback onConfirm,
}) {
  YPRoute.openDialog(
    clickMaskDismiss: false,
    backType: SmartBackType.ignore,
    builder: (context) => ConstructionLogDeleteConfirmDialog(
      onConfirm: onConfirm,
    ),
  );
}

class ConstructionLogDeleteConfirmDialog extends StatelessWidget {
  final VoidCallback onConfirm;

  const ConstructionLogDeleteConfirmDialog({
    super.key,
    required this.onConfirm,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Container(
        width: MediaQuery.of(context).size.width * 0.8,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12.r),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildTitle(),
            _buildButtons(),
          ],
        ),
      ),
    );
  }

  Widget _buildTitle() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 32.h),
      child: Text(
        '确认删除此施工日志?',
        style: TextStyle(
          fontSize: 18.sp,
          fontWeight: FontWeight.w500,
          color: Color(0xFF333333),
        ),
        textAlign: TextAlign.center,
      ),
    );
  }

  Widget _buildButtons() {
    return Container(
      decoration: BoxDecoration(
        border: Border(
          top: BorderSide(
            color: Color(0xFFE5E5E5),
            width: 1.h,
          ),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: GestureDetector(
              onTap: _onCancel,
              child: Container(
                height: 48.h,
                alignment: Alignment.center,
                decoration: BoxDecoration(
                  border: Border(
                    right: BorderSide(
                      color: Color(0xFFE5E5E5),
                      width: 1.w,
                    ),
                  ),
                ),
                child: Text(
                  '取消',
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w400,
                    color: Color(0xFF666666),
                  ),
                ),
              ),
            ),
          ),
          Expanded(
            child: GestureDetector(
              onTap: _onConfirm,
              child: Container(
                height: 48.h,
                alignment: Alignment.center,
                child: Text(
                  '确定',
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w400,
                    color: ColorsUtil.primaryColor,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 取消删除
  void _onCancel() {
    YPRoute.closeDialog();
  }

  /// 确认删除
  void _onConfirm() {
    YPRoute.closeDialog();
    onConfirm();
  }
}
