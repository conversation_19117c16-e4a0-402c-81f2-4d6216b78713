import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_construction_log/repo/model/report_list_biz_model.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'construction_log_delete_confirm_dialog.dart';

/// 施工日志删除下载弹窗
void showConstructionLogActionBottomSheet({
  required ReportNetModelABizModel logItem,
  required VoidCallback onDelete,
  required VoidCallback onDownload,
}) {
  YPRoute.openDialog(
    alignment: Alignment.bottomCenter,
    clickMaskDismiss: true,
    builder: (context) => ConstructionLogActionBottomSheet(
      logItem: logItem,
      onDelete: onDelete,
      onDownload: onDownload,
    ),
  );
}

/// 底部弹窗
class ConstructionLogActionBottomSheet extends StatelessWidget {
  final ReportNetModelABizModel logItem;
  final VoidCallback onDelete;
  final VoidCallback onDownload;

  const ConstructionLogActionBottomSheet({
    super.key,
    required this.logItem,
    required this.onDelete,
    required this.onDownload,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
      ),
      child: SafeArea(
        top: false,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildSimpleActionItem(
              title: '下载',
              textColor: Color(0xFF333333),
              onTap: () {
                YPRoute.closeDialog();
                onDownload();
              },
            ),

            _buildDivider(),

            _buildSimpleActionItem(
              title: '删除',
              textColor: Color(0xFFFF3B30),
              onTap: () {
                YPRoute.closeDialog();
                _showDeleteConfirmDialog();
              },
            ),

            _buildDivider(),

            _buildSimpleActionItem(
              title: '取消',
              textColor: Color(0xFF333333),
              onTap: () {
                YPRoute.closeDialog();
              },
            ),

            SizedBox(height: 8.h),
          ],
        ),
      ),
    );
  }

  Widget _buildSimpleActionItem({
    required String title,
    required Color textColor,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      behavior: HitTestBehavior.opaque,
      child: Container(
        width: double.infinity,
        height: 50.h,
        alignment: Alignment.center,
        child: Text(
          title,
          style: TextStyle(
            fontSize: 14.sp,
            fontWeight: FontWeight.w400,
            color: textColor,
          ),
        ),
      ),
    );
  }

  /// 分割线
  Widget _buildDivider() {
    return Container(
      height: 1.h,
      color: Color(0xFFE5E5E5),
    );
  }

  /// 显示删除确认弹窗
  void _showDeleteConfirmDialog() {
    showConstructionLogDeleteConfirmDialog(
      onConfirm: onDelete,
    );
  }
}
