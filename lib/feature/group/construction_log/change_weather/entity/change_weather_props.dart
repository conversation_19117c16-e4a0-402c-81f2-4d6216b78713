/// 修改天气页面参数
class ChangeWeatherProps {
  /// 记录日期
  final DateTime recordDate;
  
  /// 上午天气
  final String morningWeather;
  
  /// 下午天气
  final String afternoonWeather;
  
  /// 上午温度
  final String morningTemp;
  
  /// 下午温度
  final String afternoonTemp;
  
  /// 保存回调函数
  final Function(String morningWeather, String afternoonWeather, String morningTemp, String afternoonTemp) onSave;

  ChangeWeatherProps({
    required this.recordDate,
    required this.morningWeather,
    required this.afternoonWeather,
    required this.morningTemp,
    required this.afternoonTemp,
    required this.onSave,
  });
}
