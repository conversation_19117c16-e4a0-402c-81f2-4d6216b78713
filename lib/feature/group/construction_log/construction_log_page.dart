import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/feature/group/construction_log/entity/construction_log_props.dart';
import 'package:gdjg_pure_flutter/feature/group/construction_log/view/construction_log_grouped_list_view.dart';
import 'package:gdjg_pure_flutter/feature/group/construction_log/vm/construction_log_vm.dart';
import 'package:gdjg_pure_flutter/generated/assets.dart';
import 'package:gdjg_pure_flutter/utils/route_util/base_page/base_ful_page.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';
import 'package:gdjg_pure_flutter/widget/combined_filter_widget.dart';
import 'package:get/get.dart';

class ConstructionLogPage extends BaseFulPage {
  const ConstructionLogPage({super.key}) : super(appBar: null);

  @override
  createState() => _ConstructionLogPageState();
}

class _ConstructionLogPageState extends BaseFulPageState<ConstructionLogPage> {
  final ConstructionLogViewModel _viewModel = ConstructionLogViewModel();

  @override
  onPageRoute(Object? routeParams, bool fromLaunchTask) {
    var props = routeParams as ConstructionLogProps?;
    _viewModel.init(props);
  }

  @override
  void onPageDestroy() {
    super.onPageDestroy();
    _viewModel.dispose();
  }

  @override
  Widget yBuild(BuildContext context) {
    return Scaffold(
      appBar: _buildCustomAppBar(),
      body: _buildBody(),
      bottomNavigationBar: _buildBottomButton(),
    );
  }

  AppBar _buildCustomAppBar() {
    return AppBar(
      backgroundColor: Colors.white,
      leading: GestureDetector(
        onTap: () => YPRoute.closePage(),
        child: Container(
          padding: const EdgeInsets.only(left: 8.0),
          child: Image(
            image: AssetImage(Assets.commonIconArrowBack),
          ),
        ),
      ),
      leadingWidth: 38,
      titleSpacing: 08,
      title: Text(
        "施工日志",
        style: const TextStyle(
          color: Colors.black,
          fontSize: 22,
          fontWeight: FontWeight.w600,
        ),
      ),
      centerTitle: false,
      actions: [
        GestureDetector(
          onTap: () {
            //TODO: 下载实现
          },
          child: Container(
            margin: const EdgeInsets.only(right: 16.0),
            padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
            child: Row(
              children: [
                Image.asset(Assets.commonIcDownload, width: 18, height: 18),
                SizedBox(width: 2.w,),
                Text(
                  "下载",
                  style: TextStyle(
                    color: ColorsUtil.primaryColor,
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildBody() {
    return Column(
      children: [
        // 搜索框
        _buildSearchButton(),

        // 筛选器
        Obx(() => CombinedFilterWidget(
              showTypeFilter: false,
              showWorkerFilter: false,
              showProjectFilter: false,
              initialStartDate: _viewModel.us.startTime,
              initialEndDate: _viewModel.us.endTime,
              onSelectAll: () {
                _viewModel.updateDateRange(null, null);
              },
              onFilterChanged: (filter) {
                _viewModel.updateDateRange(
                  filter.startDate,
                  filter.endDate,
                );
              },
            )),

        // 日志列表
        Expanded(
          child: Obx(() => ConstructionLogGroupedListView(
                groupedList: _viewModel.us.groupedReportList,
                hasMore: _viewModel.us.hasMore,
                isEmpty: _viewModel.us.isEmpty,
                onLoadMore: _viewModel.loadMore,
                onRefresh: _viewModel.refreshData,
                onImageTap: _viewModel.onImageTap,
                onDownloadTap: _viewModel.onDownloadTap,
                onItemTap: _viewModel.onLogItemTap,
              )),
        ),
      ],
    );
  }

  //搜索框（跳转到搜索页面）
  Widget _buildSearchButton() {
    return Container(
      color: Colors.white,
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 3.h),
      child: GestureDetector(
        onTap: _viewModel.onSearchTap,
        child: Container(
          height: 38.h,
          decoration: BoxDecoration(
            color: Color(0xFFF5F5F5),
            borderRadius: BorderRadius.circular(4.r),
          ),
          child: Row(
            children: [
              SizedBox(width: 12.w),
              Icon(
                Icons.search,
                color: Color(0xFF999999),
                size: 20.w,
              ),
              SizedBox(width: 8.w),
              Text(
                "请输入日志内容",
                style: TextStyle(
                  fontSize: 14.sp,
                  color: Color(0xFF999999),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  //写日志按钮
  Widget _buildBottomButton() {
    return Container(
      color: Colors.white,
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 10.h),
      child: SafeArea(
        child: GestureDetector(
          onTap: _viewModel.onWriteLogTap,
          child: Container(
            height: 46.h,
            decoration: BoxDecoration(
              color: ColorsUtil.primaryColor,
              borderRadius: BorderRadius.circular(6.r),
            ),
            child: Center(
              child: Text(
                "写日志",
                style: TextStyle(
                  fontSize: 16.sp,
                  color: Colors.white,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
