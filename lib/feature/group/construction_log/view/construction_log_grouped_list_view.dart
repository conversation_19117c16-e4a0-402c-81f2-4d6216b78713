import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_construction_log/repo/model/report_list_biz_model.dart';
import 'package:gdjg_pure_flutter/feature/group/construction_log/view/construction_log_item_view.dart';
import 'package:gdjg_pure_flutter/feature/group/construction_log/vm/construction_log_us.dart';
import 'package:gdjg_pure_flutter/generated/assets.dart';
import 'package:gdjg_pure_flutter/utils/system_util/date_util.dart';

/// 施工日志列表
class ConstructionLogGroupedListView extends StatefulWidget {
  final List<DateGroupedLogs> groupedList;
  final bool hasMore;
  final bool isEmpty;
  final VoidCallback onLoadMore;
  final VoidCallback onRefresh;
  final Function(String, List<String>) onImageTap;
  final Function(ReportNetModelABizModel) onDownloadTap;
  final Function(ReportNetModelABizModel)? onItemTap;

  const ConstructionLogGroupedListView({
    super.key,
    required this.groupedList,
    this.hasMore = true,
    this.isEmpty = false,
    required this.onLoadMore,
    required this.onRefresh,
    required this.onImageTap,
    required this.onDownloadTap,
    this.onItemTap,
  });

  @override
  State<ConstructionLogGroupedListView> createState() => _ConstructionLogGroupedListViewState();
}

class _ConstructionLogGroupedListViewState extends State<ConstructionLogGroupedListView> {
  late ScrollController _scrollController;

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      if (widget.hasMore) {
        widget.onLoadMore();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return RefreshIndicator(
      onRefresh: () async {
        widget.onRefresh();
      },
      child: CustomScrollView(
        controller: _scrollController,
        physics: const AlwaysScrollableScrollPhysics(),
        slivers: [
          widget.isEmpty ? _buildEmptySliver() : _buildContentSliver(),
        ],
      ),
    );
  }

  /// 空状态
  Widget _buildEmptySliver() {
    return SliverToBoxAdapter(
      child: Container(
        width: double.infinity,
        height: MediaQuery.of(context).size.height * 0.6,
        padding: EdgeInsets.only(top: 80.h),
        child: Column(
          children: [
            Image.asset(
              Assets.commonIconEmptyTeamProject,
              width: 140.w,
              height: 140.w,
              fit: BoxFit.contain,
            ),
            Text(
              '暂无数据',
              style: TextStyle(
                fontSize: 14.sp,
                color: const Color(0xFF8A8A99),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 日志card
  Widget _buildContentSliver() {
    return SliverList(
      delegate: SliverChildBuilderDelegate(
        (context, index) => _buildItem(index),
        childCount: _calculateTotalItems(),
      ),
    );
  }

  int _calculateTotalItems() {
    int totalItems = 0;
    for (var group in widget.groupedList) {
      totalItems += 1; // 日期标题
      totalItems += group.logs.length; // 日志项
    }
    return totalItems;
  }

  Widget _buildItem(int index) {
    int currentIndex = 0;
    
    for (var group in widget.groupedList) {
      // 日期标题
      if (currentIndex == index) {
        return _buildDateHeader(group);
      }
      currentIndex++;
      
      // 日志项
      for (var log in group.logs) {
        if (currentIndex == index) {
          return Container(
            margin: EdgeInsets.only(bottom: 8.h),
            child: ConstructionLogItemView(
              item: log,
              onImageTap: widget.onImageTap,
              onDownloadTap: widget.onDownloadTap,
              onItemTap: widget.onItemTap,
            ),
          );
        }
        currentIndex++;
      }
    }

    return Container();
  }

  Widget _buildDateHeader(DateGroupedLogs group) {
    return Container(
      color: Color(0xFFF5F5F5),
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
      child: Row(
        children: [
          _buildStyledDateText(group),
        ],
      ),
    );
  }

  /// 日期
  Widget _buildStyledDateText(DateGroupedLogs group) {
    // 获取原始时间戳
    double timestamp = group.logs.isNotEmpty ? group.logs.first.editTime : 0;

    if (timestamp == 0) {
      return Text(
        "未知日期",
        style: TextStyle(
          fontSize: 14.sp,
          color: Color(0xFF666666),
          fontWeight: FontWeight.w500,
        ),
      );
    }

    try {
      DateTime dateTime = DateTime.fromMillisecondsSinceEpoch((timestamp * 1000).toInt());
      DateTime now = DateTime.now();
      DateTime today = DateTime(now.year, now.month, now.day);
      DateTime targetDate = DateTime(dateTime.year, dateTime.month, dateTime.day);

      const weekdays = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];
      String weekday = weekdays[dateTime.weekday - 1];

      // 格式化日期字符串
      String dateStr = DateUtil.formatChineseDate(dateTime);

      List<Widget> children = [];

      // 判断是否为今天
      if (targetDate == today) {
        children.add(Text(
          '今天',
          style: TextStyle(
            fontSize: 16.sp,
            color: Color(0xFF000000),
            fontWeight: FontWeight.w800,
          ),
        ));

        children.add(SizedBox(width: 4.w));
      }

      // 日期
      children.add(Text(
        dateStr,
        style: TextStyle(
          fontSize: 16.sp,
          color: Color(0xFF000000),
          fontWeight: FontWeight.w800,
        ),
      ));

      children.add(SizedBox(width: 4.w));

      // 星期
      children.add(Text(
        weekday,
        style: TextStyle(
          fontSize: 16.sp,
          color: Color(0xFF888996),
          fontWeight: FontWeight.w800,
        ),
      ));

      return Row(
        mainAxisSize: MainAxisSize.min,
        children: children,
      );
    } catch (e) {
      return Text(
        "未知日期",
        style: TextStyle(
          fontSize: 14.sp,
          color: Color(0xFF666666),
          fontWeight: FontWeight.w500,
        ),
      );
    }
  }




}
