import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_construction_log/repo/model/report_list_biz_model.dart';
import 'package:gdjg_pure_flutter/generated/assets.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';

/// 施工日志列表
class ConstructionLogItemView extends StatelessWidget {
  final ReportNetModelABizModel item;
  final Function(String, List<String>) onImageTap;
  final Function(ReportNetModelABizModel) onDownloadTap;
  final Function(ReportNetModelABizModel)? onItemTap;

  const ConstructionLogItemView({
    super.key,
    required this.item,
    required this.onImageTap,
    required this.onDownloadTap,
    this.onItemTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => onItemTap?.call(item),
      child: Container(
        margin: EdgeInsets.symmetric(horizontal: 14.w),
        padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(4.r),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(),
            SizedBox(height: 8.h),
            _buildContent(),
            if (item.imgs.isNotEmpty) ...[
              SizedBox(height: 10.h),
              _buildImages(),
              SizedBox(height: 6.h),
            ],
            SizedBox(height: 6.h),
            _buildTimeDisplay(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        _buildAvatar(),
        SizedBox(width: 4.w),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                "${item.xtUser?.name ?? '默认工人'}的施工日志",
                style: TextStyle(
                  fontSize: 16.sp,
                  color: Color(0xFF333333),
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
        GestureDetector(
          onTap: () {
            onDownloadTap(item);
          },
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Image.asset(
                Assets.commonIcDownload,
                width: 16.w,
                height: 16.w,
              ),
              SizedBox(width: 4.w),
              Text(
                "删除/下载",
                style: TextStyle(
                  fontSize: 14.sp,
                  color: ColorsUtil.primaryColor,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildAvatar() {
    return Container(
      width: 38.w,
      height: 38.w,
      decoration: BoxDecoration(
        color: ColorsUtil.primaryColor,
        borderRadius: BorderRadius.circular(4.r),
      ),
      child: _buildDefaultAvatar(),
    );
  }

  Widget _buildDefaultAvatar() {
    return Center(
      child: Text(
        "工人",
        style: TextStyle(
          fontSize: 12.sp,
          color: Colors.white,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildContent() {
    return Text(
      item.contents,
      style: TextStyle(
        fontSize: 14.sp,
        color: Color(0xFF333333),
        height: 1.4,
      ),
      maxLines: 3,
      overflow: TextOverflow.ellipsis,
    );
  }

  Widget _buildImages() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Wrap(
          spacing: 8.w,
          runSpacing: 8.h,
          children: _buildImageWidgets(),
        ),
      ],
    );
  }

  List<Widget> _buildImageWidgets() {
    List<Widget> imageWidgets = [];

    // 前3张图片
    int displayCount = math.min(3, item.imgs.length);
    for (int i = 0; i < displayCount; i++) {
      imageWidgets.add(_buildNormalImage(item.imgs[i]));
    }

    // 如果有第4张及以上图片，添加带蒙层的第4张
    if (item.imgs.length > 3) {
      int remainingCount = item.imgs.length - 3;
      imageWidgets.add(_buildOverlayImage(item.imgs[3], remainingCount));
    }

    return imageWidgets;
  }

  Widget _buildNormalImage(ImgBizModel img) {
    return GestureDetector(
      onTap: () => onImageTap(img.imgUrl, item.imgs.map((e) => e.imgUrl).toList()),
      child: Container(
        width: 78.w,
        height: 78.w,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(4.r),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(4.r),
          child: Image.network(
            img.imgUrl,
            fit: BoxFit.cover,
            errorBuilder: (context, error, stackTrace) => Container(
              color: Color(0xFFF5F5F5),
              child: Icon(
                Icons.image,
                color: Color(0xFF999999),
                size: 24.w,
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildOverlayImage(ImgBizModel img, int remainingCount) {
    return GestureDetector(
      onTap: () => onImageTap(img.imgUrl, item.imgs.map((e) => e.imgUrl).toList()),
      child: Container(
        width: 70.w,
        height: 70.w,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(4.r),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(4.r),
          child: Stack(
            children: [
              Image.network(
                img.imgUrl,
                width: 70.w,
                height: 70.w,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) => Container(
                  color: Color(0xFFF5F5F5),
                  child: Icon(
                    Icons.image,
                    color: Color(0xFF999999),
                    size: 24.w,
                  ),
                ),
              ),
              // 蒙层
              Container(
                width: 70.w,
                height: 70.w,
                decoration: BoxDecoration(
                  color: Colors.black.withValues(alpha: 0.5),
                  borderRadius: BorderRadius.circular(4.r),
                ),
                child: Center(
                  child: Text(
                    '+$remainingCount',
                    style: TextStyle(
                      fontSize: 16.sp,
                      color: Colors.white,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 时间显示
  Widget _buildTimeDisplay() {
    return Align(
      alignment: Alignment.centerLeft,
      child: Text(
        _formatTime(item.editTime),
        style: TextStyle(
          fontSize: 12.sp,
          color: Color(0xFF999999),
        ),
      ),
    );
  }

  String _formatTime(double timestamp) {
    if (timestamp == 0) return "";
    try {
      DateTime dateTime = DateTime.fromMillisecondsSinceEpoch((timestamp * 1000).toInt());
      return "${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}";
    } catch (e) {
      return "";
    }
  }
}
