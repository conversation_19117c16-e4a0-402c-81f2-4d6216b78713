import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_construction_log/repo/model/report_list_biz_model.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';
import 'package:gdjg_pure_flutter/utils/system_util/date_util.dart';

/// 施工日志列表项
class SimpleConstructionLogItemView extends StatelessWidget {
  final ReportNetModelABizModel item;
  final Function(ReportNetModelABizModel)? onItemTap;

  const SimpleConstructionLogItemView({
    super.key,
    required this.item,
    this.onItemTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => onItemTap?.call(item),
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 16.h),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(4.r),
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            _buildAvatar(),
            SizedBox(width: 12.w),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          "${item.xtUser?.name ?? '默认工人'}的施工日志",
                          style: TextStyle(
                            fontSize: 16.sp,
                            color: Color(0xFF333333),
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                      Text(
                        _formatTime(item.editTime),
                        style: TextStyle(
                          fontSize: 12.sp,
                          color: Color(0xFF999999),
                        ),
                      ),
                    ],
                  ),

                  Text(
                    item.contents,
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: Color(0xFF8D8C98),
                      height: 1.4,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 蓝色工人头像
  Widget _buildAvatar() {
    return Container(
      width: 60.w,
      height: 60.w,
      decoration: BoxDecoration(
        color: ColorsUtil.primaryColor,
        borderRadius: BorderRadius.circular(4.r),
      ),
      child: Center(
        child: Text(
          "工人",
          style: TextStyle(
            fontSize: 18.sp,
            color: Colors.white,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }

  /// 时间戳转日期
  String _formatTime(double timestamp) {
    if (timestamp == 0) return "";
    try {
      DateTime dateTime = DateTime.fromMillisecondsSinceEpoch((timestamp * 1000).toInt());
      return DateUtil.formatChineseDate(dateTime);
    } catch (e) {
      return "";
    }
  }
}
