import 'package:get/get.dart';
import 'package:gdjg_pure_flutter/utils/system_util/date_util.dart';

/// 选择导出时间页面UI状态
class SelectExportTimeUS {
  /// 开始时间
  final Rx<DateTime> _startTime = DateTime.now().obs;
  
  /// 结束时间
  final Rx<DateTime> _endTime = DateTime.now().obs;

  /// 获取开始时间
  DateTime get startTime => _startTime.value;
  
  /// 获取结束时间
  DateTime get endTime => _endTime.value;

  /// 获取格式化的开始时间显示文本
  String get startTimeText => DateUtil.formatDate(startTime);
  
  /// 获取格式化的结束时间显示文本
  String get endTimeText => DateUtil.formatDate(endTime);

  /// 设置开始时间
  void setStartTime(DateTime time) {
    _startTime.value = time;
  }

  /// 设置结束时间
  void setEndTime(DateTime time) {
    _endTime.value = time;
  }
}
