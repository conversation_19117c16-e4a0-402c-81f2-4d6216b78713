import 'package:gdjg_pure_flutter/data/group_data/group_construction_log/repo/model/report_list_biz_model.dart';
import 'package:get/get.dart';

/// 搜索日志页面UIState
class SearchLogUS {
  /// 搜索关键词
  final _searchKeyword = ''.obs;

  /// 搜索结果列表
  final _searchResultList = <ReportNetModelABizModel>[].obs;

  /// 是否有更多数据
  final _hasMore = true.obs;

  /// 当前页码
  final _currentPage = 1.obs;

  /// 总数
  final _total = 0.obs;

  /// 搜索状态：0-未搜索，1-搜索中，2-有结果，3-无结果
  final _searchStatus = 0.obs;

  // Getters
  String get searchKeyword => _searchKeyword.value;
  List<ReportNetModelABizModel> get searchResultList => _searchResultList;
  bool get hasMore => _hasMore.value;
  int get currentPage => _currentPage.value;
  int get total => _total.value;
  int get searchStatus => _searchStatus.value;

  /// 是否为空状态（未搜索或搜索关键词为空）
  bool get isEmpty => _searchStatus.value == 0 || _searchKeyword.value.trim().isEmpty;

  /// 是否搜索中
  bool get isSearching => _searchStatus.value == 1;

  /// 是否有搜索结果
  bool get hasSearchResult => _searchStatus.value == 2 && _searchResultList.isNotEmpty;

  /// 是否无搜索结果
  bool get hasNoResult => _searchStatus.value == 3;

  // Setters
  void setSearchKeyword(String keyword) {
    _searchKeyword.value = keyword;
  }

  void setSearchResultList(List<ReportNetModelABizModel> list) {
    _searchResultList.assignAll(list);
  }

  void addSearchResultList(List<ReportNetModelABizModel> list) {
    _searchResultList.addAll(list);
  }

  void setHasMore(bool hasMore) {
    _hasMore.value = hasMore;
  }

  void setCurrentPage(int page) {
    _currentPage.value = page;
  }

  void setTotal(int total) {
    _total.value = total;
  }

  void setSearchStatus(int status) {
    _searchStatus.value = status;
  }

  /// 重置分页
  void resetPagination() {
    _currentPage.value = 1;
    _hasMore.value = true;
  }

  /// 下一页
  void nextPage() {
    _currentPage.value++;
  }

  /// 清空搜索结果
  void clearSearchResult() {
    _searchResultList.clear();
    _total.value = 0;
    _hasMore.value = true;
    _currentPage.value = 1;
    _searchStatus.value = 0;
  }


}
