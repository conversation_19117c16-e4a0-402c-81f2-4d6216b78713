import 'dart:async';
import 'package:gdjg_pure_flutter/data/group_data/group_construction_log/repo/group_construction_log_repo.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_construction_log/repo/model/report_list_biz_model.dart';
import 'package:gdjg_pure_flutter/feature/group/construction_log/change_log/vm/change_log_vm.dart';
import 'package:gdjg_pure_flutter/feature/group/construction_log/search_log/entity/search_log_props.dart';
import 'package:gdjg_pure_flutter/feature/group/construction_log/search_log/vm/search_log_us.dart';
import 'package:gdjg_pure_flutter/init_module/init_route.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/utils/system_util/yprint.dart';

/// 搜索日志页面ViewModel
class SearchLogViewModel {
  final _groupConstructionLogRepo = GroupConstructionLogRepo();
  var us = SearchLogUS();
  Timer? _debounceTimer;
  SearchLogProps? _props;

  /// 初始化
  void init(SearchLogProps? props) {
    _props = props;
    us.setSearchStatus(0);
  }

  /// 搜索日志
  void searchLogs(String keyword) {
    us.setSearchKeyword(keyword);
    _debounceTimer?.cancel();
    
    if (keyword.trim().isEmpty) {
      // 关键词为空，清空结果并设置为未搜索状态
      us.clearSearchResult();
      return;
    }
    
    // 防抖
    _debounceTimer = Timer(const Duration(milliseconds: 500), () {
      _performSearch(keyword.trim());
    });
  }

  /// 搜索
  void _performSearch(String keyword, {bool isLoadMore = false}) async {
    if (keyword.isEmpty) return;

    if (!isLoadMore) {
      us.resetPagination();
    }

    final result = await _groupConstructionLogRepo.getReportList(
      page: us.currentPage.toString(),
      startTime: "",
      endTime: "",
      keyword: keyword,
      deptId: _props?.deptId ?? "",
    );

    if (result.isOK()) {
      final data = result.getSucData();
      if (data != null) {
        if (isLoadMore) {
          us.addSearchResultList(data.list);
        } else {
          us.setSearchResultList(data.list);
        }
        us.setTotal((data.pagination?.total ?? 0.0).toInt());
        
        // 判断是否有更多数据
        final currentPage = data.pagination?.page ?? 1.0;
        final lastPage = data.pagination?.lastPage ?? 1.0;
        us.setHasMore(currentPage < lastPage);
        
        // 设置搜索状态
        if (data.list.isEmpty && !isLoadMore) {
          us.setSearchStatus(3); // 无结果
        } else {
          us.setSearchStatus(2); // 有结果
        }
      } else {
        if (!isLoadMore) {
          us.setSearchStatus(3); // 无结果
        }
      }
    } else {
      yprint("搜索施工日志失败: ${result.fail?.errorMsg}");
      if (!isLoadMore) {
        us.setSearchStatus(3); // 无结果
      }
    }
  }

  /// 加载更多
  void loadMore() {
    if (us.hasMore && us.searchKeyword.isNotEmpty) {
      us.nextPage();
      _performSearch(us.searchKeyword, isLoadMore: true);
    }
  }

  /// 刷新搜索结果
  void refreshSearch() {
    if (us.searchKeyword.isNotEmpty) {
      _performSearch(us.searchKeyword);
    }
  }

  /// 清空搜索
  void clearSearch() {
    _debounceTimer?.cancel();
    us.setSearchKeyword('');
    us.clearSearchResult();
  }

  /// 跳转到修改日志页面
  void onLogItemTap(ReportNetModelABizModel logItem) {
    YPRoute.openPage(
      RouteNameCollection.changeLog,
      params: ChangeLogProps(
        logId: logItem.id,
      ),
    );
  }

  /// 销毁
  void dispose() {
    _debounceTimer?.cancel();
  }
}
