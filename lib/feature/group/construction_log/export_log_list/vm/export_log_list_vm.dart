import 'dart:async';
import 'package:gdjg_pure_flutter/data/group_data/group_construction_log/repo/group_construction_log_repo.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_construction_log/repo/model/task_list_biz_model.dart';
import 'package:gdjg_pure_flutter/feature/group/construction_log/export_log_list/vm/export_log_list_us.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/toast_util.dart';
import 'package:gdjg_pure_flutter/utils/wechat_util/wechat_util.dart';
import 'package:gdjg_pure_flutter/feature/feedback/util/feedback_config_util.dart';
import 'package:gdjg_pure_flutter/feature/group/construction_log/dialog/batch_delete_confirm_dialog.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:url_launcher/url_launcher.dart';

/// 导出日志列表页面ViewModel
class ExportLogListViewModel {
  final GroupConstructionLogRepo _repo = GroupConstructionLogRepo();
  final ExportLogListUS us = ExportLogListUS();

  /// 定时器
  Timer? _refreshTimer;

  /// SmartRefresh 控制器
  final RefreshController refreshController = RefreshController(initialRefresh: false);

  /// 页面大小
  static const int _pageSize = 20;

  /// 默认定时器间隔
  static const int _defaultTimerInterval = 5;

  /// 获取任务列表
  List<TaskListABizModel> get taskList => us.taskList;

  /// 是否有更多数据
  bool get hasMore => us.hasMore;

  /// 是否处于删除模式
  bool get isDeleteMode => us.isDeleteMode;

  /// 选中的任务ID列表
  List<double> get selectedTaskIds => us.selectedTaskIds;

  /// 是否全选
  bool get isAllSelected => us.isAllSelected;

  /// 获取选中任务数量
  int get selectedCount => us.selectedCount;

  /// 初始化
  void init() {
    onRefresh();
  }

  /// 数据获取方法
  Future<void> _fetchTaskListData(int page) async {
    final result = await _repo.getTaskList(
      processType: "log",
      page: page.toString(),
      perPage: _pageSize.toString(),
    );

    if (result.isOK()) {
      final bizModel = result.getSucData();
      final newTasks = bizModel?.list ?? [];

      _updateTaskList(page, newTasks);
      us.setHasMore(bizModel?.page?.hasMore ?? false);
    } else {
      throw Exception(result.fail?.errorMsg ?? '获取任务列表失败');
    }
  }

  /// 更新任务列表
  void _updateTaskList(int page, List<TaskListABizModel> newTasks) {
    if (page == 1) {
      us.setTaskList(newTasks);
    } else {
      us.addTaskList(newTasks);
    }
  }
  
  /// 下拉刷新
  void onRefresh() async {
    us.resetPaging();
    refreshController.resetNoData();

    try {
      await _fetchTaskListData(1);
      _startTimerIfNeeded();
      refreshController.refreshCompleted();
    } catch (e) {
      refreshController.refreshFailed();
    }
  }

  /// 上拉加载更多
  void onLoading() async {
    if (!us.hasMore) {
      refreshController.loadNoData();
      return;
    }

    us.incrementPage();

    try {
      await _fetchTaskListData(us.currentPage);
      _updateLoadingState();
    } catch (e) {
      refreshController.loadFailed();
    }
  }

  /// 更新加载状态
  void _updateLoadingState() {
    if (us.hasMore) {
      refreshController.loadComplete();
    } else {
      refreshController.loadNoData();
    }
  }
  
  /// 启动定时器
  void _startTimerIfNeeded() {
    _stopTimer();

    if (!us.hasUnfinishedTasks) return;

    final timerInterval = _calculateTimerInterval();
    _refreshTimer = Timer.periodic(Duration(seconds: timerInterval), (timer) async {
      onRefresh();

      if (!us.hasUnfinishedTasks) {
        _stopTimer();
      }
    });
  }

  /// 计算定时器间隔
  int _calculateTimerInterval() {
    final minEstimateSeconds = _getMinEstimateSeconds();
    return minEstimateSeconds > 0 ? minEstimateSeconds : _defaultTimerInterval;
  }

  /// 获取最短的估计时间
  int _getMinEstimateSeconds() {
    return us.taskList
        .where((task) => task.isUnfinished)
        .map((task) => _parseEstimateToSeconds(task.processData?.estimate ?? ''))
        .where((seconds) => seconds > 0)
        .fold<int>(0, (min, seconds) => min == 0 || seconds < min ? seconds : min);
  }

  /// 解析时间字符串为秒数
  int _parseEstimateToSeconds(String estimate) {
    if (estimate.isEmpty) return 0;

    try {
      final cleanEstimate = estimate.toLowerCase().trim();
      if (cleanEstimate.contains('s')) {
        final number = cleanEstimate.replaceAll('s', '').trim();
        return int.parse(number);
      }
    } catch (e) {
      return _defaultTimerInterval;
    }
    return 0;
  }
  
  /// 停止定时器
  void _stopTimer() {
    _refreshTimer?.cancel();
    _refreshTimer = null;
  }

  /// 分享任务
  void shareTask(TaskListABizModel task) async {
    try {
      if (task.id <= 0) {
        ToastUtil.showToast('任务信息无效');
        return;
      }

      // 构建分享链接
      final baseUrl = FeedbackConfigUtil.getMHyWebUrl();
      final taskUrl = '${baseUrl}task/detail?id=${task.id.toInt()}';

      // 任务标题为分享标题，副标题为描述
      final title = task.title.isNotEmpty ? task.title : '施工日志项目';
      final description = task.subtitle.isNotEmpty ? task.subtitle : '施工日志导出';

      // 分享到微信
      final success = await WeChatUtil.shareWebpage(
        webpageUrl: taskUrl,
        title: title,
        description: description,
      );

      if (!success) {
        ToastUtil.showToast('分享失败');
      }
    } catch (e) {
      ToastUtil.showToast('分享失败: $e');
    }
  }

  /// 下载任务
  Future<void> downloadTask(TaskListABizModel task) async {
    final url = task.fileData?.url;

    if (url == null || url.isEmpty) {
      ToastUtil.showToast('下载链接不存在');
      return;
    }

    try {
      final uri = Uri.parse(url);

      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      } else {
        ToastUtil.showToast('无法打开下载链接');
      }
    } catch (e) {
      ToastUtil.showToast('打开下载链接失败');
    }
  }

  /// 切换删除模式
  void toggleDeleteMode() {
    us.setDeleteMode(!us.isDeleteMode);
  }

  /// 取消删除模式
  void cancelDeleteMode() {
    us.setDeleteMode(false);
  }

  /// 切换任务选中状态
  void toggleTaskSelection(double taskId) {
    us.toggleTaskSelection(taskId);
  }

  /// 切换全选状态
  void toggleSelectAll() {
    us.setAllSelected(!us.isAllSelected);
  }

  /// 检查任务是否被选中
  bool isTaskSelected(double taskId) {
    return us.isTaskSelected(taskId);
  }

  /// 删除选中的任务
  void deleteSelectedTasks() {
    if (us.selectedCount == 0) {
      ToastUtil.showToast('暂无可选择的日志');
      return;
    }

    // 显示删除确认弹窗
    showBatchDeleteConfirmDialog(
      onConfirm: _performDelete,
    );
  }

  /// 执行删除操作
  Future<void> _performDelete() async {
    final taskIds = us.selectedTaskIds.map((id) => id.toInt()).toList();

    final result = await _repo.deleteTask(taskIds);

    if (result.isOK()) {
      ToastUtil.showToast('删除成功');
      // 退出删除模式
      us.setDeleteMode(false);

      onRefresh();
    } else {
      ToastUtil.showToast(result.fail?.errorMsg ?? '删除失败');
    }
  }

  /// 销毁资源
  void dispose() {
    _stopTimer();
    refreshController.dispose();
  }
}
