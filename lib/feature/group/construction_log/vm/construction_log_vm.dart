
import 'package:gdjg_pure_flutter/data/group_data/group_construction_log/repo/group_construction_log_repo.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_construction_log/repo/model/report_list_biz_model.dart';
import 'package:gdjg_pure_flutter/feature/group/construction_log/entity/construction_log_props.dart';
import 'package:gdjg_pure_flutter/feature/group/construction_log/change_log/vm/change_log_vm.dart';
import 'package:gdjg_pure_flutter/feature/group/construction_log/search_log/entity/search_log_props.dart';
import 'package:gdjg_pure_flutter/feature/group/construction_log/vm/construction_log_us.dart';
import 'package:gdjg_pure_flutter/feature/group/construction_log/dialog/construction_log_action_bottom_sheet.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/init_module/init_route.dart';
import 'package:gdjg_pure_flutter/utils/system_util/yprint.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/toast_util.dart';

/// 施工日志页面ViewModel
class ConstructionLogViewModel {
  final _groupConstructionLogRepo = GroupConstructionLogRepo();
  var us = ConstructionLogUS();
  ConstructionLogProps? _props;

  /// 获取标题
  String get title => _props?.getTitle() ?? "施工日志";

  /// 初始化
  void init(ConstructionLogProps? props) {
    _props = props;

    // 设置默认时间范围：5年前1月1日到当前日期
    final now = DateTime.now();
    final defaultStartTime = DateTime(now.year - 5, 1, 1); // 5年前的1月1日
    final defaultEndTime = now; // 当前日期

    us.setStartTime(props?.startTime ?? defaultStartTime);
    us.setEndTime(props?.endTime ?? defaultEndTime);
    fetchData();
  }

  /// 获取数据
  void fetchData({bool isLoadMore = false}) async {
    if (!isLoadMore) {
      us.resetPagination();
    }

    var startTime = us.startTime != null ? _formatStartDateWithTime(us.startTime!) : "";
    var endTime = us.endTime != null ? _formatEndDateWithTime(us.endTime!) : "";

    final result = await _groupConstructionLogRepo.getReportList(
      page: us.currentPage.toString(),
      startTime: startTime,
      endTime: endTime,
      keyword: "",
      deptId: _props?.deptId ?? "",
    );

    if (result.isOK()) {
      final data = result.getSucData();
      if (data != null) {
        if (isLoadMore) {
          us.addReportList(data.list);
        } else {
          us.setReportList(data.list);
        }
        us.setTotal((data.pagination?.total ?? 0.0).toInt());
        // 判断是否有更多数据
        final currentPage = data.pagination?.page ?? 1.0;
        final lastPage = data.pagination?.lastPage ?? 1.0;
        us.setHasMore(currentPage < lastPage);
      }
    } else {
      yprint("获取施工日志失败");
    }
  }

  /// 加载更多
  void loadMore() {
    if (us.hasMore) {
      us.nextPage();
      fetchData(isLoadMore: true);
    }
  }

  /// 跳转搜索页面
  void onSearchTap() {
    YPRoute.openPage(
      RouteNameCollection.searchLog,
      params: SearchLogProps(
        deptId: _props?.deptId,
      ),
    );
  }

  /// 更新日期范围
  void updateDateRange(DateTime? startTime, DateTime? endTime) {
    us.setStartTime(startTime);
    us.setEndTime(endTime);
    fetchData();
  }

  /// 刷新数据
  void refreshData() {
    fetchData();
  }

  /// 跳转写日志页面
  void onWriteLogTap() {
    // TODO: 实现跳转写日志页面
    yprint("跳转写日志页面");
  }

  /// 图片点击事件
  void onImageTap(String imageUrl, List<String> allImages) {
    // TODO: 实现图片预览功能
    yprint("点击图片: $imageUrl");
  }

  /// 日志项点击事件 - 跳转到修改日志页面
  void onLogItemTap(ReportNetModelABizModel logItem) {
    YPRoute.openPage(
      RouteNameCollection.changeLog,
      params: ChangeLogProps(
        logId: logItem.id,
      ),
    );
  }

  /// 删除/下载按钮点击事件 - 显示操作底部弹窗
  void onDownloadTap(ReportNetModelABizModel logItem) {
    showConstructionLogActionBottomSheet(
      logItem: logItem,
      onDelete: () => _deleteLog(logItem),
      onDownload: () => _downloadLog(logItem),
    );
  }

  /// 删除日志
  void _deleteLog(ReportNetModelABizModel logItem) async {
    final result = await _groupConstructionLogRepo.deleteLog(logItem.id);
    if (result.isOK()) {
      ToastUtil.showToast('删除成功');
      refreshData(); // 刷新数据
    } else {
      ToastUtil.showToast('删除失败：${result.fail?.errorMsg ?? '未知错误'}');
    }
  }

  /// 下载日志
  void _downloadLog(ReportNetModelABizModel logItem) {

  }

  /// 格式化开始时间（包含时分秒）
  String _formatStartDateWithTime(DateTime date) {
    return "${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')} 00:00:00";
  }

  /// 格式化结束时间（包含时分秒）
  String _formatEndDateWithTime(DateTime date) {
    return "${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')} 23:59:59";
  }

  /// 销毁
  void dispose() {
    // 清理资源
  }
}
