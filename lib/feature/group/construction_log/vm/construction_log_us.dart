import 'package:gdjg_pure_flutter/data/group_data/group_construction_log/repo/model/report_list_biz_model.dart';
import 'package:get/get.dart';

/// 日期分组的日志数据
class DateGroupedLogs {
  String date;
  List<ReportNetModelABizModel> logs;

  DateGroupedLogs({
    required this.date,
    required this.logs,
  });
}

/// 施工日志页面UIState
class ConstructionLogUS {
  /// 按日期分组的日志列表
  final _groupedReportList = <DateGroupedLogs>[].obs;

  /// 原始日志列表（用于分组处理）
  final _reportList = <ReportNetModelABizModel>[];

  /// 是否有更多数据
  final _hasMore = true.obs;

  /// 开始时间
  final _startTime = Rxn<DateTime>();

  /// 结束时间
  final _endTime = Rxn<DateTime>();

  /// 当前页码
  final _currentPage = 1.obs;

  /// 总数
  final _total = 0.obs;

  /// 是否为空状态
  final _isEmpty = false.obs;

  // Getters
  List<DateGroupedLogs> get groupedReportList => _groupedReportList;
  bool get hasMore => _hasMore.value;
  DateTime? get startTime => _startTime.value;
  DateTime? get endTime => _endTime.value;
  int get currentPage => _currentPage.value;
  int get total => _total.value;
  bool get isEmpty => _isEmpty.value;

  // Setters
  void setReportList(List<ReportNetModelABizModel> list) {
    _reportList.clear();
    _reportList.addAll(list);
    _isEmpty.value = list.isEmpty;
    _updateGroupedList();
  }

  void addReportList(List<ReportNetModelABizModel> list) {
    _reportList.addAll(list);
    _updateGroupedList();
  }

  /// 更新按日期分组的列表
  void _updateGroupedList() {
    Map<String, List<ReportNetModelABizModel>> groupedMap = {};

    for (var item in _reportList) {
      String dateKey = _formatDateKey(item.editTime);
      if (groupedMap[dateKey] == null) {
        groupedMap[dateKey] = [];
      }
      groupedMap[dateKey]!.add(item);
    }

    List<DateGroupedLogs> groupedList = [];
    groupedMap.forEach((date, logs) {
      groupedList.add(DateGroupedLogs(date: date, logs: logs));
    });

    _groupedReportList.value = groupedList;
  }

  /// 格式化日期为分组键
  String _formatDateKey(double timestamp) {
    if (timestamp == 0) return "未知日期";
    try {
      DateTime dateTime = DateTime.fromMillisecondsSinceEpoch((timestamp * 1000).toInt());
      DateTime now = DateTime.now();
      DateTime today = DateTime(now.year, now.month, now.day);
      DateTime targetDate = DateTime(dateTime.year, dateTime.month, dateTime.day);

      // 周几的映射
      const weekdays = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];
      String weekday = weekdays[dateTime.weekday - 1];

      String dateStr = "${dateTime.year}年${dateTime.month.toString().padLeft(2, '0')}月${dateTime.day.toString().padLeft(2, '0')}日 $weekday";

      // 如果是今天，在前面加上"今天"
      if (targetDate == today) {
        return "今天 $dateStr";
      }

      return dateStr;
    } catch (e) {
      return "未知日期";
    }
  }



  void setHasMore(bool hasMore) {
    _hasMore.value = hasMore;
  }

  void setStartTime(DateTime? time) {
    _startTime.value = time;
  }

  void setEndTime(DateTime? time) {
    _endTime.value = time;
  }

  void setCurrentPage(int page) {
    _currentPage.value = page;
  }

  void setTotal(int total) {
    _total.value = total;
  }

  void setEmpty(bool empty) {
    _isEmpty.value = empty;
  }

  /// 重置分页
  void resetPagination() {
    _currentPage.value = 1;
    _hasMore.value = true;
  }

  /// 下一页
  void nextPage() {
    _currentPage.value++;
  }
}
