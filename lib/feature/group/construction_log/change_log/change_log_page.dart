import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import 'package:gdjg_pure_flutter/feature/group/construction_log/change_log/vm/change_log_vm.dart';
import 'package:gdjg_pure_flutter/generated/assets.dart';
import 'package:gdjg_pure_flutter/utils/route_util/base_page/base_ful_page.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/utils/system_util/date_util.dart';
import 'package:gdjg_pure_flutter/utils/system_util/string_util.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/button_util.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';


/// 修改日志页面
class ChangeLogPage extends BaseFulPage {
  const ChangeLogPage({super.key}) : super(appBar: null);

  @override
  State<ChangeLogPage> createState() => _ChangeLogPageState();
}

class _ChangeLogPageState extends BaseFulPageState<ChangeLogPage> {
  late ChangeLogViewModel _viewModel;

  @override
  void onPageCreate() {
    super.onPageCreate();
    _viewModel = ChangeLogViewModel();
  }

  @override
  void onPageRoute(Object? routeParams, bool fromLaunchTask) {
    super.onPageRoute(routeParams, fromLaunchTask);
    if (routeParams is ChangeLogProps) {
      _viewModel.initializeWithLogId(routeParams.logId);
    }
  }

  @override
  void onPageDestroy() {
    super.onPageDestroy();
    _viewModel.dispose();
  }

  @override
  Widget yBuild(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      appBar: _buildAppBar(),
      body: Obx(() => _buildBody()),
      bottomNavigationBar: _buildBottomButtons(),
    );
  }

  AppBar _buildAppBar() {
    return AppBar(
      backgroundColor: Colors.white,
      elevation: 0,
      leading: GestureDetector(
        onTap: () => YPRoute.closePage(),
        child: Container(
          padding: const EdgeInsets.only(left: 8.0),
          child: Image(
            image: AssetImage(Assets.commonIconArrowBack),
          ),
        ),
      ),
      leadingWidth: 38,
      titleSpacing: 8,
      title: const Text(
        "修改日志",
        style: TextStyle(
          color: Colors.black,
          fontSize: 22,
          fontWeight: FontWeight.w600,
        ),
      ),
      actions: [
        GestureDetector(
          onTap: () {
            //下载
          },
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.download,
                  color: ColorsUtil.primaryColor,
                  size: 20.w,
                ),
                SizedBox(width: 4.w),
                Text(
                  "下载",
                  style: TextStyle(
                    fontSize: 16.sp,
                    color: ColorsUtil.primaryColor,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildBody() {

    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: 8.h),
          _buildDateAndGroupSection(),
          SizedBox(height: 8.h),
          _buildWeatherRecorderWorkerSection(),
          SizedBox(height: 8.h),
          _buildLogContentSection(),
          SizedBox(height: 8.h),
          _buildPhotosSection(),
          SizedBox(height: 8.h),
        ],
      ),
    );
  }

  /// 记录日期和班组名称
  Widget _buildDateAndGroupSection() {
    return Container(
      color: Colors.white,
      child: Column(
        children: [
          Padding(
            padding: EdgeInsets.all(16.w),
            child: Row(
              children: [
                Text(
                  '记录日期',
                  style: TextStyle(
                    fontSize: 16.sp,
                    color: const Color(0xFF333333),
                    fontWeight: FontWeight.w500,
                  ),
                ),
                SizedBox(width: 30.w,),
                Expanded(
                  child: Text(
                    DateUtil.formatDate(_viewModel.us.recordDate.value),
                    style: TextStyle(
                      fontSize: 16.sp,
                      color: const Color(0xFF333333),
                    ),
                  ),
                ),
                Icon(
                  Icons.arrow_forward_ios,
                  size: 16.w,
                  color: const Color(0xFF999999),
                ),
              ],
            ),
          ),

          Divider(
            height: 1.h,
            color: const Color(0xFFF5F6FA),
            indent: 16.w,
            endIndent: 16.w,
          ),

          Padding(
            padding: EdgeInsets.all(16.w),
            child: Row(
              children: [
                Text(
                  '班组名称',
                  style: TextStyle(
                    fontSize: 16.sp,
                    color: const Color(0xFF333333),
                    fontWeight: FontWeight.w500,
                  ),
                ),
                SizedBox(width: 30.w,),
                Expanded(
                  child: Text(
                    _viewModel.us.groupName.value,
                    style: TextStyle(
                      fontSize: 16.sp,
                      color: const Color(0xFF333333),
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 天气、记录员、施工人数
  Widget _buildWeatherRecorderWorkerSection() {
    return Container(
      color: Colors.white,
      child: Column(
        children: [
          Padding(
            padding: EdgeInsets.only(top: 16.h, left: 16.w, right: 16.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Text(
                      '天气',
                      style: TextStyle(
                        fontSize: 16.sp,
                        color: const Color(0xFF333333),
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const Spacer(),
                    ButtonUtil.buildTextButton(
                      text: '修改',
                      onPressed: _onWeatherModifyTap,
                      fontSize: 14.0,
                    ),
                  ],
                ),
                SizedBox(height: 2.h),
                _buildWeatherSingleLine(),
              ],
            ),
          ),

          Padding(
            padding: EdgeInsets.only(top: 16.h, left: 16.w, right: 16.w, bottom: 8.h),
            child: Row(
              children: [
                Text(
                  '记录员',
                  style: TextStyle(
                    fontSize: 16.sp,
                    color: const Color(0xFF333333),
                    fontWeight: FontWeight.w500,
                  ),
                ),
                SizedBox(width: 30.w,),
                Expanded(
                  child: Text(
                    _viewModel.us.recorderName.value,
                    style: TextStyle(
                      fontSize: 16.sp,
                      color: const Color(0xFF333333),
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                Icon(
                  Icons.arrow_forward_ios,
                  size: 16.w,
                  color: const Color(0xFF999999),
                ),
              ],
            ),
          ),

          Divider(
            height: 1.h,
            color: const Color(0xFFF5F6FA),
            indent: 16.w,
            endIndent: 16.w,
          ),

          Padding(
            padding: EdgeInsets.all(16.w),
            child: Row(
              children: [
                Text(
                  '施工人数',
                  style: TextStyle(
                    fontSize: 16.sp,
                    color: const Color(0xFF333333),
                    fontWeight: FontWeight.w500,
                  ),
                ),
                
                SizedBox(width: 30.w),
                
                Text(
                  _getWorkerCountDisplayText(),
                  style: TextStyle(
                    fontSize: 16.sp,
                    color: _getWorkerCountDisplayColor(),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 天气显示
  Widget _buildWeatherSingleLine() {
    return Row(
      children: [
        Text(
          '上午：${_viewModel.us.morningWeather.value}   温度：${_viewModel.us.morningTemp.value.trimTrailingZeros()}°C',
          style: TextStyle(
            fontSize: 12.sp,
            color: const Color(0xFF666666),
          ),
        ),
        const Spacer(),
        Text(
          '下午：${_viewModel.us.afternoonWeather.value}   温度：${_viewModel.us.afternoonTemp.value.trimTrailingZeros()}°C',
          style: TextStyle(
            fontSize: 12.sp,
            color: const Color(0xFF666666),
          ),
        ),
      ],
    );
  }
  
  /// 日志内容
  Widget _buildLogContentSection() {
    return Container(
      padding: EdgeInsets.only(left: 16.w, right: 16.w, top: 16.h, bottom: 2.h),
      color: Colors.white,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                '*',
                style: TextStyle(
                  fontSize: 16.sp,
                  color: Colors.red,
                  fontWeight: FontWeight.w500,
                ),
              ),
              Text(
                '日志内容：',
                style: TextStyle(
                  fontSize: 16.sp,
                  color: const Color(0xFF333333),
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
          GestureDetector(
            onTap: () {
              // 日志内容按钮点击事件
            },
            child: Container(
              width: double.infinity,
              padding: EdgeInsets.only(top: 6.h, bottom: 2.h),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    _viewModel.us.logContent.value.isEmpty ? '暂无日志内容' : _viewModel.us.logContent.value,
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: _viewModel.us.logContent.value.isEmpty
                          ? const Color(0xFF999999)
                          : const Color(0xFF333333),
                      height: 1.5,
                    ),
                  ),
                  if (_viewModel.us.logContent.value.isNotEmpty) ...[
                    Align(
                      alignment: Alignment.bottomRight,
                      child: Text(
                        '${_viewModel.us.logContent.value.length}/500',
                        style: TextStyle(
                          fontSize: 12.sp,
                          color: const Color(0xFF999999),
                        ),
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 照片
  Widget _buildPhotosSection() {
    return Container(
      padding: EdgeInsets.all(16.w),
      color: Colors.white,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildPhotosWithText(),
        ],
      ),
    );
  }

  Widget _buildPhotosWithText() {
    final images = _viewModel.us.images;

    return SizedBox(
      width: double.infinity,
      child: Stack(
        children: [
          Padding(
            padding: EdgeInsets.only(left: 48.w),
            child: Wrap(
              spacing: 8.w,
              runSpacing: 8.h,
              children: [
                // 显示现有图片
                ...images.asMap().entries.map((entry) {
                  int index = entry.key;
                  var image = entry.value;
                  return _buildImageItem(image, index);
                }),
                if (images.length < 9) _buildUploadContainer(),
              ],
            ),
          ),

          Positioned(
            left: 0,
            child: Text(
              '照片：',
              style: TextStyle(
                fontSize: 16.sp,
                color: const Color(0xFF333333),
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 图片项目
  Widget _buildImageItem(dynamic image, int index) {
    return Stack(
      clipBehavior: Clip.none,
      children: [
        Container(
          width: 80.w,
          height: 80.w,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(4.r),
            color: const Color(0xFFF5F5F5),
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(4.r),
            child: Image.network(
              image.imgUrl ?? '',
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                return Container(
                  color: const Color(0xFFF5F5F5),
                  child: const Icon(
                    Icons.image,
                    color: Color(0xFF999999),
                  ),
                );
              },
            ),
          ),
        ),
        Positioned(
          top: -12.w,
          right: -10.w,
          child: GestureDetector(
            onTap: () => _onDeleteImageTap(index),
            behavior: HitTestBehavior.opaque,
            child: Container(
              width: 24.w,
              height: 24.w,
              alignment: Alignment.center,
              child: Image.asset(
                Assets.groupIcDeletePicture,
                width: 16.w,
                height: 16.w,
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// 上传图片
  Widget _buildUploadContainer() {
    return GestureDetector(
      onTap: () {
        _viewModel.showPhotoSelectionDialog();
      },
      child: Container(
        width: 80.w,
        height: 80.w,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(4.r),
          color: const Color(0xFFF4F6FA),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Image.asset(
              Assets.commonIcUpload,
              width: 26.w,
              height: 26.w,
              color: const Color(0xFF333333),
            ),
            Text(
              '添加照片',
              style: TextStyle(
                fontSize: 13.sp,
                color: const Color(0xFF333333),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// 施工人数
  String _getWorkerCountDisplayText() {
    final workerCount = _viewModel.us.workerCount.value;
    if (workerCount.isEmpty) {
      return '请输入数字';
    }

    final count = workerCount.trimTrailingZeros();
    if (count == '0') {
      return '请输入数字';
    }

    return count;
  }

  /// 施工人数
  Color _getWorkerCountDisplayColor() {
    final workerCount = _viewModel.us.workerCount.value;
    if (workerCount.isEmpty) {
      return const Color(0xFF999999);
    }

    final count = workerCount.trimTrailingZeros();
    if (count == '0') {
      return const Color(0xFF999999);
    }

    return const Color(0xFF333333);
  }

  /// 删除、保存按钮
  Widget _buildBottomButtons() {
    return Container(
      padding: EdgeInsets.only(left: 16.w, right: 16.w, top: 10.h, bottom: 10.h),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          top: BorderSide(color: const Color(0xFFF5F5F5), width: 0.5.w),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            flex: 1,
            child: GestureDetector(
              onTap: _onDeleteTap,
              behavior: HitTestBehavior.opaque,
              child: Container(
                height: 44.h,
                alignment: Alignment.center,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(4.r),
                  border: Border.all(color: const Color(0xFFF54A45), width: 1.w),
                  color: Colors.white,
                ),
                child: Text(
                  '删除',
                  style: TextStyle(
                    fontSize: 16.sp,
                    color: const Color(0xFFF54A45),
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),
          ),

          SizedBox(width: 15.w),

          Expanded(
            flex: 2,
            child: GestureDetector(
              onTap: _onSaveTap,
              behavior: HitTestBehavior.opaque,
              child: Container(
                height: 44.h,
                alignment: Alignment.center,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(4.r),
                  color: ColorsUtil.primaryColor,
                ),
                child: Text(
                  '保存',
                  style: TextStyle(
                    fontSize: 16.sp,
                    color: Colors.white,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 删除按钮点击事件
  void _onDeleteTap() {
    // TODO: 实现删除功能
  }

  /// 保存按钮点击事件
  void _onSaveTap() {
    // TODO: 实现保存功能
  }

  /// 删除图片
  void _onDeleteImageTap(int index) {
    // 检查索引是否有效
    if (index < 0 || index >= _viewModel.us.images.length) {
      return;
    }

    _viewModel.deleteImage(index);
  }

  /// 天气修改按钮点击事件
  void _onWeatherModifyTap() {
    // TODO: 实现天气修改功能
  }

}