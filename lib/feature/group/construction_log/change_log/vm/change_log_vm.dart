
import 'package:get/get.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_construction_log/repo/group_construction_log_repo.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_construction_log/repo/model/report_list_biz_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_construction_log/repo/model/logs_getdetail_biz_model.dart';
import 'package:gdjg_pure_flutter/feature/group/construction_log/change_log/vm/change_log_us.dart';

import 'package:gdjg_pure_flutter/utils/ui_util/toast_util.dart';

/// 修改日志页面参数
class ChangeLogProps {
  /// 要修改的日志ID
  final double logId;

  const ChangeLogProps({
    required this.logId,
  });
}

/// 修改日志页面ViewModel
class ChangeLogViewModel extends GetxController {
  final ChangeLogUS us = ChangeLogUS();
  final GroupConstructionLogRepo _groupConstructionLogRepo = GroupConstructionLogRepo();

  /// 根据日志ID获取详情数据
  void initializeWithLogId(double logId) async {
    us.logId.value = logId;

    try {
      final result = await _groupConstructionLogRepo.getLogDetail(logId);
      if (result.isOK()) {
        final logData = result.getSucData();
        if (logData != null) {
          _initializeWithLogData(logData);
        } else {
          ToastUtil.showToast("获取日志详情失败：数据为空");
        }
      } else {
        ToastUtil.showToast(result.fail?.errorMsg ?? "获取日志详情失败");
      }
    } catch (e) {
      ToastUtil.showToast("获取日志详情失败");
    }
  }

  /// 使用传入的数据初始化（内部方法）
  void _initializeWithLogData(LogsGetdetailBizModel logData) {
    // 解析时间戳为日期
    DateTime recordDate = DateTime.now();
    if (logData.editTime > 0) {
      recordDate = DateTime.fromMillisecondsSinceEpoch((logData.editTime * 1000).toInt());
    }

    // 初始化状态数据
    us.logId.value = logData.id;
    us.recordDate.value = recordDate;
    us.groupName.value = logData.source;
    us.morningWeather.value = logData.dayweather;
    us.afternoonWeather.value = logData.nightweather;
    us.morningTemp.value = logData.daytemp;
    us.afternoonTemp.value = logData.nighttemp;
    us.recorderName.value = logData.username?.toString() ?? '记录员';
    us.workerCount.value = logData.constructionPeoNum.toString();
    us.logContent.value = logData.contents;
    us.images.value = logData.imgs.map((img) => ImgBizModel(imgUrl: img)).toList();


  }

  /// 删除照片
  /// [index] 照片索引
  void deleteImage(int index) {
    if (index >= 0 && index < us.images.length) {
      us.images.removeAt(index);
    }
  }

  /// 显示照片选择弹窗
  void showPhotoSelectionDialog() {

  }

  /// 销毁资源
  @override
  void dispose() {
    super.dispose();
  }

}
