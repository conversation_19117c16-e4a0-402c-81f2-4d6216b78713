import 'package:gdjg_pure_flutter/data/group_data/group_construction_log/repo/model/report_list_biz_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_construction_log/repo/model/logs_getdetail_biz_model.dart';
import 'package:get/get.dart';

/// 修改日志页面状态管理
class ChangeLogUS {
  /// 日志ID
  final Rx<double> logId = 0.0.obs;

  /// 记录日期
  final Rx<DateTime> recordDate = DateTime.now().obs;
  
  /// 班组名称
  final RxString groupName = ''.obs;
  
  /// 上午天气
  final RxString morningWeather = ''.obs;
  
  /// 下午天气
  final RxString afternoonWeather = ''.obs;
  
  /// 上午温度
  final RxString morningTemp = ''.obs;
  
  /// 下午温度
  final RxString afternoonTemp = ''.obs;
  
  /// 记录员姓名
  final RxString recorderName = ''.obs;
  
  /// 施工人数
  final RxString workerCount = ''.obs;
  
  /// 日志内容
  final RxString logContent = ''.obs;
  
  /// 图片列表
  final RxList<ImgBizModel> images = <ImgBizModel>[].obs;

  /// 原始日志数据
  LogsGetdetailBizModel? originalData;

  /// 重置状态
  void reset() {
    logId.value = 0.0;
    recordDate.value = DateTime.now();
    groupName.value = '';
    morningWeather.value = '';
    afternoonWeather.value = '';
    morningTemp.value = '';
    afternoonTemp.value = '';
    recorderName.value = '';
    workerCount.value = '';
    logContent.value = '';
    images.clear();
    originalData = null;
  }
}
