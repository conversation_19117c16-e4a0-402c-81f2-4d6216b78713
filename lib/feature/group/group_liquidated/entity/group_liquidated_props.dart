/// @date 2025/07/01
/// @description GroupUnLiquidated页入参
class GroupLiquidatedProps {
  ///项目名称
  final String workNoteName;

  ///项目id
  final String workNote;

  ///部门id
  final double deptId;

  ///是否是带班 0-否 1-是
  final double isAgent;

  ///是否加入不知道啥意思原来也没有注释
  final bool isJoin;

  GroupLiquidatedProps(
      {required this.workNoteName,
      required this.workNote,
      required this.deptId,
      required this.isAgent,
      required this.isJoin});

  @override
  String toString() {
    return 'GroupUnLiquidatedProps{title: $workNoteName}';
  }
}
