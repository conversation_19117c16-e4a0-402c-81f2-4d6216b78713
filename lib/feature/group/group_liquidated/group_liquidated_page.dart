import 'dart:async';

import 'package:flutter/material.dart';
import 'package:gdjg_pure_flutter/feature/group/group_event_bus/group_edit_wage_event_bus_model.dart';
import 'package:gdjg_pure_flutter/feature/group/group_liquidated/entity/group_liquidated_props.dart';
import 'package:gdjg_pure_flutter/feature/group/group_liquidated/view/custom_tab_bar_view.dart';
import 'package:gdjg_pure_flutter/feature/group/group_liquidated/view/settled_tab_view.dart';
import 'package:gdjg_pure_flutter/feature/group/group_liquidated/view/un_settled_tab_view.dart';
import 'package:gdjg_pure_flutter/init_module/init_route.dart';
import 'package:gdjg_pure_flutter/utils/event_bus_util/event_bus_util.dart';
import 'package:gdjg_pure_flutter/utils/route_util/base_page/base_ful_page.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'vm/group_liquidated_viewmodel.dart';

///启动当前页面
///[workNoteName] 项目名称
///[workNote] 项目id
///[dept_id] 部门id
///[identity] 1进入班组本，2进入个人本
///[isAgent] 是否是带班 0-否 1-是
void startPageGroupUnLiquidated({
  required String workNoteName,
  required String workNote,
  required double deptId,
  required bool isJoin,
  required double isAgent,
}) {
  YPRoute.openPage(RouteNameCollection.liquidated,
      params: GroupLiquidatedProps(
          workNoteName: workNoteName,
          workNote: workNote,
          deptId: deptId,
          isJoin: isJoin,
          isAgent: isAgent));
}

/// @date 2025/07/01
/// @param props 页面路由参数
/// @returns
/// @description 班组长未结页面
class GroupLiquidatedPage extends BaseFulPage {
  GroupLiquidatedPage({super.key}) : super(appBar: YPAppBar());

  @override
  createState() => _GroupUnLiquidatedPageState();
}

///班组长未结页面状态
class _GroupUnLiquidatedPageState extends BaseFulPageState<GroupLiquidatedPage>
    with SingleTickerProviderStateMixin {
  final GroupLiquidatedViewModel viewModel = GroupLiquidatedViewModel();
  late TabController _tabController;
  late GroupLiquidatedProps? props;
  StreamSubscription? _stream;
  late final VoidCallback _tabListener;
  late RefreshController _refreshController1;
  late RefreshController _refreshController2;

  @override
  void onPageRoute(Object? routeParams, bool fromLaunchTask) {
    super.onPageRoute(routeParams, fromLaunchTask);
    props = routeParams as GroupLiquidatedProps?;
    dynamicTitle = props?.workNoteName ?? '';
    viewModel.initParam(props);
    viewModel.init();
  }

  @override
  void onPageCreate() {
    super.onPageCreate();
    _stream = EventBusUtil.collect<GroupEditWageEventBusModel>((data) {
      viewModel.fetchData();
    });
    _tabListener = () {
      setState(() {});
    };
    _refreshController1 = RefreshController(initialRefresh: false);
    _refreshController2 = RefreshController(initialRefresh: false);
    _tabController = TabController(length: 2, vsync: this);
    _tabController.addListener(_tabListener);
  }

  @override
  void onPageDestroy() {
    super.onPageDestroy();
    _stream?.cancel();
    _tabController.removeListener(_tabListener); // 正确移除
    _tabController.dispose();
    _refreshController1.dispose();
    _refreshController2.dispose();
  }

  @override
  Widget yBuild(BuildContext context) {
    return Scaffold(body: contentView());
  }

  /// 实际展示的视图，需要处理：
  /// 1. 画出UI
  /// 2. 绑定UIState
  /// 3. 处理事件监听
  /// 4. 向VM传递来自View层的事件
  Widget contentView() {
    // 进行事件处理
    return Container(
      color: Colors.white,
      child: Column(children: [
        Container(
          color: Color(0XFFF5F5F5),
          height: 5,
        ),
        tabBarTitleView(),
        tabBarContentView()
      ]),
    );
  }

  ///tabBar标题
  Widget tabBarTitleView() {
    return CustomTabBar(tabController: _tabController, viewModel: viewModel);
  }

  ///barBar内容
  Widget tabBarContentView() {
    return Expanded(
      child: TabBarView(
        controller: _tabController,
        children: [
          // 未结工友列表
          UnSettledTabView(
              viewModel: viewModel, refreshController: _refreshController1),
          // 已结清列表
          SettledTabView(
              viewModel: viewModel, refreshController: _refreshController2),
        ],
      ),
    );
  }
}
