import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/feature/group/group_liquidated/vm/group_liquidated_viewmodel.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';
import 'package:gdjg_pure_flutter/widget/divider_view.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_obx_widget.dart';

///自定义tabBar
import 'package:get/get.dart';

/// 自定义 TabBar，左对齐、不卡顿
class CustomTabBar extends StatefulWidget {
  final TabController tabController;
  final GroupLiquidatedViewModel viewModel;

  const CustomTabBar({
    super.key,
    required this.tabController,
    required this.viewModel,
  });

  @override
  State<CustomTabBar> createState() => _CustomTabBarState();
}

class _CustomTabBarState extends State<CustomTabBar> {
  late TabController _controller;

  @override
  void initState() {
    super.initState();
    _controller = widget.tabController;
    _controller.animation?.addListener(_onAnimationChanged);
  }

  void _onAnimationChanged() {
    if (mounted) {
      setState(() {}); // 触发选中状态更新（避免延迟）
    }
  }

  @override
  void dispose() {
    _controller.animation?.removeListener(_onAnimationChanged);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final notSettleCount = widget.viewModel.uiState.notSettleData.length;
      final settleCount = widget.viewModel.uiState.settleData.length;
      final currentIndex = _controller.animation?.value ?? _controller.index;
      return Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              const SizedBox(width: 16),
              CustomTabLabel(
                text: '未结工友($notSettleCount)',
                isSelected: currentIndex.round() == 0,
                onTap: () => _controller.animateTo(0),
              ),
              const SizedBox(width: 40),
              CustomTabLabel(
                text: '已结清($settleCount)',
                isSelected: currentIndex.round() == 1,
                onTap: () => _controller.animateTo(1),
              ),
            ],
          ),
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 8),
            child: const DividerView(),
          ),
        ],
      );
    });
  }
}

/// 单个 label 样式
class CustomTabLabel extends StatelessWidget {
  final String text;
  final bool isSelected;
  final VoidCallback onTap;

  const CustomTabLabel({
    super.key,
    required this.text,
    required this.isSelected,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        color: Colors.transparent,
        height: 45,
        alignment: Alignment.centerLeft,
        child: Text(
          text,
          style: TextStyle(
            fontSize: 18.sp,
            color: isSelected ? ColorsUtil.primaryColor : ColorsUtil.black85,
          ),
        ),
      ),
    );
  }
}
