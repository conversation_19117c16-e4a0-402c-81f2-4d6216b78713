import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/feature/group/group_liquidated/vm/group_liquidated_viewmodel.dart';
import 'package:gdjg_pure_flutter/feature/group/group_liquidated/vm/protocol/group_liquidated_ui_state.dart';
import 'package:gdjg_pure_flutter/feature/group/group_statistics/view/empty_view.dart';
import 'package:gdjg_pure_flutter/iconfont/iconfont.dart';
import 'package:gdjg_pure_flutter/utils/system_util/font_util.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';
import 'package:gdjg_pure_flutter/widget/divider_view.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_obx_widget.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

///未结工友tabView
class UnSettledTabView extends StatelessWidget {
  final GroupLiquidatedViewModel viewModel;
  final RefreshController refreshController;

  const UnSettledTabView(
      {super.key, required this.viewModel, required this.refreshController});

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      //未结列表是否为空
      final listIsEmpty = viewModel.uiState.notSettleData.isEmpty;
      if (listIsEmpty) {
        return EmptyView(subtitle: '暂无数据');
      } else {
        return SmartRefresher(
          controller: refreshController,
          enablePullDown: true,
          enablePullUp: false,
          onRefresh: _onRefresh,
          child: contentWidget()
        );
      }
    });
  }

  Widget contentWidget() {
    return Column(
      children: [
        unSettledTotalWidget(),
        ListView.builder(
          shrinkWrap: true,
          itemCount: viewModel.uiState.notSettleData.length,
          // 添加此属性
          physics: const NeverScrollableScrollPhysics(),
          itemBuilder: (context, index) {
            final item = viewModel.uiState.notSettleData[index];
            return GestureDetector(
                onTap: () {
                  viewModel.onUnSettledItemTap(item);
                },
                child: unSettledListWidget(item));
          },
        )
      ],
    );
  }

  ///总未结组件
  Widget unSettledTotalWidget() {
    return Column(
      children: [
        Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 12),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end, // 靠右对齐
              children: [
                Text(
                  "总未结：",
                  style: TextStyle(color: ColorsUtil.black85, fontSize: 17.sp),
                ),
                Obx(() => Text(
                      viewModel.uiState.displayAllUnSettleTotal,
                      style: TextStyle(
                          color: ColorsUtil.primaryColor,
                          fontSize: 26.sp,
                          fontFamily: FontUtil.fontCondMedium),
                    )),
                SizedBox(width: 8),
                Obx(
                  () => GestureDetector(
                    onTap: () {
                      viewModel.onShowMoneyStateChangeTab();
                    },
                    child: IconFont(
                      viewModel.moneyIconFont.value,
                      size: 16,
                      color: '#5290FD',
                    ),
                  ),
                ),
              ],
            )),
        // 分割线，保持默认高度
        Container(
            margin: EdgeInsets.symmetric(horizontal: 8), child: DividerView()),
      ],
    );
  }

  ///未结列表
  Widget unSettledListWidget(GroupUnLiquidatedChildUiState item) {
    return Container(
      //设置这个是为了点击事件
      color: Colors.transparent,
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 16),
            child: Row(children: [
              Image.network(
                'https://cdn.yupaowang.com/jgjz/icon_unliquidated_user.png',
                width: 28,
                height: 28,
                fit: BoxFit.cover,
              ),
              SizedBox(width: 7.5),
              Text(item.workerName,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: TextStyle(color: ColorsUtil.black85, fontSize: 17.sp)),
              SizedBox(width: 5),
              Visibility(
                visible: item.isDeleted ?? true,
                child: Container(
                  height: 22.5,
                  padding: const EdgeInsets.symmetric(horizontal: 5),
                  decoration: BoxDecoration(
                      color: ColorsUtil.f5f5f5,
                      borderRadius: BorderRadius.circular(11)),
                  child: Center(
                    child: Text("已退场",
                        style: TextStyle(
                            color: ColorsUtil.black45, fontSize: 15.sp)),
                  ),
                ),
              ),
              Spacer(),
              Text(item.displayMoney ?? "",
                  style: TextStyle(
                      color: ColorsUtil.primaryColor,
                      fontSize: 20.sp,
                      fontFamily: FontUtil.fontCondMedium)),
              IconFont(
                IconNames.saasArrowRight,
                size: 15,
                color: '#8D8B95',
              ),
            ]),
          ),
          Container(
              margin: EdgeInsets.symmetric(horizontal: 8),
              child: DividerView()),
        ],
      ),
    );
  }

  /// 处理刷新
  Future<void> _onRefresh() async {
    try {
      await viewModel.fetchData();
      refreshController.refreshCompleted();
    } catch (e) {
      refreshController.refreshFailed();
    }
  }
}
