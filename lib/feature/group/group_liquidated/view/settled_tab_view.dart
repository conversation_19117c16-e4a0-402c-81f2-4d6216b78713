import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/feature/group/group_liquidated/vm/group_liquidated_viewmodel.dart';
import 'package:gdjg_pure_flutter/feature/group/group_liquidated/vm/protocol/group_liquidated_ui_state.dart';
import 'package:gdjg_pure_flutter/feature/group/group_statistics/view/empty_view.dart';
import 'package:gdjg_pure_flutter/iconfont/iconfont.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';
import 'package:gdjg_pure_flutter/widget/divider_view.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_obx_widget.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

///已结清tabView
class SettledTabView extends StatelessWidget {
  final GroupLiquidatedViewModel viewModel;
  final RefreshController refreshController;

  const SettledTabView(
      {super.key, required this.viewModel, required this.refreshController});

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      //已结列表是否为空
      final listIsEmpty = viewModel.uiState.settleData.isEmpty;
      if (listIsEmpty) {
        return EmptyView(subtitle: '暂无数据');
      } else {
        return SmartRefresher(
            controller: refreshController,
            enablePullDown: true,
            enablePullUp: false,
            onRefresh: _onRefresh,
            child: contentWidget());
      }
    });
  }

  Widget contentWidget() {
    return Column(
      children: [
        ListView.builder(
          shrinkWrap: true,
          itemCount: viewModel.uiState.settleData.length,
          // 添加此属性
          physics: const NeverScrollableScrollPhysics(),
          itemBuilder: (context, index) {
            final item = viewModel.uiState.settleData[index];
            return GestureDetector(
                onTap: () {
                  viewModel.onSettledItemTap(item);
                },
                child: settledListWidget(item));
          },
        )
      ],
    );
  }

  ///已结列表
  Widget settledListWidget(GroupUnLiquidatedChildUiState item) {
    return Container(
      //设置这个是为了点击事件
      color: Colors.transparent,
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 12),
            child: Row(children: [
              IconFont(
                IconNames.saasIconUserBuilding,
                size: 30,
                color: '#333333',
              ),
              SizedBox(width: 7.5),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(item.workerName,
                      style: TextStyle(
                          color: ColorsUtil.black85, fontSize: 20.sp)),
                  Text(item.showDate ?? "",
                      textAlign: TextAlign.left,
                      style: TextStyle(
                          color: ColorsUtil.black45, fontSize: 15.sp)),
                ],
              ),
              SizedBox(width: 5),
              Spacer(),
              IconFont(
                IconNames.saasArrowRight,
                size: 18,
                color: '#8A8A99',
              ),
            ]),
          ),
          Container(
              margin: EdgeInsets.symmetric(horizontal: 8),
              child: DividerView()),
        ],
      ),
    );
  }

  /// 处理刷新
  Future<void> _onRefresh() async {
    try {
      await viewModel.fetchData();
      refreshController.refreshCompleted();
    } catch (e) {
      refreshController.refreshFailed();
    }
  }
}
