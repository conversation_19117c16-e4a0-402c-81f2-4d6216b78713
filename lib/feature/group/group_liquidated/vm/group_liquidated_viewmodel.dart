import "package:gdjg_pure_flutter/data/group_data/group_un_liauidated/ds/model/param/group_project_get_project_settle_param_model.dart";
import "package:gdjg_pure_flutter/data/group_data/group_un_liauidated/repo/model/group_project_get_project_settle_biz_model.dart";
import "package:gdjg_pure_flutter/data/group_data/group_un_liauidated/repo/project_settle_repo.dart";
import "package:gdjg_pure_flutter/feature/group/common_params_model/worker_model.dart";
import "package:gdjg_pure_flutter/feature/group/group_liquidated/entity/group_liquidated_props.dart";
import "package:gdjg_pure_flutter/feature/group/group_liquidated_detail/group_liquidated_detail_page.dart";
import "package:gdjg_pure_flutter/iconfont/iconfont.dart";
import "package:gdjg_pure_flutter/utils/system_util/string_util.dart";
import "package:gdjg_pure_flutter/utils/system_util/yprint.dart";
import "package:get/get.dart";
import "protocol/group_liquidated_ui_state.dart";

/// @date 2025/07/01
/// @description GroupUnLiquidated页ViewModel
/// 1. 处理数据的加载状态，如loading和错误视图等
/// 2. 观测UIRep中的业务实体转为UIState
/// 3. 接受来自view的意图并分发事件进行处理（暂无）
/// 4. 对View层暴露事件（操作视图和处理一些与系统权限交互等场景）
class GroupLiquidatedViewModel {
  var moneyIconFont = IconNames.saasEye.obs;
  var showMoney = false;
  final uiState = GroupLiquidatedUS();
  final _repo = ProjectSettleRepo();

  ///路由参数
  GroupLiquidatedProps? props;

  initParam(GroupLiquidatedProps? props) {
    this.props = props;
  }

  init() {
    fetchData();
  }

  /// 初始化数据同步，如果不需要loading，则在这里去掉
  Future<void> fetchData() async {
    try {
      // 同步获取数据
      var result = await _repo.getProjectSettle(
          GroupProjectGetProjectSettleParamModel(workNote: getWorkNote()));
      if (result.isOK()) {
        convertEntityToUIState(result.getSucData());
      }
    } catch (e) {
      yprint(e.toString());
    }
  }

  /// 切换金额是否显示
  void onShowMoneyStateChangeTab() async {
    showMoney = !showMoney;
    moneyIconFont.value =
        showMoney ? IconNames.saasEye : IconNames.saasEyeClose;
    uiState.setDisplayAllUnSettleTotal(
        showMoney ? uiState.allUnSettleTotal : '****');
    // 切换每一项的金额显示
    final updatedList = uiState.notSettleData.map((item) {
      return GroupUnLiquidatedChildUiState(
        workerName: item.workerName,
        money: item.money,
        workerId: item.workerId,
        showDate: item.showDate,
        isDeleted: item.isDeleted,
      )..displayMoney = showMoney ? item.money : '****';
    }).toList();
    uiState.setNotSettleData(updatedList);
  }

  /// 将列表实体数据转换为UIState
  void convertEntityToUIState(GroupProjectGetProjectSettleBizModel? netModel) {
    //转换为未结算ui实体
    final notSettleUIState = netModel?.notSettleData?.list.map((entity) {
      return GroupUnLiquidatedChildUiState(
          isDeleted: entity.isDeleted == 1,
          money: entity.settle.formatDoubleToMoney().toString(),
          workerName: entity.name,
          workerId: entity.workerId);
    }).toList();
    //转换已结算ui实体
    final settleUIState = netModel?.settleData?.list.map((entity) {
      return GroupUnLiquidatedChildUiState(
          showDate: "${entity.startTime} - ${entity.endTime}",
          money: entity.settle.formatDoubleToMoney().toString(),
          workerName: entity.name,
          workerId: entity.workerId,
      );
    }).toList();
    //设置总未结算金额
    uiState.setAllUnSettleTotal(
        netModel?.notSettleData?.allSettle.formatDoubleToMoney().toString() ??
            "");
    //填充未结算数据
    uiState.setNotSettleData(notSettleUIState);
    //填充已结算数据
    uiState.setSettleData(settleUIState);
  }

  ///获取项目id
  String getWorkNote() {
    return props?.workNote ?? "";
  }

  ///未结item点击事件
  onUnSettledItemTap(GroupUnLiquidatedChildUiState item) {
    onItemClickTap(item, true);
  }

  ///已结item点击事件
  onSettledItemTap(GroupUnLiquidatedChildUiState item) {
    onItemClickTap(item, false);
  }

  ///item点击事件
  Future<void> onItemClickTap(
      GroupUnLiquidatedChildUiState item, bool isUnLiquidated) async {
    var result = await startPageGroupUnLiquidatedDetail(
        workers: [ WorkerModel(workerId: item.workerId, workerName: item.workerName)],
        workNoteName: props?.workNoteName,
        workNote: getWorkNote(),
        deptId: props?.deptId,
        isUnLiquidated: isUnLiquidated,
        isJoin: props?.isJoin,
        isAgent: props?.isAgent);
    if (result != null) {
      fetchData();
    }
  }
}
