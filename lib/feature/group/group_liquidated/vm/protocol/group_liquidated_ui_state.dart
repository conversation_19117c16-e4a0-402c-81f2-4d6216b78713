import 'package:get/get.dart';

/// @date 2025/07/01
/// @description 班组长未结UI状态
class GroupLiquidatedUS {
  /// 已结算数据
  final _settleData = <GroupUnLiquidatedChildUiState>[].obs;

  /// 未结算数据
  final _notSettleData = <GroupUnLiquidatedChildUiState>[].obs;

  /// 未结总金额
  var _allUnSettleTotal = '';

  final _displayAllUnSettleTotal = ''.obs;

  /// 更新已结算数据
  void setSettleData(List<GroupUnLiquidatedChildUiState>? data) {
    if (data != null) {
      _settleData.value = data;
    }
  }

  /// 更新未结算数据
  void setNotSettleData(List<GroupUnLiquidatedChildUiState>? data) {
    if (data != null) {
      _notSettleData.value = data;
    }
  }

  /// 更新总未结
  void setAllUnSettleTotal(String allUnSettleTotal) {
    _allUnSettleTotal = allUnSettleTotal;
    setDisplayAllUnSettleTotal(allUnSettleTotal);
  }

  void setDisplayAllUnSettleTotal(String displayAllUnSettleTotal) {
    _displayAllUnSettleTotal.value = displayAllUnSettleTotal;
  }

  /// 获取已结算数据
  List<GroupUnLiquidatedChildUiState> get settleData => _settleData.value;

  /// 获取未结算数据
  List<GroupUnLiquidatedChildUiState> get notSettleData => _notSettleData.value;

  /// 最终展示的未结算总金额
  String get displayAllUnSettleTotal => _displayAllUnSettleTotal.value;

  /// 未结算总金额
  String get allUnSettleTotal => _allUnSettleTotal;
}

class GroupUnLiquidatedChildUiState {
  /// 工人姓名
  String workerName;

  /// 金额
  String money;

  /// 工人id
  double workerId;

  /// 日期
  String? showDate;

  /// 是否退场
  bool? isDeleted;

  /// 最终显示的金额
  String? displayMoney;

  GroupUnLiquidatedChildUiState(
      {required this.money,
      required this.workerName,
      required this.workerId,
      this.showDate,
      this.isDeleted})
      : displayMoney = money;
}
