import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/feature/account/role/vm/select_role_vm.dart';
import 'package:gdjg_pure_flutter/generated/assets.dart';
import 'package:gdjg_pure_flutter/utils/route_util/base_page/base_ful_page.dart';
import 'package:get/get.dart';

class SelectRolePage extends BaseFulPage {
  SelectRolePage({super.key})
      : super(appBar: null, canBack: false, backCloseCallback: () => false);

  @override
  State createState() => _SelectRolePageState();
}

class _SelectRolePageState extends BaseFulPageState {
  final SelectRoleVM selectRoleVM = SelectRoleVM();

  @override
  Widget yBuild(BuildContext context) {
    return Container(
      color: Colors.white,
      child: Column(
        children: [_buildContent(), SizedBox(height: 150.h)],
      ),
    );
  }

  _buildContent() {
    onPersonal() {}

    onClassGroup() {}

    return Expanded(
        child: Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        SizedBox(height: 40.h),
        Text('请选择您的身份',
            style: TextStyle(color: Color(0xFF323233), fontSize: 20.sp)),
        SizedBox(height: 58.h),
        Row(
          children: [
            _buildRoleCard(LoginAssets.loginPersonMark,
                selectRoleVM.personalHint, onPersonal),
            Container(
              width: 1,
              height: 138.h,
              color: Color(0xFFE5E5E5),
            ),
            _buildRoleCard(LoginAssets.loginGroupMark,
                selectRoleVM.classGroupHint, onClassGroup)
          ],
        )
      ],
    ));
  }

  _buildRoleCard(String img, Rx<String> hint, VoidCallback onClick) {
    return Expanded(
        child: Center(
      child: GestureDetector(
        onTap: onClick,
        child: Column(
          children: [
            Image.asset(
              img,
              width: 75.w,
              height: 75.w,
            ),
            SizedBox(height: 14.5.h),
            Text(
              '我是工人',
              style: TextStyle(color: Color(0xFF323233), fontSize: 16.sp),
            ),
            SizedBox(height: 9.5.h),
            Obx(() => Text(
                  hint.value,
                  style: TextStyle(color: Color(0xFF808080), fontSize: 15.sp),
                ))
          ],
        ),
      ),
    ));
  }
}
