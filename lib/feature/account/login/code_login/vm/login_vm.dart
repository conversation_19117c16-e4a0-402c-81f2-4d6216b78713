import 'package:gdjg_pure_flutter/data/account/ds/model/param/code_login_param_model.dart';
import 'package:gdjg_pure_flutter/data/account/repo/model/send_code_biz_model.dart';
import 'package:gdjg_pure_flutter/feature/account/login/code_login/vm/login_event.dart';
import 'package:gdjg_pure_flutter/init_module/init_route.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/toast_util.dart';

import '../../../../../data/account/ds/model/param/login_share_param_model.dart';
import '../../../../../data/account/repo/auth_repo.dart';
import '../../dialog/login_privacy_dialog.dart';
import 'login_us.dart';

class LoginVM {
  final _authRepo = AuthRepo();
  final _event = LoginEvent();
  final us = LoginUS();

  var _phone = '';
  var _code = '';
  var _isPhoneFocus = false;
  var _isCodeFocus = false;

  /// 是否同意隐私政策
  var _isPrivacyAgreed = false;

  ///验证码返回的验证Token
  String? _verifyToken;

  /// 点击登录
  Future<void> onLoginTap() async {
    if (!_isPrivacyAgreed) {
      showLoginPrivacyDialog(() {
        _innerCodeLogin();
      });
    } else {
      _innerCodeLogin();
    }
  }

  Future<void> _innerCodeLogin() async {
    //TODO 需要加分享相关的参数
    var resp = await _authRepo.loginCodeLogin(CodeLoginParamModel(
        code: _code,
        tel: _phone,
        shareReq: ShareReq(),
        verifyToken: _verifyToken));
    if (resp.isOK()) {
      YPRoute.openPage(RouteNameCollection.main,closeNumBeforeOpen: 1);
    }else {
      ToastUtil.showToast(resp.fail?.errorMsg ?? "登录失败");
    }
  }

  /// 点击获取验证码
  Future<SendCodeBizModel?> onGetCodeTap() async {
    final resp = await _authRepo.sendCode(_phone);
    if (resp.isOK()) {
      _verifyToken = resp.getSucData()?.verifyToken ?? "";
      return resp.getSucData();
    } else {
      ToastUtil.showToast(resp.fail?.errorMsg ?? "获取验证码失败");
      return null;
    }
  }

  void setPhoneFocus(bool focus) {
    _isPhoneFocus = focus;
    us.setPhoneFocus(focus);
    us.setHintText(focus, _phone);
    us.setPhoneClear(focus, _phone);
  }

  void setCodeFocus(bool focus) {
    _isCodeFocus = focus;
    us.setCodeFocus(focus);
    us.setCodeClear(focus, _code);
  }

  void setPhoneNumber(String phone) {
    final relPhone = phone.replaceAll(RegExp(r'\s+'), '');
    _phone = relPhone;
    us.setPhoneClear(_isPhoneFocus, relPhone);
    _setIsLogin(relPhone, _code);
  }

  void setCode(String code) {
    _code = code;
    us.setCodeClear(_isCodeFocus, code);
    _setIsLogin(_phone, code);
  }

  /// 点击同意隐私。
  void onTogglePrivacyAgreementTap() {
    _isPrivacyAgreed = !_isPrivacyAgreed;
    us.setPrivacyAgreed(_isPrivacyAgreed);
  }

  _setIsLogin(String phone, String code) {
    us.setEnableLogin(phone, code);
  }
}
