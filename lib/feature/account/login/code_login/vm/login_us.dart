import 'package:gdjg_pure_flutter/data/account/repo/model/send_code_biz_model.dart';
import 'package:gdjg_pure_flutter/utils/regex/regex_utils.dart';
import 'package:get/get.dart';

class LoginUS {
  //以下的的属于为UI状态
  final _inputPhoneFocus = false.obs;
  final _codeFocus = false.obs;
  final _showPhoneClear = false.obs;
  final _showCodeClear = false.obs;
  final _showOneKeyLogin = true.obs;

  /// 提示文案
  final _hintText = ''.obs;
  final _enableLogin = false.obs;

  /*隐私政策同意状态*/
  final _privacyAgreed = false.obs;



  bool get inputPhoneFocus => _inputPhoneFocus.value;

  bool get codeFocus => _codeFocus.value;

  bool get showPhoneClear => _showPhoneClear.value;

  bool get showCodeClear => _showCodeClear.value;

  bool get showOneKeyLogin => _showOneKeyLogin.value;

  String get hintText => _hintText.value;

  bool get enableLogin => _enableLogin.value;

  bool get privacyAgreed => _privacyAgreed.value;


  void setPhoneFocus(bool focus) {
    _inputPhoneFocus.value = focus;
  }

  void setCodeFocus(bool focus) {
    _codeFocus.value = focus;
  }

  void setPhoneClear(bool phoneFocus, String phone) {
    _showPhoneClear.value = phoneFocus && phone.isNotEmpty;
  }

  void setCodeClear(bool codeFocus, String code) {
    _showCodeClear.value = codeFocus && code.isNotEmpty;
  }

  void setOneKeyLogin(bool show) {
    _showOneKeyLogin.value = show;
  }

  void setHintText(bool phoneFocus, String phone) {
    if (phoneFocus) {
      _hintText.value = '';
    } else {
      if (RegexUtils.isMobile(phone)) {
        _hintText.value = '';
      } else {
        _hintText.value = '请填写正确的手机号';
      }
    }
  }

  void setEnableLogin(String phone, String code) {
    _enableLogin.value =
        RegexUtils.isMobile(phone) && RegexUtils.isVerificationCode(code);
  }

  void setPrivacyAgreed(bool agreed) {
    _privacyAgreed.value = agreed;
  }
}
