import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/feature/account/login/code_login/vm/login_vm.dart';
import 'package:gdjg_pure_flutter/generated/assets.dart';
import 'package:gdjg_pure_flutter/utils/route_util/base_page/base_ful_page.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/button_util.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';
import 'package:gdjg_pure_flutter/widget/countdown_button.dart';
import 'package:get/get.dart';
import 'package:mask_text_input_formatter/mask_text_input_formatter.dart';

class LoginPage extends BaseFulPage {
  const LoginPage({super.key}) : super(appBar: null);

  @override
  State createState() => _LoginPageState();
}

class _LoginPageState extends BaseFulPageState {
  final LoginVM loginVM = LoginVM();
  final TextEditingController _phoneController = TextEditingController();
  final TextEditingController _codeController = TextEditingController();
  final FocusNode _phoneFocusNode = FocusNode();
  final FocusNode _codeFocusNode = FocusNode();

  @override
  void onPageCreate() {
    _phoneFocusNode.addListener(() {
      loginVM.setPhoneFocus(_phoneFocusNode.hasFocus);
    });
    _codeFocusNode.addListener(() {
      loginVM.setCodeFocus(_codeFocusNode.hasFocus);
    });
  }

  @override
  void onPageDestroy() {
    _phoneFocusNode.dispose();
    _codeFocusNode.dispose();
    _phoneController.dispose();
    _codeController.dispose();
  }

  _clearPhone() {
    _phoneController.clear();
    loginVM.setPhoneNumber('');
  }

  _clearCode() {
    _codeController.clear();
    loginVM.setCode('');
  }

  @override
  Widget yBuild(BuildContext context) {
    return Stack(
      children: [
        _buildTopBg(),
        Container(
          color: Colors.white,
          child: Padding(
            padding: EdgeInsets.fromLTRB(36.w, 104.h, 36.w, 0),
            child: Column(
              children: [
                _buildLoginRegisterHint(),
                _buildPhone(),
                _buildCode(),
                ..._buildHint(),
                Obx(() => ButtonUtil.buildCommonButton(
                    enabled: loginVM.us.enableLogin,
                    text: '登录',
                    onPressed: () {
                      loginVM.onLoginTap();
                    })),
                _buildOneKeyLoginText(),
                _buildPrivacyText(),
                _buildOtherLogin()
              ],
            ),
          ),
        )
      ],
    );
  }

  _buildTopBg() {
    return Container(
      height: 160.h,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Color(0xFFE8F3FF), // 开始颜色 #E8F3FF
            Color(0xFFFFFFFF), // 结束颜色 #FFFFFF
          ],
        ),
      ),
    );
  }

  _buildLoginRegisterHint() {
    return SizedBox(
      height: 48.h,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Image.asset(
            LoginAssets.loginIcPopSmile,
            width: 24.w,
            height: 24.w,
          ),
          SizedBox(width: 8.w),
          Text(
            '登录/注册',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: Colors.black,
              fontSize: 17.sp,
            ),
          ),
        ],
      ),
    );
  }

  _buildPhone() {
    return Container(
      height: 48.h,
      margin: EdgeInsets.only(top: 8.h),
      alignment: Alignment.center,
      decoration: BoxDecoration(
          border: Border(bottom: BorderSide(color: Color(0xFFEFF1F6)))),
      child: Row(
        children: [
          Obx(
            () => Image.asset(
              loginVM.us.inputPhoneFocus
                  ? LoginAssets.loginIcInputPhoneSelector
                  : LoginAssets.loginIcInputPhone,
              width: 24.w,
              height: 24.w,
            ),
          ),
          SizedBox(
            width: 12.w,
          ),
          Expanded(
              child: TextField(
            focusNode: _phoneFocusNode,
            controller: _phoneController,
            onChanged: (value) {
              loginVM.setPhoneNumber(value);
            },
            cursorColor: ColorsUtil.ypPrimaryColor,
            maxLines: 1,
            keyboardType: TextInputType.number,
            inputFormatters: <TextInputFormatter>[
              FilteringTextInputFormatter.allow(RegExp(r'^1[0-9 ]*')),
              MaskTextInputFormatter(
                  mask: '### #### ####',
                  filter: {"#": RegExp(r'[0-9]')},
                  type: MaskAutoCompletionType.lazy),
              LengthLimitingTextInputFormatter(13),
            ],
            style: TextStyle(
              color: ColorsUtil.black85,
              fontSize: 15.sp,
            ),
            decoration: InputDecoration(
              hintText: '请输入手机号码',
              hintStyle: TextStyle(color: Colors.black45),
              border: InputBorder.none,
            ),
          )),
          Obx(() => _buildClear(
              visible: loginVM.us.showPhoneClear,
              padding: EdgeInsets.fromLTRB(13.w, 3.h, 0, 3.h),
              onPressed: _clearPhone)),
        ],
      ),
    );
  }

  _buildCode() {
    return Container(
      height: 48.h,
      margin: EdgeInsets.only(top: 35.h),
      alignment: Alignment.center,
      decoration: BoxDecoration(
          border: Border(bottom: BorderSide(color: Color(0xFFEFF1F6)))),
      child: Row(
        children: [
          Obx(
            () => Image.asset(
              loginVM.us.codeFocus
                  ? LoginAssets.loginIcInputPwdSelector
                  : LoginAssets.loginIcInputPwd,
              width: 24.w,
              height: 24.w,
            ),
          ),
          SizedBox(
            width: 12.w,
          ),
          Expanded(
              child: TextField(
            focusNode: _codeFocusNode,
            controller: _codeController,
            onChanged: (value) {
              loginVM.setCode(value);
            },
            cursorColor: ColorsUtil.ypPrimaryColor,
            maxLines: 1,
            keyboardType: TextInputType.number,
            inputFormatters: <TextInputFormatter>[
              FilteringTextInputFormatter.digitsOnly,
              LengthLimitingTextInputFormatter(4),
            ],
            style: TextStyle(
              color: ColorsUtil.black85,
              fontSize: 15.sp,
            ),
            decoration: InputDecoration(
              hintText: '请输入验证码',
              hintStyle: TextStyle(color: Colors.black45),
              border: InputBorder.none,
            ),
          )),
          Obx(() => _buildClear(
              visible: loginVM.us.showCodeClear,
              padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 3.h),
              onPressed: _clearCode)),
          CountdownButton(onPressed: (startCountDown) {
            loginVM.onGetCodeTap().then((data) {
              if (data != null) {
                startCountDown(data.sendInterval);
              }
            });
          })
        ],
      ),
    );
  }

  _buildClear(
      {final bool visible = false,
      final EdgeInsetsGeometry? padding,
      final VoidCallback? onPressed}) {
    return Visibility(
      visible: visible,
      child: IconButton(
          onPressed: onPressed,
          padding: padding,
          constraints: BoxConstraints(),
          style: ButtonStyle(
            tapTargetSize: MaterialTapTargetSize.shrinkWrap, // 减小点击区域
            overlayColor: WidgetStateProperty.all(Colors.transparent),
          ),
          icon: Image.asset(
            LoginAssets.loginIcInputClose,
            width: 16.w,
            height: 16.w,
          )),
    );
  }

  _buildHint() {
    return [
      SizedBox(height: 12.h),
      Obx(
        () => Text(loginVM.us.hintText,
            style: TextStyle(color: Colors.red, fontSize: 12.sp)),
      ),
      SizedBox(height: 17.h)
    ];
  }

  _buildOneKeyLoginText() {
    return Obx(() => Visibility(
          visible: loginVM.us.showOneKeyLogin,
          child: Container(
            margin: EdgeInsets.only(top: 25.h),
            child: TextButton(
                onPressed: () {},
                style: TextButton.styleFrom(
                  minimumSize: Size.zero,
                  padding: EdgeInsets.zero,
                  // 禁用点击效果
                  overlayColor: Colors.transparent,
                ),
                child: Text('使用一键快速登录',
                    style: TextStyle(
                        color: ColorsUtil.ypPrimaryColor,
                        fontSize: 15.sp,
                        fontWeight: FontWeight.bold))),
          ),
        ));
  }

  _buildPrivacyText() {
    return Container(
      margin: EdgeInsets.only(top: 36.h),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // 复选框
          GestureDetector(
            onTap: loginVM.onTogglePrivacyAgreementTap,
            child: Padding(
              padding: EdgeInsets.only(right: 4.w),
              child: Obx(
                () => Image.asset(
                  loginVM.us.privacyAgreed
                      ? LoginAssets.loginIcSignXieyiY
                      : LoginAssets.loginIcSignXieyiN,
                  width: 16.w,
                  height: 16.w,
                ),
              ),
            ),
          ),
          // 富文本
          Text.rich(
            TextSpan(
              style: TextStyle(
                  fontSize: 13.sp, color: ColorsUtil.black45, height: 1),
              children: [
                TextSpan(text: '我已阅读并同意'),
                TextSpan(
                  text: '《隐私政策》',
                  style: TextStyle(
                      fontSize: 13.sp,
                      color: ColorsUtil.ypPrimaryColor,
                      height: 1),
                  recognizer: TapGestureRecognizer()
                    ..onTap = () => {
                          // TODO: 实现打开隐私政策页面的逻辑
                        },
                ),
                TextSpan(text: ' '),
                TextSpan(
                  text: '《服务协议》',
                  style: TextStyle(
                      fontSize: 13.sp,
                      color: ColorsUtil.ypPrimaryColor,
                      height: 1),
                  recognizer: TapGestureRecognizer()
                    ..onTap = () => {
                          // TODO: 实现打开服务协议页面的逻辑
                        },
                )
              ],
            ),
            softWrap: true,
          ),
        ],
      ),
    );
  }

  _buildOtherLogin() {
    return Expanded(
        child: Column(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        SizedBox(height: 30.h),
        Text('其他登录方式',
            style: TextStyle(color: Colors.black45, fontSize: 13.sp)),
        SizedBox(height: 16.h),
        GestureDetector(
          onTap: () {},
          child: Image.asset(
            LoginAssets.loginIcWxLogin,
            width: 40.w,
            height: 40.w,
          ),
        ),
        SizedBox(height: 50.h),
      ],
    ));
  }
}
