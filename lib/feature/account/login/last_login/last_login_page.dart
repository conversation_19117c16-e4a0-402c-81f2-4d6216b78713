import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/feature/account/login/last_login/last_login_vm.dart';
import 'package:gdjg_pure_flutter/generated/assets.dart';
import 'package:gdjg_pure_flutter/utils/route_util/base_page/base_ful_page.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';
import 'package:get/get.dart';

class LastLoginPage extends BaseFulPage {
  const LastLoginPage({super.key}) : super(appBar: null);

  @override
  State createState() => _LastLoginPageState();
}

class _LastLoginPageState extends BaseFulPageState {
  final LastLoginVM lastLoginVM = LastLoginVM();

  @override
  Widget yBuild(BuildContext context) {
    return Container(
      color: Colors.white,
      padding: EdgeInsets.symmetric(horizontal: 38.w),
      child: Column(
        children: [
          _buildSlogon(),
          _buildLoginButton(),
          _buildOtherLoginButton(),
        ],
      ),
    );
  }

  _buildSlogon() {
    return Expanded(
      child: Center(
        child: Padding(
          padding: EdgeInsets.only(top: 44.h),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Image.asset(
                LoginAssets.loginAppIcon,
                width: 52.w,
                height: 52.w,
              ),
              SizedBox(
                width: 12.w,
              ),
              Text('随时随地记工\n数据永不丢失',
                  style: TextStyle(color: Color(0xFF8A8A99), fontSize: 16.sp))
            ],
          ),
        ),
      ),
    );
  }

  _buildLoginButton() {
    return GestureDetector(
      onTap: () {},
      child: Container(
        height: 44.h,
        alignment: Alignment.center,
        decoration: BoxDecoration(
          color: ColorsUtil.primaryColor,
          borderRadius: BorderRadius.circular(4.r),
        ),
        child: Obx(() => Text(lastLoginVM.lastLoginPhone.value,
            style: TextStyle(color: Colors.white, fontSize: 16.sp))),
      ),
    );
  }

  _buildOtherLoginButton() {
    return Expanded(
      child: GestureDetector(
        onTap: () {},
        child: Container(
          margin: EdgeInsets.only(top: 24.h),
          child: Text(
            '其他方式登录',
            style: TextStyle(color: Color(0xFF323233), fontSize: 16.sp),
          ),
        ),
      ),
    );
  }
}
