import 'package:collection/collection.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/generated/assets.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';
import 'package:get/get.dart';

import 'login_error_phone_us.dart';
import 'login_error_phone_vm.dart';

/*登错弹框*/
showLoginErrorPhoneDialog(List<RelatedPhoneInfoUS> relatedPhoneInfoList) {
  YPRoute.openDialog(
      clickMaskDismiss: false,
      builder: (context) => LoginErrorPhoneDialog(relatedPhoneInfoList));
}

class LoginErrorPhoneDialog extends StatelessWidget {
  LoginErrorPhoneDialog(this.relatedPhoneInfoList, {super.key});

  final LoginErrorPhoneVM vm = LoginErrorPhoneVM();
  final List<RelatedPhoneInfoUS> relatedPhoneInfoList;

  @override
  Widget build(BuildContext context) {
    return Container(
      height: double.infinity,
      margin: EdgeInsets.only(top: 40.h),
      alignment: Alignment.bottomCenter,
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(8.r)),
        ),
        padding: EdgeInsets.symmetric(horizontal: 16.w),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ..._buildTopContent(),
              ..._buildPhoneList(),
              ..._buildBottomContent(context)
            ],
          ),
        ),
      ),
    );
  }

  _buildTopContent() {
    return [
      SizedBox(height: 24.h),
      Text(
        '当前手机登录过多个账号',
        style: TextStyle(
            fontSize: 18.sp,
            fontWeight: FontWeight.bold,
            color: Color(0xD9000000)),
      ),
      SizedBox(height: 12.h),
      Text.rich(TextSpan(
          style: TextStyle(fontSize: 12.sp, color: Color(0xD9000000)),
          children: [
            TextSpan(text: '为避免'),
            TextSpan(
                text: '数据混乱或丢失', style: TextStyle(color: Color(0xFFFF0000))),
            TextSpan(text: '，请选择需要登录的账号'),
          ])),
    ];
  }

  _buildPhoneList() {
    buildHistoryPhoneItem(RelatedPhoneInfoUS relatedPhoneInfo,
        {bool recommend = false, bool newPhone = false}) {
      return Obx(() => GestureDetector(
            onTap: () {
              vm.selected(relatedPhoneInfo);
            },
            child: Stack(
              children: [
                Container(
                  margin: EdgeInsets.only(top: 4.h, bottom: 6.h),
                  decoration: BoxDecoration(
                    color: relatedPhoneInfo.selected.value
                        ? Color(0xFF5290FD)
                        : null,
                    border: Border.all(color: Color(0x1A5290FD)),
                    borderRadius: BorderRadius.circular(8.r),
                  ),
                  child: Padding(
                    padding: EdgeInsets.all(8.r),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '手机号：${relatedPhoneInfo.tel}',
                          style: TextStyle(
                              color: relatedPhoneInfo.selected.value
                                  ? Colors.white
                                  : Color(0xD9000000),
                              fontSize: 14.sp,
                              fontWeight: FontWeight.bold),
                        ),
                        SizedBox(height: 7.h),
                        newPhone
                            ? SizedBox(
                                width: double.infinity,
                                child: Text(
                                  '新手机号，没有记工数据',
                                  style: TextStyle(
                                      color: relatedPhoneInfo.selected.value
                                          ? Colors.white
                                          : Color(0xFF5290FD),
                                      fontSize: 12.sp),
                                ),
                              )
                            : Row(
                                children: [
                                  Text.rich(
                                    TextSpan(
                                        style: TextStyle(
                                            fontSize: 12.sp,
                                            color:
                                                relatedPhoneInfo.selected.value
                                                    ? Colors.white
                                                    : Color(0xA6000000)),
                                        children: [
                                          TextSpan(text: '累计记工'),
                                          TextSpan(
                                              text: relatedPhoneInfo.day,
                                              style: TextStyle(
                                                  color: relatedPhoneInfo
                                                          .selected.value
                                                      ? Colors.white
                                                      : Color(0xFF5290FD),
                                                  fontSize: 16.sp,
                                                  fontWeight: FontWeight.bold)),
                                          TextSpan(text: '天')
                                        ]),
                                  ),
                                  if (relatedPhoneInfo.lastTime.isNotEmpty)
                                    Expanded(
                                      child: Text(
                                        textAlign: TextAlign.end,
                                        '最后记工时间：${relatedPhoneInfo.lastTime}',
                                        style: TextStyle(
                                            fontSize: 12.sp,
                                            color:
                                                relatedPhoneInfo.selected.value
                                                    ? Colors.white
                                                    : Color(0xA6000000)),
                                      ),
                                    )
                                ],
                              )
                      ],
                    ),
                  ),
                ),
                if (recommend)
                  Positioned(
                      top: 0,
                      right: 0,
                      child: Image.asset(
                        Assets.loginImgItemDialogLoginErrorPhoneTop,
                        width: 152.w,
                        height: 29.h,
                      ))
              ],
            ),
          ));
    }

    return [
      SizedBox(height: 24.h),
      Text(
        '历史登录账号',
        style: TextStyle(fontSize: 16.sp, color: Color(0xA6000000)),
      ),
      SizedBox(height: 4.h),
      ...relatedPhoneInfoList.mapIndexed(
          (index, item) => buildHistoryPhoneItem(item, recommend: index == 0)),
      SizedBox(height: 30.h),
      Text(
        '当前登录账号',
        style: TextStyle(fontSize: 16.sp, color: Color(0xA6000000)),
      ),
      //cwltodo
      buildHistoryPhoneItem(RelatedPhoneInfoUS.newPhone('====='),
          newPhone: true)
    ];
  }

  _buildBottomContent(BuildContext context) {
    onConvertWork() {
      // cwltodo
    }
    return [
      SizedBox(height: 10.h),
      Text(
        '历史登录手机号已停止使用？',
        style: TextStyle(fontSize: 14.sp, color: Color(0xD9000000)),
      ),
      SizedBox(height: 8.h),
      Text.rich(
        TextSpan(
            style: TextStyle(fontSize: 14.sp, color: Color(0xA6000000)),
            children: [
              TextSpan(text: '支持登录新手机号，进入【更多】-【修改资料】-'),
              TextSpan(
                  text: '【转移记工信息】',
                  style: TextStyle(color: ColorsUtil.primaryColor),
                  recognizer: TapGestureRecognizer()..onTap = onConvertWork),
              TextSpan(text: '，互换新旧手机号记工记录，找回历史记工数据。'),
            ]),
      ),
      SizedBox(height: 80.h),
      GestureDetector(
        onTap: () => vm.handleLogin(),
        child: Obx(
          () => Container(
            width: double.infinity,
            height: 44.h,
            alignment: Alignment.center,
            decoration: BoxDecoration(
                color: Color(vm.btnEnable.value ? 0xFF5290FD : 0x80999999),
                border: Border.all(color: Color(0x1A5290FD)),
                borderRadius: BorderRadius.circular(22.r)),
            child: Text(
              '确认登录',
              style: TextStyle(color: Colors.white, fontSize: 18.sp),
            ),
          ),
        ),
      ),
      SizedBox(height: 40.h),
    ];
  }
}
