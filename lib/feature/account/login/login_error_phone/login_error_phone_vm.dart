import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/toast_util.dart';
import 'package:get/get.dart';

import 'login_error_phone_us.dart';

class LoginErrorPhoneVM {
  RelatedPhoneInfoUS? _selectedRelatedPhoneInfo;
  var btnEnable = false.obs;

  selected(RelatedPhoneInfoUS relatedPhoneInfo) {
    btnEnable.value = true;
    _selectedRelatedPhoneInfo?.selected.value = false;
    _selectedRelatedPhoneInfo = relatedPhoneInfo..selected.value = true;
  }

  handleLogin() {
    if (_selectedRelatedPhoneInfo == null) {
      ToastUtil.showToast('请选择登录账号');
      return;
    }
    if (_selectedRelatedPhoneInfo!.isNewPhone) {
      YPRoute.closeDialog();
      return;
    }
    //cwltodo 其他
    //cwltodo 跳转到登录,携带手机号过去,同时禁止掉页面的一键登录
  }
}
