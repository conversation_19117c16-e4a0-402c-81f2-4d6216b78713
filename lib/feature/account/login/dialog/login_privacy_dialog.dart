import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';

showLoginPrivacyDialog(VoidCallback onAgree) {
  YPRoute.openDialog(
    clickMaskDismiss: false,
    onBack: () => true,
    builder: (context) => LoginPrivacyDialog(onAgree),
  );
}

class LoginPrivacyDialog extends StatelessWidget {
  const LoginPrivacyDialog(this.onAgree, {super.key});

  final VoidCallback onAgree;

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 28.w),
      width: MediaQuery.of(context).size.width,
      decoration: BoxDecoration(
          color: Colors.white, borderRadius: BorderRadius.circular(8.r)),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(height: 24.h),
          _buildDialogTitle(),
          _buildDialogContent(),
          _buildDialogOperation(context)
        ],
      ),
    );
  }

  _buildDialogTitle() {
    return Text('服务协议及隐私保护',
        style: TextStyle(
            color: Color(0xFF323233),
            fontSize: 18.sp,
            fontWeight: FontWeight.bold));
  }

  _buildDialogContent() {
    onService() {
      // cwltodo
    }

    onPrivacy() {
      // cwltodo
    }

    final highLightStyle = TextStyle(
        color: ColorsUtil.ypPrimaryColor,
        decoration: TextDecoration.underline,
        decorationColor: ColorsUtil.ypPrimaryColor,
        height: 1);
    return Container(
      margin: EdgeInsets.fromLTRB(20.w, 8.h, 20.w, 24.h),
      constraints: BoxConstraints(minHeight: 96.h),
      child: Text.rich(
        TextSpan(
            style: TextStyle(fontSize: 16.sp, color: Color(0xFF8A8A99)),
            children: [
              TextSpan(text: '为了更好地保障您的合法权益，请您阅读并同意以下协议'),
              TextSpan(
                  text: '《服务协议》',
                  style: highLightStyle,
                  recognizer: TapGestureRecognizer()..onTap = onService),
              TextSpan(text: '、'),
              TextSpan(
                  text: '《隐私政策》',
                  style: highLightStyle,
                  recognizer: TapGestureRecognizer()..onTap = onPrivacy),
              TextSpan(text: '，未注册的手机号将自动完成账户注册'),
            ]),
        textAlign: TextAlign.center,
      ),
    );
  }

  _buildDialogOperation(BuildContext context) {
    dismiss() {
      YPRoute.closeDialog();
    }

    return Container(
      decoration: BoxDecoration(
          border:
              Border(top: BorderSide(color: Color(0xFFE6E6E6), width: 1.w))),
      child: SizedBox(
        height: 48.h,
        child: Row(
          children: [
            Expanded(
                child: GestureDetector(
              onTap: dismiss,
              child: Text(
                '不同意',
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: Color(0xFF323233),
                  fontSize: 18.sp,
                ),
              ),
            )),
            VerticalDivider(width: 1.w, color: Color(0xFFE6E6E6)),
            Expanded(
                child: GestureDetector(
              onTap: () {
                dismiss();
                onAgree();
              },
              child: Text(
                '同意并登录',
                textAlign: TextAlign.center,
                style: TextStyle(
                    color: ColorsUtil.ypPrimaryColor,
                    fontSize: 18.sp,
                    fontWeight: FontWeight.bold),
              ),
            )),
          ],
        ),
      ),
    );
  }
}
