import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/feature/account/transfer/view/TransferWorkConfirmDialogContent.dart';
import 'package:gdjg_pure_flutter/feature/account/transfer/vm/transfer_work_result_us.dart';
import 'package:gdjg_pure_flutter/feature/account/transfer/vm/transfer_work_vm.dart';
import 'package:gdjg_pure_flutter/utils/route_util/base_page/base_ful_page.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/button_util.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/common_dialog.dart';
import 'package:gdjg_pure_flutter/widget/countdown_button.dart';

class TransferWorkPage extends BaseFulPage {
  const TransferWorkPage({super.key})
      : super(appBar: const YPAppBar(title: '转移记工信息'));

  @override
  State createState() => _TransferWorkPageState();
}

class _TransferWorkPageState extends BaseFulPageState {
  final TransferWorkVM vm = TransferWorkVM();
  final TextEditingController _codeController = TextEditingController();
  final TextEditingController _phoneController = TextEditingController();
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _idCardController = TextEditingController();

  void transferCheck() {
    //cwltodo
    showCommonDialog(CommonDialogConfig(
        negative: '我再想想',
        positive: '确认更换',
        onPositive: () {},
        contentWidget: TransferWorkConfirmDialogContent(
            TransferWorkResultUS(oldNum: '12', curNum: '32'))));

    showCommonDialog(CommonDialogConfig(
        title: '验证失败', content: 'xxxx', hiddenNegative: true));
  }

  void transferResult() {
    showCommonDialog(CommonDialogConfig(
      title: '已为您完成记工信息互换',
      content: '请点击“我知道了”后，使用当前手机号重新登录APP，即可查看到旧手机号中的记工信息',
      positive: '我知道了',
      onPositive: () {},
    ));
  }

  @override
  Widget yBuild(BuildContext context) {
    return Container(
      color: Colors.white,
      child: Column(
        children: [
          Container(
            height: 8.h, // 分割线高度
            color: Color(0xFFF0F0F0), // 带背景颜色
          ),
          _buildContent(),
          Container(
            height: 1.h, // 分割线高度
            color: Color(0xFFE6E6E6), // 带背景颜色
          ),
          Padding(
            padding: EdgeInsets.only(left: 16.w, right: 16.w, top: 8.h),
            child: ButtonUtil.buildCommonButton(
                text: '转移记工',
                backgroundColor: Color(0xFF5290FD),
                onPressed: () {
                  vm.transfer(_codeController.text, _phoneController.text,
                      _nameController.text, _idCardController.text);
                }),
          ),
          SizedBox(height: 20.h)
        ],
      ),
    );
  }

  Widget _buildContent() {
    final topContent = [
      SizedBox(height: 36.h),
      Text(
        '你当前绑定手机的手机号码：',
        style: TextStyle(fontSize: 16.sp, color: Colors.black),
      ),
      Container(
        height: 34.h,
        margin: EdgeInsets.only(top: 8.h),
        alignment: Alignment.center,
        child: Text(
          //cwltodo
          '1231431414',
          style: TextStyle(fontSize: 24.sp, color: Color(0xFF5290FD)),
        ),
      ),
    ];

    final bottomContent = [
      Container(
        height: 35.h,
        margin: EdgeInsets.only(top: 24.h),
        alignment: Alignment.centerLeft,
        child: Text(
          '温馨提示',
          style: TextStyle(
              fontSize: 16.sp,
              color: Color(0xFF323233),
              fontWeight: FontWeight.bold),
        ),
      ),
      SizedBox(height: 4.h),
      Text(
        '1.用户验证身份证后，可将旧手机号中的记工信息，与当前正在使用的手机号中的记工信息，进行互换。\n2.不可将两个手机号中的记工信息合并到同一手机号中。\n3.同一个用户仅可更换一次记工信息。',
        style:
            TextStyle(fontSize: 14.sp, color: Color(0xD9000000), height: 1.4),
      ),
      SizedBox(height: 20.h)
    ];

    final formItem = [
      ..._buildFormItem(
          title: '验证码',
          hint: '请输入验证码',
          isCode: true,
          controller: _codeController,
          keyboardType: TextInputType.number,
          inputFormatters: [FilteringTextInputFormatter.digitsOnly],
          maxLength: 6),
      ..._buildFormItem(
          title: '旧手机号',
          hint: '请输入旧手机号',
          controller: _phoneController,
          keyboardType: TextInputType.number,
          inputFormatters: [FilteringTextInputFormatter.digitsOnly],
          maxLength: 11),
      ..._buildFormItem(
          title: '真实姓名', hint: '请输入真实姓名', controller: _nameController),
      ..._buildFormItem(
          title: '身份证号',
          hint: '请输入身份证号',
          maxLength: 18,
          controller: _idCardController)
    ];

    return Expanded(
        child: SingleChildScrollView(
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 16.w),
        child: Column(
          children: [...topContent, ...formItem, ...bottomContent],
        ),
      ),
    ));
  }

  List<Widget> _buildFormItem({
    required String title,
    required String hint,
    bool isCode = false,
    TextEditingController? controller,
    ValueChanged<String>? onChanged,
    TextInputType? keyboardType,
    List<TextInputFormatter>? inputFormatters,
    int? maxLength,
  }) {
    return [
      SizedBox(height: 24.h),
      SizedBox(
        width: double.infinity,
        child: Text(title,
            textAlign: TextAlign.start,
            style: TextStyle(color: Color(0xFF323233), fontSize: 14.sp)),
      ),
      SizedBox(height: 10.h),
      Row(
        children: [
          Expanded(
              child: TextField(
            onChanged: onChanged,
            maxLines: 1,
            keyboardType: keyboardType,
            maxLength: maxLength,
            inputFormatters: inputFormatters,
            controller: controller,
            style: TextStyle(
              color: Colors.black,
              fontSize: 17.sp,
            ),
            decoration: InputDecoration(
                isDense: true,
                contentPadding: EdgeInsets.zero,
                hintText: hint,
                hintStyle: TextStyle(color: Color(0xFFCCCCCC)),
                border: InputBorder.none,
                counter: SizedBox.shrink()),
          )),
          if (isCode)
            CountdownButton(
              textStyle:
                  TextStyle(fontSize: 15.sp, color: ColorsUtil.primaryColor),
              onPressed: (startCountDown) {
                vm.fetchCode().then((success) {
                  if (success) {
                    startCountDown();
                  }
                });
              },
            )
        ],
      ),
      Container(
        margin: EdgeInsets.only(top: 13.h),
        height: 1.h, // 分割线高度
        color: Color(0xFFF5F5F5), // 带背景颜色
      ),
    ];
  }
}
