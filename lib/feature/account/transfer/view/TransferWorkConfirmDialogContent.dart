import 'package:flutter/material.dart';
import 'package:gdjg_pure_flutter/feature/account/transfer/vm/transfer_work_result_us.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/common_dialog.dart';

class TransferWorkConfirmDialogContent extends StatelessWidget {
  const TransferWorkConfirmDialogContent(this.transferWorkResultUS,
      {super.key});

  final TransferWorkResultUS transferWorkResultUS;

  @override
  Widget build(BuildContext context) {
    return Text.rich(
        TextSpan(style: CommonDialogConfig.defaultContentStyle, children: [
      TextSpan(text: '旧手机号：  共记工'),
      TextSpan(
          text: '${transferWorkResultUS.oldNum}天',
          style: CommonDialogConfig.defaultContentStyle
              .copyWith(color: ColorsUtil.primaryColor)),
      TextSpan(text: '\n当前手机号：  共记工'),
      TextSpan(
          text: '${transferWorkResultUS.curNum}天',
          style: CommonDialogConfig.defaultContentStyle
              .copyWith(color: ColorsUtil.primaryColor)),
    ]));
  }
}
