import 'package:gdjg_pure_flutter/utils/regex/regex_utils.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/toast_util.dart';

class TransferWorkVM {
  void transfer(String code, String oldPhone, String name, String idCard) {
    if(code.isEmpty){
      ToastUtil.showToast('请输入验证码');
      return;
    }
    if(oldPhone.isEmpty){
      ToastUtil.showToast('请输入旧手机号');
      return;
    }
    if(RegexUtils.isMobile(oldPhone)){
      ToastUtil.showToast('请输入正确的旧手机号');
      return;
    }
    if(name.isEmpty){
      ToastUtil.showToast('请输入真实姓名');
      return;
    }
    if(idCard.isEmpty){
      ToastUtil.showToast('请输入身份证号');
      return;
    }
  }

  Future<bool> fetchCode() async{
    //cwltodo
    return false;
  }
}
