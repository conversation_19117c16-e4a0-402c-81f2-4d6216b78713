import 'package:get/get.dart';

class HistoryPhoneUS {
  final _phone = ''.obs;

  String get phone => _phone.value;

  void setCurrentPhone(String phone) {
    _phone.value = phone;
  }
}

class HistoryPhoneListUS {
  final _historyPhones = <HistoryPhoneUS>[].obs;
  final _isFinishInit = false.obs;

  RxList<HistoryPhoneUS> get historyPhones => _historyPhones;

  bool get isFinishInit => _isFinishInit.value;

  void setIsFinishInit(bool isFinishInit) {
    _isFinishInit.value = isFinishInit;
  }

  void setHistoryPhones(List<HistoryPhoneUS> historyPhones) {
    _historyPhones.value = historyPhones;
  }
}
