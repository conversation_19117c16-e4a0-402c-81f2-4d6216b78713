import 'package:gdjg_pure_flutter/data/account/repo/auth_repo.dart';
import 'package:gdjg_pure_flutter/feature/account_manage/vm/account_manage_us.dart';

class AccountManageVM {
  final _authRepo = AuthRepo();
  final us = AccountManageUS();

  /// 获取用户当前手机号
  void fetchCurrentPhone() {
    us.setCurrentPhone(_authRepo.getAccount().tel);
  }

  /// 绑定手机号点击事件
  void onBindPhoneTap(){}

  /// 登录过的手机号点击事件
  void onHistoryPhoneTap(){}

  /// 切换账号点击事件
  void onSwitchAccountTap(){}

  /// 退出登录点击事件
  void onLogoutTap() async {
    // final result = await YPRoute.showConfirmDialog(
    //   title: "退出登录",
    //   content: "确认退出登录吗？",
    // );

    // if (result == true) {
    //   // 调用退出登录接口
    //   _authRepo.logout();
    //   // 跳转到登录页面
    //   YPRoute.openPage(RouteNameCollection.login, closeNumBeforeOpen: 1);
    // }
  }
}
