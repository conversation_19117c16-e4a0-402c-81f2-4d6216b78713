import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/data/account/repo/auth_repo.dart';
import 'package:gdjg_pure_flutter/feature/account_manage/vm/account_manage_vm.dart';
import 'package:gdjg_pure_flutter/iconfont/iconfont.dart';
import 'package:gdjg_pure_flutter/utils/route_util/base_page/base_ful_page.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';

/// 账号管理页面
class AccountManagePage extends BaseFulPage {
  const AccountManagePage({super.key})
      : super(appBar: const YPAppBar(title: "账号管理"));

  @override
  State<AccountManagePage> createState() => _AccountManagePageState();
}

class _AccountManagePageState extends BaseFulPageState<AccountManagePage> {
  final AccountManageVM _vm = AccountManageVM();

  @override
  void onPageCreate() {
    super.onPageCreate();
    _vm.fetchCurrentPhone();
  }

  @override
  Widget yBuild(BuildContext context) {
    return Container(
      color: ColorsUtil.ypBgColor,
      child: Column(
        children: [
          // 分割线
          SizedBox(height: 8.h),

          // 手机号相关Cell
          _buildPhoneCells(),

          // 分割线
          SizedBox(height: 8.h),

          // 切换账号按钮
          _buildSwitchAccountButton(),

          // 分割线
          SizedBox(height: 8.h),

          // 退出登录按钮
          _buildLogoutButton(),
        ],
      ),
    );
  }

  // 构建手机号相关的Cell
  Widget _buildPhoneCells() {
    return Container(
      color: Colors.white,
      child: Column(
        children: [
          // 绑定手机号
          _buildCell(
            left: "绑定手机号",
            right: _vm.us.currentPhone,
            onTap: () => _vm.onBindPhoneTap(),
          ),
          // 登录过的手机号
          _buildCell(
            left: "登录过的手机号",
            right: "",
            onTap: () => _vm.onHistoryPhoneTap(),
          ),
        ],
      ),
    );
  }

  // 构建单个Cell
  Widget _buildCell({
    required String left,
    required String right,
    VoidCallback? onTap,
  }) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.all(16),
        decoration: BoxDecoration(
          border: Border(
            bottom: BorderSide(
              color: ColorsUtil.ypGreyColor,
              width: 0.5,
            ),
          ),
        ),
        child: Row(
          children: [
            Text(
              left,
              style: TextStyle(
                fontSize: 17.sp,
                color: Color(0xFF333333),
              ),
            ),
            const Spacer(),
            Text(
              right,
              style: TextStyle(
                fontSize: 17.sp,
                color: ColorsUtil.black65,
              ),
            ),
            SizedBox(width: 4.w),
            IconFont(
              IconNames.saasArrowRight,
              size: 17,
              color: "#8a8a99",
            ),
          ],
        ),
      ),
    );
  }

  // 构建切换账号按钮
  Widget _buildSwitchAccountButton() {
    return _buildButton(
      text: "切换账号",
      textColor: ColorsUtil.primaryColor,
      onTap: () => _vm.onSwitchAccountTap(),
    );
  }

  // 构建退出登录按钮
  Widget _buildLogoutButton() {
    return _buildButton(
      text: "退出登录",
      textColor: const Color(0xFFF54A45),
      onTap: ()=>_vm.onLogoutTap(),
    );
  }

  // 通用按钮构建方法
  Widget _buildButton({
    required String text,
    required Color textColor,
    VoidCallback? onTap,
  }) {
    return InkWell(
      onTap: onTap,
      child: Container(
        width: double.infinity,
        height: 57.h,
        color: Colors.white,
        alignment: Alignment.center,
        child: Text(
          text,
          style: TextStyle(
            fontSize: 17.sp,
            color: textColor,
          ),
        ),
      ),
    );
  }
}
