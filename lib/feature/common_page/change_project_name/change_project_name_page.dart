import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/generated/assets.dart';
import 'package:gdjg_pure_flutter/utils/route_util/base_page/base_ful_page.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:get/get.dart';

import 'vm/change_project_name_vm.dart';

class ChangeProjectNameProps {
  final String? deptId;
  final String? currentName;

  ChangeProjectNameProps({this.deptId, this.currentName});
}

class ChangeProjectNamePage extends BaseFulPage {
  const ChangeProjectNamePage({super.key}) : super(appBar: null);

  @override
  State createState() => _ChangeProjectNamePageState();
}

class _ChangeProjectNamePageState extends BaseFulPageState {
  late ChangeProjectNameProps? props;
  final ChangeProjectNameVM viewModel = ChangeProjectNameVM();
  final TextEditingController _textController = TextEditingController();
  final FocusNode _focusNode = FocusNode();

  @override
  void onPageRoute(Object? routeParams, bool fromLaunchTask) {
    super.onPageRoute(routeParams, fromLaunchTask);
    props = routeParams as ChangeProjectNameProps?;
    viewModel.init(props?.deptId, props?.currentName);
    
    // 设置输入框初始值
    if (props?.currentName != null) {
      _textController.text = props!.currentName!;
      // 将光标移到末尾
      _textController.selection = TextSelection.fromPosition(
        TextPosition(offset: _textController.text.length),
      );
    }
  }

  @override
  void onPageShow() {
    super.onPageShow();
    // 自动获取焦点
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _focusNode.requestFocus();
    });
  }

  @override
  void dispose() {
    _textController.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget yBuild(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F6FA),
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back_ios,
            color: const Color(0xFF333333),
            size: 20.w,
          ),
          onPressed: () => YPRoute.closePage(),
        ),
        title: Text(
          '修改项目名称',
          style: TextStyle(
            color: const Color(0xFF333333),
            fontSize: 20.sp,
            fontWeight: FontWeight.w600,
          ),
        ),
        centerTitle: false,
        titleSpacing: -10,
        actions: [
          Obx(() => GestureDetector(
            onTap: viewModel.us.canSave ? viewModel.saveProjectName : null,
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 6.h),
              decoration: BoxDecoration(
                color: viewModel.us.canSave
                    ? const Color(0xFF5290FD)
                    : const Color(0xFFE5E5E5),
                borderRadius: BorderRadius.circular(4.r),
              ),
              child: Text(
                '保存',
                style: TextStyle(
                  color: viewModel.us.canSave
                      ? Colors.white
                      : const Color(0xFF999999),
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          )),
          SizedBox(width: 16.w),
        ],
      ),
      body: _buildContent(),
    );
  }

  Widget _buildContent() {
    return Container(
      color: Colors.white,
      margin: EdgeInsets.only(top: 8.h),
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
      child: Column(
        children: [
          Row(
            children: [
              Text(
                '项目名称',
                style: TextStyle(
                  fontSize: 18.sp,
                  color: const Color(0xFF222222),
                  fontWeight: FontWeight.w400,
                ),
              ),
              SizedBox(width: 16.w),
              Expanded(
                child: TextField(
                  controller: _textController,
                  focusNode: _focusNode,
                  onChanged: viewModel.onProjectNameChanged,
                  decoration: InputDecoration(
                    hintText: '请输入项目名称',
                    hintStyle: TextStyle(
                      fontSize: 16.sp,
                      color: const Color(0xFF999999),
                    ),
                    border: InputBorder.none,
                    enabledBorder: InputBorder.none,
                    focusedBorder: InputBorder.none,
                    contentPadding: EdgeInsets.zero,
                  ),
                  style: TextStyle(
                    fontSize: 16.sp,
                    color: const Color(0xFF333333),
                  ),
                  maxLength: 20,
                  buildCounter: (context, {required currentLength, required isFocused, maxLength}) {
                    return const SizedBox.shrink();
                  },
                ),
              ),
              Obx(() => viewModel.us.projectName.isNotEmpty
                  ? GestureDetector(
                      onTap: () {
                        _textController.clear();
                        viewModel.clearProjectName();
                      },
                      child: Container(
                        margin: EdgeInsets.only(left: 8.w),
                        child: Image.asset(
                          Assets.commonIconRoleClose,
                          width: 16.w,
                          height: 16.w,
                        ),
                      ),
                    )
                  : const SizedBox.shrink()),
            ],
          ),
          SizedBox(height: 5.h),
          Container(
            height: 1.h,
            color: const Color(0xFFE5E5E5),
          ),
        ],
      ),
    );
  }
}
