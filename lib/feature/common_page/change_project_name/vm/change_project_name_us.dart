import 'package:get/get.dart';

/// 修改项目名称UI状态管理
class ChangeProjectNameUS {
  /// 项目名称输入内容
  final _projectName = ''.obs;

  /// 初始项目名称
  final _initialProjectName = ''.obs;

  // Getters
  String get projectName => _projectName.value;
  String get initialProjectName => _initialProjectName.value;

  /// 是否可以保存
  bool get canSave => _projectName.value.trim() != _initialProjectName.value.trim();

  /// 设置项目名称
  void setProjectName(String name) {
    _projectName.value = name;
  }

  /// 设置初始项目名称
  void setInitialProjectName(String name) {
    _initialProjectName.value = name;
  }
}
