import 'package:gdjg_pure_flutter/data/worker_data/worker_project_data/ds/param/dept_update_name_param_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/worker_project_data/repo/worker_project_repo.dart';
import 'package:gdjg_pure_flutter/data/worker_data/corp_select/repo/corp_select_repo.dart';
import 'package:gdjg_pure_flutter/data/worker_data/corp_select/ds/param/corp_select_param_model.dart';
import 'package:gdjg_pure_flutter/utils/event_bus_util/event_bus_util.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/toast_util.dart';
import 'package:gdjg_pure_flutter/utils/system_util/string_util.dart';

import 'change_project_name_us.dart';

/// 修改项目名称ViewModel
class ChangeProjectNameVM {
  final _workerProjectRep = WorkerProjectRepo();
  final _corpSelectRepo = CorpSelectRepo();
  final us = ChangeProjectNameUS();

  String? _deptId;

  /// 初始化数据
  void init(String? deptId, String? currentName) {
    _deptId = deptId;
    // 获取项目详情和切换企业
    _fetchProjectDetailAndSwitchCorp();
  }

  /// 保存项目名称
  void saveProjectName() async {
    final projectName = us.projectName.trim();

    // 验证输入
    if (projectName.isEmpty) {
      ToastUtil.showToast('输入的项目名称不能为空');
      return;
    }

    if (_deptId == null || _deptId!.isEmpty) {
      ToastUtil.showToast('项目信息错误');
      return;
    }

    final param = DeptUpdateDeptNameParamModel(
      dept_id: _deptId!,
      name: projectName,
    );

    final result = await _workerProjectRep.updateDeptName(param);

    if (result.isOK()) {
      ToastUtil.showToast('修改成功！');

      // 发送项目状态变更事件
      EventBusUtil.emit<String>('project_update');
      YPRoute.closePage();
    } else {
      ToastUtil.showToast('修改失败：${result.fail?.errorMsg ?? "未知错误"}');
    }
  }

  /// 项目名称输入变化
  void onProjectNameChanged(String value) {
    us.setProjectName(value);
  }

  /// 清空项目名称
  void clearProjectName() {
    us.setProjectName('');
  }

  /// 获取项目详情和企业切换
  void _fetchProjectDetailAndSwitchCorp() async {
    if (_deptId == null || _deptId!.isEmpty) {
      return;
    }

    // 获取项目详情
    final deptDetailResult = await _workerProjectRep.getDeptDetail(_deptId!);

    if (deptDetailResult.isOK()) {
      final deptData = deptDetailResult.getSucData();
      if (deptData != null) {
        // 设置项目名称
        us.setInitialProjectName(deptData.name);
        us.setProjectName(deptData.name);

        // 企业切换
        if (deptData.corpId > 0) {
          final corpSelectParam = CorpSelectParamModel(
            corp_id: deptData.corpId.trimTrailingZeros(),
          );

          await _corpSelectRepo.fetchCorpSelect(corpSelectParam);
        }
      }
    }
  }
}
