import 'package:get/get.dart';

/// 点工UI状态管理 - 纯状态管理层
class PointWorkUS {
  // UI状态变量
  /// 当前选中的工时类型索引 (0:1个工, 1:半个工, 2:选小时, 3:休息, 4:上下午)
  final _selectedWorkTimeIndex = 0.obs;

  /// 工作小时数 (用于"1个工"类型，默认8小时)
  final _workHours = 8.0.obs;

  /// "1个工"按钮显示的文本 (如"1个工"、"2个工"等)
  final _workHoursButtonText = '1个工'.obs;

  /// 选择的具体小时数 (用于"选小时"类型)
  final _selectedHours = 0.0.obs;

  /// "选小时"按钮显示的文本 (如"选小时"、"8小时"等)
  final _selectedHoursButtonText = '选小时'.obs;

  /// 是否有加班 (true表示有加班)
  final _hasOvertime = false.obs;

  /// 加班小时数
  final _overtimeHours = 0.0.obs;

  /// 计算得出的工资金额
  final _salary = 300.0.obs;

  /// 备注信息
  final _remark = ''.obs;

  /// 照片URL列表
  final _photoUrls = <String>[].obs;

  /// 当前选中的时间段索引 (0:上午, 1:下午) - 用于"上下午"工时类型
  final _selectedTimeSlotIndex = 0.obs;

  // Getter方法
  int get selectedWorkTimeIndex => _selectedWorkTimeIndex.value;
  double get workHours => _workHours.value;
  String get workHoursButtonText => _workHoursButtonText.value;
  double get selectedHours => _selectedHours.value;
  String get selectedHoursButtonText => _selectedHoursButtonText.value;
  bool get hasOvertime => _hasOvertime.value;
  double get overtimeHours => _overtimeHours.value;
  double get salary => _salary.value;
  String get remark => _remark.value;
  List<String> get photoUrls => _photoUrls.toList();
  int get selectedTimeSlotIndex => _selectedTimeSlotIndex.value;

  // 常量列表
  /// 工时类型按钮文本列表 (对应_selectedWorkTimeIndex的各个索引值)
  final List<String> workTimeButtonList = ['1个工', '半个工', '选小时', '休息', '上下午'];

  /// 时间段选项列表 (对应_selectedTimeSlotIndex的各个索引值)
  final List<String> timeSlotList = ['上午', '下午'];

  // 基础状态设置方法
  void setSelectedWorkTimeIndex(int index) {
    _selectedWorkTimeIndex.value = index;
  }

  void setTimeSlot(int index) {
    _selectedTimeSlotIndex.value = index;
  }

  void setWorkHours(double hours) {
    _workHours.value = hours;
  }

  void setWorkHoursButtonText(String text) {
    _workHoursButtonText.value = text;
  }

  void setSelectedHours(double hours) {
    _selectedHours.value = hours;
  }

  void setSelectedHoursButtonText(String text) {
    _selectedHoursButtonText.value = text;
  }

  void setOvertime(bool overtime) {
    _hasOvertime.value = overtime;
  }

  void setOvertimeHours(double hours) {
    _overtimeHours.value = hours;
  }

  void setSalary(double salary) {
    _salary.value = salary;
  }

  void setRemark(String text) {
    _remark.value = text;
  }

  void addPhoto(String photoUrl) {
    _photoUrls.add(photoUrl);
  }

  void removePhoto(int index) {
    if (index >= 0 && index < _photoUrls.length) {
      _photoUrls.removeAt(index);
    }
  }

  // 重置所有状态到默认值
  void reset() {
    _selectedWorkTimeIndex.value = 0;
    _workHours.value = 8.0;
    _workHoursButtonText.value = '1个工';
    _selectedHours.value = 0.0;
    _selectedHoursButtonText.value = '选小时';
    _hasOvertime.value = false;
    _overtimeHours.value = 0.0;
    _salary.value = 300.0;
    _remark.value = '';
    _photoUrls.clear();
    _selectedTimeSlotIndex.value = 0;
  }
}
