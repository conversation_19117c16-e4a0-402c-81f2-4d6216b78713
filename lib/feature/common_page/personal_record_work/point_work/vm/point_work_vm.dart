import 'package:flutter/material.dart';
import 'package:gdjg_pure_flutter/feature/common_page/personal_record_work/point_work/vm/point_work_us.dart';
import 'package:gdjg_pure_flutter/widget/work_hours_keyboard_dialog.dart';
import 'package:gdjg_pure_flutter/widget/hour_keyboard_dialog.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';

/// 点工ViewModel
class PointWorkVM {
  final us = PointWorkUS();

  PointWorkVM() {
    _calculateSalary();
  }

  /// 选择工作时长按钮
  void selectWorkTimeButton(int index) {
    us.setSelectedWorkTimeIndex(index);

    // 重置工时相关状态到默认值
    _resetWorkTimeStates();

    // 重新计算工资
    _calculateSalary();
  }

  /// 设置工作小时
  void setWorkHours(double hours) {
    us.setWorkHours(hours);

    // 更新1个工按钮显示文本
    String buttonText;
    if (hours == hours.toInt()) {
      buttonText = '${hours.toInt()}个工';
    } else {
      buttonText = '$hours个工';
    }
    us.setWorkHoursButtonText(buttonText);

    _calculateSalary();
  }

  /// 设置选择的小时数
  void setSelectedHours(double hours) {
    us.setSelectedHours(hours);

    // 更新选小时按钮显示文本
    String buttonText;
    if (hours == hours.toInt()) {
      buttonText = '${hours.toInt()}小时';
    } else {
      buttonText = '$hours小时';
    }
    us.setSelectedHoursButtonText(buttonText);

    _calculateSalary();
  }

  /// 切换加班状态
  void toggleOvertime() {
    final newOvertimeState = !us.hasOvertime;
    us.setOvertime(newOvertimeState);

    // 如果取消加班，重置加班小时数
    if (!newOvertimeState) {
      us.setOvertimeHours(0.0);
    }

    _calculateSalary();
  }

  /// 设置加班小时
  void setOvertimeHours(double hours) {
    us.setOvertimeHours(hours);
    _calculateSalary();
  }

  /// 删除照片
  void deletePhoto(int index) {
    us.removePhoto(index);
  }

  /// 输入备注
  void inputRemark(String text) {
    us.setRemark(text);
  }

  /// 确认提交
  void confirmSubmit() {
    // TODO: 实现提交逻辑
  }


  /// 重置工时相关状态到默认值
  void _resetWorkTimeStates() {
    us.setWorkHours(8.0);
    us.setWorkHoursButtonText('1个工');
    us.setSelectedHours(0.0);
    us.setSelectedHoursButtonText('选小时');
  }

  /// 计算工资
  void _calculateSalary() {

  }

  // UI交互

  /// 显示工时时长键盘（1个工按钮点击）
  void showWorkHoursKeyboard(BuildContext context) {
    YPRoute.openDialog(
      builder: (context) => WorkHoursKeyboardDialog(
        onConfirm: (value) {
          setWorkHours(value);
        },
      ),
      alignment: Alignment.bottomCenter,
    );
  }

  /// 显示小时选择弹窗（选小时按钮点击）
  void showHourSelectionDialog(BuildContext context) {
    YPRoute.openDialog(
      builder: (context) => HourKeyboardDialog(
        onConfirm: (value) {
          setSelectedHours(value);
        },
      ),
      alignment: Alignment.bottomCenter,
    );
  }

  /// 显示上下午选择弹窗
  void showTimeSlotDialog() {
    // TODO: 实现上下午选择弹窗
  }

  /// 显示加班选择弹窗
  void showOvertimeDialog() {
    // TODO: 实现加班选择弹窗
  }

  /// 显示工资计算说明弹窗
  void showSalaryCalculationDialog() {
    // TODO: 实现工资计算说明弹窗
  }

  /// 显示照片选择弹窗
  void showPhotoSelectionDialog() {
    // TODO: 实现照片选择弹窗
  }

  /// 拍照
  void takePhoto() {
    // TODO: 实现拍照逻辑
  }

  /// 从相册选择
  void pickFromGallery() {
    // TODO: 实现从相册选择逻辑
  }
}
