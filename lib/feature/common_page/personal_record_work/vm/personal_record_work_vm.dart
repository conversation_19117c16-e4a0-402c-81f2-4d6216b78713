import 'package:get/get.dart';

/// 个人记工ViewModel
class PersonalRecordWorkVM {
  double? _deptId;

  final _date = ''.obs;
  final _projectId = ''.obs;

  String get date => _date.value;
  String get projectId => _projectId.value;

  final List<String> workTypes = ['点工', '包工', '短工', '工量', '其他费用'];

  PersonalRecordWorkVM({double? deptId}) {
    _deptId = deptId;
    _initData();
  }

  void _initData() {
    // 设置默认日期为今天
    final now = DateTime.now();
    final dateStr = '${now.year}年${now.month.toString().padLeft(2, '0')}月${now.day.toString().padLeft(2, '0')}日';
    _date.value = dateStr;
  }

  /// 选择日期
  void selectDate() {
    // TODO: 实现日期选择逻辑
  }

  /// 选择项目
  void selectProject() {
    // TODO: 实现项目选择逻辑
  }

  /// 选择工种
  void selectWorkType(int index) {
    // 工种选择逻辑由各个tab自己处理
  }

  /// 设置日期
  void setDate(String date) {
    _date.value = date;
  }

  /// 设置项目
  void setProjectId(String projectId) {
    _projectId.value = projectId;
  }

  /// 释放资源
  void dispose() {
    // 清理资源
  }
}
