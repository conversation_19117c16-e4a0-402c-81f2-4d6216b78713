import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 短工Tab页面
class ShortWorkTab extends StatefulWidget {
  const ShortWorkTab({super.key});

  @override
  State<ShortWorkTab> createState() => _ShortWorkTabState();
}

class _ShortWorkTabState extends State<ShortWorkTab> with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context); // 必须调用以保持页面状态
    return SingleChildScrollView(
      child: Column(
        children: [
          Container(
            width: double.infinity,
            padding: EdgeInsets.all(16.w),
            color: Colors.white,
            child: Text(
              '短工内容区域',
              style: TextStyle(fontSize: 16.sp, color: Colors.grey),
            ),
          ),
          SizedBox(height: 20.h),
        ],
      ),
    );
  }
}
