import 'package:flutter/material.dart';
import 'package:gdjg_pure_flutter/feature/common_page/notes/vm/notes_viewmodel.dart';
import 'package:gdjg_pure_flutter/utils/route_util/base_page/base_ful_page.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';
import 'package:get/get.dart';

import 'entity/notes_props.dart';

/// @date 2025/07/07
/// @param props 页面路由参数
/// @returns
/// @description Notes页面入口
class NotesPage extends BaseFulPage {
  const NotesPage({super.key}) : super(appBar: const YPAppBar(title: "备注"));

  @override
  State<StatefulWidget> createState() {
    return _NotesPageState();
  }
}

class _NotesPageState extends BaseFulPageState<NotesPage> {
  final NotesViewModel viewModel = NotesViewModel();
  NotesProps? props;

  @override
  void onPageRoute(Object? routeParams, bool fromLaunchTask) {
    super.onPageRoute(routeParams, fromLaunchTask);
    props = routeParams as NotesProps?;
    // 初始化数据
    viewModel.initData(props?.pageSource ?? NotesPageSource.other);
  }

  @override
  Object? onSend2Previous() {
    final result = viewModel.confirmNoteTap();
    if (result != null) {
      return result;
    }
    return null;
  }

  @override
  Widget yBuild(BuildContext context) {
    return Container(
      color: ColorsUtil.ypBgColor,
      child: contentView(),
    );
  }

  /// 实际展示的视图，需要处理：
  /// 1. 画出UI
  /// 2. 绑定UIState
  /// 3. 处理事件监听
  /// 4. 向VM传递来自View层的事件
  Widget contentView() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 输入框区域
          _buildInputSection(),
          const SizedBox(height: 20),
          // 确认按钮
          _buildConfirmButton(),
          const SizedBox(height: 24),
          // 历史备注标题
          Padding(
            padding: const EdgeInsets.only(left: 15),
            child: Row(
              children: [
                Text(
                  '历史备注',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: Colors.black87,
                  ),
                ),
                Obx(
                  () => Text(
                    "(${viewModel.us.historyNotes.length})",
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: Color(0xFF9d9db3),
                    ),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 10),
          // 历史备注列表
          Expanded(child: _buildHistoryList()),
        ],
      ),
    );
  }

  /// 构建输入框区域
  Widget _buildInputSection() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: Color(0xFFF0F0F0),
        borderRadius: BorderRadius.circular(4),
      ),
      child: TextField(
        autofocus: true,
        controller: viewModel.textController,
        maxLines: 7,
        maxLength: 200,
        decoration: InputDecoration(
          border: InputBorder.none,
          hintText: '请输入备注',
          hintStyle: const TextStyle(fontSize: 14, color: Color(0xFF808080)),
          counter: Obx(() => SizedBox(
                width: double.infinity,
                child: Row(
                  children: [
                    Text(
                      '${viewModel.us.count}/200',
                      style: TextStyle(
                        fontSize: 14,
                        color: ColorsUtil.black35,
                      ),
                    ),
                    const Spacer(),
                    GestureDetector(
                      onTap: () => viewModel.clearCurrentNote(),
                      child: Text(
                        '清空内容',
                        style: TextStyle(
                            fontSize: 14, color: ColorsUtil.primaryColor),
                      ),
                    ),
                  ],
                ),
              )),
        ),
      ),
    );
  }

  /// 构建确认按钮
  Widget _buildConfirmButton() {
    return SizedBox(
      width: double.infinity,
      height: 48,
      child: ElevatedButton(
        onPressed: () {
          YPRoute.closePage(onSend2Previous());
          // final result = viewModel.confirmNoteTap();
          // if (result != null) {
          //
          // }
        },
        style: ElevatedButton.styleFrom(
          backgroundColor: ColorsUtil.primaryColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(4),
          ),
        ),
        child: const Text(
          '确认',
          style: TextStyle(
            fontSize: 16,
            color: Colors.white,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }

  /// 构建历史备注列表
  Widget _buildHistoryList() {
    return Obx(() {
      final historyNotes = viewModel.us.historyNotes;
      return ListView.builder(
        itemCount: historyNotes.length,
        itemBuilder: (context, index) {
          return _buildHistoryItem(historyNotes[index]);
        },
      );
    });
  }

  /// 构建历史备注项
  Widget _buildHistoryItem(String note) {
    return Container(
      margin: const EdgeInsets.only(bottom: 10),
      alignment: Alignment.centerLeft,
      child: IntrinsicWidth(
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
          decoration: BoxDecoration(
            color: ColorsUtil.inputBgColor,
            borderRadius: BorderRadius.circular(4),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Flexible(
                child: GestureDetector(
                  onTap: () => viewModel.onHistoryNoteClick(note),
                  child: Text(
                    note,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    style: const TextStyle(
                      fontSize: 14,
                      color: Colors.black87,
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 8),
              GestureDetector(
                onTap: () => viewModel.removeHistoryNote(note),
                child: const Icon(
                  Icons.close,
                  size: 18,
                  color: Colors.grey,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
