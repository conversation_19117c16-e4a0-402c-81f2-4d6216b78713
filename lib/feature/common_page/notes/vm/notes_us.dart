import 'package:get/get.dart';

/// @date 2025/07/07
/// @description Notes页UI状态
class NotesUS extends GetxController {
  // 当前输入的备注内容
  final _currentNote = ''.obs;

  String get currentNote => _currentNote.value;

  // 历史备注列表
  final  _historyNotes = <String>[].obs;

  get historyNotes => _historyNotes.value;

  // 字符计数
  final _characterCount = 0.obs;

  get count => _characterCount.value;

  // 最大字符数限制
  static const int maxCharacters = 200;

  /// 更新当前备注内容
  void updateCurrentNote(String note) {
    _currentNote.value = note;
    _characterCount.value = note.length;
  }

  /// 清空当前备注内容
  void clearCurrentNote() {
    _currentNote.value = '';
    _characterCount.value = 0;
  }

  /// 更新历史备注列表
  void updateHistoryNotes(List<String> notes) {
    _historyNotes.value = notes;
    _historyNotes.refresh();
  }

  /// 删除历史备注
  void removeHistoryNote(String note) {
    _historyNotes.remove(note);
    _historyNotes.refresh();
  }

  /// 清空历史备注
  void clearHistoryNotes() {
    _historyNotes.clear();
    _historyNotes.refresh();
  }
}
