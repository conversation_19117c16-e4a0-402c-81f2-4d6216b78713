
import "package:gdjg_pure_flutter/data/notes_data/repo/notes_repo.dart";
import "package:gdjg_pure_flutter/feature/common_page/notes/entity/notes_props.dart";
import "package:get/get.dart";
import "package:flutter/material.dart";
import "notes_us.dart";

/// @date 2025/07/07
/// @description Notes页ViewModel
/// 1. 处理数据的加载状态，如loading和错误视图等
/// 2. 观测UIRep中的业务实体转为UIState
/// 3. 接受来自view的意图并分发事件进行处理（暂无）
/// 4. 对View层暴露事件（操作视图和处理一些与系统权限交互等场景）
class NotesViewModel {
  var us = NotesUS();

  final _notesRepo = NotesRepo();
  final TextEditingController textController = TextEditingController();

  var _pageSource = NotesPageSource.other.value;

  NotesViewModel() {
    // 监听文本输入变化
    textController.addListener(() {
      us.updateCurrentNote(textController.text);
    });
  }

  /// 初始化数据，加载历史备注
  void initData(NotesPageSource source) {
    _pageSource = source.value;
    _notesRepo.setPageSource(_pageSource);
    fetchData();
  }

  /// 初始化数据同步，加载历史备注
  void fetchData() async {
    try {
      final historyNotes = _notesRepo.getHistoryNotes();
      us.updateHistoryNotes(historyNotes);
    } finally {
    }
  }

  /// 确认保存备注
  String? confirmNoteTap() {
    final note = textController.text.trim();
    if (note.isEmpty) return null;
    // 保存到本地
    _notesRepo.saveNote(note);
    return note;
  }

  /// 清空当前输入
  void clearCurrentNote() {
    textController.clear();
    us.clearCurrentNote();
  }

  /// 点击历史备注项
  void onHistoryNoteClick(String note) {
    textController.text = note;
    us.updateCurrentNote(note);
  }

  void dispose() {
    textController.dispose();
  }

  void removeHistoryNote(String note) {
    _notesRepo.removeHistoryNote(note);
    us.removeHistoryNote(note);
  }
}
