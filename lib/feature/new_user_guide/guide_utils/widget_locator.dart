import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';

/// 组件定位工具
/// 用于获取UI组件在屏幕中的位置和尺寸
class WidgetLocator {
  final BuildContext _context; // 上下文

  WidgetLocator(this._context);

  // 获取指定GlobalKey对应组件的位置和尺寸
  Rect? getWidgetRect(GlobalKey? key) {
    final context = key?.currentContext;
    if (context == null || !context.mounted) return null;

    try {
      final renderObject = context.findRenderObject();
      if (renderObject == null) return null;

      if (renderObject is RenderBox) {
        return _getRenderBoxRect(renderObject);
      }

      if (renderObject is RenderSliverToBoxAdapter) {
        return _getSliverRect(renderObject);
      }

      final bounds = renderObject.paintBounds;
      final transform = renderObject.getTransformTo(null);
      return MatrixUtils.transformRect(transform, bounds);
    } catch (e) {
      // 可选：记录日志
      // debugPrint("WidgetLocator.getWidgetRect error: $e");
      return null;
    }
  }

  Rect? _getRenderBoxRect(RenderBox renderBox) {
    final position = renderBox.localToGlobal(Offset.zero);
    return Rect.fromLTWH(
      position.dx,
      position.dy,
      renderBox.size.width,
      renderBox.size.height,
    );
  }

  Rect? _getSliverRect(RenderSliverToBoxAdapter renderObject) {
    final child = renderObject.child;
    if (child == null) return null;

    final viewport = RenderAbstractViewport.maybeOf(renderObject);
    if (viewport == null) return null;

    final offset = viewport.getOffsetToReveal(renderObject, 0.0).offset;
    final childPosition = child.localToGlobal(Offset(0, -offset));
    if (child.size.isEmpty) return null;

    return Rect.fromLTWH(
      childPosition.dx,
      childPosition.dy,
      child.size.width,
      child.size.height,
    );
  }

  // 计算安全区域内的镂空区域（用于引导蒙层）
  Rect calculateHoleRect(Rect targetRect) {
    final mediaQuery = MediaQuery.of(_context);
    final screenSize = mediaQuery.size;
    final padding = mediaQuery.padding;

    final safeTop = padding.top;
    final safeBottom = screenSize.height - padding.bottom;

    // 确保目标矩形在屏幕可视区域内
    final clampedTop = targetRect.top.clamp(safeTop, safeBottom - targetRect.height);
    final clampedBottom = targetRect.bottom.clamp(safeTop, safeBottom);
    final holeHeight = clampedBottom - clampedTop;

    return Rect.fromLTWH(0, clampedTop, screenSize.width, holeHeight);
  }
}
