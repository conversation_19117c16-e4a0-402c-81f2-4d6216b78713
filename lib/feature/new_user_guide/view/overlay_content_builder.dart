import 'package:flutter/material.dart';
import 'package:gdjg_pure_flutter/widget/calendar/calendar_day.dart';
import 'hollow_rect_view.dart';
import 'pass_through_view.dart';
import 'triangle_painter_view.dart';
import '../config/guide_step_ui_model.dart';
import '../guide_utils/widget_locator.dart';

/// 浮层内容构建器
/// 负责构建引导浮层的UI内容
class OverlayContentBuilder {
  final BuildContext _context; // 上下文

  OverlayContentBuilder(this._context);

  // 构建完整的引导浮层内容
  Widget build({
    required Rect targetRect, // 目标组件区域
    required Rect holeRect, // 镂空区域
    required GuideStepUIModel stepConfig, // 步骤配置
    required bool isVoiceOn, // 语音状态
    required VoidCallback onNextStep, // 下一步回调
    required Function(String) onToggleVoice, // 切换语音回调
  }) {
    final screenSize = MediaQuery.of(_context).size;
    const cardHorizontalMargin = 16.0;
    final cardWidth = screenSize.width - 2 * cardHorizontalMargin;

    // 获取今天日期的位置（用于日历步骤的手指动画）
    final fingerRect =
        WidgetLocator(_context).getWidgetRect(CalendarDay.todayKey);

    return PassThroughView(
      passThroughAreas: stepConfig.shouldPassThrough ? [holeRect] : [],
      // onPassThroughTap: () {
      //   _removeOverlay(); // 点击穿透区域后关闭蒙层
      // },
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          _buildMask(holeRect), // 半透明蒙层（带镂空）
          // if (fingerRect != null) _buildFingerAnimation(fingerRect), // 手指动画
          // 引导卡片
          _buildHoleTapArea(holeRect, stepConfig, onNextStep), // 处理镂空区域点击穿透
          _buildGuideCard(
            holeRect: holeRect,
            stepConfig: stepConfig,
            cardWidth: cardWidth,
            cardHorizontalMargin: cardHorizontalMargin,
            isVoiceOn: isVoiceOn,
            onNextStep: onNextStep,
            onToggleVoice: onToggleVoice,
          ),
        ],
      ),
    );
  }

  // 构建半透明蒙层（带镂空效果）
  Widget _buildMask(Rect holeRect) {
    return SizedBox(
      width: double.infinity,
      height: double.infinity,
      child: CustomPaint(
        painter: HollowRectView(holeRect: holeRect),
      ),
    );
  }

  // 构建手指动画（引导用户点击）
  Widget _buildFingerAnimation(Rect rect) {
    return Positioned(
      left: rect.left - 10,
      top: rect.top + 10,
      child: Image.asset(
        'assets/images/worker/finger_run.gif',
        width: 50,
        height: 50,
        fit: BoxFit.contain,
      ),
    );
  }

  // 构建镂空区域的点击处理
  Widget _buildHoleTapArea(
    Rect holeRect,
    GuideStepUIModel stepConfig,
    VoidCallback onNextStep,
  ) {
    return Positioned.fromRect(
      rect: holeRect,
      child: stepConfig.shouldPassThrough
          ? Container() // 允许穿透时不处理点击
          : GestureDetector(
              onTap: onNextStep,
              behavior: HitTestBehavior.opaque,
              child: Container(color: Colors.transparent),
            ),
    );
  }

  // 构建引导卡片（包含标题、语音控制和下一步按钮）
  Widget _buildGuideCard({
    required Rect holeRect,
    required GuideStepUIModel stepConfig,
    required double cardWidth,
    required double cardHorizontalMargin,
    required bool isVoiceOn,
    required VoidCallback onNextStep,
    required Function(String) onToggleVoice,
  }) {
    const triangleSize = 24.0;

    return Positioned(
      top: holeRect.top + holeRect.height + 20, // 卡片位于目标组件下方
      left: cardHorizontalMargin,
      right: cardHorizontalMargin * 3,
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          _buildTriangleIndicator(triangleSize), // 倒三角指示器
          Container(
            width: cardWidth,
            padding: const EdgeInsets.fromLTRB(12, 15, 12, 12),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildVoiceAndStepRow(
                  // 语音控制和步骤指示器
                  stepConfig: stepConfig,
                  isVoiceOn: isVoiceOn,
                  onToggleVoice: onToggleVoice,
                ),
                const SizedBox(height: 6),
                Text(
                  // 引导标题
                  stepConfig.title,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w400,
                    color: Colors.black87,
                  ),
                ),
                const SizedBox(height: 2),
                const Text(
                  '(点击小喇叭，可关闭语音)', // 辅助文本
                  style: TextStyle(fontSize: 14, color: Colors.black87),
                ),
                const SizedBox(height: 12),
                _buildNextButton(stepConfig, onNextStep), // 下一步按钮
              ],
            ),
          ),
        ],
      ),
    );
  }

  // 构建倒三角指示器（指向目标组件）
  Widget _buildTriangleIndicator(double size) {
    return Positioned(
      left: 20,
      top: -size / 2 + 2,
      child: CustomPaint(
        size: Size(size * 1.5, size),
        painter: TrianglePainterView(color: Colors.white),
      ),
    );
  }

  // 构建语音控制和步骤指示器行
  Widget _buildVoiceAndStepRow({
    required GuideStepUIModel stepConfig,
    required bool isVoiceOn,
    required Function(String) onToggleVoice,
  }) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        GestureDetector(
          // 语音控制按钮
          onTap: () => onToggleVoice(stepConfig.voicePath),
          child: Image.asset(
            isVoiceOn
                ? 'assets/images/worker/waa_ic_guide_voice_on.gif' // 语音开启图标
                : 'assets/images/worker/waa_ic_guide_voice_off.webp', // 语音关闭图标
            height: 20,
          ),
        ),
        Text(
          // 步骤指示器
          stepConfig.step,
          style: const TextStyle(fontSize: 14, color: Colors.grey),
        ),
      ],
    );
  }

  // 构建下一步按钮
  Widget _buildNextButton(GuideStepUIModel config, VoidCallback onNext) {
    return Align(
      alignment: Alignment.centerRight,
      child: ElevatedButton(
        onPressed: onNext,
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFF5290FD),
          foregroundColor: Colors.white,
          elevation: 0,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(4),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 18, vertical: 6),
          minimumSize: Size(30, 30),
          // 设置最小尺寸
          maximumSize: Size(double.infinity, 30), // 设置最大高度
        ),
        child: Text(config.isFinish ? '开始记工' : '下一步'),
      ),
    );
  }
}
