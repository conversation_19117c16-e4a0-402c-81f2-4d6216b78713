import 'package:flutter/material.dart';

import '../config/guide_step_ui_model.dart';
import 'new_user_guided_overlay_view.dart';

/// 抽离蒙层管理逻辑到单独的类（支持外部控制显示状态）
/// 该类负责管理新用户引导蒙层的显示逻辑和状态
class NewUserGuidedOverlayManager {
  /// 引导步骤列表，直接通过构造函数传入
  final List<GuideTargetUIModel>? guideSteps;

  /// 引导完成时的回调函数
  // final VoidCallback? onFinish;
  final Function(bool isSuccess)? onFinish;
  /// 外部引导步骤构建器，允许动态生成引导步骤列表
  final List<GuideTargetUIModel>? Function()? externalGuideStepsBuilder;

  /// 显示状态，由外部控制，通过setter更新
  bool _showGuide = false;

  /// 获取当前是否应该显示引导蒙层
  bool get shouldShowGuide => _showGuide;

  /// 获取最终的引导步骤列表
  /// 优先级：外部构建器 > 构造函数直接传入的步骤 > 空列表
  List<GuideTargetUIModel> get effectiveGuideSteps {
    // 优先使用外部传入的构建器
    if (externalGuideStepsBuilder != null) {
      final steps = externalGuideStepsBuilder?.call();
      if (steps != null && steps.isNotEmpty) return steps;
    }
    // 其次使用构造函数直接传入的步骤
    if (guideSteps != null && guideSteps!.isNotEmpty) return guideSteps!;

    // 默认返回空列表
    return [];
  }

  /// 构造函数
  /// [guideSteps] 直接传入的引导步骤列表
  /// [onFinish] 引导完成时的回调
  /// [externalGuideStepsBuilder] 外部引导步骤构建器
  /// [initialShow] 初始化时的显示状态，默认为false
  NewUserGuidedOverlayManager({
    this.guideSteps,
    this.onFinish,
    this.externalGuideStepsBuilder,
    // 允许初始化时传入初始显示状态（可选）
    bool initialShow = false,
  }) : _showGuide = initialShow;

  /// 提供外部更新显示状态的方法
  /// [show] 新的显示状态
  void updateShowState(bool show) {
    _showGuide = show;
  }

  /// 完成引导流程
  /// 1. 设置显示状态为false
  /// 2. 触发完成回调
  void finishGuide(bool isSuccess) {
    _showGuide = false;
    onFinish?.call(isSuccess);
  }

  /// 构建引导蒙层组件
  /// 返回 [NewUserGuidedOverlayView] 组件实例
  Widget buildOverlay() {
    final steps = effectiveGuideSteps;
    return NewUserGuidedOverlayView(
      steps: steps,
      onFinish: finishGuide, role: UserRoleState.worker,
    );
  }
}
