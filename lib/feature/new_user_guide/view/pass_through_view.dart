import 'package:flutter/foundation.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/rendering.dart';

/// 点击穿透区域构建器
/// 提供从GlobalKey创建穿透区域的工具方法
class PassThroughAreaBuilder {
  /// 从GlobalKey列表创建穿透区域
  /// [keys] 指定需要穿透的组件Key列表
  /// [padding] 穿透区域的额外扩展边距
  static List<Rect> fromKeys(List<GlobalKey> keys, {double padding = 0}) {
    return keys.map((key) {
      final renderBox = key.currentContext?.findRenderObject() as RenderBox?;
      return renderBox != null
          ? renderBox.localToGlobal(Offset.zero) & renderBox.size
          : null;
    }).whereType<Rect>().map((r) => r.inflate(padding)).toList();
  }
}

/// 点击穿透覆盖层组件
/// 允许指定区域的事件穿透到下层Widget，常用于引导蒙层等场景
class PassThroughView extends StatelessWidget {
  /// 覆盖层内容组件
  final Widget child;

  /// 需穿透点击事件的区域列表
  final List<Rect> passThroughAreas;

  /// 创建点击穿透覆盖层
  const PassThroughView({
    super.key,
    required this.child,
    required this.passThroughAreas,
  });

  /// 使用PassThroughAreaBuilder的工厂方法创建穿透区域
  factory PassThroughView.fromKeys({
    required Widget child,
    required List<GlobalKey> keys,
    double padding = 0,
  }) {
    return PassThroughView(
      child: child,
      passThroughAreas: PassThroughAreaBuilder.fromKeys(keys, padding: padding),
    );
  }

  @override
  Widget build(BuildContext context) {
    // 使用自定义RenderObject处理事件穿透逻辑
    return _PassThroughOverlayRenderObjectWidget(
      passThroughAreas: passThroughAreas,
      child: child,
    );
  }
}

/// 处理点击穿透逻辑的RenderObjectWidget
class _PassThroughOverlayRenderObjectWidget
    extends SingleChildRenderObjectWidget {
  final List<Rect> passThroughAreas;

  const _PassThroughOverlayRenderObjectWidget({
    required this.passThroughAreas,
    required Widget super.child,
  });

  @override
  RenderObject createRenderObject(BuildContext context) {
    return _RenderPassThroughOverlay(passThroughAreas: passThroughAreas);
  }

  @override
  void updateRenderObject(
      BuildContext context, covariant _RenderPassThroughOverlay renderObject) {
    renderObject.passThroughAreas = passThroughAreas;
  }
}

/// 核心渲染对象，实现事件穿透逻辑
class _RenderPassThroughOverlay extends RenderProxyBox {
  /// 不可变的穿透区域列表
  List<Rect> _passThroughAreas;

  _RenderPassThroughOverlay({required List<Rect> passThroughAreas})
      : _passThroughAreas = List.unmodifiable(passThroughAreas),
        super();

  /// 设置穿透区域
  /// 使用列表内容比较而非引用比较，避免不必要的重绘
  set passThroughAreas(List<Rect> value) {
    if (listEquals(_passThroughAreas, value)) return;
    _passThroughAreas = List.unmodifiable(value);
    markNeedsPaint();
  }

  @override
  bool hitTest(BoxHitTestResult result, {required Offset position}) {
    // 快速判断：若无穿透区域或点击位置超出边界，使用默认命中逻辑
    if (_passThroughAreas.isEmpty || !size.contains(position)) {
      return super.hitTest(result, position: position);
    }

    // 检查点击位置是否在穿透区域内
    for (final area in _passThroughAreas) {
      if (area.contains(position)) {
        // 穿透区域内的点击事件不被当前RenderObject处理
        return false;
      }
    }

    // 非穿透区域使用默认命中逻辑
    return super.hitTest(result, position: position);
  }

  @override
  bool hitTestSelf(Offset position) {
    // 检查自身命中时同样忽略穿透区域
    for (final area in _passThroughAreas) {
      if (area.contains(position)) {
        return false;
      }
    }
    return super.hitTestSelf(position);
  }

  @override
  void handleEvent(PointerEvent event, BoxHitTestEntry entry) {
    // 可在此处添加基于事件类型的穿透逻辑
    // 例如：仅穿透点击事件，保留滚动事件
    super.handleEvent(event, entry);
  }
}