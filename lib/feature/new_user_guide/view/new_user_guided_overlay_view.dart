import 'package:flutter/material.dart';
import '../config/guide_step_ui_model.dart';
import '../guide_utils/widget_locator.dart';
import '../sounds/voice_player_manager.dart';
import 'overlay_content_builder.dart';
import '../config/step_config_provider.dart';

/// 新用户引导浮层组件
/// 负责管理引导流程的显示和控制
class NewUserGuidedOverlayView extends StatefulWidget {
  final List<GuideTargetUIModel> steps; // 引导步骤列表
  final UserRoleState role; // 用户角色（班组长/工人）
  final Function(bool isSuccess)? onFinish; // 引导完成回调

  const NewUserGuidedOverlayView({
    super.key,
    required this.steps,
    required this.role,
    required this.onFinish,
  });

  @override
  NewUserGuideOverlayState createState() => NewUserGuideOverlayState();
}

class NewUserGuideOverlayState extends State<NewUserGuidedOverlayView> {
  int _currentStep = 0; // 当前引导步骤索引
  bool _isOverlayVisible = false; // 浮层是否可见
  late OverlayEntry _overlayEntry; // 浮层内容
  late StepConfigProvider _stepConfigProvider; // 步骤配置提供者
  bool _hasPlayedCurrentVoice = false; // 当前步骤语音是否已播放
  late VoicePlayerManager _voiceManager;

  @override
  void initState() {
    super.initState();
    // 初始化语音播放器和步骤配置
    _initVoiceManager();
    _stepConfigProvider = StepConfigProvider(role: widget.role);
    // 在UI渲染完成后显示引导浮层
    WidgetsBinding.instance.addPostFrameCallback((_) => _showOverlay());
  }

  Future<void> _initVoiceManager() async {
    _voiceManager = VoicePlayerManager();
    try {
      _voiceManager.initialized;
    } catch (e) {
      print("====语音==语音初始化失败：${_voiceManager.lastError}");
    }
  }

  @override
  void dispose() {
    // 清理资源
    _removeOverlay();
    _voiceManager.stop();
    super.dispose();
  }

  // 显示引导浮层
  void _showOverlay() {
    if (_currentStep >= widget.steps.length) {
      widget.onFinish?.call(true); // 所有步骤完成
      return;
    }

    _overlayEntry = OverlayEntry(
      builder: (context) => _buildOverlayContent(context),
    );
    Overlay.of(context).insert(_overlayEntry);
    _isOverlayVisible = true;
  }

  // 移除引导浮层
  void _removeOverlay() {
    if (_isOverlayVisible) {
      _overlayEntry.remove();
      _isOverlayVisible = false;
    }
  }

  // 构建浮层内容
  Widget _buildOverlayContent(BuildContext context) {
    //获取当前步骤
    final currentTarget = widget.steps[_currentStep];
    // 使用定位工具获取目标组件位置
    final locator = WidgetLocator(context);
    // 获取指定GlobalKey对应组件的位置和尺寸
    final targetRect = locator.getWidgetRect(currentTarget.globalKey);
    // 获取当前步骤配置
    final stepConfig = _stepConfigProvider.getConfig(currentTarget.stepType);
    // 定位失败处理
    if (targetRect == null || stepConfig == null) {
      WidgetsBinding.instance.addPostFrameCallback(
        (_) => widget.onFinish?.call(false),
      );
      return Container();
    }

    // 计算安全区域内的镂空区域
    final holeRect = locator.calculateHoleRect(targetRect);

    // 自动播放语音（首次渲染且语音开启时）
    if (!_hasPlayedCurrentVoice && _voiceManager.isVoiceOn) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _voiceManager.play(stepConfig.voicePath);
      });
      _hasPlayedCurrentVoice = true;
    }

    // 使用内容构建器创建浮层UI
    return OverlayContentBuilder(context).build(
      targetRect: targetRect,
      holeRect: holeRect,
      stepConfig: stepConfig,
      isVoiceOn: _voiceManager.isVoiceOn,
      onNextStep: _nextStep,
      onToggleVoice: (path) => _updateVoiceStatus(path),
    );
  }

  // 切换语音状态
  void _updateVoiceStatus(String path) {
    setState(() => _voiceManager.updateVoiceStatus());
    _voiceManager.play(path);
    _overlayEntry.markNeedsBuild(); // 强制刷新浮层
  }

  // 进入下一步引导
  void _nextStep() {
    setState(() {
      _currentStep++;
      _hasPlayedCurrentVoice = false; // 重置语音状态
    });

    if (_currentStep < widget.steps.length) {
      _overlayEntry.markNeedsBuild(); // 刷新浮层内容
    } else {
      _removeOverlay(); // 移除浮层
      widget.onFinish?.call(true); // 通知引导完成
    }
  }

  @override
  Widget build(BuildContext context) => Container();
}
