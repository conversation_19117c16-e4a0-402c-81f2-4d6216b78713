import 'package:flutter/cupertino.dart';

/// 自定义三角形绘制器 - 绘制一个尖角朝上的三角形
class TrianglePainterView extends CustomPainter {
  final Color color;

  TrianglePainterView({required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    // 定义三角形路径（尖角朝上）
    final path = Path()
      ..moveTo(size.width / 2, 0) // 顶部尖角
      ..lineTo(0, size.height) // 左下点
      ..lineTo(size.width, size.height) // 右下点
      ..close();

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}