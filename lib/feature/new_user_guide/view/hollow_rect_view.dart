import 'package:flutter/cupertino.dart';

/// 自定义绘制器
/// 创建半透明蒙层和全屏宽度的镂空效果
class HollowRectView extends CustomPainter {
  final Rect holeRect; // 镂空区域矩形（全屏宽度）

  HollowRectView({required this.holeRect});

  @override
  void paint(Canvas canvas, Size size) {
    //创建40%透明度黑色画笔 (256*40%)
    final paint = Paint()
      ..color = Color.fromARGB(100, 0, 0, 0)
      ..style = PaintingStyle.fill;
    // 创建整个屏幕大小的矩形
    final fullScreenRect = Rect.fromLTWH(0, 0, size.width, size.height);
    // 创建路径，先添加整个屏幕矩形
    final path = Path()..addRect(fullScreenRect);
    // 从路径中减去全屏宽度的镂空区域
    path.addRect(holeRect);
    path.fillType = PathFillType.evenOdd;
    // 绘制路径，实现全屏宽度的镂空效果
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
