import 'dart:isolate';
import 'dart:async';

import 'package:get/get.dart';
import '../config/guide_step_ui_model.dart';
import 'audio_player_utils.dart';

class VoicePlayerManager extends GetxController {
  // 可观察状态
  final _isVoiceOn = true.obs;
  final _state = PlayerState.idle.obs;
  final _lastError = Rx<String?>(null);

  // Isolate相关
  late SendPort _commandSendPort;
  bool _isolateReady = false;
  final _initCompleter = Completer<void>();

  // 音频播放器实例
  final _audioPlayer = AudioPlayerUtil();

  // 对外暴露的 getter
  bool get isVoiceOn => _isVoiceOn.value;

  PlayerState get currentState => _state.value;

  String? get lastError => _lastError.value;

  Future<void> get waitUntilInitialized => _initCompleter.future;

  VoicePlayerManager() {
    _initIsolate();
  }

// 初始化Isolate（用于在后台线程处理指令）
  void _initIsolate() {
    // 创建一个接收端口，用于接收来自Isolate的消息
    final receivePort = ReceivePort();

    // 监听接收端口的消息
    receivePort.listen((message) {
      // 当收到的消息是SendPort类型时
      if (message is SendPort) {
        // 保存这个SendPort，用于后续向Isolate发送指令
        _commandSendPort = message;
        // 标记Isolate已准备好接收指令
        _isolateReady = true;
        // 完成初始化Future（仅在未完成时执行）
        if (!_initCompleter.isCompleted) {
          _initCompleter.complete();
        }
      }
      // 当收到的消息是Map类型时（即指令消息）
      else if (message is Map) {
        // 处理从Isolate转发回来的指令
        _handleCommandFromIsolate(message);
      }
    });

    // 创建并启动一个新的Isolate，执行_entryPoint函数
    // 同时将当前的receivePort.sendPort传递给新Isolate
    Isolate.spawn(_commandIsolateEntryPoint, receivePort.sendPort)
        .catchError((error) {
      // 处理Isolate创建失败的情况
      _setError('Isolate 初始化失败: $error');
      // 以错误状态完成初始化Future（仅在未完成时执行）
      if (!_initCompleter.isCompleted) {
        _initCompleter.completeError(error);
      }
    });
  }

  // Isolate入口
  static void _commandIsolateEntryPoint(SendPort mainSendPort) {
    final receivePort = ReceivePort();
    mainSendPort.send(receivePort.sendPort);

    receivePort.listen((message) {
      if (message is Map) {
        mainSendPort.send(message);
      }
    });
  }

  // 处理Isolate转发的指令
  void _handleCommandFromIsolate(Map<dynamic, dynamic> message) async {
    try {
      switch (message['command'] as String?) {
        case 'play':
          final path = message['path'] as String?;
          if (path == null) {
            _setError('play 指令缺少有效的 path');
            return;
          }
          final success = await _audioPlayer.play(path);
          _state.value = success ? PlayerState.playing : PlayerState.error;
          break;

        case 'stop':
          await _audioPlayer.stop();
          _state.value = PlayerState.stopped;
          break;
        default:
          _setError('收到未知指令: ${message['command']}');
      }
    } catch (e) {
      _setError('处理指令失败: $e');
    }
  }

  // 切换语音开关状态
  bool updateVoiceStatus() {
    _isVoiceOn.value = !_isVoiceOn.value;
    if (!_isVoiceOn.value) {
      stop();
    }
    return _isVoiceOn.value;
  }

  // 播放音频
  Future<void> play(String path) async => _sendCommand('play', path);

  // 停止播放
  Future<void> stop() async => _sendCommand('stop');

  // 统一处理指令发送
  Future<void> _sendCommand(String command, [String? path]) async {
    await waitUntilInitialized;
    if (!_isVoiceOn.value) {
      _setError('语音已关闭');
      return;
    }
    if (!_isolateReady) {
      _setError('Isolate 未就绪');
      return;
    }
    try {
      _commandSendPort.send({'command': command, 'path': path});
    } catch (e) {
      _setError('发送$command指令失败: $e');
    }
  }

  // 统一处理错误状态
  void _setError(String message) {
    _lastError.value = message;
    _state.value = PlayerState.error;
  }

  @override
  void onClose() {
    _audioPlayer.dispose();
    _state.close();
    _isVoiceOn.close();
    _lastError.close();
    if (_isolateReady) {
      _commandSendPort.send({'command': 'dispose'});
    }
    super.onClose();
  }
}
