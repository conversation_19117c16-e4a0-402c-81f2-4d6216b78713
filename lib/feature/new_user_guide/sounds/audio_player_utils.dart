import 'package:audioplayers/audioplayers.dart';
import 'package:flutter/cupertino.dart';
import 'package:synchronized/synchronized.dart';

/// 音频播放工具类
class AudioPlayerUtil {
  static final AudioPlayerUtil _instance = AudioPlayerUtil._internal();

  factory AudioPlayerUtil() => _instance;

  AudioPlayerUtil._internal();

  // 播放器实例
  AudioPlayer? _player;
  AudioCache? _audioCache;

  // 状态标记
  bool _isInitializing = false;
  bool _isDisposed = false;
  String? _lastError;
  final _lock = Lock();

  // 获取错误信息
  String? get lastError => _lastError;

  /// 初始化播放器
  Future<void> _initIfNeeded() async {
    // 如果正在初始化或已释放，等待/返回
    while (_isInitializing) {
      await Future.delayed(Duration(milliseconds: 100));
    }

    if (_isDisposed) {
      throw StateError('播放器已被释放，无法重新初始化');
    }

    if (_player != null) return;

    await _lock.synchronized(() async {
      if (_player != null) return; // 双重检查

      _isInitializing = true;
      try {
        // 创建播放器和缓存
        _player = AudioPlayer();
        _audioCache = AudioCache(prefix: 'assets/sounds/');

        // 监听播放器状态变化
        _player!.onPlayerStateChanged.listen((state) {
          debugPrint('语音-- 播放器状态: $state');
        });

        // 监听播放器完成事件
        _player!.onPlayerComplete.listen((event) {
          debugPrint('语音-- 播放完成');
        });

        debugPrint('播放器初始化完成');
      } catch (e) {
        _lastError = '初始化失败: $e';
        debugPrint('语音--初始化异常: $e');
        rethrow;
      } finally {
        _isInitializing = false;
      }
    });
  }

  /// 播放音频
  Future<bool> play(String assetPath, {int maxRetries = 2}) async {
    if (_isDisposed) {
      _lastError = '语音--播放器已被释放';
      return false;
    }

    await _initIfNeeded();
    if (_player == null) {
      _lastError = '语音--播放器初始化失败';
      return false;
    }

    int retryCount = 0;
    while (retryCount <= maxRetries) {
      try {
        return await _playInternal(assetPath);
      } catch (e) {
        retryCount++;
        if (retryCount > maxRetries) {
          _lastError = '播放失败（已重试$maxRetries次）: $e';
          debugPrint('语音--最终播放失败: $e');
          return false;
        }

        // 指数退避重试策略
        await Future.delayed(Duration(milliseconds: 200 * (retryCount + 1)));
        debugPrint('语音-- 重试播放 ($retryCount/$maxRetries): $assetPath');
      }
    }

    return false;
  }

  /// 内部播放逻辑
  Future<bool> _playInternal(String assetPath) async {
    return _lock.synchronized(() async {
      try {
        // 停止当前播放
        if (_player!.state == PlayerState.playing) {
          await _player!.stop();
        }

        // 加载音频
        final audioFile = await _audioCache!.load(assetPath);
        debugPrint('语音-- 加载音频: $assetPath (${audioFile.path})');

        // 设置源并播放
        await _player!.setSource(DeviceFileSource(audioFile.path));
        await _player!.resume();

        debugPrint('语音-- 开始播放: $assetPath');
        return true;
      } catch (e) {
        _lastError = '播放失败: $e';
        debugPrint('语音-- 播放异常: $e');
        rethrow;
      }
    });
  }

  /// 停止播放
  Future<void> stop() async {
    if (_player == null || _isDisposed) return;

    try {
      await _player!.stop();
      debugPrint('语音-- 播放已停止');
    } catch (e) {
      _lastError = '停止失败: $e';
      debugPrint('语音-- 停止异常: $e');
    }
  }

  /// 释放资源（仅在应用退出时调用）
  Future<void> dispose() async {
    if (_player == null || _isDisposed) return;

    await _lock.synchronized(() async {
      try {
        await _player!.stop();
        await _player!.dispose();
        _player = null;
        _isDisposed = true;
        debugPrint('语音-- 播放器已释放');
      } catch (e) {
        _lastError = '释放失败: $e';
        debugPrint('语音-- 释放异常: $e');
      }
    });
  }
}
