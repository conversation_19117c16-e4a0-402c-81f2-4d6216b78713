// 混入：支持外部动态控制蒙层显示
import 'package:flutter/cupertino.dart';

import 'config/guide_step_ui_model.dart';
import 'view/new_user_guided_overlay_manager.dart';
import 'view/new_user_guided_overlay_view.dart';

/// 新用户引导蒙层混入类
/// 用于为StatefulWidget提供新用户引导蒙层的管理能力
mixin NewUserGuideMixin<T extends StatefulWidget> on State<T> {
  /// 引导蒙层管理器实例
  late NewUserGuidedOverlayManager guideManager;

  /// 外部传入的引导步骤列表
  List<GuideTargetUIModel>? externalSteps;

  /// 子类必须实现此方法以提供引导步骤
  /// @return 引导步骤列表
  List<GuideTargetUIModel> getGuideSteps();

  /// 引导完成时的回调方法
  /// 子类可以重写此方法处理引导完成后的逻辑
  void onGuideFinished(bool isSuccess) {}

  /// 设置外部引导步骤
  /// @param steps 外部提供的引导步骤列表
  void setExternalGuideSteps(List<GuideTargetUIModel> steps) {
    externalSteps = steps;
    // // 如果蒙层已显示，重新构建
    // if (guideManager.shouldShowGuide) {
    //   updateGuideShowState(true);
    // }
  }

  /// 更新引导蒙层的显示状态
  /// @param show 是否显示引导蒙层
  void updateGuideShowState(bool show) {
    if (mounted && show) {
      setState(() {
        guideManager.updateShowState(show);
      });
    } else {
      print("====检查是否还有步骤需要引导=false===");
    }
  }

  @override
  void initState() {
    super.initState();
    // 初始化引导蒙层管理器
    guideManager = NewUserGuidedOverlayManager(
      guideSteps: getGuideSteps(), // 使用子类实现的引导步骤
      externalGuideStepsBuilder: () => externalSteps, // 外部步骤构建器
      onFinish: (bool isSuccess) {
        if (mounted) {
          setState(() {
            onGuideFinished(isSuccess); // 引导完成回调，传递是否成功的状态
          });
        }
      },
      // 初始状态默认不显示，等待外部参数
      initialShow: false,
    );
  }

  /// 构建包含引导蒙层的widget树
  /// @param child 原始子widget
  /// @return 添加了引导蒙层的widget树
  Widget buildWithGuideOverlay(Widget child) {
    return Stack(
      children: [
        child, // 原始内容
        // 仅当满足以下条件时显示蒙层：
        // 1. 管理器指示应显示蒙层
        // 2. 存在有效的引导步骤
        if (guideManager.shouldShowGuide &&
            guideManager.effectiveGuideSteps.isNotEmpty)
          guideManager.buildOverlay()
      ],
    );
  }
  /// 关闭引导蒙层
  void closeGuideOverlay() {
    if (mounted) {
      setState(() {
        guideManager.updateShowState(false); // 隐藏蒙层
        // 或者直接调用移除逻辑（取决于管理器的实现）
        // guideManager.removeOverlay();
      });
    }
  }
}