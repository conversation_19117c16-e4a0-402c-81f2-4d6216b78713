import 'guide_step_ui_model.dart';

/// 步骤配置提供者
/// 根据用户角色提供对应的引导步骤配置
class StepConfigProvider {
  final UserRoleState role; // 用户角色

  StepConfigProvider({required this.role});

  // 根据步骤类型获取配置
  GuideStepUIModel? getConfig(GuideStepType type) {
    return role == UserRoleState.boss ? _bossConfigs[type] : _workerConfigs[type];
  }

  // 班组长引导配置
  static const Map<GuideStepType, GuideStepUIModel> _bossConfigs = {
    GuideStepType.guideCalender: GuideStepUIModel(
      title: '点击日历上的日期，开始为工友记工',
      step: '1/5',
      voicePath: "waa_guide_calendar",
    ),
    GuideStepType.guideRecordBar: GuideStepUIModel(
      title: '选择合适的记工方式',
      step: '2/5',
      voicePath: "waa_guide_select_type.MP3",
    ),
    GuideStepType.guideMember: GuideStepUIModel(
      title: '请添加需要被记工的工友',
      step: '3/5',
      voicePath: "waa_guide_add_worker.MP3",
    ),
    GuideStepType.guideAddMember: GuideStepUIModel(
      title: '请选择以上两个方法添加工友',
      step: '4/5',
      voicePath: "waa_guide_add_way.MP3",
    ),
    GuideStepType.guideWorkTime: GuideStepUIModel(
      title: '选择工作时长，默认选择一个工',
      step: '5/5',
      voicePath: "waa_guide_select_record_time.MP3",
      isFinish: true,
    ),
  };

  // 普通工人引导配置
  static const Map<GuideStepType, GuideStepUIModel> _workerConfigs = {
    GuideStepType.guideCalender: GuideStepUIModel(
        title: '点击日历上的日期，开始第一笔记工',
        step: '1/3',
        voicePath: "waa_guide_calendar_personal.MP3",
        shouldPassThrough: true),
    GuideStepType.guideRecordBar: GuideStepUIModel(
      title: '选择合适的记工方式',
      step: '2/3',
      voicePath: "waa_guide_select_type.MP3",
      shouldPassThrough: true, // 允许点击穿透到底层组件
    ),
    GuideStepType.guideWorkTime: GuideStepUIModel(
      title: '选择工作时长，默认选择一个工',
      step: '3/3',
      voicePath: "waa_guide_select_record_time.MP3",
      isFinish: true,
    ),
  };
}
