import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'guide_step_ui_model.dart';

/// 引导目标配置类
/// 用于关联UI组件和引导步骤类型
@immutable
class GuideTargetUIModel {
  final GuideStepType stepType; // 步骤类型
  final GlobalKey globalKey; // 目标组件的GlobalKey

  const GuideTargetUIModel({
    required this.stepType,
    required this.globalKey,
  });
}

/// 引导步骤配置类
/// 定义每个引导步骤的内容和行为
@immutable
class GuideStepUIModel {
  final String title; // 引导标题
  final String step; // 步骤编号（如"1/5"）
  final bool isFinish; // 是否为最后一步
  final bool shouldPassThrough; // 是否允许点击穿透
  final String voicePath; // 语音文件路径

  const GuideStepUIModel({
    required this.title,
    required this.step,
    required this.voicePath,
    this.isFinish = false,
    this.shouldPassThrough = false,
  });
}

/// 引导步骤类型枚举
/// 引导流程
/// 工人：1、记工首页日历->2、记工页-tab->3、记工页-上班时间
/// 班组长：1、记工首页日历、2、记工页-tab、3、记工-被记账成员（班组长)、4、成员管理页-添加成员（班组长）、5、记工页-上班时间、
enum GuideStepType {
  guideCalender, // 日历
  guideRecordBar, // 记工-tab
  guideWorkTime, // 记工-上班时间
  guideMember, // 记工-被记账成员
  guideAddMember, // 记工-添加成员
}

/// 用户角色枚举
/// 区分班组长和工人的引导流程
enum UserRoleState {
  boss, // 班组长
  worker, // 工人
}

/// 播放状态枚举
enum PlayerState {
  idle, // 未播放
  playing, // 播放中
  stopped, // 已停止
  error, // 错误状态
}
