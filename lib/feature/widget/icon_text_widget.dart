import 'package:flutter/widgets.dart';

/// 已知问题: 同时设置水平和垂直方向的图标, 如果 对齐方式不是 IconAlignment.center; 对齐方式会很迷
/// 接口已经定义好了; 实现的太low; 等个有缘人用自定义布局重写
class IconText extends StatelessWidget {
  final BoxDecoration? decoration;
  final EdgeInsetsGeometry? padding;
  final Text text;
  final Widget? leftIcon;
  final Widget? rightIcon;
  final Widget? topIcon;
  final Widget? bottomIcon;
  final IconAlignments iconAlignments;
  final IconPadding iconPadding;

  const IconText(
      {super.key,
      this.decoration,
      this.padding,
      required this.text,
      this.leftIcon,
      this.rightIcon,
      this.topIcon,
      this.bottomIcon,
      this.iconAlignments = const IconAlignments.all(IconAlignment.center),
      this.iconPadding = const IconPadding.all(0)});

  @override
  Widget build(BuildContext context) {
    final Widget row;
    if (leftIcon == null && rightIcon == null) {
      row = text;
    } else {
      final List<Widget> children = [];
      if (leftIcon != null) {
        children.add(Align(
          alignment: _mappingVertical(iconAlignments.leftIconAlignment),
          child: leftIcon,
        ));
      }
      children.add(text);
      if (rightIcon != null) {
        children.add(Align(
          alignment: _mappingVertical(iconAlignments.rightIconAlignment),
          child: rightIcon,
        ));
      }
      row = Row(
        mainAxisSize: MainAxisSize.min,
        children: children,
      );
    }

    final Widget column;
    if (topIcon == null && bottomIcon == null) {
      column = row;
    } else {
      final List<Widget> children = [];
      if (topIcon != null) {
        children.add(Align(
          alignment: _mappingHorizontal(iconAlignments.topIconAlignment),
          child: topIcon,
        ));
      }
      children.add(row);
      if (bottomIcon != null) {
        children.add(Align(
          alignment: _mappingHorizontal(iconAlignments.bottomIconAlignment),
          child: bottomIcon,
        ));
      }
      column = Column(
        mainAxisSize: MainAxisSize.min,
        children: children,
      );
    }

    return DecoratedBox(
      decoration: decoration ?? const BoxDecoration(),
      child: Padding(
        padding: padding ?? const EdgeInsets.all(0),
        child: column,
      ),
    );
  }

  Alignment _mappingVertical(IconAlignment align) {
    switch (align) {
      case IconAlignment.start:
        return Alignment.topCenter;
      case IconAlignment.center:
        return Alignment.center;
      case IconAlignment.end:
        return Alignment.bottomCenter;
    }
  }

  Alignment _mappingHorizontal(IconAlignment align) {
    switch (align) {
      case IconAlignment.start:
        return Alignment.centerLeft;
      case IconAlignment.center:
        return Alignment.center;
      case IconAlignment.end:
        return Alignment.centerRight;
    }
  }
}

enum IconAlignment { start, center, end }

class IconAlignments {
  final IconAlignment leftIconAlignment;
  final IconAlignment rightIconAlignment;
  final IconAlignment topIconAlignment;
  final IconAlignment bottomIconAlignment;

  IconAlignments({
    required this.leftIconAlignment,
    required this.rightIconAlignment,
    required this.topIconAlignment,
    required this.bottomIconAlignment,
  });

  const IconAlignments.all(IconAlignment alignment)
      : leftIconAlignment = alignment,
        rightIconAlignment = alignment,
        topIconAlignment = alignment,
        bottomIconAlignment = alignment;
}

class IconPadding {
  final double leftIconPadding;
  final double rightIconPadding;
  final double topIconPadding;
  final double bottomIconPadding;

  IconPadding({
    required this.leftIconPadding,
    required this.rightIconPadding,
    required this.topIconPadding,
    required this.bottomIconPadding,
  });

  const IconPadding.all(double padding)
      : leftIconPadding = padding,
        rightIconPadding = padding,
        topIconPadding = padding,
        bottomIconPadding = padding;
}
