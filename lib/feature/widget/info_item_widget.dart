import 'package:flutter/material.dart';
import '../../utils/ui_util/colors_util.dart';

/// 信息展示项组件
class InfoItemWidget extends StatelessWidget {
  final String label;
  final String value;
  final bool showArrow;
  final bool isRequired;
  final VoidCallback? onTap;
  final Widget? customValue;

  const InfoItemWidget({
    super.key,
    required this.label,
    required this.value,
    this.showArrow = true,
    this.isRequired = false,
    this.onTap,
    this.customValue,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
        decoration: const BoxDecoration(
          color: Colors.white,
          border: Border(
            bottom: BorderSide(
              color: Color(0xFFF0F0F0),
              width: 0.5,
            ),
          ),
        ),
        child: Row(
          children: [
            // 必填标识
            if (isRequired)
              Text(
                '* ',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.red,
                ),
              ),
            // 标签
            Text(
              label,
              style: const TextStyle(
                fontSize: 16,
                color: Colors.black,
              ),
            ),
            const Spacer(),
            // 值或自定义组件
            if (customValue != null)
              customValue!
            else
              Text(
                value,
                style: TextStyle(
                  fontSize: 16,
                  color: ColorsUtil.black65,
                ),
              ),
            // 箭头
            if (showArrow) ...[
              const SizedBox(width: 8),
              Icon(
                Icons.arrow_forward_ios,
                size: 14,
                color: ColorsUtil.black45,
              ),
            ],
          ],
        ),
      ),
    );
  }
}

/// 分组标题组件
class SectionTitleWidget extends StatelessWidget {
  final String title;
  final EdgeInsetsGeometry? padding;

  const SectionTitleWidget({
    super.key,
    required this.title,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: padding ?? const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      color: const Color(0xFFF5F5F5),
      child: Text(
        title,
        style: TextStyle(
          fontSize: 14,
          color: ColorsUtil.black65,
        ),
      ),
    );
  }
}
