import 'package:gdjg_pure_flutter/data/demo_setting_data/repo/setting_repo.dart';
import 'package:get/get_rx/src/rx_types/rx_types.dart';

import '../us/setting_us.dart';

class SettingVM {
  SettingUS settingUS = SettingUS();
  final SettingRepo settingRepo = SettingRepo();

  String get myname => settingUS.name2.value;

  final Rx<SettingUS> _settingUSRx = SettingUS().obs;
  SettingUS get settingUSRx => _settingUSRx.value;

  // 修改 fetchV1 方法，添加回调函数
  fetchV1({required Function(String, String) callback}) async {
    var name = await settingRepo.getName();
    var version = await settingRepo.getVersion();
    settingUS = SettingUS(name: name, version: version);

    // 调用回调函数，传递 name 和 version
    callback(name, version);
  }

  fetchV2() async {
    var name = await settingRepo.getName();
    var version = await settingRepo.getVersion();
    settingUS = SettingUS(name: name, version: version);
    settingUS.name2.value = name;
    settingUS.version2.value = version;

    SettingUS settingUSv2 = SettingUS(name: name, version: version);
    _settingUSRx.value = settingUSv2;
  }
}
