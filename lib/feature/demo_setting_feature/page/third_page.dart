import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:gdjg_pure_flutter/feature/demo_setting_feature/page/version_page.dart';
import 'package:gdjg_pure_flutter/init_module/init_route.dart';
import 'package:gdjg_pure_flutter/utils/route_util/base_page/base_ful_page.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';

class ThirdPage extends BaseFulPage {
  const ThirdPage({super.key}):super(title: "我是第三页");

  @override
  @override
  State createState() {
    return _ThirdPageState();
  }
}

class _ThirdPageState<ThirdPage> extends BaseFulPageState {

  @override
  Widget yBuild(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        ElevatedButton(
          onPressed: () {
            YPRoute.openPage(RouteNameCollection.name, params: {"name":"fish", "age":22});
          },
          child: Text('跳转到 Name 页面'),
        ),
        SizedBox(height: 20), // 添加一些垂直间距
        ElevatedButton(
          onPressed: () {
            showDialog(
              context: context,
              builder: (context) => Dialog(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Text('这是一个自定义弹窗'),
                ),
              ),
            );
          },
          child: Text('这是一个新按钮'),
        ),
      ],
    );
  }
}
