import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:gdjg_pure_flutter/data/account/ds/auth_rds.dart';
import 'package:gdjg_pure_flutter/data/account/ds/model/param/waa_login_param_model.dart';
import 'package:gdjg_pure_flutter/feature/demo_setting_feature/page/version_page.dart';
import 'package:gdjg_pure_flutter/init_module/init_route.dart';
import 'package:gdjg_pure_flutter/utils/route_util/base_page/base_ful_page.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/toast_util.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';

import '../../../utils/system_util/yprint.dart';
import '../../../utils/ui_util/colors_util.dart';
import '../../tabbar/view/role_selection_dialog.dart';

class ThirdPage extends BaseFulPage {
  const ThirdPage({super.key}) : super(appBar: const YPAppBar(title: "我是第三页"));

  @override
  @override
  State createState() {
    return _ThirdPageState();
  }
}

class _ThirdPageState<ThirdPage> extends BaseFulPageState {
  @override
  Widget yBuild(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        ElevatedButton(
          onPressed: () {
            YPRoute.openPage(RouteNameCollection.name,
                params: {"name": "fish", "age": 22});
          },
          child: Text('跳转到 Name 页面'),
        ),
        SizedBox(height: 20), // 添加一些垂直间距
        ElevatedButton(
          onPressed: () {
            showDialog(
              context: context,
              builder: (context) => Dialog(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Text('这是一个自定义弹窗'),
                ),
              ),
            );
          },
          child: Text('弹窗'),
        ),
        SizedBox(height: 20), // 添加一些垂直间距
        ElevatedButton(
          onPressed: () {
            YPRoute.openDialog(
                builder: (context) => const RoleSelectionDialog(),
                maskColor: ColorsUtil.black50,
                onDismiss: () {
                  yprint("good");
                },
                onBack: () {
                  return true;
                });
          },
          child: Text('smart dialog'),
        ),
        SizedBox(height: 20), // 添加一些垂直间距
        ElevatedButton(
          onPressed: () {
            YPRoute.openDialog(
                builder: (context) => Dialog(
                        child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Text('这是一个自定义路由弹窗'),
                    )));
          },
          child: Text('自定义弹窗'),
        ),
        SizedBox(height: 20), // 添加一些垂直间距
        ElevatedButton(
          onPressed: () {
            AuthRds authRds = AuthRds();
            WaaLoginParamModel param  = WaaLoginParamModel(
              token: "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpYXQiOjE3NTA2Njc5OTMsImV4cCI6MTc2MTAzNTk5MywiZGF0YSI6eyJzaW5nbGUiOiJTM1c1MTVZVzdENVRTVVY2IiwidWlkIjoyMzAwMjk2NSwiYnVzaW5lc3MiOiIyIiwic3lzdGVtX3R5cGUiOiJhbmRyb2lkIiwibWluaV90b2tlbiI6IlMzVzUxNVlXN0Q1VFNVVjYiLCJpZCI6MjMwMDI5NjUsInV1aWQiOjIzMDAyOTY1fSwidG9rZW4iOnsicmVnUnQiOiJhbmRyb2lkIiwidGVuYW50S2V5IjoiSkdKWiIsInRlbmFudElkIjoyMzAwMjk2NSwicGFja2FnZU5hbWUiOiJjb20ueXVwYW8uZ29uZ2Rpamlnb25nIiwidXNlcklkIjoyMzAwMjk2NSwidG9rZW4iOiJTM1c1MTVZVzdENVRTVVY2In19.MOpvKk8IooDiILl8yq9lt1bAqHnVb92iOCRyAHQYzmc",
              newMember: "123456", origin: '', refid: '',
            );
            authRds.waaLogin(param).then((value) {
              yprint(value.toString());
            });
          },
          child: Text('请求登录'),
        ),
      ],
    );
  }
}
