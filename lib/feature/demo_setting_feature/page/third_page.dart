import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:gdjg_pure_flutter/feature/demo_setting_feature/page/version_page.dart';
import 'package:gdjg_pure_flutter/init_module/init_route.dart';
import 'package:gdjg_pure_flutter/utils/route_util/base_page/base_ful_page.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';

class ThirdPage extends BaseFulPage {

  @override
  @override
  State createState() {
    return _ThirdPageState();
  }
}

class _ThirdPageState<ThirdPage> extends BaseFulPageState {

  @override
  Widget yBuild(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('第三个页面'),
      ),
      body: Center(
        child: ElevatedButton(
          onPressed: () {
            YPRoute.openPage(RouteNameCollection.name, params: {"name":"fish", "age":22});
          },
          child: Text('跳转到 Name 页面'),
        ),
      ),
    );
  }
}
