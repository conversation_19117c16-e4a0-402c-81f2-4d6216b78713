
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:gdjg_pure_flutter/init_module/init_route.dart';
import 'package:gdjg_pure_flutter/utils/route_util/base_page/base_ful_page.dart';
import 'package:gdjg_pure_flutter/utils/system_util/yprint.dart';
import 'package:gdjg_pure_flutter/utils/web_util/web_page_param.dart';
import 'package:get/get.dart';

import '../../../utils/route_util/route_api/yp_route.dart';

class VersionPage extends BaseFulPage {
  const VersionPage({super.key}):super(appBar: const YPAppBar(title: "版本信息"));

  @override@override
  State<StatefulWidget> createState() {
    return _VersionPageState();
  }
}

class _VersionPageState extends BaseFulPageState {

  var name = "ddd".obs;

  @override
  Widget yBuild(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        ElevatedButton(
          onPressed: () {
            YPRoute.openPage(RouteNameCollection.third, params: {"name":"ddd", "age":18});
          },
          child: Text('跳转到 third page'),
        ),
        SizedBox(height: 20),
        ElevatedButton(
          onPressed: () {
            YPRoute.openPage(RouteNameCollection.third, closeNumBeforeOpen: 1);
          },
          child: Text('打开新页面关闭当前页面'),
        ),
        SizedBox(height: 20),
        ElevatedButton(
          onPressed: () {
            YPRoute.openPage(RouteNameCollection.third, clearStackAndPush: true);
          },
          child: Text('清空栈'),
        ),
        SizedBox(height: 20),
        ElevatedButton(
          onPressed: () {
            YPRoute.openWebPage(url: "https://baike.baidu.com/item/OPPO%20Reno7%20SE/59323186", title: "OPPO Reno7 SE");
          },
          child: Text('跳转网页'),
        ),
      ]); // 添加一些垂直间距
  }

  @override
  Object? onSend2Previous() {
    return {"name":"fish", "score":133.0};
  }
}