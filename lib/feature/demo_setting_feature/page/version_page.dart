
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:gdjg_pure_flutter/init_module/init_route.dart';
import 'package:gdjg_pure_flutter/utils/route_util/base_page/base_ful_page.dart';
import 'package:gdjg_pure_flutter/utils/system_util/yprint.dart';
import 'package:get/get.dart';

import '../../../utils/route_util/route_api/yp_route.dart';

class VersionPage extends BaseFulPage {
  @override@override
  State<StatefulWidget> createState() {
    return _VersionPageState();
  }
}

class _VersionPageState extends BaseFulPageState {

  var name = "ddd".obs;

  @override
  Widget yBuild(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('版本页面'),
      ),
      body: Center(
        child: ElevatedButton(
          onPressed: () {
            YPRoute.openPage(RouteNameCollection.third, params: {"name":"ddd", "age":18});
          },
          child: Text('跳转到 third page'),
        ),
      ),
    );
  }

  @override
  Object? onSend2Last() {
    return {"name":"fish", "score":133.0};
  }
}