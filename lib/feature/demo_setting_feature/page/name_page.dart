import 'package:flutter/material.dart';
import 'package:gdjg_pure_flutter/utils/route_util/base_page/base_ful_page.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/utils/system_util/yprint.dart';

class NamePage extends BaseFulPage {

  const NamePage({super.key}) : super(title: "我是名字页面", canPop: true); // 调用父类构造函数并传递 title

  @override
  @override
  State createState() {
    return _NamePageState();
  }
}

class _NamePageState<NamePage> extends BaseFulPageState {

  @override
  onPageRoute(Object? routeParams, bool fromLaunchTask) {
    yprint("");
  }

  @override
  Widget yBuild(BuildContext context) {
    return ElevatedButton(
      onPressed: () {
        YPRoute.openPage("/user_center/version", params: {"name":"ddd", "age":18})?.then((value){
          yprint("$value");
        });
      },
      child: Text('跳转到 VersionPage'),
    );
  }

  @override
  void onReceiveFromNext(Object? backData) {
    super.onReceiveFromNext(backData);
    yprint("$backData");
  }
}
