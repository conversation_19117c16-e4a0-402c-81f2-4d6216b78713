import 'package:flutter/material.dart';
import 'package:gdjg_pure_flutter/utils/route_util/base_page/base_ful_page.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/utils/system_util/yprint.dart';

class NamePage extends BaseFulPage {
  const NamePage({super.key})
      : super(appBar: const YPAppBar(title: "NamePage"), canBack: true); // 调用父类构造函数并传递 title

  @override
  State createState() {
    return _NamePageState();
  }
}

class _NamePageState<NamePage> extends BaseFulPageState {
  @override
  onPageRoute(Object? routeParams, bool fromLaunchTask) {
    yprint("");
  }

  @override
  Widget yBuild(BuildContext context) {
    return Column(mainAxisAlignment: MainAxisAlignment.center, children: [
      ElevatedButton(
        onPressed: () {
          YPRoute.openPage("/user_center/version",
              params: {"name": "ddd", "age": 18})?.then((value) {
            yprint("$value");
          });
        },
        child: Text('跳转到 VersionPage'),
      ),
      SizedBox(height: 20),
      ElevatedButton(
        onPressed: () {
          final mediaQueryData = MediaQuery.of(context);
          final screenWidth = mediaQueryData.size.width;
          final screenHeight = mediaQueryData.size.height;

          final window = WidgetsBinding.instance.window;
          final physicalSize = window.physicalSize;
          final devicePixelRatio = window.devicePixelRatio;

          final logicalWidth = physicalSize.width / devicePixelRatio;
          final logicalHeight = physicalSize.height / devicePixelRatio;

          yprint("good");
        },
        child: Text('计算尺寸'),
      )
    ]);
  }

  @override
  void onReceiveFromNext(Object? backData) {
    super.onReceiveFromNext(backData);
    yprint("$backData");
  }
}
