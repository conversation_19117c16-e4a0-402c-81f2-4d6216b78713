import 'dart:convert';

String generateLargeJson({int chunkCount = 120, int itemsPerChunk = 1000}) {
  final data = {
    'title': 'SampleData',
    'entries': List.generate(chunkCount, (chunkIdx) {
      return {
        'chunkId': chunkIdx,
        'data': List.generate(itemsPerChunk, (i) {
          return {'id': i, 'value': 'item_${chunkIdx}_$i'};
        }),
      };
    }),
  };

  // 转换为字符串
  return jsonEncode(data);
}
