import 'dart:math';

import 'package:flutter/material.dart';
import 'package:gdjg_pure_flutter/feature/demo_setting_feature/page/view/setting_child_view.dart';
import 'package:gdjg_pure_flutter/feature/demo_setting_feature/us/setting_us.dart';
import 'package:gdjg_pure_flutter/feature/demo_setting_feature/vm/setting_vm.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:get/get.dart';

import 'name_page.dart';

class SettingPage extends StatefulWidget {
  // 添加构造函数
  SettingPage({super.key}) {
    print("good");
  }

  @override
  _SettingPageState createState() => _SettingPageState();
}

class _SettingPageState extends State<SettingPage> {
  // 创建SettingUS实例
  final SettingVM settingVM = SettingVM();

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    settingVM.fetchV1(callback: (String name, String version) {
      setState(() {
      });
    });
    print("fish initState");
  }

  @override
  void dispose() {
    // TODO: implement dispose
    super.dispose();
    print("fish dispose");
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('设置'),
      ),
      body: Column(
        children: [
          // 显示name
          Text(
            'Name: ${settingVM.settingUS.name}',
            style: TextStyle(fontSize: 18),
          ),
          // 显示version
          Text(
            'Version: ${settingVM.settingUS.version}',
            style: TextStyle(fontSize: 18),
          ),
          // 显示name
          Obx(()=>
            Text(
              'Name2: ${settingVM.settingUS.name2.value}',
              style: TextStyle(fontSize: 18),
            ),
          ),
          // 显示name
          Obx(()=>
              Text(
                'Version2: ${settingVM.settingUS.version2.value}',
                style: TextStyle(fontSize: 18),
              ),
          ),
          Obx(()=>
              Text(
                'Name3: ${settingVM.settingUSRx.name}',
                style: TextStyle(fontSize: 18),
              ),
          ),
          // 显示version
          // 新增按钮1
          ElevatedButton(
            onPressed:_changeName,
            child: Text('获取name'),
          ),
          // 新增按钮2
          ElevatedButton(
            onPressed: _changeVersion,
            child: Text('获取version'),
          ),
          // 新增按钮1
          ElevatedButton(
            onPressed: _changeName2,
            child: Text('获取name2'),
          ),
          // 新增按钮2
          ElevatedButton(
            onPressed: _changeVersion2,
            child: Text('获取version2'),
          ),
          ElevatedButton(
            onPressed: _jump2NamePage,
            child: Text('跳转name page'),
          ),
          // 原有的SettingChildView
          SettingChildView(),
        ],
      ),
    );
  }

  _changeName() {
    setState(() {
      settingVM.settingUS.name = "${Random().nextInt(100)}";
    });
  }

  _changeVersion() {
    setState(() {
      settingVM.settingUS.version = "${Random().nextInt(100)}";
    });
  }

  _changeName2() {
    settingVM.settingUS.name2.value = "${Random().nextInt(100)}";
  }

  _changeVersion2() {
    settingVM.settingUS.version2.value = "${Random().nextInt(100)}";
  }

  _jump2NamePage() {
    YPRoute.openPage("/user_center/name");
  }

  void showBasicDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('提示'),
        content: Text('确定执行此操作吗？'),
        actions: [
          TextButton(
            onPressed: Navigator.of(context).pop,
            child: Text('取消'),
          ),
          TextButton(
            onPressed: () {
              // 执行确认操作
              Navigator.of(context).pop();
            },
            child: Text('确定'),
          ),
        ],
      ),
    );
  }

  void showCustomDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        child: Container(
          padding: EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('这是一个自定义弹窗'),
              SizedBox(height: 16),
              ElevatedButton(
                onPressed: Navigator.of(context).pop,
                child: Text('关闭'),
              )
            ],
          ),
        ),
      ),
    );
  }
}