
import 'package:gdjg_pure_flutter/utils/system_util/yprint.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/toast_util.dart';

import '../../../init_module/init_route.dart';
import '../../../utils/route_util/interceptors/route_interceptor.dart';

class MyRouteInterceptor implements RouteInterceptor {
  @override
  Future<bool> intercept({required String? routeName, required Object? params}) async {
    yprint("闪开，我要拦截了");
    ToastUtil.showToast("拦截生效");
    // await Future.delayed(Duration(seconds: 2));
    return false;
  }

  @override
  List<String> matchRoute() {
    return [RouteNameCollection.version];
  }

}