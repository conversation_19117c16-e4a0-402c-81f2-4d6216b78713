import 'package:flutter/material.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:gdjg_pure_flutter/feature/tabbar/us/identity_us.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';

class RoleSelectionDialog extends StatelessWidget {
  const RoleSelectionDialog({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 身份选择卡片
          const Row(
            children: [
              Expanded(
                child: _IdentityOptionCard(
                  identity: UserIdentity.worker,
                  iconPath: 'assets/images/common/icon_role_worker.webp',
                  title: '工人记工',
                ),
              ),
              SizedBox(width: 15),
              Expanded(
                child: _IdentityOptionCard(
                  identity: UserIdentity.leader,
                  iconPath: 'assets/images/common/icon_role_group.webp',
                  title: '班组长记工',
                ),
              ),
            ],
          ),

          const SizedBox(height: 50),

          // 关闭按钮
          const _CloseButton(),
        ],
      ),
    );
  }
}

/// 身份选择卡片组件
class _IdentityOptionCard extends StatelessWidget {
  final UserIdentity identity;
  final String iconPath;
  final String title;

  const _IdentityOptionCard({
    required this.identity,
    required this.iconPath,
    required this.title,
  });

  @override
  Widget build(BuildContext context) {
    final controller = IdentityUS.to;
    final isSelected = controller.currentIdentity == identity;

    return GestureDetector(
      onTap: () => controller.selectIdentity(identity),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 21, horizontal: 35),
        decoration: BoxDecoration(
          image: DecorationImage(
            image: AssetImage(
                isSelected
                    ? 'assets/images/common/identity_selected_bg.webp'
                    : 'assets/images/common/identity_not_selected_bg.webp'
            ),
            fit: BoxFit.fill,
          ),
        ),
        child: Column(
          children: [
            Image.asset(
              iconPath,
              width: 90,
              height: 90,
              fit: BoxFit.contain,
            ),
            const SizedBox(height: 8),
            Text(
              title,
              style: TextStyle(
                height: 1.2,
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: isSelected
                    ? ColorsUtil.primaryColor
                    : const Color(0xFF323233),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// 关闭按钮组件
class _CloseButton extends StatelessWidget {
  const _CloseButton();

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => YPRoute.closeDialog(),
      child: Image.asset(
        'assets/images/common/icon_role_close.webp',
        width: 39,
        height: 39,
      ),
    );
  }
}
