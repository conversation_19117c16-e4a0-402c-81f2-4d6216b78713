import 'dart:math';
import 'package:flutter/material.dart';

class RotationSwitchView extends StatefulWidget {
  final Widget frontWidget;
  final Widget backWidget;
  final bool showFrontSide;

  const RotationSwitchView({
    super.key,
    required this.frontWidget,
    required this.backWidget,
    required this.showFrontSide,
  });

  @override
  State<RotationSwitchView> createState() => _RotationSwitchViewState();
}

class _RotationSwitchViewState extends State<RotationSwitchView>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;
  double _currentRotation = 0.0;

  @override
  void initState() {
    super.initState();
    // 如果初始显示背面，初始角度应该是 pi
    if (!widget.showFrontSide) {
      _currentRotation = pi;
    }
    _controller = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    )..addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        // 规范化角度，防止其无限增长
        _currentRotation %= (2 * pi);
      }
    });

    _animation = Tween<double>(begin: _currentRotation, end: _currentRotation)
        .animate(_controller);
  }

  @override
  void didUpdateWidget(RotationSwitchView oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.showFrontSide != oldWidget.showFrontSide) {
      final double targetRotation = _currentRotation + pi;
      _animation =
          Tween<double>(begin: _currentRotation, end: targetRotation).animate(
            CurvedAnimation(
              parent: _controller,
              curve: Curves.easeInOutCubic,
            ),
          );
      _controller.forward(from: 0.0);
      _currentRotation = targetRotation;
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: const Color(0xFFF5F6FA),
      child: AnimatedBuilder(
        animation: _animation,
        builder: (context, child) {
          final angle = _animation.value;
          // 使用 cos(angle) 来确定可见性
          final isFrontVisible = cos(angle) >= 0;

          // 添加缩放效果以增强3D感
          final scale = 1.0 - 0.15 * sin(angle % pi);

          final transform = Matrix4.identity()
            ..setEntry(3, 2, 0.001) // 添加3D透视效果
            ..rotateY(-angle) // 始终顺时针旋转
            ..scale(scale); // 应用缩放

          return Center(
            child: Transform(
              transform: transform,
              alignment: Alignment.center, // 确保旋转居中
              child: IndexedStack(
                index: isFrontVisible ? 0 : 1,
                children: [
                  // 正面组件
                  widget.frontWidget,
                  // 背面组件，预先旋转 pi 使其朝向正确
                  Transform(
                    alignment: Alignment.center,
                    transform: Matrix4.identity()..rotateY(pi),
                    child: widget.backWidget,
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
} 