import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/toast_util.dart';
import '../../../utils/regex/regex_utils.dart';
import '../../../utils/route_util/route_api/yp_route.dart';
import '../../../utils/ui_util/colors_util.dart';

/// 手动添加工友弹窗组件
class AddWorkerDialog extends StatefulWidget {
  const AddWorkerDialog({
    super.key,
    required this.onConfirm,
  });

  final void Function(String name, String phone) onConfirm;

  @override
  State<AddWorkerDialog> createState() => _AddWorkerDialogState();
}

class _AddWorkerDialogState extends State<AddWorkerDialog> {
  String name = '';
  String phone = '';

  final FocusNode _nameFocusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    // 确保在 widget build 完成后再请求焦点
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _nameFocusNode.requestFocus();
    });
  }

  @override
  void dispose() {
    _nameFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 32),
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildTitle(),
            // ✅ 上半部分需要 padding 的内容
            _buildContent(),
            const SizedBox(height: 16),
            // ✅ 分割线和按钮区域不带 padding
            Divider(height: 0.5, color: ColorsUtil.divideLineColor),
            _buildBottom(),
          ],
        ),
      ),
    );
  }

  ///标题信息
  Widget _buildTitle() {
    return Padding(
      padding: const EdgeInsets.all(16), // 上下左右各 16
      child: SizedBox(
        height: 36,
        child: Center(
          child: Text(
            '手动添加工友',
            style: TextStyle(fontSize: 17, color: ColorsUtil.black85),
          ),
        ),
      ),
    );
  }

  ///内容信息
  Widget _buildContent() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 16),
          _buildInputRow(
              label: '工友姓名：',
              hint: '请输入真实姓名(必填)',
              maxLength: 20,
              onChanged: (value) => name = value,
              focusNode: _nameFocusNode),
          const SizedBox(height: 16),
          _buildInputRow(
            label: '手机号码：',
            hint: '请输入真实手机号码(选填)',
            inputFormatters: [
              FilteringTextInputFormatter.allow(RegExp(r'[0-9]')), // 只允许数字
            ],
            maxLength: 11,
            keyboardType: TextInputType.phone,
            onChanged: (value) => phone = value,
          ),
        ],
      ),
    );
  }

  /// 横向输入组件
  Widget _buildInputRow(
      {required String label,
      required String hint,
      required int maxLength,
      TextInputType? keyboardType,
      List<TextInputFormatter>? inputFormatters,
      required ValueChanged<String> onChanged,
      FocusNode? focusNode}) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Text(
          label,
          style: const TextStyle(fontSize: 17, color: Colors.black),
        ),
        const SizedBox(width: 8),
        _buildInput(
            hint: hint,
            maxLength: maxLength,
            keyboardType: keyboardType,
            inputFormatters: inputFormatters,
            onChanged: onChanged,
            focusNode: focusNode)
      ],
    );
  }

  ///输入框
  Widget _buildInput(
      {required String hint,
      required int maxLength,
      TextInputType? keyboardType,
      List<TextInputFormatter>? inputFormatters,
      required ValueChanged<String> onChanged,
      FocusNode? focusNode}) {
    return Expanded(
      // 自动填满右侧剩余空间
      child: TextField(
        focusNode: focusNode,
        // 设置光标颜色
        cursorColor: ColorsUtil.primaryColor,
        keyboardType: keyboardType,
        maxLength: maxLength,
        inputFormatters: inputFormatters,
        decoration: InputDecoration(
          counterText: '',
          hintText: hint,
          hintStyle: TextStyle(
            color: ColorsUtil.hintFontColor,
            fontSize: 17,
          ),
          // 关键代码：添加 hint
          isDense: true,
          contentPadding:
              const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
            borderSide: const BorderSide(
              color: Color(0XFFe6e6e6), // 边框颜色
              width: 1, // 👈 设置边框宽度（单位是 dp）
            ),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
            borderSide: const BorderSide(
              color: Color(0XFFe6e6e6), // 边框颜色
              width: 1, // 👈 设置边框宽度（单位是 dp）
            ),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
            borderSide: const BorderSide(
              color: Color(0XFFe6e6e6), // 边框颜色
              width: 1, // 👈 设置边框宽度（单位是 dp）
            ),
          ),
        ),
        onChanged: onChanged,
      ),
    );
  }

  ///底部提交按钮
  Widget _buildBottom() {
    return SizedBox(
      height: 48, // 👈 固定高度
      child: Row(
        children: [
          Expanded(
            child: SizedBox(
              height: 48,
              child: GestureDetector(
                behavior: HitTestBehavior.opaque,
                onTap: () => YPRoute.closeDialog(),
                child: Center(
                  child:
                      Text('取消', style: TextStyle(color: ColorsUtil.black85)),
                ),
              ),
            ),
          ),
          // 可选：中间分隔线
          VerticalDivider(width: 0.5, color: ColorsUtil.divideLineColor),
          Expanded(
            child: SizedBox(
              height: 48,
              child: GestureDetector(
                behavior: HitTestBehavior.opaque,
                onTap: () {
                  if (_valid()) {
                    YPRoute.closeDialog();
                    widget.onConfirm(name, phone);
                  }
                },
                child: Center(
                  child: Text('确认',
                      style: TextStyle(color: ColorsUtil.primaryColor)),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  ///验证提交数据
  bool _valid() {
    if (name.trim().isEmpty) {
      ToastUtil.showToast('请输入工友真实姓名');
      return false;
    }

    if (phone.trim().isNotEmpty && !RegexUtils.isMobile(phone.trim())) {
      ToastUtil.showToast('请输入正确的手机号');
      return false;
    }
    return true;
  }
}
