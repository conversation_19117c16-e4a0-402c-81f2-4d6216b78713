import 'package:flutter/material.dart';
import 'package:gdjg_pure_flutter/feature/tabbar/view/rotation_switch_view.dart';
import 'package:gdjg_pure_flutter/init_module/init_route.dart';
import 'package:gdjg_pure_flutter/utils/route_util/base_page/base_ful_page.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:get/get.dart';
import 'package:net_plugin/pigeon/api_generated/net_channel.g.dart';
import 'package:net_plugin/pigeon/net_service/net_core.dart';
import '../group/page/group_page.dart';
import '../worker/page/worker_page.dart';
import 'us/identity_us.dart';

class MainPage extends BaseFulPage {
  const MainPage({Key? key}) : super(key: key, appBar: null);

  @override
  State createState() => _MainPageState();
}

class _MainPageState<MainPage> extends BaseFulPageState {
  // const BottomTabBarPage({super.key});

  @override
  void initState() {
    super.initState();
    // testNet();
  }

  Future<void> testNet() async {
    print('----------------- start testNet');
    var jsonMap = {
      "identity": 2,
      "start_time": "2025-06-09",
      "end_time": "2025-06-09",
      "status": 0,
    };
    var res = await NetCore.requestJGPHP(
        BaseBizRequestEntity(url: '/api/v3/business/get-business-list', method: HTTP_METHOD.GET, content: jsonMap), (json){
          print('----------------- testNet res: $json');
      return json.toString();
    });
    print(res);
  }

  @override
  Widget yBuild(BuildContext context) {
    final controller = IdentityUS.to;

    return Container(
      // padding: EdgeInsets.all(10),
      // color: Colors.red,
      child: Stack(
        children: [
          Positioned(
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              child: Obx(() => RotationSwitchView(
                frontWidget: const WorkerPage(),
                backWidget: const GroupPage(),
                showFrontSide: controller.currentIdentity == UserIdentity.worker,
              ))),
          Positioned(
              bottom: 150,
              right: 10,
              child: GestureDetector(
                onTap: () {
                  // Get.toNamed(RouteNameCollection.test);
                  YPRoute.openPage(RouteNameCollection.test);
                  // Get.toNamed(Routes.test);
                },
                child: Container(
                  padding: const EdgeInsets.symmetric(vertical: 2, horizontal: 10),
                  decoration: BoxDecoration(
                    color: Color.fromRGBO(0, 0, 0, 0.55),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Text(
                    '测试',
                    style: TextStyle(color: Colors.red, fontSize: 20, fontWeight: FontWeight.bold),
                  ),
                ),
              )
          )
        ],
      ),
    );
  }
}
