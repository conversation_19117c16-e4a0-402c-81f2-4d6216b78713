import 'package:gdjg_pure_flutter/feature/group/us/group_us.dart';
import 'package:gdjg_pure_flutter/feature/tabbar/view/role_selection_dialog.dart';
import 'package:gdjg_pure_flutter/feature/worker/us/worker_us.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/utils/system_util/yprint.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';
import 'package:get/get.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';

enum UserIdentity {
  worker('工人'),
  leader('班组长');

  final String label;

  const UserIdentity(this.label);
}

class IdentityUS extends GetxController {
  static IdentityUS get to => Get.find();

  final _currentIdentity = UserIdentity.worker.obs;

  UserIdentity get currentIdentity => _currentIdentity.value;

  String get subTitle {
    return _currentIdentity.value == UserIdentity.worker
        ? '点击切换为班组长'
        : '点击切换为工人';
  }

  /// 显示身份选择弹窗
  void showRoleSelectionDialog() {
    YPRoute.openDialog(builder: (context) => const RoleSelectionDialog(),
        maskColor: ColorsUtil.black50);
  }

  /// 选择身份（从弹窗中调用）
  void selectIdentity(UserIdentity identity) {
    // 如果选择的是当前身份，直接关闭弹窗，不进行切换
    if (identity == _currentIdentity.value) {
      SmartDialog.dismiss();
      return;
    }

    // 切换到新身份
    _switchToIdentity(identity);
    SmartDialog.dismiss(); // 关闭弹窗
  }

  /// 执行身份切换逻辑
  void _switchToIdentity(UserIdentity identity) {
    _currentIdentity.value = identity;

    if (identity == UserIdentity.leader) {
      GroupUS.to.resetState();
    } else {
      WorkerUS.to.resetState();
    }
  }
}
