import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/common_dialog.dart';

/// 显示转移记工确认弹窗
/// [oldPhoneDays] 旧手机号记工天数
/// [currentPhoneDays] 当前手机号记工天数
/// [onConfirm] 确认回调
/// [onCancel] 取消回调
void showTransferConfirmDialog({
  required String oldPhoneDays,
  required String currentPhoneDays,
  required VoidCallback onConfirm,
  VoidCallback? onCancel,
}) {
  showCommonDialog(
    CommonDialogConfig(
      title: '是否确认更换手机号码',
      contentWidget: _TransferInfoContent(
        oldPhoneDays: oldPhoneDays,
        currentPhoneDays: currentPhoneDays,
      ),
      titleStyle: TextStyle(
          color: Color(0xFF323233), fontSize: 16.sp, fontWeight: FontWeight.normal),
      positive: '确认更换',
      negative: '我再想想',
      onPositive: onConfirm,
      onNegative: onCancel,
      positiveStyle:  TextStyle(
        color: ColorsUtil.primaryColor,
        fontSize: 16.sp,
        fontWeight: FontWeight.w500,
      ),
      negativeStyle:  TextStyle(
        color: Color(0xFF323233),
        fontSize: 16.sp,
      ),
    ),
    clickMaskDismiss: false,
    backDismiss: false,
  );
}

/// 转移信息内容组件
class _TransferInfoContent extends StatelessWidget {
  final String oldPhoneDays;
  final String currentPhoneDays;

  const _TransferInfoContent({
    required this.oldPhoneDays,
    required this.currentPhoneDays,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildInfoItem('旧手机号：共记工', oldPhoneDays),
          _buildInfoItem('当前手机号：共记工', currentPhoneDays),
        ],
      ),
    );
  }

  /// 构建信息项
  Widget _buildInfoItem(String prefix, String days) {
    return Container(
      height: 36.h, // 对应RN中的lineHeight: 72 (36.h * 2)
      alignment: Alignment.centerLeft,
      child: RichText(
        text: TextSpan(
          style: TextStyle(
            fontSize: 14.sp,
            color: ColorsUtil.black85,
            height: 1.4,
          ),
          children: [
            TextSpan(text: prefix),
            TextSpan(
              text: '$days天',
              style: TextStyle(
                color: ColorsUtil.primaryColor, // rgba(82, 144, 253, 1)
                fontWeight: FontWeight.normal,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
