import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/common_dialog.dart';

/// 显示转移记工成功弹窗
/// [onConfirm] 确认回调
void showTransferSuccessDialog({
  required VoidCallback onConfirm,
}) {
  showCommonDialog(
    CommonDialogConfig(
      title: '已为您完成记工信息互换',
      content: '请点击"我知道了"后，使用当前手机号重新登录APP，即可查看到旧手机号中的记工信息',
      titleStyle: TextStyle(
        color: Color(0xFF323233),
        fontSize: 16.sp,
        fontWeight: FontWeight.normal,
      ),
      contentStyle: TextStyle(
        color: Color(0xFF8A8A99),
        fontSize: 14.sp,
        height: 1.4,
      ),
      positive: '我知道了',
      hiddenNegative: false,
      onPositive: onConfirm,
      positiveStyle: TextStyle(
        color: ColorsUtil.primaryColor,
        fontSize: 16.sp,
        fontWeight: FontWeight.w500,
      ),
    ),
    clickMaskDismiss: false,
    backDismiss: false,
  );
}
