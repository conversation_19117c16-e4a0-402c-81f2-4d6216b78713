import 'package:get/get.dart';

class TransferRecordWorkUS {
  /// 当前手机号
  final _nowTel = ''.obs;

  /// 验证码
  final _code = ''.obs;

  /// 旧手机号
  final _oldPhone = ''.obs;

  /// 真实姓名
  final _realName = ''.obs;

  /// 身份证号
  final _idCard = ''.obs;

  String get nowTel => _nowTel.value;

  String get code => _code.value;

  String get oldPhone => _oldPhone.value;

  String get realName => _realName.value;

  String get idCard => _idCard.value;

  void setNowTel(String tel) {
    _nowTel.value = tel;
  }

  void setCode(String code) {
    _code.value = code;
  }

  void setOldPhone(String oldPhone) {
    _oldPhone.value = oldPhone;
  }

  void setRealName(String realName) {
    _realName.value = realName;
  }

  void setIdCard(String idCard) {
    _idCard.value = idCard;
  }
}
