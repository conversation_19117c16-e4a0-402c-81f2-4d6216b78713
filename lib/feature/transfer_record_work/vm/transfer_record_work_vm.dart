import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:gdjg_pure_flutter/data/account/repo/auth_repo.dart';
import 'package:gdjg_pure_flutter/data/transfer_record_work/ds/model/param/code_get_param_model.dart';
import 'package:gdjg_pure_flutter/data/transfer_record_work/ds/model/param/migrate_apply_param_model.dart';
import 'package:gdjg_pure_flutter/data/transfer_record_work/ds/model/param/migrate_confirm_param_model.dart';
import 'package:gdjg_pure_flutter/data/transfer_record_work/repo/transfer_record_work_repo.dart';
import 'package:gdjg_pure_flutter/feature/transfer_record_work/view/transfer_confirm_dialog.dart';
import 'package:gdjg_pure_flutter/feature/transfer_record_work/view/transfer_success_dialog.dart';
import 'package:gdjg_pure_flutter/utils/system_util/yprint.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/toast_util.dart';
import 'transfer_record_work_us.dart';

/// 转移记工页面ViewModel
class TransferRecordWorkViewModel {
  final _authRepo = AuthRepo();
  final _repo = TransferRecordWorkRepo();
  final us = TransferRecordWorkUS();

  /// 初始化页面数据
  Future<void> initialize() async {
    try {
      // todo:LINer 获取手机号
      // final currentUserTel = _authRepo.getAccount().tel;
      final currentUserTel = "***********";
      us.setNowTel(currentUserTel);
    } catch (e) {
      us.setNowTel('');
    }
  }

  /// 获取验证码
  Future<bool> onGetVerificationCodeTap() async {
    try {
      final currentTel = us.nowTel;
      if (currentTel.isEmpty) {
        ToastUtil.showToast('手机号不能为空');
        return false;
      }
      _repo.getCode(CodeGetParamModel(type: '3', tel: currentTel));
      ToastUtil.showToast('验证码已发送');
      return true;
    } catch (e) {
      ToastUtil.showToast('获取验证码失败');
      return false;
    }
  }

  /// 提交转移申请
  Future onSubmitTransferRequestTap({
    required String code,
    required String oldPhone,
    required String realName,
    required String idCard,
  }) async {
    if (!_validateForm(code, oldPhone, realName, idCard)) {
      return;
    }
    try {
      us.setCode(code);
      us.setOldPhone(oldPhone);
      us.setRealName(realName);
      us.setIdCard(idCard);
      final res = await _repo.applyMigrate(MigrateApplyParamModel(
        code: code,
        old_tel: oldPhone,
        card_name: realName,
        card_no: idCard,
      ));
      if (res.success != null) {
        var data = res.success?.data;
        if (data?.refuseReason.isNotEmpty == true) {
          ToastUtil.showToast(data?.refuseReason ?? '');
          return;
        }
        // 弹出弹窗
        var oldPhoneDays = data?.oldNum.toInt().toString() ?? '0';
        var currentPhoneDays = data?.curNum.toInt().toString() ?? '0';
        var confirmId = data?.confirmId ?? 0;
        showTransferConfirmDialog(
            oldPhoneDays: oldPhoneDays,
            currentPhoneDays: currentPhoneDays,
            onConfirm: () async {
              onConfirmTransferTap(confirmId);
            });
      }
    } catch (e) {
      yprint(e.toString());
    }
  }

  /// 确认转换
  Future<void> onConfirmTransferTap(double confirmId) async {
    try {
      // 调用确认转移接口
      final res = await _repo.confirmMigrate(MigrateConfirmParamModel(
        confirm_id: confirmId,
      ));
      if (res.success != null) {
        // 显示成功弹窗
        showTransferSuccessDialog(
          onConfirm: () {
            SmartDialog.dismiss();
           /// TODO:LINer 退出登录
          },
        );
      }
    } catch (e) {
      yprint(e.toString());
    }
  }

  /// 表单验证方法
  bool _validateForm(
      String code, String oldPhone, String realName, String idCard) {
    // 第一轮：检查所有字段是否为空（优先级最高）
    if (code.trim().isEmpty) {
      ToastUtil.showToast('请输入验证码');
      return false;
    }
    if (oldPhone.trim().isEmpty) {
      ToastUtil.showToast('请输入旧手机号');
      return false;
    }
    if (realName.trim().isEmpty) {
      ToastUtil.showToast('请输入真实姓名');
      return false;
    }
    if (idCard.trim().isEmpty) {
      ToastUtil.showToast('请输入身份证号');
      return false;
    }

    // 第二轮：检查所有字段格式是否正确
    // 正则表达式定义
    final codeRegex = RegExp(r'^\d{4}$');
    final phoneRegex = RegExp(r'^1[3-9]\d{9}$');
    final nameRegex = RegExp(r'^[\u4e00-\u9fa5a-zA-Z]{2,20}$');
    final idCardRegex = RegExp(r'^\d{17}[\dXx]$');

    if (!codeRegex.hasMatch(code.trim())) {
      ToastUtil.showToast('请填入正确的验证码');
      return false;
    }
    if (!phoneRegex.hasMatch(oldPhone.trim())) {
      ToastUtil.showToast('请填入正确的手机号');
      return false;
    }
    if (!nameRegex.hasMatch(realName.trim())) {
      ToastUtil.showToast('请填入正确的姓名');
      return false;
    }
    if (!idCardRegex.hasMatch(idCard.trim())) {
      ToastUtil.showToast('请填入正确的身份证号');
      return false;
    }
    return true;
  }

  /// 确认转移
  Future<bool> confirmTransfer(String confirmId) async {
    try {
      // TODO: 调用API确认转移

      // 模拟API调用延迟
      await Future.delayed(Duration(seconds: 1));

      ToastUtil.showToast('记工信息转移成功');
      return true;
    } catch (e) {
      ToastUtil.showToast('转移失败，请重试');
      return false;
    }
  }

  /// 初始化表单数据到US状态管理
  void initializeFormData({
    String? nowTel,
    String? code,
    String? oldPhone,
    String? realName,
    String? idCard,
  }) {
    if (nowTel != null) us.setNowTel(nowTel);
    if (code != null) us.setCode(code);
    if (oldPhone != null) us.setOldPhone(oldPhone);
    if (realName != null) us.setRealName(realName);
    if (idCard != null) us.setIdCard(idCard);
  }

  /// 清空所有表单数据
  void clearFormData() {
    us.setNowTel('');
    us.setCode('');
    us.setOldPhone('');
    us.setRealName('');
    us.setIdCard('');
  }
}
