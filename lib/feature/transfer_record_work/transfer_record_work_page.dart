import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/utils/system_util/yprint.dart';
import 'package:get/get.dart';
import 'package:gdjg_pure_flutter/feature/transfer_record_work/vm/transfer_record_work_vm.dart';
import 'package:gdjg_pure_flutter/utils/route_util/base_page/base_ful_page.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/button_util.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/toast_util.dart';
import 'package:gdjg_pure_flutter/widget/countdown_button.dart';

/// 转移记工页面
class TransferRecordWorkPage extends BaseFulPage {
  const TransferRecordWorkPage({super.key})
      : super(appBar: const YPAppBar(title: '转移记工信息'));

  @override
  State createState() => _TransferRecordWorkPageState();
}

class _TransferRecordWorkPageState
    extends BaseFulPageState<TransferRecordWorkPage> {
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  final TextEditingController _codeController = TextEditingController(); // 验证码
  final TextEditingController _oldPhoneController =
      TextEditingController(); // 旧手机号
  final TextEditingController _nameController = TextEditingController(); // 姓名
  final TextEditingController _idCardController =
      TextEditingController(); // 身份证号码
  final TransferRecordWorkViewModel _viewModel = TransferRecordWorkViewModel();

  @override
  void initState() {
    super.initState();
    // 初始化页面数据
    _viewModel.initialize();
  }

  @override
  void dispose() {
    _codeController.dispose();
    _oldPhoneController.dispose();
    _nameController.dispose();
    _idCardController.dispose();
    super.dispose();
  }

  @override
  Widget yBuild(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      resizeToAvoidBottomInset: false, // 防止键盘影响布局
      body: ListView(
        children: [
          _buildTopDivider(),
          _buildPhoneInfoSection(),
          _buildFormSection(),
          _buildTipsSection(),
          _buildFixedFooter(),
        ],
      ),
    );
  }

  /// 顶部分割线
  Widget _buildTopDivider() {
    return Container(
      height: 8.h,
      color: Color(0xFFF5F6FA),
    );
  }

  /// 当前手机号信息区域
  Widget _buildPhoneInfoSection() {
    return Container(
      margin: EdgeInsets.only(left: 24.w, right: 24.w, top: 16.h),
      padding: EdgeInsets.symmetric(vertical: 12.h),
      child: Column(
        children: [
          Text(
            '你当前绑定手机的手机号码',
            style: TextStyle(
              fontSize: 16.sp,
              color: ColorsUtil.black85,
            ),
          ),
          SizedBox(height: 3.h),
          Obx(() => Text(
                _viewModel.us.nowTel,
                style: TextStyle(
                  fontSize: 24.sp,
                  color: ColorsUtil.primaryColor,
                ),
              )),
        ],
      ),
    );
  }

  /// 表单区域
  Widget _buildFormSection() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 24.w),
      child: Form(
        key: _formKey,
        child: Column(
          children: [
            _buildFormItem(
              title: '验证码',
              child: Row(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(
                    child: _buildTextFormField(
                      controller: _codeController,
                      placeholder: '请输入验证码',
                      keyboardType: TextInputType.number,
                      maxLength: 4,
                      inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                    ),
                  ),
                  SizedBox(width: 12.w),
                  Container(
                    margin: EdgeInsets.only(right: 0.h),
                    child: CountdownButton(
                      style: ButtonStyle(
                        overlayColor:
                            MaterialStateProperty.all(Colors.transparent),
                        alignment: Alignment.topLeft,
                        padding:
                            MaterialStateProperty.all(EdgeInsets.zero), // 移除内边距
                      ),
                      defaultText: '获取验证码',
                      countdownText: '{count}s',
                      countdownDuration: 60,
                      textStyle: TextStyle(
                        fontSize: 15.sp,
                        color: ColorsUtil.primaryColor,
                      ),
                      onPressed: (startCountDown) async {
                        bool success = await _onGetCode();
                        if (success) {
                          startCountDown();
                        }
                      },
                    ),
                  ),
                ],
              ),
            ),
            _buildFormItem(
              title: '旧手机号',
              child: _buildTextFormField(
                controller: _oldPhoneController,
                placeholder: '请输入旧手机号',
                keyboardType: TextInputType.number,
                maxLength: 11,
                inputFormatters: [FilteringTextInputFormatter.digitsOnly],
              ),
            ),
            _buildFormItem(
              title: '真实姓名',
              child: _buildTextFormField(
                controller: _nameController,
                placeholder: '请输入真实姓名',
                maxLength: 20,
              ),
            ),
            _buildFormItem(
              title: '身份证号',
              child: _buildTextFormField(
                controller: _idCardController,
                placeholder: '请输入身份证号',
                maxLength: 18,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 温馨提示区域
  Widget _buildTipsSection() {
    return Container(
      padding: EdgeInsets.only(left: 24.w, right: 24.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '温馨提示',
            style: TextStyle(
              fontSize: 16.sp,
              color: ColorsUtil.black85,
            ),
          ),
          SizedBox(height: 8.h),
          _buildTipText('1.用户验证身份证后，可将旧手机号中的记工信息，与当前正在使用的手机号中的记工信息，进行互换。'),
          _buildTipText('2.不可将两个手机号中的记工信息合并同一手机号中。'),
          _buildTipText('3.同一个用户仅可更换一次记工信息。'),
        ],
      ),
    );
  }

  /// 提示文本
  Widget _buildTipText(String text) {
    return Container(
      margin: EdgeInsets.only(bottom: 5.h),
      child: Text(
        text,
        style: TextStyle(
          fontSize: 14.sp,
          color: ColorsUtil.black85,
          height: 1.4,
        ),
      ),
    );
  }

  /// 表单项
  Widget _buildFormItem({
    required String title,
    required Widget child,
  }) {
    return Container(
      margin: EdgeInsets.only(bottom: 24.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: TextStyle(
              fontSize: 14.sp,
              color: ColorsUtil.black85,
            ),
          ),
          SizedBox(height: 8.h),
          Container(
            // color: ColorsUtil.greyColor,
            height: 35.h,
            child: child,
          ),
          Divider(height: 1, color: Color(0xFFE5E5E5))
        ],
      ),
    );
  }

  /// 文本输入框
  Widget _buildTextFormField({
    required TextEditingController controller,
    required String placeholder,
    TextInputType? keyboardType,
    List<TextInputFormatter>? inputFormatters,
    int? maxLength,
  }) {
    return TextFormField(
      controller: controller,
      keyboardType: keyboardType,
      inputFormatters: inputFormatters,
      maxLength: maxLength,
      style: TextStyle(
        fontSize: 16.sp,
        color: Colors.black,
      ),
      decoration: InputDecoration(
        hintText: placeholder,
        hintStyle: TextStyle(
          fontSize: 16.sp,
          color: Color(0xFFCCCCCC),
        ),
        border: InputBorder.none,
        contentPadding: EdgeInsets.zero,
        counterText: '',
        isDense: true,
      ),
    );
  }

  /// 固定底部按钮
  Widget _buildFixedFooter() {
    return Container(
      padding: EdgeInsets.only(left: 24.w, right: 24.w, top: 48.h),
      decoration: BoxDecoration(
        color: Colors.white,
      ),
      child: ButtonUtil.buildCommonButton(
        padding: EdgeInsets.all(0),
        text: '转移记工',
        backgroundColor: ColorsUtil.primaryColor,
        onPressed: _onSubmit,
      ),
    );
  }

  /// 获取验证码
  Future<bool> _onGetCode() async {
    return await _viewModel.onGetVerificationCodeTap();
  }

  /// 提交表单
  void _onSubmit() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }
    try {
      _viewModel.onSubmitTransferRequestTap(
        code: _codeController.text,
        oldPhone: _oldPhoneController.text,
        realName: _nameController.text,
        idCard: _idCardController.text,
      );
    } catch (e) {
      yprint(e.toString());
    }
  }
}
