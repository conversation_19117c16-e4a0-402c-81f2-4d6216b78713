import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/feature/widget/empty_status_widget.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';

class VisitorLeaderUnsettledSubpage extends StatelessWidget {
  const VisitorLeaderUnsettledSubpage({super.key});

  @override
  Widget build(BuildContext context) => Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Align(
            alignment: Alignment.centerLeft,
            child: Padding(
              padding: EdgeInsets.only(left: 8.w, right: 8.w, top: 12.h),
              child: Text(
                '未结工友(0)',
                style: TextStyle(
                  fontSize: 18.sp,
                  color: ColorsUtil.ypPrimaryColor,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          SizedBox(
            height: 60.h,
          ),
          EmptyStatusWidget(text: '您尚未登录，无法查询到您的未结算的工友信息'),
        ],
      );
}
