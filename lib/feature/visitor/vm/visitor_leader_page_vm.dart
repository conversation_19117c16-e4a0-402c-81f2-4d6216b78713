import 'package:gdjg_pure_flutter/feature/visitor/us/visitor_main_tabs_us.dart';
import 'package:get/get_state_manager/src/simple/get_controllers.dart';

import '../../../data/visitor/repo/visitor_leader_page_repo.dart';


class VisitorLeaderPageVM extends GetxController {
  final VisitorLeaderPageRepo _repo = VisitorLeaderPageRepo();
  final VisitorMainTabsUS _us = VisitorMainTabsUS();

  int get index => _us.index;

  @override
  void onInit() {
    super.onInit();
    _fetchIndex();
  }

  Future<void> _fetchIndex() async {
    _us.updateIndex(await _repo.fetchIndex());
  }

  Future<void> updateIndex(int index) async {
    _us.updateIndex(await _repo.updateIndex(index));
  }
}
