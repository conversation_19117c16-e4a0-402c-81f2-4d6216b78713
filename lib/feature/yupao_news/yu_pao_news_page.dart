import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/utils/route_util/base_page/base_ful_page.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'vm/yu_pao_news_vm.dart';
import 'vm/yu_pao_news_us.dart';
import 'view/news_card_view.dart';

/// @description 鱼泡资讯页面入口
class YuPaoNewsPage extends BaseFulPage {
  YuPaoNewsPage({super.key}) : super(appBar: YPAppBar(title: "鱼泡资讯"));

  @override
  State<YuPaoNewsPage> createState() => _YuPaoNewsPageState();
}

class _YuPaoNewsPageState extends BaseFulPageState<YuPaoNewsPage> {
  late YuPaoNewsVM _vm;

  @override
  void onPageCreate() {
    super.onPageCreate();
    // 初始化ViewModel
    _vm = YuPaoNewsVM();
  }

  @override
  void dispose() {
    _vm.dispose();
    super.dispose();
  }

  @override
  Widget yBuild(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Column(
        children: [
          Container(
            height: 1.w,
            color: Color(0xFFF5F5F5),
          ),
          Expanded(
            child: Container(
              padding: EdgeInsets.symmetric(vertical: 16.h),
              child: SmartRefresher(
                controller: _vm.refreshController,
                enablePullDown: true,
                enablePullUp: true,
                onRefresh: _vm.onRefresh,
                onLoading: _vm.onLoading,
                child: CustomScrollView(
                  slivers: [
                    Obx(() => SliverList.builder(
                      itemCount: _vm.us.newsList.length,
                      itemBuilder: (context, index) {
                        final newsItem = _vm.us.newsList[index];
                        return NewsCardView(
                          key: ValueKey(newsItem.index),
                          data: newsItem,
                        );
                      },
                    )),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}