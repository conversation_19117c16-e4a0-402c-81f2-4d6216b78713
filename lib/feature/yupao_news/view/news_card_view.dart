import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';
import '../vm/yu_pao_news_us.dart';

/// 新闻卡片组件
class NewsCardView extends StatelessWidget {
  final YuPaoNewsItemUS data;

  const NewsCardView({
    super.key,
    required this.data,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () {
        if(data.jumpUrl.isNotEmpty){
          YPRoute.openWebPage(url: data.jumpUrl);
        }
      },
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 16.w),
        height: 78.h,
        margin: EdgeInsets.only(bottom: 12.h),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start, // 垂直居中对齐
          children: [
            // 左侧图片
            Container(
              width: 60.w,
              height: 60.h,
              margin: EdgeInsets.only(right: 10.w),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(4.r),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(4.r),
                child: Image.network(
                  data.coverImg,
                  width: 60.w,
                  height: 60.h,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    // 图片加载失败时的占位符
                    return Container(
                      width: 60.w,
                      height: 60.h,
                      decoration: BoxDecoration(
                        color: Color(0xFFF5F5F5),
                        borderRadius: BorderRadius.circular(4.r),
                      ),
                    );
                  },
                ),
              ),
            ),
            // 右侧内容
            Expanded(
              child: Container(
                decoration: BoxDecoration(
                  border: Border(
                    bottom: BorderSide(
                      color: Color(0xFFF5F5F5),
                      width: 1.w,
                    ),
                  ),
                ),
                padding: EdgeInsets.only(bottom: 12.h),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 标题
                    Expanded(
                      child: Container(
                        margin: EdgeInsets.only(bottom: 4.h),
                        child: Text(
                          data.title,
                          style: TextStyle(
                            fontSize: 14.sp,
                            color: ColorsUtil.black85,
                            fontWeight: FontWeight.w500,
                            height: 18.sp / 14.sp,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ),
                    // 底部作者和时间
                    Row(
                      children: [
                        // 作者
                        Expanded(
                          child: Container(
                            margin: EdgeInsets.only(right: 10.w),
                            child: Text(
                              data.author,
                              style: TextStyle(
                                fontSize: 12.sp,
                                color: Color.fromRGBO(138, 138, 153, 1),
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ),
                        // 时间
                        Text(
                          data.time,
                          style: TextStyle(
                            fontSize: 12.sp,
                            color: Color.fromRGBO(138, 138, 153, 1),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
