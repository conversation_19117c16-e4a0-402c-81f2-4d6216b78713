import 'package:gdjg_pure_flutter/data/yu_pao_news_data/ds/model/param/news_get_list_param_model.dart';
import 'package:gdjg_pure_flutter/data/yu_pao_news_data/repo/model/news_get_list_biz_model.dart';
import 'package:gdjg_pure_flutter/data/yu_pao_news_data/repo/yu_pao_new_repo.dart';
import 'package:gdjg_pure_flutter/feature/yupao_news/vm/yu_pao_news_us.dart';
import 'package:gdjg_pure_flutter/utils/refresh_loadmore/refresh_loadmore_vvm.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

class YuPaoNewsVM {
  final _repo = YuPaoNewsRepo();
  final us = YuPaoNewsListUS();
  
  /// 分页管理
  final RefreshController refreshController = RefreshController(initialRefresh: false);
  late RefreshLoadMoreVvm<YuPaoNewsItemUS> newsRefresh;
  
  YuPaoNewsVM() {
    // 初始化分页管理，pageSize = 15
    newsRefresh = RefreshLoadMoreVvm<YuPaoNewsItemUS>(
      refreshController: refreshController,
      dataFetcher: fetchYuPaoNewsList,
      pageSize: 15,
    );
    
    // 初始化数据
    onRefresh();
  }

  /// 获取数据 - 分页请求
  Future<List<YuPaoNewsItemUS>> fetchYuPaoNewsList(int page, int pageSize) async {
    final param = NewsGetListParamModel(
      page: page.toString(),
      pages: page.toString(),
      pageSize: "15",
    );
    
    final res = await _repo.getNewsList(param);
    if (res.isOK()) {
      final data = res.getSucData();
      if (data != null) {
        return convertEntityToUIState(data.list);
      }
    }
    return [];
  }

  /// 转换数据
  List<YuPaoNewsItemUS> convertEntityToUIState(List<NewsGetListABizModel> dataList) {
    return dataList.map((item) {
      final newsItem = YuPaoNewsItemUS();
      newsItem.setTitle(item.title);
      newsItem.setAuthor(item.author);
      newsItem.setTime(item.time);
      newsItem.setCoverImg(item.coverImg);
      newsItem.setJumpUrl(item.jumpUrl);
      return newsItem;
    }).toList();
  }

  /// 下拉刷新
  Future<void> onRefresh() async {
    await newsRefresh.refresh();
    // 更新UI状态
    us.setNewsList(newsRefresh.list);
  }

  /// 上拉加载更多
  Future<void> onLoading() async {
    await newsRefresh.loadMore();
    // 更新UI状态
    us.setNewsList(newsRefresh.list);
  }

  /// 获取当前新闻列表
  List<YuPaoNewsItemUS> get newsList => newsRefresh.list;

  /// 是否还有更多数据
  bool get hasMore => newsRefresh.hasMore;

  /// 释放资源
  void dispose() {
    refreshController.dispose();
  }
}

