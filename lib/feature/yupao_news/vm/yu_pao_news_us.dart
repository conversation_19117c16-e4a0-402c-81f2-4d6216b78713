import 'dart:ffi';

import 'package:get/get.dart';

class YuPaoNewsListUS {
  final _newsList = <YuPaoNewsItemUS>[].obs;

  List<YuPaoNewsItemUS> get newsList => _newsList;

  void setNewsList(List<YuPaoNewsItemUS> newsList) {
    _newsList.value = newsList;
  }
}

class YuPaoNewsItemUS {
  /// index
  final _index = 0.obs;

  /// 标题
  final _title = ''.obs;

  /// 作者
  final _author = ''.obs;

  /// 时间
  final _time = ''.obs;

  /// 封面图片
  final _coverImg = ''.obs;

  /// 跳转链接
  final _jumpUrl = ''.obs;

  int get index => _index.value;

  String get title => _title.value;

  String get author => _author.value;

  String get time => _time.value;

  String get coverImg => _coverImg.value;

  String get jumpUrl => _jumpUrl.value;

  void setIndex(int index) {
    _index.value = index;
  }

  void setTitle(String title) {
    _title.value = title;
  }

  void setAuthor(String author) {
    _author.value = author;
  }

  void setTime(String time) {
    _time.value = time;
  }

  void setCoverImg(String coverImg) {
    _coverImg.value = coverImg;
  }

  void setJumpUrl(String jumpUrl) {
    _jumpUrl.value = jumpUrl;
  }
}
