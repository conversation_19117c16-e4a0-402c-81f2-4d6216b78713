import 'package:gdjg_pure_flutter/feature/income_expenses/ui_model/annual_incomes_pend_year_ui_model.dart';
import 'package:get/get_rx/src/rx_types/rx_types.dart';

import '../ui_model/annual_incomes_first_item_view_ui_model.dart';
import '../ui_model/annual_incomes_pend_dept_ui_model.dart';
import '../ui_model/annual_incomes_pend_ui_model.dart';
import '../ui_model/annual_incomes_total_head_view_ui_model.dart';

class AnnualIncomeSpendDeptUS {
  /// 项目选择弹窗-项目列表
  final List<AnnualIncomeSpendDeptUIModel> _proList =
      <AnnualIncomeSpendDeptUIModel>[].obs;

  /// 时间选择弹窗-年份列表
  final List<AnnualIncomeSpendYearUIModel> _yearList =
      <AnnualIncomeSpendYearUIModel>[].obs;

  /// 类型弹窗-全部收入支出列表
  final List<AnnualIncomeSpendUIModel> _allTypeList =
      <AnnualIncomeSpendUIModel>[].obs;

  /// 类型弹窗-收入列表
  final List<AnnualIncomeSpendUIModel> _incomeList =
      <AnnualIncomeSpendUIModel>[].obs;

  /// 类型弹窗-支出列表
  final List<AnnualIncomeSpendUIModel> _spendList =
      <AnnualIncomeSpendUIModel>[].obs;

  /// 页面-顶部数据
  final Rx<AnnualIncomeSpendTotalHeadUIModel> _totalHeadData =
      AnnualIncomeSpendTotalHeadUIModel().obs;

  /// 页面 一级列表数据
  final List<AnnualIncomesFirstItemViewUiModel> _recordItemList =
      <AnnualIncomesFirstItemViewUiModel>[].obs;

  /// 时间选择弹窗-选中年数据
  final _selectionYearInfo = DateTime.now().year.toInt().obs;

  /// 项目选择弹窗-选中项目数据
  final Rx<AnnualIncomeSpendDeptUIModel> _selectionProInfo =
      AnnualIncomeSpendDeptUIModel().obs;

  ///类型弹窗- 选中类型数据
  final Rx<AnnualIncomeSpendUIModel> _selectionTypeInfo =
      AnnualIncomeSpendUIModel().obs;

  List<AnnualIncomeSpendDeptUIModel> get proList => _proList;

  List<AnnualIncomeSpendYearUIModel> get yearList => _yearList;

  List<AnnualIncomeSpendUIModel> get allTypeList => _allTypeList;

  List<AnnualIncomeSpendUIModel> get incomeList => _incomeList;

  List<AnnualIncomeSpendUIModel> get spendList => _spendList;

  AnnualIncomeSpendTotalHeadUIModel get totalHeadData => _totalHeadData.value;

  List<AnnualIncomesFirstItemViewUiModel> get recordItemList => _recordItemList;

  int get selectionYearInfo => _selectionYearInfo.value;

  AnnualIncomeSpendDeptUIModel get selectionProInfo => _selectionProInfo.value;

  AnnualIncomeSpendUIModel get selectionTypeInfo => _selectionTypeInfo.value;



  void setProList(List<AnnualIncomeSpendDeptUIModel> list) {
    _proList.assignAll(list);
  }

  void setYearList(List<AnnualIncomeSpendYearUIModel> list) {
    _yearList.assignAll(list);
  }

  void setAllTypeList() {
    _allTypeList.clear();
    _allTypeList.assignAll([AnnualIncomeSpendUIModel(name: "全部分类", fixId: -1)]);
    updateSelectionTypeInfo(_allTypeList[0]);
  }

  void setIncomeList(List<AnnualIncomeSpendUIModel> list) {
    _incomeList.assignAll(list);
  }

  void setSpendList(List<AnnualIncomeSpendUIModel> list) {
    _spendList.assignAll(list);
  }

  void setTotalHeadData(AnnualIncomeSpendTotalHeadUIModel total) {
    _totalHeadData.value = total;
  }

  void setRecordItemList(List<AnnualIncomesFirstItemViewUiModel> list) {
    _recordItemList.assignAll(list);
  }

  ///设置选中的年
  void updateSelectionYearInfo(int year) {
    _selectionYearInfo.value = year;
  }

  ///设置选中的项目
  void updateSelectionProInfo(AnnualIncomeSpendDeptUIModel bean) {
    _selectionProInfo.value = bean;
  }

  ///设置选中的类型
  void updateSelectionTypeInfo(AnnualIncomeSpendUIModel info) {
    _selectionTypeInfo.value = info;
  }
}
