import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';
import 'package:gdjg_pure_flutter/widget/hour_keyboard_dialog.dart';
import 'package:gdjg_pure_flutter/widget/work_hours_keyboard_dialog.dart';

/// 小时选择结果类型
class HourSelectionType {
  static const String hour = 'hour';      // 小时
  static const String workDay = 'workDay'; // 工天
}

/// 小时选择结果
class HourSelectionResult {
  /// 选择的数值
  final double value;

  /// 选择类型
  final String type;

  const HourSelectionResult({
    required this.value,
    required this.type,
  });
}

/// 时间选择弹窗组件
class HourSelectorDialog extends StatefulWidget {
  /// 确认回调
  final Function(HourSelectionResult) onConfirm;

  /// 初始选中的小时数
  final double? initHour;

  /// 初始选中的工天数
  final double? initWorkDay;

  /// 是否开启键盘输入功能
  final bool enableKeyboard;

  const HourSelectorDialog({
    super.key,
    required this.onConfirm,
    this.initHour,
    this.initWorkDay,
    this.enableKeyboard = false,
  });

  @override
  State<HourSelectorDialog> createState() => _HourSelectorDialogState();
}

class _HourSelectorDialogState extends State<HourSelectorDialog> {
  late List<double> hourOptions;
  double? selectedInitValue;
  bool isHalfWorkDaySelected = false;

  @override
  void initState() {
    super.initState();
    _generateHourOptions();
    _determineInitialSelection();
  }

  /// 初始值
  void _determineInitialSelection() {
    // 优先选择小时
    if (widget.initHour != null) {
      selectedInitValue = widget.initHour;
      isHalfWorkDaySelected = false;
    } else if (widget.initWorkDay != null) {
      // 工天只保留0.5，如果是0.5就选中半个工按钮
      if (widget.initWorkDay == 0.5) {
        selectedInitValue = null;
        isHalfWorkDaySelected = true;
      } else {
        // 其他工天值不显示选中状态
        selectedInitValue = null;
        isHalfWorkDaySelected = false;
      }
    } else {
      selectedInitValue = null;
      isHalfWorkDaySelected = false;
    }
  }

  /// 小时选项列表（0.5-24小时）
  void _generateHourOptions() {
    hourOptions = [];
    for (double i = 0.5; i <= 24.0; i += 0.5) {
      hourOptions.add(i);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: widget.enableKeyboard ? 400.h : 340.h,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(12.r),
          topRight: Radius.circular(12.r),
        ),
      ),
      child: Column(
        children: [
          _buildTitleBar(),
          if (widget.enableKeyboard) _buildKeyboardButtons(),
          Expanded(child: _buildHourGrid()),
        ],
      ),
    );
  }

  /// 构建标题栏
  Widget _buildTitleBar() {
    return Container(
      height: 40.h,
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: ColorsUtil.divideLineColor,
            width: 1,
          ),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          GestureDetector(
            onTap: () => YPRoute.closeDialog(),
            child: Icon(
              Icons.close,
              size: 20.w,
              color: const Color(0xFF8A8A99),
            ),
          ),
          Text(
            '请选择上班时长',
            style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.w600,
              color: const Color(0xFF323233),
            ),
          ),
          SizedBox(width: 20.w),
        ],
      ),
    );
  }

  /// 构建键盘输入按钮区域
  Widget _buildKeyboardButtons() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: ColorsUtil.divideLineColor,
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          // 输入工天按钮
          Expanded(
            child: GestureDetector(
              onTap: _onWorkDayInputTap,
              child: Container(
                height: 36.h,
                decoration: BoxDecoration(
                  color: Colors.white,
                  border: Border.all(
                    color: const Color(0xFFE0E0E0),
                    width: 1,
                  ),
                  borderRadius: BorderRadius.circular(4.r),
                ),
                child: Center(
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.border_color,
                        size: 16.sp,
                        color: const Color(0xFF666666),
                      ),
                      SizedBox(width: 4.w),
                      Text(
                        '输入工天',
                        style: TextStyle(
                          fontSize: 14.sp,
                          color: const Color(0xFF666666),
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
          SizedBox(width: 12.w),
          // 输入小时按钮
          Expanded(
            child: GestureDetector(
              onTap: _onHourInputTap,
              child: Container(
                height: 36.h,
                decoration: BoxDecoration(
                  color: Colors.white,
                  border: Border.all(
                    color: const Color(0xFFE0E0E0),
                    width: 1,
                  ),
                  borderRadius: BorderRadius.circular(4.r),
                ),
                child: Center(
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.border_color,
                        size: 16.sp,
                        color: const Color(0xFF666666),
                      ),
                      SizedBox(width: 4.w),
                      Text(
                        '输入小时',
                        style: TextStyle(
                          fontSize: 14.sp,
                          color: const Color(0xFF666666),
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
          SizedBox(width: 12.w),
          // 半个工按钮
          Expanded(
            child: GestureDetector(
              onTap: () => _onQuickSelect(0.5),
              child: Container(
                height: 36.h,
                decoration: BoxDecoration(
                  color: isHalfWorkDaySelected ? ColorsUtil.primaryColor : const Color(0xFFF1F1F1),
                  borderRadius: BorderRadius.circular(4.r),
                ),
                child: Center(
                  child: Text(
                    '半个工',
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: isHalfWorkDaySelected ? Colors.white : const Color(0xFF323233),
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 小时选择网格
  Widget _buildHourGrid() {
    return Padding(
      padding: EdgeInsets.fromLTRB(16.w, 10.h, 16.w, 16.w),
      child: GridView.builder(
        padding: EdgeInsets.zero,
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 4,
          crossAxisSpacing: 12,
          mainAxisSpacing: 12,
          childAspectRatio: 1.9,
        ),
        itemCount: hourOptions.length,
        itemBuilder: (context, index) {
          final hour = hourOptions[index];
          final isSelected = selectedInitValue == hour;

          return GestureDetector(
            onTap: () => _onHourSelected(hour),
            child: Container(
              decoration: BoxDecoration(
                color: isSelected ? ColorsUtil.primaryColor : Color(0xFFF1F1F1),
                borderRadius: BorderRadius.circular(4.r),
              ),
              child: Center(
                child: Text(
                  _formatHour(hour),
                  style: TextStyle(
                    fontSize: 16.sp,
                    color: isSelected ? Colors.white : const Color(0xFF323233),
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  /// 格式化小时显示文本
  String _formatHour(double hour) {
    if (hour == hour.toInt()) {
      return '${hour.toInt()}小时';
    } else {
      return '$hour小时';
    }
  }

  /// 小时选择触发事件
  void _onHourSelected(double hour) {
    final result = HourSelectionResult(
      value: hour,
      type: HourSelectionType.hour,
    );
    widget.onConfirm(result);
    YPRoute.closeDialog();
  }

  /// 工天输入按钮点击
  void _onWorkDayInputTap() {
    YPRoute.openDialog(
      alignment: Alignment.bottomCenter,
      builder: (context) => WorkHoursKeyboardDialog(
        onConfirm: (value) {
          final result = HourSelectionResult(
            value: value,
            type: HourSelectionType.workDay,
          );
          widget.onConfirm(result);
          YPRoute.closeDialog(); // 关闭 HourSelectorDialog
        },
      ),
    );
  }

  /// 小时输入按钮点击
  void _onHourInputTap() {
    YPRoute.openDialog(
      alignment: Alignment.bottomCenter,
      builder: (context) => HourKeyboardDialog(
        onConfirm: (value) {
          final result = HourSelectionResult(
            value: value,
            type: HourSelectionType.hour,
          );
          widget.onConfirm(result);
          YPRoute.closeDialog(); // 关闭 HourSelectorDialog
        },
      ),
    );
  }

  /// 快速选择（半个工）
  void _onQuickSelect(double value) {
    final result = HourSelectionResult(
      value: value,
      type: HourSelectionType.workDay,
    );
    widget.onConfirm(result);
    YPRoute.closeDialog();
  }
}

/// 显示时间选择弹窗
void showHourSelectorDialog({
  required Function(HourSelectionResult) onConfirm,
  double? initHour,
  double? initWorkDay,
  bool enableKeyboard = false,
}) {
  YPRoute.openDialog(
    alignment: Alignment.bottomCenter,
    builder: (context) => HourSelectorDialog(
      onConfirm: onConfirm,
      initHour: initHour,
      initWorkDay: initWorkDay,
      enableKeyboard: enableKeyboard,
    ),
  );
}
