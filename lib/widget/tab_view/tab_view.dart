import 'package:flutter/material.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';

/// TabView数据模型
class TabViewItem {
  final String title;
  final Widget content;

  TabViewItem({
    required this.title,
    required this.content,
  });
}

/// 横向滑动切换页面的TabView组件
class TabView extends StatefulWidget {
  final List<TabViewItem> items;
  final double indicatorHeight;
  final double indicatorWidth;
  final Color? indicatorColor;
  final TextStyle? titleStyle;
  final TextStyle? selectedTitleStyle;
  final Color? backgroundColor;
  final double tabHeight;

  const TabView({
    super.key,
    required this.items,
    this.indicatorHeight = 2.0,
    this.indicatorWidth = 20.0,
    this.indicatorColor,
    this.titleStyle,
    this.selectedTitleStyle,
    this.backgroundColor,
    this.tabHeight = 50.0,
  });

  @override
  State<TabView> createState() => _TabViewState();
}

class _TabViewState extends State<TabView> {
  late PageController _pageController;
  int _currentIndex = 0;

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // 标题栏
        _buildTabBar(),
        // 页面内容
        Expanded(
          child: _buildPageView(),
        ),
      ],
    );
  }

  /// 构建标题栏
  Widget _buildTabBar() {
    return Container(
      height: widget.tabHeight,
      color: widget.backgroundColor ?? Colors.white,
      child: Column(
        children: [
          // 标题行
          Expanded(
            child: Row(
              children: List.generate(
                widget.items.length,
                (index) => _buildTabItem(index),
              ),
            ),
          ),
          // 指示器
          _buildIndicator(),
        ],
      ),
    );
  }

  /// 构建单个标题项
  Widget _buildTabItem(int index) {
    final isSelected = index == _currentIndex;
    final item = widget.items[index];

    return Expanded(
      child: GestureDetector(
        onTap: () => _onTabTap(index),
        child: Container(
          alignment: Alignment.center,
          child: Text(
            item.title,
            style: isSelected
                ? (widget.selectedTitleStyle ??
                    TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: widget.indicatorColor ?? ColorsUtil.primaryColor,
                    ))
                : (widget.titleStyle ??
                    TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.normal,
                      color: Colors.grey[600],
                    )),
          ),
        ),
      ),
    );
  }

  /// 构建指示器
  Widget _buildIndicator() {
    return SizedBox(
      height: widget.indicatorHeight,
      child: Stack(
        children: [
          // 指示器
          AnimatedPositioned(
            duration: const Duration(milliseconds: 200),
            curve: Curves.easeInOut,
            left: _getIndicatorPosition(),
            child: Container(
              width: widget.indicatorWidth,
              height: widget.indicatorHeight,
              decoration: BoxDecoration(
                color: widget.indicatorColor ?? ColorsUtil.primaryColor,
                borderRadius: BorderRadius.circular(widget.indicatorHeight / 2),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建页面视图
  Widget _buildPageView() {
    return PageView.builder(
      controller: _pageController,
      onPageChanged: _onPageChanged,
      itemCount: widget.items.length,
      itemBuilder: (context, index) {
        return widget.items[index].content;
      },
    );
  }

  /// 计算指示器位置
  double _getIndicatorPosition() {
    final screenWidth = MediaQuery.of(context).size.width;
    final tabWidth = screenWidth / widget.items.length;
    final indicatorOffset = (tabWidth - widget.indicatorWidth) / 2;
    return _currentIndex * tabWidth + indicatorOffset;
  }

  /// 标题点击事件
  void _onTabTap(int index) {
    if (index != _currentIndex) {
      _pageController.animateToPage(
        index,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  /// 页面切换事件
  void _onPageChanged(int index) {
    setState(() {
      _currentIndex = index;
    });
  }
}
