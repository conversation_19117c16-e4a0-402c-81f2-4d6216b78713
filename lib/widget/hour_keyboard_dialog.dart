import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/toast_util.dart';

/// 小时输入键盘弹窗
class HourKeyboardDialog extends StatefulWidget {
  /// 确认回调
  final Function(double value) onConfirm;

  const HourKeyboardDialog({
    super.key,
    required this.onConfirm,
  });

  @override
  State<HourKeyboardDialog> createState() => _HourKeyboardDialogState();
}

class _HourKeyboardDialogState extends State<HourKeyboardDialog> {
  String _inputValue = '';
  bool _hasUserInput = false;

  @override
  void initState() {
    super.initState();
    _inputValue = '';
    _hasUserInput = false;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildTitleBar(),
          _buildValueDisplay(),
          _buildDivider(),
          _buildKeyboard(),
          SizedBox(height: MediaQuery.of(context).padding.bottom),
        ],
      ),
    );
  }

  /// 构建标题栏
  Widget _buildTitleBar() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
      decoration: const BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: Color(0xFFE0E0E0),
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          GestureDetector(
            onTap: () => YPRoute.closeDialog(),
            child: Icon(
              Icons.close,
              size: 18.sp,
              color: const Color(0xFF666666),
            ),
          ),
          Expanded(
            child: Center(
              child: Text(
                '请输入小时数',
                style: TextStyle(
                  fontSize: 16.sp,
                  color: const Color(0xFF333333),
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
          SizedBox(width: 18.w),
        ],
      ),
    );
  }

  /// 构建数值显示区域
  Widget _buildValueDisplay() {
    Color textColor = _hasUserInput ? const Color(0xFF5290FD) : const Color(0xFFCCCCCC);

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 10.h),
      child: Row(
        children: [
          Text(
            _inputValue.isEmpty ? '0' : _inputValue,
            style: TextStyle(
              fontSize: 34.sp,
              color: textColor,
            ),
          ),
          SizedBox(width: 8.w),
          Text(
            '小时',
            style: TextStyle(
              fontSize: 18.sp,
              color: const Color(0xFF333333),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建分隔线
  Widget _buildDivider() {
    Color dividerColor = _hasUserInput ? const Color(0xFF5290FD) : const Color(0xFFE0E0E0);
    return Container(
      margin: EdgeInsets.only(left: 20.w, bottom: 24.h),
      height: 1,
      color: dividerColor,
    );
  }

  /// 构建数字键盘
  Widget _buildKeyboard() {
    return Row(
      children: [
        Expanded(
          flex: 3,
          child: Column(
            children: [
              Row(children: [_buildKeyButton('1'), _buildKeyButton('2'), _buildKeyButton('3')]),
              Row(children: [_buildKeyButton('4'), _buildKeyButton('5'), _buildKeyButton('6')]),
              Row(children: [_buildKeyButton('7'), _buildKeyButton('8'), _buildKeyButton('9')]),
              Row(children: [_buildWideKeyButton('0'), _buildKeyButton('.')]),
            ],
          ),
        ),
        Expanded(
          flex: 1,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildDeleteButton(),
              _buildConfirmButton(),
            ],
          ),
        ),
      ],
    );
  }

  /// 构建数字按键
  Widget _buildKeyButton(String key) {
    return Expanded(
      child: GestureDetector(
        onTap: () => _onKeyPressed(key),
        child: Container(
          height: 52.h,
          decoration: const BoxDecoration(
            color: Color(0xFFF8F8F8),
            border: Border(
              top: BorderSide(color: Color(0xFFE0E0E0), width: 0.5),
              left: BorderSide(color: Color(0xFFE0E0E0), width: 0.5),
              right: BorderSide(color: Color(0xFFE0E0E0), width: 0.5),
              bottom: BorderSide(color: Color(0xFFE0E0E0), width: 0.5),
            ),
          ),
          child: Center(
            child: Text(
              key,
              style: TextStyle(
                fontSize: 24.sp,
                color: const Color(0xFF333333),
                fontWeight: FontWeight.w400,
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// 构建0按键
  Widget _buildWideKeyButton(String key) {
    return Expanded(
      flex: 2,
      child: GestureDetector(
        onTap: () => _onKeyPressed(key),
        child: Container(
          height: 52.h,
          decoration: const BoxDecoration(
            color: Color(0xFFF8F8F8),
            border: Border(
              top: BorderSide(color: Color(0xFFE0E0E0), width: 0.5),
              left: BorderSide(color: Color(0xFFE0E0E0), width: 0.5),
              right: BorderSide(color: Color(0xFFE0E0E0), width: 0.5),
              bottom: BorderSide(color: Color(0xFFE0E0E0), width: 0.5),
            ),
          ),
          child: Center(
            child: Text(
              key,
              style: TextStyle(
                fontSize: 24.sp,
                color: const Color(0xFF333333),
                fontWeight: FontWeight.w400,
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// 构建删除按键
  Widget _buildDeleteButton() {
    return GestureDetector(
      onTap: _onDeletePressed,
      child: Container(
        height: 52.h,
        decoration: const BoxDecoration(
          color: Color(0xFFE8E8E8),
          border: Border(
            top: BorderSide(color: Color(0xFFE0E0E0), width: 0.5),
            left: BorderSide(color: Color(0xFFE0E0E0), width: 0.5),
            right: BorderSide(color: Color(0xFFE0E0E0), width: 0.5),
            bottom: BorderSide(color: Color(0xFFE0E0E0), width: 0.5),
          ),
        ),
        child: Center(
          child: Text(
            '清除',
            style: TextStyle(
              fontSize: 16.sp,
              color: const Color(0xFF666666),
              fontWeight: FontWeight.w400,
            ),
          ),
        ),
      ),
    );
  }

  /// 构建确认按钮
  Widget _buildConfirmButton() {
    return GestureDetector(
      onTap: _onConfirmPressed,
      child: Container(
        height: 156.h,
        decoration: const BoxDecoration(
          color: Color(0xFF5290FD),
          border: Border(
            top: BorderSide(color: Color(0xFFE0E0E0), width: 0.5),
            left: BorderSide(color: Color(0xFFE0E0E0), width: 0.5),
            right: BorderSide(color: Color(0xFFE0E0E0), width: 0.5),
            bottom: BorderSide(color: Color(0xFFE0E0E0), width: 0.5),
          ),
        ),
        child: Center(
          child: Text(
            '确定',
            style: TextStyle(
              fontSize: 18.sp,
              color: Colors.white,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ),
    );
  }

  /// 处理数字按键点击
  void _onKeyPressed(String key) {
    setState(() {
      String newValue = _inputValue;

      if (key == '.') {
        if (!_hasUserInput) return;
        if (!newValue.contains('.')) {
          newValue += '.';
        }
      } else {
        if (!_hasUserInput) {
          newValue = key;
          _hasUserInput = true;
        } else {
          newValue += key;
        }
      }

      double? value = double.tryParse(newValue);

      // 检查是否超出最大值99.9
      if (value != null && value > 99.9) {
        ToastUtil.showToast('最大值不能超过99.9');
        return;
      }

      // 检查小数位数限制
      if (newValue.contains('.')) {
        List<String> parts = newValue.split('.');
        if (parts.length == 2 && parts[1].length > 1) {
          ToastUtil.showToast('最多只能输入1位小数');
          return;
        }
      }

      if (value != null && value <= 99.9) {
        if (newValue.contains('.')) {
          List<String> parts = newValue.split('.');
          if (parts.length == 2 && parts[1].length <= 1) {
            _inputValue = newValue;
          }
        } else {
          _inputValue = newValue;
        }
      }
    });
  }

  /// 处理删除按键点击
  void _onDeletePressed() {
    setState(() {
      if (_hasUserInput && _inputValue.isNotEmpty) {
        _inputValue = _inputValue.substring(0, _inputValue.length - 1);
        if (_inputValue.isEmpty) {
          _hasUserInput = false;
        }
      }
    });
  }

  /// 处理确认按钮点击
  void _onConfirmPressed() {
    double value = 0.0;
    if (_inputValue.isNotEmpty) {
      value = double.tryParse(_inputValue) ?? 0.0;
    }

    if (value <= 0) {
      ToastUtil.showToast('请输入小时数');
      return;
    }

    widget.onConfirm(value);
    YPRoute.closeDialog();
  }
}
