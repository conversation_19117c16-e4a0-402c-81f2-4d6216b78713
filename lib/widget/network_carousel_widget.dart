import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/data/utils_data/carousel/rep/carousel_rep.dart';
import 'package:gdjg_pure_flutter/data/utils_data/carousel/entity/adposition_detail_biz_model.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/banner_utils.dart';

/*
      网络轮播组件使用
          NetworkCarouselWidget(
            code: 'JGJZ_HOME_GROUP_BANER',
            pageCode: 'JGJZ_HOME_GROUP_BANER',
          )
 */

/// 网络轮播组件
/// 支持通过网络请求获取Banner数据
class NetworkCarouselWidget extends StatefulWidget {
  /// 广告位标识
  final String code;

  /// 页面标识（用于埋点统计）
  final String? pageCode;

  /// 广告类型，可选
  final String? type;

  /// 是否大广告，可选
  final int? big;

  /// 轮播图高度，默认48
  final double height;

  /// 是否自动播放，默认true
  final bool autoPlay;

  /// 自动播放间隔(毫秒)，默认3000
  final int autoPlayInterval;

  /// 是否显示指示器，默认true
  final bool showIndicator;

  /// 指示器颜色，默认#91B2C7
  final Color indicatorColor;

  /// 激活指示器颜色，默认#007AFC
  final Color indicatorActiveColor;

  /// 指示器大小，默认6.0
  final double indicatorSize;

  /// 页面切换回调
  final Function(int index)? onPageChanged;

  /// 是否无限循环，默认true
  final bool infiniteLoop;

  /// 图片适配方式，默认BoxFit.cover
  final BoxFit fit;

  /// 圆角半径，默认8.0
  final double borderRadius;

  /// 数据加载完成回调
  final Function(List<AdpositionDetailABizModel> banners)? onDataLoaded;

  const NetworkCarouselWidget({
    super.key,
    required this.code,
    this.pageCode,
    this.type,
    this.big,
    this.height = 48,
    this.autoPlay = true,
    this.autoPlayInterval = 3000,
    this.showIndicator = true,
    this.indicatorColor = const Color(0x7F91B2C7),
    this.indicatorActiveColor = const Color(0xFF007AFC),
    this.indicatorSize = 6.0,
    this.onPageChanged,
    this.infiniteLoop = true,
    this.fit = BoxFit.cover,
    this.borderRadius = 8.0,
    this.onDataLoaded,
  });

  @override
  State<NetworkCarouselWidget> createState() => _NetworkCarouselWidgetState();
}

class _NetworkCarouselWidgetState extends State<NetworkCarouselWidget>
    with WidgetsBindingObserver {
  // 常量定义
  static const int _infiniteLoopInitialPage = 10000; // 无限循环初始页面索引
  static const Duration _animationDuration = Duration(milliseconds: 300); // 页面切换动画时长
  static const Duration _scrollEndDelay = Duration(seconds: 2); // 滑动结束后恢复自动播放的延迟时间
  
  // 控制器和定时器
  late PageController _pageController; // 页面控制器
  Timer? _timer; // 自动播放定时器
  Timer? _scrollEndTimer; // 滑动结束延迟定时器

  // 状态变量
  int _currentIndex = 0; // 当前页面索引
  bool _isVisible = true; // 组件是否可见
  bool _isAnimating = false; // 是否正在执行动画
  bool _isScrolling = false; // 是否正在滑动
  bool _isProgrammaticScroll = false; // 是否为程序触发的滑动

  // 网络请求相关
  final CarouselRep _carouselRep = CarouselRep(); // 轮播图数据仓库
  List<AdpositionDetailABizModel> _banners = []; // Banner数据列表
  List<String> _imageUrls = []; // 图片URL缓存列表

  // 图片预加载相关
  final Set<String> _preloadedImages = {}; // 已预加载图片URL集合

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _initializeController();
    _loadBannerData();
  }

  /// 加载Banner数据
  Future<void> _loadBannerData() async {
    final banners = await _carouselRep.getBannerByCode(
      widget.code,
      type: widget.type,
      big: widget.big,
    );

    if (mounted) {
      setState(() {
        _banners = banners;
        _imageUrls = banners.map((banner) => banner.img).toList();
      });

      // 数据加载完成后预加载图片
      await _preloadImages();

      // 回调通知
      widget.onDataLoaded?.call(banners);
    }
  }

  /// 预加载图片
  Future<void> _preloadImages() async {
    if (_imageUrls.isEmpty || !mounted) return;

    // 预加载所有图片
    final preloadFutures = _imageUrls.map((imageUrl) async {
      if (!_preloadedImages.contains(imageUrl)) {
        await precacheImage(NetworkImage(imageUrl), context);
        if (mounted) {
          _preloadedImages.add(imageUrl);
        }
      }
    });

    await Future.wait(preloadFutures);

    // 预加载完成后自动播放
    if (mounted) {
      _startAutoPlay();
    }
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    // 根据应用生命周期管理自动播放
    switch (state) {
      case AppLifecycleState.paused:
      case AppLifecycleState.inactive:
        _isVisible = false;
        _pauseAutoPlay();
        break;
      case AppLifecycleState.resumed:
        _isVisible = true;
        _startAutoPlay();
        break;
      default:
        break;
    }
  }

  // 暂停自动播放
  void _pauseAutoPlay() {
    _timer?.cancel();
    _timer = null;
  }

  /// 滑动开始处理
  void _onScrollStart() {
    _isScrolling = true;
    _pauseAutoPlay();
    _scrollEndTimer?.cancel();
  }

  /// 滑动结束处理
  void _onScrollEnd() {
    _isScrolling = false;
    _scrollEndTimer?.cancel();
    // 延迟后恢复自动播放
    _scrollEndTimer = Timer(_scrollEndDelay, () {
      if (!_isScrolling && mounted) {
        _startAutoPlay();
      }
    });
  }

  void _initializeController() {
    final initialPage = widget.infiniteLoop ? _infiniteLoopInitialPage : 0;
    _pageController = PageController(initialPage: initialPage);
    _currentIndex = 0;
  }

  void _startAutoPlay() {
    // 停止现有Timer
    _pauseAutoPlay();

    // 检查自动播放条件
    if (!_canAutoPlay()) return;

    _timer = Timer.periodic(
      Duration(milliseconds: widget.autoPlayInterval),
      (timer) {
        if (!_canAutoPlay() || !_pageController.hasClients) {
          timer.cancel();
          _timer = null;
          return;
        }
        _nextPage();
      },
    );
  }

  /// 检查是否可以自动播放
  bool _canAutoPlay() {
    return widget.autoPlay &&
        _imageUrls.length > 1 &&
        mounted &&
        _isVisible &&
        !_isScrolling;
  }

  void _nextPage() {
    if (!mounted || !_pageController.hasClients || _isAnimating || _isScrolling) return;

    _isAnimating = true;
    _isProgrammaticScroll = true;
    final currentPage = _pageController.page?.round() ?? 0;

    _pageController.animateToPage(
      currentPage + 1,
      duration: _animationDuration,
      curve: Curves.easeInOut,
    ).whenComplete(() {
      if (mounted) {
        _isAnimating = false;
        _isProgrammaticScroll = false;
      }
    });
  }

  void _onPageChanged(int index) {
    if (!mounted || _imageUrls.isEmpty) return;

    final newIndex = index % _imageUrls.length;
    if (_currentIndex != newIndex) {
      _currentIndex = newIndex;
      setState(() {});
      widget.onPageChanged?.call(_currentIndex);
    }
  }

  Widget _buildItem(String imageUrl, int realIndex) {
    return GestureDetector(
      onTap: () async {
        // 点击时暂停自动播放
        _pauseAutoPlay();

        // 使用BannerUtils处理点击
        final banner = _banners[realIndex];
        await BannerUtils.onItemClick(
          context,
          banner,
          pageCode: widget.pageCode,
        );
      },
      child: ClipRRect(
        borderRadius: BorderRadius.circular(widget.borderRadius),
        child: Image.network(
          imageUrl,
          fit: widget.fit,
          errorBuilder: (context, error, stackTrace) {
            return Container(color: Colors.white);
          },
        ),
      ),
    );
  }

  Widget _buildIndicator() {
    if (!widget.showIndicator || _imageUrls.length <= 1) {
      return const SizedBox.shrink();
    }

    return Positioned(
      bottom: 2.h,
      left: 0,
      right: 0,
      child: Center(
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: List.generate(
            _imageUrls.length,
                (index) => Container(
              width: widget.indicatorSize.w,
              height: widget.indicatorSize.w,
              margin: EdgeInsets.symmetric(horizontal: 2.w),
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: index == _currentIndex
                    ? widget.indicatorActiveColor
                    : widget.indicatorColor,
              ),
            ),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    if (_imageUrls.isEmpty) {
      return SizedBox(
        height: widget.height,
        child: Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(widget.borderRadius),
          ),
        ),
      );
    }

    return SizedBox(
      height: widget.height,
      child: Stack(
        children: [
          NotificationListener<ScrollNotification>(
            onNotification: (notification) {
              // 处理手动滑动
              if (!_isProgrammaticScroll) {
                if (notification is ScrollStartNotification) {
                  _onScrollStart();
                } else if (notification is ScrollEndNotification) {
                  _onScrollEnd();
                }
              }
              return false;
            },
            child: PageView.builder(
              controller: _pageController,
              onPageChanged: _onPageChanged,
              itemCount: widget.infiniteLoop ? null : _imageUrls.length,
              itemBuilder: (context, index) {
                final realIndex = index % _imageUrls.length;
                return _buildItem(_imageUrls[realIndex], realIndex);
              },
            ),
          ),
          _buildIndicator(),
        ],
      ),
    );
  }

  /// 刷新数据
  Future<void> refresh() async {
    await _loadBannerData();
  }

  @override
  void dispose() {
    _pauseAutoPlay();
    _scrollEndTimer?.cancel();
    _pageController.dispose();
    _preloadedImages.clear();
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }
}
