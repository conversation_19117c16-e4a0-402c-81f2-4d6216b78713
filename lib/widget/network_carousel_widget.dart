import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/data/utils_data/carousel/rep/carousel_rep.dart';
import 'package:gdjg_pure_flutter/data/utils_data/carousel/entity/adposition_detail_biz_model.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/banner_utils.dart';

/*
      网络轮播组件使用
          NetworkCarouselWidget(
            code: 'JGJZ_HOME_GROUP_BANER',
            pageCode: 'JGJZ_HOME_GROUP_BANER',
          )
 */

/// 网络轮播组件
/// 支持通过网络请求获取Banner数据
class NetworkCarouselWidget extends StatefulWidget {
  /// 广告位标识
  final String code;

  /// 页面标识（用于埋点统计）
  final String? pageCode;

  /// 广告类型，可选
  final String? type;

  /// 是否大广告，可选
  final int? big;

  /// 轮播图高度，默认48
  final double height;

  /// 是否自动播放，默认true
  final bool autoPlay;

  /// 自动播放间隔(毫秒)，默认3000
  final int autoPlayInterval;

  /// 是否显示指示器，默认true
  final bool showIndicator;

  /// 指示器颜色，默认#91B2C7
  final Color indicatorColor;

  /// 激活指示器颜色，默认#007AFC
  final Color indicatorActiveColor;

  /// 指示器大小，默认6.0
  final double indicatorSize;

  /// 页面切换回调
  final Function(int index)? onPageChanged;

  /// 是否无限循环，默认true
  final bool infiniteLoop;

  /// 图片适配方式，默认BoxFit.cover
  final BoxFit fit;

  /// 圆角半径，默认8.0
  final double borderRadius;

  /// 数据加载完成回调
  final Function(List<AdpositionDetailABizModel> banners)? onDataLoaded;

  /// 数据加载失败回调
  final Function(String error)? onLoadError;

  const NetworkCarouselWidget({
    super.key,
    required this.code,
    this.pageCode,
    this.type,
    this.big,
    this.height = 48,
    this.autoPlay = true,
    this.autoPlayInterval = 3000,
    this.showIndicator = true,
    this.indicatorColor = const Color(0x7F91B2C7),
    this.indicatorActiveColor = const Color(0xFF007AFC),
    this.indicatorSize = 6.0,
    this.onPageChanged,
    this.infiniteLoop = true,
    this.fit = BoxFit.cover,
    this.borderRadius = 8.0,
    this.onDataLoaded,
    this.onLoadError,
  });

  @override
  State<NetworkCarouselWidget> createState() => _NetworkCarouselWidgetState();
}

class _NetworkCarouselWidgetState extends State<NetworkCarouselWidget>
    with WidgetsBindingObserver {
  late PageController _pageController;
  Timer? _timer;
  int _currentIndex = 0;
  bool _isVisible = true;

  // 网络请求相关
  final CarouselRep _carouselRep = CarouselRep();
  List<AdpositionDetailABizModel> _banners = [];
  bool _isLoading = true;
  String? _errorMessage;

  /// 获取图片URL列表
  List<String> get _imageUrls {
    return _banners.map((banner) => banner.img).toList();
  }

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _initializeController();
    _loadBannerData();
  }

  /// 加载Banner数据
  Future<void> _loadBannerData() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      final banners = await _carouselRep.getBannerByCode(
        widget.code,
        type: widget.type,
        big: widget.big,
      );

      if (mounted) {
        setState(() {
          _banners = banners;
          _isLoading = false;
        });

        // 数据加载完成后启动自动播放
        _startAutoPlay();

        // 回调通知
        widget.onDataLoaded?.call(banners);
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
          _errorMessage = '加载失败: $e';
        });

        // 错误回调
        widget.onLoadError?.call(_errorMessage!);
      }
    }
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    // 根据应用生命周期管理自动播放
    switch (state) {
      case AppLifecycleState.paused:
      case AppLifecycleState.inactive:
        _isVisible = false;
        _pauseAutoPlay();
        break;
      case AppLifecycleState.resumed:
        _isVisible = true;
        _resumeAutoPlay();
        break;
      default:
        break;
    }
  }

  // 暂停自动播放
  void _pauseAutoPlay() {
    _timer?.cancel();
    _timer = null;
  }

  // 恢复自动播放
  void _resumeAutoPlay() {
    _startAutoPlay();
  }

  void _initializeController() {
    // 无限循环：从一个大数的中间位置开始
    final initialPage = widget.infiniteLoop ? 10000 : 0;
    _pageController = PageController(initialPage: initialPage);
    _currentIndex = 0;
  }

  void _startAutoPlay() {
    // 检查自动播放条件
    if (!widget.autoPlay ||
        _imageUrls.length <= 1 ||
        !mounted ||
        !_isVisible ||
        _isLoading) {
      return;
    }

    // 如果已有Timer，先停止
    if (_timer != null) {
      _pauseAutoPlay();
    }

    _timer = Timer.periodic(
      Duration(milliseconds: widget.autoPlayInterval),
          (timer) {
        if (!mounted || !_isVisible || !_pageController.hasClients) {
          timer.cancel();
          _timer = null;
          return;
        }
        _nextPage();
      },
    );
  }

  void _nextPage() {
    if (!mounted || !_pageController.hasClients) return;

    try {
      final currentPage = _pageController.page?.round() ?? 0;
      _pageController.animateToPage(
        currentPage + 1,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    } catch (e) {
      // 忽略错误，避免影响用户体验
    }
  }

  void _onPageChanged(int index) {
    if (!mounted) return;

    // 计算真实索引
    _currentIndex = index % _imageUrls.length;

    setState(() {});
    widget.onPageChanged?.call(_currentIndex);
  }

  Widget _buildItem(String imageUrl, int realIndex) {
    return GestureDetector(
      onTap: () async {
        // 使用BannerUtils处理点击
        final banner = _banners[realIndex];
        await BannerUtils.onItemClick(
          context,
          banner,
          pageCode: widget.pageCode,
        );
      },
      child: ClipRRect(
        borderRadius: BorderRadius.circular(widget.borderRadius),
        child: Image.network(
          imageUrl,
          fit: widget.fit,
          // 图片加载状态显示
          loadingBuilder: (context, child, loadingProgress) {
            if (loadingProgress == null) return child;

            final progress = loadingProgress.expectedTotalBytes != null
                ? loadingProgress.cumulativeBytesLoaded / loadingProgress.expectedTotalBytes!
                : null;

            return Container(
              color: Colors.grey[100],
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    SizedBox(
                      width: 24.w,
                      height: 24.h,
                      child: CircularProgressIndicator(
                        strokeWidth: 2.0,
                        value: progress,
                        backgroundColor: Colors.grey[300],
                      ),
                    ),
                    if (progress != null) ...[
                      SizedBox(height: 8.h),
                      Text(
                        '${(progress * 100).toInt()}%',
                        style: TextStyle(
                          fontSize: 12.sp,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            );
          },
          // 图片加载失败状态显示
          errorBuilder: (context, error, stackTrace) {
            return Container(
              color: Colors.grey[200],
              child: Center(
                child: Icon(
                  Icons.broken_image_outlined,
                  color: Colors.grey[400],
                  size: 28.w,
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildIndicator() {
    if (!widget.showIndicator || _imageUrls.length <= 1) {
      return const SizedBox.shrink();
    }

    return Positioned(
      bottom: 2.h,
      left: 0,
      right: 0,
      child: Center(
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: List.generate(
            _imageUrls.length,
                (index) => Container(
              width: widget.indicatorSize.w,
              height: widget.indicatorSize.w,
              margin: EdgeInsets.symmetric(horizontal: 2.w),
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: index == _currentIndex
                    ? widget.indicatorActiveColor
                    : widget.indicatorColor,
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// 构建加载状态
  Widget _buildLoadingState() {
    return SizedBox(
      height: widget.height,
      child: Container(
        decoration: BoxDecoration(
          color: Colors.grey[100],
          borderRadius: BorderRadius.circular(widget.borderRadius),
        ),
        child: const Center(
          child: CircularProgressIndicator(
            strokeWidth: 2.0,
          ),
        ),
      ),
    );
  }

  /// 构建错误状态
  Widget _buildErrorState() {
    return SizedBox(
      height: widget.height,
      child: Container(
        decoration: BoxDecoration(
          color: Colors.grey[200],
          borderRadius: BorderRadius.circular(widget.borderRadius),
        ),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error_outline,
                color: Colors.grey[400],
                size: 32.w,
              ),
              SizedBox(height: 8.h),
              Text(
                _errorMessage ?? '加载失败',
                style: TextStyle(
                  color: Colors.grey[500],
                  fontSize: 12.sp,
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 8.h),
              GestureDetector(
                onTap: _loadBannerData,
                child: Container(
                  padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
                  decoration: BoxDecoration(
                    color: Colors.blue,
                    borderRadius: BorderRadius.circular(4.r),
                  ),
                  child: Text(
                    '重试',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 12.sp,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建空数据状态
  Widget _buildEmptyState() {
    return SizedBox(
      height: widget.height,
      child: Container(
        decoration: BoxDecoration(
          color: Colors.grey[200],
          borderRadius: BorderRadius.circular(widget.borderRadius),
        ),
        child: Center(
          child: Icon(
            Icons.image_not_supported_outlined,
            color: Colors.grey[400],
            size: 32.w,
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    // 加载状态
    if (_isLoading) {
      return _buildLoadingState();
    }

    // 错误状态
    if (_errorMessage != null) {
      return _buildErrorState();
    }

    // 空数据状态
    if (_imageUrls.isEmpty) {
      return _buildEmptyState();
    }

    return SizedBox(
      height: widget.height,
      child: Stack(
        children: [
          PageView.builder(
            controller: _pageController,
            onPageChanged: _onPageChanged,
            itemCount: widget.infiniteLoop ? null : _imageUrls.length,
            itemBuilder: (context, index) {
              final realIndex = index % _imageUrls.length;
              return _buildItem(_imageUrls[realIndex], realIndex);
            },
          ),
          _buildIndicator(),
        ],
      ),
    );
  }

  /// 刷新数据
  Future<void> refresh() async {
    await _loadBannerData();
  }

  @override
  void dispose() {
    _pauseAutoPlay();
    _pageController.dispose();
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }
}
