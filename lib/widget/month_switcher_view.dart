import 'package:flutter/material.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/month_select_util.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/toast_util.dart';

typedef OnMonthRangeSelected = void Function(DateTime currentTime);

/// 月份切换控件
class MonthSwitcherView extends StatefulWidget {
  final DateTime? initialTime;
  final OnMonthRangeSelected onYearMonthSelected;

  const MonthSwitcherView({
    super.key,
    this.initialTime,
    required this.onYearMonthSelected,
  });

  @override
  State<MonthSwitcherView> createState() => _MonthSwitcherViewState();
}

class _MonthSwitcherViewState extends State<MonthSwitcherView> {
  late DateTime _time;
  final DateTime now = DateTime.now();

  @override
  void initState() {
    super.initState();
    _time = widget.initialTime ?? now;
  }

  @override
  void didUpdateWidget(covariant MonthSwitcherView oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.initialTime != widget.initialTime) {
      setState(() {
        _time = widget.initialTime ?? now;
      });
    }
  }

  void _notifyParent(DateTime time) {
    widget.onYearMonthSelected(time);
  }

  /// 显示月份选择器
  void _showMonthPicker() {
    MonthSelectUtil.show(
      context: context,
      initialYear: _time.year,
      initialMonth: _time.month,
      onSelected: (year, month) {
        setState(() {
          _time = DateTime(year, month);
        });
        _notifyParent(_time);
      },
    );
  }

  void _changeMonth(int delta) {
    if (delta == 1) {
      // 获取当前时间
      final now = DateTime.now();

      // 构造切换后的时间
      final nextMonth = DateTime(_time.year, _time.month + delta);

      // 如果切换后的时间大于当前时间，则不允许切换
      if (nextMonth.isAfter(now)) {
        ToastUtil.showToast("下个月还没有到哟");
        return;
      }
    }
    setState(() {
      _time = DateTime(_time.year, _time.month + delta);
    });
    _notifyParent(_time);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 40,
      width: double.infinity,
      color: const Color(0xFFF0F0F0),
      child: Row(
        children: [
          // 左侧“上一月”按钮
          GestureDetector(
            onTap: () {
              _changeMonth(-1);
            },
            child: SizedBox(
              width: 98,
              height: double.infinity,
              child: Center(
                child: Text('上一月', style: _monthButtonTextStyle),
              ),
            ),
          ),
          // 中间区域（带背景图）
          Expanded(
            flex: 1,
            child: GestureDetector(
              onTap: () {
                _showMonthPicker();
              },
              child: Container(
                decoration: BoxDecoration(
                  image: DecorationImage(
                    image: AssetImage(
                        "assets/images/group/waa_ic_psca_date_bg.webp"),
                    fit: BoxFit.fill,
                  ),
                ),
                child: Center(
                  child: _monthDisplayRow("${_time.year}年${_time.month}月"),
                ),
              ),
            ),
          ),

          // 右侧按钮
          GestureDetector(
            onTap: () {
              _changeMonth(1);
            },
            child: SizedBox(
              width: 98,
              height: double.infinity,
              child: Center(
                child: Text('下一月', style: _monthButtonTextStyle),
              ),
            ),
          ),
        ],
      ),
    );
  }

  ///判断是否到下个月
  bool _isSameOrBeforeCurrentMonth(DateTime date) {
    final now = DateTime.now();
    return date.year < now.year ||
        (date.year == now.year && date.month <= now.month);
  }

  Widget _monthDisplayRow(String monthText) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          monthText,
          style: _monthTextStyle,
        ),
        SizedBox(width: 4), // 更好的间距控制
        Image.asset(
          "assets/images/group/waa_svg_select_time.webp",
          width: 12,
          height: 12,
        ),
      ],
    );
  }

  // --- UI 样式和辅助方法 ---
  TextStyle get _monthTextStyle => const TextStyle(
        fontSize: 16,
        color: Color(0xFF323233),
        fontWeight: FontWeight.bold,
      );

  TextStyle get _monthButtonTextStyle => const TextStyle(
        fontSize: 14,
        color: Color(0xFF323233),
      );
}
