import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 60秒倒计时按钮控件
class CountdownButton extends StatefulWidget {
  /// 默认显示的文字
  final String defaultText;

  /// 倒计时显示的文字格式，{count}会被替换为倒计时数字
  final String countdownText;

  /// 点击事件回调 返回值控制是否开启倒计时
  final Function(void Function([int? countdownDuration]) startCountDown)? onPressed;
  /// 倒计时时长（秒），默认60秒
  final int countdownDuration;

  /// 按钮样式
  final ButtonStyle? style;

  /// 文字样式
  final TextStyle? textStyle;

  /// 是否禁用按钮
  final bool disabled;

  const CountdownButton({
    super.key,
    this.defaultText = '获取验证码',
    this.countdownText = '{count}s后重新获取',
    this.onPressed,
    this.countdownDuration = 60,
    this.style,
    this.textStyle,
    this.disabled = false,
  });

  @override
  State<CountdownButton> createState() => _CountdownButtonState();
}

class _CountdownButtonState extends State<CountdownButton> {
  Timer? _timer;
  int _currentCount = 0;
  bool _isCountingDown = false;

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  /// 开始倒计时
  void _startCountdown([int? countdownDuration]) {
    if (_isCountingDown || widget.disabled) return;

    setState(() {
      _isCountingDown = true;
      _currentCount = countdownDuration ?? widget.countdownDuration;
    });

    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        _currentCount--;
      });

      if (_currentCount <= 0) {
        _stopCountdown();
      }
    });
  }

  /// 停止倒计时
  void _stopCountdown() {
    _timer?.cancel();
    setState(() {
      _isCountingDown = false;
      _currentCount = 0;
    });
  }

  /// 获取当前显示的文字
  String get _displayText {
    if (_isCountingDown) {
      return widget.countdownText
          .replaceAll('{count}', _currentCount.toString());
    }
    return widget.defaultText;
  }

  /// 判断按钮是否应该被禁用
  bool get _isDisabled {
    return widget.disabled || _isCountingDown;
  }

  @override
  Widget build(BuildContext context) {
    return TextButton(
      onPressed: _isDisabled ? null : (){
        widget.onPressed?.call(_startCountdown);
      },
      style: widget.style ??
          TextButton.styleFrom(
            minimumSize: Size.zero,
            padding: EdgeInsets.zero,
            // 禁用点击效果
            overlayColor: Colors.transparent,
          ),
      child: Text(
        _displayText,
        style: widget.textStyle ??
            TextStyle(
              color: _isDisabled ? Colors.black45 : Color(0xFF0092FF),
              fontSize: 15.sp,
            ),
      ),
    );
  }
}
