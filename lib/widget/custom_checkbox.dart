import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';

/// 自定义圆形复选框组件
class CustomCheckbox extends StatelessWidget {
  /// 是否选中
  final bool isSelected;
  
  /// 点击回调
  final VoidCallback onTap;
  
  /// 复选框尺寸，默认16.w
  final double? size;
  
  /// 选中时的颜色，默认使用主题色
  final Color? selectedColor;
  
  /// 边框颜色，默认使用主题色
  final Color? borderColor;
  
  /// 选中时图标颜色，默认白色
  final Color? iconColor;

  const CustomCheckbox({
    super.key,
    required this.isSelected,
    required this.onTap,
    this.size,
    this.selectedColor,
    this.borderColor,
    this.iconColor,
  });

  @override
  Widget build(BuildContext context) {
    final checkboxSize = size ?? 16.w;
    final primaryColor = selectedColor ?? ColorsUtil.primaryColor;
    final checkBorderColor = borderColor ?? ColorsUtil.primaryColor;
    final checkIconColor = iconColor ?? Colors.white;

    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: checkboxSize,
        height: checkboxSize,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: isSelected ? primaryColor : Colors.transparent,
          border: Border.all(
            color: checkBorderColor,
            width: 1,
          ),
        ),
        child: isSelected
            ? Icon(
                Icons.check,
                color: checkIconColor,
                size: (checkboxSize * 0.875).toDouble(), // 14/16 = 0.875
              )
            : null,
      ),
    );
  }
}
