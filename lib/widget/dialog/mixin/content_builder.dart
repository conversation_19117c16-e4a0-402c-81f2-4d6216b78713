import 'package:flutter/cupertino.dart';

import 'builder_units.dart';

mixin ContentBuilder {
  Widget? _content;

  Widget? get content => _content;

  void setContentByText(String? text,
          {TextStyle? style = BuilderUnits.undefinedTextStyle}) =>
      _content = BuilderUnits.createTextByText(
          text, style, BuilderUnits.contentTextStyle);

  void setContentBySpans(List<TextSpan>? spans) =>
      _content = BuilderUnits.createTextBySpans(spans);

  void setContentByWidget(Text? widget) => _content = widget;
}
