import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../utils/route_util/route_api/yp_route.dart';
import '../../../utils/ui_util/colors_util.dart';

class BuilderUnits {
  static const TextStyle undefinedTextStyle = TextStyle();
  static const BoxDecoration undefinedDecoration = BoxDecoration();

  static TextStyle get titleTextStyle => TextStyle(
        color: ColorsUtil.black85,
        fontSize: 18.sp,
        fontWeight: FontWeight.bold,
      );

  static TextStyle get contentTextStyle => TextStyle(
        color: ColorsUtil.black85,
        fontSize: 14.sp,
      );

  static TextStyle get inputTitleStyle => TextStyle(
        color: ColorsUtil.black85,
        fontSize: 14.sp,
      );

  static TextStyle get negativeTextStyle => TextStyle(
        color: ColorsUtil.black85,
        fontSize: 14.sp,
      );

  static TextStyle get positiveTextStyle => TextStyle(
        color: Colors.white,
        fontSize: 14.sp,
      );

  static BoxDecoration get negativeFilledDecoration => BoxDecoration(
        color: ColorsUtil.ypGreyColor,
        borderRadius: BorderRadius.all(Radius.circular(8)),
      );

  static BoxDecoration get positiveFilledDecoration => BoxDecoration(
        color: ColorsUtil.ypPrimaryColor,
        borderRadius: BorderRadius.all(Radius.circular(8)),
      );

  static void onNegativeButtonTapDefault<T>(T t) => YPRoute.closeDialog();

  static resolve<T>(T target, T fallback) {
    if (target is TextStyle) {
      return identical(target, undefinedTextStyle) ? fallback : target;
    } else if (target is BoxDecoration) {
      return identical(target, undefinedDecoration) ? fallback : target;
    } else {
      return fallback;
    }
  }

  static Text? createTextByText(
          String? text, TextStyle? style, TextStyle? fallback) =>
      text == null
          ? null
          : Text(text, style: BuilderUnits.resolve(style, fallback));

  static Text? createTextBySpans(List<TextSpan>? spans) =>
      spans == null ? null : Text.rich(TextSpan(children: spans));

  static Widget? createDecoratedWidget(
    Widget? widget,
    BoxDecoration? decoration,
    BoxDecoration? fallback,
  ) =>
      widget == null
          ? null
          : Container(
              decoration: BuilderUnits.resolve(decoration, fallback),
              alignment: Alignment.center,
              child: widget,
            );
}
