import 'package:flutter/material.dart';

import 'calendar.dart';
import 'dayevent/DayEvent.dart';
import 'dynamic_height_calendar.dart';

class ScrollPage extends StatelessWidget {
  const ScrollPage({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Dynamic Height PageView',
      theme: ThemeData(primarySwatch: Colors.blue),
      home: const DynamicPageViewInScrollView(),
    );
  }
}

class DynamicPageViewInScrollView extends StatefulWidget {
  const DynamicPageViewInScrollView({super.key});

  @override
  State<DynamicPageViewInScrollView> createState() =>
      _DynamicPageViewInScrollViewState();
}

class _DynamicPageViewInScrollViewState
    extends State<DynamicPageViewInScrollView> {
  final PageController _pageController = PageController();
  final List<GlobalKey> _pageKeys = List.generate(3, (_) => GlobalKey());
  final ValueNotifier<double> _currentHeight = ValueNotifier<double>(200);
  int _currentPageIndex = 0;

  // 页面内容列表（可动态修改）
  final List<Widget> _pages = [
    _buildPage(0, Colors.red),
    _buildPage(1, Colors.blue),
    _buildPage(2, Colors.green),
  ];

  static Widget _buildPage(int index, Color color) {
    final events = {
      DateTime(2025, 6, 15): [
        DayEvent(type: DayEventType.Remark),
        DayEvent(type: DayEventType.Borrowing),
        DayEvent(type: DayEventType.Settlement),
        DayEvent(type: DayEventType.DailyPaid, value: 1),
        DayEvent(type: DayEventType.LumpSum, value: 99),
        DayEvent(type: DayEventType.Temporary, value: 11),
        DayEvent(type: DayEventType.Workload, value: 15),
      ],
      DateTime(2025, 6, 16): [DayEvent(type: DayEventType.Remark)],
    };
    return FullEventCalendar(
      currentDate: DateTime.now(),
      events: index == 1 ? events : {},
    );
  }

  // 关键方法：更新当前高度
  void _updateHeight() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final context = _pageKeys[_currentPageIndex].currentContext;
      if (context != null && context.mounted) {
        final renderBox = context.findRenderObject() as RenderBox?;
        if (renderBox != null) {
          final newHeight = renderBox.size.height;
          if (_currentHeight.value != newHeight) {
            _currentHeight.value = newHeight;
          }
        }
      }
    });
  }

  @override
  void initState() {
    super.initState();
    // 初始加载后计算高度
    WidgetsBinding.instance.addPostFrameCallback((_) => _updateHeight());
  }

  @override
  void dispose() {
    _pageController.dispose();
    _currentHeight.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: CustomScrollView(
        slivers: [
          SliverAppBar(
            title: const Text('真正动态高度 PageView'),
            pinned: true,
            expandedHeight: 150,
            flexibleSpace: FlexibleSpaceBar(
              background: Container(color: Colors.blue[100]),
            ),
          ),

          // 动态高度部分
          DynamicHeightCalendar(),
          // ValueListenableBuilder<double>(
          //   valueListenable: _currentHeight,
          //   builder: (context, height, _) {
          //     return SliverToBoxAdapter(
          //       child: SizedBox(
          //         height: height,
          //         child: PageView.builder(
          //           controller: _pageController,
          //           itemCount: _pages.length,
          //           onPageChanged: (index) {
          //             setState(() => _currentPageIndex = index);
          //             _updateHeight();
          //           },
          //           itemBuilder: (context, index) {
          //             return SingleChildScrollView(
          //               // 确保超长内容可滚动
          //               physics: const NeverScrollableScrollPhysics(), // 禁用内部滚动
          //               child: KeyedSubtree(
          //                 key: _pageKeys[index],
          //                 child: _pages[index],
          //               ),
          //             );
          //           },
          //         ),
          //       ),
          //     );
          //   },
          // ),
          // 其他内容
          SliverList(
            delegate: SliverChildBuilderDelegate(
              (_, index) => ListTile(title: Text('列表项 $index')),
              childCount: 20,
            ),
          ),
        ],
      ),
    );
  }
}
