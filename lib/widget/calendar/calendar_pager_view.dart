import 'dart:async';
import 'package:flutter/material.dart';
import 'package:gdjg_pure_flutter/widget/calendar/utils.dart';

class CalendarPagerView extends StatefulWidget {
  final DateTime startDate;
  final DateTime endDate;
  final Widget Function(BuildContext context, DateTime date) itemBuilder;
  final void Function(BuildContext? context, DateTime currentDate)?
      onPageChanged;
  final double viewportFraction;
  final double pageSpacing;
  final DateTime? initialDate;
  PageController? pageController;

  CalendarPagerView(
      {super.key,
      required this.startDate,
      required this.endDate,
      required this.itemBuilder,
      this.onPageChanged,
      this.viewportFraction = 0.9,
      this.pageSpacing = 10,
      this.initialDate,
      this.pageController})
      : assert(viewportFraction > 0 && viewportFraction <= 1),
        assert(
          !endDate.isBefore(startDate),
          'endDate cannot be before startDate',
        );

  @override
  State<CalendarPagerView> createState() => _CalendarPagerViewState();
}

class _CalendarPagerViewState extends State<CalendarPagerView> {
  late final PageController _pageController;
  late int _currentPage;
  late int _totalPages;
  late List<GlobalKey> _pageKeys;

  // 滑动状态相关变量
  bool _isScrolling = false;
  Timer? _scrollEndTimer;
  int _lastReportedPage = 0;

  @override
  void initState() {
    super.initState();
    _totalPages = _calculateTotalPages();
    _pageKeys = List.generate(_totalPages, (_) => GlobalKey());
    _currentPage = calculatePageIndex(
        initDate: widget.initialDate ?? DateTime.now(),
        startDate: widget.startDate,
        endDate: widget.endDate);
    _pageController = widget.pageController ??
        PageController(
          viewportFraction: widget.viewportFraction,
          initialPage: _currentPage,
        );

    _pageController.addListener(() {
      // 确保当前 ScrollMetrics 是 PageMetrics 类型
      if (_pageController.position is PageMetrics) {
        final PageMetrics metrics = _pageController.position as PageMetrics;

        // 只在滑动结束后执行

      }
    });
    // 初始加载后计算高度
    WidgetsBinding.instance.addPostFrameCallback((_) {
      widget.onPageChanged?.call(
        _pageKeys[_currentPage].currentContext,
        _dateForIndex(_currentPage),
      );
    });
  }

  int _calculateTotalPages() {
    final yearDiff = widget.endDate.year - widget.startDate.year;
    return yearDiff * 12 + widget.endDate.month - widget.startDate.month + 1;
  }

  DateTime _dateForIndex(int index) {
    final yearOffset = (widget.startDate.month + index - 1) ~/ 12;
    final month = (widget.startDate.month + index - 1) % 12 + 1;
    return DateTime(widget.startDate.year + yearOffset, month);
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.white,
      child: PageView.builder(
        controller: _pageController,
        itemCount: _totalPages,
        onPageChanged: (index) {
          setState(() => _currentPage = index);
          widget.onPageChanged?.call(
            _pageKeys[index].currentContext,
            _dateForIndex(index),
          );
        },
        padEnds: false,
        physics: const ClampingScrollPhysics(),
        itemBuilder: (context, index) {
          return SingleChildScrollView(
            // 确保超长内容可滚动
            physics: const NeverScrollableScrollPhysics(),
            child: KeyedSubtree(
              key: _pageKeys[index],
              child: widget.itemBuilder(context, _dateForIndex(index)),
            ),
          );
        },
      ),
    );
  }
}
