import 'package:flutter/material.dart';
import 'package:gdjg_pure_flutter/init_module/init_route.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/widget/calendar/utils.dart';
import 'calendar.dart';
import 'calendar_pager_view.dart';
import 'dayevent/DayEvent.dart';

class DynamicHeightCalendar extends StatefulWidget {
  final ValueChanged<DateTime> onValueChange;

  final Map<DateTime, List<DayEvent>> events;

  const DynamicHeightCalendar(
      {super.key, required this.onValueChange, required this.events});

  @override
  State<DynamicHeightCalendar> createState() => DynamicHeightCalendarState();
}

class DynamicHeightCalendarState extends State<DynamicHeightCalendar> {
  DateTime currentDate = DateTime.now();

  BuildContext? _currentContext;

  DateTime startDate = DateTime.now().copyWith(year: 2000, month: 1, day: 1);
  DateTime endDate = DateTime.now();

  late PageController _pageController;

  final ValueNotifier<double> _currentHeight = ValueNotifier<double>(200);

  @override
  void initState() {
    super.initState();
    _pageController = PageController(
      initialPage: calculatePageIndex(
        initDate: currentDate,
        startDate: startDate,
        endDate: endDate,
      ),
    );
  }

  @override
  void didUpdateWidget(covariant DynamicHeightCalendar oldWidget) {
    super.didUpdateWidget(oldWidget);
    updateHeight();
  }

  @override
  void dispose() {
    super.dispose();
    _pageController.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<double>(
      valueListenable: _currentHeight,
      builder: (context, height, _) {
        return SliverToBoxAdapter(
          child: SizedBox(
            height: height,
            child: CalendarPagerView(
              onPageChanged: (context, date) {
                _currentContext = context;
                updateHeight();
                widget.onValueChange(date);
              },
              viewportFraction: 1,
              initialDate: currentDate,
              pageController: _pageController,
              startDate: DateTime.now().copyWith(year: 2000),
              endDate: DateTime.now(),
              itemBuilder: (context, cur) {
                return FullEventCalendar(
                  onTap: (date) {
                    YPRoute.openPage(RouteNameCollection.dailyFlow);
                  },
                  currentDate: cur,
                  events: widget.events,
                );
              },
            ),
          ),
        );
      },
    );
  }

  // 关键方法：更新当前高度
  void updateHeight() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_currentContext != null && _currentContext!.mounted) {
        final renderBox = _currentContext!.findRenderObject() as RenderBox?;
        if (renderBox != null) {
          renderBox.markNeedsLayout();
          final newHeight = renderBox.size.height;
          if (_currentHeight.value != newHeight) {
            _currentHeight.value = newHeight;
          }
        }
      }
    });
  }

  // 切换月份
  void changeMonth(DateTime date) {
    setState(() {
      currentDate = date;
      final index = calculatePageIndex(
        initDate: date,
        startDate: startDate,
        endDate: endDate,
      );
      debugPrint('Date:${date},Index:$index');
      _pageController.animateToPage(
        index,
        duration: Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    });
  }
}
