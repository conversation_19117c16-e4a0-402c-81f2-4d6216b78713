import 'package:flutter/material.dart';

class CalendarWeek extends StatelessWidget {
  const CalendarWeek({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      child: Row(
        children: [
          _weekdayHeader(label: '日'),
          _weekdayHeader(label: '一'),
          _weekdayHeader(label: '二'),
          _weekdayHeader(label: '三'),
          _weekdayHeader(label: '四'),
          _weekdayHeader(label: '五'),
          _weekdayHeader(label: '六'),
        ],
      ),
    );
  }

  Widget _weekdayHeader({String? label}) {
    return Expanded(
      child: Center(
        child: Text(
          label ?? "",
          style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 14),
        ),
      ),
    );
  }
}
