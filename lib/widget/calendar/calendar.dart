import 'package:flutter/material.dart';
import 'package:gdjg_pure_flutter/widget/calendar/calendar_day.dart';
import 'package:gdjg_pure_flutter/widget/calendar/dayevent/DayEvent.dart';

class FullEventCalendar extends StatefulWidget {
  final DateTime currentDate;
  final Map<DateTime, List<DayEvent>> events;
  final Function(DateTime date) onTap;

  const FullEventCalendar(
      {Key? key,
      required this.currentDate,
      this.events = const {},
      required this.onTap})
      : super(key: key);

  @override
  _FullEventCalendarState createState() => _FullEventCalendarState();
}

class _FullEventCalendarState extends State<FullEventCalendar> {
  late DateTime _displayedMonth;

  @override
  void initState() {
    super.initState();
    _displayedMonth = DateTime(
      widget.currentDate.year,
      widget.currentDate.month,
      1,
    );
  }

  int _getDaysInMonth(int year, int month) {
    return DateTime(year, month + 1, 0).day;
  }

  @override
  Widget build(BuildContext context) {
    final daysInMonth = _getDaysInMonth(
      _displayedMonth.year,
      _displayedMonth.month,
    );
    final firstWeekday = _displayedMonth.weekday % 7;

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        _buildCalendarGrid(firstWeekday, daysInMonth),
      ],
    );
  }

  Widget _buildCalendarGrid(int firstWeekday, int daysInMonth) {
    final totalCells = daysInMonth + firstWeekday;
    final totalWeeks = (totalCells / 7).ceil();
    return Column(
      children: List.generate(totalWeeks, (weekIndex) {
        return _CalendarWeekRow(
          weekIndex: weekIndex,
          firstWeekday: firstWeekday,
          daysInMonth: daysInMonth,
          displayedMonth: _displayedMonth,
          events: widget.events,
          onTap: widget.onTap,
        );
      }),
    );
  }
}

class _CalendarWeekRow extends StatelessWidget {
  final int weekIndex;
  final int firstWeekday;
  final int daysInMonth;
  final DateTime displayedMonth;
  final Map<DateTime, List<DayEvent>> events;
  final Function(DateTime date) onTap;

  const _CalendarWeekRow({
    required this.weekIndex,
    required this.firstWeekday,
    required this.daysInMonth,
    required this.displayedMonth,
    required this.events,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return IntrinsicHeight(
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: List.generate(7, (weekday) {
          final day = _calculateDay(weekIndex, weekday);
          if (day == null) return const Expanded(child: SizedBox());

          final date = DateTime(displayedMonth.year, displayedMonth.month, day);
          final eventList = events[date] ?? [];

          return Expanded(
            child: CalendarDay(
              day: day,
              date: date,
              eventList: eventList,
              onTap: onTap,
            ),
          );
        }),
      ),
    );
  }

  int? _calculateDay(int weekIndex, int weekday) {
    final day = (weekIndex * 7) + weekday - firstWeekday + 1;
    return (day < 1 || day > daysInMonth) ? null : day;
  }
}
