bool isSameDay(DateTime first, DateTime second) {
  return first.year == second.year &&
      first.month == second.month &&
      first.day == second.day;
}

///判断DateTime是否是未来日期
bool isFutureDate(DateTime date) {
  return date.isAfter(DateTime.now());
}

/// 计算pager页码
int calculatePageIndex(
    {required DateTime initDate,
    required DateTime startDate,
    required DateTime endDate}) {
  assert(
    !endDate.isBefore(startDate),
    'initialDate cannot be before startDate',
  );
  assert(
    !initDate.isAfter(endDate),
    'initialDate cannot be after endDate',
  );

  final yearDiff = initDate.year - startDate.year;
  return yearDiff * 12 + (initDate.month - endDate.month);
}

bool isAfterToday(DateTime date,DateTime now) {
  // 获取当前日期（时间部分设为00:00:00）
  // final now = DateTime.now();
  final today = DateTime(now.year, now.month, now.day);

  // 目标日期也去掉时间部分（确保比较的是日期而非具体时间点）
  final targetDate = DateTime(date.year, date.month, date.day);

  // 判断目标日期是否在今天之后
  return targetDate.isAfter(today);
}
