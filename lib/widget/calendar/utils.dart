bool isSameDay(DateTime first, DateTime second) {
  return first.year == second.year &&
      first.month == second.month &&
      first.day == second.day;
}

/// 计算pager页码
int calculatePageIndex(
    {required DateTime initDate,
    required DateTime startDate,
    required DateTime endDate}) {
  assert(
    !endDate.isBefore(startDate),
    'initialDate cannot be before startDate',
  );
  assert(
    !initDate.isAfter(endDate),
    'initialDate cannot be after endDate',
  );

  final yearDiff = initDate.year - startDate.year;
  return yearDiff * 12 + (initDate.month - endDate.month);
}
