import 'package:flutter/material.dart';
import 'package:gdjg_pure_flutter/widget/calendar/dayevent/DayEvent.dart';
import 'package:gdjg_pure_flutter/widget/calendar/utils.dart';
import 'package:lunar/calendar/Lunar.dart';

class CalendarDay extends StatelessWidget {
  final int day;
  final DateTime date;
  final List<DayEvent> eventList;
  final Function(DateTime date) onTap;

  const CalendarDay({
    required this.day,
    required this.date,
    required this.eventList,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final isToday = isSameDay(date, DateTime.now());
    final colorScheme = Theme.of(context).colorScheme;

    // 判断是否展示备注提示
    final hasRemark = eventList.any((i) => i.type == DayEventType.Remark);
    // 借支
    final borrowingInfo =
        eventList.where((i) => i.type == DayEventType.Borrowing).firstOrNull;
    // 结算
    final settlementInfo =
        eventList.where((i) => i.type == DayEventType.Settlement).firstOrNull;
    // 点工
    final dailyPaidInfo =
        eventList.where((i) => i.type == DayEventType.DailyPaid).firstOrNull;
    final lumpSumInfo =
        eventList.where((i) => i.type == DayEventType.LumpSum).firstOrNull;
    final temporaryInfo =
        eventList.where((i) => i.type == DayEventType.Temporary).firstOrNull;
    final workloadInfo =
        eventList.where((i) => i.type == DayEventType.Workload).firstOrNull;
    // 当日无任何事件
    final emptyEvent =
        eventList.where((i) => i.type != DayEventType.Remark).isEmpty;

    // 判断是否展示农历(1、无事件2、非当前日)
    final showLunar = emptyEvent && !isToday;

    final showToday = emptyEvent && isToday;

    final lunarDay = Lunar.fromDate(date).getDayInChinese();

    return GestureDetector(
      onTap: () {
        onTap(date);
      },
      child: Container(
        margin: const EdgeInsets.all(2),
        decoration: BoxDecoration(
          color: eventList.isNotEmpty
              ? Color(0xFF5290FD).withValues(alpha: 0.1)
              : null,
          borderRadius: BorderRadius.circular(4),
          border: eventList.isNotEmpty
              ? Border.all(color: Color(0xFF5290FD), width: 1)
              : null,
        ),
        child: Stack(
          alignment: AlignmentDirectional.center,
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 4.0),
              child: Column(
                mainAxisSize: MainAxisSize.max,
                children: [
                  // 当前日
                  Text(
                    day.toString(),
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: !emptyEvent
                          ? colorScheme.primary
                          : isToday
                              ? colorScheme.error
                              : Colors.black.withValues(alpha: 0.85),
                      fontSize: 19,
                    ),
                  ),
                  // 当前农历日
                  Visibility(
                    visible: showLunar,
                    child: Text(
                      lunarDay,
                      style: TextStyle(
                        color: Colors.black.withValues(alpha: 0.25),
                        fontSize: 10,
                      ),
                    ),
                  ),
                  // 当前日事件
                  Visibility(
                      visible: showToday,
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                            vertical: 2, horizontal: 6),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(12),
                          color: colorScheme.primary,
                        ),
                        child: Text(
                          "今天",
                          style: TextStyle(
                              color: colorScheme.onPrimary, fontSize: 10),
                        ),
                      )),
                  Row(
                    mainAxisSize: MainAxisSize.max,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Visibility(
                        visible: borrowingInfo != null,
                        child: const _TagChild(
                          label: "借",
                          color: Color(0xFFFFA011),
                        ),
                      ),
                      Visibility(
                        visible: borrowingInfo != null,
                        child: const SizedBox(width: 2),
                      ),
                      Visibility(
                        visible: settlementInfo != null,
                        child: const _TagChild(
                          label: "结",
                          color: Color(0xFF029855),
                        ),
                      ),
                    ],
                  ),

                  Visibility(
                    visible: dailyPaidInfo != null,
                    child: _TextChild(value: "${dailyPaidInfo?.value ?? ""}工"),
                  ),
                  Visibility(
                    visible: lumpSumInfo != null,
                    child: _TextChild(value: "包${lumpSumInfo?.value ?? ""}工"),
                  ),
                  Visibility(
                    visible: temporaryInfo != null,
                    child: _TextChild(value: "短${temporaryInfo?.value ?? ""}"),
                  ),
                  Visibility(
                    visible: workloadInfo != null,
                    child: _TextChild(value: "量${workloadInfo?.value ?? ""}笔"),
                  ),
                ],
              ),
            ),
            if (hasRemark)
              const Positioned(
                right: 2,
                top: 2,
                child: Text(
                  "备",
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 9,
                    color: Color(0xFF5290FD),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}

class _TagChild extends StatelessWidget {
  final String label;
  final Color color;

  const _TagChild({super.key, required this.label, required this.color});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 14,
      width: 14,
      alignment: Alignment.center,
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.all(Radius.circular(2)),
      ),
      child: Text(
        label,
        style: TextStyle(
          fontWeight: FontWeight.bold,
          color: Colors.white,
          fontSize: 9,
        ),
      ),
    );
  }
}

class _TextChild extends StatelessWidget {
  final String value;

  const _TextChild({super.key, required this.value});

  @override
  Widget build(BuildContext context) {
    return Text(
      value,
      style: TextStyle(
        color: Colors.black.withValues(alpha: 0.85),
        fontSize: 9,
      ),
    );
  }
}
