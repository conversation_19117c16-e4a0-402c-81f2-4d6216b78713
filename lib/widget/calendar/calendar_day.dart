import 'package:flutter/material.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';
import 'package:gdjg_pure_flutter/widget/calendar/dayevent/DayEvent.dart';
import 'package:gdjg_pure_flutter/widget/calendar/utils.dart';
import 'package:lunar/calendar/Lunar.dart';

class CalendarDay extends StatelessWidget {
  final int day;
  final DateTime date;
  final List<DayEvent> eventList;
  final Function(DateTime date) onTap;
  static final GlobalKey todayKey = GlobalKey();

  const CalendarDay({
    super.key,
    required this.day,
    required this.date,
    required this.eventList,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final isToday = isSameDay(date, DateTime.now());
    final isFutureDay = isFutureDate(date);

    // 判断是否展示备注提示
    final hasRemark = eventList.any((i) => i.type == DayEventType.Remark);
    // 借支
    final hasBorrowing = eventList.any((i) => i.type == DayEventType.Borrowing);
    // 结算
    final hasSettlement =
        eventList.any((i) => i.type == DayEventType.Settlement);
    // 休息
    final hasRest = eventList.any((i) => i.type == DayEventType.Rest);

    List<DayEvent> filteredList = eventList
        .where((event) =>
            event.type != DayEventType.Borrowing &&
            event.type != DayEventType.Rest &&
            event.type != DayEventType.Settlement &&
            event.type != DayEventType.Remark)
        .toList();
    // 当日无任何事件
    final emptyEvent = eventList.isEmpty;
    // 判断是否展示农历(1、无事件2、非当前日)
    final showLunar = emptyEvent && !isToday;

    final showToday = emptyEvent && isToday;

    final lunarDay = Lunar.fromDate(date).getDayInChinese();

    return GestureDetector(
      onTap: () {
        onTap(date);
      },
      child: Container(
        margin: const EdgeInsets.all(1),
        decoration: BoxDecoration(
          color: eventList.isNotEmpty
              ? (hasRest
                  ? Color(0xFF029855).withValues(alpha: 0.1)
                  : Color(0xFF5290FD).withValues(alpha: 0.1))
              : null,
          borderRadius: BorderRadius.circular(4),
          border: eventList.isNotEmpty
              ? (hasRest
                  ? Border.all(color: Color(0xFF029855), width: 1)
                  : Border.all(color: Color(0xFF5290FD), width: 1))
              : null,
        ),
        child: Stack(
          alignment: AlignmentDirectional.center,
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 4.0),
              child: Column(
                mainAxisSize: MainAxisSize.max,
                children: [
                  // 当前日
                  Text(
                    key: isToday ? todayKey : null,
                    day.toString(),
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: !emptyEvent
                          ? (hasRest ? Color(0xFF029855) : Color(0xFF5290FD))
                          : isFutureDay ? Color(0x73000000)
                          : (isToday ? Color(0xFFF54A45) : Color(0xFF323232)),
                      fontSize: 16,
                    ),
                  ),
                  // 当前农历日
                  Visibility(
                    visible: showLunar,
                    child: Text(
                      lunarDay,
                      style: TextStyle(
                        color: Color(0x40000000),
                        fontSize: 10,
                      ),
                    ),
                  ),
                  // 当前日事件
                  Visibility(
                      visible: showToday,
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                            vertical: 2, horizontal: 6),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(12),
                          color: Color(0xFF5290FD),
                        ),
                        child: Text(
                          "今天",
                          style: TextStyle(color: Colors.white, fontSize: 10),
                        ),
                      )),
                  Row(
                    mainAxisSize: MainAxisSize.max,
                    mainAxisAlignment: MainAxisAlignment.center,
                    spacing: 2,
                    children: [
                      Visibility(
                        visible: hasBorrowing,
                        child: const _TagChild(
                          label: "借",
                          fontSize: 10,
                          bgColor: Color(0xFFFFA011),
                        ),
                      ),
                      Visibility(
                        visible: hasSettlement,
                        child: const _TagChild(
                          label: "结",
                          fontSize: 10,
                          bgColor: Color(0xFF029855),
                        ),
                      ),
                    ],
                  ),

                  if (hasRemark)
                    const Positioned(
                      right: 2,
                      top: 2,
                      child: Text(
                        "备",
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 9,
                          color: Color(0xFF5290FD),
                        ),
                      ),
                    ),
                  if (hasRest)
                    const Positioned(
                      right: 2,
                      top: 2,
                      child: Text(
                        "休",
                        style: TextStyle(
                          fontSize: 14,
                          color: Color(0xFF029855),
                        ),
                      ),
                    ),
                  if (filteredList.isNotEmpty)
                    Column(
                      children: filteredList.map((event) {
                        return _TextChild(
                            value: event.uiState?.value ?? "",
                            color: event.uiState?.color);
                      }).toList(),
                    )
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _TagChild extends StatelessWidget {
  final String label;
  final double fontSize;
  final Color bgColor;

  const _TagChild({required this.label, required this.fontSize, required this.bgColor});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 14,
      width: 14,
      alignment: Alignment.center,
      decoration: BoxDecoration(
        color: bgColor,
        borderRadius: BorderRadius.all(Radius.circular(2)),
      ),
      child: Text(
        label,
        style: TextStyle(
          fontWeight: FontWeight.bold,
          color: Colors.white,
          fontSize: fontSize,
        ),
      ),
    );
  }
}

class _TextChild extends StatelessWidget {
  final String value;
  final Color? color;

  const _TextChild({required this.value, this.color});

  @override
  Widget build(BuildContext context) {
    return Text(
      value,
      style: TextStyle(
        color: color ?? ColorsUtil.black85,
        fontSize: 10,
      ),
    );
  }
}
