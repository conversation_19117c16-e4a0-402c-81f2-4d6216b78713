import 'package:flutter/material.dart';

enum DayEventType {
  // 备注
  Remark,
  // 借支
  Borrowing,
  // 结算
  Settlement,
  // 点工
  DailyPaid,
  // 包工
  LumpSum,
  // 短工
  Temporary,
  // 工量
  Workload,
  // 其他费用
  Expense,
  // 休息
  Rest,
}

class DayEvent<T> {
  final DayEventType type;
  final DayEventUIState? uiState;

  DayEvent({required this.type, this.uiState});

}

class DayEventUIState {
  String value;

  Color? color;

  DayEventUIState({this.value = "", this.color});

}
