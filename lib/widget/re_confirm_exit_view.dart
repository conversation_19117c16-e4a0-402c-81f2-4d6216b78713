import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/toast_util.dart';
class ReConfirmExitView extends StatefulWidget {
  const ReConfirmExitView({super.key, required this.child});

  final Widget child;

  @override
  State<ReConfirmExitView> createState() => _ReConfirmExitViewState();
}

class _ReConfirmExitViewState extends State<ReConfirmExitView> {
  DateTime? _lastPressedAtTime;

  @override
  Widget build(BuildContext context) => PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, _) {
        if (didPop) return;
        if (_lastPressedAtTime == null ||
            DateTime.now().difference(_lastPressedAtTime!) >
                Duration(seconds: 2)) {
          _lastPressedAtTime = DateTime.now();
          ToastUtil.showToast('再按一次退出程序');
        } else {
          //cwltodo 后面再看用哪个
          // Navigator.of(context).pop(false);
          // 退出应用
          SystemNavigator.pop();
        }
      },
      child: widget.child);
}
