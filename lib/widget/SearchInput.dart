import 'package:flutter/material.dart';

class SearchInput extends StatelessWidget {
  //提示文案
  final String hintText;

  final IconData prefixIcon;

  //文本变化监听
  final ValueChanged<String>? onChanged;

  //最大输入字符数参数，如果不传可以无限输入
  final int? maxLength;

  const SearchInput({
    super.key,
    this.hintText = '请输入搜索内容',
    this.prefixIcon = Icons.search,
    this.onChanged,
    this.maxLength, // ✅ 接收参数
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 40, // 设置高度为 80
      child: TextField(
          maxLength: maxLength,
          style: TextStyle(
            color: Color(0XFF000000),
            fontSize: 14,
          ),
          decoration: _buildInputWeight()),
    );
  }

  ///输入框配置信息
  InputDecoration _buildInputWeight() {
    return InputDecoration(
      // —— 文字相关 ——————————————————————
      hintText: hintText,
      hintStyle: const TextStyle(
        color: Color(0xFF999999),
        fontSize: 14,
      ),
      counterText: '',
      // 隐藏底部字数计数器

      // —— 图标相关 ——————————————————————
      prefix: Padding(
        padding: EdgeInsets.only(right: 8),
        child: Icon(
          prefixIcon,
          size: 20,
          color: Colors.black,
        ),
      ),

      // —— 样式与布局 ——————————————————————
      filled: true,
      fillColor: const Color(0xFFF0F0F0),
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(4),
        borderSide: BorderSide.none, // 去掉边框线
      ),

      // —— 内边距 ————————————————————————
      contentPadding: const EdgeInsets.symmetric(
        horizontal: 0,
        vertical: 12,
      ),
    );
  }
}
