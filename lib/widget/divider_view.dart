import 'package:flutter/material.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';

/// <AUTHOR>
/// @date 2025/2/11/周二
/// @description 请求网络时的空、异常状态视图
class DividerView extends StatelessWidget {
  final double? height;
  final Color? color;

  const DividerView({super.key, this.height, this.color});

  @override
  Widget build(BuildContext context) {
    return Divider(
      height: height ?? 0.5,
      thickness: height ?? 0.5,
      color: color ?? ColorsUtil.f5f5f5,
    );
  }
}
