import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';

/// 关闭回调
typedef OnCloseTap = void Function(String? id);

/// 点击回调
typedef OnLabelTap = void Function(String? id);

/// 文本标签，项目中一般有三种样式：
/// 1. 整体灰色
/// 2. 整体蓝色
/// 3. 白底蓝边蓝字
/// 4. 支持标签点击，支持添加一个关闭icon
/// 如果复杂的样式可以使用自定义属性；如果太复杂建议自定义标签；
class TextLabel extends StatelessWidget {
  /// 标签内容
  final String label;

  /// 标签样式-如果是常见的三种样式，仅需要使用该属性。
  final TextLabelType? labelType;

  /// 是否有边框
  final bool? hasBorder;

  /// 标签内容
  final String? id;

////////////////////以下自定义属性使用时，请自行DIY标签样式//////////////////////
  /// 自定义标签字体大小-仅修改字体大小
  final double? labelSize;

  /// 文字是否加粗
  final bool? isBold;

  /// 内边距
  final EdgeInsets? padding;

  /// 自定义标签字体行高-仅修改字体行高(取值UI尺寸：行高/字体)
  final double? lineHeight;

  /// 自定义标签字体颜色-仅修改字体颜色
  final Color? labelColor;

  /// 自定义标签文字样式-有自定义style优先使用自定义style
  final TextStyle? style;

  /// 自定义标签宽度：默认按内边距定义标签，如果按宽高定义标签，注意文字是否显示得下！！！
  final double? width;

  /// 自定义标签高度：默认按内边距定义标签，如果按宽高定义标签，注意文字是否显示得下！！！
  final double? height;

  /// 自定义标签边框
  final BoxDecoration? borderDecoration;

  /// 自定义标签背景颜色
  final Color? bgColor;

  /// 有边框时也可以自定义边框颜色
  final Color? borderColor;

  /// 是否显示关闭图标
  final bool? showCloseImage;

  /// 自定义关闭图标
  final Image? closeImage;

  /// 关闭图标点击回调：如果展示了关闭按钮，则会调用此回调
  final OnCloseTap? onCloseTap;

  /// 标签点击回调
  final OnLabelTap? onLabelTap;

  const TextLabel({
    super.key,
    required this.label,
    this.labelType,
    this.style,
    this.width,
    this.height,
    this.borderDecoration,
    this.bgColor,
    this.labelColor,
    this.labelSize,
    this.lineHeight,
    this.hasBorder,
    this.borderColor,
    this.showCloseImage,
    this.closeImage,
    this.id,
    this.onCloseTap,
    this.onLabelTap,
    this.isBold,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    final hasWidthAndHeight = (width ?? 0) > 0 && (height ?? 0) > 0;

    return Container(
      width: hasWidthAndHeight ? width : null,
      height: hasWidthAndHeight ? height : null,
      padding: hasWidthAndHeight ? null : getPadding(),
      decoration: getBoxDecoration(),
      child: GestureDetector(
        onTap: onLabelClick,
        child: _buildChild(hasWidthAndHeight),
      ),
    );
  }

  void onLabelClick() {
    print('点击事件');
    if (showCloseImage == true) {
      onCloseTap?.call(id);
      return;
    }
    onLabelTap?.call(id);
  }

  Widget _buildIcon() {
    if (showCloseImage == true) {
      return getCloseImage();
    }
    return Container();
  }

  Widget _buildChild(bool hasWidthAndHeight) {
    if (hasWidthAndHeight) {
      return Center(
        child: IntrinsicWidth(
          child: _buildRow(),
        ),
      );
    }
    return IntrinsicWidth(
      child: _buildRow(),
    );
  }

  Widget _buildRow() {
    return Row(
      spacing: 2,
      children: [
        _buildText(),
        _buildIcon(),
      ],
    );
  }

  Widget _buildText() {
    return Text(
      label,
      style: style ??
          TextStyle(
            fontSize: labelSize ?? 14.sp,
            color: getTextColor(),
            height: lineHeight ?? 1.2,
            fontWeight: isBold == true ? FontWeight.bold : FontWeight.normal,
          ),
    );
  }

  Image getCloseImage() {
    if (closeImage != null) {
      return closeImage ??
          Image.asset("assets/images/common/icon_close.webp",
              width: 16, height: 16);
    }
    if (labelType == TextLabelType.grey) {
      return Image.asset("assets/images/common/icon_close.webp",
          width: 12, height: 12);
    }

    return Image.asset("assets/images/common/icon_close_blue.png",
        width: 12, height: 12);
  }

  EdgeInsets getPadding() {
    if (padding != null) {
      return padding ?? const EdgeInsets.symmetric(horizontal: 8, vertical: 4);
    }
    return const EdgeInsets.symmetric(horizontal: 8, vertical: 4);
  }

  /// 优先使用自定义边框
  BoxDecoration getBoxDecoration() {
    if (borderDecoration != null) {
      return borderDecoration ?? BoxDecoration();
    }
    return BoxDecoration(
      borderRadius: BorderRadius.circular(4),
      color: getBgColor(),
      border: hasBorder == true
          ? Border.all(color: getBorderColor(), width: 1.w)
          : null,
    );
  }

  /// 优先使用自定义边框颜色
  Color getBorderColor() {
    if (borderColor != null) {
      return borderColor ?? getTextColor();
    }
    if (labelType == TextLabelType.grey) {
      return getBgColor();
    } else if (labelType == TextLabelType.blue ||
        labelType == TextLabelType.white) {
      return ColorsUtil.ypPrimaryColor;
    } else {
      return labelColor ?? getTextColor();
    }
  }

  /// 优先使用自定义背景颜色
  Color getBgColor() {
    if (bgColor != null) {
      return bgColor ?? ColorsUtil.ypGreyColor;
    }
    if (labelType == TextLabelType.grey) {
      return ColorsUtil.ypGreyColor;
    } else if (labelType == TextLabelType.blue) {
      return ColorsUtil.primaryColor15;
    } else if (labelType == TextLabelType.white) {
      return Colors.white;
    } else {
      return labelColor ?? ColorsUtil.ypGreyColor;
    }
  }

  /// 优先使用自定义文字颜色
  Color getTextColor() {
    if (labelColor != null) {
      return labelColor ?? ColorsUtil.black65;
    }
    if (labelType == TextLabelType.grey) {
      return ColorsUtil.black65;
    } else if (labelType == TextLabelType.blue ||
        labelType == TextLabelType.white) {
      return ColorsUtil.ypPrimaryColor;
    } else {
      return labelColor ?? ColorsUtil.black65;
    }
  }
}

/// 标签样式
/// @date 2025/06/13
/// @description 标签类型
enum TextLabelType {
  /// 整体灰色
  grey,

  /// 整体蓝色
  blue,

  /// 白底蓝边蓝字
  white,
}
