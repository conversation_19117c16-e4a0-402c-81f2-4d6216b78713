import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';

/// 文本标签组件
class TextLabel extends StatelessWidget {
  final String label;
  final TextStyle? style;
  final double? width;
  final double? height;

  const TextLabel({
    super.key,
    required this.label,
    this.style,
    this.width,
    this.height,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width,
      height: height,
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(4),
        border: Border.all(
          color: ColorsUtil.primaryColor,
          width: 0.5,
        ),
      ),
      child: Text(
        label,
        style: style ??
            TextStyle(
              fontSize: 14.sp,
              color: ColorsUtil.black85,
            ),
      ),
    );
  }
}
