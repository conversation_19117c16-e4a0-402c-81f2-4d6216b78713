import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/feature/group/common_page/select_type_page/entity/select_type_props.dart';
import 'package:gdjg_pure_flutter/generated/assets.dart';
import 'package:gdjg_pure_flutter/init_module/init_route.dart';
import 'package:gdjg_pure_flutter/model/RecordType.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/button_util.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/month_select_util.dart';
import 'package:gdjg_pure_flutter/widget/date_picker_widget.dart';

/// 筛选数据模型
class CombinedFilterData {
  final DateTime startDate;
  final DateTime endDate;
  final List<RwaRecordType> selectedTypes;

  CombinedFilterData({
    DateTime? startDate,
    DateTime? endDate,
    this.selectedTypes = const [],
  }) : startDate = startDate ?? DateTime(DateTime.now().year - 1, 1, 1),
       endDate = endDate ?? DateTime.now();

  @override
  String toString() {
    return 'CombinedFilterData(startDate: $startDate, endDate: $endDate, selectedTypes: $selectedTypes)';
  }
}

/// 组合筛选器
class CombinedFilterWidget extends StatefulWidget {
  final Function(CombinedFilterData)? onFilterChanged;
  final DateTime? initialStartDate;
  final DateTime? initialEndDate;
  final bool showTypeFilter;

  const CombinedFilterWidget({
    super.key,
    this.onFilterChanged,
    this.initialStartDate,
    this.initialEndDate,
    this.showTypeFilter = true,
  });

  @override
  State<CombinedFilterWidget> createState() => _CombinedFilterWidgetState();
}

class _CombinedFilterWidgetState extends State<CombinedFilterWidget> {
  late DateTime _startDate;
  late DateTime _endDate;

  late DateTime _initialStartDate;
  late DateTime _initialEndDate;

  int? _selectedMonthValue;
  bool _isMonthSelected = false;
  bool _isAllSelected = false;
  List<RwaRecordType> _selectedTypes = [];

  @override
  void initState() {
    super.initState();
    // 初始化时间
    _initialStartDate = widget.initialStartDate ?? DateTime(DateTime.now().year - 1, 1, 1);
    _initialEndDate = widget.initialEndDate ?? DateTime.now();

    // 设置当前时间
    _startDate = _initialStartDate;
    _endDate = _initialEndDate;
  }

  @override
  void didUpdateWidget(covariant CombinedFilterWidget oldWidget) {
    super.didUpdateWidget(oldWidget);

    // 检查新的初始日期和结束日期是否与旧的不同
    if (widget.initialStartDate != oldWidget.initialStartDate ||
        widget.initialEndDate != oldWidget.initialEndDate) {
      // 更新 _startDate 和 _endDate
      setState(() {
        _initialStartDate = widget.initialStartDate ?? DateTime(DateTime.now().year - 1, 1, 1);
        _initialEndDate = widget.initialEndDate ?? DateTime.now();

        // 设置当前时间
        _startDate = _initialStartDate;
        _endDate = _initialEndDate;
      });
    }
  }

  /// 重置所有筛选状态
  void _resetFilterStates() {
    _isMonthSelected = false;
    _isAllSelected = false;
    _selectedMonthValue = null;
  }

  /// 设置月份选择状态
  void _setMonthSelectedState(int month) {
    _resetFilterStates();
    _isMonthSelected = true;
    _selectedMonthValue = month;
  }

  /// 设置全部选择状态
  void _setAllSelectedState() {
    _resetFilterStates();
    _isAllSelected = true;
  }

  /// 触发筛选回调
  void _triggerCallback() {
    if (widget.onFilterChanged != null) {
      final filterData = CombinedFilterData(
        startDate: _startDate,
        endDate: _endDate,
        selectedTypes: _selectedTypes,
      );
      widget.onFilterChanged!(filterData);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.white,
      child: Column(
        children: [
          _buildDateRangeSection(),
          Divider(
            height: 1,
            indent: 16.w,
            endIndent: 16.w,
            color: ColorsUtil.divideLineColor,
          ),
          _buildFilterButtonsSection(),
        ],
      ),
    );
  }

  /// 构建日期范围选择区域
  Widget _buildDateRangeSection() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
      child: Row(
        children: [
          Expanded(
            flex: 3,
            child: Row(
              children: [
                Flexible(
                  child: _buildDateSelector(_formatDate(_startDate), true),
                ),
                Icon(Icons.arrow_drop_down, size: 18.w,),
                Padding(
                  padding: EdgeInsets.only(right: 5.w),
                  child: Text(
                    '至',
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: const Color(0xFF323233),
                    ),
                  ),
                ),
                Flexible(
                  child: _buildDateSelector(_formatDate(_endDate), false),
                ),
                Icon(Icons.arrow_drop_down, size: 18.w,),
              ],
            ),
          ),
          ButtonUtil.buildCommonButtonWithBorder(
            height: 30.h,
            width: 50.w,
            text: _isMonthSelected ? '${_selectedMonthValue.toString().padLeft(2, '0')}月' : '月份',
            onPressed: () => _showMonthPicker(),
            selected: _isMonthSelected,
          ),
          SizedBox(width: 8.w),
          ButtonUtil.buildCommonButtonWithBorder(
            height: 30.h,
            width: 50.w,
            text: '全部',
            selected: _isAllSelected,
            onPressed: () {
              setState(() {
                _setAllSelectedState();
                _startDate = _initialStartDate;
                _endDate = _initialEndDate;
              });
              _triggerCallback();
            },
          ),
        ],
      ),
    );
  }

  /// 构建筛选按钮区域
  Widget _buildFilterButtonsSection() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 6.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              ButtonUtil.buildGreyButtonSelect(
                text: '全部工友',
                onPressed: () {},
              ),
              if (widget.showTypeFilter) ...[
                SizedBox(width: 8.w),
                if (_selectedTypes.isEmpty || _selectedTypes.length == 1)
                  ButtonUtil.buildGreyButtonSelect(
                    text: _getTypeButtonText(),
                    onPressed: _onTypeButtonPressed,
                  )
                else
                  _buildMultiLineTypeButton(),
              ],
            ],
          ),
        ],
      ),
    );
  }

  /// 获取类型按钮显示文本
  String _getTypeButtonText() {
    if (_selectedTypes.isEmpty) {
      return '类型';
    } else if (_selectedTypes.length == 1) {
      return _selectedTypes.first.label;
    } else {
      // 生成类型名称字符串，用|分隔
      return _selectedTypes.map((type) => type.label).join('|');
    }
  }

  /// 构建多行类型按钮
  Widget _buildMultiLineTypeButton() {
    List<List<RwaRecordType>> typeRows = [];
    for (int i = 0; i < _selectedTypes.length; i += 5) {
      int end = (i + 5 < _selectedTypes.length) ? i + 5 : _selectedTypes.length;
      typeRows.add(_selectedTypes.sublist(i, end));
    }

    return GestureDetector(
      onTap: _onTypeButtonPressed,
      child: Container(
        padding: EdgeInsets.only(left: 8.1.w, right: 7.w, top: 6.5.h, bottom: 6.5.h),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(4.r),
          color: ColorsUtil.inputBgColor,
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: typeRows.map((row) =>
                  Text(
                    row.map((type) => type.label).join('|'),
                    style: TextStyle(fontSize: 13.sp),
                  )
              ).toList(),
            ),
            SizedBox(width: 2.6.w),
            Image(
              image: AssetImage(Assets.commonIconTagSelect),
              width: 12.2.w,
              height: 13.3.h,
            )
          ],
        ),
      ),
    );
  }

  /// 处理类型按钮点击
  void _onTypeButtonPressed() {
    final props = SelectTypeProps(
      recordTypeList: _selectedTypes,
      isShowExpense: false,
    );

    YPRoute.openPage(RouteNameCollection.selectRecordType, params: props)?.then((result) {
      if (result != null && result is List<RwaRecordType>) {
        setState(() {
          _selectedTypes = result;
        });
        _triggerCallback();
      }
    });
  }

  /// 构建日期选择器
  Widget _buildDateSelector(String dateText, bool isStartDate) {
    return GestureDetector(
      onTap: () => _showDatePicker(isStartDate),
      child: Text(
        dateText,
        style: TextStyle(
          fontSize: 15.sp,
          fontWeight: FontWeight.w800,
          color: const Color(0xFF323233),
        ),
      ),
    );
  }

  /// 格式化日期显示
  String _formatDate(DateTime date) {
    return '${date.year}-${date.month}-${date.day}';
  }

  /// 显示日期选择器
  void _showDatePicker(bool isStartDate) {
    YPRoute.openDialog(builder: (context) => DatePickerWidget(
      initialDate: isStartDate ? _startDate : _endDate,
      isStartDate: isStartDate,
      minDate: isStartDate ? null : _startDate, // 结束日期不能早于开始日期
      onConfirm: (selectedDate) {
        setState(() {
          if (isStartDate) {
            _startDate = selectedDate;
          } else {
            _endDate = selectedDate;
          }
          _resetFilterStates();
        });
        _triggerCallback();
      },
    ),
        alignment: Alignment.bottomCenter,
        maskColor: Colors.black.withValues(alpha: 0.5));
  }

  /// 显示月份选择器
  void _showMonthPicker() {
    MonthSelectUtil.show(
      context: context,
      initialYear: _isMonthSelected ? _startDate.year : null,
      initialMonth: _isMonthSelected ? _selectedMonthValue : null,
      onSelected: (year, month) {
        setState(() {
          _startDate = DateTime(year, month, 1); // 月份第一天
          _endDate = DateTime(year, month + 1, 0); // 月份最后一天
          _setMonthSelectedState(month);
        });
        _triggerCallback();
      },
    );
  }
}