import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/button_util.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/month_select_util.dart';

/// 筛选数据模型
class CombinedFilterData {
  final DateTime startDate;
  final DateTime endDate;

  CombinedFilterData({
    required this.startDate,
    required this.endDate,
  });

  @override
  String toString() {
    return 'CombinedFilterData(startDate: $startDate, endDate: $endDate)';
  }
}

/// 组合筛选器
class CombinedFilterWidget extends StatefulWidget {
  final Function(CombinedFilterData)? onFilterChanged;

  const CombinedFilterWidget({
    super.key,
    this.onFilterChanged,
  });

  @override
  State<CombinedFilterWidget> createState() => _CombinedFilterWidgetState();
}

class _CombinedFilterWidgetState extends State<CombinedFilterWidget> {
  // 初始化日期
  DateTime _startDate = DateTime(DateTime.now().year - 1, 1, 1);
  DateTime _endDate = DateTime.now();

  // 月份选择状态
  int? _selectedMonthValue;
  bool _isMonthSelected = false;

  /// 触发筛选数据回调
  void _triggerCallback() {
    if (widget.onFilterChanged != null) {
      final filterData = CombinedFilterData(
        startDate: _startDate,
        endDate: _endDate,
      );
      widget.onFilterChanged!(filterData);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.white,
      child: Column(
        children: [
          _buildDateRangeSection(),
          Divider(
            height: 1,
            indent: 16.w,
            endIndent: 16.w,
            color: ColorsUtil.divideLineColor,
          ),
          _buildFilterButtonsSection(),
        ],
      ),
    );
  }

  Widget _buildDateRangeSection() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
      child: Row(
        children: [
          Expanded(
            child: Row(
              children: [
                _buildDateSelector(_formatDate(_startDate), true),
                Icon(Icons.arrow_drop_down, size: 18.w,),
                Padding(
                  padding: EdgeInsets.only(right: 5.w),
                  child: Text(
                    '至',
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: const Color(0xFF323233),
                    ),
                  ),
                ),
                _buildDateSelector(_formatDate(_endDate), false),
                Icon(Icons.arrow_drop_down, size: 18.w,),
              ],
            ),
          ),
          SizedBox(width: 8.w),
          ButtonUtil.buildCommonButtonWithBorder(
            text: _isMonthSelected ? '${_selectedMonthValue.toString().padLeft(2, '0')}月' : '月份',
            onPressed: () => _showMonthPicker(),
            selected: _isMonthSelected,
          ),
          SizedBox(width: 8.w),
          ButtonUtil.buildCommonButtonWithBorder(
            text: '全部',
            onPressed: () {
              setState(() {
                _isMonthSelected = false;
                _selectedMonthValue = null;
                // 重置日期为默认范围
                _startDate = DateTime(DateTime.now().year - 1, 1, 1);
                _endDate = DateTime.now();
              });
              _triggerCallback();
            },
          ),
        ],
      ),
    );
  }

  Widget _buildFilterButtonsSection() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 6.h),
      child: Row(
        children: [
          ButtonUtil.buildGreyButtonSelect(
            text: '全部工友',
            onPressed: () {},
          ),
          SizedBox(width: 8.w),
          ButtonUtil.buildGreyButtonSelect(
            text: '类型',
            onPressed: () {},
          ),
        ],
      ),
    );
  }

  /// 构建日期选择器
  Widget _buildDateSelector(String dateText, bool isStartDate) {
    return GestureDetector(
      onTap: () => _showDatePicker(isStartDate),
      child: Text(
        dateText,
        style: TextStyle(
          fontSize: 16.sp,
          fontWeight: FontWeight.w800,
          color: const Color(0xFF323233),
        ),
      ),
    );
  }

  /// 格式化日期
  String _formatDate(DateTime date) {
    return '${date.year}-${date.month}-${date.day}';
  }

  void _showDatePicker(bool isStartDate) {
    SmartDialog.show(
      builder: (context) => _DatePickerWidget(
        initialDate: isStartDate ? _startDate : _endDate,
        isStartDate: isStartDate,
        minDate: isStartDate ? null : _startDate, // 结束日期不能小于开始日期
        onConfirm: (selectedDate) {
          setState(() {
            if (isStartDate) {
              _startDate = selectedDate;
            } else {
              _endDate = selectedDate;
            }
          });
          _triggerCallback();
        },
      ),
      alignment: Alignment.bottomCenter,
      maskColor: Colors.black.withValues(alpha: 0.5),
    );
  }

  /// 显示月份选择器
  void _showMonthPicker() {
    MonthSelectUtil.show(
      context: context,
      initialYear: _startDate.year,
      initialMonth: _startDate.month,
      onSelected: (year, month) {
        setState(() {
          // 设置开始日期为选中月份的第一天
          _startDate = DateTime(year, month, 1);
          // 设置结束日期为选中月份的最后一天
          _endDate = DateTime(year, month + 1, 0);
          // 更新月份选择状态
          _selectedMonthValue = month;
          _isMonthSelected = true;
        });
        _triggerCallback();
      },
    );
  }
}

/// 日期选择器组件
class _DatePickerWidget extends StatefulWidget {
  final DateTime initialDate;
  final Function(DateTime) onConfirm;
  final DateTime? minDate; // 最小可选日期
  final bool isStartDate; // 是否为开始日期选择器

  const _DatePickerWidget({
    required this.initialDate,
    required this.onConfirm,
    required this.isStartDate,
    this.minDate,
  });

  @override
  State<_DatePickerWidget> createState() => _DatePickerWidgetState();
}

class _DatePickerWidgetState extends State<_DatePickerWidget> {
  late FixedExtentScrollController _yearController;
  late FixedExtentScrollController _monthController;
  late FixedExtentScrollController _dayController;

  late int _selectedYear;
  late int _selectedMonth;
  late int _selectedDay;
  late List<int> _years;
  final DateTime _now = DateTime.now();

  @override
  void initState() {
    super.initState();
    _initializeData();
    _initializeControllers();
  }

  /// 初始化数据
  void _initializeData() {
    _years = !widget.isStartDate && widget.minDate != null
        ? List.generate(_now.year - widget.minDate!.year + 1, (index) => widget.minDate!.year + index)
        : List.generate(6, (index) => _now.year - 5 + index);

    var initialDate = widget.initialDate.isAfter(_now) ? _now : widget.initialDate;
    if (widget.minDate != null && initialDate.isBefore(widget.minDate!)) {
      initialDate = widget.minDate!;
    }

    _selectedYear = initialDate.year;
    _selectedMonth = initialDate.month;
    _selectedDay = initialDate.day;
  }

  /// 初始化控制器
  void _initializeControllers() {
    final yearIndex = _years.indexOf(_selectedYear);
    _yearController = FixedExtentScrollController(initialItem: yearIndex >= 0 ? yearIndex : 0);
    _monthController = FixedExtentScrollController(initialItem: _selectedMonth - _getMinMonth(_selectedYear));
    _dayController = FixedExtentScrollController(initialItem: _selectedDay - _getMinDay(_selectedYear, _selectedMonth));
  }

  @override
  void dispose() {
    _yearController.dispose();
    _monthController.dispose();
    _dayController.dispose();
    super.dispose();
  }

  /// 获取指定年月的天数
  int _getDaysInMonth(int year, int month) => DateTime(year, month + 1, 0).day;

  /// 获取最小月份
  int _getMinMonth(int year) {
    return (!widget.isStartDate && widget.minDate != null && year == widget.minDate!.year)
        ? widget.minDate!.month
        : 1;
  }

  /// 获取最大月份
  int _getMaxMonth(int year) => year == _now.year ? _now.month : 12;

  /// 获取最小日期
  int _getMinDay(int year, int month) {
    return (!widget.isStartDate &&
            widget.minDate != null &&
            year == widget.minDate!.year &&
            month == widget.minDate!.month)
        ? widget.minDate!.day
        : 1;
  }

  /// 获取最大日期
  int _getMaxDay(int year, int month) {
    return (year == _now.year && month == _now.month) ? _now.day : _getDaysInMonth(year, month);
  }

  /// 控制器动画
  void _animateToItem(FixedExtentScrollController controller, int index) {
    controller.animateToItem(index, duration: const Duration(milliseconds: 100), curve: Curves.easeInOut);
  }

  /// 验证并调整日期
  void _validateAndAdjustDate() {
    final maxMonth = _getMaxMonth(_selectedYear);
    final minMonth = _getMinMonth(_selectedYear);

    if (_selectedMonth > maxMonth) {
      _selectedMonth = maxMonth;
      _animateToItem(_monthController, _selectedMonth - minMonth);
    } else if (_selectedMonth < minMonth) {
      _selectedMonth = minMonth;
      _animateToItem(_monthController, 0);
    }

    final maxDay = _getMaxDay(_selectedYear, _selectedMonth);
    final minDay = _getMinDay(_selectedYear, _selectedMonth);

    if (_selectedDay > maxDay) {
      _selectedDay = maxDay;
      _animateToItem(_dayController, _selectedDay - minDay);
    } else if (_selectedDay < minDay) {
      _selectedDay = minDay;
      _animateToItem(_dayController, 0);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 250.h,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(12.r),
          topRight: Radius.circular(12.r),
        ),
      ),
      child: Column(
        children: [
          _buildTitleBar(),
          Expanded(child: _buildPickerArea()),
        ],
      ),
    );
  }

  Widget _buildTitleBar() {
    return Container(
      height: 50.h,
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: ColorsUtil.divideLineColor,
            width: 1,
          ),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          GestureDetector(
            onTap: () => SmartDialog.dismiss(),
            child: Icon(
              Icons.close,
              size: 20.w,
              color: const Color(0xFF8A8A99),
            ),
          ),
          Text(
            '请选择时间',
            style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.w600,
              color: const Color(0xFF323233),
            ),
          ),
          GestureDetector(
            onTap: _onConfirm,
            child: Text(
              '确定',
              style: TextStyle(
                fontSize: 16.sp,
                color: ColorsUtil.primaryColor,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建选择器区域
  Widget _buildPickerArea() {
    return Stack(
      children: [
        Row(
          children: [
            Expanded(child: _buildYearColumn()),
            Expanded(child: _buildMonthColumn()),
            Expanded(child: _buildDayColumn()),
          ],
        ),
        Center(
          child: Container(
            height: 40.h,
            decoration: const BoxDecoration(
              border: Border(
                top: BorderSide(color: Color(0xFFE0E0E0), width: 1),
                bottom: BorderSide(color: Color(0xFFE0E0E0), width: 1),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildYearColumn() {
    return _buildScrollColumn(
      controller: _yearController,
      itemCount: _years.length,
      onSelectedItemChanged: (index) {
        setState(() {
          _selectedYear = _years[index];
          _validateAndAdjustDate();
        });
      },
      itemBuilder: (index) => '${_years[index]}年',
    );
  }

  Widget _buildMonthColumn() {
    final minMonth = _getMinMonth(_selectedYear);
    final maxMonth = _getMaxMonth(_selectedYear);

    return _buildScrollColumn(
      controller: _monthController,
      itemCount: maxMonth - minMonth + 1,
      onSelectedItemChanged: (index) {
        setState(() {
          _selectedMonth = minMonth + index;
          _validateAndAdjustDate();
        });
      },
      itemBuilder: (index) => '${(minMonth + index).toString().padLeft(2, '0')}月',
    );
  }

  Widget _buildDayColumn() {
    final minDay = _getMinDay(_selectedYear, _selectedMonth);
    final maxDay = _getMaxDay(_selectedYear, _selectedMonth);

    return _buildScrollColumn(
      controller: _dayController,
      itemCount: maxDay - minDay + 1,
      onSelectedItemChanged: (index) {
        setState(() {
          _selectedDay = minDay + index;
        });
      },
      itemBuilder: (index) => '${(minDay + index).toString().padLeft(2, '0')}日',
    );
  }

  /// 滚动列构建方法
  Widget _buildScrollColumn({
    required FixedExtentScrollController controller,
    required int itemCount,
    required Function(int) onSelectedItemChanged,
    required String Function(int) itemBuilder,
  }) {
    return ListWheelScrollView.useDelegate(
      controller: controller,
      itemExtent: 40.h,
      physics: const FixedExtentScrollPhysics(),
      onSelectedItemChanged: onSelectedItemChanged,
      childDelegate: ListWheelChildBuilderDelegate(
        childCount: itemCount,
        builder: (context, index) {
          if (index < 0 || index >= itemCount) return null;
          return Container(
            alignment: Alignment.center,
            child: Text(
              itemBuilder(index),
              style: TextStyle(fontSize: 18.sp, color: const Color(0xFF323233)),
            ),
          );
        },
      ),
    );
  }

  void _onConfirm() {
    final selectedDate = DateTime(_selectedYear, _selectedMonth, _selectedDay);
    widget.onConfirm(selectedDate);
    SmartDialog.dismiss();
  }
}
