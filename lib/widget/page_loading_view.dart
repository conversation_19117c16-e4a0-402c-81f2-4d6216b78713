import 'package:flutter/material.dart';

/// <AUTHOR>
/// @date 2025/2/11/周二
/// @description 请求网络时的loading
class PageLoadingView extends StatefulWidget {
  const PageLoadingView({super.key, this.onReload});
  final Function()? onReload;

  @override
  State<StatefulWidget> createState() => _PageLoadingState();
}

class _PageLoadingState extends State<PageLoadingView> {
  @override
  Widget build(BuildContext context) {
    return const Center(child: CircularProgressIndicator());
  }
}
