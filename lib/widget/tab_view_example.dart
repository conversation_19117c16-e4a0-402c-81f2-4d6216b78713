import 'package:flutter/material.dart';
import 'package:gdjg_pure_flutter/widget/tab_view.dart';

/// TabView使用示例
class TabViewExample extends StatelessWidget {
  const TabViewExample({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('TabView示例'),
        backgroundColor: Colors.white,
      ),
      body: TabView(
        items: _buildTabItems(),
        indicatorHeight: 3.0,
        indicatorWidth: 30.0,
        tabHeight: 50.0,
      ),
    );
  }

  /// 构建Tab数据
  List<TabViewItem> _buildTabItems() {
    return [
      TabViewItem(
        title: '点工',
        content: _buildPageContent('点工', '按小时计费的工作模式', Icons.access_time),
      ),
      TabViewItem(
        title: '包工',
        content: _buildPageContent('包工', '按项目包干的工作模式', Icons.work),
      ),
      TabViewItem(
        title: '短工',
        content: _buildPageContent('短工', '短期临时工作模式', Icons.schedule),
      ),
      TabViewItem(
        title: '工量',
        content: _buildPageContent('工量', '按工作量计费模式', Icons.assessment),
      ),
    ];
  }

  /// 构建页面内容
  Widget _buildPageContent(String title, String description, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            size: 80,
            color: Colors.blue[300],
          ),
          const SizedBox(height: 20),
          Text(
            title,
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 10),
          Text(
            description,
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 30),
          ElevatedButton(
            onPressed: () {
              // 处理按钮点击
              print('$title 按钮被点击');
            },
            child: Text('选择$title'),
          ),
        ],
      ),
    );
  }
}

/// 简化版使用示例
class SimpleTabViewExample extends StatelessWidget {
  const SimpleTabViewExample({super.key});

  @override
  Widget build(BuildContext context) {
    return TabView(
      items: [
        TabViewItem(
          title: '点工',
          content: const Center(child: Text('点工页面内容')),
        ),
        TabViewItem(
          title: '包工',
          content: const Center(child: Text('包工页面内容')),
        ),
        TabViewItem(
          title: '短工',
          content: const Center(child: Text('短工页面内容')),
        ),
        TabViewItem(
          title: '工量',
          content: const Center(child: Text('工量页面内容')),
        ),
      ],
    );
  }
}
