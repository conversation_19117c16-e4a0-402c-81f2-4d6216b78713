import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/generated/assets.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/toast_util.dart';

/// 显示删除确认弹窗
/// [onConfirm] 确认删除回调函数
void showDeleteConfirmDialog({required VoidCallback onConfirm}) {
  YPRoute.openDialog(
    clickMaskDismiss: false,
    onBack: () => true,
    builder: (context) => DeleteConfirmDialog(onConfirm: onConfirm),
  );
}

/// 删除确认弹窗组件
class DeleteConfirmDialog extends StatefulWidget {
  final VoidCallback onConfirm;

  const DeleteConfirmDialog({
    super.key,
    required this.onConfirm,
  });

  @override
  State<DeleteConfirmDialog> createState() => _DeleteConfirmDialogState();
}

class _DeleteConfirmDialogState extends State<DeleteConfirmDialog> {
  late int confirmCode;
  late TextEditingController _inputController;

  @override
  void initState() {
    super.initState();
    // 生成随机四位数确认码
    confirmCode = Random().nextInt(9000) + 1000;
    _inputController = TextEditingController();
  }

  @override
  void dispose() {
    _inputController.dispose();
    super.dispose();
  }

  /// 验证输入
  bool _validateInput() {
    final input = _inputController.text.trim();
    if (input.isEmpty) {
      ToastUtil.showToast('请输入正确的确认码');
      return false;
    }
    if (input != confirmCode.toString()) {
      ToastUtil.showToast('请输入正确的确认码');
      return false;
    }
    return true;
  }

  /// 确认删除
  void _onConfirmDelete() {
    if (_validateInput()) {
      YPRoute.closeDialog();
      widget.onConfirm();
    }
  }

  /// 取消删除
  void _onCancel() {
    YPRoute.closeDialog();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      alignment: Alignment.topCenter,
      children: [
        Container(
          width: MediaQuery.of(context).size.width * 0.85,
          margin: EdgeInsets.only(top: 38.h),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8.r),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildHeader(),
              _buildContent(),
              _buildInputSection(),
              _buildButtons(),
            ],
          ),
        ),
        // 顶部图标
        Positioned(
          top: 0,
          child: Stack(
            alignment: Alignment.center,
            children: [
              // 白色圆形底
              Container(
                width: 70.w,
                height: 70.h,
                decoration: const BoxDecoration(
                  color: Colors.white,
                  shape: BoxShape.circle,
                ),
              ),
              // 感叹号图标
              Image.asset(
                Assets.workerIconDeleteWarning,
                width: 80.w,
                height: 80.h,
              ),
              // 遮罩
              Container(
                width: 70.w,
                height: 70.h,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: RadialGradient(
                    center: Alignment.center,
                    radius: 0.5,
                    colors: [
                      Colors.white.withValues(alpha: 0.0),
                      Colors.white.withValues(alpha:0.6),
                    ],
                    stops: const [0.0, 1.0],
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildHeader() {
    return Padding(
      padding: EdgeInsets.only(top: 60.h, bottom: 8.h),
      child: Text(
        '确定删除该项目？',
        style: TextStyle(
          color: ColorsUtil.black85,
          fontSize: 22.sp,
          fontWeight: FontWeight.w800,
        ),
      ),
    );
  }

  Widget _buildContent() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 24.w),
      child: Column(
        children: [
          Text(
            '您将会失去该项目的：',
            style: TextStyle(
              color: ColorsUtil.black65,
              fontSize: 14.sp,
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 12.h),
          // 记录列表
          _buildRecordList(),
        ],
      ),
    );
  }

  Widget _buildRecordList() {
    final records = ['记工记录', '件支记录', '考勤记录'];

    return Column(
      children: records.map((record) => _buildRecordItem(record)).toList(),
    );
  }

  Widget _buildRecordItem(String text) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 4.h),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // 红色圆点
          Container(
            width: 6.w,
            height: 6.h,
            decoration: const BoxDecoration(
              color: Colors.red,
              shape: BoxShape.circle,
            ),
          ),
          SizedBox(width: 8.w),
          Text(
            text,
            style: TextStyle(
              color: ColorsUtil.black65,
              fontSize: 14.sp,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInputSection() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 10.h),
      child: Column(
        children: [
          Text(
            '输入「$confirmCode」 确认删除',
            style: TextStyle(
              color: ColorsUtil.black85,
              fontSize: 16.sp,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 12.h),
          Container(
            height: 44.h,
            decoration: BoxDecoration(
              color: const Color(0xFFFAFAFA),
              borderRadius: BorderRadius.circular(4.r),
              border: Border.all(
                color: const Color(0xFFE0E0E0), // 灰色边框
                width: 1.w,
              ),
            ),
            child: TextField(
              controller: _inputController,
              keyboardType: TextInputType.number,
              textAlign: TextAlign.center,
              inputFormatters: [
                FilteringTextInputFormatter.digitsOnly,
                LengthLimitingTextInputFormatter(4),
              ],
              style: TextStyle(
                color: ColorsUtil.black85,
                fontSize: 14.sp,
                fontWeight: FontWeight.w800
              ),
              decoration: const InputDecoration(
                border: InputBorder.none,
                contentPadding: EdgeInsets.zero, // 移除内边距
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 按钮
  Widget _buildButtons() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 20.h),
      child: Row(
        children: [
          // 取消按钮
          Expanded(
            child: GestureDetector(
              onTap: _onCancel,
              child: Container(
                height: 44.h,
                alignment: Alignment.center,
                decoration: BoxDecoration(
                  color: const Color(0xFFFDEFEE),
                  borderRadius: BorderRadius.circular(6.r),
                ),
                child: Text(
                  '取消',
                  style: TextStyle(
                    color: const Color(0xFFE0574F),
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ),
            ),
          ),

          SizedBox(width: 16.w), // 按钮间距

          // 确认删除按钮
          Expanded(
            child: GestureDetector(
              onTap: _onConfirmDelete,
              child: Container(
                height: 44.h,
                alignment: Alignment.center,
                decoration: BoxDecoration(
                  color: const Color(0xFFFF6B6B),
                  borderRadius: BorderRadius.circular(6.r),
                ),
                child: Text(
                  '确认删除',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}