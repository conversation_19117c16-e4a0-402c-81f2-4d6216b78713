import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../utils/route_util/route_api/yp_route.dart';
import '../utils/ui_util/colors_util.dart';

/// 巧克力按钮Dialog
/// 使用[ChocolateKeyDialogBuilder]构建
class ChocolateKeyDialog extends StatelessWidget {
  final Widget? title;
  final Widget? content;
  final Widget? startButton;
  final Widget? endButton;
  final void Function()? onStartTapCallback;
  final void Function()? onEndTapCallback;

  const ChocolateKeyDialog({
    super.key,
    this.title,
    this.content,
    this.startButton,
    this.endButton,
    this.onStartTapCallback,
    this.onEndTapCallback,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 24.h),
      width: MediaQuery.of(context).size.width * 0.85,
      decoration: BoxDecoration(
          color: Colors.white, borderRadius: BorderRadius.circular(8.r)),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: _createMainColumnChildren(),
      ),
    );
  }

  List<Widget> _createMainColumnChildren() {
    final Widget? titleL = title;
    final Widget? contentL = content;
    final Widget? buttonRow = _createButtonRow();
    final List<Widget> column = [];
    if (titleL != null) {
      column.add(titleL);
    }
    if (titleL != null && contentL != null) {
      column.add(SizedBox(height: 16.h));
    }
    if (contentL != null) {
      column.add(contentL);
    }
    if (column.isNotEmpty && buttonRow != null) {
      column.add(SizedBox(height: 16.h));
    }
    if (buttonRow != null) {
      column.add(buttonRow);
    }
    return column;
  }

  Widget? _createButtonRow() {
    final Widget? startButtonPack = startButton == null
        ? null
        : Expanded(
            child: GestureDetector(
            onTap: onStartTapCallback,
            child: startButton,
          ));
    final Widget? endButtonPack = endButton == null
        ? null
        : Expanded(
            child: GestureDetector(
            onTap: onEndTapCallback,
            child: endButton,
          ));
    final List<Widget> row;
    if (startButtonPack != null && endButtonPack != null) {
      row = [startButtonPack, SizedBox(width: 8.w), endButtonPack];
    } else if (startButtonPack != null) {
      row = [startButtonPack];
    } else if (endButtonPack != null) {
      row = [endButtonPack];
    } else {
      return null;
    }
    return SizedBox(
        height: 40.h,
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: row,
        ));
  }
}

/// 巧克力按钮Dialog构建器
/// 需要的元素内容设置 非null
/// title 和 content 默认具有相应的样式
/// startButton 默认被配置为 negative 样式
/// endButton 默认被配置为 positive 样式
class ChocolateKeyDialogBuilder {
  Widget? _title;
  Widget? _content;
  Widget? _startButton;
  Widget? _endButton;
  void Function()? _onStartTapCallback = YPRoute.closeDialog;
  void Function()? _onEndTapCallback;

  /// 语言限制; 默认可选参数只能是const; 某些时候可能需要非const的默认参数
  /// 使用一个未定义对象规避
  static const TextStyle _undefinedTextStyle = TextStyle();
  static const BoxDecoration _undefinedDecoration = BoxDecoration();

  static TextStyle titleTextStyle() => TextStyle(
        color: ColorsUtil.black85,
        fontSize: 18.sp,
        fontWeight: FontWeight.bold,
      );

  static TextStyle contentTextStyle() => TextStyle(
        color: ColorsUtil.black85,
        fontSize: 14.sp,
      );

  static TextStyle negativeTextStyle() => TextStyle(
        color: ColorsUtil.black85,
        fontSize: 14.sp,
      );

  static TextStyle positiveTextStyle() => TextStyle(
        color: Colors.white,
        fontSize: 14.sp,
      );

  static BoxDecoration negativeChocolateDecoration() => BoxDecoration(
        color: ColorsUtil.ypGreyColor,
        borderRadius: BorderRadius.all(Radius.circular(8)),
      );

  static BoxDecoration positiveChocolateDecoration() => BoxDecoration(
        color: ColorsUtil.ypPrimaryColor,
        borderRadius: BorderRadius.all(Radius.circular(8)),
      );

  ChocolateKeyDialogBuilder setTitleByText(String? text,
      {TextStyle? style = _undefinedTextStyle}) {
    _title = _createTextByText(text, _resolve(style, titleTextStyle()));
    return this;
  }

  ChocolateKeyDialogBuilder setTitleBySpans(List<TextSpan>? spans) {
    _title = _createTextBySpans(spans);
    return this;
  }

  ChocolateKeyDialogBuilder setTitleByWidget(Text? widget) {
    _title = widget;
    return this;
  }

  ChocolateKeyDialogBuilder setContentByText(String? text,
      {TextStyle? style = _undefinedTextStyle}) {
    _content = _createTextByText(text, _resolve(style, contentTextStyle()));
    return this;
  }

  ChocolateKeyDialogBuilder setContentBySpans(List<TextSpan>? spans) {
    _content = _createTextBySpans(spans);
    return this;
  }

  ChocolateKeyDialogBuilder setContentByWidget(Text? widget) {
    _content = widget;
    return this;
  }

  ChocolateKeyDialogBuilder setStartButtonByText(
    String? text, {
    TextStyle? style = _undefinedTextStyle,
    BoxDecoration? decoration = _undefinedDecoration,
  }) =>
      setStartButtonByWidget(
        _createTextByText(text, _resolve(style, negativeTextStyle())),
        decoration: decoration,
      );

  ChocolateKeyDialogBuilder setStartButtonBySpans(List<TextSpan>? spans,
          {BoxDecoration? decoration = _undefinedDecoration}) =>
      setStartButtonByWidget(
        _createTextBySpans(spans),
        decoration: decoration,
      );

  ChocolateKeyDialogBuilder setStartButtonByWidget(Text? widget,
      {BoxDecoration? decoration = _undefinedDecoration}) {
    _startButton = _createChocolateByWidget(
      widget,
      _resolve(decoration, negativeChocolateDecoration()),
    );
    return this;
  }

  ChocolateKeyDialogBuilder setEndButtonByText(
    String? text, {
    TextStyle? style = _undefinedTextStyle,
    BoxDecoration? decoration = _undefinedDecoration,
  }) =>
      setEndButtonByWidget(
        _createTextByText(text, _resolve(style, positiveTextStyle())),
        decoration: decoration,
      );

  ChocolateKeyDialogBuilder setEndButtonBySpans(List<TextSpan>? spans,
          {BoxDecoration? decoration}) =>
      setEndButtonByWidget(
        _createTextBySpans(spans),
        decoration: decoration,
      );

  ChocolateKeyDialogBuilder setEndButtonByWidget(Text? widget,
      {BoxDecoration? decoration = _undefinedDecoration}) {
    _endButton = _createChocolateByWidget(
        widget,
        _resolve(
          decoration,
          positiveChocolateDecoration(),
        ));
    return this;
  }

  ChocolateKeyDialogBuilder setOnStartTapCallback(void Function()? callback) {
    _onStartTapCallback = callback;
    return this;
  }

  ChocolateKeyDialogBuilder setOnEndTapCallback(void Function()? callback) {
    _onEndTapCallback = callback;
    return this;
  }

  Text? _createTextByText(String? text, TextStyle? style) =>
      text == null ? null : Text(text, style: style);

  Text? _createTextBySpans(List<TextSpan>? spans) =>
      spans == null ? null : Text.rich(TextSpan(children: spans));

  Widget? _createChocolateByWidget(
    Widget? widget,
    BoxDecoration? decoration,
  ) {
    return widget == null
        ? null
        : Container(
            decoration: decoration,
            alignment: Alignment.center,
            child: widget,
          );
  }

  T _resolve<T>(T target, T fallback) {
    if (target is TextStyle) {
      return identical(target, _undefinedTextStyle) ? fallback : target;
    } else if (target is BoxDecoration) {
      return identical(target, _undefinedDecoration) ? fallback : target;
    } else {
      return target;
    }
  }

  Widget build() {
    return ChocolateKeyDialog(
      title: _title,
      content: _content,
      startButton: _startButton,
      endButton: _endButton,
      onStartTapCallback: _onStartTapCallback,
      onEndTapCallback: _onEndTapCallback,
    );
  }
}
