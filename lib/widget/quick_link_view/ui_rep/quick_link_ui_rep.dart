import 'package:gdjg_pure_flutter/data/quick_link_data/repo/model/quick_link_entity.dart';
import 'package:gdjg_pure_flutter/data/quick_link_data/repo/quick_link_repo.dart';
import 'package:gdjg_pure_flutter/model/RecordNoteType.dart';
import 'package:get/get.dart';

/// @date 2025/06/21
/// @description QuickLink页UI仓库
/// 1.为vm提供业务数据
/// 2.解耦视图层与业务仓库之间的耦合
class QuickLinkUIRep {
  final QuickLinkRepo _quickLinkRepo = QuickLinkRepo();

  /// 实体数据
  var entity = QuickLinkUIRepEntity().obs;

  bool getStatus() {
    return true;
  }

  /// 初始化数据同步，返回同步是否成功的状态给UI层
  /// 如果成功，则将业务数据存储在entity中
  Future<QuickLinkUIRepEntity> fetchData(RecordNoteType noteType) async {
    // 调用网络的方法获取数据
    var result = await _quickLinkRepo.getQuickLinkData(noteType);
    // 返回成功的情况
    entity.value = QuickLinkUIRepEntity(data: result.getSucData());
    return entity.value;
  }
}

class QuickLinkUIRepEntity {
  List<QuickLinkEntity>? data;

  QuickLinkUIRepEntity({this.data});

  @override
  String toString() {
    return 'WorkerFlowUIRepEntity{data: $data}';
  }
}
