import 'package:flutter/material.dart';
import 'package:gdjg_pure_flutter/model/RecordNoteType.dart';
import 'package:gdjg_pure_flutter/widget/quick_link_view/vm/protocol/quick_link_ui_state.dart';
import 'package:get/get.dart';

import 'entity/quick_link_props.dart';
import 'vm/quick_link_vmcell.dart';

/// @date 2025/06/21
/// @param props 页面路由参数
/// @returns
/// @description QuickLink页面入口
class QuickLinkView extends StatelessWidget {
  QuickLinkView({super.key, this.props})
      : viewmodel = QuickLinkVMCell(props?.noteType ?? RecordNoteType.personal);

  final QuickLinkProps? props;
  final QuickLinkVMCell viewmodel;


  /// 有的组件不需要加载转态和错误处理，去掉即可
  @override
  Widget build(BuildContext context) {
    return contentView();
  }

  /// 实际展示的视图，需要处理：
  /// 1. 画出UI
  /// 2. 绑定UIState
  /// 3. 处理事件监听
  /// 4. 向VM传递来自View层的事件
  Widget contentView() {
    // 进行事件处理
    // handleQuickLinkVMEvent(props.vm)
    return Obx(() {
      // 如果没有数据，返回空容器
      if (viewmodel.isEmpty.value) {
        return const SizedBox.shrink();
      }

      return _buildDynamicProBenchView(viewmodel.uiState.value.list);
    });
  }

  ///这一部分是动态的
  Widget _buildDynamicProBenchView(List<QuickLinkItemUIState> items) {
    final wedges = items.map((element) {
      return _buildFunctionItem(element.title, element.icon, () {});
    }).toList();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Divider(height: 4, color: Color(0xFFF0F0F0), thickness: 4),
        _buildSectionTitle("找工人/项目"),
        _buildFunctionGrid(wedges),
      ],
    );
  }

  // 标题部分
  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 16, 0, 16),
      child: Text(
        title,
        style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Color(0xFF323232)),
      ),
    );
  }

  // 功能按钮网格
  Widget _buildFunctionGrid(List<Widget> items) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(0, 0, 0, 16),
      child: Wrap(
        runSpacing: 16,
        children: items,
      ),
    );
  }

  // 单个功能项
  Widget _buildFunctionItem(String label, String icon, VoidCallback onTap) {
    return FractionallySizedBox(
      widthFactor: 0.25,
      child: InkWell(
        onTap: onTap,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Image.network(icon, width: 48, height: 48),
            const SizedBox(height: 4),
            Text(
              label,
              textAlign: TextAlign.center,
              style: const TextStyle(fontSize: 14, color: Color(0xFF323232)),
            ),
          ],
        ),
      ),
    );
  }
}
