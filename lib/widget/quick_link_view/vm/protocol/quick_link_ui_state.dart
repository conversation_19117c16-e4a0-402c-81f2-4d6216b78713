/// @date 2025/06/21
/// @description JDetail页UI状态
class QuickLinkUIState {
  QuickLinkUIState({required this.list});
  List<QuickLinkItemUIState> list = [];
}

class QuickLinkItemUIState {
  QuickLinkItemUIState({
    this.code = "",
    this.icon = "",
    this.title = "",
    this.dest = "",
    this.url = "",
    this.webTitle = "",
    this.alert = "",
    this.miniAppid = "",
    this.originalId = "",
    this.miniPath = "",
    this.highlight = 0.0,
  });

  String code;
  String icon;
  String title;
  String dest;
  String url;
  String webTitle;
  String alert;
  String miniAppid;
  String originalId;
  String miniPath;
  double highlight;
}
