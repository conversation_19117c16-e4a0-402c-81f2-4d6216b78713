import "package:gdjg_pure_flutter/model/RecordNoteType.dart";
import "package:get/get.dart";

import "../ui_rep/quick_link_ui_rep.dart";
import "protocol/quick_link_ui_state.dart";

/// @date 2025/06/21
/// @description QuickLink页VMCell
/// 1. 处理数据的加载状态，如loading和错误视图等
/// 2. 观测UIRep中的业务实体转为UIState
/// 3. 接受来自view的意图并分发事件进行处理
/// 4. 对View层暴露事件（操作视图和处理一些与系统权限交互等场景）
class QuickLinkVMCell {
  var isEmpty = false.obs;
  var uiState = QuickLinkUIState(list: []).obs;
  var uiRep = QuickLinkUIRep();

  QuickLinkVMCell(RecordNoteType noteType) {
    fetchData(noteType);
    ever(uiRep.entity, (value) {
      _convertEntityToUIState(value);
    });
  }

  /// 初始化数据同步，如果不需要loading，则在这里去掉
  void fetchData(RecordNoteType noteType) async {
    try {
      await uiRep.fetchData(noteType);
    } catch (e) {
      _resetUIState();
    }
  }

  /// 将实体数据转换为UIState
  /// @param entity
  /// @returns
  void _convertEntityToUIState(QuickLinkUIRepEntity entity) {
    uiState.value = QuickLinkUIState(
      list: entity.data
              ?.map((element) => QuickLinkItemUIState(
                    code: element.code,
                    icon: element.icon,
                    title: element.title,
                    dest: element.dest,
                    url: element.url,
                    webTitle: element.webTitle,
                    alert: element.alert,
                    miniAppid: element.miniAppid,
                    originalId: element.originalId,
                    miniPath: element.miniPath,
                    highlight: element.highlight,
                  ))
              .toList() ??
          [],
    );
    isEmpty.value = uiState.value.list.isEmpty;
  }

  void _resetUIState() {
    uiState.value = QuickLinkUIState(list: []);
    isEmpty.value = true;
  }
}
