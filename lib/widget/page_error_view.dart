import 'package:flutter/material.dart';

/// <AUTHOR>
/// @date 2025/2/11/周二
/// @description 请求网络时的空、异常状态视图
class PageErrorView extends StatefulWidget {
  const PageErrorView({super.key, this.onReload});
  final Function()? onReload;

  @override
  State<StatefulWidget> createState() => _PageErrorState();
}

class _PageErrorState extends State<PageErrorView> {
  @override
  Widget build(BuildContext context) {
    return Center(
      child: InkWell(
        onTap: () {
          widget.onReload?.call();
        },
        child: const Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error),
            Text("还没有数据，点我重试"),
          ],
        ),
      ),
    );
  }
}
