
import 'package:flutter/material.dart';

class DashedVerticalDivider extends StatelessWidget {
  final double width;
  final double dashWidth;
  final double dashHeight;
  final Color color;
  final double height;

  const DashedVerticalDivider({
    this.width = 1,
    this.dashWidth = 4,
    this.dashHeight = 8,
    this.color = Colors.grey,
    this.height = double.infinity,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: width,
      height: height,
      child: CustomPaint(
        painter: _DashedVerticalLinePainter(
          dashWidth: dashWidth,
          dashHeight: dashHeight,
          color: color,
        ),
      ),
    );
  }
}

class _DashedVerticalLinePainter extends CustomPainter {
  final double dashWidth;
  final double dashHeight;
  final Color color;

  _DashedVerticalLinePainter({
    required this.dashWidth,
    required this.dashHeight,
    required this.color,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = size.width;

    double startY = 0;
    while (startY < size.height) {
      canvas.drawLine(
        Offset(0, startY),
        Offset(0, startY + dashHeight),
        paint,
      );
      startY += dashHeight + dashWidth; // 虚线间隔
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}