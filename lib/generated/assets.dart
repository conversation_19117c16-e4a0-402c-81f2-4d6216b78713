///This file is automatically generated. DO NOT EDIT, all your changes would be lost.
class Assets {
  Assets._();

  static const String commonEmptyBubble = 'assets/images/common/empty-bubble.png';
  static const String commonIcTakePhoneSmall = 'assets/images/common/ic_take_phone_small.webp';
  static const String commonIconArrowBack = 'assets/images/common/icon_arrow_back.png';
  static const String commonIconArrowRightGrey = 'assets/images/common/icon_arrow_right_grey.png';
  static const String commonIconClose = 'assets/images/common/icon_close.webp';
  static const String commonIconCloseBlue = 'assets/images/common/icon_close_blue.png';
  static const String commonIconExchange = 'assets/images/common/icon_exchange.webp';
  static const String commonIconFlowRecordable = 'assets/images/common/icon_flow_recordable.png';
  static const String commonIconMonthLeft = 'assets/images/common/icon_month_left.webp';
  static const String commonIconMonthRight = 'assets/images/common/icon_month_right.webp';
  static const String commonIconRoleClose = 'assets/images/common/icon_role_close.webp';
  static const String commonIconRoleGroup = 'assets/images/common/icon_role_group.webp';
  static const String commonIconRoleWorker = 'assets/images/common/icon_role_worker.webp';
  static const String commonIconTagSelect = 'assets/images/common/icon_tag_select.png';
  static const String commonIdentityNotSelectedBg = 'assets/images/common/identity_not_selected_bg.webp';
  static const String commonIdentitySelectedBg = 'assets/images/common/identity_selected_bg.webp';
  static const String commonImageTakePhoto = 'assets/images/common/image_take_photo.png';
  static const String commonNetLoading = 'assets/images/common/net_loading.png';
  static const String commonWaaIcFindProCheck = 'assets/images/common/waa_ic_find_pro_check.webp';
  static const String commonWaaIcFindProUncheck = 'assets/images/common/waa_ic_find_pro_uncheck.webp';
  static const String fontsCondMedium = 'assets/fonts/cond_medium.otf';
  static const String groupIcCalendarMenuClock = 'assets/images/group/ic_calendar_menu_clock.webp';
  static const String groupIcCalendarMenuClockStatistic = 'assets/images/group/ic_calendar_menu_clock_statistic.webp';
  static const String groupIcCalendarMenuConstructionLogs = 'assets/images/group/ic_calendar_menu_construction_logs.webp';
  static const String groupIcCalendarMenuMandate = 'assets/images/group/ic_calendar_menu_mandate.webp';
  static const String groupIcCalendarMenuNotCheckout = 'assets/images/group/ic_calendar_menu_not_checkout.webp';
  static const String groupIcCalendarMenuPointWages = 'assets/images/group/ic_calendar_menu_point_wages.webp';
  static const String groupIcCalendarMenuProjectSetting = 'assets/images/group/ic_calendar_menu_project_setting.webp';
  static const String groupIcCalendarMenuRecycleBin = 'assets/images/group/ic_calendar_menu_recycle_bin.webp';
  static const String groupIcCalendarMenuSceneWorker = 'assets/images/group/ic_calendar_menu_scene_worker.webp';
  static const String groupIcCalendarMenuShiftSetting = 'assets/images/group/ic_calendar_menu_shift_setting.webp';
  static const String groupIcCalendarMenuStatistics = 'assets/images/group/ic_calendar_menu_statistics.webp';
  static const String groupIcCalendarMenuTeamCloudAlbum = 'assets/images/group/ic_calendar_menu_team_cloud_album.webp';
  static const String groupIcCalendarMenuWorkSchedules = 'assets/images/group/ic_calendar_menu_work_schedules.png';
  static const String groupStatisticsTestJson = 'lib/data/group_data/group_statistics/test_json.json';
  static const String groupWaaIcBigCalendarMore = 'assets/images/group/waa_ic_big_calendar_more.webp';
  static const String groupWaaIcBigCalendarMoreUp = 'assets/images/group/waa_ic_big_calendar_more_up.webp';
  static const String groupWaaIcPscaDateBg = 'assets/images/group/waa_ic_psca_date_bg.webp';
  static const String groupWaaSvgProEye = 'assets/images/group/waa_svg_pro_eye.webp';
  static const String groupWaaSvgProEyeClose = 'assets/images/group/waa_svg_pro_eye_close.webp';
  static const String groupWaaSvgSelectTime = 'assets/images/group/waa_svg_select_time.webp';
  static const String imagesGroupIcCalendarMenuWorkSchedules = 'assets/images/group/ic_calendar_menu_work_schedules.webp';
  static const String loginIcon = 'assets/images/login/icon.webp';
  static const String loginImgItemDialogLoginErrorPhoneTop = 'assets/images/login/img_item_dialog_login_error_phone_top.png';
  static const String loginLoginIcBlackLineBack = 'assets/images/login/login_ic_black_line_back.webp';
  static const String loginLoginIcClose = 'assets/images/login/login_ic_close.webp';
  static const String loginLoginIcCodeClose = 'assets/images/login/login_ic_code_close.webp';
  static const String loginLoginIcInputClose = 'assets/images/login/login_ic_input_close.webp';
  static const String loginLoginIcInputPhone = 'assets/images/login/login_ic_input_phone.webp';
  static const String loginLoginIcInputPhoneSelector = 'assets/images/login/login_ic_input_phone_selector.webp';
  static const String loginLoginIcInputPwd = 'assets/images/login/login_ic_input_pwd.webp';
  static const String loginLoginIcInputPwdSelector = 'assets/images/login/login_ic_input_pwd_selector.webp';
  static const String loginLoginIcPopSmile = 'assets/images/login/login_ic_pop_smile.webp';
  static const String loginLoginIcSignXieyiN = 'assets/images/login/login_ic_sign_xieyi_n.webp';
  static const String loginLoginIcSignXieyiY = 'assets/images/login/login_ic_sign_xieyi_y.webp';
  static const String loginLoginIcWxLogin = 'assets/images/login/login_ic_wx_login.webp';
  static const String loginLoginnewIcDelete = 'assets/images/login/loginnew_ic_delete.webp';
  static const String loginWaaIcHomeCreateGroupMark = 'assets/images/login/waa_ic_home_create_group_mark.webp';
  static const String loginWaaIcHomeCreatePersonMark = 'assets/images/login/waa_ic_home_create_person_mark.webp';
  static const String tabbarIconTabbarCalendarNormal = 'assets/images/tabbar/icon_tabbar_calendar_normal.webp';
  static const String tabbarIconTabbarCalendarSelected = 'assets/images/tabbar/icon_tabbar_calendar_selected.webp';
  static const String tabbarIconTabbarFlowNomal = 'assets/images/tabbar/icon_tabbar_flow_nomal.webp';
  static const String tabbarIconTabbarFlowSelected = 'assets/images/tabbar/icon_tabbar_flow_selected.webp';
  static const String tabbarIconTabbarMineNormal = 'assets/images/tabbar/icon_tabbar_mine_normal.webp';
  static const String tabbarIconTabbarMineSelected = 'assets/images/tabbar/icon_tabbar_mine_selected.webp';
  static const String tabbarIconTabbarNotSettledNormal = 'assets/images/tabbar/icon_tabbar_not_settled_normal.webp';
  static const String tabbarIconTabbarNotSettledSelected = 'assets/images/tabbar/icon_tabbar_not_settled_selected.webp';
  static const String tabbarIconTabbarProjectNomal = 'assets/images/tabbar/icon_tabbar_project_nomal.webp';
  static const String tabbarIconTabbarProjectSelected = 'assets/images/tabbar/icon_tabbar_project_selected.webp';
  static const String tabbarIconTabbarStatisticNormal = 'assets/images/tabbar/icon_tabbar_statistic_normal.webp';
  static const String tabbarIconTabbarStatisticSelected = 'assets/images/tabbar/icon_tabbar_statistic_selected.webp';

}

abstract class LoginAssets {
  static const String _root = 'assets/images/login/';
  static const String loginIcPopSmile = '${_root}login_ic_pop_smile.webp';
  static const String loginIcInputPhone = '${_root}login_ic_input_phone.webp';
  static const String loginIcInputPhoneSelector = '${_root}login_ic_input_phone_selector.webp';
  static const String loginIcInputPwd = '${_root}login_ic_input_pwd.webp';
  static const String loginIcInputPwdSelector = '${_root}login_ic_input_pwd_selector.webp';
  static const String loginIcInputClose = '${_root}login_ic_input_close.webp';
  static const String loginIcBlackLineBack = '${_root}login_ic_black_line_back.webp';
  static const String loginIcClose = '${_root}login_ic_close.webp';
  static const String loginIcCodeClose = '${_root}login_ic_code_close.webp';
  static const String loginIcSignXieyiN = '${_root}login_ic_sign_xieyi_n.webp';
  static const String loginIcSignXieyiY = '${_root}login_ic_sign_xieyi_y.webp';
  static const String loginIcWxLogin = '${_root}login_ic_wx_login.webp';
  static const String loginnewIcDelete = '${_root}loginnew_ic_delete.webp';
  static const String loginAppIcon = '${_root}icon.webp';
  static const String loginGroupMark = '${_root}waa_ic_home_create_group_mark.webp';
  static const String loginPersonMark = '${_root}waa_ic_home_create_person_mark.webp';
  static const String loginErrorPhoneMark = '${_root}img_item_dialog_login_error_phone_top.png';
}
