///This file is automatically generated. DO NOT EDIT, all your changes would be lost.
class Assets {
  Assets._();

  static const String commonIconArrowBack = 'assets/images/common/icon_arrow_back.png';
  static const String commonIconArrowRightGrey = 'assets/images/common/icon_arrow_right_grey.png';
  static const String commonIconTagSelect = 'assets/images/common/icon_tag_select.png';
  static const String commonImageTakePhoto = 'assets/images/common/image_take_photo.png';
  static const String commonNetLoading = 'assets/images/common/net_loading.png';
  static const String tabbarIconTabbarCalendarNormal = 'assets/images/tabbar/icon_tabbar_calendar_normal.webp';
  static const String tabbarIconTabbarCalendarSelected = 'assets/images/tabbar/icon_tabbar_calendar_selected.webp';
  static const String tabbarIconTabbarFlowNomal = 'assets/images/tabbar/icon_tabbar_flow_nomal.webp';
  static const String tabbarIconTabbarFlowSelected = 'assets/images/tabbar/icon_tabbar_flow_selected.webp';
  static const String tabbarIconTabbarMineNormal = 'assets/images/tabbar/icon_tabbar_mine_normal.webp';
  static const String tabbarIconTabbarMineSelected = 'assets/images/tabbar/icon_tabbar_mine_selected.webp';
  static const String tabbarIconTabbarNotSettledNormal = 'assets/images/tabbar/icon_tabbar_not_settled_normal.webp';
  static const String tabbarIconTabbarNotSettledSelected = 'assets/images/tabbar/icon_tabbar_not_settled_selected.webp';
  static const String tabbarIconTabbarProjectNomal = 'assets/images/tabbar/icon_tabbar_project_nomal.webp';
  static const String tabbarIconTabbarProjectSelected = 'assets/images/tabbar/icon_tabbar_project_selected.webp';
  static const String tabbarIconTabbarStatisticNormal = 'assets/images/tabbar/icon_tabbar_statistic_normal.webp';
  static const String tabbarIconTabbarStatisticSelected = 'assets/images/tabbar/icon_tabbar_statistic_selected.webp';

}

abstract class LoginAssets {
  static const String _root = 'assets/images/login/';
  static const String loginIcPopSmile = '${_root}login_ic_pop_smile.webp';
  static const String loginIcInputPhone = '${_root}login_ic_input_phone.webp';
  static const String loginIcInputPhoneSelector = '${_root}login_ic_input_phone_selector.webp';
  static const String loginIcInputPwd = '${_root}login_ic_input_pwd.webp';
  static const String loginIcInputPwdSelector = '${_root}login_ic_input_pwd_selector.webp';
  static const String loginIcInputClose = '${_root}login_ic_input_close.webp';
  static const String loginIcBlackLineBack = '${_root}login_ic_black_line_back.webp';
  static const String loginIcClose = '${_root}login_ic_close.webp';
  static const String loginIcCodeClose = '${_root}login_ic_code_close.webp';
  static const String loginIcSignXieyiN = '${_root}login_ic_sign_xieyi_n.webp';
  static const String loginIcSignXieyiY = '${_root}login_ic_sign_xieyi_y.webp';
  static const String loginIcWxLogin = '${_root}login_ic_wx_login.webp';
  static const String loginnewIcDelete = '${_root}loginnew_ic_delete.webp';
  static const String loginAppIcon = '${_root}icon.webp';
  static const String loginGroupMark = '${_root}waa_ic_home_create_group_mark.webp';
  static const String loginPersonMark = '${_root}waa_ic_home_create_person_mark.webp';
  static const String loginErrorPhoneMark = '${_root}img_item_dialog_login_error_phone_top.png';
}
