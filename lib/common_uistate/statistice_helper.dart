import 'package:gdjg_pure_flutter/common_uistate/statistics_ui_state.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_calendar/repo/model/group_calendar_biz_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_pro_bill/repo/model/group_business_get_group_business_list_biz_model.dart';
import 'package:gdjg_pure_flutter/model/RecordType.dart';
import 'package:gdjg_pure_flutter/utils/system_util/string_util.dart';

class StatisticsUIStateHelper {
  ///构建统计项
  /// ```dart
  ///bizModel 统计数据
  ///isShowUnsettle 是否显示未结
  ///isShowDailyWages 是否显示短工笔数
  /// ```
  static List<StatisticsItemUIState> buildStatisticsItem(
      CountBizModel? bizModel,
      {bool isShowUnsettle = true,
      bool isShowDailyWages = true}) {
    final List<StatisticsItemUIState> statisticsItemList = [];
    if (bizModel == null) return statisticsItemList;
    // 1. 点工
    var spotWork = bizModel.spotWork;
    if (spotWork != null && spotWork.num > 0) {
      final detail = _buildWorkDetailString(
        workTime: spotWork.workTime,
        workTimeHour: double.tryParse(spotWork.workTimeHour) ?? 0.0,
        overtimeWork: spotWork.overtimeWork,
        overtime: double.tryParse(spotWork.overtime) ?? 0.0,
      );
      statisticsItemList.add(StatisticsItemUIState(
        recordType: RwaRecordType.workDays,
        typeName: "点工",
        detail: detail,
        feeMoney: spotWork.spotWorkFeeMoney.formatDoubleToMoney(),
        isRecordWorkType: true,
      ));
    }

    // 2. 包工
    var contractor = bizModel.contractor;
    if (contractor != null && contractor.num > 0) {
      final detail = _buildWorkDetailString(
          workTime: contractor.contractorWorkTime,
          workTimeHour:
              double.tryParse(contractor.contractorWorkTimeHour) ?? 0.0,
          overtimeWork: "",
          overtime: double.tryParse(contractor.contractorOvertime) ?? 0.0);
      statisticsItemList.add(StatisticsItemUIState(
        recordType: RwaRecordType.packageWork,
        typeName: "包工",
        detail: detail,
        feeMoney: contractor.contractorMoney.formatDoubleToMoney(),
        isRecordWorkType: true,
      ));
    }
    // 3. 工量
    var unit = bizModel.unit;
    if (unit != null && unit.num > 0) {
      for (var unit in unit.countUnit) {
        statisticsItemList.add(StatisticsItemUIState(
          recordType: RwaRecordType.workLoad,
          typeName: "工量",
          unitWorkTypeName: unit.unitWorkTypeName,
          unitWorkNum: unit.num > 0 ? "${unit.num.toStringAsFixed(0)}笔" : "",
          feeMoney: unit.unitMoney.formatStringToMoney(),
          total: (int.parse(unit.count) > 0)
              ? "总计:${unit.count}${unit.unitWorkTypeUnit}"
              : "",
          unitWorkType: unit.unitWorkType,
          isRecordWorkType: true,
        ));
      }
    }
    // 4. 短工
    var workMoney = bizModel.workMoney;
    if (workMoney != null && workMoney.num > 0) {
      statisticsItemList.add(StatisticsItemUIState(
        recordType: RwaRecordType.dailyWages,
        typeName: "短工",
        feeMoney: workMoney.workMoney.formatStringToMoney(),
        detail: isShowDailyWages
            ? "工资:${workMoney.workMoney.formatStringToMoney()}"
            : "",
        isRecordWorkType: true,
      ));
    }

    // 5. 借支
    var borrow = bizModel.borrow;
    if (borrow != null && borrow.num > 0) {
      statisticsItemList.add(StatisticsItemUIState(
        recordType: RwaRecordType.debt,
        typeName: "借支",
        feeMoney: borrow.borrowCount.formatDoubleToMoney(),
        isRecordWorkType: false,
      ));
    }

    // 6. 未结
    if (isShowUnsettle) {
      var unsettled = bizModel.unsettled;
      if (unsettled != null && unsettled > 0) {
        statisticsItemList.add(StatisticsItemUIState(
          recordType: RwaRecordType.wageLast,
          typeName: "未结",
          detail: "",
          feeMoney: unsettled.formatDoubleToMoney(),
          isRecordWorkType: false,
        ));
      }
    }

    // 7. 结算
    var income = bizModel.income;
    if (income != null && income > 0) {
      statisticsItemList.add(StatisticsItemUIState(
        recordType: RwaRecordType.wageLast,
        typeName: "结算",
        detail: "",
        feeMoney: income.formatDoubleToMoney(),
        isRecordWorkType: false,
      ));
    }
    return statisticsItemList;
  }

  /// 构建点工和包工详情
  static String _buildWorkDetailString({
    required String workTime,
    required double workTimeHour,
    required String overtimeWork,
    required double overtime,
  }) {
    final List<String> workParts = [];
    if (double.tryParse(workTime) != null && double.parse(workTime) > 0) {
      workParts.add("${workTime.trimTrailingZeros()}个工");
    }
    if (workTimeHour > 0) {
      workParts.add("${workTimeHour.trimTrailingZeros()}小时");
    }
    final workDetail = workParts.join(' + ');

    final List<String> overtimeParts = [];
    if (double.tryParse(overtimeWork) != null &&
        double.parse(overtimeWork) > 0) {
      overtimeParts.add("${overtimeWork.trimTrailingZeros()}个工");
    }
    if (overtime > 0) {
      overtimeParts.add("${overtime.trimTrailingZeros()}小时");
    }
    final overtimeDetail = overtimeParts.join(' + ');

    final List<String> finalParts = [];
    if (workDetail.isNotEmpty) {
      finalParts.add("上班: $workDetail");
    }
    if (overtimeDetail.isNotEmpty) {
      finalParts.add("加班: $overtimeDetail");
    }

    return finalParts.join('\n');
  }

  static RwaRecordType getRwaRecordType(double businessType) {
    if (businessType == RecordType.workDays.value) {
      return RwaRecordType.workDays;
    }
    if (businessType == RecordType.workLoad.value) {
      return RwaRecordType.workLoad;
    }
    if (businessType == RecordType.dailyWages.value) {
      return RwaRecordType.dailyWages;
    }
    if (businessType == RecordType.packageWork.value) {
      return RwaRecordType.packageWork;
    }
    if (businessType == RecordType.debt.value) {
      return RwaRecordType.debt;
    }
    if (businessType == RecordType.wageLast.value) {
      return RwaRecordType.wageLast;
    }
    if (businessType == RecordType.incomeLast.value) {
      return RwaRecordType.incomeLast;
    }
    if (businessType == RecordType.expenditure.value) {
      return RwaRecordType.expenditure;
    }
    return RwaRecordType.expense;
  }

  static String getBusinessTypeName(double businessType) {
    if (businessType == RecordType.workDays.value) {
      return "点工";
    }
    if (businessType == RecordType.workLoad.value) {
      return "工量";
    }
    if (businessType == RecordType.dailyWages.value) {
      return "短工";
    }
    if (businessType == RecordType.packageWork.value) {
      return "包工";
    }
    if (businessType == RecordType.debt.value) {
      return "借支";
    }
    if (businessType == RecordType.wageLast.value) {
      return "结算";
    }
    if (businessType == RecordType.incomeLast.value) {
      return "收入";
    }
    if (businessType == RecordType.expenditure.value) {
      return "支出";
    }
    return "记录";
  }

  static String getShowContentText(
      GroupBusinessGetGroupBusinessListBBizModel entity) {
    final businessType = entity.businessType;
    String contentText = '';
    if (businessType == RecordType.workDays.value) {
      if (entity.workTime > 0) {
        contentText = '上班:${entity.workTime.trimTrailingZeros()}个工';
      }
      if (entity.workTimeHour > 0) {
        contentText = '上班:${entity.workTimeHour.trimTrailingZeros()}小时';
      }
      if (contentText.isEmpty) {
        contentText = '上班:休息';
      }

      if (entity.morningWorkTime != null ||
          entity.morningWorkTimeHour != null ||
          entity.afternoonWorkTime != null ||
          entity.afternoonWorkTimeHour != null) {
        contentText = '';
        if ((entity.morningWorkTime ?? 0) > 0) {
          contentText = '上午:${entity.morningWorkTime?.trimTrailingZeros()}个工';
        } else {
          contentText = '上午:休息';
        }
        if ((entity.morningWorkTimeHour ?? 0) > 0) {
          contentText =
              '上午:${entity.morningWorkTimeHour?.trimTrailingZeros()}小时';
        } else if ((entity.morningWorkTime ?? 0) < 0) {
          contentText = '上午:休息';
        }
        if ((entity.afternoonWorkTime ?? 0) > 0) {
          contentText +=
              '\n下午:${entity.afternoonWorkTime?.trimTrailingZeros()}个工';
        } else if (entity.afternoonWorkTime != null) {
          contentText += '\n下午:休息';
        }
        if ((entity.afternoonWorkTimeHour ?? 0) > 0) {
          contentText +=
              '\n下午:${entity.afternoonWorkTimeHour?.trimTrailingZeros()}小时';
        } else if (entity.afternoonWorkTimeHour != null) {
          contentText += '\n下午:休息';
        }
      }

      if (entity.overtime > 0) {
        contentText += '\n加班:${entity.overtime.trimTrailingZeros()}小时';
      }
      if (entity.overtimeWork > 0) {
        contentText += '\n加班:${entity.overtimeWork.trimTrailingZeros()}个工';
      }
      return contentText;
    }

    //包工
    if (businessType == RecordType.packageWork.value) {
      if (entity.workTime > 0) {
        contentText = '上班:${entity.workTime.trimTrailingZeros()}个工';
      }
      if (entity.workTimeHour > 0) {
        contentText = '上班:${entity.workTimeHour.trimTrailingZeros()}小时';
      }
      if (contentText.isEmpty) {
        contentText = '上班:休息';
      }

      if (entity.morningWorkTime != null ||
          entity.morningWorkTimeHour != null ||
          entity.afternoonWorkTime != null ||
          entity.afternoonWorkTimeHour != null) {
        contentText = '';
        if ((entity.morningWorkTime ?? 0) > 0) {
          contentText = '上午:${entity.morningWorkTime?.trimTrailingZeros()}个工';
        } else {
          contentText = '上午:休息';
        }
        if ((entity.morningWorkTimeHour ?? 0) > 0) {
          contentText =
              '上午:${entity.morningWorkTimeHour?.trimTrailingZeros()}小时';
        } else if ((entity.morningWorkTime ?? 0) < 0) {
          contentText = '上午:休息';
        }
        if ((entity.afternoonWorkTime ?? 0) > 0) {
          contentText +=
              '\n下午:${entity.afternoonWorkTime?.trimTrailingZeros()}个工';
        } else if (entity.afternoonWorkTime != null) {
          contentText += '\n下午:休息';
        }
        if ((entity.afternoonWorkTimeHour ?? 0) > 0) {
          contentText +=
              '\n下午:${entity.afternoonWorkTimeHour?.trimTrailingZeros()}小时';
        } else if (entity.afternoonWorkTimeHour != null) {
          contentText += '\n下午:休息';
        }
      }

      if (entity.overtime > 0) {
        contentText += '\n加班:${entity.overtime.trimTrailingZeros()}小时';
      }
      return contentText;
    }

    if (businessType == RecordType.workLoad.value) {
      contentText = '工程量:${entity.unitNum}${entity.unitWorkTypeUnit}';
    }

    return contentText;
  }
}
