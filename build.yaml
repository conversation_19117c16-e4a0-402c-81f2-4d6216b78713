targets:
  $default:
#    如需自定义输入输出路径，需要声明
#    sources:
#      - $package$
#    上下两行功能一样
#      - lib/**
#      - gen/**
    sources:
      include:
        - tool/**
      exclude:
        - lib/**
    builders:
      runner_entity:gen:
        enabled: true
        options:
          id: 0
builders:
  gen:
    import: "package:runner_entity/builders.dart"
    builder_factories:
      - genDartEntity
    build_extensions:
      tool/gen.runner_entity:
        - tool/generated/net_model.dart
        - tool/generated/biz_model.dart
        - tool/generated/param_model.dart
    auto_apply: dependents
    runs_before:
      - json_serializable|json_serializable
    build_to: source
