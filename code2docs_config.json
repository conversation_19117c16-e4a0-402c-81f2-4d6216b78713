{"blacklistPattern": ["^*build*$", "^.git$", "^.idea$", ".*\\.webp$", "^*.xml$", "^\\.git.*"], "ruleList": [{"id": "LY", "name": "Flutter路由", "pathList": ["lib/init_module/init_route.dart", "lib/utils/route_util"], "wikiNode": "QBWSwSrrai3Z71kOSEbcViFonTb", "promptWords": "", "maintainer": "", "blacklistPattern": "", "funcDesc": ""}, {"id": "YYCSH", "name": "Flutter应用初始化", "pathList": ["lib/init_module/init_start.dart"], "wikiNode": "Owx2w9M7ZiOvp2kPZGvcKhuLnld", "promptWords": "", "maintainer": "", "blacklistPattern": "", "funcDesc": ""}, {"id": "KVCC", "name": "Flutter kv存储", "pathList": ["lib/utils/store_util"], "wikiNode": "DO9Zwr7ZEizeOck3AANcp0DSnmg", "promptWords": "", "maintainer": "", "blacklistPattern": "", "funcDesc": ""}, {"id": "XTGJL", "name": "Flutter系统工具类", "pathList": ["lib/utils/system_util"], "wikiNode": "FovUw5QJNiMS9vkHzYIcu6KcnGf", "promptWords": "", "maintainer": "", "blacklistPattern": "", "funcDesc": ""}, {"id": "UIGJL", "name": "Flutter UI工具类", "pathList": ["lib/utils/ui_util"], "wikiNode": "BZIrwoEZKibUxak7LkHcQ1ROnth", "promptWords": "", "maintainer": "", "blacklistPattern": "", "funcDesc": ""}, {"id": "RQXZZJ", "name": "Flutter日期选择组件", "pathList": ["lib/widget/combined_filter_widget.dart"], "wikiNode": "AgsrwTYNFihFoSkDnY0cSWUcn8t", "promptWords": "", "maintainer": "", "blacklistPattern": "", "funcDesc": ""}, {"id": "DJSZJ", "name": "Flutter倒计时组件", "pathList": ["lib/widget/countdown_button.dart"], "wikiNode": "C7KswtMPtifu9Okj7dNcI9eQnmg", "promptWords": "", "maintainer": "", "blacklistPattern": "", "funcDesc": ""}, {"id": "JZCWZJ", "name": "Flutter加载错误组件", "pathList": ["lib/widget/page_error_view.dart"], "wikiNode": "XRhrw6t3RizpyGkReo6cmW2XnLd", "promptWords": "", "maintainer": "", "blacklistPattern": "", "funcDesc": ""}, {"id": "JZZZJ", "name": "Flutter加载中组件", "pathList": ["lib/widget/page_loading_view.dart"], "wikiNode": "MwZgwRNCMiLUD9kvfd0cNVC9nqg", "promptWords": "", "maintainer": "", "blacklistPattern": "", "funcDesc": ""}, {"id": "TCQRZJ", "name": "Flutter退出确认组件", "pathList": ["lib/widget/re_confirm_exit_view.dart"], "wikiNode": "XdLKwnhgsieS5EkyYyrcCncgnLd", "promptWords": "", "maintainer": "", "blacklistPattern": "", "funcDesc": ""}]}