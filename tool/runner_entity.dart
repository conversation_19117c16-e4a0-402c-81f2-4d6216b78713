import 'dart:convert';
import 'dart:io';

void main(List<String> arguments) async {
  print('🚀 runner_entity开始生成实体类...');

  /// 👇🏻 点击左上方运行按钮方式，打开这一行，更改id，再点击运行按钮
  // var arguments = ["--id=0000"];

  if (arguments.isEmpty) {
    print('❌ 参数不能为空');
    return;
  }
  if (arguments.isNotEmpty && arguments.first.startsWith("--id=")) {
    var id = arguments.first.replaceFirst("--id=", "");
    if (id.isEmpty) {
      print('❌  id不能为空');
      return;
    }
    final result = await Process.run(
      'dart',
      [
        'run',
        'build_runner',
        'build',
        '--delete-conflicting-outputs',
        '--define=runner_entity:gen=id=$id',
      ],
      runInShell: true,
      stdoutEncoding: Encoding.getByName('utf-8'),
      stderrEncoding: Encoding.getByName('utf-8'),
    );
    print("${result.stdout}");
    if (result.exitCode != 0) {
      print("${result.stdout}");
    }
  }

  List<File> oldFiles = [
    File('tool/generated/biz_model.dart'),
    File('tool/generated/net_model.dart'),
    File('tool/generated/net_model.g.dart'),
    File('tool/generated/param_model.dart'),
  ];
  var className = "";
  var entityFile = oldFiles[0];
  if (!await entityFile.exists()) {
    print('❌ $entityFile not found');
    return;
  }
  var content = await entityFile.readAsString();
  final classRegex = RegExp(r'class\s+([A-Za-z0-9_]+)BizModel\s*{');
  final match = classRegex.firstMatch(content);
  className = match?.group(1) ?? '*';
  className = toSnakeCase(className);
  for (var oldFile in oldFiles) {
    await updateFileContent(oldFile, className);
  }

  List<File> newFiles = [
    File('tool/generated/${className}_biz_model.dart'),
    File('tool/generated/${className}_net_model.dart'),
    File('tool/generated/${className}_net_model.g.dart'),
    File('tool/generated/${className}_param_model.dart'),
  ];
  for (var i = 0; i < oldFiles.length; i++) {
    final oldFile = oldFiles[i];
    final newFile = newFiles[i];
    if (await oldFile.exists()) {
      await oldFile.rename(newFile.path);
    }
    if (i == oldFiles.length - 1) {
      print("✅  ====== 完成 ======");
    }
  }
}

Future<void> updateFileContent(File file, String className) async {
  var fContent = await file.readAsString();
  if (file.path.endsWith("biz_model.dart")) {
    return;
  } else if (file.path.endsWith("net_model.dart")) {
    fContent = fContent.replaceFirst(
      "part 'net_model.g.dart';",
      "part '${className}_net_model.g.dart';",
    );
    fContent = fContent.replaceFirst(
      "import 'biz_model.dart';",
      "import '${className}_biz_model.dart';",
    );
  } else if (file.path.endsWith("net_model.g.dart")) {
    fContent = fContent.replaceFirst(
      "part of 'net_model.dart';",
      "part of '${className}_net_model.dart';",
    );
  } else if (file.path.endsWith("param_model.dart")) {
    if (fContent.isEmpty) {
      await file.delete();
      return;
    }
  }
  await file.writeAsString(fContent);
}

String toSnakeCase(String input) {
  final regex = RegExp(r'(?<=[a-z0-9])[A-Z]');
  return input.replaceAllMapped(regex, (match) {
    var result = match.group(0);
    return '_$result';
  }).toLowerCase();
}
